services:
  build_translation:
    build:
      context: .
      dockerfile: compose/translation/Dockerfile
      tags:
        - cs_web_translation
    profiles: ["build_translation"]

  run_missing_translation:
    image: cs_web_translation:latest
    profiles: ["missing_translations"]
    volumes:
      - /tmp:/tmp
      - .:/app
    env_file:
      - ./compose/translation/cs-web-env.env
    command: /start.sh

  # You can add a new language by adding a semicolon-separated language code to the command below.
  # For example, to add Spanish, use: /run_makemessage.sh sv,de,fr,it,nl,pl,es
  run_makemessages:
    image: cs_web_translation:latest
    profiles: ["run_makemessages"]
    volumes:
      - .:/app
    env_file:
      - ./compose/translation/cs-web-env.env
    command: /run_makemessage.sh sv,de,fr,it,nl,pl

  run_translation: &translation
    image: cs_web_translation:latest
    profiles: ["run_translations"]
    env_file:
      - ./compose/translation/cs-web-env.env
    environment:
      - DEEPL_TRANSLATE_KEY=${DEEPL_TRANSLATE_KEY}

  # You can add a new language by adding a new service e.g. translation-worker-7 and passing the language code to the command.
  # For example, to add Spanish, use: command: /generate_translations.sh es

  translation-worker-1:
    <<: *translation
    profiles: ["run_translations"]
    volumes:
      - .:/app
    command: /generate_translations.sh fr

  translation-worker-2:
    <<: *translation
    profiles: ["run_translations"]
    volumes:
      - .:/app
    command: /generate_translations.sh de

  translation-worker-3:
    <<: *translation
    profiles: ["run_translations"]
    volumes:
      - .:/app
    command: /generate_translations.sh it

  translation-worker-4:
    <<: *translation
    profiles: ["run_translations"]
    volumes:
      - .:/app
    command: /generate_translations.sh nl

  translation-worker-5:
    <<: *translation
    profiles: ["run_translations"]
    volumes:
      - .:/app
    command: /generate_translations.sh pl
    
  translation-worker-6:
    <<: *translation
    profiles: ["run_translations"]
    volumes:
      - .:/app
    command: /generate_translations.sh sv
  
  remove_symlinks:
    image: cs_web_translation:latest
    profiles: ["remove_symlinks"]
    volumes:
      - .:/app
    command: /remove_symlinks.sh