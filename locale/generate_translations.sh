#!/bin/bash
set -euo pipefail

if [ "$#" -ne 2 ]; then
  echo "Usage: $0 <language_codes> <deepl_translate_key>" >&2
  echo "  language_codes: Single language code or comma-separated list (e.g., 'fr' or 'fr,de,it')" >&2
  echo "  Example languages: sv,de,fr,it,nl,pl" >&2
  exit 1
fi

LANGUAGES_INPUT="$1"
DEEPL_TRANSLATE_KEY="$2"

# Convert comma-separated list to array
IFS=',' read -ra LANGUAGES <<< "$LANGUAGES_INPUT"

if command -v docker compose &> /dev/null
then
    DOCKER_COMPOSE_CMD="docker compose"
else
    DOCKER_COMPOSE_CMD="docker-compose"
fi

COUNT_CONTAINERS=$(docker ps -q | wc -l)
if [ "$COUNT_CONTAINERS" -gt "0" ]; then
  $DOCKER_COMPOSE_CMD --compatibility down
fi

echo "Processing languages: ${LANGUAGES[@]}"
echo "=========================================="

echo "Validate symlinks do not exist before creating them"
$DOCKER_COMPOSE_CMD run django rm -f /app/src/cyber_smart/apps/templates
$DOCKER_COMPOSE_CMD run django rm -f /app/src/cyber_smart/apps/static

echo "Creating symlinks for templates and static folders"
$DOCKER_COMPOSE_CMD run django ln -s /app/templates /app/src/cyber_smart/apps/templates
$DOCKER_COMPOSE_CMD run django ln -s /app/static /app/src/cyber_smart/apps/static

for LANGUAGE in "${LANGUAGES[@]}"; do
  echo ""
  echo "Processing language: $LANGUAGE"
  echo "=========================================="

  echo "Making translations for files in /src for $LANGUAGE"
  $DOCKER_COMPOSE_CMD run --remove-orphans django python manage.py makemessages --no-obsolete --no-location -l $LANGUAGE

  echo "Deleting /static/jsi18n folder, since it clashes with the next steps"
  $DOCKER_COMPOSE_CMD run django rm -rf /app/src/cyber_smart/apps/static/jsi18n

  echo "Making translations for JS and HTML files in /templates and /static for $LANGUAGE"
  $DOCKER_COMPOSE_CMD run django python manage.py makemessages --symlinks --no-obsolete --no-location -l $LANGUAGE
  $DOCKER_COMPOSE_CMD run django python manage.py makemessages --symlinks -d djangojs --no-obsolete --no-location --extension=js -l $LANGUAGE
  # ignore these errors, to be addressed in CUSTEAM-2583
  $DOCKER_COMPOSE_CMD run django python manage.py makemessages --symlinks -d djangojs --no-obsolete --no-location --extension=html -l $LANGUAGE || True

  echo "Translating messages to $LANGUAGE"
  $DOCKER_COMPOSE_CMD run -e DEEPL_TRANSLATE_KEY=$DEEPL_TRANSLATE_KEY django python manage.py translate_messages --untranslated -l $LANGUAGE

  echo "Compiling messages, please check the errors and resolve"
  $DOCKER_COMPOSE_CMD run django python manage.py compilemessages --use-fuzzy -l $LANGUAGE
  $DOCKER_COMPOSE_CMD run django python manage.py compilejsi18n -l $LANGUAGE

  echo "Completed processing for $LANGUAGE"
done

echo "Removing symlinks for templates and static folders"
$DOCKER_COMPOSE_CMD run django rm -f /app/src/cyber_smart/apps/templates
$DOCKER_COMPOSE_CMD run django rm -f /app/src/cyber_smart/apps/static

echo ""
echo "=========================================="
echo "All translations have been generated!"
echo "=========================================="
