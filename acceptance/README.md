# Quick start

### Pre-requisites

Clone the repository:
```
git clone https://github.com/cyber-smart/cybersmart-platform-core.git
```

### Create docker container

```
docker run -d -p 4444:4444 selenium/standalone-firefox
```


### Install requirements
From the directory `cybersmart-platform-core`, run:

```
poetry install --only acceptance_test
```

### Run tests

```
python3 -m pytest acceptance/acceptance_tests
```

### Parameters

`--test_env`

Default value is `develop`. But can be set to `staging` or `localhost`. If running against `localhost`, then you need to build the web app first before running the tests

`-k`

Can be used to specify which test to run e.g. by test id, name or test folder name

`--headless`

Default is `True`. Can be set to `False` if you need to see the tests running on the browser

`-n`

This is used to run tests in parallel. `n` is the number of workers processes. When set to `auto` this will be equal to the number of CPUs

## Run tests in the same way as on CI

```
set -a &&
TEST_ENV=develop &&
TESTS_TO_RUN="not_accessibility_and_not_cleanup_and_not_security_and_not_mobileflow_and_not_emailflow_and_not_nis2" &&
docker compose -f docker-compose-acceptance-test.yml up --exit-code-from acceptance --build
```
The value of `TEST_ENV` can either be `develop` or `staging`. For the most up-to-date information on which specific tests are being run on CI. Please refer to `config.yml`

## Reporting

The html test report will be available in the `results` directory, after a test run, and is called `acceptance_test_report.html`

## Load testing

Information for load tests can be found here: https://cybersmart.atlassian.net/wiki/spaces/PE/pages/4990926889/Pre-release+Load+Tests+-+Observability-Driven+Approach


## Running tests on local web app build

Build the web app locally using the following commands:

```
aws-google-auth -p cs-aws-stating-s3
scripts/build-local-env-for-acceptance-test.sh dumpdb <password>
```

or if you already have the latest `cyberdb.dump.bz2` file, then just use this command:

```
scripts/build-local-env-for-acceptance-test.sh
```

Then run the following commands:

```
poetry install --only acceptance_test
python3 -m pytest acceptance/acceptance_tests -k TEST_ID --test_env localhost
```

Where `TEST_ID` is the ID of the test you want to run

### Other useful commands when running tests on local web app build

Running all acceptance tests

```
python3 -m pytest acceptance/acceptance_tests -k "not accessibility and not cleanup and not security and not test_signup and not T729 and not T723 and not T724 and not T802 and not T803 and not T804" --test_env localhost --reruns 1 --reruns-delay 2 -n 3
```


Running all accessibility tests

```
python3 -m pytest acceptance/acceptance_tests -k "T754 or T755 or T756 or T761 or T762" --test_env localhost --reruns 1 --reruns-delay 2
```

Running all security tests

```
python3 -m pytest acceptance/acceptance_tests -k security --test_env localhost --reruns 1 --reruns-delay 2
```

### Environment variables
When running Trustd tests (i.e. tests in `test_trustd.py`), you'll need to set the `TRUSTD_CLIENT_SECRET` variable

When running the Trustd mobile test (`test_trustd_mobileflow`), you'll need to set the following environment variables
`LAMBDA_TEST_USERNAME`
`LAMBDA_TEST_ACCESS_KEY`
`PLAYSTORE_FIRST_NAME`
`PLAYSTORE_EMAIL_ADDRESS`
`PLAYSTORE_PASSWORD`