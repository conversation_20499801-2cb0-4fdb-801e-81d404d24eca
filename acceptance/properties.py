# NOTE: if these prefixes are updated then please update AUTO_TEST_ORG_PREFIX and AUTO_TEST_TRUSTD_ORG_PREFIX in cs_settings.py as well
auto_test_org_prefix = 'AUTOMATEDTEST_org'
auto_test_trustd_org_prefix = 'AUTOMATEDTEST_Trustd_Org'

email_api_token = 'iISBjLFU9klh574UQiaObO3QPp3Dh673'

email_server_id = 'quwij3il'

# NOTE: if you update email_domain here, please also update in appusers.tests.test_tasks.cleanup_acceptance_test_users
email_domain = 'quwij3il.mailosaur.net'

users = {
    'iasme_partner_user': {
        'dev': {
            'username': '<EMAIL>',
            'partner_id': 4524,
            'user_id': 22573
        },
        'stg': {
            'username': '<EMAIL>',
            'partner_id': 1980,
            'user_id': 11010
        }
    },
    'non_iasme_partner_user': {
        'dev': {
            'username': '<EMAIL>',
            'partner_id': 4521,
            'user_id': 22558
        },
        'stg': {
            'username': '<EMAIL>',
            'partner_id': 1979,
            'user_id': 11009
        }
    },
    'exertis_distrib_user': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'non_exertis_distrib_user': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'brigantia_distrib_user': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'learn_lite': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'direct_user': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'org_admin': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'roles': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'device_user': {
        'dev': {'username': f'auto_test_device_user@{email_domain}'},
        'stg': {'username': f'auto_test_device_user_stg@{email_domain}'}
    },
    'uba_off_user': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'uba_on_user': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'vss_off_non_iasme_user': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'vss_off_iasme_user': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'impersonated_user_id': {
        'dev': 94618,
        'stg': 43510
    },
    'mfa_token_user': {
        'dev': {
            'username': '<EMAIL>',
            'user_profile_id': 42721
        },
        'stg': {
            'username': '<EMAIL>',
            'user_profile_id': 18979
        }
    },
    'beta_partner_user': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'trustd_mobile_user': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'direct_partner_user': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    },
    'translations_user': {
        'dev': {'username': '<EMAIL>'},
        'stg': {'username': '<EMAIL>'}
    }
}

org_uuids = {
    'learn_lite': {
        'dev': 'a4c9358c-3c72-4038-a7cb-d4854123dade',
        'stg': 'a0a1bc05-80b5-42d8-a193-c7c9b5a669e5'
    },
    'bulk_deployment_org': {
        'dev': 'f14cb47b-330a-4ebd-8de7-814d6a5b0c17',
        'stg': 'ef878e45-da38-47ff-8161-0c5377aaae30'
    },
    'individual_deployment_org': {
        'dev': '94edf49b-3b7c-4ea1-a428-d17b85b1c8f0',
        'stg': '5488fb6a-f1a2-46e4-9696-20522bcce25d'
    },
    'org_admin_org': {
        'dev': '31ab8df3-1803-4d7a-9dd1-0839992b7a5c',
        'stg': '470c3ebc-f250-4d7d-b89b-961ebcacebe3'
    },
    'direct_org': {
        'dev': '21b734b1-0237-4c0d-a4b9-6b2ee7e8d954',
        'stg': 'ff2e60a0-c3a4-48c1-a2f0-a2daf117729e'
    },
    'certs_org': {
        'dev': '10bba1d1-2e89-4b7c-aa92-2b2455473045',
        'stg': 'f13c6728-c90c-4aca-b00b-ffec161635f4'
    },
    'trustd_individual_org': {
        'dev': '23e1de21-3084-43cb-ba1e-562672aebe83',
        'stg': 'a6589377-a1aa-483f-96b3-aa1ef859ad6c'
    },
    'trustd_bulk_org': {
        'dev': 'dfc756cc-781d-4d9e-bf27-06ab5bc1addc',
        'stg': 'df285e78-86e3-4907-aabe-ab18257b7038'
    },
    'org_with_device': {
        'dev': '45f979be-182a-4ab2-86ed-1e5afe7e108d',
        'stg': '6089c03b-2113-4fc6-9b48-a815aeb9c0ad'
    },
    'org_without_device': {
        'dev': 'd9fc0533-c0d4-44ee-8e35-d9687cf4d27d',
        'stg': '13b9805c-2b25-4d16-a1ac-f17025c3d30a'
    },
    'translations_org': {
        'dev': '4664baa2-5ee9-439e-9a2a-9a5d2b2acc3e',
        'stg': 'dc800c31-73b1-4f6d-b5c0-4577dffd80b6'
    },
}

appuser_uuids = {
    'trustd_bulk_org': {
        'dev': '54f69833-85c4-4555-b0e3-dd193dfa6eb0',
        'stg': 'ac992761-cbb7-47b1-b600-35eea9fd4370'
    },
    'trustd_individual_org': {
        'dev': 'c22c398d-c9d8-4e92-bd92-323fc4ea98f3',
        'stg': 'f35a7037-b573-41df-84a7-2b1805420899'
    }
}

waffle_switch_ids = {
    'disable_creation_of_trustd_customer': {
        'dev': '728',
        'stg': '631'
    }
}

application_partner_ids = {
    'dev': 7555,
    'stg': 3308
}

application_partner_org_ids = {
    'dev': 'a9732a6e-798d-4393-8d24-562b1349cc5e',
    'stg': 'c709662c-77c1-44ec-945e-5b8476d39104'
}

exertis_distributor_id = 167

org_names = {
    'trustd_individual_org': {
        'dev': 'auto test beta org dev [TEST]',
        'stg': 'auto test partner beta org stg [TEST]'
    },
    'translations_org': {
        'dev': 'auto test translation org dev',
        'stg': 'auto test translation org stg'
    }
}

existing_orgs = {'dev': 'auto_test_existing_org_dev', 'stg': 'auto_test_existing_org_stg'}

bulk_org_ids = {'dev': 37682, 'stg': 15028}

cert_version_ids = {
    'dev': '486',
    'stg': '651'
}

trustd_device_id = {
    'dev': 'f342feb4-72e7-434c-a11c-fe3b0f9dccf3',
    'stg': '7b2125ab-9661-4a02-963b-05a5c7b97703'
}

trustd_policy_device_id = {
    'dev': '0fa0eb69-5134-466a-a30e-525874e7f6b8',
    'stg': '42f23e5e-8673-4b2f-aae7-3cdc51a63cb0'
}

trustd_org_ids_for_device_cleanup = {
    'dev': '102210',
    'stg': '39181'
}

mobile_enrolment_key = {
    'dev': 'YFVN7GT7BKAWHOPP6KROV2DU41TMN3A7',
    'stg': 'NBS7DO1O2YS04A1O2FWHXUE6ZJGPDQEF'
}

policy_name_for_trustd = 'mobile_test_policy'

additional_trust_device_ids = {
    'dev': [
        '97b9634b-a9f0-46d7-b1a3-cf5f80ef6b84',
        '8bcba8a9-2f6d-45a6-a104-00f078db7443',
        'ba1a9094-f7b3-4c7c-9d7e-22d1b2a2f5dc',
        '19a2a157-d611-4fb3-9188-e9f6029b1949',
        '7417e54d-f7a2-4933-92e5-db375a3aa382'
    ],
    'stg': [
        '3e94640f-58b0-4eab-9537-118e31b2e2d9',
        'de8f0870-f754-4c42-9070-231c04f62782',
        'd8aca0da-abf0-4086-b40a-b574d36901ad',
        '3378ab55-d22d-4aa3-ad9d-b65caa46510c',
        '85a8b0ae-29a8-466e-ae4d-fa1c052555b9'
    ]
}

trustd_customer_id = {
    'dev': {
        'individual': 'dc1cd07f-98e7-4ce8-899a-32f460cd4731',
        'bulk': 'b1bfc437-663b-47a4-91cf-82537a15289b'
    },
    'stg': {
        'individual': '91c09e6e-b229-48eb-906e-230b7e05a0c4',
        'bulk': 'c4e9be70-1592-4975-8ec5-dbc8b4ad3d0c'
    }
}

trustd_policy_customer_id = {
    'dev': 'd7bd518b-5260-4967-a680-ce8c08c2b1f8',
    'stg': '21cf08a3-87f3-4c4f-8e42-23cfaa7ab5cd'
}

trustd_client_id = {
    'dev': 'pquyHAaG9OsiZHVkCnQdbfgRhu7TxFAHdstNgGnq',
    'stg': 'TJI5kR757KFx2X0b28xf6ngtSB8KNXT9VwxMMTi6'
}

desktop_device_info = {
    'dev': 'macOS Sonoma - 14 - Apple [x1]',
    'stg': 'macOS Sonoma - 14 - Apple [x1]'
}

mobile_device_info = {
    'dev': 'iOS - 1 - Vendor QH85O0B [x1]',
    'stg': 'iOS - 1 - Vendor AS38GMT [x1]'
}

appinstalls_1 = {
    'dev': {
        "id": 9048,
        "uuid": '9d1430e4-aae6-4b74-9487-50af183ac7e7',
        "device_id": 'a0b2362a-717e-4bc5-a332-1c681e831bc9',
        "serial_number": 'ahK8N7yB3MamxigtWSG5iT',
        "device_name": 'XLWONR2'
    },
    'stg': {
        "id": 10893,
        "uuid": 'f9d0b58d-56f6-405e-a865-00ab73b7971a',
        "device_id": 'aae65212-c3a1-4b25-b634-f0f18ec4890f',
        "serial_number": 'gD7LeFW5io5Rb6gT3TPBgq',
        "device_name": 'OYVFT6W'
    }
}

appinstalls_2 = {
    'dev': {
        "uuid": "22fa9aaf-72ef-4bd5-828f-02d22309f4ce",
        "device_id": "7271c38a-d9b5-45e4-abd4-aa44a53817d4",
        "serial_number": "eerQKeoXeFVPmmH2hfJU3f",
        "hostname": "Host EF625DM",
        "domain": "policies_bulk_org_domain_dev.com",
        "user": "policies_bulk_org_user_dev",
        "platform": "android",
        "release": "13",
        "policy_id": 3299,
        "policy_name": "bulk_org_policy_dev",
        "policy_version_id": 3627,
    },
    'stg': {
        "uuid": "fb5f09d2-90a7-4993-a93d-4d801ef450bb",
        "device_id": "d8b20558-622e-43be-8fd9-2d36b6c954f1",
        "serial_number": "TvbxvmLn2pZJzqETSQDDTe",
        "hostname": "Host SGYTLRD",
        "domain": "policies_bulk_org_domain_stg.com",
        "user": "policies_bulk_org_user_stg",
        "platform": "android",
        "release": "13",
        "policy_id": 4033,
        "policy_name": "bulk_org_policy_stg",
        "policy_version_id": 4453,
    }
}

appinstalls_3 = {
    'dev': {
        'manually_resolved': {
            "uuid": '913fa699-d8fb-4b20-b5d5-fcdfff31646a',
            "platform": "win32",
            "release": "10.0.10240",
            "hostname": 'Host IFD2ZI9',
            "device_id": 'b956ee73-0d07-4008-9445-e4f230abed77',
            "serial_number": '4vhkBJE9m6vUsdFXLzaD43',
            "caption": 'OS IFD2ZI9'
        },
        'permanently_resolved': {
            "uuid": '83d6e099-6175-4c33-bf22-f7b6445a7a0e',
            "platform": "win32",
            "release": "10.0.10240",
            "hostname": 'Host W8GTZ31',
            "device_id": '077308fd-8a5f-4bc5-a192-1fb7dd14471d',
            "serial_number": '2mjH6dJ2xT7wDgn8zQrKY7',
            "caption": 'OS W8GTZ31'
        }
    },
    'stg': {
        'manually_resolved': {
            "uuid": '12ad5f47-3d67-4370-9ec9-536ce9cf33ef',
            "platform": "win32",
            "release": "10.0.10240",
            "hostname": 'Host ONDQCW0',
            "device_id": '52eb9cbc-92c0-4b90-8548-b126b22db1a0',
            "serial_number": 'jMwvWKChYoKh2SehAAKW36',
            "caption": 'OS ONDQCW0'
        },
        'permanently_resolved': {
            "uuid": '6b43bb3e-e89d-44e1-b338-c2ecf226d129',
            "platform": "win32",
            "release": "10.0.10240",
            "hostname": 'Host M313G7S',
            "device_id": 'e0140573-e235-4a6d-8af8-ef09d18244a7',
            "serial_number": '96ww85R2NTL9PKSFeZSkhK',
            "caption": 'OS M313G7S'
        }
    }
}

appinstalls_4 = {
    'dev': {
        'windows': {
            "uuid": "83d6e099-6175-4c33-bf22-f7b6445a7a0e",
            "platform": "win32",
            "release": "10.0.10240",
            "hostname": "Host W53RRRM",
            "device_id": "4391999b-6b7b-40f2-bb84-27811f66ab04",
            "serial_number": "4v3Kvc4vAsgbPk2oCi2QFZ"
        },
        'mac': {
            "uuid": "83d6e099-6175-4c33-bf22-f7b6445a7a0e",
            "platform": "darwin",
            "release": "21.6.0",
            "hostname": "Host A178R5T",
            "device_id": "ee18cf63-500d-4ed7-8af5-74d64a633ade",
            "serial_number": "STXiK7LYoqKtxZCfa2UyAv"
        },
        'ios': {
            "uuid": "83d6e099-6175-4c33-bf22-f7b6445a7a0e",
            "platform": "ios",
            "release": "12.0",
            "hostname": "Host 44JKJ0R",
            "device_id": "1fcfcb1a-5fe8-4d72-8855-b5f14ecf5dc0",
            "serial_number": "PkX3Gu5SYXPKgpYtoAdRsH"
        },
        'android': {
            "uuid": "83d6e099-6175-4c33-bf22-f7b6445a7a0e",
            "platform": "android",
            "release": "10.0",
            "hostname": "Host IP6D8ZI",
            "device_id": "f0c6ba08-f1f0-4825-b5e5-ff3972e2ecc8",
            "serial_number": "VoWwYzEWmVVwxQxHPjBsRN"
        }
    },
    'stg': {
        'windows': {
            "uuid": "6b43bb3e-e89d-44e1-b338-c2ecf226d129",
            "platform": "win32",
            "release": "10.0.10240",
            "hostname": "Host GONUFGI",
            "device_id": "c9c62789-0253-495f-b67c-8baf8f201cf8",
            "serial_number": "JUAUSPtL72jDLttUsUpoDM"
        },
        'mac': {
            "uuid": "6b43bb3e-e89d-44e1-b338-c2ecf226d129",
            "platform": "darwin",
            "release": "21.6.0",
            "hostname": "Host S2BDF6W",
            "device_id": "2a1ced6c-eaa7-4128-902c-************",
            "serial_number": "Ua5sDFHLpYmjmhV2ZstAae"
        },
        'ios': {
            "uuid": "6b43bb3e-e89d-44e1-b338-c2ecf226d129",
            "platform": "ios",
            "release": "12.0",
            "hostname": "Host 8TZ27GW",
            "device_id": "a1e7deff-25e2-4d86-aafc-fadf7601795b",
            "serial_number": "aVyfE4a2srPX5mqq6PpDqy"
        },
        'android': {
            "uuid": "6b43bb3e-e89d-44e1-b338-c2ecf226d129",
            "platform": "android",
            "release": "10.0",
            "hostname": "Host WQ2LQAM",
            "device_id": "c2474c7d-6a5f-4e7d-9da0-f4f7187f9145",
            "serial_number": "ApH78gdGSov3rNQNVusFm5"
        }
    }
}

appinstalls_5 = {
    'dev': {"uuid": 'ba03aa25-cb32-4ae1-b410-f644bdf7991b'},
    'stg': {"uuid": 'b3673a3a-798a-4840-a467-b0426a2357e6'}
}

appinstalls_6 = {
    'dev': {
        'device_id': 'V5-4fe10dc2-c5a2-5c20-89a4-b876a52bbee7',
        'os_user': 'seyiadmin',
        'serial_number': 'VX20XCYF4G',
        'appuser_uuid': '84191d47-6126-4a69-a7ee-481ae5e29d10'
    },
    'stg': {
        'device_id': 'V5-4fe10dc2-c5a2-5c20-89a4-b876a52bbee7',
        'os_user': 'seyiadmin',
        'serial_number': 'VX20XCYF4G',
        'appuser_uuid': 'e453c2d4-b677-4f92-9792-a51668ca58d2'
    }
}

appinstalls_7 = {
    'dev': {
        'uuid': '6525d18b-6db8-44da-906d-408e8bbb2c54',
        'device_id': 'V5-2364b4a4-0243-417d-ab7f-d18d17e86e44',
        'serial_number': '0000-0015-**************-1045-50',
        'os_user': 'seyiadmin11062024'
    },
    'stg': {
        'uuid': '8841ffd5-3a14-41f6-9df3-b4fa444b3ca2',
        'device_id': 'V5-2364b4a4-0243-417d-ab7f-d18d17e86e44',
        'serial_number': '0000-0015-**************-1045-50',
        'os_user': 'seyiadmin11062024'
    }
}

appinstalls_8 = {
    'dev': {
        'device_id': 'V5-4fe10dc2-c5a2-5c20-89a4-b876a52bbee7',
        'os_user': 'seyiadmin',
        'serial_number': 'VX20XCYF4G',
        'appuser_uuid': 'e3f09980-b893-47ef-bf4d-5ebb81ffa616'
    },
    'stg': {
        'device_id': 'V5-4fe10dc2-c5a2-5c20-89a4-b876a52bbee7',
        'os_user': 'seyiadmin',
        'serial_number': 'VX20XCYF4G',
        'appuser_uuid': 'ef13d33d-e488-4216-a9d1-6b3663731d95'
    }
}

appinstalls_9 = {
    'dev': {
        'device_id': 'V5-ca501672-9b05-5a05-8ebe-27dc1283eb77',
        'os_user': 'mypc',
        'serial_number': 'C02YF3EYJG5H',
        'appuser_uuid': 'e3f09980-b893-47ef-bf4d-5ebb81ffa616'
    },
    'stg': {
        'device_id': 'V5-ca501672-9b05-5a05-8ebe-27dc1283eb77',
        'os_user': 'mypc',
        'serial_number': 'C02YF3EYJG5H',
        'appuser_uuid': 'ef13d33d-e488-4216-a9d1-6b3663731d95'
    }
}

appinstalls_10 = {
    'dev': {
        'device_id': 'V5-f3d2b97f-a099-4863-a06c-4a4319ec45cb',
        'os_user': 'seyiadmin',
        'serial_number': 'Parallels-68 B3 BF 48 79 7A 4D ED B7 70 F2 29 B7 19 39 5D',
        'appuser_uuid': '84191d47-6126-4a69-a7ee-481ae5e29d10'
    },
    'stg': {
        'device_id': 'V5-f3d2b97f-a099-4863-a06c-4a4319ec45cb',
        'os_user': 'seyiadmin',
        'serial_number': 'Parallels-68 B3 BF 48 79 7A 4D ED B7 70 F2 29 B7 19 39 5D',
        'appuser_uuid': 'e453c2d4-b677-4f92-9792-a51668ca58d2'
    }
}

appinstalls_11 = {
    'dev': {
        'device_id': 'V5-4fe10dc2-c5a2-5c20-89a4-b876a52bbee7',
        'os_user': 'seyiadmin',
        'serial_number': 'VX20XCYF4G',
        'appuser_uuid': 'b415535b-1f1c-40a8-a9b9-535e98a884c8'
    },
    'stg': {
        'device_id': 'V5-4fe10dc2-c5a2-5c20-89a4-b876a52bbee7',
        'os_user': 'seyiadmin',
        'serial_number': 'VX20XCYF4G',
        'appuser_uuid': '1ade7094-63e0-48ad-9f1e-fb934d596377'
    }
}

appinstalls_mac_individual = {
    'dev': {
        'device_id': '4fe10dc2-c5a2-5c20-89a4-b876a52bbee7',
        'os_user': 'seyiadmin',
        'serial_number': 'VX20XCYF4G',
        'appuser_uuid': '3cff107c-5548-4b0b-8dac-bc07d79bf206'
    },
    'stg': {
        'device_id': '4fe10dc2-c5a2-5c20-89a4-b876a52bbee7',
        'os_user': 'seyiadmin',
        'serial_number': 'VX20XCYF4G',
        'appuser_uuid': 'bc322d9f-0480-42e8-bdb1-417b46c423c7'
    }
}


appinstalls_mac_bulk = {
    'dev': {
        'device_id': '4fe10dc2-c5a2-5c20-89a4-b876a52bbee7',
        'os_user': 'seyiadmin',
        'serial_number': 'VX20XCYF4G',
        'appuser_uuid': '34024056-9efb-43f6-bcb0-3ad70e55df85'
    },
    'stg': {
        'device_id': '4fe10dc2-c5a2-5c20-89a4-b876a52bbee7',
        'os_user': 'seyiadmin',
        'serial_number': 'VX20XCYF4G',
        'appuser_uuid': 'cf967fc5-2f90-457d-bb04-acc3ff063d46'
    }
}

appinstalls_mac_bulk_uba = {
    'dev': {
        'device_id': '4fe10dc2-c5a2-5c20-89a4-b876a52bbee7',
        'os_user': 'seyiadmin',
        'serial_number': 'VX20XCYF4G',
        'appuser_uuid': 'f3cd11c6-6eea-4bbd-a2c8-6e89d7100326'
    },
    'stg': {
        'device_id': '4fe10dc2-c5a2-5c20-89a4-b876a52bbee7',
        'os_user': 'seyiadmin',
        'serial_number': 'VX20XCYF4G',
        'appuser_uuid': '87c24714-80cf-4c86-a3a4-b817f66e2589'
    }
}

appinstalls_windows_individual = {
    'dev': {
        'device_id': 'f3d2b97f-a099-4863-a06c-4a4319ec45cb',
        'os_user': 'seyiadmin',
        'serial_number': 'Parallels-68 B3 BF 48 79 7A 4D ED B7 70 F2 29 B7 19 39 5D',
        'appuser_uuid': '3cff107c-5548-4b0b-8dac-bc07d79bf206'
    },
    'stg': {
        'device_id': 'f3d2b97f-a099-4863-a06c-4a4319ec45cb',
        'os_user': 'seyiadmin',
        'serial_number': 'Parallels-68 B3 BF 48 79 7A 4D ED B7 70 F2 29 B7 19 39 5D',
        'appuser_uuid': 'bc322d9f-0480-42e8-bdb1-417b46c423c7'
    }
}

appinstalls_windows_bulk = {
    'dev': {
        'device_id': 'f3d2b97f-a099-4863-a06c-4a4319ec45cb',
        'os_user': 'seyiadmin',
        'serial_number': 'Parallels-68 B3 BF 48 79 7A 4D ED B7 70 F2 29 B7 19 39 5D',
        'appuser_uuid': '34024056-9efb-43f6-bcb0-3ad70e55df85'
    },
    'stg': {
        'device_id': 'f3d2b97f-a099-4863-a06c-4a4319ec45cb',
        'os_user': 'seyiadmin',
        'serial_number': 'Parallels-68 B3 BF 48 79 7A 4D ED B7 70 F2 29 B7 19 39 5D',
        'appuser_uuid': 'cf967fc5-2f90-457d-bb04-acc3ff063d46'
    }
}

appinstalls_windows_bulk_uba = {
    'dev': {
        'device_id': 'f3d2b97f-a099-4863-a06c-4a4319ec45cb',
        'os_user': 'seyiadmin',
        'serial_number': 'Parallels-68 B3 BF 48 79 7A 4D ED B7 70 F2 29 B7 19 39 5D',
        'appuser_uuid': 'f3cd11c6-6eea-4bbd-a2c8-6e89d7100326'
    },
    'stg': {
        'device_id': 'f3d2b97f-a099-4863-a06c-4a4319ec45cb',
        'os_user': 'seyiadmin',
        'serial_number': 'Parallels-68 B3 BF 48 79 7A 4D ED B7 70 F2 29 B7 19 39 5D',
        'appuser_uuid': '87c24714-80cf-4c86-a3a4-b817f66e2589'
    }
}

product_version_id = {
    'dev': '35040',
    'stg': '49787'
}
cap_policy_name_individual = {
    'dev': 'auto_test_policy_cap_individual_dev',
    'stg': 'auto_test_policy_cap_individual_stg'
}

cap_policy_name_bulk = {
    'dev': 'auto_test_policy_cap_bulk_dev',
    'stg': 'auto_test_policy_cap_bulk_stg'
}

cap_policy_name_bulk_uba = {
    'dev': 'auto_test_policy_cap_bulk_uba_dev',
    'stg': 'auto_test_policy_cap_bulk_uba_stg'
}

appuser_uuids_for_v1_policies_diff_os_users = {
    'dev': 'b19feb25-1d21-4302-8621-d5553ab235d3',
    'stg': 'ffdd6edc-a210-4921-9420-fce874b3d654'
}

appuser_uuids_for_v1_policies_same_os_users = {
    'dev': 'dc9f0277-2ace-4458-92c1-08d5be3ecf34',
    'stg': '3db6914f-94f5-4af9-9fca-5384e052eabc'
}

uuids_for_load_testing = {'dev': '91b9362a-2820-44e4-a30a-c607310307fb', 'stg': '9dc15b21-d208-4cb4-8a56-4d312568bf24'}
uuid_for_cap_v5_load_testing = '781e0e85-7cf6-4e09-8878-d60d3e5da2ee'

chargebee_customer_ids = {
    'dev': ['169ll2TbE1TlyZYW', '169ll2TbDmCgfPlw', 'AzylLzTv1kr9gHvQ', '16CH7uTvOHYI82V0D', '169zTzUlNLOUf19Or'],
    'stg': ['AzZj2xTbEAp4NTHP', '169ll2TbE6zN2ds5', 'AzqLbDTv1sxTtL7e', '16CH7uTvOKMnL2WYl', '16Bl0hUlNNQ3B1CmK']
}

test_card_details = {
    "number": "****************",
    "cvv": "123",
    "expiry_year": 2030,
    "expiry_month": 12
}

active_protect_report_content = {
    'full': {
        'dev': 'Host W53RRRM,4391999b-6b7b-40f2-bb84-27811f66ab04,4v3Kvc4vAsgbPk2oCi2QFZ,4.10.1,10.0.10240,10.0,'
               'Windows 10,Password,,,Pass,Validated',
        'stg': 'Host ONDQCW0,52eb9cbc-92c0-4b90-8548-b126b22db1a0,jMwvWKChYoKh2SehAAKW36,4.10.1,10.0.10240,10.0,'
               'Windows 10,Password,,,Pass,Validated'
    },
    'only_failed_checks': {
        'dev': 'Standard User Account,Host W53RRRM,test test,<EMAIL>,'
               '4391999b-6b7b-40f2-bb84-27811f66ab04,4v3Kvc4vAsgbPk2oCi2QFZ,4.10.1,10.0.10240,10.0,Windows 10',
        'stg': 'Anti-malware installed and running,Host M313G7S,test test,<EMAIL>,'
               'e0140573-e235-4a6d-8af8-ef09d18244a7,96ww85R2NTL9PKSFeZSkhK,4.10.1,10.0.10240,10.0,Windows 10'
    }
}

chargebee_api_token = 'test_aWoiNxdX4F5J1k7iocpcurwZBCsmCCIAh'

ce_survey_body = {
    'dev': {
        'csrfmiddlewaretoken': '',
        'response_3931_1.0': 'Test',
        'response_3933_2.0': 'LTD  - Limited Company (Ltd or PLC)',
        'response_3934_3.0': 'CN001234',
        'response_3936_4.0_address1': 'test address1',
        'response_3936_4.0_address2': 'test address2',
        'response_3936_4.0_city': 'test city',
        'response_3936_4.0_county': 'test county',
        'response_3936_4.0_postcode': 'E1 5JL',
        'response_3936_4.0_country': 'United Kingdom',
        'response_3938_5.0': 'APRE',
        'response_3938_5.0_industry_description': '',
        'response_3939_6.0': 'example.com',
        'response_3940_7.0': 'Renewal',
        'response_3941_8.0_2562': 'on',
        'response_3941_8.0_2565': 'on',
        'response_3942_8.1': 'Test',
        'response_3943_8.2': 'Test',
        'response_3944_8.3': 'Test',
        'response_3945_8.4': 'Test',
        'response_3946_8.5': 'Test',
        'response_3947_9.0': 'on',
        'response_3948_10.0': 'on',
        'response_3950_11.0': 'on',
        'response_3949_11.0': ['Test', 'on'],
        'response_3951_12.0': 'Test',
        'response_3952_13.0': 'Test',
        'unsupported_os_releases': '[]',
        'response_3953_14.0': 'Test',
        'response_3954_14.1': 'Test',
        'response_3955_15.0': 'Test',
        'response_3956_16.0': 'Test',
        'response_3957_17.0': 'Test',
        'response_3958_17.1': 'Test',
        'response_3959_18.0': 'Test',
        'response_3960_19.0': 'Test',
        'response_3961_20.0_name': 'test name',
        'response_3961_20.0_role': 'test role',
        'response_3962_21.0': 'on',
        'response_3963_21.1': 'on',
        'response_3964_21.2': 'Test',
        'response_3965_22.0': 'on',
        'response_3966_22.1': 'Test',
        'response_3967_23.0_2570': 'on',
        'response_3967_23.0_other_value': '',
        'response_3968_24.0': 'on',
        'response_3969_25.0': 'on',
        'response_3970_26.0': 'Test',
        'response_3971_27.0': 'on',
        'response_3972_28.0': 'Test',
        'response_3973_29.0': 'on',
        'response_3974_30.0': 'on',
        'response_3975_31.0': 'Test',
        'response_3976_33.0': 'Test',
        'response_3977_34.0': 'on',
        'response_3978_35.0': 'on',
        'response_3979_36.0': ['Test','on'],
        'response_3980_37.0_2575': 'on',
        'response_3980_37.0_other_value': '',
        'response_3981_38.0': 'Test',
        'response_3982_39.0': 'A. Throttling the rate of attempts',
        'response_3983_40.0': 'on',
        'response_3984_41.0': 'on',
        'response_3985_42.0': 'Test',
        'response_3986_43.0': 'on',
        'response_3987_44.0': 'on',
        'response_3988_44.1': 'Test',
        'response_3989_44.2': 'Test',
        'response_3990_44.3': 'Test',
        'response_3991_44.4': 'Test',
        'response_3992_45.0': 'on',
        'response_3993_45.1': 'Test',
        'response_3994_46.0': 'on',
        'response_3995_46.1': 'on',
        'response_3996_46.2': 'Test',
        'response_3997_47.0': 'on',
        'response_3998_47.1': 'on',
        'response_3999_47.2': 'Test',
        'response_4000_48.0': 'on',
        'response_4001_49.0': 'Test',
        'response_4002_50.0': 'Test',
        'response_4003_51.0': 'on',
        'response_4004_52.0': 'Test',
        'response_4005_53.0': 'Test',
        'response_4006_54.0': 'Test',
        'response_4007_55.0': 'Test',
        'response_4008_56.0': 'Test',
        'response_4009_57.0': 'on',
        'response_4010_58.0': 'on',
        'response_4011_59.0': 'Test',
        'response_4012_60.0': 'Test',
        'response_4013_61.0': 'Test',
        'response_4014_62.0': 'on',
        'response_4015_63.0': 'on',
        'response_4016_64.0': 'Test',
        'response_4017_65.0': 'on',
        'response_4018_66.0': 'on',
        'response_4019_67.0_2583': 'on',
        'response_4019_67.0_other_value': '',
        'response_4020_68.0': 'on',
        'response_4021_69.0': 'on',
        'response_4022_70.0': 'on',
        'response_4023_71.0': 'on',
        'response_3930_1.0': 'on',
        'response_3932_2.0': 'off',
        'response_3935_3.0': 'We already have Cyber Insurance',
        'response_3937_4.0': '<EMAIL>',
        'submit-survey-signal': 'submit-survey-signal'
    },
    'stg': {
        'csrfmiddlewaretoken': '',
        'response_5218_1.0': 'Test',
        'response_5220_2.0': 'LTD  - Limited Company (Ltd or PLC)',
        'response_5221_3.0': 'CN001234',
        'response_5223_4.0_address1': 'test address1',
        'response_5223_4.0_address2': 'test address2',
        'response_5223_4.0_city': 'test city',
        'response_5223_4.0_county': 'test county',
        'response_5223_4.0_postcode': 'E1 5JL',
        'response_5223_4.0_country': 'United Kingdom',
        'response_5225_5.0': 'APRE',
        'response_5225_5.0_industry_description': '',
        'response_5226_6.0': 'example.com',
        'response_5227_7.0': 'Renewal',
        'response_5228_8.0_4047': 'on',
        'response_5228_8.0_4049': 'on',
        'response_5229_8.1': 'Test',
        'response_5230_8.2': 'Test',
        'response_5231_8.3': 'Test',
        'response_5232_8.4': 'Test',
        'response_5233_8.5': 'Test',
        'response_5234_9.0': 'on',
        'response_5235_10.0': 'on',
        'response_5236_11.0': 'on',
        'response_5237_11.0': ['Test','on'],
        'response_5238_12.0': 'Test',
        'response_5239_13.0': 'Test',
        'unsupported_os_releases': '[]',
        'response_5240_14.0': 'Test',
        'response_5241_14.1': 'Test',
        'response_5242_15.0': 'Test',
        'response_5243_16.0': 'Test',
        'response_5244_17.0': 'Test',
        'response_5245_17.1': 'Test',
        'response_5246_18.0': 'Test',
        'response_5247_19.0': 'Test',
        'response_5248_20.0_name': 'test name',
        'response_5248_20.0_role': 'test role',
        'response_5249_21.0': 'on',
        'response_5250_21.1': 'on',
        'response_5251_21.2': 'Test',
        'response_5252_22.0': 'on',
        'response_5253_22.1': 'Test',
        'response_5254_23.0_4055': 'on',
        'response_5254_23.0_other_value': '',
        'response_5255_24.0': 'on',
        'response_5256_25.0': 'on',
        'response_5257_26.0': 'Test',
        'response_5258_27.0': 'on',
        'response_5259_28.0': 'Test',
        'response_5260_29.0': 'on',
        'response_5261_30.0': 'on',
        'response_5262_31.0': 'Test',
        'response_5263_33.0': 'Test',
        'response_5264_34.0': 'on',
        'response_5265_35.0': 'on',
        'response_5266_36.0': ['Test','on'],
        'response_5267_37.0_4060': 'on',
        'response_5267_37.0_other_value': '',
        'response_5268_38.0': 'Test',
        'response_5269_39.0': 'A. Throttling the rate of attempts',
        'response_5270_40.0': 'on',
        'response_5271_41.0': 'on',
        'response_5272_42.0': 'Test',
        'response_5273_43.0': 'on',
        'response_5274_44.0': 'on',
        'response_5275_44.1': 'Test',
        'response_5276_44.2': 'Test',
        'response_5277_44.3': 'Test',
        'response_5278_44.4': 'Test',
        'response_5279_45.0': 'on',
        'response_5280_45.1': 'Test',
        'response_5281_46.0': 'on',
        'response_5282_46.1': 'on',
        'response_5283_46.2': 'Test',
        'response_5284_47.0': 'on',
        'response_5285_47.1': 'on',
        'response_5286_47.2': 'Test',
        'response_5287_48.0': 'on',
        'response_5288_49.0': 'Test',
        'response_5289_50.0': 'Test',
        'response_5290_51.0': 'on',
        'response_5291_52.0': 'Test',
        'response_5292_53.0': 'Test',
        'response_5293_54.0': 'Test',
        'response_5294_55.0': 'Test',
        'response_5295_56.0': 'Test',
        'response_5296_57.0': 'on',
        'response_5297_58.0': 'on',
        'response_5298_59.0': 'Test',
        'response_5299_60.0': 'Test',
        'response_5300_61.0': 'Test',
        'response_5301_62.0': 'on',
        'response_5302_63.0': 'on',
        'response_5303_64.0': 'Test',
        'response_5304_65.0': 'on',
        'response_5305_66.0': 'on',
        'response_5306_67.0_4068': 'on',
        'response_5306_67.0_other_value': '',
        'response_5307_68.0': 'on',
        'response_5308_69.0': 'on',
        'response_5309_70.0': 'on',
        'response_5310_71.0': 'on',
        'response_5217_1.0': 'on',
        'response_5219_2.0': 'off',
        'response_5222_3.0': 'We already have Cyber Insurance',
        'response_5224_4.0': '<EMAIL>',
        'submit-survey-signal': 'submit-survey-signal'
    }
}

webhook_body = {
        "data": {
            "created": "2024-01-08T12:23:20Z",
            "customer_id": "",
            "deleted": False,
            "email": "",
            "emails_sent": 1,
            "enrolled_on": "2024-01-08T12:30:17Z",
            "id": "",
            "last_seen": "",
            "managed_by": "CONTROL",
            "name": "",
            "platform": "android",
            "device_model": "auto_test_device_model",
            "device_make": "OnePlus",
            "device_os_version": "11",
            "test_data": False,
            "risk_rating": {
                "rating": "HIGH",
                "high_risk_count": 0,
                "low_risk_count": 0,
                "calculation_source": "DEVICE",
                "medium_risk_count": 0,
                "updated": "2024-01-08T12:30:24Z"
            },
            "app_version": "10.35",
            "status": "ACTIVE",
            "device_type": "CORPORATE",
            "device_risk_state": {
                "risk_rating": "HIGH",
                "calculated_risk_rating": "HIGH",
                "risks": [
                    {
                        "indicator": "Your device disconnected from a WiFi network",
                        "risk_rating": "INFO",
                        "result_code": 1180,
                        "created_local": "2024-01-16T08:47:46.895+0000",
                        "category": "WIFI",
                        "created_utc": "2024-01-16T08:47:46.895+0000",
                        "calculated_risk_rating": "INFO"
                    },
                    {
                        "indicator": "Web protection configured",
                        "risk_rating": "INFO",
                        "result_code": 1560,
                        "created_local": "2024-01-16T08:49:02.763+0000",
                        "category": "LINK_CHECKER_CONFIGURATION",
                        "created_utc": "2024-01-16T08:49:02.763+0000",
                        "calculated_risk_rating": "INFO"
                    },
                    {
                        "indicator": "Device password protected",
                        "risk_rating": "INFO",
                        "result_code": 1290,
                        "created_local": "2024-01-08T12:30:51.442+0000",
                        "category": "PIN_NOT_SET",
                        "created_utc": "2024-01-08T12:30:51.442+0000",
                        "calculated_risk_rating": "INFO"
                    },
                    {
                        "indicator": "Android Debug Bridge disabled",
                        "risk_rating": "INFO",
                        "result_code": 1230,
                        "created_local": "2024-01-08T12:30:51.338+0000",
                        "category": "ADB_ENABLED",
                        "created_utc": "2024-01-08T12:30:51.338+0000",
                        "calculated_risk_rating": "INFO"
                    },
                    {
                        "indicator": "Google Play Protect enabled",
                        "risk_rating": "INFO",
                        "result_code": 1223,
                        "created_local": "2024-01-08T12:30:51.500+0000",
                        "category": "GPPS_DISABLED",
                        "created_utc": "2024-01-08T12:30:51.500+0000",
                        "calculated_risk_rating": "INFO"
                    },
                    {
                        "indicator": "OS supported by Android",
                        "risk_rating": "INFO",
                        "result_code": 1204,
                        "created_local": "2024-01-08T12:30:51.415+0000",
                        "category": "OS_OUT_OF_DATE",
                        "created_utc": "2024-01-08T12:30:51.415+0000",
                        "calculated_risk_rating": "INFO"
                    },
                    {
                        "indicator": "No Root Access Detected",
                        "risk_rating": "INFO",
                        "result_code": 1280,
                        "created_local": "2024-01-08T12:30:51.473+0000",
                        "category": "ROOTED",
                        "created_utc": "2024-01-08T12:30:51.473+0000",
                        "calculated_risk_rating": "INFO"
                    },
                    {
                        "indicator": "Device requires update",
                        "risk_rating": "HIGH",
                        "result_code": 1201,
                        "created_local": "2024-01-08T12:30:51.364+0000",
                        "category": "NOT_PATCHED",
                        "created_utc": "2024-01-08T12:30:51.364+0000",
                        "calculated_risk_rating": "HIGH"
                    }
                ]
            },
            "external_device_id": "string",
            "external_user_id": "string",
            "external_verified_on": "2024-01-04T08:56:08.973Z"
        },
        "client_id": "",
        "client_secret": ""
    }

opswat_payload = {
        "device_id": "",
        "serial_number": "",
        "os_users_results": [
            {
                "os_user_name": "TestUser",
                "os_user_domain": "",
                "software": [
                    {
                        "product": {
                            "id": 100014,
                            "name": "Firefox"
                        },
                        "vendor": {
                            "id": 100012,
                            "name": "Mozilla Corporation"
                        },
                        "version": "136.0.4",
                        "categories": [
                            4,
                            6
                        ],
                        "cves": [
                            {
                              "cve_id": "CVE-2025-3030",
                              "description": "Memory safety bugs present in Firefox 136, Thunderbird 136, Firefox ESR 128.8, and Thunderbird 128.8. Some of these bugs showed evidence of memory corruption and we presume that with enough effort some of these could have been exploited to run arbitrary code.",
                              "id": 20253030,
                              "published_at": "2025-04-02T09:12:12.000Z",
                              "severity": "IMPORTANT",
                              "severity_index": 67,
                              "details": {
                                "security_protection": "",
                                "resolution": [
                                  {
                                    "higher_than_or_equal_to_version": "137.0",
                                    "product_name": "Firefox",
                                    "product_id": 100014
                                  }
                                ],
                                "references": [
                                  {
                                    "url": "https://bugzilla.mozilla.org/buglist.cgi?bug_id=1850615"
                                  }
                                ],
                                "last_modified_epoch": 1744103533,
                                "opswat_products_info": [],
                                "cwe": "CWE-416",
                                "cvss_3_0": {
                                  "availability_impact": "HIGH",
                                  "confidentiality_impact": "HIGH",
                                  "impact_score": "5.9",
                                  "base_score": "8.1",
                                  "attack_complexity": "HIGH",
                                  "exploitability_score": "2.2",
                                  "user_interaction": "NONE",
                                  "integrity_impact": "HIGH",
                                  "privileges_required": "NONE",
                                  "scope": "UNCHANGED",
                                  "base_severity": "HIGH",
                                  "attack_vector": "NETWORK",
                                  "vector_string": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H"
                                },
                                "published_epoch": 1743585132,
                                "cvss_2_0": {
                                  "access_complexity": "",
                                  "confidentiality_impact": "",
                                  "access_vector": "",
                                  "authentication": "",
                                  "score": "",
                                  "availability_impact": "",
                                  "generated_on_epoch": 0,
                                  "source": "",
                                  "integrity_impact": ""
                                },
                                "cpe": ""
                              }
                            }
                        ],
                        "signature": {
                            "id": 100292,
                            "name": "Firefox"
                        }
                    }
                ]
            }
        ],
        "app_user_uuid": ""
    }
