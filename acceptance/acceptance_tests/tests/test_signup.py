from selenium.common import TimeoutException

from acceptance.acceptance_tests.helpers.api_helper.billing import get_subscriptions_details_via_API
from acceptance.acceptance_tests.helpers.common_functions import logout
from acceptance.acceptance_tests.helpers.custom_decorators import mark_as_known_failure_on_exception
from acceptance.acceptance_tests.helpers.date_util import get_transformed_date
from acceptance.acceptance_tests.helpers.test_logger import step
from acceptance.acceptance_tests.pages.admin.direct_chargebee_subs_admin import DirectChargeBeeSubsAdminPage
from acceptance.acceptance_tests.pages.admin.organisations_admin import OrgAdminPage
from acceptance.acceptance_tests.pages.non_admin.aviva_insurance import AvivaInsurancePage
from acceptance.acceptance_tests.pages.non_admin.customer_info import CustomerInfoPage
from acceptance.acceptance_tests.pages.non_admin.org_dashboard import IndividualOrgDashboardPage
from acceptance.acceptance_tests.pages.non_admin.payment import PaymentPage
from acceptance.acceptance_tests.pages.non_admin.terms import TermsPage
from acceptance.acceptance_tests.steps.common_steps import CommonSteps
from acceptance.properties_manager import get_auto_test_org_prefix


# def test_correct_plan_shows_on_customer_info_page_CUSTEAM_T679(driver, base_url):
#     plan_short = 'Cyber Essentials & IASME GDPR Ready'
#     plan_full = 'Cyber Essentials & IASME GDPR with CyberSmart Active Protect'
#     company_size = '2 - 4 people'
#     subs_type = 'annual'
#
#     step('Given a user is on the plans page')
#     plans_page = go_to_plans_page(driver, base_url)
#
#     step(f'When the company size of "{company_size}" has been selected')
#     plans_page.select_company_size(company_size)
#
#     step(f'And the subscription type of "{subs_type}" has been chosen')
#     plans_page.choose_subscription_type(subs_type)
#
#     step(f'And the buy now button has been clicked for the plan "{plan_short}"')
#     plans_page.buy_now(plan_short)
#
#     step(f'Then the customer info page shows the correct plan "{plan_full}"')
#     customer_info_page = CustomerInfoPage(driver)
#     customer_info_page.wait_until_page_displayed()
#     assert customer_info_page.shows_correct_plan(plan_full)


def test_1_person_gets_free_active_protect_CUSTEAM_T680(driver, base_url):
    company_size = '1 person'
    expected_software_product_name = 'with FREE CyberSmart Active Protect'

    step('Given a user is on the plans page')
    common_steps = CommonSteps(driver)
    plans_page = common_steps.go_to_plans_page(base_url)

    step(f'When the company size of "{company_size}" has been selected')
    plans_page.select_company_size(company_size)

    step(f'Then all the plans have the text "{expected_software_product_name}"')
    actual_software_product_names = plans_page.get_software_product_names_on_cards()
    for name in actual_software_product_names:
        assert name.text == expected_software_product_name


def test_35_plus_people_need_quote_CUSTEAM_T681(driver, base_url):
    company_size = '35+ people'
    expected_choose_button_text = 'Request Quote'

    step('Given a user is on the plans page')
    common_steps = CommonSteps(driver)
    plans_page = common_steps.go_to_plans_page(base_url)

    step(f'When the company size of "{company_size}" has been selected')
    plans_page.select_company_size(company_size)

    step(f'Then all the plans include a button labelled "{expected_choose_button_text}"')
    choose_buttons = plans_page.get_choose_buttons()
    for button in choose_buttons:
        assert button.text == expected_choose_button_text


def test_certs_plan_filter_CUSTEAM_T682(driver, base_url):
    step('Given a user is on the plans page')
    common_steps = CommonSteps(driver)
    plans_page = common_steps.go_to_plans_page(base_url)

    step('When "View certifications only" is selected')
    plans_page.view_certs_only()

    step('Then only certification plans are displayed')
    assert plans_page.only_cert_plans_displayed()

    step('When "I already have Cyber Essentials" is also selected')
    plans_page.view_certs_without_ce_only()

    step('Then only certification plans without Cyber Essentials are displayed')
    assert plans_page.only_cert_plans_without_ce_displayed()

def test_edit_plans_CUSTEAM_T683(driver, base_url):
    plan_short = 'Cyber Essentials'

    step('Given a user is on the plans page')
    common_steps = CommonSteps(driver)
    plans_page = common_steps.go_to_plans_page(base_url)

    step('And the buy now button has been clicked for a plan')
    plans_page.buy_now(plan_short)

    step('And the customer info page is displayed')
    customer_info_page = CustomerInfoPage(driver)
    customer_info_page.wait_until_page_displayed()

    step('When the "Edit plan button" is clicked')
    customer_info_page.edit_plan()

    step('Then the user is returned to the plans page')
    plans_page.wait_until_page_loads()

    step(f'When the buy now button has been clicked for "{plan_short}"')
    plans_page.buy_now(plan_short)

    step('And the customer info page is displayed')
    customer_info_page.wait_until_page_displayed()

    step('And the "return to plan select link" is clicked')
    customer_info_page.return_to_plan_select()

    step('Then the user is returned to the plans page')
    plans_page.wait_until_page_loads()


def test_checkout_page_has_correct_info_CUSTEAM_T684(driver, base_url):
    plan_short = 'Cyber Essentials & Cyber Essentials Plus'
    first_name = 'test first name'
    last_name = 'test last name'
    phone = '02012341234'
    company = f'{get_auto_test_org_prefix()}{get_transformed_date()}'
    email = f'test{get_transformed_date()}@example.com'

    step('Given a user is on the plans page')
    common_steps = CommonSteps(driver)
    plans_page = common_steps.go_to_plans_page(base_url)

    step(f'When the buy now button has been clicked for "{plan_short}"')
    plans_page.buy_now(plan_short)

    step('When personal details are entered into the customer info page')
    step('And the personal details are submitted')
    customer_info_page = CustomerInfoPage(driver)
    customer_info_page.wait_until_page_displayed()
    customer_info_page.enter_customer_info(first_name, last_name, email, phone, company)

    step('Then the payment page shows those personal details correctly')
    payment_page = PaymentPage(driver)
    assert payment_page.checkout_summary_info_displayed('Name', f'{first_name} {last_name}')
    assert payment_page.checkout_summary_info_displayed('Telephone', phone)
    assert payment_page.checkout_summary_info_displayed('Email', email)
    assert payment_page.checkout_summary_info_displayed('Company', company)


# Marked as known failure due to the Terms link going to the wrong page
@mark_as_known_failure_on_exception(TimeoutException)
def test_terms_and_privacy_policy_CUSTEAM_T685(driver, base_url):
    plan_short = 'Cyber Essentials'

    step('Given a plan has been selected')
    common_steps = CommonSteps(driver)
    plans_page = common_steps.go_to_plans_page(base_url)
    plans_page.buy_now(plan_short)

    step('And the user is on the customer info page')
    customer_info_page = CustomerInfoPage(driver)
    customer_info_page.wait_until_page_displayed()

    step('When the terms link is clicked')
    customer_info_page.click_terms()
    terms_window = driver.window_handles[1]

    step('Then the terms page can be accessed')
    driver.switch_to.window(terms_window)
    terms_page = TermsPage(driver)
    assert terms_page.page_title_displayed()

#commented out to review flakiness
# @pytest.mark.flaky(reruns=1, reruns_delay=2)
# def test_emailflow_direct_user_signs_up_for_CE_only_and_makes_payment_CUSTEAM_T738(driver, base_url):
#     common_steps = CommonSteps(driver)
#     user_details = common_steps.signup_and_make_payment(
#         base_url, 'direct-customer-v4-ce-only-monthly', get_email_domain())
#     user_email = user_details.get('user_email')
#     step('And the CyberSmart welcome email is received')
#     email_subject = 'Welcome to CyberSmart'
#     email_body = 'set your password'
#     email = common_steps.check_email_using_email_body(user_email, email_subject, email_body)
#
#     step('And the set password link in the email leads to the set password page')
#     set_password_link = common_steps.get_link_from_email(email, 'Set Password')
#     common_steps.go_to_page_via_email_link(set_password_link, base_url)
#     set_password_pg = SetPasswordPage(driver)
#     set_password_pg.change_password_button_is_displayed()
#     assert set_password_pg.password_fields_are_displayed()


def test_aviva_coupon_is_used_during_signup_CUSTEAM_T758(driver, base_url, username, password):
    common_steps = CommonSteps(driver)
    coupon = 'AVIVACUSTOMER'
    user_details = common_steps.signup_and_make_payment(
        base_url, 'direct-customer-v4-ce-only-monthly', 'example.com', coupon)

    step('And the Aviva info nav link is displayed')
    org_dashboard_pg = IndividualOrgDashboardPage(driver)
    org_dashboard_pg.nav_bar_link_is_displayed('Aviva info')

    step('And the Aviva info page can be accessed')
    org_uuid = org_dashboard_pg.get_org_uuid()
    aviva_insurance_page = AvivaInsurancePage(driver)
    aviva_insurance_page.go_to_page(base_url, org_uuid)
    aviva_insurance_page.wait_until_page_is_displayed()

    step('And the customer type in django is Aviva')
    logout(driver, base_url)
    common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
    org_admin_page = OrgAdminPage(driver)
    org_id = org_admin_page.get_org_id(base_url, user_details.get('org_name'))
    assert org_admin_page.get_customer_type(base_url, org_id) == 'Aviva'

    step('And the coupon is visible in chargebee')
    customer_id = user_details.get('customer_id')
    subs_admin_page = DirectChargeBeeSubsAdminPage(driver)
    subs_admin_page.search(base_url, customer_id)
    subs_id = subs_admin_page.get_subscription_id()

    subscription_details = get_subscriptions_details_via_API(subs_id)
    assert subscription_details.get('subscription').get('coupon') == coupon
