from datetime import datetime, timedelta

import pytest

from acceptance.acceptance_tests.helpers.api_helper.billing import purchase_subscription_via_API, \
    request_cancellation_via_API
from acceptance.acceptance_tests.helpers.api_helper.certos import start_survey_via_API
from acceptance.acceptance_tests.helpers.api_helper.general import add_purchase_order_via_API, impersonate_user_via_API
from acceptance.acceptance_tests.helpers.api_helper.org_creation import (
    create_custom_plan_CE_only_org_via_API,
    create_custom_individual_deployment_org_with_CE_and_privacy_toolbox, create_custom_org_with_CE_only_via_API,
    create_core_org_via_API)
from acceptance.acceptance_tests.helpers.common_functions import logout
from acceptance.acceptance_tests.helpers.date_util import get_transformed_date
from acceptance.acceptance_tests.helpers.test_logger import step
from acceptance.acceptance_tests.helpers.wait_statements import wait_until_cmd_executed, wait_until_command_executed
from acceptance.acceptance_tests.pages.admin.partner_chargebee_subs_admin import PartnerChargeBeeSubsAdminPage
from acceptance.acceptance_tests.pages.non_admin.rnr_toolbox import RnrToolboxPage
from acceptance.acceptance_tests.pages.non_admin.subscriptions import SubscriptionsPage
from acceptance.acceptance_tests.steps.common_steps import CommonSteps
from acceptance.properties_manager import get_username, get_auto_test_org_prefix, \
    get_chargebee_api_token, get_impersonated_user_id


def update_chargebee_term_end_date(subscription_id, days_till_term_end):
    import chargebee
    from datetime import datetime, timedelta
    chargebee.configure(get_chargebee_api_token(), "cybersmart-test")
    new_term_end_date = datetime.now() + timedelta(days=days_till_term_end)
    timestamp = int(new_term_end_date.timestamp())
    chargebee.Subscription.change_term_end(
        subscription_id,
        {"term_ends_at": timestamp}
    )

def request_renewal_cancellation(driver, base_url, username, password, days_till_term_end):
    direct_partner_username = get_username('direct_partner_user', base_url)
    step(f'Given a direct partner user {direct_partner_username} is logged in')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, direct_partner_username, password)

    step('And a new custom org with CE only is added')
    org_details = create_custom_org_with_CE_only_via_API(driver, base_url)

    step('And the cert survey has begun')
    start_survey_via_API(driver, base_url, org_details.get('org_uuid'))

    step('And the subscription page shows an active subscription')
    org_name = org_details.get('org_name')
    subscriptions_pg = SubscriptionsPage(driver)
    subscriptions_pg.go_to_specific_subscription(base_url, org_name)
    next_billing_date = subscriptions_pg.get_next_billing_date(org_name)
    one_year_later = datetime.now() + timedelta(days=365)
    next_billing_year = one_year_later.year
    assert str(next_billing_year) in next_billing_date

    step(f'When the current term end date is set to {days_till_term_end} days from now')
    logout(driver, base_url)
    common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
    partner_chargebee_subs_admin_pg = PartnerChargeBeeSubsAdminPage(driver)
    chargebee_subs_id = partner_chargebee_subs_admin_pg.get_chargebee_subs_id(base_url, org_name)
    update_chargebee_term_end_date(chargebee_subs_id, days_till_term_end)

    step('And the request cancellation option is available')
    logout(driver, base_url)
    common_steps.login(base_url, direct_partner_username, password)
    subscriptions_pg.go_to_specific_subscription(base_url, org_name)
    confirm_that_term_end_date_has_been_changed(driver, base_url, org_name)
    subscriptions_pg.request_cancellation_option_is_present()

    step('And a renewal cancellation request is made')
    request_cancellation_via_API(driver, base_url, chargebee_subs_id)

    def action():
        subscriptions_pg.go_to_specific_subscription(base_url, org_name)
        subscriptions_pg.cancel_request_sent_label_is_displayed()

    wait_until_cmd_executed(action, max_tries=3, polling_seconds=0.5)
    return {'org_name': org_name, 'chargebee_subs_id': chargebee_subs_id}


def confirm_that_term_end_date_has_been_changed(driver, base_url, org_name):
    now = datetime.now()

    def action():
        subscriptions_pg = SubscriptionsPage(driver)
        subscriptions_pg.go_to_specific_subscription(base_url, org_name)
        assert str(now.year) in subscriptions_pg.get_next_billing_date(org_name)

    wait_until_command_executed(action, max_tries=6, polling_seconds=10, specific_exception=AssertionError)

#commented out due to changes in CUSTEAM-2021: auto tests needs to be updated
# def test_automatic_subscriptions_for_new_non_iasme_orgs_CUSTEAM_T706(driver, base_url, password):
#     user_type = 'non_iasme_partner_user'
#     username = get_username(user_type, base_url)
#     step(f'Given a non-IASME partner {username} is logged in')
#     common_steps = CommonSteps(driver)
#     common_steps.login(base_url, username, password)
#
#     step('And a new custom org with CE only is added')
#     org_details = create_custom_org_with_CE_only_via_API(driver, base_url)
#
#     step('And there is currently no subscriptions for the new org')
#     org_name = org_details.get('org_name')
#     subscriptions_pg = SubscriptionsPage(driver)
#     subscriptions_pg.go_to_specific_subscription(base_url, org_name)
#     subscriptions_pg.ce_survey_has_not_started(org_name)
#     subscriptions_pg.subscription_does_not_exist(org_name, 'Cyber Essentials')
#
#     step('When the cert survey has begun')
#     start_survey_via_API(driver, base_url, org_details.get('org_uuid'))
#
#     step('Then the subscription page shows an active subscription')
#     subscriptions_pg.go_to_specific_subscription(base_url, org_name)
#     next_billing_date = subscriptions_pg.get_next_billing_date(org_name)
#     one_year_later = datetime.now() + timedelta(days=365)
#     next_billing_year = one_year_later.year
#     assert str(next_billing_year) in next_billing_date
#
#     step('And the plan name is as expected')
#     assert subscriptions_pg.get_plan_name(org_name) == 'par-v4-ce-an-d'
#
#     step('And the price format is as expected')
#     subscription_details = subscriptions_pg.get_subscription_details(org_name)
#     assert '£' in subscription_details and '/year' in subscription_details

@pytest.mark.flaky(reruns=1, reruns_delay=2)
def test_purchase_order_is_added_to_subscription_CUSTEAM_T742(driver, base_url, password):
    user_type = 'non_iasme_partner_user'
    username = get_username(user_type, base_url)
    step(f'Given a non-IASME partner user {username} is logged in')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)

    step('And a new custom org with CE only is added')
    org_details = create_custom_org_with_CE_only_via_API(driver, base_url)

    step('And the cert survey has begun')
    start_survey_via_API(driver, base_url, org_details.get('org_uuid'))

    step('And the subscription page shows an active subscription')
    org_name = org_details.get('org_name')
    subscriptions_pg = SubscriptionsPage(driver)
    subscriptions_pg.go_to_specific_subscription(base_url, org_name)
    next_billing_date = subscriptions_pg.get_next_billing_date(org_name)
    one_year_later = datetime.now() + timedelta(days=365)
    next_billing_year = one_year_later.year
    assert str(next_billing_year) in next_billing_date

    step('When a purchase order is added')
    purchase_order_number = get_transformed_date()
    subscription_id = subscriptions_pg.get_subscription_id(org_name)
    add_purchase_order_via_API(driver, base_url, purchase_order_number, subscription_id)

    step('And the purchase order is displayed on the page')
    subscriptions_pg.refresh()
    product_title = 'Cyber Essentials'
    assert subscriptions_pg.get_purchase_order_on_table(org_name, product_title) == purchase_order_number
    assert subscriptions_pg.get_purchase_order_on_modal(org_name, product_title) == purchase_order_number

    step('And the purchase order is downloaded')
    purchase_order_pdf_text = subscriptions_pg.get_purchase_order_pdf_text(org_name, product_title)

    step('Then the purchase order pdf content is as expected')
    assert 'Auto test purchase order document' in purchase_order_pdf_text


def test_charity_voucher_applies_discount_CUSTEAM_T752(driver, base_url, password):
    user_type = 'non_iasme_partner_user'
    username = get_username(user_type, base_url)
    step(f'Given a non-IASME partner user {username} is logged in')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)

    voucher_code = '100POUNDSFORCHARITIES'
    industry = 'CHAR'
    step(f'When an individual deployment core org of type {industry}'
         f' is created with a charity voucher code {voucher_code}')
    org_name = f'{get_auto_test_org_prefix()}{get_transformed_date()}'
    create_core_org_via_API(driver, base_url, org_name, 'individual', voucher_code, industry)

    step('Then the discount is reflected in the subscriptions overview page')
    subscriptions_pg = SubscriptionsPage(driver)
    subscriptions_pg.go_to_specific_subscription(base_url, org_name)
    subscription_details = subscriptions_pg.get_subscription_details(org_name)
    assert "- £100.0 discount" in subscription_details


def test_only_charity_orgs_can_use_charity_voucher_CUSTEAM_T753(driver, base_url, password):
    user_type = 'non_iasme_partner_user'
    username = get_username(user_type, base_url)
    step(f'Given a non-IASME partner {username} is logged in')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)

    voucher_code = '100POUNDSFORCHARITIES'
    industry = 'APRE'
    step(f'When an individual deployment core org of type {industry}'
         f' is created with a charity voucher code {voucher_code}')
    org_name = f'{get_auto_test_org_prefix()}{get_transformed_date()}'
    response = create_core_org_via_API(driver, base_url, org_name, 'individual', voucher_code, industry)

    step('Then an error message is displayed indicating the coupon is not valid for the org type')
    assert 'Coupon with this code is not valid for this type of organisation industry' in response.text


def test_privacy_toolbox_can_be_purchased_after_org_creation_CUSTEAM_T759(driver, base_url, username, password):
    step('Given a user is logged in')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)

    step('And a new custom org with CE only is added')
    org_details = create_custom_org_with_CE_only_via_API(driver, base_url)

    step('When the subscriptions overview page is accessed')
    org_name = org_details.get('org_name')
    subscriptions_pg = SubscriptionsPage(driver)
    subscriptions_pg.go_to_specific_subscription(base_url, org_name)

    step("And the privacy toolbox subscription does not exist")
    product_title = "Privacy Toolbox"
    subscriptions_pg.subscription_does_not_exist(org_name, product_title)

    step('Then the "purchase subscription" button for privacy toolbox is displayed')
    subscriptions_pg.purchase_subscription_btn_is_displayed(org_name)

    step('And privacy toolbox can be purchased')
    purchase_subscription_via_API(driver, base_url, org_details.get('org_uuid'), 'Privacy Toolbox', 'gdpr')
    subscriptions_pg.go_to_specific_subscription(base_url, org_name)
    subscriptions_pg.subscription_is_active(org_name, product_title)


def test_custom_plan_reflects_correctly_on_subs_overview_page_CUSTEAM_T760(driver, base_url, username, password):
    step('Given a user is logged in')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)

    step("And another user is impersonated")
    impersonate_user_via_API(driver, base_url, get_impersonated_user_id(base_url))

    step('When a custom plan is purchased for CE only')
    org_name = create_custom_plan_CE_only_org_via_API(driver, base_url)

    step('Then the subscriptions overview page reflects this plan correctly')
    subs_page = SubscriptionsPage(driver)
    subs_page.go_to_specific_subscription(base_url, org_name)
    subs_page.subscription_is_active(org_name)
    subscription_details = subs_page.get_subscription_details(org_name)
    assert 'partner-bundle-ce-annual' in subscription_details.lower()


def test_direct_user_can_purchase_rnr_toolbox_CUSTEAM_T763(driver, base_url):
    common_steps = CommonSteps(driver)
    common_steps.signup_and_make_payment(
        base_url, 'direct-customer-v4-ce-only-monthly', 'example.com')

    step('And navigates to the R&R toolbox page')
    common_steps.go_to_home_page(base_url)
    org_dashboard_url = common_steps.get_current_url()
    org_uuid = org_dashboard_url.split('organisation/')[1].split("/")[0]
    rnr_toolbox_pg = RnrToolboxPage(driver)
    rnr_toolbox_pg.go_to_page(base_url, org_uuid)

    step('When R&R toolbox is purchased')
    purchase_subscription_via_API(driver, base_url, org_uuid, 'R&R Toolbox', 'r_and_r_toolbox_monthly')

    step('Then the R&R post purchase page is displayed')
    rnr_toolbox_pg = RnrToolboxPage(driver)
    rnr_toolbox_pg.go_to_page(base_url, org_uuid)
    rnr_toolbox_pg.post_purchase_page_is_displayed()


@pytest.mark.flaky(reruns=1, reruns_delay=2)
def test_purchase_order_is_added_when_CE_started_CUSTEAM_T764(driver, base_url, password):
    user_type = 'non_iasme_partner_user'
    username = get_username(user_type, base_url)
    step(f'Given a non-IASME partner user {username} is logged in')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)

    step('And creates a custom individual deployment, CE and privacy toolbox org with a purchase order')
    po_number = f'PO_{get_transformed_date()}'
    org_details = create_custom_individual_deployment_org_with_CE_and_privacy_toolbox(driver, base_url, po_number)
    org_name = org_details.get('org_name')

    step('And the subscriptions overview page shows the same purchase order for CAP and privacy toolbox only')
    subs_page = SubscriptionsPage(driver)
    subs_page.go_to_specific_subscription(base_url, org_name)
    privacy_toolbox_title = 'Privacy Toolbox'
    cap_title = 'CyberSmart Active Protect'
    ce_title = 'Cyber Essentials'
    assert subs_page.get_purchase_order_on_table(org_name, privacy_toolbox_title) == po_number
    assert subs_page.get_purchase_order_on_table(org_name, cap_title) == po_number
    subs_page.subscription_does_not_exist(org_name, ce_title)

    step('When the CE survey has begun')
    start_survey_via_API(driver, base_url, org_details.get('org_uuid'))

    step('Then the subscriptions over page shows the same purchase order for CE, CAP and privacy toolbox')
    subs_page.go_to_specific_subscription(base_url, org_name)
    assert subs_page.get_purchase_order_on_table(org_name, privacy_toolbox_title) == po_number
    assert subs_page.get_purchase_order_on_table(org_name, cap_title) == po_number
    assert subs_page.get_purchase_order_on_table(org_name, ce_title) == po_number

#commented out due to changes in CUSTEAM-2021: auto tests needs to be updated
# @pytest.mark.flaky(reruns=1, reruns_delay=2)
# def test_renewal_cancellation_90_to_30_days_CUSTEAM_T974(driver, base_url, username, password):
#     subs_details = request_renewal_cancellation(driver, base_url, username, password, days_till_term_end=40)
#     org_name = subs_details.get('org_name')
#     chargebee_subs_id = subs_details.get('chargebee_subs_id')
#
#     step('Then the cancellation request can be actioned')
#     logout(driver, base_url)
#     common_steps = CommonSteps(driver)
#     common_steps.login(base_url, username, password)
#     impersonate_user_via_API(driver, base_url, get_impersonated_user_id(base_url))
#     confirm_cancellation_via_API(driver, base_url, chargebee_subs_id)
#     subscriptions_pg = SubscriptionsPage(driver)
#
#     def action():
#         subscriptions_pg.go_to_specific_subscription(base_url, org_name)
#         subscriptions_pg.renewal_cancelled_label_is_displayed()
#
#     wait_until_cmd_executed(action, max_tries=6, polling_seconds=0.5)
#

#commented out due to changes in CUSTEAM-2021: auto tests needs to be updated
# @pytest.mark.flaky(reruns=1, reruns_delay=2)
# def test_renewal_cancellation_can_be_stopped_CUSTEAM_T975(driver, base_url, username, password):
#     subs_details = request_renewal_cancellation(driver, base_url, username, password, days_till_term_end=40)
#     org_name = subs_details.get('org_name')
#     chargebee_subs_id = subs_details.get('chargebee_subs_id')
#
#     step('Then the cancellation request can be stopped')
#     logout(driver, base_url)
#     common_steps = CommonSteps(driver)
#     common_steps.login(base_url, username, password)
#     impersonate_user_via_API(driver, base_url, get_impersonated_user_id(base_url))
#     stop_cancellation_via_API(driver, base_url, chargebee_subs_id)
#     subscriptions_pg = SubscriptionsPage(driver)
#
#     def action():
#         subscriptions_pg.go_to_specific_subscription(base_url, org_name)
#         subscriptions_pg.get_subscription_row(org_name)
#         assert subscriptions_pg.cancel_request_sent_label_is_not_displayed()
#
#     wait_until_command_executed(action, max_tries=3, polling_seconds=10, specific_exception=AssertionError)


@pytest.mark.flaky(reruns=1, reruns_delay=2)
def test_request_cancellation_option_not_displayed_more_than_90_days_till_term_end_CUSTEAM_T1289(driver, base_url, username, password):
    direct_partner_username = get_username('direct_partner_user', base_url)
    step(f'Given a direct partner user {direct_partner_username} is logged in')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, direct_partner_username, password)

    step('And a new custom org with CE only is added')
    org_details = create_custom_org_with_CE_only_via_API(driver, base_url)

    step('And the cert survey has begun')
    start_survey_via_API(driver, base_url, org_details.get('org_uuid'))

    step('And the subscription page shows an active subscription')
    org_name = org_details.get('org_name')
    subscriptions_pg = SubscriptionsPage(driver)
    subscriptions_pg.go_to_specific_subscription(base_url, org_name)
    next_billing_date = subscriptions_pg.get_next_billing_date(org_name)
    one_year_later = datetime.now() + timedelta(days=365)
    next_billing_year = one_year_later.year
    assert str(next_billing_year) in next_billing_date

    days_till_term_end = 100
    step(f'When the current term end date is set to {days_till_term_end} days from now')
    logout(driver, base_url)
    common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
    partner_chargebee_subs_admin_pg = PartnerChargeBeeSubsAdminPage(driver)
    chargebee_subs_id = partner_chargebee_subs_admin_pg.get_chargebee_subs_id(base_url, org_name)
    update_chargebee_term_end_date(chargebee_subs_id, days_till_term_end)

    step('Then the request cancellation option is not displayed')
    logout(driver, base_url)
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)
    impersonate_user_via_API(driver, base_url, get_impersonated_user_id(base_url))
    confirm_that_term_end_date_has_been_changed(driver, base_url, org_name)
    subscriptions_pg = SubscriptionsPage(driver)
    assert subscriptions_pg.request_cancellation_options_is_not_available()
