import pytest

from acceptance.acceptance_tests.helpers.api_helper.certos import start_survey_via_API, submit_declaration_via_API, \
    push_back_declaration_via_API, add_note_for_info_request_via_API, \
    request_more_info_via_API, add_requested_info_via_API, accept_answer_via_API, request_declaration_signing_via_API, \
    submit_survey_with_specific_details_via_API, assist_with_active_protect_via_API
from acceptance.acceptance_tests.helpers.api_helper.org_creation import create_custom_org_with_CE_only_via_API
from acceptance.acceptance_tests.helpers.date_util import get_transformed_date
from acceptance.acceptance_tests.helpers.pdf_reader import get_text_from_pdf
from acceptance.acceptance_tests.helpers.test_logger import step
from acceptance.acceptance_tests.pages.non_admin.certificate import CertificatePage
from acceptance.acceptance_tests.pages.non_admin.certos_dashboard import CertOSDashboardPage
from acceptance.acceptance_tests.pages.non_admin.declaration import DeclarationPage
from acceptance.acceptance_tests.steps.certos_steps import CertosSteps
from acceptance.acceptance_tests.steps.common_steps import CommonSteps
from acceptance.properties_manager import get_cert_version_id, get_email_domain, get_env, get_org_uuid, \
    get_desktop_device_info, get_mobile_device_info


@pytest.mark.flaky(reruns=1, reruns_delay=2)
def test_certos_happy_path_CUSTEAM_T690(driver, base_url, password):
    user_type = 'non_iasme_partner_user'
    certos_steps = CertosSteps(driver)
    org_details = certos_steps.create_org_and_complete_survey(base_url, password, user_type)
    certos_steps.sign_declaration_and_issue_cert(base_url, org_details)

    step('Then the certificate can be viewed on the certifications page')
    step('And the contents of the certificate and report are as expected')
    cert_page = CertificatePage(driver)
    cert_version_id = get_cert_version_id(base_url)
    cert_page.go_to_page(base_url, org_details.get('org_uuid'), cert_version_id)
    cert_download_link = cert_page.get_certificate_download_link()
    report_download_link = cert_page.get_report_download_link()
    cert_pdf_text = get_text_from_pdf(cert_download_link)
    report_pdf_text = get_text_from_pdf(report_download_link)
    assert 'Automated test certificate file' in cert_pdf_text
    assert 'Automated test report file' in report_pdf_text


@pytest.mark.flaky(reruns=1, reruns_delay=2)
def test_certos_request_info_from_client_CUSTEAM_T691(driver, base_url, password):
    user_type = 'non_iasme_partner_user'
    certos_steps = CertosSteps(driver)
    org_details = certos_steps.create_org_and_complete_survey(base_url, password, user_type)

    step('And the declaration is signed')
    org_uuid = org_details.get('org_uuid')
    submit_declaration_via_API(driver, base_url, org_uuid)

    question_id = "A5.1"
    step(f'And more information has been requested by the assessor on Secure Configuration {question_id}')
    certos_dashboard = CertOSDashboardPage(driver)
    certos_dashboard.go_to_certos_dashboard_for_specific_org(base_url, org_uuid)
    cert_pk = certos_dashboard.get_cert_pk()
    info_request_msg = 'Provide more info please'
    question_pks = {
        'dev': '3976',
        'stg': '5263'
    }
    question_pk = question_pks[get_env(base_url)]
    add_note_for_info_request_via_API(driver, base_url, org_uuid, cert_pk, question_pk, info_request_msg)
    request_more_info_via_API(driver, base_url, org_uuid)

    step('When a client opens the survey')
    step('And the info request message is "Provide more info please"')
    cert_page = CertificatePage(driver)
    cert_version_id = get_cert_version_id(base_url)
    cert_page.go_to_page(base_url, org_uuid, cert_version_id)
    assert info_request_msg in cert_page.get_info_requested_msg(question_id)

    step('And the client adds more info')
    info_provided_msg = 'More info has been provided'
    question_key = f'response_{question_pk}_33.0'
    add_requested_info_via_API(driver, base_url, org_uuid, cert_version_id, question_key, info_provided_msg)

    step('And the assessor opens up the re-submitted question')
    step('Then the provided info is "More info has been provided"')
    certos_dashboard.go_to_certos_dashboard_for_specific_org(base_url, org_uuid)
    assert info_provided_msg in certos_dashboard.get_re_assessment_answer()

    step('And the answer can be accepted')
    note_id = certos_dashboard.get_note_id()
    accept_answer_via_API(driver, base_url, org_uuid, note_id, question_pk, cert_pk)
    certos_dashboard.go_to_certos_dashboard_for_specific_org(base_url, org_uuid)
    certos_dashboard.answer_accepted(question_pk)


def test_certos_incomplete_questions_CUSTEAM_T692(driver, base_url, username, password):
    step(f'Given a user is logged in as {username}')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)

    step('When a custom org with CE only is created')
    org_details = create_custom_org_with_CE_only_via_API(driver, base_url)

    step('And the survey is started')
    org_uuid = org_details.get('org_uuid')
    start_survey_via_API(driver, base_url, org_uuid)

    step('And the survey is submitted with empty text boxes for questions A7.5, A7.6, A7.7')
    # Incomplete question names in develop and staging for questions A7.5, A7.6 and A7.7 respectively
    incomplete_questions = {
        'dev': {
            'response_4006_54.0': '',
            'response_4007_55.0': '',
            'response_4008_56.0': ''
        },
        'stg': {
            'response_5293_54.0': '',
            'response_5294_55.0': '',
            'response_5295_56.0': ''
        }
    }
    incomplete_questions = incomplete_questions[get_env(base_url)]
    response_page = submit_survey_with_specific_details_via_API(
        driver, base_url, username, org_uuid, incomplete_questions)

    step('Then the expected error message is displayed')
    actual_error_msg = response_page.find('a', class_='fix-response').find_parent().get_text()
    expected_error_message = 'Please review questions A4.7, A6.3, A7.5, A7.6, A7.7 to continue to certification'
    assert expected_error_message in actual_error_msg

@pytest.mark.flaky(reruns=1, reruns_delay=2)
def test_emailflow_declaration_signing_email_is_sent_CUSTEAM_T744(driver, base_url, password):
    user_type = 'iasme_partner_user'
    certos_steps = CertosSteps(driver)
    org_details = certos_steps.create_org_and_complete_survey(base_url, password, user_type)

    step('When a declaration signing request is made')
    email_for_declaration_signing = f'test{get_transformed_date()}@{get_email_domain()}'
    org_uuid = org_details.get('org_uuid')
    request_declaration_signing_via_API(driver, base_url, org_uuid, email_for_declaration_signing)

    step('Then the declaration signing email is received')
    email_subject = 'Please review and approve your certification submission'
    common_steps = CommonSteps(driver)
    email = common_steps.check_email(email_for_declaration_signing, email_subject)

    step('And the declaration signing link in the email leads to the declaration signing page')
    declaration_signing_link = common_steps.get_link_from_email(email, 'Review')
    common_steps.go_to_page_via_email_link(declaration_signing_link, base_url)
    declaration_page = DeclarationPage(driver)
    assert declaration_page.is_displayed()


@pytest.mark.flaky(reruns=1, reruns_delay=2)
def test_auditor_can_push_back_on_declaration_CUSTEAM_T745(driver, base_url, password):
    user_type = 'iasme_partner_user'
    certos_steps = CertosSteps(driver)
    org_details = certos_steps.create_org_and_complete_survey(base_url, password, user_type)

    step('And the declaration is signed')
    org_uuid = org_details.get('org_uuid')
    submit_declaration_via_API(driver, base_url, org_uuid)

    step('When the declaration is pushed back by an auditor')
    push_back_declaration_via_API(driver, base_url, org_uuid)

    step('Then the cert status on the certos dashboard displays "Request sent"')
    certos_dashboard = CertOSDashboardPage(driver)
    certos_dashboard.go_to_page(base_url)
    assert 'Request sent' in certos_dashboard.get_cert_status(org_details.get('org_name'))


def test_special_characters_are_accepted_on_survey_CUSTEAM_T793(driver, base_url, username, password):
    step(f'Given a user is logged in as {username}')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)

    step('When a custom org with CE only is created')
    org_details = create_custom_org_with_CE_only_via_API(driver, base_url)

    step('And the survey is started')
    org_uuid = org_details.get('org_uuid')
    start_survey_via_API(driver, base_url, org_uuid)

    survey_input = "^ _ - ' \" ® @ & ( ) . -> $ ? ! * , / : ; + = £ [ ] #"
    question_id = 'A7.5'
    step(f'And the survey is submitted with accepted special characters {survey_input} for question {question_id}')
    question_names = {
        'dev': 'response_4006_54.0',
        'stg': 'response_5293_54.0'
    }
    question_name = question_names[get_env(base_url)]
    special_characters = {
        question_name: survey_input
    }
    response_page = submit_survey_with_specific_details_via_API(
        driver, base_url, username, org_uuid, special_characters)

    step('Then the submission is successful')
    step('And the declaration page is displayed')
    assert 'Do you hold a director (or equivalent) position at the company' in response_page.get_text()
    assert 'Question number A7.5. Invalid characters' not in response_page.get_text()


def test_active_protect_button_when_devices_exist_CUSTEAM_T841(driver, base_url, username, password):
    step(f'Given a user is logged in as {username}')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)

    step('And an org has devices')
    step('When a request is made to the "survey-get-devices" endpoint')
    org_uuid = get_org_uuid('org_with_device', base_url)
    response = assist_with_active_protect_via_API(driver, base_url, org_uuid)

    step('Then the response includes both mobile and desktop devices')
    response_json = response.json()
    assert get_desktop_device_info(base_url) == response_json.get('equipment_desktop')
    assert get_mobile_device_info(base_url) == response_json.get('equipment_mobile')


def test_active_protect_button_when_devices_do_not_exist_CUSTEAM_T842(driver, base_url, username, password):
    step(f'Given a user is logged in as {username}')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)

    step('And an org has no devices')
    step('When a request is made to the "survey-get-devices" endpoint')
    org_uuid = get_org_uuid('org_without_device', base_url)
    response = assist_with_active_protect_via_API(driver, base_url, org_uuid)

    step('Then the response is empty')
    response_json = response.json()
    assert response_json == {}
