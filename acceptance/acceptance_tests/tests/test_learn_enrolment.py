#As these tests are lms dependent and we have been getting intermittent lms outages, we need to disable them until
#we can find a work around for those outages
# import logging
#
# import pytest
# from bs4 import BeautifulSoup
#
# from acceptance.acceptance_tests.helpers.api_helper.device_mgmt import add_fake_device_via_API, \
#     get_rules_via_API
# from acceptance.acceptance_tests.helpers.date_util import get_transformed_date
# from acceptance.acceptance_tests.helpers.test_logger import step
# from acceptance.acceptance_tests.pages.non_admin.learn_lite import LearnLitePage
# from acceptance.acceptance_tests.pages.non_admin.org_dashboard import IndividualOrgDashboardPage
# from acceptance.acceptance_tests.steps.common_steps import CommonSteps
# from acceptance.properties_manager import get_username
#
# LOGGER = logging.getLogger(__name__)
# org_type = 'learn_lite'
#
# @pytest.mark.flaky(reruns=1, reruns_delay=2)
# def test_modules_table_column_headers_are_displayed_CUSTEAM_T703(driver, base_url, password):
#     username = get_username(org_type, base_url)
#     step(f'Given a user is logged in as {username}')
#     common_steps = CommonSteps(driver)
#     common_steps.login(base_url, username, password)
#
#     step('And has navigated to the learn lite modules page')
#     common_steps.go_to_learn_lite_page()
#
#     step('Then the modules, users completed and average score column headers are displayed')
#     learn_lite_page = LearnLitePage(driver)
#     learn_lite_page.modules_column_header_is_displayed()
#     learn_lite_page.users_completed_column_header_is_displayed()
#     learn_lite_page.average_score_column_header_is_displayed()
#
#
# def test_direct_user_signs_up_and_enables_learn_lite_CUSTEAM_T739(driver, base_url):
#     common_steps = CommonSteps(driver)
#     common_steps.signup_and_make_payment(base_url, 'direct-customer-v5-app+ce-monthly', 'example.com')
#     org_dashboard = IndividualOrgDashboardPage(driver)
#     appuser_uuid = org_dashboard.get_appuser_uuid()
#     soup = BeautifulSoup(driver.page_source, features="html.parser")
#     org_dashboard_url = common_steps.get_current_url()
#
#     step('When a device is added')
#     hostname = f'Host{get_transformed_date()}'
#     caption = f'OS{get_transformed_date()}'
#     device_id = f'device{get_transformed_date()}'
#     serial_number = f'serial{get_transformed_date()}'
#     org_uuid = org_dashboard_url.split('organisation/')[1].split("/")[0]
#     add_fake_device_via_API(driver, base_url, appuser_uuid, hostname, caption, device_id, serial_number, org_uuid)
#
#     step('Then the get rules response shows training tab enabled as false')
#     appinstall_data = {
#             "uuid": appuser_uuid,
#             "platform": "win32",
#             "release": "10.0.10240",
#             "hostname": hostname,
#             "device_id": device_id,
#             "serial_number": serial_number,
#             "caption": caption
#         }
#     response = get_rules_via_API(driver, base_url, appinstall_data, soup)
#     assert not response.json().get('training_tab')
#
#     step('When learn lite is activated')
#     common_steps.activate_learn_lite_via_url(org_dashboard_url)
#     learn_lite_page = LearnLitePage(driver)
#     learn_lite_page.page_is_displayed()
#
#     step('Then the get rules response shows training tab enabled as true')
#     response = get_rules_via_API(driver, base_url, appinstall_data, soup)
#     assert response.json().get('training_tab')
