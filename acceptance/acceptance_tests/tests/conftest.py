import glob
import json
import os
import urllib.parse
import datetime
from datetime import timedelta

import pytest
from selenium import webdriver
from selenium.common import InvalidSessionIdException
from selenium.webdriver.common.by import By

from acceptance.acceptance_tests.helpers.date_util import get_transformed_date
from acceptance.properties_manager import get_username

local_results_dir = "results/"
circleci_results_dir = "./../../results/"
url_develop = "https://develop.cybersmart.co.uk/"
url_staging = "https://thats.cybersmart.co.uk/"
url_staging_eu = "https://staging-eu.cybersmart.com/"
url_localhost = "http://localhost:8000/"
username_develop = get_username('iasme_partner_user', url_develop)
username_staging = get_username('iasme_partner_user', url_staging)


def pytest_addoption(parser):
    parser.addoption("--test_env", action="store", default='develop')
    parser.addoption("--base_url", action="store", default=url_develop)
    parser.addoption("--username", action="store", default=username_develop)
    parser.addoption("--password", action="store", default='99Password99')
    parser.addoption("--selenium_server", action="store", default='localhost')
    parser.addoption("--env", action="store", default='local')
    parser.addoption("--headless", action="store", default='true')


def pytest_configure(config):
    env = config.option.env.lower()
    xml_report_name = 'junitxml_report.xml'
    html_report_name = 'acceptance_test_report.html'

    if config.option.xmlpath is None and env == 'local':
        config.option.xmlpath = os.path.join(local_results_dir, xml_report_name)
    elif config.option.xmlpath is None and env == 'circleci':
        config.option.xmlpath = os.path.join(circleci_results_dir, xml_report_name)

    if config.option.htmlpath is None and env == 'local':
        config.option.htmlpath = os.path.join(local_results_dir, html_report_name)
    elif config.option.htmlpath is None and env == 'circleci':
        config.option.htmlpath = os.path.join(circleci_results_dir, html_report_name)

    if env == 'circleci':
        config.option.selenium_server = 'selenium'

    if config.option.test_env.lower() == 'staging':
        config.option.base_url = url_staging
        config.option.username = username_staging

    if config.option.test_env.lower() == 'staging_eu':
        config.option.base_url = url_staging_eu
        config.option.username = username_staging

    if config.option.test_env.lower() == 'localhost':
        config.option.base_url = url_localhost
        config.option.username = username_staging

    config.option.override_ini = ['log_cli=true']
    config.option.log_cli_level = 'INFO'
    config.option.log_format = "%(levelname)s  %(message)s"
    config.option.log_cli_format = "%(levelname)s  %(message)s"

    global url_to_show_on_report
    url_to_show_on_report = config.option.base_url

    global password_to_show_on_report
    password_to_show_on_report = config.option.password


def pytest_generate_tests(metafunc):
    # This is called for every test. Only get/set command line arguments
    # if the argument is specified in the list of test "fixturenames".
    option_value = metafunc.config.option.test_env
    if 'test_env' in metafunc.fixturenames and option_value is not None:
        metafunc.parametrize("test_env", [option_value])

    option_value = metafunc.config.option.base_url
    if 'base_url' in metafunc.fixturenames and option_value is not None:
        metafunc.parametrize("base_url", [option_value])

    option_value = metafunc.config.option.username
    if 'username' in metafunc.fixturenames and option_value is not None:
        metafunc.parametrize("username", [option_value])

    option_value = metafunc.config.option.password
    if 'password' in metafunc.fixturenames and option_value is not None:
        metafunc.parametrize("password", [option_value])

    option_value = metafunc.config.option.selenium_server
    if 'selenium_server' in metafunc.fixturenames and option_value is not None:
        metafunc.parametrize("selenium_server", [option_value])

    option_value = metafunc.config.option.env
    if 'env' in metafunc.fixturenames and option_value is not None:
        metafunc.parametrize("env", [option_value])

    option_value = metafunc.config.option.headless
    if 'headless' in metafunc.fixturenames and option_value is not None:
        metafunc.parametrize("headless", [option_value])


# @pytest.fixture
# def driver(selenium_server, base_url, headless, request):
#     language_marker = request.node.get_closest_marker('language')
#     browser_language = language_marker.args[0] if language_marker else 'en-GB'
#
#     # initialize selenium webdriver
#     opts = webdriver.ChromeOptions()
#     opts.add_argument('--no-sandbox')
#     opts.add_argument('--disable-dev-shm-usage')
#     opts.add_argument("--window-size=1920,1080")
#     user_agent = 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* ' \
#                  'Safari/537.36 Selenium'
#     opts.add_argument(f'user-agent={user_agent}')
#     opts.add_argument(f'--lang={browser_language}')
#     opts.add_experimental_option('prefs', {
#         'intl.accept_languages': browser_language
#     })
#
#     if headless == 'true' and 'localhost' not in base_url:
#         browser = webdriver.Remote(command_executor=f'http://{selenium_server}:4444', options=opts)
#     elif 'localhost' in base_url:
#         opts.add_argument("--headless")
#         browser = webdriver.Chrome(options=opts)
#     else:
#         browser = webdriver.Chrome(options=opts)
#
#     browser.maximize_window()
#
#     # Return webdriver instance for the setup
#     yield browser
#
#     # Quit the webdriver instance for the setup
#     try:
#         if browser.session_id:
#             browser.quit()
#     except Exception as e:
#         warn(f"Failed to quit browser: {e}")


@pytest.fixture
def driver(selenium_server, base_url, headless, request):
    language_marker = request.node.get_closest_marker('language')
    browser_language = language_marker.args[0] if language_marker else 'en-GB'

    opts = webdriver.FirefoxOptions()
    opts.add_argument("--window-size=1920,1080")
    user_agent = 'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0 Selenium'
    opts.set_preference("general.useragent.override", user_agent)
    opts.set_preference("intl.accept_languages", browser_language)
    opts.set_preference("intl.locale.requested", browser_language)

    if headless == 'true' and 'localhost' not in base_url:
        browser = webdriver.Remote(command_executor=f'http://{selenium_server}:4444', options=opts)
    elif 'localhost' in base_url:
        opts.add_argument("--headless")
        browser = webdriver.Firefox(options=opts)
    else:
        browser = webdriver.Firefox(options=opts)

    browser.maximize_window()
    yield browser
    try:
        browser.quit()
    except InvalidSessionIdException:
        # Session already closed or Selenium crashed – nothing to clean up
        pass

def pytest_html_results_summary(prefix, summary, postfix, session):
    """modifying the summary in pytest environment"""
    # Aggregate exception data from all files
    known_failures = []
    new_failures = []
    aggregated_outcomes = {}
    # Aggregate test outcomes from outcome files
    test_names = set()
    failed_test_names = set()

    for outcome_file in glob.glob("acceptance_test_outcomes_*.txt"):
        with open(outcome_file) as f:
            for line in f:
                test_name, outcome = line.strip().split(': ')
                test_names.add(test_name)
                if outcome == 'failed':
                    failed_test_names.add(test_name)
                aggregated_outcomes[test_name] = outcome
        os.remove(outcome_file)

    total_tests = len(test_names)
    failed_tests = len(failed_test_names)

    # Create test statistics file - only in CircleCI environment
    if session.config.option.env.lower() == 'circleci':
        try:
            results_dir = circleci_results_dir
            stats_file_path = os.path.join(results_dir, 'test_stats.txt')
            with open(stats_file_path, 'w', encoding='utf_8') as stats_file:
                stats_file.write(f"total_tests: {total_tests}\n")
                stats_file.write(f"failed_tests: {failed_tests}\n")
        except Exception as e:
            print(f"Error writing test statistics: {e}")

    for filename in os.listdir():
        if filename.startswith('acceptance_test_exceptions_'):
            with open(filename) as file:
                for line in file:
                    test_name, exception_type = line.strip().split(': ')
                    final_outcome = aggregated_outcomes.get(test_name)

                    if exception_type == 'KnownFailureException':
                        known_failures.append(test_name)
                    elif final_outcome != 'passed':
                        new_failures.append(test_name)
            os.remove(filename)

    try:
        postfix.extend([f'<h4>Url: {url_to_show_on_report}</h4>'])
        postfix.extend([f'<h4>Password: {password_to_show_on_report} (This is the same for all users)</h4>'])
        postfix.extend(['<h4>Usernames can be found in the log output of each test</h4>'])
        postfix.extend(['<h4>Note: Login tests have their own separate usernames and passwords</h4>'])

        if known_failures and not new_failures:
            message = "All failures are known failures."
            try:
                # Used for sending custom messages to the circleci slack channel when all failures are known failures
                file_path = os.path.join(circleci_results_dir, 'known_failure_message.txt')
                with open(file_path, 'w', encoding='utf_8') as file_:
                    file_.write(message)
            except FileNotFoundError:
                pass
        elif known_failures and new_failures:
            # Join the test names with <br> tags to display one per line
            new_failure_names = '<br>'.join(new_failures)
            message = f"There are a mix of known failures and new failures. The new failures are:<br>{new_failure_names}"
        else:
            return

        style = "font-size: large; font-weight: bold; color: red;"
        postfix.extend([f'<div class="summary" style="{style}">{message}</div>'])
    except Exception as e:
        print(f"Error: {e}")


def pytest_html_results_table_header(cells):
    """meta programming to modify header of the result"""
    # removing old table headers
    del cells[1]
    # adding new headers
    cells.insert(0, '<th class="sortable">Feature</th>')
    cells.insert(1, '<th>Scenario</th>')
    cells.insert(2, '<th class="sortable time" col="time">Time</th>')
    cells.pop()


def pytest_html_results_table_row(report, cells):
    """orienting the data gotten from pytest_runtest_makereport and sending it as row to the result"""
    # Only process reports that have the feature/scenario attributes
    # This excludes collection reports and other report types
    if hasattr(report, 'feature') and hasattr(report, 'scenario'):
        del cells[1]
        cells.insert(0, f'<td>{report.feature}</td>')
        cells.insert(1, f'<td>{report.scenario}</td>')
        cells.insert(2, f'<td class="col-time">{datetime.datetime.now(datetime.timezone.utc)}</td>')
        cells.pop()


@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_makereport(item):
    pytest_html = item.config.pluginmanager.getplugin("html")
    outcome = yield
    report = outcome.get_result()
    extra = getattr(report, "extra", [])
    report.feature = str(item.function.__module__).split('test_')[1]
    report.scenario = str(item.function.__name__).split('test_')[1]

    if report.when == "call" and report.failed and 'driver' in item.funcargs:
        driver = item.funcargs['driver']
        current_url = ""
        if driver:
            try:
                current_url = driver.current_url
            except Exception as e:
                print(f"Error accessing driver.current_url: {str(e)}")

        if driver and current_url and 'http' in current_url:
            viewport_scrnshot_filename = f"{getattr(item.obj, '__name__')}_{get_transformed_date()}.png"
            full_page_scrnshot_filename = f"{getattr(item.obj, '__name__')}_{get_transformed_date()}_full_page.png"
            results_dir = local_results_dir if item.config.option.env.lower() == 'local' else circleci_results_dir
            viewport_scrnshot_path = os.path.abspath(results_dir + viewport_scrnshot_filename)
            full_page_scrnshot_path = os.path.abspath(results_dir + full_page_scrnshot_filename)

            try:
                driver.save_screenshot(viewport_scrnshot_path)
                original_size = driver.get_window_size()
                required_width = driver.execute_script('return document.body.parentNode.scrollWidth')
                required_height = driver.execute_script('return document.body.parentNode.scrollHeight')
                driver.set_window_size(required_width, required_height)
                driver.find_element(By.CSS_SELECTOR, 'body').screenshot(full_page_scrnshot_path)
                driver.set_window_size(original_size['width'], original_size['height'])
            except Exception as e:
                print(f"Error taking screenshots: {str(e)}")

            custom_html = f'<div><figure style="float: right"><img src="{viewport_scrnshot_filename}" alt="screenshot" ' \
                          'style="width:304px;height:228px;" onclick="window.open(this.src)"><figcap' \
                          f'tion><a href="{full_page_scrnshot_filename}" target="_blank" style="color: blue">Click here for ' \
                          'full page screenshot</a></figcaption></figure></div> '
            extra.append(pytest_html.extras.html(custom_html))
            try:
                extra.append(pytest_html.extras.html(
                    f'<p>URL on failure: <a href="{current_url}" target="_blank"'
                    f'  style="color: blue">{current_url}</a></p>'))
            except Exception as e:
                print(f"Error adding URL to report: {str(e)}")
            logs = []
            try:
                for entry in driver.get_log('browser'):
                    logs.append(entry)
            except Exception as e:
                print(f"Error getting browser logs: {str(e)}")
            logs_filename = "acceptance_test_logs.txt"
            try:
                text_file = open(os.path.abspath(results_dir + logs_filename), "w", encoding='utf-8')
                text_file.write(json.dumps(logs, indent=4))
                text_file.close()
                extra.append(pytest_html.extras.html(f'<p><a href="{logs_filename}" target="_blank" style="color: '
                                                     'blue">Browser Console Logs</a></p>'))
            except Exception as e:
                extra.append(pytest_html.extras.html(f'<p>Browser Console Logs could not be obtained due to: {e}</p>'))
                pass

            dom_filename = "acceptance_test_dom.txt"
            dom = ""
            try:
                dom = driver.find_element(By.XPATH, "//body").get_attribute("innerHTML")
            except Exception as e:
                print(f"Error capturing DOM: {str(e)}")
            try:
                text_file = open(os.path.abspath(results_dir + dom_filename), "w", encoding='utf-8')
                text_file.write(dom)
                text_file.close()
                extra.append(pytest_html.extras.html(f'<p><a href="{dom_filename}" target="_blank" style="color: '
                                                     'blue">DOM state on failure</a></p>'))
            except Exception as e:
                extra.append(pytest_html.extras.html(f'<p>DOM could not be obtained due to: {e}</p>'))
                pass

            # Add Sentry logs section - only for develop and staging environments
            if current_url and "localhost" not in current_url:
                try:
                    # Get the timestamp when the test failed
                    failure_time = datetime.datetime.now(datetime.timezone.utc)
                    # Look for logs from 1 minute before failure to now
                    start_time = failure_time - timedelta(minutes=1)

                    # Format times for Sentry URL
                    end_time_str = failure_time.strftime("%Y-%m-%dT%H:%M:%S")
                    start_time_str = start_time.strftime("%Y-%m-%dT%H:%M:%S")

                    # URL encode the times
                    end_time_encoded = urllib.parse.quote(end_time_str)
                    start_time_encoded = urllib.parse.quote(start_time_str)

                    sentry_project_id = "6106636"
                    environment_name = "develop"

                    if "thats.cybersmart.co.uk" in current_url:
                        sentry_project_id = "5473895"
                        environment_name = "staging"

                    sentry_url = (
                        "https://cybersmart.sentry.io/issues/"
                        f"?end={end_time_encoded}"
                        f"&project={sentry_project_id}"
                        f"&start={start_time_encoded}"
                        "&utc=true"
                    )

                    extra.append(pytest_html.extras.html(
                        f'<p><a href="{sentry_url}" target="_blank" style="color: blue">'
                        f'Sentry Logs ({environment_name})</a> '
                        f'({start_time.strftime("%H:%M:%S")} - {failure_time.strftime("%H:%M:%S")} UTC)</p>'
                    ))
                except Exception as e:
                    extra.append(pytest_html.extras.html(f'<p>Failed to generate Sentry logs link: {str(e)}</p>'))
    report.extras = extra


def pytest_exception_interact(node, call, report):
    """Hook that is called whenever an exception is caught by pytest."""
    excinfo = call.excinfo

    # Handle different node types
    if hasattr(node, 'originalname'):
        # This is a test item (has originalname)
        test_name = node.originalname
    elif hasattr(node, 'nodeid'):
        # This is a Module or other collector (has nodeid)
        test_name = node.nodeid
    else:
        # Fallback to string representation
        test_name = str(node)

    # Collect the name of the exception and the name of the test/module
    exception_info = (test_name, excinfo.type.__name__)

    # Write exception data to a file, using a unique filename based on the process ID
    filename = f'acceptance_test_exceptions_{os.getpid()}.txt'
    with open(filename, 'a', encoding='utf_8') as file:
        file.write(f"{exception_info[0]}: {exception_info[1]}\n")


def pytest_runtest_logreport(report):
    if report.when == 'call':
        test_name = report.location[2].split("[")[0]
        with open(f"acceptance_test_outcomes_{os.getpid()}.txt", 'a', encoding='utf_8') as f:
            f.write(f"{test_name}: {report.outcome}\n")
