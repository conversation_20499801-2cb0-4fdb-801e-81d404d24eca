import pytest

from acceptance.acceptance_tests.helpers.api_helper.general import set_org_language_default
from acceptance.acceptance_tests.helpers.common_functions import logout
from acceptance.acceptance_tests.helpers.test_logger import step
from acceptance.acceptance_tests.pages.admin.organisations_settings_admin import OrgSettingsAdminPage
from acceptance.acceptance_tests.pages.non_admin.org_dashboard import IndividualOrgDashboardPage
from acceptance.acceptance_tests.pages.non_admin.subscriptions import SubscriptionsPage
from acceptance.acceptance_tests.steps.common_steps import CommonSteps
from acceptance.properties_manager import get_org_name, get_username, get_org_uuid

lang_abbrev = {
    'french': 'fr',
    'swedish': 'sv',
    'italian': 'it',
    'polish': 'pl'
}
dashboard_translations = {
    'swedish': 'Instrumentpanel',
    'french': 'Tableau de bord',
    'italian': 'Cruscotto',
    'polish': 'Pulpit nawigacyjny',
}

def verify_browser_language(driver, base_url, username, password, org_default_lang, default_browser_lang):
    step(f"Given an org's default language is set to {org_default_lang}")
    step(f'And the default browser language preference is {default_browser_lang}')
    common_steps = CommonSteps(driver)
    common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
    org_settings_admin_pg = OrgSettingsAdminPage(driver)
    org_type = 'translations_org'
    org_settings_id = org_settings_admin_pg.get_org_settings_id(base_url, get_org_name(org_type, base_url))
    set_org_language_default(driver, base_url, org_settings_id, lang_abbrev[org_default_lang])
    logout(driver, base_url)

    browser_lang_unsupported = default_browser_lang.lower() == "chinese"
    step('When an org admin is logged in')
    step('Then the org dashboard is displayed '
         f'in {org_default_lang if browser_lang_unsupported else default_browser_lang}')
    common_steps.login(base_url, get_username('translations_user', base_url), password)
    org_dashboard = IndividualOrgDashboardPage(driver)
    org_dashboard.go_to_page(base_url, get_org_uuid(org_type, base_url))
    org_dashboard.refresh()
    dashboard_translation = dashboard_translations[
        org_default_lang.lower() if browser_lang_unsupported else default_browser_lang.lower()]
    org_dashboard.page_title_is_displayed(dashboard_translation)


@pytest.mark.language('sv-SE')
def test_browser_language_override_swedish_CUSTEAM_T1365(driver, base_url, username, password):
    verify_browser_language(
        driver, base_url, username, password, org_default_lang='french', default_browser_lang='swedish')

@pytest.mark.language('it-IT')
def test_browser_language_override_italian_CUSTEAM_T1366(driver, base_url, username, password):
    verify_browser_language(
        driver, base_url, username, password, org_default_lang='swedish', default_browser_lang='italian')

@pytest.mark.language('fr-FR')
def test_browser_language_override_french_CUSTEAM_T1367(driver, base_url, username, password):
    verify_browser_language(
        driver, base_url, username, password, org_default_lang='italian', default_browser_lang='french')

@pytest.mark.language('pl-PL')
def test_browser_language_override_polish_CUSTEAM_T1368(driver, base_url, username, password):
    verify_browser_language(
        driver, base_url, username, password, org_default_lang='french', default_browser_lang='polish')

@pytest.mark.language('zh-CN')
def test_unsupported_browser_language_chinese_CUSTEAM_T1369(driver, base_url, username, password):
    verify_browser_language(
        driver, base_url, username, password, org_default_lang='swedish', default_browser_lang='chinese')

@pytest.mark.language('fr-FR')
def test_abbreviations_are_translated_CUSTEAM_T1371(driver, base_url, username, password):
    step(f'Given a partner user {username} is logged in')
    step('And the browser language preference is French')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)

    step('Then the subscriptions overview page shows VAT abbreviation as TVA')
    subscriptions_pg = SubscriptionsPage(driver)
    subscriptions_pg.go_to_page(base_url)
    vat_info = subscriptions_pg.get_vat_info()
    expected_vat_msg = "Tous les prix s'entendent hors TVA"
    assert expected_vat_msg in vat_info
