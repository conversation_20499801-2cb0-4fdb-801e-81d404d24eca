import json
import os
import urllib.parse

import requests
from bs4 import <PERSON><PERSON><PERSON><PERSON>

from acceptance.acceptance_tests.helpers.api_helper.utils import get_headers, get_csrfmiddlewaretoken, \
    app_form_url_encoded_content_type, html_parser, admin_headers
from acceptance.acceptance_tests.helpers.custom_exceptions import RequestFailedException
from acceptance.properties_manager import get_product_version_id, get_opswat_payload


def app_checkin_via_API(driver, base_url, soup, appinstall, unique_id, checks_pass=True):
    headers = get_headers(driver)
    headers.update({
        'referer': os.path.join(base_url, f'{appinstall.get("org_id")}'),
        'x-csrftoken': get_csrfmiddlewaretoken(soup),
    })
    app_checkin_url = os.path.join(base_url, 'api/v2/results/')
    body = {
        'uuid': appinstall.get('uuid'),
        'device_id': appinstall.get('device_id'),
        'serial_number': appinstall.get('serial_number'),
        "app_version": "4.10.1",
        'os': {
            'platform': 'darwin',
            'release': f'macOS Monterey {unique_id}',
            'build': '21G83',
            'version': '12.5.1',
            'kernel': '21.6.0'
        },
        'response': [
            {
                "response": checks_pass,
                "response_text": "Success" if checks_pass else "Failed",
                "report_only": True,
                "question_id": 3
            }
        ],
        'inapp_response': [
            {
                "question_id": 18,
                "product_name": f"Avast Security {unique_id}",
                "product_status": "off"
            }
        ],
        'network_interfaces': [
            {
                "name": "aa2",
                "desc": "Wi-Fi",
                "type": "Wireless",
                "ip_address": "************",
                "mac_address": f"5c:52:30:9c:de:{unique_id}",
                "gateway_ip": "***********",
                "netmask": "*************",
                "status": "2",
                "public_ip": "",
                "local_ip": "",
                "gateway_mac": "",
                "cidr": ""
            }
        ]
    }
    return requests.post(url=app_checkin_url, json=body, headers=headers)


def get_rules_via_API(driver, base_url, body, soup, token=None):
    headers = get_headers(driver)
    headers.update({
        'referer': base_url,
        'x-csrftoken': token if token else get_csrfmiddlewaretoken(soup),
    })
    url = os.path.join(base_url, 'api/v2/get-rules/')
    return requests.post(url=url, json=body, headers=headers)


def delete_policy_agreement_via_API(driver, base_url, soup, policy_agreement_id):
    url = os.path.join(base_url, 'admin/appusers/appinstallpolicyagreement/')
    body = f'csrfmiddlewaretoken={get_csrfmiddlewaretoken(soup)}' \
           f'&_selected_action={policy_agreement_id}&action=delete_selected&post=yes'
    headers = get_headers(driver)
    headers.update({
                       'Referer': url,
                       'Content-Type': app_form_url_encoded_content_type,
                   } | admin_headers)
    response = requests.request("POST", url, headers=headers, data=body)
    soup = BeautifulSoup(response.text, features=html_parser)
    if len(soup.find_all("li", string="Successfully deleted 1 Policy agreement.")) == 0:
        raise Exception(f'An issue occured. Policy agreement {policy_agreement_id} was not deleted')


def change_manually_resolved_status_via_API(driver, soup, url):
    token = get_csrfmiddlewaretoken(soup)
    body = f"csrfmiddlewaretoken={token}&check_id=5&id_response=2011669&manual_resolved=true"
    headers = get_headers(driver)
    headers.update({
        'referer': url,
        'Content-Type': app_form_url_encoded_content_type,
        'x-csrftoken': token,
        'x-requested-with': 'XMLHttpRequest'
    })
    response = requests.request("POST", url, headers=headers, data=body)
    if response.json()['status'] != 1 or response.status_code != 200:
        raise Exception(f'Check resolved status could not be changed. Response json was {response.json()}')


def change_permanently_resolved_status_via_API(driver, base_url, token, org_uuid, path):
    referrer = os.path.join(base_url, f'organisation/{org_uuid}/check-report/')
    url = os.path.join(referrer, f'bulk-permanent-resolve{path}')
    body = f"csrfmiddlewaretoken={token}&question_pk=8"
    headers = get_headers(driver)
    headers.update({
        'referer': referrer,
        'Content-Type': app_form_url_encoded_content_type,
        'x-csrftoken': 'undefined',
        'x-requested-with': 'XMLHttpRequest'
    })
    response = requests.request("POST", url, headers=headers, data=body)
    if response.json()['status'] != 1:
        raise Exception(f'Permanently resolved status was not changed. Response was {response.text}')


def add_fake_device_via_API(driver, base_url, appinstall_uuid, hostname, caption, device_id, serial_number, org_uuid):
    url = os.path.join(base_url, f'app-install/fake/{appinstall_uuid}')
    payload = (f'os=14&hostname={hostname}&caption={caption}&platform=darwin&release=1.1.1&os_release=1.1.1&os_info=null'
               f'&base_os=darwin&security_patch=12.06.2020&device_id={device_id}&serial_number={serial_number}'
               '&installer=pkg&date_removed=&app_version=5.5.0&machine_vendor=Vendor%20RI4Z9NA'
               '&machine_model=Model%20RI4Z9NA&system_info=&device_type=1&fake_report=on&osuser_username=TestUser'
               '&osuser_domain=&app_user_id=')
    headers = get_headers(driver)
    headers.update({
        'referer': os.path.join(base_url, f'organisation/{org_uuid}/'),
        'Content-Type': app_form_url_encoded_content_type,
    })
    response = requests.request("POST", url, headers=headers, data=payload)
    response_txt = response.text
    if "new app was successfully added" not in response_txt.lower():
        raise Exception(f'An issue occurred, fake device was not added. Response was {response_txt}')


def activate_device_via_API(driver, base_url, soup, registration_details):
    url = os.path.join(base_url, 'api/v2/activate/')
    headers = get_headers(driver)
    headers.update({
        'X-CSRFToken': get_csrfmiddlewaretoken(soup),
        'Content-Type': app_form_url_encoded_content_type,
        'referer': base_url
    })
    response = requests.request("POST", url, headers=headers, data=registration_details)
    response_json = response.json()
    if not response_json.get('uuid'):
        raise Exception(f'An issue occurred, device was not activated. Response was {response.text}')


def validate_email_via_API(driver, base_url, payload):
    url = os.path.join(base_url, 'api/v2/validate/email/')
    headers = get_headers(driver)
    soup = BeautifulSoup(driver.page_source, features="html.parser")
    headers.update({
        'X-CSRFToken': get_csrfmiddlewaretoken(soup),
        'Content-Type': app_form_url_encoded_content_type,
        'referer': base_url
    })
    response = requests.request("POST", url, headers=headers, data=payload)
    if not response.json().get('uuid'):
        raise Exception(f'An issue occurred, email was not validated. Response was {response.text}')


def get_uba_status_via_API(driver, base_url, payload):
    url = os.path.join(base_url, 'api/v2/status/')
    headers = get_headers(driver)
    soup = BeautifulSoup(driver.page_source, features="html.parser")
    headers.update({
        'X-CSRFToken': get_csrfmiddlewaretoken(soup),
        'Content-Type': app_form_url_encoded_content_type,
        'referer': base_url
    })
    response = requests.request("POST", url, headers=headers, data=payload)
    response_json = response.json()
    if not response_json.get('user_device_attribution'):
        raise Exception(f'An issue occurred, uba status was not obtained. Response was {response.text}')
    return response_json


def add_policies_via_API(driver, base_url, org_uuid, policy_name, policy_group_id):
    url = os.path.join(base_url, f'organisation/{org_uuid}/policies/upload/')
    body = {
        'policy_name': policy_name,
        'policy_version': '1.0',
        'policy_groups': policy_group_id
    }
    current_file_path = os.path.dirname(os.path.realpath(__file__))
    parent_directory_path = os.path.abspath(os.path.join(current_file_path, os.pardir))
    policy_pdf_path = os.path.abspath(os.path.join(
        parent_directory_path, os.pardir, 'test_policy_document.pdf'))
    files = [
        ('policy_file', (
            'test_policy_document.pdf', open(policy_pdf_path, 'rb'),
            'application/pdf'))
    ]
    headers = get_headers(driver)
    headers.update({
        'accept': '*/*',
        'referer': base_url,
        'x-csrftoken': get_csrfmiddlewaretoken(None, driver, base_url),
        'x-requested-with': 'XMLHttpRequest'
    })
    response = requests.request("POST", url, headers=headers, data=body, files=files)
    response_json = response.json()
    if not response_json.get('success'):
        raise Exception(f'Policy not added successfully. Response was {response_json}')


def get_policies_via_API(driver, base_url, appuser_uuid):
    url = os.path.join(base_url, 'api/v3/policies/')
    headers = {
        'accept': 'application/json',
        'X-CSRFToken': get_csrfmiddlewaretoken(None, driver, base_url),
        'appuser-uuid': appuser_uuid
    }
    return requests.request("GET", url, headers=headers)


def update_policy_status_via_API(driver, base_url, appuser_uuid, policy_version_uuid, read_agreed_status):
    url = os.path.join(base_url, f'api/v3/policies/{policy_version_uuid}/')
    payload = json.dumps(read_agreed_status)
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'X-CSRFToken': get_csrfmiddlewaretoken(None, driver, base_url),
        'appuser-uuid': appuser_uuid
    }
    return requests.request("PUT", url, headers=headers, data=payload)


def get_policies_for_CAP_v5_via_API_beta(base_url, os_user, device_id, serial_number, appuser_uuid):
    url = os.path.join(

        base_url, f'api/v3/app-user-policies/?os_user={os_user}&device_id={device_id}&serial_number={serial_number}')
    headers = {'APPUSER-UUID': appuser_uuid}
    return requests.request("GET", url, headers=headers)


def get_policies_for_trustd_via_API_beta(base_url, trustd_device_id, auth_token):
    url = os.path.join(base_url, 'api/v3/app-user-policies/')
    headers = {
        'accept': 'application/json',
        'TRUSTDDEVICE-UUID': trustd_device_id,
        'Authorization': f'Basic {auth_token}'
    }
    return requests.request("GET", url, headers=headers)


def update_policy_status_for_CAP_v5_via_API_beta(
        base_url, os_user, device_id, serial_number, appuser_uuid, policy_version_uuid, policy_status):
    url = os.path.join(base_url, f'api/v3/app-user-policies/{policy_version_uuid}/')
    payload_dict = {
        "device_id": device_id,
        "serial_number": serial_number,
        "os_user": os_user,
        "read": "true"
    }
    payload_dict.update(policy_status)
    payload = json.dumps(payload_dict)
    headers = {
        'accept': 'application/json',
        'APPUSER-UUID': appuser_uuid,
        'Content-Type': 'application/json'
    }
    return requests.request("PATCH", url, headers=headers, data=payload)


def change_deployment_method_via_API(driver, base_url, org_id, partner_id, user_id, org_admin_id, org_name):
    url = os.path.join(base_url, f'admin/organisations/organisation/{org_id}/change/')
    token = get_csrfmiddlewaretoken(None, driver, base_url)
    payload = f'csrfmiddlewaretoken={token}&change-deployment-type=Change%20to%20bulk%20deployment' \
              f'&superscript_product_code=0&name={org_name}' \
              '&industry=APRE&size=2-4%20EMPLOYEES&email_message=test&showmodal=on' \
              f'&auto_update=on&security_emails_frequency=weekly&partner={partner_id}&software_support=on' \
              '&pricing_band=6&disable_user_fix=on&trainings_tab_enabled=false&enable_onboarding_email=on' \
              '&admins-TOTAL_FORMS=2&admins-INITIAL_FORMS=1&admins-MIN_NUM_FORMS=0&admins-MAX_NUM_FORMS=1000' \
              f'&admins-0-user={user_id}&admins-0-is_admin=on&admins-0-subscribed=on&admins-0-id={org_admin_id}&' \
              f'admins-0-organisation={org_id}&admins-1-subscribed=on&admins-1-organisation={org_id}&' \
              f'admins-__prefix__-subscribed=on&admins-__prefix__-organisation={org_id}'
    headers = get_headers(driver)
    headers.update({
                       'Content-Type': app_form_url_encoded_content_type,
                       'referer': base_url
                   } | admin_headers)
    response = requests.request("POST", url, headers=headers, data=payload)
    response_txt = response.text
    if 'was changed successfully' not in response_txt:
        raise Exception(f'Deployment method not changed. Response was {response_txt}')


def simulate_vulnerabilities_via_v5_API(base_url, appuser_uuid, device_id, serial_number):
    url = os.path.join(base_url, 'api/v3/vulnerabilities/check/')
    body = {
        "device_id": device_id,
        "serial_number": serial_number,
        "platform": "darwin",
        "software": [
            {
                "product": "visual studio code",
                "vendor": "microsoft",
                "version": "1.82.0",
                "installed_date": "2023-11-01T00:00:00Z"
            },
            {
                "product": "Firefox",
                "vendor": "Mozilla Corporation",
                "version": "136.0.4",
                "installed_date": "2023-11-01T00:00:00Z"
            }]
    }
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'Appuser-uuid': appuser_uuid
    }
    return requests.request("POST", url, headers=headers, json=body)


def mobile_app_checkin_via_API(base_url, webhook_body):
    url = os.path.join(base_url, 'api/v2/trustd/risk/')
    payload = json.dumps(webhook_body)
    headers = {'Content-Type': 'application/json'}
    return requests.request("POST", url, headers=headers, data=payload)

def schedule_product_installer_create_via_API(driver, base_url, appinstall_id):
    url = os.path.join(base_url, 'api/v3/opswat-patch/scheduled-product-installer-create/')
    payload = json.dumps({
        "opswat_product_patch_installer": {
            "opswat_id": "string",
            "product": {
                "opswat_id": "string",
                "name": "string",
                "vendor": 0
            },
            "product_id": 0,
            "patch_installer": {
                "product_name": "string",
                "vulnerabilities": {},
                "release_note_link": "string",
                "eula_link": "string",
                "latest_version": "string",
                "language_default": "string",
                "fresh_installable": True,
                "release_date": "2025-04-16",
                "requires_reboot": True,
                "requires_uninstall_first": True,
                "schema_version": 0
            },
            "patch_installer_id": 0
        },
        "opswat_product_patch_installer_id": 305,
        "app_install": appinstall_id
    })
    headers = get_headers(driver)
    headers.update({
        'referer': base_url,
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'X-CSRFTOKEN': get_csrfmiddlewaretoken(None, driver, base_url)
    })
    return requests.request("POST", url, headers=headers, data=payload)


def get_scheduled_product_installers_via_API(driver, base_url, device_id, serial_number, appuser_uuid):
    url = os.path.join(base_url, f'api/v3/opswat-patch/scheduled-product-installers/?device_id={device_id}'
                                 f'&serial_number={serial_number}&all=true')
    headers = {
        'accept': 'application/json',
        'APPUSER-UUID': appuser_uuid,
        'X-CSRFTOKEN': get_csrfmiddlewaretoken(None, driver, base_url)
    }

    return requests.request("GET", url, headers=headers)


def patch_summary_via_API(driver, base_url, device_details_page_url, appinstall_id):
    url = os.path.join(device_details_page_url, 'patch-summary/')
    composite_id = f'{appinstall_id}:opswat:{get_product_version_id(base_url)}'
    encoded_composite_id = urllib.parse.quote(composite_id)
    payload = f'selected_packages={encoded_composite_id}'
    headers = get_headers(driver)
    headers.update({
        'Referer': device_details_page_url,
        'HX-Request': 'true',
        'HX-Trigger': 'patch-package-button',
        'HX-Current-URL': device_details_page_url,
        'X-CSRFToken': get_csrfmiddlewaretoken(None, driver, base_url),
        'Content-Type': app_form_url_encoded_content_type,
    })
    response = requests.request("POST", url, headers=headers, data=payload)
    response_txt = response.text
    expected_msg = 'This will install the latest version(s) of software on this device'
    if expected_msg.lower() not in response_txt.lower():
        raise RequestFailedException(f'Patch summary was not successful. Response was {response_txt}')


def patch_history_via_API(driver, base_url, device_details_page_url, appinstall_id):
    url = os.path.join(device_details_page_url, 'patch-history/')
    composite_id = f'{appinstall_id}:opswat:{get_product_version_id(base_url)}'
    encoded_composite_id = urllib.parse.quote(composite_id)
    payload = f'packages={encoded_composite_id}'
    headers = get_headers(driver)
    headers.update({
        'Referer': device_details_page_url,
        'HX-Request': 'true',
        'HX-Target': 'installed-software-content',
        'HX-Current-URL': device_details_page_url,
        'X-CSRFToken': get_csrfmiddlewaretoken(None, driver, base_url),
        'Content-Type': app_form_url_encoded_content_type,
    })
    response = requests.request("POST", url, headers=headers, data=payload)
    response_txt = response.text
    expected_msg = 'The patches have been successfully scheduled'
    if expected_msg.lower() not in response_txt.lower():
        raise RequestFailedException(f'Patch history was not successful. Response was {response_txt}')


def post_event_log_via_API(
        driver, base_url, device_id, serial_number, appuser_uuid, opswat_scheduled_product_installer_id, status='pending'):
    url = os.path.join(base_url, 'api/v3/opswat-patch/event-log/')

    payload = json.dumps({
        "opswat_scheduled_product_installer": opswat_scheduled_product_installer_id,
        "status": status,
        "error_code": "",
        "details": "string",
        "device_id": device_id,
        "serial_number": serial_number
    })
    headers = {
        'Content-Type': 'application/json',
        'Appuser-uuid': appuser_uuid,
        'X-CSRFTOKEN': get_csrfmiddlewaretoken(None,driver, base_url),
    }
    return requests.request("POST", url, headers=headers, data=payload)


def enable_patching_via_API(driver, base_url, org_settings_id):
    url = os.path.join(base_url, f'admin/organisations/organisationsettings/{org_settings_id}/change/')
    payload = (f'csrfmiddlewaretoken={get_csrfmiddlewaretoken(None, driver, base_url)}'
               '&academy_enabled=on&certificate_auto_renewal_enabled=on&patch_enabled=on'
               '&lms_send_email_notifications=on&cap_steps_to_fix_enabled=on&cap_vulnerable_software_enabled=on'
               '&cap_auto_update_enabled=on&features_help_text=%7B%7D&default_language=en-gb&_save=Save')
    headers = get_headers(driver)
    headers.update({
                       'Referer': url,
                       'Content-Type': app_form_url_encoded_content_type,
                   } | admin_headers)
    response = requests.request("POST", url, headers=headers, data=payload)
    response_txt = response.text
    if 'was changed successfully' not in response_txt.lower():
        raise RequestFailedException(f'Patching was not enabled. Response was {response_txt}')


def opswat_software_via_API(driver, base_url, device_id, serial_number, appuser_uuid):
    url = os.path.join(base_url, 'api/v3/opswat/software/')
    payload = json.dumps(get_opswat_payload(device_id, serial_number, appuser_uuid))
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'X-CSRFTOKEN': get_csrfmiddlewaretoken(None, driver, base_url)
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    response_text = response.text
    if 'device_id' not in response_text:
        raise RequestFailedException(f'OPSWAT request failed. Response was {response_text}')


def delete_check_manual_fix_for_autoplay_disabled_check_via_API(driver, base_url, device_id, check_manual_fix_id):
    url = os.path.join(base_url, f'admin/appusers/checkmanualfix/?q={device_id}&app_check__id__exact=5')

    payload = (f'csrfmiddlewaretoken={get_csrfmiddlewaretoken(None, driver, base_url)}'
               f'&_selected_action={check_manual_fix_id}&action=delete_selected&post=yes')
    headers = get_headers(driver)
    headers.update({
                       'Referer': url,
                       'Content-Type': app_form_url_encoded_content_type,
                   } | admin_headers)

    response = requests.request("POST", url, headers=headers, data=payload)
    response_txt = response.text.lower()
    if 'successfully deleted 1 check manual fix' not in response_txt:
        raise RequestFailedException(f'Check manual fix was not deleted. Response was {response_txt}')
