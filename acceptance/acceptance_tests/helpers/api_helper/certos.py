import os

import requests
from bs4 import BeautifulSoup

from acceptance.acceptance_tests.helpers.api_helper.survey_utils import start_survey_common
from acceptance.acceptance_tests.helpers.api_helper.utils import get_headers, app_form_url_encoded_content_type, \
    html_parser, admin_headers, get_csrfmiddlewaretoken
from acceptance.acceptance_tests.helpers.custom_exceptions import RequestFailedException
from acceptance.acceptance_tests.helpers.date_util import get_date_for_yesterday
from acceptance.properties_manager import get_cert_version_id, get_body_for_CE_survey

img_str = (
    "data%3Aimage%2Fpng%3Bbase64%2C"
    "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVR4nGNgYGAAAAAEAAH2FzhVAAAAAElFTkSuQmCC"
)

def start_survey_via_API(driver, base_url, org_uuid):
    start_survey_common(driver, base_url, org_uuid)


def submit_survey_via_API(driver, base_url, username, org_uuid, insurance_opt_in):
    headers = get_headers(driver)
    cert_version_id = get_cert_version_id(base_url)
    url = os.path.join(base_url, f'organisation/{org_uuid}/certificate/1/version/{cert_version_id}/')
    csrfmiddlewaretoken = get_csrfmiddlewaretoken(None, driver, base_url)
    body = get_body_for_CE_survey(base_url, csrfmiddlewaretoken, username, insurance_opt_in)
    headers.update({
        'content-type': app_form_url_encoded_content_type,
        'referer': url
    })
    response = requests.request("POST", url, headers=headers, data=body)
    return response.text


def submit_survey_with_specific_details_via_API(driver, base_url, username, org_uuid, specific_details):
    cert_version_id = get_cert_version_id(base_url)
    url = os.path.join(base_url, f'organisation/{org_uuid}/certificate/1/version/{cert_version_id}/')
    csrfmiddlewaretoken = get_csrfmiddlewaretoken(None, driver, base_url)
    body = get_body_for_CE_survey(base_url, csrfmiddlewaretoken, username, insurance_opt_in=True)
    body.update(specific_details)
    headers = get_headers(driver)
    headers.update({
        'content-type': app_form_url_encoded_content_type,
        'referer': url
    })
    response = requests.request("POST", url, headers=headers, data=body)
    soup = BeautifulSoup(response.text, features=html_parser)
    return soup


def request_declaration_signing_via_API(driver, base_url, org_uuid, email):
    referrer = os.path.join(base_url, f'organisation/{org_uuid}/declaration/1/')
    url = os.path.join(referrer, 'external-signing/')
    headers = get_headers(driver)
    headers.update({
        'content-type': app_form_url_encoded_content_type,
        'referer': referrer
    })
    body = f'csrfmiddlewaretoken={get_csrfmiddlewaretoken(None, driver, base_url)}&name=test&email={email}&title=test'
    response = requests.request("POST", url, headers=headers, data=body)
    expected_msg = f'You have sent this declaration for external signing to: {email}'
    if expected_msg not in response.text:
        raise Exception(f'Declaration signing request was not sent. The response was {response.text}')


def submit_declaration_via_API(driver, base_url, org_uuid):
    url = os.path.join(base_url, f'organisation/{org_uuid}/declaration/1/external/')
    body = f'csrfmiddlewaretoken={get_csrfmiddlewaretoken(None, driver, base_url)}' \
           '&name=auto_test_first_name%2Bauto_test_last_name&job=test' \
           f'&signature_name=d{img_str}&action='
    headers = get_headers(driver)
    headers.update({
        'content-type': app_form_url_encoded_content_type,
        'referer': url
    })
    response = requests.request("POST", url, headers=headers, data=body)
    if 'Declaration Signed' not in response.text:
        raise Exception(f'Declaration was not submitted. Response was {response.text}')


def push_back_declaration_via_API(driver, base_url, org_uuid):
    url = os.path.join(base_url, f'certos/dashboard/{org_uuid}/1/')
    body = f'csrfmiddlewaretoken={get_csrfmiddlewaretoken(None, driver, base_url)}&push_back_declaration=on'
    headers = get_headers(driver)
    headers.update({
        'content-type': app_form_url_encoded_content_type,
        'referer': url
    })
    response = requests.request("POST", url, headers=headers, data=body)
    if 'The questions were sent back to the client' not in response.text:
        raise Exception(f'Declaration was not pushed back. Response was {response.text}')


def get_declaration_signing_page_via_API(driver, base_url, org_uuid):
    referer_path = f'organisation/{org_uuid}/declaration/1/'
    url_path = f'organisation/{org_uuid}/declaration/1/external/'
    target_reached_locator = '.not-approved i'
    err_msg = 'An issue occured, declcaration signing page was not retrieved'
    return survey_pages_get_request_helper(driver, base_url, referer_path, url_path, target_reached_locator, err_msg)


def go_back_survey_via_API(driver, base_url, org_uuid):
    referer_path = f'organisation/{org_uuid}/declaration/1/'
    url_path = f'organisation/{org_uuid}/back-to-survey/1/'
    target_reached_locator = '[data-question-order]'
    err_msg = 'An issue occured, was not able to go back to survey'
    return survey_pages_get_request_helper(driver, base_url, referer_path, url_path, target_reached_locator, err_msg)


def issue_certificate_via_API(driver, base_url, org_cert_id, issue_cert_date=None):
    url = os.path.join(base_url, 'admin/rulebook/issuedcertification/add/')
    yesterday = get_date_for_yesterday()
    body = {'csrfmiddlewaretoken': get_csrfmiddlewaretoken(None, driver, base_url),
            'certificate': org_cert_id,
            'date_0': issue_cert_date if issue_cert_date else f'{yesterday.day}/{yesterday.month}/{yesterday.year}',
            'date_1': '09:12:19',
            '_save': 'Save'}
    current_file_path = os.path.dirname(os.path.realpath(__file__))
    parent_directory_path = os.path.abspath(os.path.join(current_file_path, os.pardir))
    cert_file_pdf_path = os.path.abspath(os.path.join(parent_directory_path, os.pardir, 'Certificate.pdf'))
    report_file_pdf_path = os.path.abspath(os.path.join(parent_directory_path, os.pardir, 'user_report.pdf'))
    files = [
        ('certificate_file', (
            'Certificate.pdf', open(cert_file_pdf_path, 'rb'),
            'application/pdf')),
        ('report_file', (
            'user_report.pdf', open(report_file_pdf_path, 'rb'),
            'application/pdf'))
    ]
    headers = get_headers(driver)
    headers.update({'referer': url} | admin_headers)
    response = requests.request("POST", url, headers=headers, data=body, files=files)
    if 'was added successfully' not in response.text:
        raise Exception(f'Certificate was not issued. Response was {response.text}')


def change_CE_cert_issue_date_via_API(driver, base_url, new_date, issued_cert_id, org_cert_id):
    url = os.path.join(base_url, f'admin/rulebook/issuedcertification/{issued_cert_id}/change/')
    body = {'csrfmiddlewaretoken': get_csrfmiddlewaretoken(None, driver, base_url),
            'certificate': org_cert_id,
            'date_0': new_date,
            'date_1': '09:12:19',
            '_save': 'Save'}
    headers = get_headers(driver)
    headers.update({'referer': url} | admin_headers)
    response = requests.request("POST", url, headers=headers, data=body)
    if 'was changed successfully' not in response.text:
        raise Exception(f'Certificate issue date was not changed. Response was {response.text}')


def set_certificate_to_expired_via_API(driver, base_url, org_cert_id, org_id):
    url = os.path.join(base_url, f'admin/organisations/organisationcertification/{org_cert_id}/change/')
    payload = {'csrfmiddlewaretoken': get_csrfmiddlewaretoken(None, driver, base_url),
               'organisation': org_id,
               'version': get_cert_version_id(base_url),
               'status': 'EXD',
               'immediate_requested_coverage_amount': '0',
               '_save': 'Save'}
    headers = get_headers(driver)
    headers.update({'referer': url} | admin_headers)
    response = requests.request("POST", url, headers=headers, data=payload)
    if 'was changed successfully' not in response.text:
        raise Exception(f'Certificate status was not changed. Response was {response.text}')


def add_note_for_info_request_via_API(driver, base_url, org_uuid, cert_pk, question_pk, info_request_msg):
    url = os.path.join(base_url, 'certos/dashboard/note/')
    body = f"question_pk={question_pk}&certification_pk={cert_pk}&text=Provide+more+info+please"
    headers = get_headers(driver)
    headers.update({
        'content-type': app_form_url_encoded_content_type,
        'referer': os.path.join(base_url, f'certos/dashboard/{org_uuid}/1/'),
        'x-csrftoken': get_csrfmiddlewaretoken(None, driver, base_url)
    })
    response = requests.request("POST", url, headers=headers, data=body)
    if info_request_msg not in response.text:
        raise Exception(f'Note was not added. Response was {response.text}')


def request_more_info_via_API(driver, base_url, org_uuid):
    url = os.path.join(base_url, f'certos/dashboard/{org_uuid}/1/')
    body = f'csrfmiddlewaretoken={get_csrfmiddlewaretoken(None, driver, base_url)}&push_back_declaration='
    headers = get_headers(driver)
    headers.update({
        'content-type': app_form_url_encoded_content_type,
        'referer': url
    })
    response = requests.request("POST", url, headers=headers, data=body)
    if 'The questions were sent back to the client' not in response.text:
        raise Exception(f'Note was not added. Response was {response.text}')


def add_requested_info_via_API(driver, base_url, org_uuid, cert_version_id, question_key, info_provided_msg):
    url = os.path.join(base_url, f'organisation/{org_uuid}/certificate/1/version/{cert_version_id}/')
    body = {'time': '283',
            'csrfmiddlewaretoken': get_csrfmiddlewaretoken(None, driver, base_url),
            question_key: info_provided_msg,
            'submit-survey-signal': 'submit-survey-signal'}
    headers = get_headers(driver)
    headers.update({
        'content-type': app_form_url_encoded_content_type,
        'referer': url
    })
    requests.request("POST", url, headers=headers, data=body)


def accept_answer_via_API(driver, base_url, org_uuid, note_id, question_pk, cert_pk):
    url = os.path.join(base_url, f'certos/dashboard/note/{note_id}/')
    body = f"question_pk={question_pk}&certification_pk={cert_pk}"
    headers = get_headers(driver)
    headers.update({
        'content-type': app_form_url_encoded_content_type,
        'referer': os.path.join(base_url, f'certos/dashboard/{org_uuid}/1/'),
        'x-csrftoken': get_csrfmiddlewaretoken(None, driver, base_url)
    })
    response = requests.request("POST", url, headers=headers, data=body)
    json = response.json()
    if not json.get('success'):
        raise Exception(f'Answer was not accepted. Response was {response.text}')


def survey_pages_get_request_helper(driver, base_url, referer_path, url_path, target_reached_locator, err_msg):
    headers = get_headers(driver)
    headers.update({
        'referer': os.path.join(base_url, referer_path)
    })
    url = os.path.join(base_url, url_path)
    response = requests.request("GET", url, headers=headers)
    soup = BeautifulSoup(response.text, features=html_parser)
    if soup.select_one(target_reached_locator) is None:
        raise Exception(err_msg)
    return soup


def request_CEP_audit_via_API(driver, base_url, username, org_uuid, org_name, ce_date_achieved):
    url = os.path.join(base_url, f'partners/request-ce-plus-audit/{org_uuid}/')
    payload = f'csrfmiddlewaretoken={get_csrfmiddlewaretoken(None, driver, base_url)}' \
              f'&valid_ce_certificate=Yes,+from+CyberSmart&organisation_name={org_name}+[TEST]' \
              f'&ce_date_achieved={ce_date_achieved}&first_name=auto_test_first_name&last_name=auto_test_last_name' \
              f'&email={username}&desktop_devices_in_scope=Test\nTest&mobile_devices_in_scope=Test'
    headers = get_headers(driver)
    headers.update({
        'referer': base_url,
        'content-type': app_form_url_encoded_content_type
    })
    response = requests.request("POST", url, headers=headers, data=payload)
    response_txt = response.text
    if 'You have successfully requested a Cyber Essentials Plus audit. Please wait for someone to contact you' \
            not in response_txt:
        raise RequestFailedException(f'CEP audit was not requested. Response was {response_txt}')


def add_CE_via_API(driver, base_url, org_uuid):
    url = os.path.join(
        base_url, f'partners/enable-cyber-essentials/{org_uuid}/?next=/organisation/{org_uuid}/certificates/')
    body = f'csrfmiddlewaretoken={get_csrfmiddlewaretoken(None, driver, base_url)}'
    headers = get_headers(driver)
    headers.update({
        'referer': os.path.join(base_url, f'organisation/{org_uuid}/certificates/'),
        'content-type': app_form_url_encoded_content_type
    })
    response = requests.request("POST", url, headers=headers, data=body)
    response_txt = response.text
    if 'cyber essentials subscription was enabled for this organisation' not in response_txt.lower():
        raise RequestFailedException(f'CE subscriptions was not added. Response was {response_txt}')


def assist_with_active_protect_via_API(driver, base_url, org_uuid):
    url = os.path.join(base_url, f'organisation/{org_uuid}/survey-get-devices/1/')
    headers = get_headers(driver)
    headers.update({'referer': base_url})
    return requests.request("GET", url, headers=headers)


def get_org_cert_status_via_API(base_url, access_token):
    url = os.path.join(base_url, 'api/v3/organisations/certifications/')
    headers = {'Authorization': f'Bearer {access_token}'}
    return requests.request("GET", url, headers=headers)


def send_declaration_for_external_signing_via_API(driver, base_url, org_uuid, email):
    url = os.path.join(base_url, f'organisation/{org_uuid}/declaration/1/external-signing/')
    payload = (f'csrfmiddlewaretoken={get_csrfmiddlewaretoken(None, driver, base_url)}&name=test&title=test'
               f'&email={email}&secondary_email=')
    headers = get_headers(driver)
    headers.update({
        'referer': os.path.join(base_url, f'organisation/{org_uuid}/declaration/1/'),
        'content-type': app_form_url_encoded_content_type
    })
    response = requests.request("POST", url, headers=headers, data=payload)
    response_txt = response.text
    if 'You have sent this declaration for external signing' not in response_txt:
        raise RequestFailedException(f'External declaration was not sent. Response was {response_txt}')


def submit_declaration_with_insurance_upgrade_via_API(driver, base_url, org_uuid):
    url = os.path.join(base_url, f'organisation/{org_uuid}/declaration/1/external/')
    payload = (f'csrfmiddlewaretoken={get_csrfmiddlewaretoken(None, driver, base_url)}&insurance_upgrade=true&'
               'name=auto_test_first_name%20auto_test_last_name&'
               f'job=test&signature_name={img_str}&action=')
    headers = get_headers(driver)
    headers.update({
        'referer': os.path.join(base_url, f'organisation/{org_uuid}/declaration/1/external/'),
        'content-type': app_form_url_encoded_content_type
    })
    response = requests.request("POST", url, headers=headers, data=payload)
    response_txt = response.text
    if 'Declaration Signed' not in response_txt:
        raise RequestFailedException(f'Declaration was not signed. Response was {response_txt}')


def submit_declaration_with_insurance_opt_in_via_API(driver, base_url, org_uuid, email):
    url = os.path.join(base_url, f'organisation/{org_uuid}/declaration/1/external/')
    payload = (f'csrfmiddlewaretoken={get_csrfmiddlewaretoken(None, driver, base_url)}&insurance_choice=true'
               f'&other_reason=&revenue=10000000&email_address={email}&insurance_upgrade=false&name=test&job=test'
               f'&signature_name={img_str}&action=')
    headers = get_headers(driver)
    headers.update({
        'referer': os.path.join(base_url, f'organisation/{org_uuid}/declaration/1/external/'),
        'content-type': app_form_url_encoded_content_type
    })
    response = requests.request("POST", url, headers=headers, data=payload)
    response_txt = response.text
    if 'Declaration Signed' not in response_txt:
        raise RequestFailedException(f'Declaration was not signed. Response was {response_txt}')
