import functools

import pytest

from acceptance.acceptance_tests.helpers.custom_exceptions import KnownFailureException
from acceptance.acceptance_tests.helpers.settings_checks import is_cs_direct_vss_on
from acceptance.acceptance_tests.helpers.waffle_checks import is_waffle_switch_on, is_waffle_flag_on


def mark_as_known_failure_if_switch_on(switch_name):
    """
    Generates a KnownFailureException for tests with known failures due to a specific feature flag being switched on.
    """

    def decorator(test_func):
        @functools.wraps(test_func)
        def wrapper(*args, **kwargs):
            driver = kwargs.get('driver')
            base_url = kwargs.get('base_url')
            username = kwargs.get('username')
            password = kwargs.get('password')

            waffle_switch_status = is_waffle_switch_on(driver, base_url, username, password, switch_name)

            try:
                result = test_func(*args, **kwargs)
                return result
            except Exception as e:
                if waffle_switch_status:
                    raise KnownFailureException(
                        f'Test failed with known issue due to waffle switch {switch_name} being on.'
                        f' Original error: {str(e)}')
                else:
                    raise

        return wrapper

    return decorator


def skip_unless_waffle_switch_on(switch_name):
    def decorator(test_func):
        @functools.wraps(test_func)
        def wrapper(*args, **kwargs):
            driver = kwargs.get('driver')
            base_url = kwargs.get('base_url')
            username = kwargs.get('username')
            password = kwargs.get('password')

            if not is_waffle_switch_on(driver, base_url, username, password, switch_name):
                pytest.skip(f'Waffle switch {switch_name} is off. Therefore, this test will be skipped')
            return test_func(*args, **kwargs)

        return wrapper

    return decorator


def skip_unless_waffle_switch_off(switch_name):
    def decorator(test_func):
        @functools.wraps(test_func)
        def wrapper(*args, **kwargs):
            driver = kwargs.get('driver')
            base_url = kwargs.get('base_url')
            username = kwargs.get('username')
            password = kwargs.get('password')

            if is_waffle_switch_on(driver, base_url, username, password, switch_name):
                pytest.skip(f'Waffle switch {switch_name} is on. Therefore, this test will be skipped')
            return test_func(*args, **kwargs)

        return wrapper

    return decorator


def skip_unless_cs_direct_vss_off():
    def decorator(test_func):
        @functools.wraps(test_func)
        def wrapper(*args, **kwargs):
            driver = kwargs.get('driver')
            base_url = kwargs.get('base_url')
            username = kwargs.get('username')
            password = kwargs.get('password')

            if is_cs_direct_vss_on(driver, base_url, username, password):
                pytest.skip('CS direct vss is on. Therefore, this test will be skipped')
            return test_func(*args, **kwargs)

        return wrapper

    return decorator


def mark_as_known_failure_on_exception(exception_type):
    """
    Decorator to mark a test as a known failure if it raises the specified exception.
    """

    def decorator(test_func):
        @functools.wraps(test_func)
        def wrapper(*args, **kwargs):
            try:
                result = test_func(*args, **kwargs)
                return result
            except exception_type as e:
                raise KnownFailureException(
                    f'Test failed with a known issue: {str(e)}'
                ) from e

        return wrapper

    return decorator


def skip_unless_staging():
    def decorator(test_func):
        @functools.wraps(test_func)
        def wrapper(*args, **kwargs):
            base_url = kwargs.get('base_url')
            if "thats" not in base_url:
                pytest.skip('Test skipped because environment is not staging')
            return test_func(*args, **kwargs)

        return wrapper

    return decorator

def skip_unless_waffle_flag_on(flag_name):
    def decorator(test_func):
        @functools.wraps(test_func)
        def wrapper(*args, **kwargs):
            driver = kwargs.get('driver')
            base_url = kwargs.get('base_url')
            username = kwargs.get('username')
            password = kwargs.get('password')

            if not is_waffle_flag_on(driver, base_url, username, password, flag_name):
                pytest.skip(f'Waffle flag {flag_name} is off. Therefore, this test will be skipped')
            return test_func(*args, **kwargs)

        return wrapper

    return decorator
