from selenium.webdriver.common.by import By

from acceptance.acceptance_tests.helpers.wait_statements import wait_until_presence_of_element, \
    wait_until_invisibility_of_element


class PlansPage:
    MONTHLY_SUBS = (By.CSS_SELECTOR, '[class*="subscription-period monthly"]')
    YEARLY_SUBS = (By.CSS_SELECTOR, '[class*="subscription-period annual"]')
    ANIMATING_PLAN_PRICE = (By.XPATH, '//*[contains(@id, "plan-price") and text()="XX.XX"]')
    COMPANY_SIZE = (By.CLASS_NAME, 'dropdown-toggle')
    VIEW_CERTS_ONLY = (By.XPATH, '//*[@class="toggle-thumb"][./../../span[text()="View certifications only"]]')
    ALREADY_CERTIFIED = (By.XPATH, '//*[@class="toggle-thumb"][./../../span[text()="I already have Cyber Essentials"]]')
    CARD_INFO = (By.CLASS_NAME, 'software-product-name')
    CHOOSE_BUTTON = (By.CSS_SELECTOR, '.software-plans .choose-button-title')
    CERT_PLANS_DISPLAYED = (By.CSS_SELECTOR, '[class="certifications-plans"][style="display: block;"]')
    CERT_PLANS_NOT_DISPLAYED = (By.CSS_SELECTOR, '[class="certifications-plans"][style="display: none;"]')
    CERT_PLANS_WITHOUT_CE_DISPLAYED = (
        By.CSS_SELECTOR, '[class="certifications-plans-without-ce"][style="display: block;"]')
    CERT_PLANS_WITHOUT_CE_NOT_DISPLAYED = (
        By.CSS_SELECTOR, '[class="certifications-plans-without-ce"][style="display: none;"]')
    SOFTWARE_PLANS_DISPLAYED = (By.CSS_SELECTOR, '[class="software-plans"][style="display: block;"]')
    SOFTWARE_PLANS_NOT_DISPLAYED = (By.CSS_SELECTOR, '[class="software-plans"][style="display: none;"]')
    DIVIDE_LINE = (By.CLASS_NAME, 'divide-line')

    def __init__(self, driver):
        self.driver = driver

    @staticmethod
    def buy_now_locator(card_title):
        return f'//*[@class="software-plans"]//*[@class="title-container"]/' \
               f'*[@class="card-title" and text()="{card_title}"]/../following-sibling::*/button/*[text()="Buy Now"]'

    @staticmethod
    def company_size_option_locator(company_size):
        return By.XPATH, f'//a[text()="{company_size}"]'

    def choose_subscription_type(self, subs_type):
        if subs_type.lower() == 'annual':
            self.driver.find_element(*self.YEARLY_SUBS).click()
            wait_until_presence_of_element(self.driver, self.MONTHLY_SUBS)
        else:
            self.driver.find_element(*self.MONTHLY_SUBS).click()

    def select_company_size(self, company_size):
        self.driver.find_element(*self.COMPANY_SIZE).click()
        wait_until_presence_of_element(self.driver, self.company_size_option_locator(company_size), 3).click()

    def wait_until_page_loads(self):
        wait_until_presence_of_element(self.driver, self.MONTHLY_SUBS)
        wait_until_invisibility_of_element(self.driver, self.ANIMATING_PLAN_PRICE)

    def get_software_product_names_on_cards(self):
        return self.driver.find_elements(*self.CARD_INFO)

    def buy_now(self, product_title):
        buy_now_button = self.driver.find_element(By.XPATH, self.buy_now_locator(product_title))
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        buy_now_button.click()

    def get_choose_buttons(self):
        return self.driver.find_elements(*self.CHOOSE_BUTTON)

    def view_certs_only(self):
        self.driver.find_element(*self.VIEW_CERTS_ONLY).click()

    def view_certs_without_ce_only(self):
        self.driver.find_element(*self.ALREADY_CERTIFIED).click()

    def only_cert_plans_displayed(self):
        self.driver.find_element(*self.CERT_PLANS_WITHOUT_CE_NOT_DISPLAYED)
        self.driver.find_element(*self.SOFTWARE_PLANS_NOT_DISPLAYED)
        return self.driver.find_element(*self.CERT_PLANS_DISPLAYED).is_displayed()

    def only_cert_plans_without_ce_displayed(self):
        self.driver.find_element(*self.CERT_PLANS_NOT_DISPLAYED)
        self.driver.find_element(*self.SOFTWARE_PLANS_NOT_DISPLAYED)
        return self.driver.find_element(*self.CERT_PLANS_WITHOUT_CE_DISPLAYED).is_displayed()
