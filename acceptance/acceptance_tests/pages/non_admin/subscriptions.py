import os

from selenium.webdriver.common.by import By

from acceptance.acceptance_tests.helpers.pdf_reader import get_text_from_pdf
from acceptance.acceptance_tests.helpers.wait_statements import wait_until_visibility_of_element, \
    wait_until_presence_of_element
from acceptance.acceptance_tests.pages.non_admin.base import BasePage


class SubscriptionsPage(BasePage):
    NO_CE_SUBSCRIPTION_INDICATOR = (By.XPATH, '//*[contains(.,"Certification not started")]')
    PLAN_NAME = (By.CLASS_NAME, 'plan')
    CERT_NAME = (By.CLASS_NAME, 'certef-name')
    PRICE_BILLING_DATA_ITEM = (By.XPATH, '//*[contains(@class, "billing-data-item")][.//*[contains(@class, '
                                         '"data-key")] and .//*[contains(text(), "Price")]]')
    NEXT_BILLING_DATA_ITEM = (By.XPATH, '//*[contains(@class, "billing-data-item")][.//*[contains(text(), "Next billing")]]')
    DATA_VALUE = (By.CLASS_NAME, 'data-value')
    PURCHASE_ORDER_ON_TABLE = (By.CSS_SELECTOR, '.billing-data-item .po-text')
    PURCHASE_ORDER_ON_MODAL = (By.CSS_SELECTOR, '.billing-actions .po-text')
    PURCHASE_ORDER_PDF_LINK = (By.CSS_SELECTOR, '.po a')
    SUBSCRIPTION_ID = (By.CSS_SELECTOR, '[data-subscription]')
    PURCHASE_SUBSCRIPTION_BUTTON = (By.CSS_SELECTOR, '[id*=add-subscription-button]')
    PRIVACY_TOOLBOX_SUBS_INDICATOR = (By.XPATH, '//*[contains(@class, "subscription-cell")][.//*[contains(text(), '
                                                '"Privacy Toolbox")] and .//*[contains(text(), "Purchased")]]')
    BUNDLE_INDICATOR = (By.CSS_SELECTOR, '.bundle .chip')
    REQUEST_CANCELLATION_OPTION = (By.CSS_SELECTOR, 'a[id*="renewal-cancellation"]')
    CANCEL_REQUEST_SENT_LABEL = (By.XPATH, '//*[contains(text(),"Cancellation requested")]')
    RENEWAL_CANCELLED_LABEL = (By.XPATH, '//*[contains(text(),"Renewal cancelled")]')
    VAT_INFO = (By.CSS_SELECTOR, '[test-id="vat_info"]')

    def __init__(self, driver):
        super().__init__(driver)


    @staticmethod
    def subscription_row_locator(company_name):
        return By.XPATH, f'//*[contains(@class, "sub-row")][.//*[contains(text(),"{company_name}")]]'

    @staticmethod
    def subscription_cell_locator(cert_title):
        return By.XPATH, f'//*[contains(@class, "subscription-cell")][.//*[text()="{cert_title}"]]'

    @staticmethod
    def row_product_section_locator(product_title):
        return By.XPATH, f'//*[contains(@aria-labelledby, "sub-row")][.//*[contains(text(), "{product_title}")]]'

    def go_to_page(self, base_url):
        self.driver.get(os.path.join(base_url, 'partners/subscriptions-overview/'))

    def go_to_specific_subscription(self, base_url, company_name):
        self.driver.get(os.path.join(base_url, f'partners/subscriptions-overview/?search={company_name}'))

    def get_subscription_row(self, company_name):
        return wait_until_visibility_of_element(self.driver, self.subscription_row_locator(company_name))

    def get_subscription_cell(self, company_name, cert_title):
        return self.get_subscription_row(company_name).find_element(*self.subscription_cell_locator(cert_title))

    def get_next_billing_date(self, company_name, product_title='Cyber Essentials'):
        subscription_row = self.get_subscription_row(company_name)
        subscription_body_container = subscription_row.find_element(*self.row_product_section_locator(product_title))
        next_billing_date_item = subscription_body_container.find_element(*self.NEXT_BILLING_DATA_ITEM)
        return next_billing_date_item.get_attribute('innerHTML').strip()

    def subscription_is_active(self, company_name, product_title='Cyber Essentials'):
        subscription_row = self.get_subscription_row(company_name)
        subscription_row.find_element(*self.row_product_section_locator(product_title))

    def ce_survey_has_not_started(self, company_name):
        self.get_subscription_row(company_name).find_element(*self.NO_CE_SUBSCRIPTION_INDICATOR)

    def get_plan_name(self, company_name):
        return self.get_subscription_row(company_name).find_element(*self.PLAN_NAME).get_attribute('innerHTML')

    def get_cert_name(self, company_name):
        return self.get_subscription_row(company_name).find_element(*self.CERT_NAME).get_attribute('innerHTML')

    def get_purchase_order_on_table(self, company_name, product_title):
        return self.get_subscription_row(company_name).find_element(
            *self.row_product_section_locator(product_title)).find_element(
            *self.PURCHASE_ORDER_ON_TABLE).get_attribute('innerHTML')

    def subscription_does_not_exist(self, company_name, product_title='Cyber Essentials'):
        return len(self.get_subscription_row(company_name).find_elements(
            *self.row_product_section_locator(product_title))) == 0

    def get_subscription_details(self, company_name, product_title='Cyber Essentials'):
        return self.get_subscription_row(company_name).find_element(
            *self.row_product_section_locator(product_title)).get_attribute('innerHTML')

    def get_purchase_order_on_modal(self, company_name, product_title):
        return self.get_subscription_row(company_name).find_element(
            *self.row_product_section_locator(product_title)).find_element(
            *self.PURCHASE_ORDER_ON_MODAL).get_attribute('innerHTML')

    def get_subscription_id(self, company_name):
        return self.get_subscription_row(company_name).find_element(
            *self.SUBSCRIPTION_ID).get_attribute('data-subscription')

    def get_purchase_order_pdf_text(self, company_name, product_title):
        po_download_link = self.get_subscription_row(company_name).find_element(
            *self.row_product_section_locator(product_title)).find_element(
            *self.PURCHASE_ORDER_PDF_LINK).get_attribute('href')
        return get_text_from_pdf(po_download_link)

    def purchase_subscription_btn_is_displayed(self, company_name):
        subscription_row = self.get_subscription_row(company_name)
        subscription_row.find_element(*self.PURCHASE_SUBSCRIPTION_BUTTON)

    def privacy_toolbox_subs_is_active(self, company_name):
        subscription_row = self.get_subscription_row(company_name)
        subscription_row.find_element(*self.PRIVACY_TOOLBOX_SUBS_INDICATOR)

    def get_bundle_label(self, company_name, cert_title):
        return self.get_subscription_cell(company_name, cert_title).find_element(
            *self.BUNDLE_INDICATOR).get_attribute('innerHTML')

    def request_cancellation_option_is_present(self):
        wait_until_presence_of_element(self.driver, self.REQUEST_CANCELLATION_OPTION)

    def cancel_request_sent_label_is_displayed(self):
        wait_until_presence_of_element(self.driver, self.CANCEL_REQUEST_SENT_LABEL)

    def renewal_cancelled_label_is_displayed(self):
        wait_until_presence_of_element(self.driver, self.RENEWAL_CANCELLED_LABEL)

    def cancel_request_sent_label_is_not_displayed(self):
        cancel_request_sent_label = self.driver.find_elements(*self.CANCEL_REQUEST_SENT_LABEL)
        return len(cancel_request_sent_label) == 0

    def request_cancellation_options_is_not_available(self):
        request_cancellation_option = self.driver.find_elements(*self.REQUEST_CANCELLATION_OPTION)
        return len(request_cancellation_option) == 0

    def get_vat_info(self):
        return wait_until_presence_of_element(self.driver, self.VAT_INFO).get_attribute('innerHTML')

