import copy

from acceptance.properties import users, org_uuids, appinstalls_1, appinstalls_2, appinstalls_4, \
    appinstalls_3, uuids_for_load_testing, cert_version_ids, bulk_org_ids, \
    active_protect_report_content, chargebee_customer_ids, chargebee_api_token, test_card_details, existing_orgs, \
    appinstalls_5, email_api_token, email_server_id, email_domain, webhook_body, trustd_device_id, trustd_customer_id, \
    trustd_client_id, additional_trust_device_ids, org_names, appuser_uuids, appinstalls_6, appinstalls_7, \
    appuser_uuids_for_v1_policies_diff_os_users, appuser_uuids_for_v1_policies_same_os_users, appinstalls_8, \
    appinstalls_9, trustd_policy_customer_id, appinstalls_11, \
    desktop_device_info, mobile_device_info, auto_test_org_prefix, trustd_org_ids_for_device_cleanup, \
    application_partner_ids, uuid_for_cap_v5_load_testing, waffle_switch_ids, appinstalls_mac_individual, \
    appinstalls_windows_individual, cap_policy_name_individual, appinstalls_mac_bulk, appinstalls_windows_bulk, \
    cap_policy_name_bulk, appinstalls_mac_bulk_uba, appinstalls_windows_bulk_uba, cap_policy_name_bulk_uba, \
    policy_name_for_trustd, trustd_policy_device_id, exertis_distributor_id, ce_survey_body, mobile_enrolment_key, \
    application_partner_org_ids, product_version_id, auto_test_trustd_org_prefix, opswat_payload


def get_username(user_type, base_url):
    user_type_ = copy.deepcopy(user_type)
    return users[user_type_][get_env(base_url)]['username']


def get_auto_test_org_prefix():
    return auto_test_org_prefix

def get_trustd_auto_test_org_prefix():
    return auto_test_trustd_org_prefix

def get_org_uuid(org_type, base_url):
    org_type_ = copy.deepcopy(org_type)
    return org_uuids[org_type_][get_env(base_url)]


def get_appuser_uuid(user_type, base_url):
    return appuser_uuids[user_type][get_env(base_url)]


def get_desktop_device_info(base_url):
    return desktop_device_info[get_env(base_url)]


def get_mobile_device_info(base_url):
    return mobile_device_info[get_env(base_url)]


def get_org_name(org_type, base_url):
    return org_names[org_type][get_env(base_url)]


def get_body_for_CE_survey(base_url, token, username, insurance_opt_in=True):
    survey_body_ = copy.deepcopy(ce_survey_body[get_env(base_url)])
    if 'develop' in base_url:
        username_key = 'response_3937_4.0'
        insurance_opt_in_key = 'response_3932_2.0'
    else:
        username_key = 'response_5224_4.0'
        insurance_opt_in_key = 'response_5219_2.0'
    survey_body_.update({
        'csrfmiddlewaretoken': token,
    })
    if insurance_opt_in:
        survey_body_.update({
            insurance_opt_in_key: 'on',
            username_key: username
        })
    return survey_body_


def get_appinstall_1(base_url):
    """
    Typically used for app-checkin tests, but may be used for other types of tests
    """
    appinstalls_ = copy.deepcopy(appinstalls_1)
    return appinstalls_[get_env(base_url)]


def get_appinstall_2(base_url):
    """
    Typically used for policies tests, but may be used for other types of tests
    """
    appinstalls_ = copy.deepcopy(appinstalls_2)
    return appinstalls_[get_env(base_url)]


def get_appinstall_4(base_url, platform):
    """
    Typically used for get-rules tests, but may be used for other types of tests
    """
    appinstalls_ = copy.deepcopy(appinstalls_4)
    return appinstalls_[get_env(base_url)][platform.lower()]


def get_appinstall_3(base_url, resolved_type):
    """
    Typically used for resolved checks tests, but may be used for other types of tests
    """
    appinstalls_ = copy.deepcopy(appinstalls_3)
    return appinstalls_[get_env(base_url)][resolved_type.lower()]


def get_appinstall_5(base_url):
    """
    Typically used for inactive policies tests, but may be used for other types of tests
    """
    appinstalls_ = copy.deepcopy(appinstalls_5)
    return appinstalls_[get_env(base_url)]


def get_appinstall_6(base_url):
    """
    Typically used for policies tests
    """
    appinstalls_ = copy.deepcopy(appinstalls_6)
    return appinstalls_[get_env(base_url)]


def get_appinstall_7(base_url):
    """
    Typically used for inactive policies tests, but may be used for other types of tests
    """
    appinstalls_ = copy.deepcopy(appinstalls_7)
    return appinstalls_[get_env(base_url)]


def get_appinstall_8(base_url):
    """
    Typically used for policies tests
    """
    appinstalls_ = copy.deepcopy(appinstalls_8)
    return appinstalls_[get_env(base_url)]


def get_appinstall_9(base_url):
    """
    Typically used for policies tests
    """
    appinstalls_ = copy.deepcopy(appinstalls_9)
    return appinstalls_[get_env(base_url)]


def get_appinstall_10(base_url):
    """
    Typically used for policies tests
    """
    appinstalls_ = copy.deepcopy(appinstalls_9)
    return appinstalls_[get_env(base_url)]


def get_appinstall_11(base_url):
    """
    Typically used for policies tests
    """
    appinstalls_ = copy.deepcopy(appinstalls_11)
    return appinstalls_[get_env(base_url)]


def get_appinstall_for_mac_indivudal(base_url):
    appinstalls_ = copy.deepcopy(appinstalls_mac_individual)
    return appinstalls_[get_env(base_url)]


def get_appinstall_for_windows_indivudal(base_url):
    appinstalls_ = copy.deepcopy(appinstalls_windows_individual)
    return appinstalls_[get_env(base_url)]


def get_appinstall_for_mac_bulk(base_url):
    appinstalls_ = copy.deepcopy(appinstalls_mac_bulk)
    return appinstalls_[get_env(base_url)]


def get_appinstall_for_windows_bulk(base_url):
    appinstalls_ = copy.deepcopy(appinstalls_windows_bulk)
    return appinstalls_[get_env(base_url)]


def get_appinstall_for_mac_bulk_uba(base_url):
    appinstalls_ = copy.deepcopy(appinstalls_mac_bulk_uba)
    return appinstalls_[get_env(base_url)]


def get_appinstall_for_windows_bulk_uba(base_url):
    appinstalls_ = copy.deepcopy(appinstalls_windows_bulk_uba)
    return appinstalls_[get_env(base_url)]


def get_cap_policy_name_individual(base_url):
    return cap_policy_name_individual[get_env(base_url)]


def get_cap_policy_name_bulk(base_url):
    return cap_policy_name_bulk[get_env(base_url)]


def get_cap_policy_name_bulk_uba(base_url):
    return cap_policy_name_bulk_uba[get_env(base_url)]


def get_uuid_for_load_testing(base_url):
    uuids_for_load_testing_ = copy.deepcopy(uuids_for_load_testing)
    return uuids_for_load_testing_[get_env(base_url)]


def get_uuid_for_cap_v5_load_testing():
    return uuid_for_cap_v5_load_testing

def get_cert_version_id(base_url):
    return cert_version_ids[get_env(base_url)]

def get_impersonated_user_id(base_url):
    return users['impersonated_user_id'][get_env(base_url)]

def get_bulk_org_id(base_url):
    return bulk_org_ids[get_env(base_url)]


def get_active_protect_report_content(report_type, base_url):
    return active_protect_report_content[report_type][get_env(base_url)]


def get_chargebee_customer_id(base_url):
    return chargebee_customer_ids[get_env(base_url)]


def get_chargebee_api_token():
    return chargebee_api_token


def get_test_card_details():
    return test_card_details


def get_existing_org_name(base_url):
    return existing_orgs[get_env(base_url)]


def get_email_api_token():
    return email_api_token


def get_email_server_id():
    return email_server_id


def get_email_domain():
    return email_domain


def get_mfa_token_user(base_url):
    return users['mfa_token_user'][get_env(base_url)]


def get_trustd_device_id(base_url):
    return trustd_device_id[get_env(base_url)]


def get_trustd_policy_device_id(base_url):
    return trustd_policy_device_id[get_env(base_url)]


def get_policy_name_for_trustd():
    return policy_name_for_trustd


def get_trustd_customer_id(base_url, org_type):
    return trustd_customer_id[get_env(base_url)][org_type]


def get_trustd_policy_customer_id(base_url):
    return trustd_policy_customer_id[get_env(base_url)]


def get_trustd_client_id(base_url):
    return trustd_client_id[get_env(base_url)]


def get_trustd_org_ids_for_device_cleanup(base_url):
    return trustd_org_ids_for_device_cleanup[get_env(base_url)]


def get_webhook_body():
    webhook_body_ = copy.deepcopy(webhook_body)
    return webhook_body_


def get_additional_trustd_device_ids(base_url):
    return additional_trust_device_ids[get_env(base_url)]


def get_appuser_uuids_for_v1_policies_diff_os_users(base_url):
    return appuser_uuids_for_v1_policies_diff_os_users[get_env(base_url)]


def get_appuser_uuids_for_v1_policies_same_os_users(base_url):
    return appuser_uuids_for_v1_policies_same_os_users[get_env(base_url)]


def get_application_partner_id(base_url):
    return application_partner_ids[get_env(base_url)]


def get_application_partner_org_id(base_url):
    return application_partner_org_ids[get_env(base_url)]


def get_waffle_switch_id(base_url, switch_name):
    return waffle_switch_ids[switch_name][get_env(base_url)]


def get_exertis_distributor_id():
    return exertis_distributor_id


def get_mobile_enrolment_key(base_url):
    return mobile_enrolment_key[get_env(base_url)]

def get_product_version_id(base_url):
    return product_version_id[get_env(base_url)]

def get_opswat_payload(device_id, serial_number, appuser_uuid):
    payload = copy.deepcopy(opswat_payload)
    payload.update({
        "device_id": device_id,
        "serial_number": serial_number,
        "app_user_uuid": appuser_uuid
    })
    return payload

def get_env(base_url):
    return 'dev' if 'develop' in base_url else 'stg'
