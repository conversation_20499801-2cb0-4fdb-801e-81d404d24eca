import os
from threading import Lock
from locust import FastHttpUser, task, between
from acceptance.load_tests.opswat_appinstall_details import app_install_details
from acceptance.acceptance_tests.helpers.test_logger import custom_print
from acceptance.load_tests.load_utils import generate_load, get_failure_types
from acceptance.properties_manager import get_opswat_payload

# Global index and lock to ensure unique appinstall per user
_appinstall_index = 0
_appinstall_lock = Lock()
_shared_appuser_uuid = "802fcbc9-841b-412b-9811-82ace9f1ea44"


class PatchUser(FastHttpUser):
    """Load test user that performs the full patching flow"""
    wait_time = between(3, 10)

    def on_start(self):
        """Initialize user with unique app install details"""
        global _appinstall_index
        with _appinstall_lock:
            self.appinstall = app_install_details[_appinstall_index % len(app_install_details)]
            _appinstall_index += 1

    @task
    def full_patching_flow(self):
        """Execute the complete patching flow with all three endpoints"""
        self._submit_software_info()
        installer_id = self._fetch_scheduled_patch_installers()
        if installer_id:
            self._log_patch_event(installer_id)

    def _submit_software_info(self):
        """POST to api/v3/opswat/software/"""
        url = "/api/v3/opswat/software/"
        payload = get_opswat_payload(self.appinstall['device_id'], self.appinstall['serial_number'], _shared_appuser_uuid)
        headers = {
            "accept": "application/json",
            "content-type": "application/json",
        }

        with self.client.post(url, json=payload, headers=headers, name="submit_software", catch_response=True) as response:
            if response.status_code != 201:
                error_msg = f"Submit software failed - Response status code was {response.status_code}  - Body: {response.text}"
                response.failure(error_msg)
                raise Exception(error_msg)

    def _fetch_scheduled_patch_installers(self):
        """GET from api/v3/opswat-patch/scheduled-product-installers/ and return installer ID"""
        url = (
            "/api/v3/opswat-patch/scheduled-product-installers/"
            f"?device_id={self.appinstall['device_id']}&serial_number={self.appinstall['serial_number']}&all=true"
        )
        headers = {
            "accept": "application/json",
            "APPUSER-UUID": _shared_appuser_uuid
        }
        with self.client.get(url, headers=headers, name="scheduled_product_installers", catch_response=True) as response:
            if response.status_code != 200:
                error_msg = f"Fetch installers failed - Response status code was {response.status_code}  - Body: {response.text}"
                response.failure(error_msg)
                raise Exception(error_msg)

            try:
                response_json = response.json()
                if response_json and len(response_json) > 0:
                    # Extract the first installer's ID
                    installer_id = response_json[0].get('id')
                    return installer_id
                else:
                    custom_print(f"No scheduled installers found for device {self.appinstall['device_id']}")
                    return None
            except Exception as e:
                error_msg = f"Failed to parse scheduled installers response: {str(e)}"
                response.failure(error_msg)
                return None

    def _log_patch_event(self, installer_id):
        """POST to api/v3/opswat-patch/event-log/"""
        url = "/api/v3/opswat-patch/event-log/"
        payload = {
            "opswat_scheduled_product_installer": installer_id,
            "status": "pending",
            "error_code": "string",
            "details": "string",
            "device_id": self.appinstall['device_id'],
            "serial_number": self.appinstall['serial_number']
        }
        headers = {
            "content-type": "application/json",
            "APPUSER-UUID": _shared_appuser_uuid
        }
        with self.client.post(url, json=payload, headers=headers, name="log_event", catch_response=True) as response:
            if response.status_code != 201:
                error_msg = f"Log event failed - Response status code was {response.status_code} - Body: {response.text}"
                response.failure(error_msg)
                raise Exception(error_msg)


def test_load_full_patching_flow_CUSTEAM_T1380(base_url):
    """Load test for the complete patching flow"""
    locust_ui = False
    number_of_users = int(os.environ.get("LOAD_TEST_NUMBER_OF_USERS", "5"))
    spawn_rate = int(os.environ.get("LOAD_TEST_SPAWN_RATE", "1"))
    duration_of_load_test = int(os.environ.get("LOAD_TEST_DURATION", "10"))
    host = base_url.rstrip("/")
    user_class = PatchUser

    env = generate_load(base_url, locust_ui, number_of_users, spawn_rate, duration_of_load_test, host, user_class)

    # assert to check that the test is actually working
    assert env.stats.total.avg_response_time > 0

    # Get stats for each endpoint
    software_stat = env.stats.get("submit_software", "POST")
    installers_stat = env.stats.get("scheduled_product_installers", "GET")
    event_log_stat = env.stats.get("log_event", "POST")

    # Get failure types for each endpoint
    software_failures = get_failure_types(env, "POST", "submit_software")
    installers_failures = get_failure_types(env, "GET", "scheduled_product_installers")
    event_log_failures = get_failure_types(env, "POST", "log_event")

    custom_print("******************** Full Patching Flow Load Test Results ***********************")

    # Submit Software Stats
    custom_print("\n--- POST /api/v3/opswat/software/ ---")
    custom_print(f"Median response time: {software_stat.median_response_time} ms")
    custom_print(f"Average response time: {software_stat.avg_response_time} ms")
    custom_print(f"95th percentile: {software_stat.get_response_time_percentile(0.95)} ms")
    custom_print(f"99th percentile: {software_stat.get_response_time_percentile(0.99)} ms")
    custom_print(f"Failures (status 0): {software_failures.get('status0_failures')}")
    custom_print(f"Failures (non-zero): {software_failures.get('non_zero_failures')}")
    custom_print(f"Number of requests: {software_stat.num_requests}")

    # Scheduled Product Installers Stats
    custom_print("\n--- GET /api/v3/opswat-patch/scheduled-product-installers/ ---")
    custom_print(f"Median response time: {installers_stat.median_response_time} ms")
    custom_print(f"Average response time: {installers_stat.avg_response_time} ms")
    custom_print(f"95th percentile: {installers_stat.get_response_time_percentile(0.95)} ms")
    custom_print(f"99th percentile: {installers_stat.get_response_time_percentile(0.99)} ms")
    custom_print(f"Failures (status 0): {installers_failures.get('status0_failures')}")
    custom_print(f"Failures (non-zero): {installers_failures.get('non_zero_failures')}")
    custom_print(f"Number of requests: {installers_stat.num_requests}")


    # Event Log Stats
    custom_print("\n--- POST /api/v3/opswat-patch/event-log/ ---")
    custom_print(f"Median response time: {event_log_stat.median_response_time} ms")
    custom_print(f"Average response time: {event_log_stat.avg_response_time} ms")
    custom_print(f"95th percentile: {event_log_stat.get_response_time_percentile(0.95)} ms")
    custom_print(f"99th percentile: {event_log_stat.get_response_time_percentile(0.99)} ms")
    custom_print(f"Failures (status 0): {event_log_failures.get('status0_failures')}")
    custom_print(f"Failures (non-zero): {event_log_failures.get('non_zero_failures')}")
    custom_print(f"Number of requests: {event_log_stat.num_requests}")

    # Overall Stats
    custom_print("\n--- Overall Test Summary ---")
    custom_print(f"Total requests: {env.stats.total.num_requests}")
    custom_print(f"Total failures: {env.stats.total.num_failures}")
