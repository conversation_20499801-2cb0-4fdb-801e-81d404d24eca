version: '3.2'

services:

  valkey:
    image: valkey/valkey:alpine
    ports:
      - "6379:6379"

  postgres:
    image: postgres:17-alpine
    restart: always
    environment:
      - POSTGRES_USER=cybersmart
      - POSTGRES_PASSWORD=password
      - POSTGRES_HOST_AUTH_METHOD=trust
      - POSTGRES_MULTIPLE_DATABASES=cybersmart_test,cybersmart_test_archive
    volumes:
      - type: tmpfs
        target: /dev/shm/pgdata/data
      - ./pg-init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"

  tests:
    build:
      context: .
      dockerfile: ./compose/tests/Dockerfile
      tags:
        - cyber-smart-tests
    depends_on:
      - postgres
      - valkey
    volumes:
      - ./results:/results
    environment:
      - DB_HOST=postgres
      - DB_USER=cybersmart
      - DB_PASSWORD=password
      - DB_NAME=cybersmart_test
      - ARCHIVE_DB_NAME=cybersmart_test_archive
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}
      - MIGRATIONCI_S3_BUCKET=cs-django-migration-ci
    command: /start.sh
