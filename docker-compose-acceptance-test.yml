version: '3.2'

services:
  selenium:
    image: selenium/hub:4.33.0-20250606
    container_name: seleniumHub
    ports:
      - '4444:4444'
      - '4442:4442'
      - '4443:4443'
  firefox:
    image: selenium/node-firefox:4.33.0-20250606
    shm_size: 2gb
    environment:
      - SE_EVENT_BUS_HOST=seleniumHub
      - SE_EVENT_BUS_PUBLISH_PORT=4442
      - SE_EVENT_BUS_SUBSCRIBE_PORT=4443
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_MAX_INSTANCES=1
    depends_on:
      - selenium
    deploy:
      replicas: 8

  acceptance:
    build:
      context: .
      dockerfile: compose/acceptance_tests/Dockerfile
      tags:
        - cyber-smart-acceptance
    depends_on:
      - selenium
    ports:
      - "8000:8000"
    volumes:
      - ./results:/results
      - ./acceptance/files.txt:/app/acceptance/files.txt
    environment:
      - PYTHONPATH=/app
      - LOAD_TEST_NUMBER_OF_USERS=$LOAD_TEST_NUMBER_OF_USERS
      - LOAD_TEST_SPAWN_RATE=$LOAD_TEST_SPAWN_RATE
      - LOAD_TEST_DURATION=$LOAD_TEST_DURATION
      - LOAD_TEST_CAP_VERSION=$LOAD_TEST_CAP_VERSION
    command: /start.sh $TEST_ENV $TESTS_TO_RUN $TRUSTD_CLIENT_SECRET_DEV $TRUSTD_CLIENT_SECRET_STG $PLAYSTORE_FIRST_NAME $PLAYSTORE_EMAIL_ADDRESS $PLAYSTORE_PASSWORD $LAMBDA_TEST_USERNAME $LAMBDA_TEST_ACCESS_KEY
