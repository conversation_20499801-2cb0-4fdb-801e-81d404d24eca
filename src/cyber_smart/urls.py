import debug_toolbar
from django.conf import settings
from django.contrib import admin
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from django.shortcuts import redirect
from django.urls import include, re_path as url
from django.views.decorators.csrf import csrf_exempt
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from oauth2_provider.urls import base_urlpatterns
from rest_framework import permissions

from accounts.views import signup_redirect_view
from api.v1.mobile_activate.views import MobileQRCode
from common.admin import admin_site
from common.views import (
    PasswordResetViewCustom,
    PasswordResetFromKeyCustomView,
    TriggerBillingRenewalEmailsView,
    PlanTransitionsTriggerView,
    SideNavigationStateView,
    PostmarkWebHookView,
    FEStoryBook,
    RedirectIfAuthenticatedLoginView,
)
from dashboard.views import CVEView
from konsento.server import CyberSmartServer
from rulebook.views import UploadCertificationVersionView, PervadeQuestionsView, MigrateAnswersView
from .partners_api_swagger import partner_swagger

schema_view = get_schema_view(
    openapi.Info(
        title="CyberSmart API",
        default_version='v1',
        description="CyberSmart API documentation",
        terms_of_service="https://app.cybersmart.co.uk",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="CyberSmart License")
    ),
    validators=['flex', 'ssv'],
    public=True,
    permission_classes=[permissions.IsAuthenticated, permissions.IsAdminUser],
)

# needs to be initialized before urlpatterns and admin
simple_sso_server = CyberSmartServer()

admin.autodiscover()
admin.site.login = login_required(admin.site.login)

urlpatterns = [
    # Models Docs.
    url(r'^admin/migrate/answers$', MigrateAnswersView.as_view(), name='migrate-answers'),
    url(r'^admin/fe', FEStoryBook.as_view(), name='fe-story-book'),
    url(r"^admin/pervade/questions$", PervadeQuestionsView.as_view(), name="pervade-questions"),
    url(
        r"^admin/upload/certification/$", UploadCertificationVersionView.as_view(), name="upload-certification-version"
    ),
    url(r'^admin/shell/', include('django_admin_shell.urls')),
    url(r'^admin/', admin_site.urls),
    url(r'^impersonate/', include('impersonate.urls')),
    url(r'^robots.txt$', lambda r: HttpResponse("User-agent: *\nDisallow: /", content_type="text/plain")),
    url(r'^__debug__/', include(debug_toolbar.urls)),
    url(r'^silk/', include('silk.urls', namespace='silk')),

    url(r'^api/', include('api.v1.urls', namespace='api')),
    url(r'^api/v2/', include('api.v2.urls', namespace='api-v2')),
    url(r'^api/v3/', include('api.v3.urls', namespace='api-v3')),
    # This is where SSO authentication gets handled at server side
    url(r'^api/server/', include(simple_sso_server.get_urls())),

    url(r'^oauth2/', include((base_urlpatterns, 'oauth2_provider'), namespace='oauth2_provider')),
]


# API documentation
urlpatterns += [
    url(r'^swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    url(r'^swagger/$', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    url(r'^redoc/$', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    url(r'^partner/swagger/$', partner_swagger.with_ui(cache_timeout=0), name='partner-swagger-ui'),
]

urlpatterns += [
    url(r"^password/reset/key/(?P<uidb36>[0-9A-Za-z]+)-(?P<key>.+)/$",
        PasswordResetFromKeyCustomView.as_view(),
        name="account_reset_password_from_key"),
    url(r"^password/reset/$", PasswordResetViewCustom.as_view(), name="account_reset_password"),

    url(r"^login/$", RedirectIfAuthenticatedLoginView.as_view(), name="account_login"),

    # Include the allauth and 2FA urls from their respective packages.
    url(r'^', include('allauth_2fa.urls')),
    url(r'^', include('allauth.urls')),
    url(r'^waffle/', include('waffle.urls')),

    url(r'^', include('dashboard.urls', namespace='dashboard')),
    url(r'^', include('rulebook.urls', namespace='rulebook')),
    url(r'^', include("appusers.urls", namespace="appusers")),
    url(r'^', include("smart_policies.urls", namespace="smart_policies")),

    url(r'^distributors/', include('distributors.urls', namespace='distributors')),
    url(r'^partners/', include('partners.urls', namespace='partners')),
    url(r"^organisation/", include("organisations.urls", namespace="organisations")),
    url(r'^billing/', include('billing.urls', namespace='billing')),
    url(r'', include('accounts.urls', namespace='settings')),
    url(r'^notifications/', include('notifications.urls', namespace='notifications')),
    url(r"^lms/", include("lms.urls", namespace="lms")),
    url(r"^", include("learn.urls", namespace="learn")),
    url(r"^", include("trustd.urls", namespace="trustd")),

    url(r'^social-tags/', include('social_tags.urls', namespace='social-tags')),

    url(r'^signup/', include('signup.urls', namespace='signup')),

    url(r"^salesforce/", include("sales.salesforce.urls", namespace="salesforce")),

    url(r'^cves/$', CVEView.as_view(), name='software-cves'),
    url(r'^ckeditor/', include('ckeditor_uploader.urls')),
    url(r"^smart-score/", include("smart_score.urls", namespace="smart-score")),
    # Redirects the old login urls to the new one
    url(r'^accounts/signup/$', signup_redirect_view),
    url(r'^accounts/login/$', lambda r: redirect('/login/', permanent=False)),

    url('select2/', include('django_select2.urls')),

    # Vodafone Webhooks
    url(r'^api/vodafone/', include('vodafone.urls', namespace='vodafone')),
    # Postmark Webhook
    url(r'^postmark/webhook$', PostmarkWebHookView.as_view()),
    # Thunderbird urls
    url(r'^thunderbird/', include('thunderbird.urls', namespace='thunderbird')),
    # QR code page
    url(r'^qr-code/$', MobileQRCode.as_view(), name='qr-code'),
    url(r'^side/navigation/$', csrf_exempt(SideNavigationStateView.as_view()), name="cs-sidenav"),
]

# add debug trigger
if not settings.IS_PROD:
    urlpatterns += [
        url(
            r'^billing/renewal/emails/trigger/$',
            TriggerBillingRenewalEmailsView.as_view(),
            name='trigger-billing-renewal-emails'
        ),
        url(r'^plan/transitions/trigger/$', PlanTransitionsTriggerView.as_view(), name='trigger-plan-transitions'),
        # url('admin/doc/', include('django.contrib.admindocs.urls')),
    ]

handler400 = "common.views.bad_request"
handler403 = "common.views.permission_denied"
handler404 = "common.views.page_not_found"
handler500 = "common.views.server_error"

# noinspection PyInterpreter
if settings.DEBUG and not settings.IS_STAGE and not settings.IS_DEVELOP:
    from django.conf.urls.static import static

    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
