import logging
from django.db import transaction
from django.db.models import Q

from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from common.forms import ListField
from django import forms
from django.core.exceptions import ValidationError
from api.v3.opswat.utils import CUSTOM_ID_PREFIX
from opswat.models import ProductVersion
from opswat_patch.models import (
    OpswatScheduledProductInstaller,
    OpswatPatchEventLog, OpswatPatchAttempt, OpswatPatchJob,
)
from opswat_patch.models.constants import PENDING
from opswat.models import InstalledProductVersion
from opswat_patch.utils import enrich_installed_software_with_installers
from websocket_integration.services import publish_patch_installer_event


logger = logging.getLogger("opswat_patch")


class SchedulePatchInstallationForm(forms.Form):
    """
    A form to facilitate scheduling patch installations for a set of packages for a given app installs.

    This class provides mechanisms to validate and schedule patch installations
    associated with a specific package level and application installation. The
    form ensures that all package IDs provided are valid and not already scheduled,
    and provides functions to retrieve validated packages and schedule the patches.

    Attributes
    ----------
    app_installs : ListField
        A required field containing a list of application installation IDs to install.
        The IDs must correspond to primary keys of valid models.
    packages : ListField
        A required field containing a list of package IDs to install. The IDs must
        correspond to primary keys of valid models.
    validated_packages : list
        Stores the list of validated package objects after successful form
        validation.
    organisation : organisations.models.Organisation
        The organisation associated with the app installs.
    package_level : str
        The hierarchy level of the package (e.g., app_install or organization).
    user : UserType
        The user who initiated the patch scheduling process.

    Raises
    ------
    ValueError
        If the `package_level` provided is not supported.
    ValidationError
        If any of the package IDs are invalid, do not exist, or have already been
        scheduled for a patch installation.
    """
    app_installs = ListField(
        required=True,
        help_text="List of app install IDs to install. Must be valid model primary keys."
    )
    packages = ListField(
        required=True,
        help_text="List of package IDs to install. Must be valid model primary keys."
    )

    def __init__(self, organisation, package_level: str, user, *args, **kwargs):
        """
        Initializes an instance of the class.

        Attributes:
            organisation: The organisation associated with the app installs.
            package_level: The level of the package (e.g., "app_install", "organisation").
            user: The user who initiated the patch scheduling process.
            validated_packages: A list that holds validated packages. Initially empty.
            _model_map: A dictionary mapping package level types to corresponding summary models.

        Raises:
            ValueError: If an unsupported package level is provided.

        """
        super().__init__(*args, **kwargs)
        self.organisation = organisation
        self.package_level = package_level
        self.user = user
        self.validated_packages = []

        self._model_map = {
            "app_install": InstalledSoftwareAppInstallIndividual,
            "organisation": InstalledSoftwareOrganisationIndividual,
        }

        if self.package_level not in self._model_map:
            raise ValueError(f"Unsupported package_level: {self.package_level}")

    def clean_app_installs(self) -> list:
        """
        Cleans and filters the app installations related to the specified organisation.

        This method filters the installed devices of the organisation to include
        only those whose primary keys match the app installations provided and
        support OPSWAT patching. It then returns a flat list of the IDs of these
        app installations.

        If multiple app installs are provided, it silently skips those that don't
        support OPSWAT patching. If only one app install is provided and it doesn't
        support OPSWAT patching, it raises a ValidationError.

        Returns:
            list: A flat list of IDs for the filtered app installations that support OPSWAT patching.

        Raises:
            ValidationError: If only one app install is provided and it doesn't support OPSWAT patching.
        """
        app_installs = self.organisation.installed_devices.filter(pk__in=self.data["app_installs"])

        # Filter app installs that support OPSWAT patching
        supported_app_installs = []
        unsupported_app_installs = []

        for app_install in app_installs:
            if app_install.supports_opswat_patch():
                supported_app_installs.append(app_install.id)
            else:
                unsupported_app_installs.append(app_install.device_id)

        # If there's only one app install and it doesn't support OPSWAT patching, raise an error
        if len(app_installs) == 1 and len(unsupported_app_installs) == 1:
            raise ValidationError(
                f"This device does not support OPSWAT patching (requires CAP version 5.5.0 or higher): "
                f"{unsupported_app_installs[0]}"
            )
        return supported_app_installs


    def clean(self) -> dict:
        """
        Validates and cleans a list of package IDs.

        This method processes a list of package IDs, ensuring that each package exists
        and that no patch has already been scheduled for it. It collects valid packages
        and raises a ValidationError if any issues are encountered with the provided
        package IDs.

        Raises:
            ValidationError: If one or more of the provided package IDs are invalid or
            have issues, an exception is raised containing the list of errors.

        Returns:
            dict: The cleaned data containing the validated package IDs and app
            installation IDs.
        """
        cleaned_data = super().clean()

        if "app_installs" not in cleaned_data or "packages" not in cleaned_data:
            return cleaned_data

        packages = cleaned_data["packages"]
        errors = []
        model = self._model_map[self.package_level]

        for package_id in packages:
            try:
                package = model.objects.get(id=package_id)
            except model.DoesNotExist:
                errors.append("The requested software package could not be found in the system."
                              " Please verify your selection and try again.")
                continue

            if self._package_has_multiple_ids(package):
                package.source_id = self._clean_package_source_id(package)
                if not package.source_id:
                    # if cleaned source_id does not contain any valid IDs
                    errors.append(f'Package "{package.product}" temporarily does not support patching.')
                    continue

            package = enrich_installed_software_with_installers([package])[0]

            # Check if package is already scheduled
            if self.package_level == "organisation":
                # For organization level, we blindly accept any submit
                # and only scheduled the installs for those that are not already scheduled
                # No error is ever thrown from this form that would say it's already scheduled.
                pass
            else:
                # For app_install level, check the aggregate installer
                if package.app_install_product_installer and package.app_install_product_installer.get('is_scheduled', False):
                    errors.append("This software update is already in the installation queue. "
                                  "No need to schedule it again.")
                    continue

            self.validated_packages.append(package)

        if errors:
            raise ValidationError(errors)

        return cleaned_data

    def _package_has_multiple_ids(self, package):
        """
        Checks if the package has multiple IDs.

        This method determines if the package has multiple IDs by checking
        if the source_id contains a comma, indicating multiple product versions.
        """
        return "," in package.source_id

    def _clean_package_source_id(self, package) -> str:
        """
        Cleans the `source_id` field of a package by removing IDs associated with
        custom products or vendors.

        The `source_id` is expected to be a comma-separated string of ProductVersion IDs or a single ID.
        This method:
        - Parses and strips individual IDs.
        - Filters out ProductVersions whose related product or vendor has an `opswat_id` starting with CUSTOM_ID_PREFIX.
        - Returns a cleaned, comma-separated string of valid ProductVersion IDs.
        """
        raw_ids = [id_.strip() for id_ in package.source_id.split(",") if id_.strip()]
        if not raw_ids:
            return package.source_id

        valid_ids = (
            ProductVersion.objects
            .filter(id__in=raw_ids)
            .exclude(
                Q(product__opswat_id__startswith=CUSTOM_ID_PREFIX) |
                Q(product__vendor__opswat_id__startswith=CUSTOM_ID_PREFIX)
            )
            .values_list("id", flat=True)
        )

        return ",".join(str(id_) for id_ in sorted(valid_ids))


    def schedule_patch_installation(self):
        """
        Schedules patch installations for each (app_install, validated_package) pair.
        If a schedule already exists, update its status.
        """
        to_update = []
        new_lookup = {}
        patch_attempts = {}
        patch_attempts_to_create = []
        event_logs_to_create = []

        logger.debug("Validated packages: %s", self.validated_packages)
        for package in self.validated_packages:
            if not hasattr(package, 'app_install_product_installer'):
                logger.debug(f"Package {package} does not have an app_install_product_installer. Skipping.")
                continue

            for app_install_id in self.cleaned_data["app_installs"]:
                # Check if we have per-app_install installer info (for organization level)
                installer_id = None
                if (hasattr(package, 'app_install_product_installers_by_app_install_dynamic') and
                    app_install_id in package.app_install_product_installers_by_app_install_dynamic):
                    # Use the specific installer for this app_install
                    installer_info = package.app_install_product_installers_by_app_install_dynamic[app_install_id]
                    installer_id = installer_info['app_install_product_installer_id']
                else:
                    # Fall back to the aggregate installer
                    installer_id = package.app_install_product_installer['app_install_product_installer_id']

                # Skip if no installer found for this app_install
                if not installer_id:
                    logger.debug(f"Patch form: No installer found for app_install {app_install_id} and package {package}")
                    continue

                try:
                    installed_product_version = InstalledProductVersion.objects.get(
                        product_version__id__in=package.get_source_id_list(),
                        installed_product__app_install__id=app_install_id
                    )
                    package.signature = installed_product_version.signature
                except InstalledProductVersion.DoesNotExist:
                    logger.debug(f"Patch form: No installed product version found for app_install {app_install_id} and package {package}")
                    continue

                key = (installer_id, app_install_id)
                installer = OpswatScheduledProductInstaller(
                    opswat_product_patch_installer_id=installer_id,
                    signature=package.signature,
                    app_install_id=app_install_id,
                    creator=self.user,
                    status=PENDING,
                )
                new_lookup[key] = installer
                patch_attempt = OpswatPatchAttempt(
                    status=PENDING,
                    initiated_by=self.user,
                    from_version=installed_product_version.product_version.raw_version,
                    to_version=package.app_install_product_installer["installer_version"]
                )
                patch_attempts[
                    f"{installer_id}:{app_install_id}"
                ] = patch_attempt

        existing = OpswatScheduledProductInstaller.objects.filter(
            opswat_product_patch_installer_id__in=[k[0] for k in new_lookup],
            app_install_id__in=[k[1] for k in new_lookup]
        )
        logger.debug(f"Patch form: Found {existing.count()} existing installers")

        for existing_installer in existing:
            key = (existing_installer.opswat_product_patch_installer_id, existing_installer.app_install_id)
            existing_installer.status = PENDING
            to_update.append(existing_installer)

            event_logs_to_create.append(
                OpswatPatchEventLog(
                    opswat_scheduled_product_installer=existing_installer,
                    status=PENDING,
                )
            )
            new_lookup.pop(key, None)

        to_create = list(new_lookup.values())
        logger.debug(f"Patch form: Creating {len(to_create)} new installers and updating {len(to_update)} existing ones")

        with transaction.atomic():
            patch_job = OpswatPatchJob.objects.create(
                organisation_id=self.organisation.id,
                initiated_by=self.user
            )
            if to_create:
                created_installers = OpswatScheduledProductInstaller.objects.bulk_create(to_create)

                for installer in created_installers:
                    patch_attempt = patch_attempts[f"{installer.opswat_product_patch_installer.id}:{installer.app_install_id}"]
                    patch_attempt.patch_job = patch_job
                    patch_attempt.scheduled_product_installer = installer
                    patch_attempts_to_create.append(
                        patch_attempt
                    )
            if to_update:
                OpswatScheduledProductInstaller.objects.bulk_update(to_update, ["status"])

                for installer in to_update:
                    patch_attempt = patch_attempts[f"{installer.opswat_product_patch_installer.id}:{installer.app_install_id}"]
                    patch_attempt.patch_job = patch_job
                    patch_attempt.scheduled_product_installer = installer
                    patch_attempts_to_create.append(
                        patch_attempt
                    )

            if patch_attempts_to_create:
                created_attempts = OpswatPatchAttempt.objects.bulk_create(patch_attempts_to_create)

                for attempt in created_attempts:
                    event_logs_to_create.append(
                        OpswatPatchEventLog(
                            opswat_scheduled_product_installer=attempt.scheduled_product_installer,
                            patch_attempt=attempt,
                            status=PENDING,
                        )
                    )

            if event_logs_to_create:
                OpswatPatchEventLog.objects.bulk_create(event_logs_to_create)

        # Publish WebSocket events for each affected app_install after atomic save
        affected_app_install_ids = set()

        # Collect app_install_ids from newly created installers
        if to_create:
            for installer in created_installers:
                affected_app_install_ids.add(installer.app_install_id)

        # Collect app_install_ids from updated installers
        for installer in to_update:
            affected_app_install_ids.add(installer.app_install_id)

        # Get all affected app_installs in a single query
        if affected_app_install_ids:
            from appusers.models import AppInstall
            app_installs = AppInstall.objects.filter(id__in=affected_app_install_ids)

            # Publish event for each affected app_install
            for app_install in app_installs:
                publish_patch_installer_event(app_install)
