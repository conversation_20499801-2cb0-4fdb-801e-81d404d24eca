
from django.contrib import admin
from admin_auto_filters.filters import AutocompleteFilterFactory
from .models.installed_software_organisation_individual import InstalledSoftwareOrganisationIndividual


@admin.register(InstalledSoftwareOrganisationIndividual)
class InstalledSoftwareOrganisationIndividualAdmin(admin.ModelAdmin):
    list_display = ['id', 'organisation', 'product', 'version', 'source', 'is_vulnerable', "device_count", 'cve_count']
    list_filter = [
        AutocompleteFilterFactory('Organisation', 'organisation'),
        AutocompleteFilterFactory('Partner', 'organisation__partner'),
        'source',
        'is_vulnerable',
        'created',
        'modified'
    ]
    search_fields = ['product', 'vendor', 'version', 'organisation__name']
    readonly_fields = ['created', 'modified']
    raw_id_fields = ['organisation']

    def device_count(self, obj):
        """
        Returns the count of devices associated with this installed software.
        """
        return obj.device_count
    device_count.short_description = "Device Count"
