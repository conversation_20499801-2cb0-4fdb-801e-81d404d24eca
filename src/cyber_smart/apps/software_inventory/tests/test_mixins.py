
from django.db.models.query import QuerySet
from django.test import RequestFactory, TestCase
from django.views.generic import View

from software_inventory.filters.view_mixins import FilterViewMixin


class DummyQuerySet(QuerySet):
    def __init__(self):
        self.filtered_calls = []

    def filter(self, **kwargs):
        self.filtered_calls.append(kwargs)
        return self


class FakeView(FilterViewMixin, View):
    FILTER_REGISTRY = {}

    def __init__(self, request):
        self.request = request

    def enrich_queryset(self, queryset):
        return queryset


class FilterViewMixinTests(TestCase):
    def setUp(self):
        self.factory = RequestFactory()

        def make_filter_cls(name, is_db_filter=True):
            class DummyFilter:
                is_database_filter = is_db_filter

                @staticmethod
                def apply(queryset, values):
                    return queryset.filter(dummy__in=values)

                key = name
            return DummyFilter
        self.dummy_filter_cls = make_filter_cls

    def test_parsed_filters(self):
        request = self.factory.get("/?filter_dummy=value&filter_another=value2")
        view = FakeView(request)
        view.FILTER_REGISTRY = {"filter_dummy": self.dummy_filter_cls("filter_dummy")}

        self.assertEqual(view.parsed_filters, {"filter_dummy": ["value"]})

    def test_has_post_retrieval_filters_applied(self):
        request = self.factory.get("/?filter_dummy=value&filter_another=value2")
        view = FakeView(request)
        view.FILTER_REGISTRY = {"filter_dummy": self.dummy_filter_cls("filter_dummy", is_db_filter=False)}

        self.assertTrue(view.has_post_retrieval_filters_applied)

        view.FILTER_REGISTRY = {"filter_dummy": self.dummy_filter_cls("filter_dummy", is_db_filter=True)}
        self.assertFalse(view.has_post_retrieval_filters_applied)

    def test_filter_queryset_with_db_and_non_db_filters(self):
        request = self.factory.get("/?filter_db=1&filter_non_db=2&filter_another=3")
        view = FakeView(request)
        view.FILTER_REGISTRY = {
            "filter_db": self.dummy_filter_cls("filter_db", is_db_filter=True),
            "filter_non_db": self.dummy_filter_cls("filter_non_db", is_db_filter=False),
        }

        self.assertEqual(view.parsed_filters, {"filter_db": ["1"], "filter_non_db": ["2"]})

        queryset = DummyQuerySet()
        result = view.filter_queryset(queryset)

        # Should have filters applied
        self.assertTrue(result.filtered_calls)

    def test_enrich_queryset_default(self):
        request = self.factory.get("/")
        view = FakeView(request)
        queryset = DummyQuerySet()

        result = view.enrich_queryset(queryset)
        self.assertEqual(result, queryset)
