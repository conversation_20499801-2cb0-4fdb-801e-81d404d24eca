from django.contrib.auth.models import User
from django.test import TestCase
from django.urls import reverse

from appusers.models.factories import (
    AppInstallFactory,
    AppInstallOSUserFactory,
    AppUserFactory,
)
from appusers.tests.helpers import (
    SoftwarePackageHelperTestMixin,
    CHROME,
    FIREFOX,
    VSCODE,
    ACTIVE_PROTECT,
    EDGE,
    SAFARI,
    PYCHARM,
    ZOOM,
    SLACK,
    SPOTIFY,
    VLC,
    STEAM,
    GOOGLE,
    MOZILLA,
    MICROSOFT,
    APPLE,
    VALVE,
    VERSION_2_0_0
)
from opswat.models import Product, InstalledProductVersion
from opswat_patch.factories import (
    OpswatProductPatchInstallerFactory,
    OpswatScheduledProductInstallerFactory, OpswatPatchAttemptFactory, OpswatPatchJobFactory,
)
from opswat.factories import ProductSignatureFactory
from organisations.factories import OrganisationAdminFactory, OrganisationFactory

# Test data constants
TEST_USERNAME = "admin"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "password"
TEST_ORG_NAME = "Org 1"
ROWS_PER_PAGE = 100

class SoftwarePackagesViewFiltersTestCase(SoftwarePackageHelperTestMixin, TestCase):
    """
    Test case for software packages / patch history filters.
    """
    @classmethod
    def setUpTestData(cls):
        cls.org = OrganisationFactory(name=TEST_ORG_NAME)
        cls.app_user = AppUserFactory(organisation=cls.org)
        cls.app_install = AppInstallFactory(app_user=cls.app_user)
        AppInstallOSUserFactory(app_install=cls.app_install)

        # Create the test user and admin once
        cls.user = User.objects.create_superuser(TEST_USERNAME, TEST_EMAIL, TEST_PASSWORD)
        OrganisationAdminFactory(organisation=cls.org, user=cls.user)

        # Create test packages once during test data setup
        # This avoids recreating them for each test method
        SoftwarePackageHelperTestMixin()._create_test_packages(cls.app_install)

    def setUp(self):
        # Just log in, packages are already created
        self.client.login(username=TEST_USERNAME, password=TEST_PASSWORD)

    def _get_filtered_response(self, filter_name, filter_value, view_name):
        return self.client.get(
            reverse(view_name, args=[self.org.secure_id]),
            {filter_name: filter_value, "rows_per_page": ROWS_PER_PAGE}
        )

    def _assert_filter_results(self, filter_name, test_cases, view_name="organisations:installed-software"):
        for filter_value, expected_found, expected_not_found in test_cases:
            with self.subTest(filter_value=filter_value):
                if isinstance(filter_value, str):
                    filter_value = [filter_value] if filter_name == "filter_vendor" else filter_value

                response = self._get_filtered_response(filter_name, filter_value, view_name)
                self.assertEqual(response.status_code, 200)

                if filter_name != "filter_vendor":
                    table_content = response.content.decode('utf-8').split('<table')[1].split('</table>')[0]
                    for name in expected_found:
                        self.assertIn(name, table_content)
                    for name in expected_not_found:
                        self.assertNotIn(name, table_content)
                else:
                    for name in expected_found:
                        self.assertContains(response, name)
                    for name in expected_not_found:
                        self.assertNotContains(response, name)

    def test_filter_by_vendor(self):
        test_cases = [
            (MICROSOFT, [EDGE, PYCHARM, VSCODE], [CHROME, FIREFOX]),
            (GOOGLE, [CHROME], [FIREFOX, EDGE]),
            (MOZILLA, [FIREFOX], [CHROME, EDGE]),
            (APPLE, [SAFARI], [CHROME, FIREFOX]),
            (VALVE, [STEAM], [CHROME, FIREFOX]),
            ([MICROSOFT, GOOGLE], [EDGE, PYCHARM, VSCODE, CHROME], [FIREFOX, SAFARI])
        ]
        self._assert_filter_results("filter_vendor", test_cases)

    def test_filter_by_severity(self):
        test_cases = [
            ("critical", [ACTIVE_PROTECT, EDGE], [SAFARI, CHROME, FIREFOX, PYCHARM, ZOOM, SLACK, SPOTIFY, VLC, STEAM, VSCODE]),
            ("high", [SAFARI, CHROME], [ACTIVE_PROTECT, EDGE, FIREFOX, PYCHARM, ZOOM, SLACK, SPOTIFY, VLC, STEAM, VSCODE]),
            ("medium", [FIREFOX, PYCHARM], [ACTIVE_PROTECT, EDGE, SAFARI, CHROME, ZOOM, SLACK, SPOTIFY, VLC, STEAM, VSCODE]),
            ("low", [ZOOM, SLACK, SPOTIFY, VLC], [ACTIVE_PROTECT, EDGE, SAFARI, CHROME, FIREFOX, PYCHARM, STEAM, VSCODE]),
            ("info", [STEAM], [ACTIVE_PROTECT, EDGE, SAFARI, CHROME, FIREFOX, PYCHARM, ZOOM, SLACK, SPOTIFY, VSCODE, VLC]),
            ("unknown", [VSCODE], [ACTIVE_PROTECT, EDGE, SAFARI, CHROME, FIREFOX, PYCHARM, ZOOM, SLACK, SPOTIFY, VLC, STEAM]),
            (["info", "unknown"], [STEAM, VSCODE], [ACTIVE_PROTECT, EDGE, SAFARI, CHROME, FIREFOX, PYCHARM, ZOOM, SLACK, SPOTIFY, VLC]),
            (
                ["critical", "high", "medium", "low", "info", "unknown"],
                [ACTIVE_PROTECT, EDGE, SAFARI, CHROME, FIREFOX, PYCHARM, ZOOM, SLACK, SPOTIFY, VLC, STEAM, VSCODE],
                []
            ),
            (["critical", "info", "medium"], [ACTIVE_PROTECT, EDGE, STEAM, FIREFOX, PYCHARM], [SAFARI, CHROME, ZOOM, SLACK, SPOTIFY, VLC, VSCODE]),
        ]
        self._assert_filter_results("filter_severity", test_cases)

    def test_filter_by_patchable(self):
        # Get the PyCharm product and create a signature for it
        pycharm_product = Product.objects.get(name=PYCHARM)
        # Create patch installer with the signature
        patch_installer = OpswatProductPatchInstallerFactory(
            product=pycharm_product,
            patch_installer__latest_version=VERSION_2_0_0,
        )
        signature = ProductSignatureFactory(product=pycharm_product)
        patch_installer.signatures.add(signature)
        # Find the installed product version for PyCharm and add the signature
        installed_product_versions = InstalledProductVersion.objects.filter(
            product_version__product=pycharm_product
        )
        for ipv in installed_product_versions:
            ipv.signature = signature
            ipv.save()


        test_cases = [
            ("patch available", [PYCHARM], [SAFARI, CHROME, FIREFOX, ZOOM, SLACK, SPOTIFY, VLC, STEAM, VSCODE]),
            ("no known patch", [SAFARI, CHROME, FIREFOX, ZOOM, SLACK, SPOTIFY, VLC, STEAM, VSCODE, ACTIVE_PROTECT], [PYCHARM])
        ]
        self._assert_filter_results("filter_patchable", test_cases)

    def test_patch_history_filter_by_status(self):
        self._create_patch_installers()

        test_cases = [
            ("pending", [PYCHARM], [SAFARI, CHROME, FIREFOX, ZOOM, SLACK, SPOTIFY, VLC, STEAM]),
            ("scheduled", [SAFARI], [PYCHARM, CHROME, FIREFOX, ZOOM, SLACK, SPOTIFY, VLC, STEAM]),
            ("in_progress", [CHROME], [PYCHARM, SAFARI, FIREFOX, ZOOM, SLACK, SPOTIFY, VLC, STEAM]),
            ("installed", [FIREFOX], [PYCHARM, SAFARI, CHROME, ZOOM, SLACK, SPOTIFY, VLC, STEAM]),
            ("waiting_for_restart", [ZOOM], [PYCHARM, SAFARI, CHROME, FIREFOX, SLACK, SPOTIFY, VLC, STEAM]),
            ("complete", [SLACK], [PYCHARM, SAFARI, CHROME, FIREFOX, ZOOM, SPOTIFY, VLC, STEAM]),
            ("timed_out", [SPOTIFY], [PYCHARM, SAFARI, CHROME, FIREFOX, ZOOM, SLACK, VLC, STEAM]),
            ("rolled_back", [VLC], [PYCHARM, SAFARI, CHROME, FIREFOX, ZOOM, SLACK, SPOTIFY]),
            ("error", [STEAM], [PYCHARM, SAFARI, CHROME, FIREFOX, ZOOM, SLACK])
        ]

        self._assert_filter_results("filter_status", test_cases, view_name="organisations:patch-history")

    def _create_patch_installers(self) -> None:
        status_mapping = {
            PYCHARM: "pending",
            SAFARI: "scheduled",
            CHROME: "in_progress",
            FIREFOX: "installed",
            ZOOM: "waiting_for_restart",
            SLACK: "complete",
            SPOTIFY: "timed_out",
            VLC: "rolled_back",
            STEAM: "error"
        }

        for product_name, status in status_mapping.items():
            scheduled_product_installer = OpswatScheduledProductInstallerFactory(
                app_install=self.app_install,
                opswat_product_patch_installer__product__name=product_name,
                status=status
            )
            patch_job = OpswatPatchJobFactory(organisation=self.org)
            OpswatPatchAttemptFactory(
                patch_job=patch_job,
                scheduled_product_installer=scheduled_product_installer,
                status=status
            )
