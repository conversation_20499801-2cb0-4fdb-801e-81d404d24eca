from django.test import TestCase

from accounts.factories import UserFactory
from appusers.models import InstalledSoftwareAppInstallIndividual
from appusers.models.factories import AppInstallFactory
from appusers.tests.helpers import SoftwarePackageHelperTestMixin
from opswat_patch.models import Opswat<PERSON>atchJob, IN_PROGRESS, OpswatPatchAttempt, PENDING, COMPLETE, ERROR
from opswat_patch.models.constants import STATUS_AGGREGATION_GROUPS
from organisations.factories import OrganisationFactory
from software_inventory.forms import SchedulePatchInstallationForm
from software_inventory.models import InstalledSoftwareOrganisationIndividual


class PatchJobAndPatchAttemptTestCase(TestCase, SoftwarePackageHelperTestMixin):
    """
    A test case for verifying the accurate behavior of patch jobs and patch attempts.

    This test case is intended for validating the integration and functional behavior
    of application patch jobs and patch attempts. It ensures that the patch job and
    patch attempt structures are correctly created, scheduled, updated, and verified
    based on app installs and their corresponding software packages.
    """
    def setUp(self):
        self.user = UserFactory()
        self.organisation = OrganisationFactory()

        self.app_install_1 = AppInstallFactory(app_user__organisation=self.organisation)
        self.app_install_2 = AppInstallFactory(app_user__organisation=self.organisation)

        self.app_install_1_packages = self._add_packages_to_install(
            self.app_install_1,
            {
                "chatgpt": ("ChatGPT", "OpenAI", "1.0.0"),
                "evernote": ("Evernote", "Evernote Corporation", "10.0.0"),
                "slack": ("Slack", "Slack Technologies", "4.0.0"),
            },
        )
        self.app_install_2_packages = self._add_packages_to_install(
            self.app_install_2,
            {
                "pycharm": ("PyCharm", "JetBrains", "3.4.1"),
                "evernote": ("Evernote", "Evernote Corporation", "10.0.0"),
                "slack": ("Slack", "Slack Technologies", "4.0.0"),
            },
        )
        self._refresh_summaries()

    def _add_packages_to_install(self, app_install, package_data):
        """
        Adds software packages to the installation process and creates a dictionary with package details.

        This method processes given package data, extracts information for each package, and adds it
        to the installation process using the `_add_software_package_from_opswat_scanner` method. It
        also creates and returns a dictionary mapping keys to package details.
        """
        package_dict = {}
        for key, (product, vendor, version) in package_data.items():
            self._add_software_package_from_opswat_scanner(
                app_install,
                product=product,
                vendor=vendor,
                version=version,
                cves=[],
                patch_available=True,
            )
            package_dict[key] = {
                "product": product,
                "vendor": vendor,
                "version": version,
            }
        return package_dict

    def _create_schedule_patch_installation_form(self, level, app_installs, package_ids):
        return SchedulePatchInstallationForm(
            organisation=self.organisation,
            package_level=level,
            user=self.user,
            data={
                "app_installs": [ai.pk for ai in app_installs],
                "packages": package_ids,
            },
        )

    def _assert_patch_job_created(self):
        """
        Checks the creation of a single OpswatPatchJob instance in the database.

        This method ensures that exactly one instance of OpswatPatchJob is created
        and verifies that it belongs to the correct organisation with a status
        set to "IN_PROGRESS".

        Raises:
            AssertionError: If the conditions for the OpswatPatchJob creation
            or status are not met.
        """
        self.assertEqual(OpswatPatchJob.objects.count(), 1)
        self.assertTrue(
            OpswatPatchJob.objects.filter(
                organisation=self.organisation,
                status=IN_PROGRESS,
            ).exists()
        )

    def _assert_patch_attempts(self, count, app_installs, products):
        """
        Asserts the number of patch attempts for specified app installs and products. This
        method verifies that the number of OpswatPatchAttempt objects matching the given filters
        aligns with the expected count.

        Attributes:
            count (int): The expected number of patch attempts to match.
            app_installs (list): List of app installation instances to match against.
            products (list): List of product names to filter patch attempt objects.

        Args:
            count: The expected number of patch attempts.
            app_installs: A list containing app install objects which are used to filter
                the patch attempts.
            products: A list of strings representing product names used to filter the
                patch attempt objects.
        """
        attempts = OpswatPatchAttempt.objects.filter(
            scheduled_product_installer__app_install__in=app_installs,
            scheduled_product_installer__opswat_product_patch_installer__product__name__in=products,
            status=PENDING,
        )
        self.assertEqual(attempts.count(), count)

    def test_one_app_install_one_package(self):
        """
        Validates the installation of a single application and schedules patch installation.
        This method ensures that the necessary packages for the application are properly
        retrieved, validates the generated form, and schedules the patch installation. It
        also verifies that the patch job creation and the attempts align with expectations.
        """
        product_name = self.app_install_1_packages["chatgpt"]["product"]
        package_ids = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_1,
            product=product_name,
        ).values_list("pk", flat=True)

        form = self._create_schedule_patch_installation_form("app_install", [self.app_install_1], list(package_ids))

        self.assertTrue(form.is_valid(), form.errors)
        form.schedule_patch_installation()

        self._assert_patch_job_created()
        self._assert_patch_attempts(1, [self.app_install_1], [product_name])

    def test_one_app_install_multiple_packages(self):
        """
        Tests the ability to install multiple packages for a single application instance.

        This method verifies that the form is correctly initialized with the given app
        install and package IDs. It ensures the form successfully validates and schedules
        the patch installation. The test also confirms that the patch job is created
        and the correct patch attempts are made for the provided app install and products.
        """
        products = [
            self.app_install_1_packages["chatgpt"]["product"],
            self.app_install_1_packages["evernote"]["product"],
        ]
        package_ids = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_1,
            product__in=products,
        ).values_list("pk", flat=True)

        form = self._create_schedule_patch_installation_form("app_install", [self.app_install_1], list(package_ids))

        self.assertTrue(form.is_valid(), form.errors)
        form.schedule_patch_installation()

        self._assert_patch_job_created()
        self._assert_patch_attempts(2, [self.app_install_1], products)

    def test_multiple_app_installs_one_package(self):
        """
        Tests the process of handling multiple application installations associated with the same
        package within an organisation. This test ensures the form validation is correct, the patch
        installation schedule is created, and patch attempts are correctly processed.
        """
        product = self.app_install_1_packages["evernote"]["product"]
        package_ids = InstalledSoftwareOrganisationIndividual.objects.filter(
            product=product,
        ).values_list("pk", flat=True)

        form = self._create_schedule_patch_installation_form(
            "organisation", [self.app_install_1, self.app_install_2], list(package_ids)
        )

        self.assertTrue(form.is_valid(), form.errors)
        form.schedule_patch_installation()

        self._assert_patch_job_created()
        self._assert_patch_attempts(2, [self.app_install_1, self.app_install_2], [product])

    def test_multiple_app_installs_multiple_packages(self):
        """
        Tests scheduling patch installation for multiple app installs with multiple packages.

        This test verifies the correct behavior of the schedule_patch_installation method when applied
        to multiple app installations, ensuring that patch jobs and attempts are created for the given
        app installations and associated package products.
        """
        products = [
            self.app_install_1_packages["evernote"]["product"],
            self.app_install_2_packages["slack"]["product"],
        ]
        package_ids = InstalledSoftwareOrganisationIndividual.objects.filter(
            product__in=products,
        ).values_list("pk", flat=True)

        form = self._create_schedule_patch_installation_form("organisation", [self.app_install_1, self.app_install_2], list(package_ids))

        self.assertTrue(form.is_valid(), form.errors)
        form.schedule_patch_installation()

        self._assert_patch_job_created()
        self._assert_patch_attempts(4, [self.app_install_1, self.app_install_2], products)

    def test_job_status_updates_on_attempt_status_change(self):
        """
        Tests the update of job status based on changes in the statuses of associated attempts
        for a patch installation operation. The function ensures that the job's state transitions
        align with the expected behavior defined by the status aggregation rules and sequence of
        attempt statuses during patch installation tasks.
        """
        products = [
            self.app_install_1_packages["chatgpt"]["product"],
            self.app_install_1_packages["evernote"]["product"],
        ]
        package_ids = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_1,
            product__in=products,
        ).values_list("pk", flat=True)
        form = self._create_schedule_patch_installation_form("app_install", [self.app_install_1], list(package_ids))
        self.assertTrue(form.is_valid(), form.errors)
        form.schedule_patch_installation()
        job = OpswatPatchJob.objects.first()
        attempts = list(OpswatPatchAttempt.objects.filter(patch_job=job).order_by("id"))

        self.assertEqual(job.status, IN_PROGRESS)

        attempts[0].status = IN_PROGRESS
        attempts[0].save()
        job.refresh_from_db()
        self.assertEqual(job.status, IN_PROGRESS)

        for attempt in attempts:
            attempt.status = COMPLETE
            attempt.save()
        job.refresh_from_db()
        self.assertEqual(job.status, COMPLETE)

        attempts[0].status = ERROR
        attempts[0].save()
        job.refresh_from_db()
        self.assertEqual(job.status, ERROR)

        attempts[0].status = IN_PROGRESS
        attempts[0].save()
        job.refresh_from_db()
        self.assertEqual(job.status, IN_PROGRESS)

        # test all statuses against expected job status
        status_to_expected_job_status = {}
        for group_status, statuses in STATUS_AGGREGATION_GROUPS.items():
            for status in statuses:
                status_to_expected_job_status[status] = group_status

        for status, expected_job_status in status_to_expected_job_status.items():
            for attempt in attempts:
                attempt.status = status
                attempt.save()
            job.refresh_from_db()
            self.assertEqual(
                job.status, expected_job_status,
                f"Job status should be {expected_job_status} when all attempts are {status}"
            )
