from django.test import TestCase

from appusers.tests.helpers import SoftwarePackageHelperTestMixin
from common.base_tests import BaseTestCase
from opswat.factories import (
    ProductVendorFactory, ProductFactory, ProductVersionFactory,
    ProductSignatureFactory
)
from opswat_patch.factories import (
    OpswatProductPatchInstallerFactory, OpswatScheduledProductInstallerFactory,
    OpswatPatchInstallerFactory
)
from vulnerabilities.utils import OPSWAT_SOURCE
from appusers.models.factories import AppVersionFactory
from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from organisations.views import PackageDetailsView
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from software_inventory.views.packages_list import PackageBaseView


class PackageDetailViewTest(BaseTestCase, TestCase, SoftwarePackageHelperTestMixin):
    """
    Test PackageBaseView functionality, particularly the is_scheduled status
    for devices with pending patches.
    """

    def setUp(self):
        self.creating_user()
        self.core_setting_up()
        self.creating_app_user()

        # Create OPSWAT product structure
        self.vendor = ProductVendorFactory(
            name="Microsoft",
            opswat_id="microsoft"
        )
        self.product = ProductFactory(
            name="Microsoft Edge",
            vendor=self.vendor,
            opswat_id="edge"
        )
        self.product_version = ProductVersionFactory(
            product=self.product,
            raw_version="100.0.0",
            major=100,
            minor=0,
            patch=0
        )

        # Create product signature
        self.signature = ProductSignatureFactory(
            product=self.product,
            name="Microsoft Edge Signature"
        )

        # Add the installed software using the helper
        self._add_software_package_from_opswat_scanner(
            self.app_install,
            product="Microsoft Edge",
            vendor="Microsoft",
            version="100.0.0",
            cves=[]
        )

        # Create patch installer
        self.patch_installer = OpswatPatchInstallerFactory(
            latest_version="101.0.0"
        )

        # Create product patch installer association
        self.product_patch_installer = OpswatProductPatchInstallerFactory(
            product=self.product,
            patch_installer=self.patch_installer,
            title="Microsoft Edge Update"
        )

        # Add signature to the product patch installer
        self.product_patch_installer.signatures.add(self.signature)

        # Create a scheduled (pending) patch for this app install
        self.scheduled_patch = OpswatScheduledProductInstallerFactory(
            opswat_product_patch_installer=self.product_patch_installer,
            app_install=self.app_install,
            signature=self.signature,
            status='pending'
        )

        self._refresh_summaries()


    def test_package_get_devices_logic(self):
        """
        Test the _get_devices logic from PackageBaseView directly.
        """
        # Get the installed software
        installed_software = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install,
            source=OPSWAT_SOURCE
        ).first()

        # Create a minimal view instance to test the method
        view = PackageBaseView()
        view.organisation = self.organisation
        view.kwargs = {'package_id': installed_software.id}

        # Call the _get_devices method directly with the package
        devices = view._get_devices(installed_software)

        # There should be one device
        self.assertEqual(len(devices), 1, "Should have one device")

        device = devices[0]
        self.assertEqual(device['id'], self.app_install.id)

        # Check is_scheduled status is correct.
        self.assertTrue(
            device['is_scheduled'],
            "Device should show is_scheduled=True for pending patch"
        )
        self.assertFalse(
            device['supports_opswat_patch'],
            "Device should show supports_opswat_patch=False for out of date version"
        )

    def test_context_data(self):
        """
        Test the context data returned by the PackageDetailsView.
        """
        test_cases = [
            {
                'description': 'Test context data with a device pre 5.5.0',
                'install_version': AppVersionFactory(major=5, minor=4, patch=0),
                'any_device_without_patching_capabilities': True
            },
            {
                'description': 'Test context data with a device post 5.5.0',
                'install_version': AppVersionFactory(major=5, minor=5, patch=0),
                'any_device_without_patching_capabilities': False
            }
        ]

        for case in test_cases:
            with self.subTest(case=case['description']):
                self.app_install.version = case['install_version']
                self.app_install.app_version = case['install_version'].get_version_string()
                self.app_install.save(update_fields=['version'])
                # Get the installed software
                installed_software = InstalledSoftwareOrganisationIndividual.objects.filter(
                    organisation=self.organisation,
                    source=OPSWAT_SOURCE
                ).first()


                view = PackageDetailsView()
                view.organisation = self.organisation
                view.kwargs = {'package_id': installed_software.id}

                context = view.get_context_data()

                self.assertIn('organisation', context)
                self.assertIn('view_level', context)
                self.assertIn('devices', context)
                self.assertIn('any_device_without_patching_capabilities', context)

                self.assertEqual(context['organisation'], self.organisation)
                self.assertEqual(context['view_level'], 'organisation')
                self.assertEqual(len(context['devices']), 1)
                self.assertEqual(case['any_device_without_patching_capabilities'], context['any_device_without_patching_capabilities'])
