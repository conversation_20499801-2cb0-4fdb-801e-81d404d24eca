from django.test import TestCase
from django.contrib.auth.models import User
from django.urls import reverse

from appusers.models.factories import AppInstallFactory, AppUserFactory
from appusers.tests.helpers import SoftwarePackageHelperTestMixin
from organisations.factories import OrganisationFactory, OrganisationAdminFactory
from opswat_patch.factories import OpswatScheduledProductInstallerFactory, OpswatPatchAttemptFactory, \
    OpswatProductPatchInstallerFactory, OpswatPatchJobFactory
from vulnerabilities.utils import OPSWAT_SOURCE


class SoftwareInventoryReportTestCase(TestCase, SoftwarePackageHelperTestMixin):
    """
    Test software inventory and patch history report CSVs at organisation and app install level.
    """
    def setUp(self):
        self.test_username = "admin"
        self.test_email = "<EMAIL>"
        self.test_password = "password"

        self.organisation = OrganisationFactory()
        self.app_user = AppUserFactory(organisation=self.organisation)
        self.app_install = AppInstallFactory(app_user=self.app_user)
        self.packages = [
            {
                "product": "Active Protect", "vendor": "CyberSmart", "version": "5.5.0", "installer_version": "5.6.0",
                "source": OPSWAT_SOURCE, "cves": []
            },
            {
                "product": "Edge Browser", "vendor": "Microsoft", "version": "1.2.3", "installer_version": "1.2.4",
                "source": OPSWAT_SOURCE, "cves": []
            }
        ]
        self._create_packages_with_cves(self.app_install, self.packages)

        for package in self.packages:
            patch_installer = OpswatProductPatchInstallerFactory(
                patch_installer__latest_version=package["installer_version"],
                product__name=package["product"],
                product__vendor__name=package["vendor"],
                patch_installer__vulnerabilities=package["cves"],
            )
            scheduled_installer = OpswatScheduledProductInstallerFactory(
                app_install=self.app_install,
                opswat_product_patch_installer=patch_installer
            )
            patch_job = OpswatPatchJobFactory(
                organisation=self.organisation
            )
            OpswatPatchAttemptFactory(
                patch_job=patch_job,
                scheduled_product_installer=scheduled_installer,
                from_version=package["version"],
                to_version=package["installer_version"]
            )

        self._refresh_summaries()
        self._setup_auth()

    def _setup_auth(self):
        self.user = User.objects.create_superuser(self.test_username, self.test_email, self.test_password)
        OrganisationAdminFactory(organisation=self.organisation, user=self.user)
        self.client.login(username=self.test_username, password=self.test_password)

    def _assert_csv_response(self, response):
        self.assertEqual(response.status_code, 200)
        self.assertIn("text/csv", response["Content-Type"])
        return response.content.decode().splitlines()

    def _assert_csv_header_contains(self, content, expected_columns):
        header = content[0]
        for col in expected_columns:
            self.assertIn(col, header)

    def _assert_rows_contain_products(self, content, products):
        for product_name, vendor_name in products:
            self.assertTrue(any(
                product_name in row and vendor_name in row for row in content
            ), f"{product_name} by {vendor_name} not found in CSV")

    def _assert_patch_history_row(self, content, include_devices_column=False):
        self.assertEqual(len(content), 3)
        for package in self.packages:
            product_name = package["product"]
            product_version = package["version"]
            installer_version = package["installer_version"]
            vendor_name = package["vendor"]
            expected_prefix = f"{product_name},{product_version} - {installer_version},{vendor_name}"
            row_found = any([row for row in content[1:] if row.startswith(expected_prefix)])
            self.assertTrue(row_found)

    def test_installed_software_report_org_level(self):
        """
        Installed software report at the organisation level contains correct headers and data rows.
        """
        url = reverse(
            "organisations:installed-software-report", kwargs={"org_id": self.organisation.secure_id}
        )
        content = self._assert_csv_response(self.client.get(url))

        expected_columns = [
            "Severity", "Name", "Version", "Vendor", "Vulnerabilities", "Devices", "Patch Detected"
        ]
        self._assert_csv_header_contains(content, expected_columns)
        self._assert_rows_contain_products(content, [
            ("Active Protect", "CyberSmart"),
            ("Edge Browser", "Microsoft")
        ])

    def test_patch_history_report_org_level(self):
        """
        Patch history report at the organisation level contains correct headers and data row.
        """
        url = reverse("organisations:patch-history-report", kwargs={"org_id": self.organisation.secure_id})
        content = self._assert_csv_response(self.client.get(url))

        expected_columns = [
            "Name", "Version", "Vendor", "Patch Status", "Started", "Finished", "Devices"
        ]
        self._assert_csv_header_contains(content, expected_columns)
        self._assert_patch_history_row(content, include_devices_column=True)

    def test_installed_software_report_app_install_level(self):
        """
        Installed software report at the app install level contains correct headers and data rows.
        """
        content = self._assert_csv_response(self.client.get(self.app_install.url_installed_software_report()))

        expected_columns = [
            "Severity", "Name", "Version", "Vendor", "Vulnerabilities", "Patch Detected"
        ]
        self._assert_csv_header_contains(content, expected_columns)
        self._assert_rows_contain_products(content, [
            ("Active Protect", "CyberSmart"),
            ("Edge Browser", "Microsoft")
        ])

    def test_patch_history_report_app_install_level(self):
        """
        Patch history report at the app install level contains correct headers and data row.
        """
        content = self._assert_csv_response(self.client.get(self.app_install.url_patch_history_report()))

        expected_columns = [
            "Name", "Version", "Vendor", "Patch Status", "Started", "Finished"
        ]
        self._assert_csv_header_contains(content, expected_columns)
        self._assert_patch_history_row(content)
