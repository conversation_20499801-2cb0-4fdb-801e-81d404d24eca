from unittest.mock import patch

from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from appusers.tests.test_constants import (
    AI2_EDGE,
)
from appusers.tests.helpers import SoftwarePackageHelperTestMixin
from django.test import TestCase
from django.utils import timezone
from opswat.models import Product
from opswat_patch.factories import (
    OpswatPatchInstallerFactory,
    OpswatProductPatchInstallerFactory,
)
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from vulnerabilities.utils import OPSWAT_SOURCE


class TestPopulateWithOpswatProductPatchInstaller(SoftwarePackageHelperTestMixin, TestCase):
    """Test cases for _populate_with_opswat_product_patch_installer methods in InstalledSoftwareAppInstallIndividual and InstalledSoftwareOrganisationIndividual,
    that enrich the records with OPSWAT patching data.
    """

    def setUp(self):
        self.setUpTwoOrganisationsFourAppInstalls()

    def test_populate_app_install_level(self):
        """Test that _populate_with_opswat_product_patch_installer correctly populates app install level records."""
        records = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_2,
            source=OPSWAT_SOURCE
        )

        # Create patch installers for Edge
        edge_patch = OpswatPatchInstallerFactory(
            product_name="Microsoft Edge",
            latest_version="101.0.0.0"
        )
        edge_product_patch = OpswatProductPatchInstallerFactory(
            product=Product.objects.get(name=AI2_EDGE),
            patch_installer=edge_patch,
            title="Edge Security Update"
        )

        # Mock the calculate_enrich_installed_software_with_installers function
        with patch('opswat_patch.utils.calculate_enrich_installed_software_with_installers') as mock_enrich:
            # Set up the mock to return enriched records
            def side_effect(records):
                for record in records:
                    if record.product == AI2_EDGE:
                        record.app_install_product_installer = {
                            'app_install_product_installer_id': edge_product_patch.id,
                            'title': 'Edge Security Update',
                            'is_scheduled': False,
                            'installer_version': '101.0.0.0',
                            'patch_detected_date': timezone.now(),
                        }
                return records

            mock_enrich.side_effect = side_effect

            # Call the method
            InstalledSoftwareAppInstallIndividual._populate_with_opswat_product_patch_installer(
                records,
                self.app_install_2.id
            )

            # Verify the method was called with correct parameters
            mock_enrich.assert_called_once()
            call_args = mock_enrich.call_args
            self.assertEqual(len(call_args[0][0]), 3)  # Should only pass OPSWAT records (Edge, Safari, Package)

        # Reload records from database
        edge_record = InstalledSoftwareAppInstallIndividual.objects.get(
            app_install=self.app_install_2,
            product=AI2_EDGE
        )

        # Verify the record was updated
        self.assertEqual(edge_record.opswat_product_patch_installer_id, edge_product_patch.id)
        self.assertIsNotNone(edge_record.patch_detected_date)

    def test_populate_app_install_level_clears_old_enrichment(self):
        """Test that old enrichment is cleared when no installer is found."""
        # Create and set old enrichment data
        old_patch = OpswatPatchInstallerFactory(
            product_name="Old Patch",
            latest_version="1.0.0"
        )
        old_product_patch = OpswatProductPatchInstallerFactory(
            product=Product.objects.get(name=AI2_EDGE),
            patch_installer=old_patch,
            title="Old Patch"
        )

        edge_record = InstalledSoftwareAppInstallIndividual.objects.get(
            app_install=self.app_install_2,
            product=AI2_EDGE
        )
        edge_record.opswat_product_patch_installer = old_product_patch
        edge_record.patch_detected_date = timezone.now()
        edge_record.save()

        # Get OPSWAT software records (after setting old enrichment)
        records = list(InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_2,
            source=OPSWAT_SOURCE
        ))

        # Mock enrichment to return no installer
        with patch('opswat_patch.utils.calculate_enrich_installed_software_with_installers') as mock_enrich:
            # Return records without app_install_product_installer attribute
            def side_effect(records):
                # Don't add any enrichment data - this simulates no matching installer found
                for record in records:
                    # Ensure no enrichment data is attached
                    if hasattr(record, 'app_install_product_installer'):
                        delattr(record, 'app_install_product_installer')
                return records

            mock_enrich.side_effect = side_effect

            # Call the method
            InstalledSoftwareAppInstallIndividual._populate_with_opswat_product_patch_installer(
                records,
                self.app_install_2.id
            )

        # Reload and verify old enrichment was cleared
        edge_record.refresh_from_db()
        self.assertIsNone(edge_record.opswat_product_patch_installer)
        self.assertIsNone(edge_record.patch_detected_date)

    def test_populate_organisation_level(self):
        """Test that _populate_with_opswat_product_patch_installer correctly populates org level records."""
        # Get OPSWAT software records for org1
        records = list(InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=self.org1,
            source=OPSWAT_SOURCE
        ))

        # We know from the test setup that org1 has OPSWAT records
        self.assertGreater(len(records), 0, "Should have OPSWAT records for org1")

        # Create patch installers for ALL OPSWAT products in org1
        patch_installers = {}
        for record in records:
            patch_installer = OpswatPatchInstallerFactory(
                product_name=f"{record.product} Patch",
                latest_version="101.0.0.0"
            )
            # Get or create the product
            product, _ = Product.objects.get_or_create(name=record.product)
            product_patch = OpswatProductPatchInstallerFactory(
                product=product,
                patch_installer=patch_installer,
                title=f"{record.product} Security Update"
            )
            patch_installers[record.product] = product_patch

        # Mock the calculate_enrich_installed_software_with_installers function
        with patch('opswat_patch.utils.calculate_enrich_installed_software_with_installers') as mock_enrich:
            # Set up the mock to return enriched records
            def side_effect(records):
                for record in records:
                    if record.product in patch_installers:
                        product_patch = patch_installers[record.product]
                        record.app_install_product_installer = {
                            'app_install_product_installer_id': product_patch.id,
                            'title': f'{record.product} Security Update',
                            'is_scheduled': False,
                            'installer_version': '101.0.0.0',
                            'patch_detected_date': timezone.now(),
                        }
                        # Also add individual mappings for org level
                        if hasattr(record, 'app_install_ids'):
                            record.app_install_product_installers_by_app_install_dynamic = {
                                str(app_id): {
                                    'app_install_product_installer_id': product_patch.id,
                                    'is_scheduled': False,
                                }
                                for app_id in record.app_install_ids[:2] if record.app_install_ids
                            }
                return records

            mock_enrich.side_effect = side_effect

            # Call the method for org level
            InstalledSoftwareOrganisationIndividual._populate_with_opswat_product_patch_installer(
                records
            )

            # Verify the method was called once
            mock_enrich.assert_called_once()

        # Reload ALL records from database and verify they were ALL updated
        for record in records:
            record.refresh_from_db()
            expected_patch = patch_installers[record.product]
            # Verify the record was updated with the correct patch installer
            self.assertEqual(record.opswat_product_patch_installer_id, expected_patch.id,
                           f"Record for {record.product} should have been updated")
            self.assertIsNotNone(record.patch_detected_date,
                               f"Record for {record.product} should have patch_detected_date")
            if hasattr(record, 'app_install_ids') and record.app_install_ids:
                self.assertIsNotNone(record.app_install_product_installers_by_app_install,
                                   f"Record for {record.product} should have individual mappings")

    def test_populate_with_no_records(self):
        """Test that _populate_with_opswat_product_patch_installer handles empty records gracefully."""
        empty_records = []

        # Should not raise any errors
        InstalledSoftwareAppInstallIndividual._populate_with_opswat_product_patch_installer(
            empty_records,
            self.app_install_1.id
        )
