from django.test import TestCase
from django.core.exceptions import ValidationError
from unittest.mock import MagicMock, patch

from accounts.factories import UserFactory
from api.v3.opswat.utils import CUSTOM_ID_PREFIX
from appusers.models import AppInstall, InstalledSoftwareAppInstallIndividual
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from appusers.models.factories import AppInstallFactory, AppUserFactory
from opswat.factories import (
    ProductVersionFactory,
    InstalledProductVersionFactory,
    ProductSignatureFactory,
    InstalledProductFactory,
    OpswatOperatingSystemFactory
)
from opswat_patch.factories import (
    OpswatScheduledProductInstallerFactory,
    OpswatProductPatchInstallerFactory,
    OpswatPatchInstallerFactory
)
from opswat_patch.models import PENDING, OpswatScheduledProductInstaller
from organisations.factories import OrganisationFactory
from organisations.models import Organisation
from software_inventory.forms import SchedulePatchInstallationForm


class SchedulePatchInstallationFormTest(TestCase):
    """Test cases for the SchedulePatchInstallationForm."""
    def setUp(self):
        """Set up test data."""
        self.organisation = MagicMock(spec=Organisation)
        self.user = MagicMock()
        self.app_install_1 = MagicMock(spec=AppInstall)
        self.app_install_1.id = 1
        self.app_install_1.device_id = "device-1"
        self.app_install_1.supports_opswat_patch.return_value = True

        self.app_install_2 = MagicMock(spec=AppInstall)
        self.app_install_2.id = 2
        self.app_install_2.device_id = "device-2"
        self.app_install_2.supports_opswat_patch.return_value = False

        self.app_installs = [self.app_install_1, self.app_install_2]

    def test_clean_app_installs_with_multiple_app_installs_skips_unsupported(self):
        """Test that unsupported app installs are silently skipped when multiple app installs are provided."""
        form = SchedulePatchInstallationForm(
            organisation=self.organisation,
            package_level="app_install",
            user=self.user,
            data={"app_installs": [1, 2], "packages": [1]}
        )

        # Mock the filter method to return a queryset with our mocked app installs
        mock_queryset = MagicMock()
        mock_queryset.__iter__.return_value = self.app_installs
        self.organisation.installed_devices.filter.return_value = mock_queryset

        # Test that only supported app installs are returned
        result = form.clean_app_installs()
        self.assertEqual(result, [1])  # Only the supported app install ID should be returned

    @patch("software_inventory.forms.enrich_installed_software_with_installers")
    @patch("software_inventory.forms.InstalledSoftwareAppInstallIndividual.objects.get")
    def test_clean_app_installs_with_single_unsupported_app_install_raises_error(self, mock_get, mock_enrich):
        """Test that ValidationError is raised when a single app install doesn't support OPSWAT patching."""
        form = SchedulePatchInstallationForm(
            organisation=self.organisation,
            package_level="app_install",
            user=self.user,
            data={"app_installs": [2], "packages": [1]}  # Only the unsupported app install
        )

        # Mock the filter method to return a queryset with only the unsupported app install
        mock_queryset = MagicMock()
        mock_queryset.__iter__.return_value = [self.app_install_2]
        mock_queryset.__len__.return_value = 1
        self.organisation.installed_devices.filter.return_value = mock_queryset

        # Test that ValidationError is raised
        with self.assertRaises(ValidationError) as context:
            form.clean_app_installs()

        # Check that the error message contains the device ID
        self.assertIn("device-2", str(context.exception))
        self.assertIn("does not support OPSWAT patching", str(context.exception))


        # Set up the mock package
        magic_mock = MagicMock(spec=InstalledSoftwareAppInstallIndividual)
        magic_mock.source_id = "1"
        magic_mock.product_id = 1
        magic_mock.app_install_product_installer = None  # No installer available
        mock_get.return_value = magic_mock

        # Mock enrich_installed_software_with_installers to return the package without installer
        mock_enrich.return_value = [magic_mock]

        self.assertFalse(form.is_valid())

    @patch("software_inventory.forms.enrich_installed_software_with_installers")
    @patch("software_inventory.forms.InstalledSoftwareAppInstallIndividual.objects.get")
    def test_package_with_multiple_source_ids(self, mock_get, mock_enrich):
        """
        Validates handling of packages with multiple comma-separated source IDs.

        The form should:
        - Reject the package if none of the product/vendor IDs has a valid opswat_id.
        - Accept the package if at least one product/vendor ID has a valid opswat_id.
        """
        broken_id_1 = "3765"
        broken_id_2 = "2345"
        broken_package_id = f"{broken_id_1},{broken_id_2}"

        test_cases = [
            {
                "description": "rejected when both IDs belong to product and vendor that use custom OPSWAT IDs",
                "opswat_ids": {
                    broken_id_1: {"product__opswat_id": f"{CUSTOM_ID_PREFIX}productid"},
                    broken_id_2: {"product__vendor__opswat_id": f"{CUSTOM_ID_PREFIX}vendorid"},
                },
                "expected_valid": False,
                "expected_cleaned_source_id": "",
                "expected_error": 'Package "Broken Package" temporarily does not support patching.',
            },
            {
                "description": "allowed when one IDs belong to product/vendor with valid OPSWAT IDs",
                "opswat_ids": {
                    broken_id_1: {"product__opswat_id": f"{CUSTOM_ID_PREFIX}productid2"},
                    broken_id_2: {"product__vendor__opswat_id": "102344"},  # valid OPSWAT ID
                },
                "expected_valid": True,
                "expected_cleaned_source_id": broken_id_2,
            },
            {
                "description": "allowed when both IDs belong to product/vendor with valid OPSWAT IDs",
                "opswat_ids": {
                    broken_id_1: {"product__opswat_id": "102344"},  # valid OPSWAT ID
                    broken_id_2: {"product__vendor__opswat_id": "102345"},  # valid OPSWAT ID
                },
                "expected_valid": True,
                "expected_cleaned_source_id": ",".join(sorted([broken_id_1, broken_id_2])),
            }
        ]

        for case in test_cases:
            with self.subTest(msg=case["description"]):
                for pk, kwargs in case["opswat_ids"].items():
                    ProductVersionFactory.create(id=pk, **kwargs)

                magic_mock = MagicMock(spec=InstalledSoftwareAppInstallIndividual)
                magic_mock.product = "Broken Package"
                magic_mock.source_id = broken_package_id
                magic_mock.product_id = 1
                mock_get.return_value = magic_mock

                # Create enriched package with app_install_product_installer
                enriched_package = MagicMock(spec=InstalledSoftwareAppInstallIndividual)
                enriched_package.product = "Broken Package"
                enriched_package.source_id = broken_package_id if case["expected_valid"] else broken_package_id
                enriched_package.product_id = 1

                if case["expected_valid"]:
                    # For valid case, package should have installer info
                    enriched_package.app_install_product_installer = {
                        'app_install_product_installer_id': 123,
                        'title': 'Test Installer',
                        'is_scheduled': False,
                        'installer_version': '1.0',
                        'patch_detected_date': None
                    }
                else:
                    # For invalid case, package won't have installer (because it's not patchable)
                    enriched_package.app_install_product_installer = None

                mock_enrich.return_value = [enriched_package]

                form = SchedulePatchInstallationForm(
                    organisation=self.organisation,
                    package_level="app_install",
                    user=self.user,
                    data={"app_installs": [1], "packages": [broken_package_id]}
                )

                if case["expected_valid"]:
                    self.assertTrue(form.is_valid(), msg="Form should be valid")
                else:
                    self.assertFalse(form.is_valid(), msg="Form should be invalid")
                    self.assertIn(case["expected_error"], str(form.errors.values()))
                self.assertEqual(
                    form._clean_package_source_id(magic_mock), case["expected_cleaned_source_id"]
                )
                ProductVersionFactory._meta.model.objects.all().delete()

    def test_clean_app_installs_with_supported_app_installs(self):
        """Test that no ValidationError is raised when all app installs support OPSWAT patching."""
        form = SchedulePatchInstallationForm(
            organisation=self.organisation,
            package_level="app_install",
            user=self.user,
            data={"app_installs": [1], "packages": [1]}
        )

        # Mock the filter method to return a queryset with only supported app installs
        mock_queryset = MagicMock()
        mock_queryset.__iter__.return_value = [self.app_install_1]
        self.organisation.installed_devices.filter.return_value = mock_queryset
        mock_queryset.values_list.return_value = [1]

        # Test that no ValidationError is raised
        result = form.clean_app_installs()
        self.assertEqual(result, [1])

    def test_schedule_patch_installation_creates_with_signature(self):
        """Test that schedule_patch_installation creates installers with proper signature."""
        app_install = AppInstallFactory(app_user=AppUserFactory())
        app_install.supports_opswat_patch = MagicMock(return_value=True)
        installed_version = InstalledProductVersionFactory(installed_product__app_install=app_install)
        installed_version.signature = ProductSignatureFactory(product=installed_version.product_version.product)
        installed_version.save()
        installed_version_2 = InstalledProductVersionFactory(installed_product__app_install=app_install)
        installed_version_2.signature = ProductSignatureFactory(product=installed_version_2.product_version.product)
        installed_version_2.save()
        installed_version_3 = InstalledProductVersionFactory(installed_product__app_install=app_install)
        installed_version_3.signature = ProductSignatureFactory(product=installed_version_3.product_version.product)
        installed_version_3.save()

        form = SchedulePatchInstallationForm(
            organisation=OrganisationFactory(),
            package_level="app_install",
            user=UserFactory(),
            data={"app_installs": [app_install.id], "packages": [1]}
        )

        # Mock the validated_packages
        mock_package_existing = MagicMock()
        # the OpswatScheduledProductInstaller will already exist for installed_version with a signature
        app_install_scheduled_installer = OpswatScheduledProductInstallerFactory(
            app_install=app_install,
            opswat_product_patch_installer__product=installed_version.product_version.product,
            signature=installed_version.signature
        )
        installer_ver = app_install_scheduled_installer.opswat_product_patch_installer.patch_installer.latest_version
        mock_package_existing.app_install_product_installer = {
            'app_install_product_installer_id': app_install_scheduled_installer.opswat_product_patch_installer.id,
            "installer_version": installer_ver,
        }
        mock_package_existing.source_id = installed_version.product_version.id
        mock_package_existing.get_source_id_list = MagicMock(return_value=[mock_package_existing.source_id])
        mock_package_existing.signature = installed_version.signature
        # the other package will not have an existing scheduled installer
        mock_package_non_existing = MagicMock()
        patch_installer = OpswatProductPatchInstallerFactory(product=installed_version_2.product_version.product)
        mock_package_non_existing.app_install_product_installer = {
            'app_install_product_installer_id': patch_installer.id,
            "installer_version": installer_ver,
        }
        mock_package_non_existing.source_id = installed_version_2.product_version.id
        mock_package_non_existing.get_source_id_list = MagicMock(return_value=[mock_package_non_existing.source_id])
        mock_package_non_existing.signature = installed_version_2.signature
        # the last package will have an existing scheduled installer with signature
        app_install_scheduled_installer_3 = OpswatScheduledProductInstallerFactory(
            app_install=app_install,
            opswat_product_patch_installer__product=installed_version_3.product_version.product,
            signature=installed_version_3.signature
        )
        mock_package_existing_sign = MagicMock()
        installer_ver = app_install_scheduled_installer_3.opswat_product_patch_installer.patch_installer.latest_version
        mock_package_existing_sign.app_install_product_installer = {
            'app_install_product_installer_id': app_install_scheduled_installer_3.opswat_product_patch_installer.id,
            "installer_version": installer_ver
        }
        mock_package_existing_sign.source_id = installed_version_3.product_version.id
        mock_package_existing_sign.get_source_id_list = MagicMock(return_value=[mock_package_existing_sign.source_id])
        mock_package_existing_sign.signature = installed_version_3.signature

        # Set up the form's validated packages
        form.validated_packages = [mock_package_existing, mock_package_non_existing, mock_package_existing_sign]
        form.cleaned_data = {'app_installs': [app_install.id]}

        self.assertEqual(OpswatScheduledProductInstaller.objects.count(), 2)
        # Call the method
        form.schedule_patch_installation()
        self.assertEqual(OpswatScheduledProductInstaller.objects.count(), 3)

        app_install_scheduled_installer.refresh_from_db()
        # the first installer should keep its signature
        self.assertEqual(app_install_scheduled_installer.signature, installed_version.signature)
        self.assertEqual(app_install_scheduled_installer.status, PENDING)

        # the second installer should keep the signature
        self.assertEqual(app_install_scheduled_installer_3.signature, installed_version_3.signature)
        self.assertEqual(app_install_scheduled_installer_3.status, PENDING)

        # this would have been created
        app_install_scheduled_installer_2 = OpswatScheduledProductInstaller.objects.get(
            app_install=app_install,
            opswat_product_patch_installer=patch_installer,
        )
        self.assertEqual(app_install_scheduled_installer_2.signature, installed_version_2.signature)

    def test_organisation_level_uses_per_app_install_installer(self):
        """Test that organization-level scheduling uses per-app_install installer when available."""
        # Create test data
        user = UserFactory()
        organisation = OrganisationFactory()
        app_install_1 = AppInstallFactory(app_user=AppUserFactory(organisation=organisation))
        app_install_2 = AppInstallFactory(app_user=AppUserFactory(organisation=organisation))

        # Add OS information to app_installs
        # Windows OS ID is typically in range 1-20
        OpswatOperatingSystemFactory(app_install=app_install_1, os_id=10)  # Windows
        # macOS OS ID is typically in range 55-70
        OpswatOperatingSystemFactory(app_install=app_install_2, os_id=60)  # macOS

        # Create a product and version with specific raw_version
        product_version = ProductVersionFactory(raw_version="1.0.0")

        # Create patch installers with newer versions
        patch_installer_1 = OpswatPatchInstallerFactory(latest_version="2.0.0")
        patch_installer_2 = OpswatPatchInstallerFactory(latest_version="2.0.0")

        # Create installers with OS restrictions
        windows_installer = OpswatProductPatchInstallerFactory(
            product=product_version.product,
            patch_installer=patch_installer_1,
            os_allow="[1,20]",  # Only allow Windows OS range
            title="Windows Installer"
        )
        macos_installer = OpswatProductPatchInstallerFactory(
            product=product_version.product,
            patch_installer=patch_installer_2,
            os_allow="[55,70]",  # Only allow macOS OS range
            title="macOS Installer"
        )

        # Create signatures for matching
        signature_1 = ProductSignatureFactory(product=product_version.product)
        signature_2 = ProductSignatureFactory(product=product_version.product)

        # Associate signatures with installers
        windows_installer.signatures.add(signature_1)
        macos_installer.signatures.add(signature_2)

        # Create installed product versions with signatures
        InstalledProductVersionFactory(
            installed_product__app_install=app_install_1,
            product_version=product_version,
            signature=signature_1  # Windows signature
        )
        InstalledProductVersionFactory(
            installed_product__app_install=app_install_2,
            product_version=product_version,
            signature=signature_2  # macOS signature
        )

        # Create the organization-level record and trigger enrichment
        InstalledSoftwareOrganisationIndividual.update_individual_record(
            organisation_id=organisation.id
        )

        # Get the created record
        org_software = InstalledSoftwareOrganisationIndividual.objects.get(
            organisation=organisation,
            source_id=str(product_version.id)
        )

        # Create form
        form = SchedulePatchInstallationForm(
            organisation=organisation,
            package_level="organisation",
            user=user,
            data={
                "app_installs": [app_install_1.id, app_install_2.id],
                "packages": [org_software.id]
            }
        )

        # Validate form
        self.assertTrue(form.is_valid(), form.errors)

        # Schedule installation
        form.schedule_patch_installation()

        # Verify that different installers were scheduled for different app_installs
        scheduled_1 = OpswatScheduledProductInstaller.objects.get(
            app_install=app_install_1
        )
        scheduled_2 = OpswatScheduledProductInstaller.objects.get(
            app_install=app_install_2
        )

        # Each app_install should get the installer matching its OS
        self.assertEqual(scheduled_1.opswat_product_patch_installer_id, windows_installer.id)
        self.assertEqual(scheduled_2.opswat_product_patch_installer_id, macos_installer.id)

    def test_organisation_level_allows_partial_scheduling_e2e(self):
        """E2E test that organization-level validation allows scheduling when only some app_installs have it scheduled."""
        # Create real organization and users
        organisation = OrganisationFactory()
        user = UserFactory()

        # Create app users and installs
        app_user_1 = AppUserFactory(organisation=organisation)
        app_user_2 = AppUserFactory(organisation=organisation)
        app_user_3 = AppUserFactory(organisation=organisation)

        app_install_1 = AppInstallFactory(app_user=app_user_1)
        app_install_2 = AppInstallFactory(app_user=app_user_2)
        app_install_3 = AppInstallFactory(app_user=app_user_3)

        # Add OS info to app_installs
        OpswatOperatingSystemFactory(app_install=app_install_1, os_id=10)  # Windows
        OpswatOperatingSystemFactory(app_install=app_install_2, os_id=60)  # macOS
        OpswatOperatingSystemFactory(app_install=app_install_3, os_id=10)  # Windows

        # Create a product and version
        product_version = ProductVersionFactory(raw_version="1.0.0")

        # Create the patch installer first with a newer version
        patch_installer_1 = OpswatPatchInstallerFactory(latest_version="2.0.0")
        patch_installer_2 = OpswatPatchInstallerFactory(latest_version="2.0.0")

        # Create installers with OS restrictions
        windows_installer = OpswatProductPatchInstallerFactory(
            product=product_version.product,
            patch_installer=patch_installer_1,
            os_allow="[1,20]",  # Windows only
            title="Windows Installer"
        )
        macos_installer = OpswatProductPatchInstallerFactory(
            product=product_version.product,
            patch_installer=patch_installer_2,
            os_allow="[55,70]",  # macOS only
            title="macOS Installer"
        )

        # Create signatures for the installers
        signature_1 = ProductSignatureFactory(product=product_version.product)
        signature_2 = ProductSignatureFactory(product=product_version.product)

        # Associate signatures with installers
        windows_installer.signatures.add(signature_1)
        macos_installer.signatures.add(signature_2)

        # Install the product on all devices
        installed_product_1 = InstalledProductFactory(app_install=app_install_1)
        installed_product_2 = InstalledProductFactory(app_install=app_install_2)
        installed_product_3 = InstalledProductFactory(app_install=app_install_3)

        InstalledProductVersionFactory(
            installed_product=installed_product_1,
            product_version=product_version,
            signature=signature_1  # Match Windows installer signature
        )
        InstalledProductVersionFactory(
            installed_product=installed_product_2,
            product_version=product_version,
            signature=signature_2  # Match macOS installer signature
        )
        InstalledProductVersionFactory(
            installed_product=installed_product_3,
            product_version=product_version,
            signature=signature_1  # Match Windows installer signature
        )

        # Create existing scheduled installer for app_install_1 only
        existing_scheduled = OpswatScheduledProductInstallerFactory(
            app_install=app_install_1,
            opswat_product_patch_installer=windows_installer,
            status="failed"  # Not pending, but still counts as scheduled
        )

        # Refresh materialized view
        InstalledSoftwareOrganisationIndividual.refresh()

        # Get the package ID from the materialized view
        org_software = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=organisation,
            source_id=str(product_version.id)
        ).first()

        self.assertIsNotNone(org_software, "Organization software should exist in materialized view")

        # Create form - trying to schedule for all 3 app_installs
        form = SchedulePatchInstallationForm(
            organisation=organisation,
            package_level="organisation",
            user=user,
            data={
                "app_installs": [app_install_1.id, app_install_2.id, app_install_3.id],
                "packages": [org_software.id]
            }
        )

        # Form should be VALID because only 1 out of 3 app_installs has it scheduled
        self.assertTrue(form.is_valid(), f"Form should be valid but has errors: {form.errors}")

        # Schedule the installation
        form.schedule_patch_installation()

        # Verify results
        # 1. Existing scheduled installer should be updated to PENDING
        existing_scheduled.refresh_from_db()
        self.assertEqual(existing_scheduled.status, PENDING)

        # 2. New installer should be created for app_install_2 (macOS)
        new_scheduled_2 = OpswatScheduledProductInstaller.objects.get(
            app_install=app_install_2
        )
        self.assertEqual(new_scheduled_2.opswat_product_patch_installer_id, macos_installer.id)
        self.assertEqual(new_scheduled_2.status, PENDING)

        # 3. New installer should be created for app_install_3 (Windows)
        new_scheduled_3 = OpswatScheduledProductInstaller.objects.get(
            app_install=app_install_3,
            opswat_product_patch_installer=windows_installer  # Same as app_install_1
        )
        self.assertEqual(new_scheduled_3.status, PENDING)

        # Now test that if ALL are scheduled, the form still accepts but doesn't create duplicates
        # Create another form trying to schedule the same package again
        form2 = SchedulePatchInstallationForm(
            organisation=organisation,
            package_level="organisation",
            user=user,
            data={
                "app_installs": [app_install_1.id, app_install_2.id, app_install_3.id],
                "packages": [org_software.id]
            }
        )

        # Form should be VALID because organization level never rejects (based on the pass statement)
        self.assertTrue(form2.is_valid())

        # Get the count of scheduled installers before
        scheduled_count_before = OpswatScheduledProductInstaller.objects.filter(
            app_install__in=[app_install_1, app_install_2, app_install_3]
        ).count()
        self.assertEqual(scheduled_count_before, 3)  # One for each app_install

        # Schedule again - it should update existing ones, not create new ones
        form2.schedule_patch_installation()

        # Count should remain the same
        scheduled_count_after = OpswatScheduledProductInstaller.objects.filter(
            app_install__in=[app_install_1, app_install_2, app_install_3]
        ).count()
        self.assertEqual(scheduled_count_after, 3)  # No new ones created

    @patch("software_inventory.forms.enrich_installed_software_with_installers")
    @patch("software_inventory.forms.InstalledSoftwareOrganisationIndividual.objects.get")
    def test_organisation_level_partial_scheduling_updates_existing(self, mock_get, mock_enrich):
        """Test that partial scheduling correctly updates existing scheduled installers."""
        # Create test data
        user = UserFactory()
        organisation = OrganisationFactory()
        app_install_1 = AppInstallFactory(app_user=AppUserFactory(organisation=organisation))
        app_install_2 = AppInstallFactory(app_user=AppUserFactory(organisation=organisation))

        # Create an installer
        installer = OpswatProductPatchInstallerFactory()

        # Create installed product versions
        signature = ProductSignatureFactory(product=installer.product)
        ipv1 = InstalledProductVersionFactory(
            installed_product__app_install=app_install_1,
            product_version__product=installer.product,
            signature=signature
        )
        InstalledProductVersionFactory(
            installed_product__app_install=app_install_2,
            product_version=ipv1.product_version,  # Same version
            signature=signature
        )

        # Create existing scheduled installer for app_install_1
        existing_scheduled = OpswatScheduledProductInstallerFactory(
            app_install=app_install_1,
            opswat_product_patch_installer=installer,
            status="failed"  # Not pending
        )

        # Mock the package
        mock_package = MagicMock()
        mock_package.source_id = str(ipv1.product_version.id)
        mock_package.product = "Test Product"
        mock_package.get_source_id_list = MagicMock(return_value=[mock_package.source_id])

        mock_package.app_install_product_installer = {
            'app_install_product_installer_id': installer.id,
            "installer_version": installer.patch_installer.latest_version,
            'is_scheduled': False  # Aggregate shows not scheduled
        }

        # Per-app_install info shows app_install_1 is scheduled
        mock_package.app_install_product_installers_by_app_install_dynamic = {
            app_install_1.id: {
                'app_install_product_installer_id': installer.id,
                "installer_version": installer.patch_installer.latest_version,
                'is_scheduled': True
            },
            app_install_2.id: {
                'app_install_product_installer_id': installer.id,
                "installer_version": installer.patch_installer.latest_version,
                'is_scheduled': False
            }
        }

        mock_get.return_value = mock_package
        mock_enrich.return_value = [mock_package]

        app_install_1.supports_opswat_patch = MagicMock(return_value=True)
        app_install_2.supports_opswat_patch = MagicMock(return_value=True)

        # Create form
        form = SchedulePatchInstallationForm(
            organisation=organisation,
            package_level="organisation",
            user=user,
            data={
                "app_installs": [app_install_1.id, app_install_2.id],
                "packages": ["test_package_id"]
            }
        )

        # Validate form - should be valid
        self.assertTrue(form.is_valid())

        # Schedule installation
        form.schedule_patch_installation()

        # Verify existing installer was updated
        existing_scheduled.refresh_from_db()
        self.assertEqual(existing_scheduled.status, PENDING)

        # Verify new installer was created for app_install_2
        new_scheduled = OpswatScheduledProductInstaller.objects.get(
            app_install=app_install_2,
            opswat_product_patch_installer=installer
        )
        self.assertEqual(new_scheduled.status, PENDING)
