import logging
from typing import List
from django.db import models, connection
from django.contrib.postgres.fields import ArrayField
from model_utils.models import TimeStampedModel

from vulnerabilities.utils import OPSWAT_SOURCE, REGULAR_SOURCE

from .mixins import OpswatMetadataMixin

logger = logging.getLogger(__name__)


class InstalledSoftwareOrganisationIndividual(OpswatMetadataMixin, TimeStampedModel, models.Model):
    """
    A Django model to store individual installed software records at the Organisation level.

    This model uses the same query as InstalledSoftwareOrganisationSummary materialized view
    but allows for individual record updates through async jobs.

    The unique constraint is on organisation_id, product_lc, and version_lc to match
    the materialized view's grouping.
    """
    # Composite key fields
    id = models.CharField(verbose_name='ID', max_length=266, primary_key=True, unique=True, db_index=True)
    source = models.CharField(
        verbose_name="Source System", max_length=50, choices=[(REGULAR_SOURCE, "Regular"), (OPSWAT_SOURCE, "OPSWAT")]
    )
    source_id = models.CharField(verbose_name="Source ID", help_text="This is the Opswat.ProductVersion.id for the OPSWAT records", max_length=255)

    # Foreign key to Organisation
    organisation = models.ForeignKey(
        "organisations.Organisation", related_name="installed_software_individual", on_delete=models.CASCADE
    )

    # Product information
    vendor = models.CharField(verbose_name="Product Vendor", max_length=1255, null=True, blank=True)
    product = models.CharField(verbose_name="Product Name", db_index=True, max_length=1255)
    version = models.CharField(verbose_name="Product Version", max_length=1255)

    # Lowercase versions for uniqueness constraint
    product_lc = models.CharField(verbose_name="Product Name Lowercase", max_length=1255, db_index=True)
    version_lc = models.CharField(verbose_name="Product Version Lowercase", max_length=1255, db_index=True)

    # Additional fields
    mobile_app = models.BooleanField(verbose_name="Wrapped iOS mobile app for M1 macOS", default=False)
    is_vulnerable = models.BooleanField(verbose_name="Is Vulnerable", default=False)
    cve_count = models.PositiveIntegerField(verbose_name="CVE Count", default=0)
    highest_severity = models.FloatField(verbose_name="Highest Severity", default=0.0)

    # Signatures field to store product signatures (array of integers)
    signatures = ArrayField(
        base_field=models.IntegerField(),
        verbose_name="Product Signatures", default=list, blank=True,
        help_text="Array of signature IDs associated with this software")

    # App install IDs that contribute to this summary
    app_install_ids = ArrayField(
        base_field=models.IntegerField(),
        default=list,
        verbose_name="App Install IDs",
        help_text="Array of app_install IDs that contribute to this summary"
    )
    device_count = models.PositiveIntegerField(
        verbose_name="Device Count",
        default=0,
        help_text="Number of devices this software is installed on"
    )

    # Aggregated InstalledProductVersion IDs
    aggregated_installed_product_version_ids = ArrayField(
        base_field=models.IntegerField(),
        default=list,
        verbose_name="Aggregated Installed Product Version IDs",
        help_text="Array of InstalledProductVersion IDs that were aggregated in this record"
    )

    # OPSWAT enrichment fields - these can change
    # depending on new OPSWAT data or new data in this model.
    opswat_product_patch_installer = models.ForeignKey(
        "opswat_patch.OpswatProductPatchInstaller",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="installed_software_organisation_individual",
        help_text="The matching OPSWAT patch installer for this installed software. This is used just as a general guidance." \
        " The model contains multiple app_installs aggregated here, and depending on what Operating systems they use, they may have different installers." \
        " Use app_install_product_installers_by_app_install to get the per-app-install mappings. This field is used to get an aggregate for the majority case."
    )
    patch_detected_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Date when this patch was first detected for this installed software. See similar note as in opswat_product_patch_installer." \
        " This field contains aggregated data from all app_installs and is likely to be incorrect."
    )
    app_install_product_installers_by_app_install = models.JSONField(
        default=dict,
        blank=True,
        help_text="Per-app-install installer mappings for organization-level records." \
        " Has a shape like: {AppInstall.id: OpswatProductPatchInstaller.id}"
    )

    class Meta:
        app_label = "software_inventory"
        verbose_name = "Installed Software Individual [Organisation level]"
        verbose_name_plural = "Installed Software Individuals [Organisation level]"
        unique_together = ['organisation', 'product_lc', 'version_lc']
        indexes = [
            models.Index(fields=['organisation', 'product_lc', 'version_lc']),
        ]

    def __str__(self):
        return f"({self.id}) {self.vendor}, {self.product}, {self.version}, CVEs: {self.cve_count}"

    @classmethod
    def refresh(cls):
        """
        Simulates the materialized view refresh process.
        Mostly for unit testing.
        """
        from organisations.models import Organisation
        for organisation in Organisation.objects.all():
            InstalledSoftwareOrganisationIndividual.update_individual_record(
                organisation_id=organisation.id,
            )

    @classmethod
    def refresh_async(cls):
        """
        Simulates the materialized view refresh process.
        Mostly for unit testing.
        """
        from software_inventory.tasks import update_installed_software_organisation_individual
        from organisations.models import Organisation
        for organisation in Organisation.objects.all():
            update_installed_software_organisation_individual.delay(organisation.id)

    @classmethod
    def update_individual_record(cls, organisation_id: int):
        """
        Update records for a given Organisation ID.
        Fetches all relevant software records for the organisation and synchronizes them with the database.
        This involves creating new records, updating existing ones if they've changed,
        and deleting records that are no longer reported for the organisation.
        """
        # The SQL query is adapted from InstalledSoftwareOrganisationSummary
        sql_query = """
        WITH active_installs AS (          /* only active installs once */
            SELECT
                ai.id              AS app_install_id,
                au.organisation_id
            FROM   appusers_appinstall ai
            JOIN   appusers_appuser   au ON au.id = ai.app_user_id
            WHERE  au.active
              AND  NOT ai.inactive
              AND  au.organisation_id = %s
        ),
        /* OPSWAT vulnerability facts – limited to org's product versions */
        opswat_cve_stats AS (
            SELECT
                cpv.productversion_id,
                COUNT(cve.id)                  ::int      AS cve_count,
                MAX(cve.severity_index / 10.0)::float  AS highest_severity,
                TRUE                                        AS is_vulnerable
            FROM   opswat_cve_product_version  cpv
            JOIN   opswat_cve                  cve ON cve.id = cpv.cve_id
            WHERE  cpv.productversion_id IN (
                SELECT DISTINCT ippv.product_version_id
                FROM   active_installs ai
                JOIN   opswat_installedproduct ip ON ip.app_install_id = ai.app_install_id
                JOIN   opswat_installedproductversion ippv ON ippv.installed_product_id = ip.id
            )
            GROUP  BY cpv.productversion_id
        ),
        /* Regular-source vulnerability facts – limited to org's software packages */
        regular_cve_stats AS (
            SELECT
                sp.id                                         AS software_id,
                COUNT(cver_repo.id)             ::int        AS cve_count,
                MAX(cver_repo.base_score)::float   AS highest_severity,
                TRUE                                          AS is_vulnerable
            FROM   appusers_softwarepackagecves               spc
            JOIN   appusers_softwarepackagecvescverepository  spcr
                   ON spcr.software_package_cves_id = spc.id
            JOIN   vulnerabilities_cverepository              cver_repo
                   ON cver_repo.id = spcr.cve_repository_id
            JOIN   appusers_softwarepackage                   sp
                   ON sp.id = spc.software_id
            WHERE  sp.id IN (
                SELECT DISTINCT aos.software_id
                FROM   active_installs ai
                JOIN   appusers_appreport ar ON ar.app_install_id = ai.app_install_id
                JOIN   appusers_apposinstalledsoftware aos ON aos.report_id = ar.id
            )
            GROUP  BY sp.id
        ),
        /* OPSWAT rows (already deduped on product/version) */
        opswat_rows AS (
            SELECT
                ai.app_install_id,
                'opswat'                                   AS source,
                pv.id::varchar                             AS source_id,
                (ai.organisation_id || ':opswat:' || pv.id)::varchar AS id,
                pvdr.name                                  AS vendor,
                p.name                                     AS product,
                LOWER(p.name)                              AS product_lc,
                pv.raw_version                             AS version,
                LOWER(pv.raw_version)                      AS version_lc,
                FALSE                                      AS mobile_app,
                COALESCE(cs.is_vulnerable, FALSE)          AS is_vulnerable,
                COALESCE(cs.cve_count, 0)                  AS cve_count,
                COALESCE(cs.highest_severity, 0)           AS highest_severity,
                ARRAY_AGG(DISTINCT CASE WHEN ippv.signature_id IS NOT NULL THEN ippv.signature_id::integer ELSE NULL END) FILTER (WHERE ippv.signature_id IS NOT NULL) AS signatures,
                ARRAY_AGG(DISTINCT ippv.id) AS installed_product_version_ids,
                ai.organisation_id
            FROM   active_installs           ai
            JOIN   opswat_installedproduct   ip   ON ip.app_install_id     = ai.app_install_id
            JOIN   opswat_installedproductversion ippv
                   ON ippv.installed_product_id = ip.id
            JOIN   opswat_productversion     pv   ON pv.id                 = ippv.product_version_id
            JOIN   opswat_product            p    ON p.id                  = pv.product_id
            JOIN   opswat_productvendor      pvdr ON pvdr.id               = p.vendor_id
            LEFT   JOIN opswat_cve_stats     cs   ON cs.productversion_id = pv.id
            GROUP  BY ai.app_install_id,
                     pv.id, pvdr.name, p.name, pv.raw_version,
                     cs.is_vulnerable, cs.cve_count, cs.highest_severity,
                     ai.organisation_id
        ),
        /* Just a flag saying whether an install has *any* OPSWAT row */
        opswat_exists AS (
            SELECT DISTINCT app_install_id FROM opswat_rows
        ),
        /* Regular rows (collision-filtered and vulnerability-adjusted) */
        regular_rows AS (
            SELECT
                ai.app_install_id,
                'regular'                               AS source,
                sp.id::varchar                          AS source_id,
                (ai.organisation_id || ':regular:' || sp.id)::varchar AS id,
                sp.vendor                               AS vendor,
                sp.product                              AS product,
                LOWER(sp.product)                       AS product_lc,
                sp.version                              AS version,
                LOWER(sp.version)                       AS version_lc,
                sp.mobile_app                           AS mobile_app,
                CASE WHEN oe.app_install_id IS NOT NULL
                     THEN FALSE
                     ELSE COALESCE(rcs.is_vulnerable, FALSE)
                END                                     AS is_vulnerable,
                CASE WHEN oe.app_install_id IS NOT NULL
                     THEN 0
                     ELSE COALESCE(rcs.cve_count, 0)
                END                                     AS cve_count,
                CASE WHEN oe.app_install_id IS NOT NULL
                     THEN 0
                     ELSE COALESCE(rcs.highest_severity, 0)
                END                                     AS highest_severity,
                ARRAY[]::integer[]                      AS signatures,
                ARRAY[]::integer[]                      AS installed_product_version_ids,
                ai.organisation_id
            FROM   active_installs                ai
            JOIN   appusers_appreport             ar   ON ar.app_install_id = ai.app_install_id
            JOIN   appusers_apposinstalledsoftware aos ON aos.report_id     = ar.id
            JOIN   appusers_softwarepackage       sp   ON sp.id            = aos.software_id
            LEFT   JOIN opswat_exists             oe   ON oe.app_install_id = ai.app_install_id
            LEFT   JOIN regular_cve_stats         rcs  ON rcs.software_id   = sp.id
            /* drop rows that EXACTLY collide with an OPSWAT product+version */
            WHERE  NOT EXISTS (
                     SELECT 1
                     FROM   opswat_rows orw
                     WHERE  orw.app_install_id = ai.app_install_id
                       AND  orw.product_lc     = LOWER(sp.product)
                       AND  orw.version_lc     = LOWER(sp.version)
                   )
            GROUP  BY ai.app_install_id,
                     sp.id, sp.vendor, sp.product, sp.version, sp.mobile_app,
                     oe.app_install_id,
                     rcs.is_vulnerable, rcs.cve_count, rcs.highest_severity,
                     ai.organisation_id
        ),
        /* Unioned set */
        combined AS (
            SELECT * FROM opswat_rows
            UNION ALL
            SELECT * FROM regular_rows
        )

        /* Final aggregation exactly as before */
        SELECT
            STRING_AGG(DISTINCT c.id,          ',')               AS id,
            c.organisation_id,
            MIN(c.vendor)                                         AS vendor,
            MIN(c.product)                                        AS product,
            MIN(c.version)                                        AS version,
            STRING_AGG(DISTINCT c.source,    ',')                 AS source,
            STRING_AGG(DISTINCT c.source_id, ',')                 AS source_id,
            BOOL_OR(c.mobile_app)                                 AS mobile_app,
            c.product_lc,
            c.version_lc,
            CASE
                WHEN BOOL_OR(c.source = 'opswat')
                     THEN MAX(CASE WHEN c.source = 'opswat'
                                   THEN c.highest_severity
                                   ELSE 0 END)
                ELSE MAX(c.highest_severity)
            END                                                 AS highest_severity,
            CASE
                WHEN BOOL_OR(c.source = 'opswat')
                     THEN SUM(CASE WHEN c.source = 'opswat'
                                   THEN c.cve_count
                                   ELSE 0 END)
                ELSE SUM(c.cve_count)
            END                                                 AS cve_count,
            CASE
                WHEN BOOL_OR(c.source = 'opswat')
                     THEN BOOL_OR(CASE WHEN c.source = 'opswat'
                                       THEN c.is_vulnerable
                                       ELSE FALSE END)
                ELSE BOOL_OR(c.is_vulnerable)
            END                                                 AS is_vulnerable,
            coalesce(min(c.signatures) filter(where c.signatures <> '{}'), '{}') as signatures,
            ARRAY_AGG(DISTINCT c.app_install_id) AS app_install_ids,
            CARDINALITY(ARRAY_AGG(DISTINCT c.app_install_id)) AS device_count,
            COALESCE(ARRAY_AGG(DISTINCT pvid.id) FILTER (WHERE pvid.id IS NOT NULL), '{}') AS aggregated_installed_product_version_ids
        FROM   combined c
        LEFT JOIN LATERAL unnest(c.installed_product_version_ids) AS pvid(id) ON TRUE
        GROUP  BY
            c.organisation_id,
            c.product_lc,
            c.version_lc;
        """

        with connection.cursor() as cursor:
            cursor.execute(sql_query, [organisation_id])
            columns = [col[0] for col in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        # Fetch existing records for the organisation
        existing_records_qs = cls.objects.filter(organisation_id=organisation_id)
        existing_records_map = {record.id: record for record in existing_records_qs}
        # Also create a map by unique constraint fields for duplicate detection
        existing_unique_map = {
            (record.organisation_id, record.product_lc, record.version_lc): record
            for record in existing_records_qs
        }

        records_to_create_instances = []
        records_to_update_instances = []
        records_to_delete_for_id_change = []  # Track records that need deletion due to ID change

        # Fields on the model that are populated from the query data
        model_fields_from_data = [
            'organisation_id', 'source', 'source_id', 'vendor', 'product', 'version',
            'product_lc', 'version_lc', 'mobile_app', 'is_vulnerable',
            'cve_count', 'highest_severity', 'signatures', 'app_install_ids', "device_count",
            'aggregated_installed_product_version_ids'
        ]

        query_result_ids = set()

        if not results:
            # All existing records for this organisation should be deleted if query returns nothing
            if existing_records_map:
                cls.objects.filter(id__in=list(existing_records_map.keys())).delete()
            return []

        for data_row in results:
            record_id_from_query = data_row['id']
            query_result_ids.add(record_id_from_query)

            create_or_update_kwargs = {'id': record_id_from_query}
            for field_name in model_fields_from_data:
                create_or_update_kwargs[field_name] = data_row[field_name]

            # Check for existing record by unique constraint fields
            unique_key = (
                data_row['organisation_id'],
                data_row['product_lc'],
                data_row['version_lc']
            )

            existing_record = None

            if record_id_from_query in existing_records_map:
                existing_record = existing_records_map[record_id_from_query]
            elif unique_key in existing_unique_map:
                # Found existing record with same unique constraint but different ID
                existing_record = existing_unique_map[unique_key]
                # Mark old record for deletion and create new one with correct ID
                records_to_delete_for_id_change.append(existing_record.id)
                # Don't track the old ID in query_result_ids so it gets deleted
                # Create new record with the correct ID
                records_to_create_instances.append(cls(**create_or_update_kwargs))
                continue

            if existing_record:
                has_changed = False
                for field_name in model_fields_from_data:
                    existing_value = getattr(existing_record, field_name)
                    new_value = data_row[field_name]

                    # Type-specific comparison for arrays
                    if isinstance(existing_value, list) and isinstance(new_value, list):
                        # Compare arrays as sets to ignore order differences
                        values_different = set(existing_value) != set(new_value)
                    else:
                        # For non-array fields, direct comparison
                        values_different = existing_value != new_value

                    if values_different:
                        setattr(existing_record, field_name, new_value)
                        has_changed = True
                if has_changed:
                    records_to_update_instances.append(existing_record)
            else:
                records_to_create_instances.append(cls(**create_or_update_kwargs))

        # Perform bulk database operations
        # IMPORTANT: Delete records first to avoid constraint violations when creating new ones
        ids_in_db_for_scope = set(existing_records_map.keys())
        ids_to_delete = ids_in_db_for_scope - query_result_ids
        # Also include records marked for deletion due to ID changes
        ids_to_delete = ids_to_delete.union(set(records_to_delete_for_id_change))
        if ids_to_delete:
            cls.objects.filter(id__in=list(ids_to_delete)).delete()

        if records_to_create_instances:
            cls.objects.bulk_create(records_to_create_instances, ignore_conflicts=False)

        if records_to_update_instances:
            cls.objects.bulk_update(records_to_update_instances, model_fields_from_data)

        all_affected_records = records_to_update_instances + records_to_create_instances

        # Apply enrichment to all affected records
        cls._populate_with_opswat_product_patch_installer(all_affected_records)

        return all_affected_records

    @classmethod
    def _populate_with_opswat_product_patch_installer(cls, records: List['InstalledSoftwareOrganisationIndividual']) -> None:
        """
        Apply OPSWAT enrichment to organisation-level records by using the existing enrichment function
        and storing results in the database.
        """
        # Filter to only OPSWAT records (enrichment only applies to these)
        opswat_records = [r for r in records if r.source == OPSWAT_SOURCE]
        if not opswat_records:
            return

        from opswat_patch.utils import calculate_enrich_installed_software_with_installers
        enriched_records = calculate_enrich_installed_software_with_installers(opswat_records)

        # Extract enrichment data and store in database
        records_to_update = []
        for record in enriched_records:
            # Check if record has enrichment data
            installer_data = getattr(record, 'app_install_product_installer', None)
            individual_mappings_data = getattr(record, 'app_install_product_installers_by_app_install_dynamic', {})
            individual_mappings_data_to_save = {
                int(app_install_id): int(mapping_data['app_install_product_installer_id'])
                for app_install_id, mapping_data in individual_mappings_data.items()
            }

            # Handle main installer data
            installer_id = None
            patch_detected_date = None
            if installer_data:
                installer_id = installer_data.get('app_install_product_installer_id')
                patch_detected_date = installer_data.get('patch_detected_date')

            if (
                record.opswat_product_patch_installer_id != installer_id or
                record.patch_detected_date != patch_detected_date or record.app_install_product_installers_by_app_install != individual_mappings_data_to_save
            ):
                record.opswat_product_patch_installer_id = installer_id
                record.patch_detected_date = patch_detected_date
                record.app_install_product_installers_by_app_install = individual_mappings_data_to_save
                records_to_update.append(record)

        if not records_to_update:
            return

        cls.objects.bulk_update(
            records_to_update,
            ['opswat_product_patch_installer', 'patch_detected_date', 'app_install_product_installers_by_app_install']
        )
