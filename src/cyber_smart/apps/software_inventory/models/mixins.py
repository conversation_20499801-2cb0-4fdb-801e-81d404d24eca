import operator
from typing import Optional

from django.utils.functional import cached_property


class OpswatMetadataMixin:
    """
    Mixin that provides convenience properties and methods to determine
    the patchability and package source classification.
    """

    # Attribute name of the related installer dictionary on the instance.
    PRODUCT_INSTALLER_ATTR = "app_install_product_installer"

    source: str
    version: str
    source_id: str

    @cached_property
    def is_patchable(self) -> bool:
        """
        Determine if the software package can be patched.

        Requirements:
          - Source must be OPSWAT.
          - Installer data must exist.
          - Installer must not already be scheduled.
          - Installed version must be lower than installer version.
        """
        from opswat_patch.utils import compare_semver_core

        if not self.is_opswat_source:
            return False

        installer = self._installer
        if not installer or installer.get("is_scheduled", False):
            return False

        installer_version = self.installer_version
        if not installer_version:
            return False

        return compare_semver_core(self.version, installer_version, operator.lt)

    @property
    def installer_version(self) -> Optional[str]:
        """
        Return the version from the installer if available.
        """
        return self._installer.get("installer_version") if self._installer else None

    @property
    def is_opswat_source(self) -> bool:
        """
        Check if the software source is OPSWAT.
        """
        from vulnerabilities.utils import OPSWAT_SOURCE
        return self.source == OPSWAT_SOURCE

    @property
    def is_regular_source(self) -> bool:
        """
        Check if the software source is Regular.
        """
        from vulnerabilities.utils import REGULAR_SOURCE
        return self.source == REGULAR_SOURCE

    @property
    def _installer(self) -> Optional[dict]:
        """
        Get the installer metadata dictionary from the expected attribute.
        Internal use only.
        """
        return getattr(self, self.PRODUCT_INSTALLER_ATTR, None)

    def get_source_id_list(self) -> list:
        """
        Returns a list of source IDs for the software package.

        If `source_id` contains multiple IDs separated by commas, splits them into a list.
        Otherwise, returns a single-item list containing the only ID.

        This is useful for packages that have multiple platform-specific source IDs
        (e.g., macOS and Windows versions).
        """
        return [self.source_id] if "," not in self.source_id else self.source_id.split(",")
