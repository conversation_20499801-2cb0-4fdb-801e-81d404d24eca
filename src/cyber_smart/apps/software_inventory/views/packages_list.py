from itertools import chain

from django.utils.functional import cached_property

from accounts.mixins import PermissionRoleRequiredMixin
from accounts.permissions import DEVICES_PERMISSION
from appusers.models import (
    AppInstall,
    AppOSInstalledSoftware,
    SoftwarePackageCVEs,
)
from django.db.models import QuerySet, Q
from django.shortcuts import get_object_or_404
from django.views.generic import TemplateView
from common.mixins import OrderingViewMixin
from opswat.models import CVE, ProductVersion
from opswat.utils import parse_composite_ids
from opswat_patch.models import OpswatScheduledProductInstaller
from opswat_patch.utils import enrich_installed_software_with_installers
from organisations.mixins import OrganisationAccessMixin
from signup.mixins import SignupMixin
from vulnerabilities.models import CVERepository
from vulnerabilities.utils import aggregate_installed_software_counts
from software_inventory.filters.packages_list import SeverityFilter, FILTER_REGISTRY
from software_inventory.filters.view_mixins import FilterViewMixin
from .base import SoftwareInventoryBaseView


class InstalledSoftwareBaseView(FilterViewMixin, OrderingViewMixin, SoftwareInventoryBaseView):
    """
    Base view for installed software list views.
    """
    template_name = "software_inventory/components/package_list/main.html"
    context_object_name = "packages"
    search_fields = ["product", "vendor", "version"]
    ordering_fields_map_used = False
    FILTER_REGISTRY = FILTER_REGISTRY

    @cached_property
    def __patchable_packages_ids(self) -> list[int]:
        """
        Returns a list of eligible package IDs for patching.
        Package property `is_patchable` is used to determine if a package can be patched.
        """
        return [package.id for package in enrich_installed_software_with_installers(
            self.get_queryset()
        ) if package.is_patchable]

    def get_template_names(self) -> list[str]:
        """
        Returns the appropriate template to be used for rendering the view.

        If the request is made via HTMX, this method inspects the `HX-Target` header
        to determine which partial template to return based on the target element.

        Falls back to the default template defined in `self.template_name` if no
        matching HTMX target is found or the request is not an HTMX request.
        """
        if self.request.headers.get("HX-Request"):
            target = self.request.headers.get("HX-Target")
            if target == "tab-main-content":
                return ["software_inventory/components/package_list/tab_content.html"]
            if target in ["app-install-installed-software", "installed-software-content"]:
                return ["software_inventory/components/package_list/page_content.html"]
        return [self.template_name]

    def get_queryset(self) -> QuerySet:
        """
        Returns the queryset for the installed software.
        """
        qs = self.filter_queryset(self.ordering_queryset(super().get_queryset()))
        qs = enrich_installed_software_with_installers(qs)
        return qs

    def get_severity_filter_counters(self) -> dict:
        """
        Returns a dictionary with the counts of each severity level.
        """
        severity_counts = {}
        for severity in SeverityFilter.SEVERITY_INDEX_MAP.keys():
            min_val, max_val, source = SeverityFilter.SEVERITY_INDEX_MAP[severity]
            severity_filter = self.get_raw_queryset().filter(
                Q(highest_severity__range=(min_val, max_val))
            )
            if source is not None:
                severity_filter = severity_filter.filter(source=source)
            severity_counts[severity] = severity_filter.count()
        return severity_counts

    def get_context_data(self, *, object_list=None, **kwargs) -> dict:
        """
        Returns the context data for the template.
        """
        context = super().get_context_data(object_list=object_list, **kwargs)
        total_count, safe_count, vulnerable_count, _, regular_count = aggregate_installed_software_counts(
            self.get_raw_queryset(), include_source_counts=True
        )
        context["total_count"] = total_count
        context["safe_count"] = safe_count
        context["vulnerable_count"] = vulnerable_count
        context["other_count"] = regular_count
        context["installed_devices_count"] = self.organisation.installed_devices_count
        context["bulk_enrollment"] = self.organisation.is_bulk_enrollment_type
        context["vendors"] = self.get_raw_queryset().values_list("vendor", flat=True).order_by("vendor").distinct()
        context["severity_counts"] = self.get_severity_filter_counters()
        context["patchable_packages_ids"] = self.__patchable_packages_ids

        return context


class PackageBaseView(PermissionRoleRequiredMixin, SignupMixin, OrganisationAccessMixin, TemplateView):
    """
    Package base view for displaying package details.
    """
    template_name = "software_inventory/components/package_details/main.html"
    permission_required = DEVICES_PERMISSION
    view_level = "app_install"

    def get_raw_queryset(self) -> QuerySet:
        """
        Returns a basic queryset where the package is located.
        """
        raise NotImplementedError()

    def _get_package(self):
        """
        Returns the package object based on the URL parameters or raises 404 if not found.
        """
        return get_object_or_404(self.get_raw_queryset(), id=self.kwargs.get("package_id"))

    def _get_cves(self) -> list[dict]:
        """
        Fetches CVEs associated with a package. Handles both regular and OPSWAT software CVEs.

        Returns:
            list[dict]: A list of CVE dictionaries with relevant details.
        """
        opswat_ids, regular_ids = parse_composite_ids(self.kwargs.get("package_id"))

        regular_cves = self._get_regular_cves(regular_ids) if regular_ids else []
        opswat_cves = self._get_opswat_cves(opswat_ids) if opswat_ids else []

        return regular_cves + opswat_cves

    def _get_regular_cves(self, regular_ids: list) -> list[CVERepository]:
        """
        Fetches CVEs for regular software packages.

        Args:
            regular_ids (list): List of regular software package IDs.

        Returns:
            list: List of CVERepository objects.
        """
        vulnerable_packages = SoftwarePackageCVEs.objects.filter(software_id__in=regular_ids)
        cve_ids = set(chain.from_iterable(vulnerable_packages.values_list("cves", flat=True)))

        return list(
            CVERepository.objects.filter(cve_id__in=cve_ids)
            .order_by('-base_score', '-published_at')
        )

    def _get_opswat_cves(self, opswat_ids: list) -> list[dict]:
        """
        Fetches CVEs for OPSWAT software packages.

        Args:
            opswat_ids (list): List of OPSWAT product version IDs.

        Returns:
            list: List of CVE dictionaries matching CVERepository fields.
        """
        product_versions = ProductVersion.objects.filter(id__in=opswat_ids)
        cves = list(
            CVE.objects.filter(product_version__in=product_versions)
            .order_by('-severity_index', '-published_at')
        )

        return [
            {
                'cve_id': cve.cve_id,
                'base_score': cve.severity_index / 10.0,  # Convert 0-100 to 0-10 scale
                'base_severity': cve.get_severity_display(),
                'published_at': cve.published_at,
                'description': cve.description,
                'references': cve.details.get('references', []) if cve.details else []
            }
            for cve in cves
        ]


    def _get_devices(self, package) -> list[dict]:
        """
        Returns devices where the package is installed.
        """
        opswat_ids, regular_ids = parse_composite_ids(self.kwargs.get("package_id"))

        def get_device_ids(queryset, field_name):
            """
            Helper function to retrieve distinct device IDs from a queryset.
            """
            return set(queryset.values_list(field_name, flat=True)) if queryset.exists() else set()

        # Fetch regular software device IDs
        user_installs_regular = get_device_ids(
            AppOSInstalledSoftware.objects.filter(
                software_id__in=regular_ids,
                report__app_install__app_user__organisation=self.organisation,
                report__app_install__app_user__active=True,
                report__app_install__inactive=False
            ).distinct("report__app_install"),
            "report__app_install_id"
        )

        # Fetch OPSWAT software device IDs
        user_installs_opswat = get_device_ids(
            AppInstall.objects.filter(
                installed_products__product_versions__id__in=opswat_ids,
                app_user__organisation=self.organisation,
                app_user__active=True,
                inactive=False
            ),
            "id"
        )

        # Combine IDs from both scanners
        combined_ids = user_installs_regular | user_installs_opswat

        app_installs = AppInstall.objects.active().installs_without_deprecated_duplicates().filter(
            app_user__organisation=self.organisation,
            id__in=combined_ids
        ).select_related("version", "app_user", "app_user__organisation").prefetch_related("user_login_records__user")

        # Pre-fetch scheduled installer data to avoid N+1 queries
        version_ids = package.source_id.split(",")
        app_install_ids = [obj.id for obj in app_installs]

        # Get all scheduled installers for these app_installs and versions in one query
        scheduled_app_installs = set(
            OpswatScheduledProductInstaller.non_terminal.filter(
                app_install_id__in=app_install_ids,
                opswat_product_patch_installer__product__versions__id__in=version_ids,
            ).values_list('app_install_id', flat=True)
        )

        return [
            {
                "id": obj.id,
                "hostname": obj.hostname,
                "last_username": obj.last_login_username,
                "url": obj.url,
                "is_scheduled": obj.id in scheduled_app_installs,
                'supports_opswat_patch': obj.supports_opswat_patch()
            } for obj in app_installs
        ]

    def _enrich_package_with_installers(self, package):
        raise NotImplementedError()

    def get_context_data(self, **kwargs):
        """
        Returns the context data for the template.
        """
        context = super().get_context_data()
        package = self._get_package()
        context["organisation"] = self.organisation
        context["package"] = self._enrich_package_with_installers(package)
        context["view_level"] = self.view_level
        context["devices"] = self._get_devices(package)
        context["any_device_without_patching_capabilities"] = any(not device.get("supports_opswat_patch", False) for device in context["devices"])
        context["cves"] = self._get_cves()
        context["cves_count"] = len(context["cves"])
        return context
