import json

from django.utils.functional import cached_property

from accounts.permissions import DEVICES_PERMISSION
from django.shortcuts import get_object_or_404
from django.forms import Form
from django.http import HttpResponse, HttpResponseRedirect
from django.utils.translation import gettext as _
from django.contrib import messages
from django.db.models import QuerySet
from opswat_patch.models import OpswatPatchAttempt
from opswat_patch.models.constants import SHORT_STATUS, IN_PROGRESS, COMPLETE, ERROR
from software_inventory.filters.view_mixins import FilterViewMixin
from software_inventory.filters.patch_history import FILTER_REGISTRY

from .base import SoftwareInventoryBaseView


class PatchHistoryBaseView(FilterViewMixin, SoftwareInventoryBaseView):
    """
    View to show the patch history.
    """
    http_method_names = ["get", "post"]
    template_name = "software_inventory/components/patch_history/main.html"
    context_object_name = "attempts"
    search_fields = [
        "scheduled_product_installer__app_install__hostname",
        "scheduled_product_installer__opswat_product_patch_installer__product__name",
        "scheduled_product_installer__opswat_product_patch_installer__product__vendor__name"
    ]
    FILTER_REGISTRY = FILTER_REGISTRY

    def get_queryset(self) -> QuerySet:
        """
        Filters the queryset based on the applied filters.
        """
        return self.filter_queryset(super().get_queryset()).order_by("-modified")

    def get_form_class(self) -> type[Form]:
        """
        Returns the form class to be used for scheduling patch installations.
        To be overridden in the child class.
        The form class should have a method `schedule_patch_installation` that
        handles the scheduling of patch installations.
        """
        raise NotImplementedError()

    def get_form_kwargs(self) -> dict:
        """
        Returns the keyword arguments to be passed to the form class.
        To be overridden in the child class.
        """
        raise NotImplementedError()

    def post(self, request, *args, **kwargs) -> HttpResponse:
        """
        Handles the POST request for the patch history view.
        """
        self.object_list = self.get_queryset()
        form = self.get_form_class()(**self.get_form_kwargs())

        if form.is_valid():
            form.schedule_patch_installation()
            messages.success(request, json.dumps({
                "title": str(_("Patch Installation Scheduled")),
                "text": str(_("The patches have been successfully scheduled. "
                              "You can track their progress in the patch history details section."))
            }), extra_tags="patch-history")
        else:
            messages.error(request, json.dumps({
                "title": str(_("Patch Submission Failed")),
                "text": str(_("We encountered an issue while submitting the patches. "
                              "Please check the error details below"
                              ", resolve them, and try again:<br/>")) + "<br/>".join(sum(form.errors.values(), []))
            }), extra_tags="patch-history")
        return HttpResponseRedirect(".")

    def get_context_data(self, *, object_list=None, **kwargs) -> dict:
        context = super().get_context_data(object_list=object_list, **kwargs)
        context["attempts_count"] = self.get_raw_queryset().count()
        context["vendors"] = self._get_vendors()
        context["status_filter_options"] = SHORT_STATUS.items()
        context["status_in_progress"] = IN_PROGRESS
        context["status_complete"] = COMPLETE
        context["status_error"] = ERROR
        return context

    def get_template_names(self) -> list[str]:
        """
        Returns the appropriate template to be used for rendering the view.

        If the request is made via HTMX, this method inspects the `HX-Target` header
        to determine which partial template to return based on the target element.

        Falls back to the default template defined in `self.template_name` if no
        matching HTMX target is found or the request is not an HTMX request.
        """
        if self.request.headers.get("HX-Request"):
            target = self.request.headers.get("HX-Target")
            if target == "tab-main-content":
                return ["software_inventory/components/patch_history/tab_content.html"]
            if target in ["app-install-patch-history", "installed-software-content"]:
                return ["software_inventory/components/patch_history/page_content.html"]
        return [self.template_name]


class PatchAttemptDetailsBaseView(SoftwareInventoryBaseView):
    """
    Patch attempt details view.
    """
    template_name = "software_inventory/components/patch_details/main.html"
    permission_required = DEVICES_PERMISSION
    context_object_name = "patch_attempts"
    paginate_by = 1000

    def get_raw_queryset(self) -> QuerySet:
        """
        Returns the raw queryset for the patch installers.
        """
        return OpswatPatchAttempt.objects.filter(
            scheduled_product_installer__app_install__app_user__organisation=self.organisation,
            scheduled_product_installer=self._patch_attempt.scheduled_product_installer,
            id=self._patch_attempt.id
        ).select_related(
            "scheduled_product_installer__app_install",
            "scheduled_product_installer__opswat_product_patch_installer__product"
        ).prefetch_related("event_logs").order_by("-created")

    @cached_property
    def _patch_attempt(self):
        """
        Returns the patch attempt object based on the URL parameters or raises 404 if not found.
        """
        return get_object_or_404(OpswatPatchAttempt.objects.filter(
            scheduled_product_installer__app_install__app_user__organisation=self.organisation,
        ).select_related(
            "scheduled_product_installer__app_install",
            "scheduled_product_installer__opswat_product_patch_installer__product"
        ).prefetch_related("event_logs"), id=self.kwargs.get("attempt_id"))


    def get_context_data(self, **kwargs):
        context = super().get_context_data()
        context["organisation"] = self.organisation
        context["patch_attempt"] = self._patch_attempt
        context["status_in_progress"] = IN_PROGRESS
        context["status_complete"] = COMPLETE
        context["status_error"] = ERROR
        return context
