import logging
from celery import shared_task as task
from organisations.models import Organisation

logger = logging.getLogger(__name__)


@task
def update_installed_software_organisation_individual(organisation_id: int):
    """
    Update installed software records for a single organisation.
    """
    from software_inventory.models import InstalledSoftwareOrganisationIndividual

    try:
        InstalledSoftwareOrganisationIndividual.update_individual_record(organisation_id)
        logger.info(f"Successfully updated installed software for organisation {organisation_id}")
    except Exception as e:
        logger.error(f"Error updating installed software for organisation {organisation_id}: {e}")
        raise


@task
def update_installed_software_organisation_individual_batch(organisation_ids: list[int]):
    """
    Update installed software records for multiple organisations.
    """
    for organisation_id in organisation_ids:
        update_installed_software_organisation_individual.delay(organisation_id)


@task
def update_installed_software_all_organisations(is_async: bool = True):
    """
    Update installed software records for all organisations.
    """
    organisation_ids = Organisation.objects.values_list('id', flat=True)

    if is_async:
        for organisation_id in organisation_ids:
            update_installed_software_organisation_individual.delay(organisation_id)
    else:
        for organisation_id in organisation_ids:
            update_installed_software_organisation_individual(organisation_id)
