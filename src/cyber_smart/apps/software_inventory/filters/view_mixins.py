from typing import Any, Dict, Type
from django.db.models import QuerySet
from django.utils.functional import cached_property

from common.filters import parse_filter_params, QueryParamFilter


class FilterViewMixin:
    """
    Mixin to handle filtering of software inventory views.

    Requires:
    - self.request: Django request object
    - self.FILTER_REGISTRY: Dict[str, Type[QueryParamFilter]]
    """

    FILTER_REGISTRY: Dict[str, Type[QueryParamFilter]]

    @cached_property
    def parsed_filters(self) -> dict:
        """
        Parses the filters from the request GET parameters based on the FILTER_REGISTRY.
        Cached to avoid recomputation.
        """
        return parse_filter_params(self.request.GET, set(self.FILTER_REGISTRY.keys()))

    @property
    def has_post_retrieval_filters_applied(self) -> bool:
        """
        Checks if any non-database filters are present in the request.
        """
        non_db_keys = {
            f.key for f in self.FILTER_REGISTRY.values() if not f.is_database_filter
        }
        return any(
            key.startswith("filter_") and key in non_db_keys
            for key in self.request.GET.keys()
        )

    def filter_queryset(self, queryset: QuerySet) -> Any:
        """
        Applies database and non-database filters to the queryset.

        Returns a QuerySet or list depending on the filters applied.
        """
        for is_db_filter in (True, False):
            if not is_db_filter and self.has_post_retrieval_filters_applied:
                queryset = self.enrich_queryset(queryset)

            for key, values in self.parsed_filters.items():
                filter_class = self.FILTER_REGISTRY.get(key)
                if filter_class and filter_class.is_database_filter == is_db_filter:
                    queryset = filter_class.apply(queryset, values)

        return queryset

    def enrich_queryset(self, queryset: QuerySet) -> list:
        """
        Hook for enriching the queryset before non-database filtering.
        Override in subclasses.
        """
        raise NotImplementedError()

    def get_context_data(self, **kwargs) -> dict:
        context = super().get_context_data(**kwargs)
        context["filters_applied"] = self.parsed_filters
        return context
