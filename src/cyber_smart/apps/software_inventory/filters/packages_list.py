from typing import Iterable, Any
from datetime import timedelta

from django.utils import timezone
from django.db.models import QuerySet
from django.db.models import Q

from common.filters import QueryParamFilter
from .common import VendorFilter
from vulnerabilities.utils import REGULAR_SOURCE, OPSWAT_SOURCE


class SeverityFilter(QueryParamFilter):
    """
    Represents a filter applied to a queryset based on severity levels.
    """
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"
    UNKNOWN = "unknown"

    key = "filter_severity"

    OPTIONS = [CRITICAL, HIGH, MEDIUM, LOW, INFO, UNKNOWN]

    # OPSWAT severity mapping (based on severity_index/10)
    OPSWAT_SEVERITY_MAP = {
        CRITICAL: (8.1, 10.0),    # severity_index 81-99
        HIGH: (6.1, 8.0),         # severity_index 61-80
        MEDIUM: (4.1, 6.0),       # severity_index 41-60
        LOW: (0.1, 4.0),          # severity_index 1-40
        INFO: (0.0, 0.0),         # severity_index 0
    }

    # Regular scanner severity mapping (standard CVSS scores)
    REGULAR_SEVERITY_MAP = {
        CRITICAL: (9.0, 10.0),
        HIGH: (7.0, 8.9),
        MEDIUM: (4.0, 6.9),
        LOW: (0.1, 3.9),
        UNKNOWN: (0.0, 0.0),
    }

    SEVERITY_INDEX_MAP = {
        # Severity levels mapped to their respective ranges and sources
        # The ranges are defined as tuples of (min_value, max_value, source)
        # where source = None means no filtering by source,
        # with source != None it applies the given source (OPSWAT_SOURCE or REGULAR_SOURCE)
        # We use OPSWAT like ranges, because deprecation of using regular scanner
        # for vulnerabilities is planned.
        CRITICAL: (8.1, 10.0, None),
        HIGH: (6.1, 8.0, None),
        MEDIUM: (4.1, 6.0, None),
        LOW: (0.1, 4.0, None),
        INFO: (0.0, 0.0, OPSWAT_SOURCE), # INFO is used for OPSWAT source
        UNKNOWN: (0.0, 0.0, REGULAR_SOURCE), # UNKNOWN is used for REGULAR source
    }

    def get_options(self) -> list[str]:
        """
        Returns a list of severity options for the filter.
        """
        return self.OPTIONS

    def apply(self, queryset, values: list[str]) -> QuerySet:
        """
        Applies the severity filter to the queryset.
        """
        if not values:
            return queryset

        severity_q = Q()
        for value in values:
            if value not in self.SEVERITY_INDEX_MAP:
                continue
            min_val, max_val, source = self.SEVERITY_INDEX_MAP[value]
            filter_value_q = Q(highest_severity__range=(min_val, max_val))
            if source is not None:
                filter_value_q &= Q(source=source)
            severity_q |= filter_value_q

        return queryset.filter(severity_q)


class PatchAgeFilter(QueryParamFilter):
    """
    Represents a filter applied to a queryset based on patch age.
    """
    DAYS_14 = "14 days"
    MONTH_1 = "1 month"
    MONTHS_3_PLUS = "3 months +"

    key = "filter_patch_age"

    OPTIONS = [DAYS_14, MONTH_1, MONTHS_3_PLUS]

    def get_options(self) -> list[str]:
        """
        Returns a list of patch age options for the filter.
        """
        return self.OPTIONS

    def apply(self, items: Iterable[Any], values: list[str]) -> Iterable[Any]:
        """
        Applies the patch age filter to the items.
        """
        if not values:
            return items

        now = timezone.now()
        filtered_records = []


        for record in items:
            detection_date = getattr(record, "app_install_product_installer", dict()).get("patch_detected_date")
            if not detection_date:
                continue


            for value in values:
                if value == self.DAYS_14 and detection_date <= now - timedelta(days=14):
                    filtered_records.append(record)
                    break
                elif value == self.MONTH_1 and detection_date <= now - timedelta(days=30):
                    filtered_records.append(record)
                    break
                elif value == self.MONTHS_3_PLUS and detection_date <= now - timedelta(days=90):
                    filtered_records.append(record)
                    break

        return filtered_records

    @property
    def is_database_filter(self) -> bool:
        """
        Indicates that this filter is not applied at the database level.
        """
        return False


class PatchableFilter(QueryParamFilter):
    """
    Represents a filter applied to a queryset based on patchable status.
    """
    PATCH_AVAILABLE = "patch available"
    NO_KNOWN_PATCH = "no known patch"

    key = "filter_patchable"

    OPTIONS = [PATCH_AVAILABLE, NO_KNOWN_PATCH]

    def get_options(self) -> list[str]:
        """
        Returns a list of patchable options for the filter.
        """
        return self.OPTIONS

    def apply(self, items: Iterable[Any], values: list[str]) -> Iterable[Any]:
        """
        Applies patchable filter to the items.
        """
        if not values:
            return items

        filtered_records = []

        for record in items:
            patch = getattr(record, "app_install_product_installer", None)

            for value in values:
                if value == self.PATCH_AVAILABLE and patch:
                    filtered_records.append(record)
                elif value == self.NO_KNOWN_PATCH and not patch:
                    filtered_records.append(record)

        return filtered_records

    @property
    def is_database_filter(self) -> bool:
        """
        Indicates that this filter is not applied at the database level.
        """
        return False


FILTER_REGISTRY = {
    f.key: f for f in [
        SeverityFilter(),
        PatchAgeFilter(),
        PatchableFilter(),
        VendorFilter(),
    ]
}
