from django.db.models import QuerySet, Q

from common.filters import QueryParamFilter
from opswat_patch.models.constants import ACTION_STATUS_CHOICES
from .common import VendorFilter as BaseVendorFilter


class StatusFilter(QueryParamFilter):
    """
    Represents a scheduled patch history status filter.
    """
    key = "filter_status"

    OPTIONS = [status[0] for status in ACTION_STATUS_CHOICES]

    def get_options(self) -> list[str]:
        """
        Returns a list of status options for the filter.
        """
        return self.OPTIONS

    def apply(self, queryset, values: list[str]) -> QuerySet:
        """
        Applies the status filter to the queryset.
        """
        if not values:
            return queryset

        severity_q = Q()
        for value in values:
            if value in self.get_options():
                severity_q |= Q(status=value)

        return queryset.filter(severity_q)


class VendorFilter(BaseVendorFilter):
    """
    Represents a filter applied to a queryset based on a package vendor.
    """
    vendor_lookup = "scheduled_product_installer__opswat_product_patch_installer__product__vendor__name__iexact"


class OrgLevelVendorFilter(BaseVendorFilter):
    """
    Represents a filter applied to a queryset based on a package vendor at the organisation level.
    """
    vendor_lookup = ("attempts__scheduled_product_installer__"
                     "opswat_product_patch_installer__product__vendor__name__iexact")


FILTER_REGISTRY = {
    f.key: f for f in [
        StatusFilter(),
        VendorFilter(),
    ]
}

ORGANISATION_FILTER_REGISTRY = {
    f.key: f for f in [
        StatusFilter(),
        OrgLevelVendorFilter(),
    ]
}
