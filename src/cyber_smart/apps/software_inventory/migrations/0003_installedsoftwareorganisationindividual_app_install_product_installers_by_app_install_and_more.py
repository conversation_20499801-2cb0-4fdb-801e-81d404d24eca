# Generated by Django 5.1.11 on 2025-07-21 20:32

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "opswat_patch",
            "0018_opswatpatchattempt_opswatpatcheventlog_patch_attempt_and_more",
        ),
        ("software_inventory", "0002_increase_source_field_length"),
    ]

    operations = [
        migrations.AddField(
            model_name="installedsoftwareorganisationindividual",
            name="app_install_product_installers_by_app_install",
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AddField(
            model_name="installedsoftwareorganisationindividual",
            name="opswat_product_patch_installer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="opswat_patch.opswatproductpatchinstaller",
            ),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name="installedsoftwareorganisationindividual",
            name="patch_detected_date",
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
