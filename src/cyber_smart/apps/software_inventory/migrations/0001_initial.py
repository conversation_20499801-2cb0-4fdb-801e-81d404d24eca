# Generated by Django 5.1.11 on 2025-07-14 14:57

import django.contrib.postgres.fields
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("organisations", "0194_alter_organisation_created_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="InstalledSoftwareOrganisationIndividual",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        max_length=266,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("source", models.Char<PERSON>ield(max_length=10)),
                ("source_id", models.CharField(max_length=255)),
                ("vendor", models.CharField(blank=True, max_length=1255, null=True)),
                ("product", models.Char<PERSON>ield(db_index=True, max_length=1255)),
                ("version", models.CharField(max_length=1255)),
                ("product_lc", models.CharField(db_index=True, max_length=1255)),
                ("version_lc", models.CharField(db_index=True, max_length=1255)),
                ("mobile_app", models.BooleanField(default=False)),
                ("is_vulnerable", models.BooleanField(default=False)),
                ("freq", models.PositiveIntegerField(default=0)),
                ("cve_count", models.PositiveIntegerField(default=0)),
                ("highest_severity", models.FloatField(default=0.0)),
                (
                    "signatures",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.IntegerField(),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
                (
                    "app_install_ids",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.IntegerField(), default=list, size=None
                    ),
                ),
                (
                    "organisation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="organisations.organisation",
                    ),
                ),
            ],
            options={
                "verbose_name": "Installed Software Individual [Organisation level]",
                "verbose_name_plural": "Installed Software Individuals [Organisation level]",
                "indexes": [
                    models.Index(
                        fields=["organisation", "product_lc", "version_lc"],
                        name="software_in_organis_aefc5d_idx",
                    )
                ],
                "unique_together": {("organisation", "product_lc", "version_lc")},
            },
        ),
    ]
