import simple_history
from django.db import models

from accounts.models import Profile
from appusers.models import A<PERSON><PERSON><PERSON><PERSON>, AppUser
from billing.models import DirectCustomer, DirectSubscription, PartnerBilling, PartnerSubscription
from distributors.models import Distributor, DistributorUser
from organisations.models import (
    OrganisationAdmin, OrganisationCertification, OrganisationApprovedDomain,
    OrganisationSettings,
)
from partners.models import Partner, PartnerUser
from rulebook.models import CertificationSurvey, SurveyAssessment
from trustd.models import TrustdCustomer, TrustdDevice, TrustdIndicatorAggregation

# Note: all history models are registered here and not on models (history=),
# otherwise Django Admin shell will break.

# Trustd models
simple_history.register(
    TrustdCustomer,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)
simple_history.register(
    TrustdDevice,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)
simple_history.register(
    TrustdIndicatorAggregation,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)

# Other apps
simple_history.register(
    Profile,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)

# AppUser models
simple_history.register(
    AppUser,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)
simple_history.register(
    AppInstall,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True),
    excluded_fields=['system_info']
)

# Distributor models
simple_history.register(
    Distributor,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)
simple_history.register(
    DistributorUser,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)

# Organisation models
# as an exception, we must register this directly on the model otherwise tests fail due to the is_test column
# simple_history.register(
#     Organisation,
#     app='history',
#     history_user_id_field=models.IntegerField(null=True, blank=True),
# )
simple_history.register(
    OrganisationAdmin,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)
simple_history.register(
    OrganisationCertification,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)
simple_history.register(
    OrganisationApprovedDomain,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)
simple_history.register(
    OrganisationSettings,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)

# Partner models
simple_history.register(
    Partner,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)
simple_history.register(
    PartnerUser,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)

# Rulebook models
simple_history.register(
    CertificationSurvey,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)
simple_history.register(
    SurveyAssessment,
    app='history',
    history_user_id_field=models.IntegerField(null=True, blank=True)
)

# Exception: this is registered in appusers.translation.py since it needs to add the new translation fields
# simple_history.register(
#     AppFile,
#     app='history',
#     history_user_id_field=models.IntegerField(null=True, blank=True)
# )

# legacy models stored in the primary db
simple_history.register(
    DirectCustomer,
    app='billing'
)
simple_history.register(
    DirectSubscription,
    app='billing'
)
simple_history.register(
    PartnerBilling,
    app='billing'
)
simple_history.register(
    PartnerSubscription,
    app='billing'
)
