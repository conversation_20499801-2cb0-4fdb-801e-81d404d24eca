# Generated by Django 5.1.11 on 2025-07-25 18:41

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import simple_history.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('history', '0057_remove_historicalpartner_forced_language'),
        ('rulebook', '0165_alter_appcheck_created_alter_appcheck_modified_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalSurveyAssessment',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True)),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False)),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False)),
                ('status', models.SmallIntegerField(default=0)),
                ('history_user_id', models.IntegerField(blank=True, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(max_length=1)),
                ('survey', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='rulebook.certificationsurvey')),
            ],
            options={
                'verbose_name': 'historical Survey Assessment',
                'verbose_name_plural': "historical Survey's Assessments",
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
