#!/usr/bin/env python
"""
Test WebSocket client for local development.
This script connects to the WebSocket server and logs all received messages.
"""

import asyncio
import json
import logging
import os
import sys
from typing import Dict, Optional
from urllib.parse import urlencode

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cyber_smart.settings.local')
import django  # noqa: E402
django.setup()

# These imports require Django to be setup first
import websockets  # noqa: E402
from appusers.models import AppInstall  # noqa: E402

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def run_test_connection(device_id: Optional[str] = None) -> None:
    """
    Connect to WebSocket server and log all messages.
    If device_id is not provided, uses the first available AppInstall.
    """
    # Get credentials from database
    if device_id:
        app_install = AppInstall.objects.filter(
            device_id=device_id,
            uninstalled_at__isnull=True
        ).first()
    else:
        app_install = AppInstall.objects.filter(
            uninstalled_at__isnull=True
        ).first()

    if not app_install:
        logger.error("No valid AppInstall found. Please create test data first.")
        return

    # Build URL with authentication query parameters
    auth_params: Dict[str, str] = {
        "device_id": app_install.device_id,
        "serial_number": app_install.serial_number,
        "app_user_uuid": str(app_install.app_user.uuid)
    }
    query_string = urlencode(auth_params)
    uri: str = f"ws://localhost:8888/?{query_string}"

    logger.info(f"Connecting to WebSocket server as device {app_install.device_id}")
    logger.info(f"URL: {uri}")

    try:
        async with websockets.connect(uri) as websocket:
            logger.info("Connected successfully!")

            # Receive messages
            async for message in websocket:
                data = json.loads(message)
                logger.info(f"Received message: {json.dumps(data, indent=2)}")
                print(f"\nReceived WebSocket Event:\n{json.dumps(data, indent=2)}\n")

    except websockets.exceptions.ConnectionClosed as e:
        logger.error(f"Connection closed: {e}")
    except Exception as e:
        logger.error(f"Error: {e}")


def main() -> None:
    """Main entry point."""
    device_id: Optional[str] = sys.argv[1] if len(sys.argv) > 1 else None
    asyncio.run(run_test_connection(device_id))


if __name__ == "__main__":
    main()
