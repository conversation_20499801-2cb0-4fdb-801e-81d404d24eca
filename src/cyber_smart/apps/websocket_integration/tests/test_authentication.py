"""
Tests for WebSocket authentication mechanism.
These tests verify that the authentication correlation works correctly
and that there are no race conditions when multiple clients connect.
"""
import pytest
import asyncio
import json
from unittest.mock import patch, MagicMock, AsyncMock

from cyber_smart.apps.websocket_integration.server import process_request, handler
from websockets.asyncio.server import ServerConnection
from websockets.http11 import Request


class TestWebSocketAuthentication:
    """Test WebSocket authentication mechanism."""

    @pytest.fixture
    def mock_auth_install(self):
        """Create a mock authenticated app install."""
        mock = MagicMock()
        mock.device_id = "test-device-123"
        mock.id = 1
        mock.app_user.uuid = "test-user-uuid"
        return mock

    @pytest.fixture
    def mock_connection(self):
        """Create a mock ServerConnection."""
        connection = MagicMock(spec=ServerConnection)
        connection.respond = MagicMock()
        return connection

    @pytest.fixture
    def mock_request(self):
        """Create a mock HTTP request with auth params."""
        request = MagicMock(spec=Request)
        request.path = "/ws?device_id=test-device&serial_number=TEST123&app_user_uuid=test-user"
        return request

    @pytest.mark.asyncio
    async def test_process_request_stores_auth_on_connection(self, mock_connection, mock_request, mock_auth_install):
        """Test that process_request stores authentication on the connection object."""
        # Mock the authenticate_cap_device function
        with patch('cyber_smart.apps.websocket_integration.server.authenticate_cap_device') as mock_auth:
            mock_auth.return_value = mock_auth_install

            # Call process_request
            response = await process_request(mock_connection, mock_request)

            # Verify authentication was stored on connection
            assert hasattr(mock_connection, 'ws_authenticated_app_install')
            assert mock_connection.ws_authenticated_app_install == mock_auth_install
            assert response is None  # Should continue with handshake

    @pytest.mark.asyncio
    async def test_process_request_rejects_missing_params(self, mock_connection):
        """Test that missing authentication parameters are rejected."""
        # Create request without query params
        request = MagicMock(spec=Request)
        request.path = "/ws"

        await process_request(mock_connection, request)

        # Should return 400 Bad Request
        mock_connection.respond.assert_called_once()
        args = mock_connection.respond.call_args[0]
        assert args[0].value == 400  # HTTPStatus.BAD_REQUEST

    @pytest.mark.asyncio
    async def test_process_request_rejects_invalid_auth(self, mock_connection, mock_request):
        """Test that invalid authentication is rejected."""
        # Mock authenticate_cap_device to return None (auth failed)
        with patch('cyber_smart.apps.websocket_integration.server.authenticate_cap_device') as mock_auth:
            mock_auth.return_value = None

            await process_request(mock_connection, mock_request)

            # Should return 401 Unauthorized
            mock_connection.respond.assert_called_once()
            args = mock_connection.respond.call_args[0]
            assert args[0].value == 401  # HTTPStatus.UNAUTHORIZED

    @pytest.mark.asyncio
    async def test_handler_retrieves_auth_from_connection(self, mock_auth_install):
        """Test that handler retrieves authentication from the connection object."""
        # Create a mock websocket with authentication
        websocket = AsyncMock(spec=ServerConnection)
        websocket.ws_authenticated_app_install = mock_auth_install
        websocket.send = AsyncMock()  # Mock send method

        # Create an event to signal when connection is registered
        connection_registered = asyncio.Event()

        # Mock wait_closed to capture the state before cleanup
        async def mock_wait_closed():
            # Signal that connection was registered
            connection_registered.set()
            # Simulate waiting forever (would normally wait for connection close)
            await asyncio.sleep(0.1)

        websocket.wait_closed = mock_wait_closed

        # Import CONNECTIONS directly to track changes
        from cyber_smart.apps.websocket_integration.server import CONNECTIONS

        # Clear any existing connections
        CONNECTIONS.clear()

        # Mock Redis
        with patch('cyber_smart.apps.websocket_integration.server.aioredis'):
            # Create a task for the handler (it will run until wait_closed completes)
            handler_task = asyncio.create_task(handler(websocket))

            # Wait for connection to be registered
            await connection_registered.wait()

            # Verify authentication success message was sent
            websocket.send.assert_called_once()
            sent_message = json.loads(websocket.send.call_args[0][0])
            assert sent_message['type'] == 'auth_success'
            assert sent_message['id'] == mock_auth_install.id

            # Verify connection was tracked
            assert websocket in CONNECTIONS
            assert CONNECTIONS[websocket]['device'] == mock_auth_install
            assert CONNECTIONS[websocket]['device_id'] == mock_auth_install.device_id
            assert CONNECTIONS[websocket]['channel'] == f"cap_events:{mock_auth_install.id}"

            # Cancel the handler task to clean up
            handler_task.cancel()
            try:
                await handler_task
            except asyncio.CancelledError:
                pass

        # Clean up
        CONNECTIONS.clear()

    @pytest.mark.asyncio
    async def test_handler_rejects_unauthenticated_connection(self):
        """Test that handler rejects connections without authentication."""
        # Create a mock websocket without authentication
        websocket = AsyncMock(spec=ServerConnection)
        # Simulate getattr returning None for missing attribute
        type(websocket).ws_authenticated_app_install = None

        # Call handler
        await handler(websocket)

        # Verify connection was closed with policy violation
        websocket.close.assert_called_once()
        args = websocket.close.call_args[0]
        assert args[0].value == 1008  # CloseCode.POLICY_VIOLATION

    @pytest.mark.asyncio
    async def test_concurrent_connections_no_race_condition(self, mock_connection, mock_auth_install):
        """Test that concurrent connections don't experience race conditions."""
        # Create multiple mock connections and requests
        connections = []
        requests = []
        auth_installs = []

        for i in range(5):
            conn = MagicMock(spec=ServerConnection)
            conn.respond = MagicMock()
            connections.append(conn)

            req = MagicMock(spec=Request)
            req.path = f"/ws?device_id=device-{i}&serial_number=SERIAL-{i}&app_user_uuid=user-{i}"
            requests.append(req)

            auth = MagicMock()
            auth.device_id = f"device-{i}"
            auth.id = i
            auth.app_user.uuid = f"user-{i}"
            auth_installs.append(auth)

        # Mock authenticate_cap_device to return different auth for each device
        with patch('cyber_smart.apps.websocket_integration.server.authenticate_cap_device') as mock_auth:
            def auth_side_effect(auth_data):
                # Return the correct auth based on device_id
                device_id = auth_data['device_id']
                for auth in auth_installs:
                    if auth.device_id == device_id:
                        return auth
                return None

            mock_auth.side_effect = auth_side_effect

            # Process all requests concurrently
            tasks = []
            for conn, req in zip(connections, requests):
                tasks.append(process_request(conn, req))

            await asyncio.gather(*tasks)

            # Verify each connection got its own authentication
            for i, conn in enumerate(connections):
                assert hasattr(conn, 'ws_authenticated_app_install')
                assert conn.ws_authenticated_app_install == auth_installs[i]
                assert conn.ws_authenticated_app_install.device_id == f"device-{i}"

    @pytest.mark.asyncio
    async def test_health_check_endpoint(self, mock_connection):
        """Test that health check endpoint works without authentication."""
        # Create health check request
        request = MagicMock(spec=Request)
        request.path = "/ws/health"

        await process_request(mock_connection, request)

        # Should return 200 OK
        mock_connection.respond.assert_called_once()
        args = mock_connection.respond.call_args[0]
        assert args[0].value == 200  # HTTPStatus.OK
        assert args[1] == "OK\n"
