from __future__ import absolute_import, unicode_literals

from datetime import datetime
from itertools import groupby

from billiard.exceptions import WorkerLostError
from celery import shared_task as task
from celery.utils.log import get_task_logger
from django.conf import settings
from django.core.cache import cache
from django.db.models import Q
from django.db.utils import IntegrityError
from django.forms.models import model_to_dict
from django.urls import reverse

from appusers.models import AppFile, AppUser
from billing.providers.chargebee import SUBSCRIPTION_LIVE, plans
from common.models import ProjectSettings
from emails.tasks import api_template_email, UNSUBSCRIBE_GROUPS
from lms import utils as lms_utils
from lms.exceptions import LmsNotAvailableError
from lms.handlers import LmsProvider
from notifications.handlers import NotificationHandler
from organisations.models import Organisation, OrganisationSettings

logger = get_task_logger(__name__)


def _parse_course_date(_date):
    if not _date:
        return '-'
    return datetime.strptime(_date, "%Y-%m-%dT%H:%M:%SZ").strftime('%d %B %Y')


@task
def init_trainings_notify():
    """
    Iterate over organization and init task for each one
    """
    academy_lite_enabled_organisations = OrganisationSettings.objects.filter(
        lms_send_email_notifications=True,
        organization__user_journey_passed__isnull=False,
        academy_enabled=True,
    )
    for obj in academy_lite_enabled_organisations:
        users_training_notify.apply_async(args=[obj.organization.id])


@task(
    autoretry_for=(IntegrityError, WorkerLostError, LmsNotAvailableError),
    retry_backoff=60*60*2,  # 2 hours
    rate_limit='5/m',
)
def users_training_notify(company_id):
    """
    Send a letter to users who have overdue courses. Overdue days must be in 3, 7, 30, 60
    """
    if settings.IS_TEST:
        return

    notify_diff_time = [3, 7, 30, 60]

    organisation = Organisation.objects.get(pk=company_id)
    provider = LmsProvider()
    courses = provider.get_organization_courses(organization=organisation)
    users = organisation.academy_users

    if not courses:
        raise LmsNotAvailableError("Service is not available")

    users_to_notify = []
    results = filter(lambda x: x.is_overdue, provider.get_results_for_courses(courses, users))

    for user, result in groupby(results, lambda item: item.user):
        result = list(sorted(result, key=lambda _result: _result.overdue_days, reverse=True))
        if result[0].overdue_days not in notify_diff_time:
            continue
        users_to_notify.append({'user': user, 'result': result})

    for user_to_notify in users_to_notify:
        substitutions = [
            {
                'id': 'modules',
                'value': [
                    {'name': item.course['name'], 'days': item.overdue_days}
                    for item in user_to_notify['result']
                ]
            },
            {
                'id': 'user_name',
                'value': user_to_notify['user'].get_full_name
            },
        ]
        api_template_email.delay(
            to_email=user_to_notify['user'].email,
            subject='You\'re behind...',
            template_id='d-c7b77a42efd64888912f065c535fb058',
            substitutions=substitutions,
            custom_args=[
                {
                    'Name': 'You\'re behind...',
                    'Category': 'Academy',
                    'Environment': settings.ENVIRONMENT_NAME,
                    'Audience': organisation.get_email_audience(),
                }
            ],
            vodafone_customer=organisation.is_vodafone_customer,
            organisation_id=organisation.id,
            unsubscribe_group=UNSUBSCRIBE_GROUPS['ACTIVE_PROTECT_ACADEMY'],
        )


@task(time_limit=6 * 60)
def add_user_handlers(org_id, app_users_uuid):
    """
    Create or link LMS users and set the LMS enrollment status for each user in an org
    """
    organisation = Organisation.objects.get(pk=org_id)
    app_users = AppUser.objects.filter(
        uuid__in=app_users_uuid,
    ).exclude(
        lms_enrollment_status=AppUser.STATUS.LMS_SKIPPED_ENROLLMENT,
    )

    if not organisation.is_partner_org:
        # Filter out partner users if organisation is not a partner org
        app_users = lms_utils.filter_app_users_for_academy(organisation, app_users)

    provider = LmsProvider()
    courses = provider.get_organization_courses(organization=organisation)
    latest_app_version = '.'.join(AppFile.get_desktop_version().split('.')[:3])  # version without Travis build id
    for app_user in app_users:
        lms_utils.create_lms_user_if_not_exists(app_user)
        user_provider = LmsProvider(app_user)
        user_provider.enroll_user_to_courses(courses)
        app_user.set_to_lms_enrolled()
        lms_utils.send_lms_enrollment_email(organisation, app_user, latest_app_version, courses)


@task
def enroll_users_to_course(course_id):
    key = f'lms_{course_id}'
    if cache.get(key):
        return
    cache.set(key, 1, 60 * 10)  # set key in the cache to prevent spamming LMS

    course = LmsProvider().get_course(course_id).json()
    provider = LmsProvider()

    for user in AppUser.objects.filter(
            organisation__user_journey_passed__isnull=False,
            organisation__settings__lms_send_email_notifications=True
    ).exclude(lms_enrollment_status=AppUser.STATUS.LMS_SKIPPED_ENROLLMENT):
        provider.create_user(email=user.email, name='{0} {1}'.format(user.first_name, user.last_name), app_user=user)
        try:
            LmsProvider(user).enroll_user_to_course(course)
        except KeyError:
            logger.error(f'User {user} has not been enrolled!')


@task
def enroll_failed_appusers_to_lms():
    software_plans = plans.ALL_SOFTWARE_PLANS
    individual_enrollement_organisations = Organisation.objects.filter(bulk_install=False, user_journey_passed__isnull=False)
    direct_lms_enabled_organisations = individual_enrollement_organisations.filter(settings__academy_enabled=True).filter(
        Q(
            Q(customer__isnull=False) & Q(customer__subscriptions__status__in=SUBSCRIPTION_LIVE)
            & Q(customer__subscriptions__plan_id__in=software_plans)
            ) |
        Q(Q(customer__isnull=True) & Q(software_support=True))
    ).distinct().values_list("pk", flat=True)

    indirect_lms_enabled_organisations = Organisation.objects.none()
    if ProjectSettings.is_academy_enabled():
        indirect_lms_enabled_organisations = individual_enrollement_organisations.filter(settings__academy_enabled=False).filter(
                Q(partner__distributor__is_insurer=True) |
                Q(is_partner_org=True) |
                Q(partner__distributor__name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
            ).filter(
                Q(
                    Q(customer__isnull=False) & Q(customer__subscriptions__status__in=SUBSCRIPTION_LIVE)
                    & Q(customer__subscriptions__plan_id__in=software_plans)
                ) |
                Q(Q(customer__isnull=True) & Q(software_support=True))
            ).distinct().values_list("pk", flat=True)

    organisation_with_training = list(direct_lms_enabled_organisations) + list(indirect_lms_enabled_organisations)

    if not organisation_with_training:
        return
    app_users_uuid = AppUser.objects.filter(
        organisation_id__in=organisation_with_training,
        lms_enrollment_status=AppUser.STATUS.LMS_NOT_ENROLLED,
        active=True
    ).values_list("uuid", flat=True)
    if app_users_uuid:
        # Using first organisation, this information don't matter.
        add_user_handlers.delay(organisation_with_training[0], list(app_users_uuid))


@task
def send_notification_about_user_training_modules():
    """ Sends notifications about users that have LMS academy modules still to complete or failed """
    provider = LmsProvider()
    organisations = Organisation.objects.filter(
        user_journey_passed__isnull=False,
        settings__academy_enabled=True,
    ).exclude(
        partner__name__in=settings.CANCELLED_PARTNER_NAMES
    ).prefetch_related('app_users', 'settings')
    for organisation in organisations.all():
        all_results = []
        [all_results.extend(result.get('results')) for result in provider._get_users_and_results(organisation)]
        for user_course in all_results:
            kwargs = {
                'user_name': user_course.get_full_name_or_email(),
                'organisation_id': organisation.id,
                'organisation_name': organisation.name,
                'module_name': user_course.course.get('name'),
                'url': reverse('dashboard:training-progress-modules', kwargs={
                    'org_id': organisation.secure_id,
                })
            }
            if not user_course.is_completed and not user_course.is_failed:
                NotificationHandler.send_notification(message_type='user_has_due_training_module', **kwargs)
            if user_course.is_failed:
                NotificationHandler.send_notification(message_type='user_has_failing_training_module', **kwargs)


@task
def update_analytic_data():
    """
    Updates analytic data for users that have LMS academy modules
    """
    provider = LmsProvider()
    organisations = Organisation.objects.filter(
        user_journey_passed__isnull=False,
        settings__academy_enabled=True,
    ).exclude(partner__name__in=settings.CANCELLED_PARTNER_NAMES).prefetch_related("app_users", "settings")

    for organisation in organisations.all():
        academy_users = organisation.academy_users.select_related("organisation")
        courses = provider.get_organization_courses(organization=organisation)
        results = provider.get_results_for_courses(courses, academy_users, "username")
        for user in academy_users:
            analytic = getattr(user, "analytics", None)
            if analytic:
                original_analytic = model_to_dict(analytic)
                _results = list(filter(lambda item: item.email == user.email, results))
                analytic.academy_ranking = 0
                analytic.academy_points = sum([item.points for item in _results])
                analytic.academy_modules_total = len(_results)
                analytic.academy_modules_completed = len([True for item in _results if item.is_completed])
                if model_to_dict(analytic) != original_analytic:
                    logger.info(
                        f"Updating LMS analytic data for user {user.email} in organisation {organisation.name}."
                    )
                    analytic.save()
