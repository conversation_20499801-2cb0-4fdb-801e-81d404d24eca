import json
import os
import uuid
from calendar import monthrange
from datetime import timed<PERSON><PERSON>
from unittest import skip

from dateutil.relativedelta import relativedelta
from django.test import TestCase, override_settings
from django.urls import reverse
from django.utils import timezone
from mock import MagicMock, patch, call

from appusers.models import AppUser
from common.base_tests import BaseTestCase
from lms.handlers import UserCourseResult
from lms.tasks import (enroll_users_to_course, init_trainings_notify,
                       send_notification_about_user_training_modules, users_training_notify)
from organisations.factories import OrganisationFactory
from organisations.models import OrganisationSettings


def _from_json_file(file_name):
    with open(file_name) as f:
        return json.loads(f.read())


class InitTrainingsNotifyTestCase(BaseTestCase, TestCase):

    @patch('lms.tasks.users_training_notify.apply_async')
    def test_correct_result(self, _users_training_notify):
        self.core_setting_up()

        org_1 = OrganisationFactory.create(partner=self.direct_partner)
        org_1.settings.lms_send_email_notifications = True
        org_1.settings.save()
        org_2 = OrganisationFactory.create(partner=self.direct_partner)
        org_2.settings.lms_send_email_notifications = False
        org_2.settings.save()
        org_3 = OrganisationFactory.create(user_journey_passed=timezone.now(), partner=self.direct_partner)
        org_3.settings.lms_send_email_notifications = True
        org_3.settings.save()
        org_4 = OrganisationFactory.create(user_journey_passed=timezone.now(), partner=self.direct_partner)
        org_4.settings.lms_send_email_notifications = True
        org_4.settings.academy_enabled = True
        org_4.settings.save()
        init_trainings_notify()

        _users_training_notify.assert_called_once()
        _users_training_notify.assert_called_with(args=[org_4.id])


class UsersTrainingNotify(BaseTestCase, TestCase):

    @skip("Temporary skip test")
    @override_settings(IS_TEST=False)
    @patch('common.models.model_crud')
    @patch('lms.handlers.LmsProvider.get_results_for_courses')
    @patch('lms.handlers.LmsProvider.get_organization_courses')
    @patch('lms.tasks.api_template_email.delay')
    def test_notify(self, api_template_email, get_organization_courses, get_results_for_courses, model_crud):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()

        org = self.organisation
        user = self.user
        app_user = self.app_user
        org.user_journey_passed = timezone.now() - timedelta(days=100)
        org.save()

        subject = 'Overdue courses'
        template_id = 'd-c7b77a42efd64888912f065c535fb058'

        OrganisationSettings.objects.create(organization=org, lms_send_email_notifications=True)

        username = f'{app_user.organisation_id}-{app_user.id}'

        now = timezone.now()
        courses = _from_json_file(os.path.join(os.path.dirname(__file__), 'courses.json'))
        result_1 = UserCourseResult(
            app_user, courses[0],
            {'username': username, 'email': user.email,
             'course_id': courses[0]['id'],
             'percent_grade': 0.0,
             'passed_timestamp': '2020-12-15T09:42:00.874380Z'})
        result_2 = UserCourseResult(
            app_user, courses[1],
            {'username': username, 'email': user.email,
             'course_id': courses[0]['id'],
             'percent_grade': 0.0,
             'passed_timestamp': '2020-12-15T09:42:00.874380Z'})

        get_organization_courses.return_value = courses

        get_results_for_courses.return_value = [result_1, result_2]

        # The first course overdue is 1 day. The user will be notified
        prev_moth = now - relativedelta(months=1)
        prev_month_day = monthrange(prev_moth.year, prev_moth.month)[1]

        app_user.created = now - timedelta(days=prev_month_day + 2)
        app_user.save()

        users_training_notify(org.id)
        api_template_email.assert_not_called()

        # The first course overdue is 3 day. The user will be notified
        app_user.created = now - timedelta(days=prev_month_day + 3)
        app_user.save()

        users_training_notify(org.id)
        api_template_email.assert_called_with(
            to_email=app_user.email, subject=subject, template_id=template_id,
            substitutions=[{'id': 'modules', 'value': [{'name': courses[0]['name'], 'days': 3}]},
                           {'id': 'distributor-admin-name', 'value': app_user.get_full_name}])


class EnrollUsersToCourseTestCase(BaseTestCase, TestCase):

    @patch('lms.tasks.LmsProvider.get_course')
    @patch('common.models.model_crud')
    @patch('lms.tasks.LmsProvider.enroll_user_to_course')
    @patch('lms.tasks.LmsProvider.create_user')
    def test_correct_result(self, create_user, enroll_user_to_course, model_crud, get_course):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()

        mock = MagicMock()
        get_course.return_value = mock

        org = self.organisation
        org.user_journey_passed = timezone.now() - timedelta(days=100)
        OrganisationSettings.objects.get_or_create(organization=org, defaults={'lms_send_email_notifications': True})
        org.save()

        enroll_users_to_course(uuid.uuid4().hex.upper()[0:12])
        enroll_user_to_course.assert_called_once()


@patch('notifications.handlers.NotificationHandler.send_notification')
@patch('lms.handlers.LmsProvider.get_results_for_courses')
@patch('lms.handlers.LmsProvider.get_organization_courses')
class NotificationUserHasDueTrainingModulesTaskTest(BaseTestCase, TestCase):

    def setUp(self) -> None:
        self.core_setting_up()
        self.creating_app_user()
        self.organisation.user_journey_passed = timezone.now()
        self.organisation.save()
        self.organisation.settings.academy_enabled = True
        self.organisation.settings.save()
        # enrol user to academy
        self.app_user.active = True
        self.app_user.lms_enrollment_status = AppUser.STATUS.LMS_ENROLLED
        self.app_user.save()

        self.app_user_2 = AppUser.objects.create(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='AppUser 2',
            last_name='Last AppUser 2',
            active=True,
            lms_enrollment_status=AppUser.STATUS.LMS_ENROLLED,
        )
        self.courses = _from_json_file(os.path.join(os.path.dirname(__file__), 'courses.json'))
        self.kwargs_1 = {
            'message_type': 'user_has_due_training_module',
            'user_name': f'{self.app_user.first_name} {self.app_user.last_name}',
            'organisation_id': self.organisation.id,
            'organisation_name': self.organisation.name,
            'module_name': self.courses[0].get('name'),
            'url': reverse('dashboard:training-progress-modules', kwargs={
                'org_id': self.organisation.secure_id,
            })
        }
        self.kwargs_2 = {
            'message_type': 'user_has_due_training_module',
            'user_name': f'{self.app_user_2.first_name} {self.app_user_2.last_name}',
            'organisation_id': self.organisation.id,
            'organisation_name': self.organisation.name,
            'module_name': self.courses[2].get('name'),
            'url': reverse('dashboard:training-progress-modules', kwargs={
                'org_id': self.organisation.secure_id,
            })
        }

    def create_results(self, percent_1=None, percent_2=0.9, percent_3=None):
        username = f'{self.app_user.organisation_id}-{self.app_user.id}'
        username_2 = f'{self.app_user_2.organisation_id}-{self.app_user_2.id}'

        result_1 = UserCourseResult(
            self.app_user, self.courses[0],
            {'username': username, 'email': self.app_user.email,
             'course_id': self.courses[0]['id'],
             'percent_grade': percent_1,
             'passed_timestamp': None})
        result_2 = UserCourseResult(
            self.app_user, self.courses[1],
            {'username': username, 'email': self.app_user.email,
             'course_id': self.courses[1]['id'],
             'percent_grade': percent_2,
             'passed_timestamp': '2020-12-15T09:42:00.874380Z'})
        result_3 = UserCourseResult(
            self.app_user_2, self.courses[2],
            {'username': username_2, 'email': self.app_user_2.email,
             'course_id': self.courses[2]['id'],
             'percent_grade': percent_3,
             'passed_timestamp': None})
        return result_1, result_2, result_3

    def test_send_notification_not_completed(
            self, get_organization_courses, get_results_for_courses, send_notification
    ):
        """ Only courses that have not been completed by users will have notifications be sent """

        result_1, result_2, result_3 = self.create_results()
        get_organization_courses.return_value = self.courses
        get_results_for_courses.return_value = [result_1, result_2, result_3]

        send_notification_about_user_training_modules()
        send_notification.assert_has_calls([call(**self.kwargs_1), call(**self.kwargs_2)], any_order=True)

    def test_send_notification_failed(
            self, get_organization_courses, get_results_for_courses, send_notification
    ):
        """ Only courses that have been failed by users will have notifications be sent """
        # result_2 and result_3 should be the failing ones, since 0.7 < 0.8
        result_1, result_2, result_3 = self.create_results(0.9, 0.7, 0.7)

        get_organization_courses.return_value = self.courses
        get_results_for_courses.return_value = [result_1, result_2, result_3]

        self.kwargs_1['module_name'] = self.courses[1].get('name')
        self.kwargs_2['module_name'] = self.courses[2].get('name')
        self.kwargs_1['message_type'] = 'user_has_failing_training_module'
        self.kwargs_2['message_type'] = 'user_has_failing_training_module'

        send_notification_about_user_training_modules()
        send_notification.assert_has_calls([call(**self.kwargs_1), call(**self.kwargs_2)], any_order=True)

    def test_not_send_notification(self, _, __, send_notification):
        """ Organisation does not have app users, therefore no notifications to be sent """
        AppUser.objects.all().delete()
        send_notification_about_user_training_modules()
        send_notification.assert_not_called()
