from django.db import models
from django.db.models.query import QuerySet

from beta_features.models import BetaFeature
from beta_features.utils import (
    CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME, CAP_V_FIVE_BETA_VERBOSE_NAME, TRUSTD_BETA_VERBOSE_NAME
)
from beta_features.utils import get_beta_app_install_queryset_filter


class BetaDevicesMetricsMethodsMixin:
    """
    Mixin for adding methods to get beta devices metrics.
    """
    @property
    def installed_beta_apps(self) -> QuerySet:
        """
        Returns all installed beta apps.
        """
        return self.installed_devices_with_deprecated_duplicates.filter(get_beta_app_install_queryset_filter())

    @property
    def installed_beta_apps_count(self) -> int:
        """
        Returns all installed beta apps count.
        """
        return self.installed_beta_apps.count()

    @property
    def beta_secure_devices(self) -> QuerySet:
        """
        Returns all secure devices that have a beta version installed
        """
        return self.secure_devices.filter(get_beta_app_install_queryset_filter())

    @property
    def beta_secure_devices_count(self) -> int:
        """
        Returns the total number of secure devices that have a beta version installed
        """
        return self.beta_secure_devices.count()

    @property
    def beta_insecure_devices(self) -> QuerySet:
        """
        Returns all insecure devices that have a beta version installed
        """
        return self.insecure_devices.filter(get_beta_app_install_queryset_filter())

    @property
    def beta_insecure_devices_count(self) -> int:
        """
        Returns the total number of insecure devices that have a beta version installed
        """
        return self.beta_insecure_devices.count()

    @property
    def beta_non_mobile_devices(self) -> QuerySet:
        """
        Returns all non-mobile devices that have a beta version installed
        """
        return self.non_mobile_devices.filter(get_beta_app_install_queryset_filter())

    @property
    def beta_non_mobile_devices_count(self) -> int:
        """
        Returns the total number of non-mobile devices that have a beta version installed
        """
        return self.beta_non_mobile_devices.count()

    @property
    def beta_desktop_devices(self) -> QuerySet:
        """
        Returns all desktop devices that have a beta version installed
        """
        return self.desktop_devices.filter(get_beta_app_install_queryset_filter())

    @property
    def beta_desktop_devices_count(self) -> int:
        """
        Returns the total number of desktop devices that have a beta version installed
        """
        return self.beta_desktop_devices.count()

    @property
    def beta_mobile_devices(self) -> QuerySet:
        """
        Returns all mobile devices that have a beta version installed
        """
        return self.mobile_devices.filter(get_beta_app_install_queryset_filter())

    @property
    def beta_mobile_devices_count(self) -> int:
        """
        Returns the total number of mobile devices that have a beta version installed
        """
        return self.beta_mobile_devices.count()

    @property
    def beta_macos_devices(self) -> QuerySet:
        """
        Returns all macOS devices that have a beta version installed
        """
        return self.macos_devices.filter(get_beta_app_install_queryset_filter())

    @property
    def beta_macos_devices_count(self) -> int:
        """
        Returns the total number of macOS devices that have a beta version installed
        """
        return self.beta_macos_devices.count()

    @property
    def beta_windows_devices(self) -> QuerySet:
        """
        Returns all Windows devices that have a beta version installed
        """
        return self.windows_devices.filter(get_beta_app_install_queryset_filter())

    @property
    def beta_windows_devices_count(self) -> int:
        """
        Returns the total number of Windows devices that have a beta version installed
        """
        return self.beta_windows_devices.count()

    @property
    def beta_android_devices(self) -> QuerySet:
        """
        Returns all Android devices that have a beta version installed
        """
        return self.android_devices.filter(get_beta_app_install_queryset_filter())

    @property
    def beta_android_devices_count(self) -> int:
        """
        Returns the total number of Android devices that have a beta version installed
        """
        return self.beta_android_devices.count()

    @property
    def beta_ios_devices(self) -> QuerySet:
        """
        Returns all iOS devices that have a beta version installed
        """
        return self.ios_devices.filter(get_beta_app_install_queryset_filter())

    @property
    def beta_ios_devices_count(self) -> int:
        """
        Returns the total number of iOS devices that have a beta version installed
        """
        return self.beta_ios_devices.count()

    @property
    def beta_active_devices_30d(self) -> QuerySet:
        """
        Returns all active devices that have a beta version installed
        """
        return self.active_devices_30d.filter(get_beta_app_install_queryset_filter())

    @property
    def beta_active_devices_30d_count(self) -> int:
        """
        Returns the total number of active devices that have a beta version installed
        """
        return self.beta_active_devices_30d.count()


class BetaViewMixin:
    """
    Base mixin for beta views.
    """
    def get_beta_context(self, main_entity) -> dict:
        """
        Returns beta features context
        :param main_entity: Partner or Organisation
        :return: enabled beta features dict
        """

        from organisations.models import Organisation
        from partners.models import Partner

        enrolled_beta_features = BetaFeature.objects.none()
        if isinstance(main_entity, Partner | Organisation):
            enrolled_beta_features = main_entity.get_enrolled_beta_features()

        return {
            'enrolled_beta_features': enrolled_beta_features,
            'has_any_enrolled_beta_features': len(enrolled_beta_features) > 0,
        }


class BetaDevicesMetricsMixin(models.Model):
    """
    This model is intended to store beta (CAPv5 & Trustd) devices metric.
    """
    installed_beta_apps_count = models.IntegerField(
        verbose_name=f"Total beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) devices count", default=0
    )
    beta_secure_devices_count = models.IntegerField(
        verbose_name=f"Secure beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) devices count",
        help_text=f"Beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) devices that have passed all checks",
        default=0
    )
    beta_active_devices_30d_count = models.IntegerField(
        verbose_name=f"Active beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) devices [30d] count",
        help_text=f"Beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) "
                  "devices that have reported their status in the last 30 days",
        default=0
    )
    beta_mobile_devices_count = models.IntegerField(
        verbose_name=f"Mobile beta ({TRUSTD_BETA_VERBOSE_NAME}) devices count",
        help_text=f"Beta ({TRUSTD_BETA_VERBOSE_NAME})"
                  " devices that are running mobile operating systems",
        default=0
    )
    beta_macos_devices_count = models.IntegerField(
        verbose_name=f"macOS beta ({CAP_V_FIVE_BETA_VERBOSE_NAME}) devices count",
        help_text=f"Beta ({CAP_V_FIVE_BETA_VERBOSE_NAME}) devices that are running MacOS",
        default=0
    )
    beta_windows_devices_count = models.IntegerField(
        verbose_name=f"Windows beta ({CAP_V_FIVE_BETA_VERBOSE_NAME}) devices count",
        help_text=f"Beta ({CAP_V_FIVE_BETA_VERBOSE_NAME}) devices that are running Windows",
        default=0
    )

    class Meta:
        abstract = True


class BetaUsersMetricsMixin(models.Model):
    """
    This model is intended to store beta (CAPv5 & Trustd) users metric.
    """
    total_beta_users_count = models.IntegerField(
        verbose_name=f"Total beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) users count", default=0
    )
    installed_beta_users_count = models.IntegerField(
        verbose_name=f"Installed beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) users count",
        help_text=f"Beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) users that have installed the beta app",
        default=0
    )
    secure_beta_users_count = models.IntegerField(
        verbose_name=f"Secure beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) users count",
        help_text=f"Beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) users that have passed all checks",
        default=0
    )
    insecure_beta_users_count = models.IntegerField(
        verbose_name=f"Insecure beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) users count",
        help_text=f"Beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) users that have failed one or more checks",
        default=0
    )

    class Meta:
        abstract = True


class BetaOrganisationsMetricsMixin(models.Model):
    """
    This model is intended to store partner beta (CAPv5 & Trustd) organisations related metric.
    """
    beta_organisations_count = models.IntegerField(
        verbose_name=f"Total beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) organisations count",
        default=0
    )
    secure_beta_organisations_count = models.IntegerField(
        verbose_name=f"Secure beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) organisations count",
        help_text=f"Beta ({CAP_V_FIVE_BETA_AND_TRUSTD_BETA_VERBOSE_NAME}) "
                  "Organisations where all devices have passed all checks",
        default=0
    )

    class Meta:
        abstract = True
