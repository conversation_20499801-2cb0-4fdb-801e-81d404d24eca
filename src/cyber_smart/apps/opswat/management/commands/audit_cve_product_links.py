from io import BytesIO

import ijson
import requests
from django.core.management.base import BaseCommand
from packaging.version import parse as V

from cyber_smart.settings import CLOUDFLARE_DB_BUCKET
from opswat.models import ProductVersion, CVE

VULN_URL = f"https://{CLOUDFLARE_DB_BUCKET}.cybersmart.co.uk/analog/server/vuln_associations.json"

class Command(BaseCommand):
    help = (
        "Audit ProductVersion CVE links against OPSWAT, ignoring OS. "
        "Reports missing and extra links."
    )

    def add_arguments(self, parser):
        parser.add_argument(
            '--remove-extra',
            action='store_true',
            help='Remove extra ProductVersion–CVE links that are not present in OPSWAT associations.'
        )

    def handle(self, *args, **options):
        vid_to_pids = self.get_vendor_id_to_products_id_map()
        relevant_vids = set(vid_to_pids)

        resp = requests.get(VULN_URL, stream=True)
        if resp.status_code != 200:
            self.stderr.write(f"Failed to fetch OPSWAT data: {resp.status_code} {resp.reason}")
            return

        data = resp.content
        json_stream = BytesIO(data)
        assoc_map = self.get_product_cves_associations(json_stream, relevant_vids, vid_to_pids)

        if not assoc_map:
            self.stdout.write("No relevant OPSWAT associations found.")
            return

        # All relevant ProductVersions
        all_pvs = ProductVersion.objects.select_related(
            'product__vendor', 'product'
        ).values(
            'id', 'raw_version', 'product__vendor__opswat_id', 'product__opswat_id'
        ).iterator()

        want_links = set()  # (pv.id, cve_id)
        out_links = set()  # ProductVersions outside OPSWAT associations
        for pv in all_pvs:
            vid = str(pv['product__vendor__opswat_id'])
            pid = str(pv['product__opswat_id'])
            rules = assoc_map.get((vid, pid), [])
            try:
                rv = V(pv['raw_version'])
            except Exception:
                continue
            matched_any = False
            for rule in rules:
                try:
                    start_v = V(rule['start'])
                    limit_v = V(rule['limit'])
                except Exception:
                    continue
                if start_v <= rv <= limit_v:
                    matched_any = True
                    want_links.add((pv['id'], rule['cve']))
            if not matched_any:
                out_links.add((pv['id'], rv))

        # All ProductVersion – CVE
        relevant_pv_ids = {pv_id for pv_id, _ in want_links} | {pv_id for pv_id, _ in out_links}
        have_links = set(
            ProductVersion.objects
            .filter(id__in=relevant_pv_ids)
            .values_list('id', 'cves__cve_id')
            .exclude(cves__isnull=True)
        )
        # these are the links that are expected based on OPSWAT associations
        missing = sorted(want_links - have_links)
        # these are the links that are in the DB but not in OPSWAT
        extra = sorted(have_links - want_links)

        self.stdout.write(f"\nExpected CVE links : {len(want_links):,}")
        self.stdout.write(f"Already in DB     : {len(want_links & have_links):,}")
        self.stdout.write(f"Missing links     : {len(missing):,}")
        self.stdout.write(f"Extra links       : {len(extra):,}")

        if options.get('remove_extra') and extra:
            self.remove_incorrect_cve_links(extra)

    def remove_incorrect_cve_links(self, extra):
        pv_ids = {pv_id for pv_id, _ in extra}
        cve_ids = {cve_id for _, cve_id in extra}
        pvs = {pv.id: pv for pv in ProductVersion.objects.filter(id__in=pv_ids)}
        cves = {cve.cve_id: cve for cve in CVE.objects.filter(cve_id__in=cve_ids)}
        for pv_id, cve_id in extra:
            pv = pvs.get(pv_id)
            cve = cves.get(cve_id)
            if pv and cve:
                pv.cves.remove(cve)

    def get_product_cves_associations(self, json_stream, relevant_vids, vid_to_pids):
        assoc_map = {}
        for _, assoc in ijson.kvitems(json_stream, 'oesis.item.vuln_associations'):
            vid = str(assoc['v4_vid'])
            if vid not in relevant_vids:
                continue
            cve_id = assoc['cve']
            for r in assoc['ranges']:
                start, limit = r['start'], r['limit']
                for pid in assoc['v4_pids']:
                    pid = str(pid)
                    if pid not in vid_to_pids[vid]:
                        continue
                    assoc_map.setdefault((vid, pid), []).append({
                        'start': start,
                        'limit': limit,
                        'cve': cve_id,
                    })
        return assoc_map

    def get_vendor_id_to_products_id_map(self):
        # vendor opswat_id to products opswat_id mapping
        vid_to_pids = {}
        for vid, pid in (
                ProductVersion.objects
                        .values_list('product__vendor__opswat_id', 'product__opswat_id')
                        .distinct()
        ):
            vid_to_pids.setdefault(str(vid), set()).add(str(pid))
        return vid_to_pids
