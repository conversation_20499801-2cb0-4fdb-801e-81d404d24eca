from hashlib import md5
from typing import Optional, Union, Literal

from django.conf import settings
from django.core.cache import cache
from django.db import models
from semantic_version import Version
from vulnerabilities.utils import OPSWAT_SOURCE

from appusers.models.materialized_views import (
    InstalledSoftwarePartnerSummary,
)
from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from software_inventory.models import InstalledSoftwareOrganisationIndividual


class InstalledProductsHasher:
    """
    A class responsible for generating a consistent hash based on installed products' details.
    """

    def __init__(self, installed_products: list[dict[str, Union[int, str, list[str]]]]):
        self.installed_products = installed_products

    def generate_hash(self) -> str:
        """
        Generates an MD5 hash from installed products' details.
        """
        sorted_products = self._sort_products()
        formatted_string = self._format_products(sorted_products)
        return self._compute_md5(formatted_string)

    def _sort_products(self) -> list[dict[str, Union[int, str, list[str]]]]:
        """
        Sorts the list of products by 'opswat_id' to ensure consistency.
        """
        return sorted(self.installed_products, key=lambda product: product["opswat_id"])

    def _format_products(self, sorted_products: list[dict[str, Union[int, str, list[str]]]]) -> str:
        """
        Formats the product details into a consistent string representation.
        """
        formatted_entries = []
        for product in sorted_products:
            opswat_id = product["opswat_id"]
            version = product["version"]
            cves = product.get("cves", [])
            cves_str = ",".join(sorted(cves)) if cves else ""
            formatted_entries.append(f"{opswat_id}:{version}:{cves_str}")

        return "|".join(formatted_entries)

    @staticmethod
    def _compute_md5(input_string: str) -> str:
        """
        Computes the MD5 hash of the given input string.
        """
        return md5(input_string.encode("utf-8")).hexdigest()


def parse_semantic_version(version: str) -> Optional[Version]:
    """
    Parses the version string into a semantic version object.
    """
    try:
        return Version.coerce(version)
    except ValueError:
        return None


class OpswatCache:
    """
    Utility class to handle opswat_id to database record id mapping via caching.
    """
    CACHE_KEY_PREFIX: str = "opswat_id_to_db_id:"

    @staticmethod
    def get(model: models.Model, opswat_id: str) -> Optional[int]:
        """
        Retrieves the database record ID by opswat_id from the cache or database.
        """
        cache_key = f"{OpswatCache.CACHE_KEY_PREFIX}{model._meta.label_lower}:{opswat_id}"

        if not settings.IS_TEST:
            # get from cache if available
            if db_record_id := cache.get(cache_key):
                return db_record_id

        # get from db and save to cache
        if record_id := model.objects.filter(opswat_id=opswat_id).values_list("id", flat=True).first():
            cache.set(cache_key, record_id)
            return record_id


def get_installed_software_status(
        instance_id: int, level: Literal["app_install", "organisation", "partner"]
) -> dict[str, int]:
    """
    Retrieves the software installation status summary for a given instance and level.

    Args:
        instance_id: The ID of the app installation, organisation, or partner.
        level (Literal["appinstall", "organisation", "partner"]): The level at which to fetch software status.

    Returns:
        Dict[str, int]: A dictionary containing:
            - "total": Total software installations.
            - "vulnerable": Count of vulnerable software installations.
            - "safe": Count of non-vulnerable software installations.

    Raises:
        ValueError: If an invalid `level` is provided.
    """

    queryset_map = {
        "app_install": InstalledSoftwareAppInstallIndividual.objects.filter(app_install_id=instance_id),
        "organisation": InstalledSoftwareOrganisationIndividual.objects.filter(organisation_id=instance_id),
        "partner": InstalledSoftwarePartnerSummary.objects.filter(partner_id=instance_id),
    }

    queryset = queryset_map.get(level)
    if queryset is None:
        raise ValueError(f"Invalid level: {level}. Expected 'app_install', 'organisation', or 'partner'.")

    counts = queryset.aggregate(
        total=models.Count("id"),
        vulnerable=models.Count("id", filter=models.Q(is_vulnerable=True))
    )

    total = counts.get("total") or 0
    vulnerable = counts.get("vulnerable") or 0

    return {
        "total": total,
        "vulnerable": vulnerable,
        "safe": total - vulnerable
    }


def parse_composite_ids(composite_id_str: str) -> tuple[list[str], list[str]]:
    """
    Parses a comma-separated string of composite IDs into OPSWAT and regular source IDs.

    Args:
        composite_id_str: A string containing comma-separated composite IDs in the format "app_install_id:source:source_id"

    Returns:
        A tuple containing two lists:
        - List of OPSWAT source IDs
        - List of regular source IDs
    """
    source_ids = composite_id_str.split(',')
    opswat_ids = set()
    regular_ids = set()

    for composite_id in source_ids:
        # Format: app_install_id:source:source_id
        app_install_id, source, source_id = composite_id.split(':')
        if source == OPSWAT_SOURCE:
            opswat_ids.add(source_id)
        else:
            regular_ids.add(source_id)

    return list(opswat_ids), list(regular_ids)
