from factory.django import DjangoModelFactory
import factory

from .models import ProductCategory, ProductVendor, Product, ProductVersion, CVE, InstalledProduct, ProductSignature, InstalledProductVersion
from .opswat_operating_system import OpswatOperatingSystem


class ProductCategoryFactory(DjangoModelFactory):
    class Meta:
        model = ProductCategory
        django_get_or_create = ('opswat_id',)

    name = factory.Sequence(lambda n: f"Category {n}")
    opswat_id = factory.Sequence(lambda n: str(n))


class ProductVendorFactory(DjangoModelFactory):
    class Meta:
        model = ProductVendor

    name = factory.Sequence(lambda n: f"Vendor {n}")
    opswat_id = factory.Sequence(lambda n: str(100000 + n))

    @classmethod
    def get_or_create(cls, **kwargs):
        defaults = kwargs.pop('defaults', {})
        try:
            instance = ProductVendor.objects.get(name=kwargs.get('name'))
            return instance, False
        except ProductVendor.DoesNotExist:
            create_kwargs = {**defaults, **kwargs}
            return cls.create(**create_kwargs), True


class ProductFactory(DjangoModelFactory):
    class Meta:
        model = Product

    name = factory.Sequence(lambda n: f"Product {n}")
    vendor = factory.SubFactory(ProductVendorFactory)
    opswat_id = factory.Sequence(lambda n: str(200000 + n))

    @factory.post_generation
    def categories(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for category in extracted:
                self.categories.add(category)
        else:
            self.categories.add(ProductCategoryFactory())

    @classmethod
    def get_or_create(cls, **kwargs):
        defaults = kwargs.pop('defaults', {})
        try:
            instance = Product.objects.get(name=kwargs.get('name'), vendor=kwargs.get('vendor'))
            return instance, False
        except Product.DoesNotExist:
            create_kwargs = {**defaults, **kwargs}
            return cls.create(**create_kwargs), True


class ProductVersionFactory(DjangoModelFactory):
    class Meta:
        model = ProductVersion

    product = factory.SubFactory(ProductFactory)
    raw_version = factory.Sequence(lambda n: f"{n}.0.0")
    major = factory.Sequence(lambda n: n)
    minor = 0
    patch = 0
    version_channel = "stable"

    @classmethod
    def get_or_create(cls, **kwargs):
        defaults = kwargs.pop('defaults', {})
        try:
            instance = ProductVersion.objects.get(
                product=kwargs.get('product'),
                raw_version=kwargs.get('raw_version')
            )
            return instance, False
        except ProductVersion.DoesNotExist:
            create_kwargs = {**defaults, **kwargs}
            return cls.create(**create_kwargs), True


class CVEFactory(DjangoModelFactory):
    class Meta:
        model = CVE

    opswat_id = factory.Sequence(lambda n: str(400000 + n))
    severity_index = 0
    severity = CVE.SEVERITY_MODERATE
    description = "Test CVE"
    published_at = factory.Faker("date_time_this_decade", tzinfo=None)
    details = {}


class InstalledProductFactory(DjangoModelFactory):
    class Meta:
        model = InstalledProduct


class InstalledProductVersionFactory(DjangoModelFactory):
    class Meta:
        model = InstalledProductVersion

    installed_product = factory.SubFactory(InstalledProductFactory)
    product_version = factory.SubFactory(ProductVersionFactory)


class ProductSignatureFactory(DjangoModelFactory):
    class Meta:
        model = ProductSignature

    opswat_id = factory.Sequence(lambda n: str(300000 + n))
    name = factory.Sequence(lambda n: f"Signature {n}")
    support_3rd_party_patch = False
    product = factory.SubFactory(ProductFactory)


class OpswatOperatingSystemFactory(DjangoModelFactory):
    class Meta:
        model = OpswatOperatingSystem

    os_id = factory.Sequence(lambda n: n)
    os_type = OpswatOperatingSystem.OS_TYPE_WINDOWS
    architecture = "64-bit"
