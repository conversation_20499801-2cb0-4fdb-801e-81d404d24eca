import io
from unittest import mock
import json

from django.core.management import call_command
from django.test import TestCase

from opswat.factories import ProductVendorFactory, ProductFactory, ProductVersionFactory, CVEFactory


class AuditCVEProductLinksCommandTest(TestCase):
    def setUp(self):
        self.vendor = ProductVendorFactory(opswat_id=91, name="Adobe")
        self.product = ProductFactory(vendor=self.vendor, opswat_id=101, name="Acrobat Reader")
        self.product2 = ProductFactory(vendor=self.vendor, opswat_id=183, name="Acrobat")
        # ProductVersions
        self.pv_4_0 = ProductVersionFactory(product=self.product, raw_version="4.0", major=4, minor=0, patch=0)
        self.pv_4_0_5 = ProductVersionFactory(product=self.product, raw_version="4.0.5", major=4, minor=0, patch=5)
        self.pv_5_0 = ProductVersionFactory(product=self.product, raw_version="5.0", major=5, minor=0, patch=0)
        self.pv_3_0 = ProductVersionFactory(product=self.product, raw_version="3.0", major=3, minor=0, patch=0)
        self.pv_9_9 = ProductVersionFactory(product=self.product, raw_version="9.9.9", major=9, minor=9, patch=9)
        self.pv_1_2 = ProductVersionFactory(product=self.product2, raw_version="1.2", major=1, minor=2, patch=0)
        # CVEs
        self.cve1 = CVEFactory(cve_id="CVE-1999-1576")
        self.cve2 = CVEFactory(cve_id="CVE-2000-0713")
        self.cve3 = CVEFactory(cve_id="CVE-2001-1069")
        self.cve4 = CVEFactory(cve_id="CVE-2001-1070")
        # Link some ProductVersions to CVEs
        self.pv_4_0.cves.add(self.cve1)
        self.pv_1_2.cves.add(self.cve3)
        self.pv_1_2.cves.add(self.cve4)  # this should not be linked to product
        self.pv_4_0_5.cves.add(self.cve1)
        self.pv_4_0_5.cves.add(self.cve2)  # this should not be linked to product
        self.pv_4_0_5.cves.add(self.cve4)  # this should not be linked to product
        self.pv_9_9.cves.add(self.cve1)  # this should not be linked to product

        self.mock_json = {"oesis":[{"header":{"name":"OPSWAT 3rd-party Vulnerability Association Database","timestamp":1751490231,"time":"2025-07-03 04:03:51 +0700","copyright":"OPSWAT, all rights reserved, 2025. No distribution without prior permission. All information confidential.","version":"1.0","schema_version":"1.0-1.0","description":"This file contains vulnerability associations."}}]}
        self.mock_json["oesis"].append({
            "vuln_associations": {
                "2209976": {
                    "_id": 2209976,
                    "type_id": 7,
                    "v4_vid": int(self.vendor.opswat_id),
                    "v4_pids": [int(self.product.opswat_id)],
                    "ranges": [{"start": "3.0", "limit": "3.5"},{"start":"4.0","limit":"4.0.6"}],
                    "cve": self.cve1.cve_id,
                    "cpe": "cpe:/a:adobe:acrobat reader",
                    "timestamp": 1749705083,
                    "schema_version": "1.0-1.0",
                    "os_type": 1
                },
                "2210016": {
                    "_id": 2210016,
                    "type_id": 7,
                    "v4_vid": int(self.vendor.opswat_id),
                    "v4_pids": [int(self.product.opswat_id)],
                    "ranges": [{"start": "5.0", "limit": "6.0"}],
                    "cve": self.cve2.cve_id,
                    "cpe": "cpe:/a:adobe:acrobat reader",
                    "timestamp": 1749705089,
                    "schema_version": "1.0-1.0",
                    "os_type": 1
                },
                "2210017": {
                    "_id": 2210017,
                    "type_id": 7,
                    "v4_vid": int(self.vendor.opswat_id),
                    "v4_pids": [int(self.product2.opswat_id)],
                    "ranges": [{"start": "1.0", "limit": "2.0"}],
                    "cve": self.cve3.cve_id,
                    "cpe": "cpe:/a:adobe:acrobat",
                    "timestamp": 1749705090,
                    "schema_version": "1.0-1.0",
                    "os_type": 1
                },
                "2210018": {
                    "_id": 2210018,
                    "type_id": 7,
                    "v4_vid": 456,
                    "v4_pids": [123, 345],
                    "ranges": [{"start": "1.0", "limit": "2.0"}],
                    "cve": self.cve4.cve_id,
                    "cpe": "cpe:/a:other:product",
                    "timestamp": 1749705091,
                    "schema_version": "1.0-1.0",
                    "os_type": 1
                },
                "2210019": {
                    "_id": 2210019,
                    "type_id": 7,
                    "v4_vid": 1234,
                    "v4_pids": [5678],
                    "ranges": [{"start": "1.0", "limit": "2.0"}],
                    "cve": "CVE-2023-1234",
                    "cpe": "cpe:/a:other:product2",
                    "timestamp": 1749705092,
                    "schema_version": "1.0-1.0",
                    "os_type": 1
                }
            }
        })

    @mock.patch("requests.get")
    def test_audit_cve_product_links_reports_missing_and_extra(self, mock_requests_get):
        vuln_assocs = self.mock_json["oesis"][1]["vuln_associations"]
        mock_resp = mock.Mock()
        mock_resp.content = json.dumps({"oesis": [self.mock_json["oesis"][0], {"vuln_associations": vuln_assocs}] }).encode("utf-8")
        mock_resp.status_code = 200
        mock_resp.__enter__ = lambda s: s
        mock_resp.__exit__ = mock.Mock()
        mock_requests_get.return_value = mock_resp
        out = io.StringIO()
        call_command("audit_cve_product_links", stdout=out)
        output = out.getvalue()
        # Check for expected output
        self.assertIn("Expected CVE links : 5", output)
        self.assertIn("Already in DB     : 3", output)
        self.assertIn("Missing links     : 2", output)

    @mock.patch("requests.get")
    def test_audit_cve_product_links_no_missing_or_extra(self, mock_requests_get):
        # Remove all links for a clean slate
        for cve in [self.cve1, self.cve2, self.cve3, self.cve4]:
            cve.product_version.clear()
        # Add all correct links so that DB matches vuln_associations
        self.cve1.product_version.set([self.pv_3_0, self.pv_4_0, self.pv_4_0_5])
        self.cve2.product_version.set([self.pv_5_0])
        self.cve3.product_version.set([self.pv_1_2])
        self.cve4.product_version.set([])
        vuln_assocs = self.mock_json["oesis"][1]["vuln_associations"]
        mock_resp = mock.Mock()
        mock_resp.content = json.dumps({"oesis": [self.mock_json["oesis"][0], {"vuln_associations": vuln_assocs}] }).encode("utf-8")
        mock_resp.status_code = 200
        mock_requests_get.return_value = mock_resp
        out = io.StringIO()
        call_command("audit_cve_product_links", stdout=out)
        output = out.getvalue()
        self.assertIn("Expected CVE links : 5", output)
        self.assertIn("Already in DB     : 5", output)
        self.assertIn("Missing links     : 0", output)

    @mock.patch("requests.get")
    def test_remove_extra_links(self, mock_requests_get):
        vuln_assocs = self.mock_json["oesis"][1]["vuln_associations"]
        mock_resp = mock.Mock()
        mock_resp.content = json.dumps({"oesis": [self.mock_json["oesis"][0], {"vuln_associations": vuln_assocs}] }).encode("utf-8")
        mock_resp.status_code = 200
        mock_resp.__enter__ = lambda s: s
        mock_resp.__exit__ = mock.Mock()
        mock_requests_get.return_value = mock_resp
        out = io.StringIO()
        call_command("audit_cve_product_links", "--remove-extra", stdout=out)
        out.getvalue()
        # Only the correct links should remain
        self.assertTrue(self.cve1.product_version.filter(id=self.pv_4_0.id).exists())
        self.assertTrue(self.cve1.product_version.filter(id=self.pv_4_0_5.id).exists())
        self.assertTrue(self.cve3.product_version.filter(id=self.pv_1_2.id).exists())
        self.assertFalse(self.cve2.product_version.filter(id=self.pv_4_0_5.id).exists())
        self.assertFalse(self.cve4.product_version.filter(id=self.pv_1_2.id).exists())
        self.assertFalse(self.cve4.product_version.filter(id=self.pv_4_0_5.id).exists())
        self.assertFalse(self.cve1.product_version.filter(id=self.pv_9_9.id).exists())
