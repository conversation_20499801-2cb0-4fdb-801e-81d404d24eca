from admin_auto_filters.filters import AutocompleteFilterFactory
from django.contrib import admin
from django.db.models import Exists, OuterRef, Count

from .models import Product, ProductCategory, ProductVendor, ProductVersion, CVE, InstalledProduct, InstalledProductVersion, ProductSignature, OpswatOperatingSystem


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ("name", "opswat_id", "modified", "created")
    ordering = ("name", "opswat_id", "modified", "created")
    readonly_fields = ("opswat_id",)
    search_fields = ("name", "opswat_id")


@admin.register(ProductVendor)
class ProductVendorAdmin(admin.ModelAdmin):
    list_display = ("name", "opswat_id", "modified", "created")
    ordering = ("opswat_id", "name", "modified", "created")
    readonly_fields = ("opswat_id",)
    search_fields = ("name", "opswat_id")


class ProductVersionInline(admin.TabularInline):
    model = ProductVersion
    extra = 0
    raw_id_fields = ("product",)
    fields = ("raw_version", "major", "minor", "patch", "product", "is_vulnerable")
    readonly_fields = ("raw_version", "major", "minor", "patch", "is_vulnerable")

    def is_vulnerable(self, obj):
        return obj.cves.exists()


class ProductSignatureInline(admin.TabularInline):
    model = ProductSignature
    extra = 0
    fields = ('display_actual_id', 'opswat_id', 'name', 'support_3rd_party_patch')
    readonly_fields = ('display_actual_id', 'opswat_id', 'name', 'support_3rd_party_patch')
    can_delete = False
    max_num = 0

    def display_actual_id(self, obj):
        return obj.id
    display_actual_id.short_description = "ID"


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ("name", "vendor", "opswat_id", "modified", "created")
    ordering = ("name", "vendor", "opswat_id", "modified", "created")
    readonly_fields = ("opswat_id",)
    search_fields = ("name", "vendor__name", "opswat_id")
    list_filter = (AutocompleteFilterFactory("Vendor", "vendor"), "categories")
    raw_id_fields = ("vendor", "categories")
    inlines = (ProductVersionInline, ProductSignatureInline)


class ProductVersionCVEsInline(admin.TabularInline):
    model = CVE.product_version.through
    extra = 0
    raw_id_fields = ("cve",)


@admin.register(ProductVersion)
class ProductVersionAdmin(admin.ModelAdmin):
    list_display = (
        "product_name", "product_vendor_name", "raw_version", "major", "minor", "patch", "is_vulnerable",
        "modified", "created"
    )
    ordering = (
        "product__name", "product__vendor__name", "raw_version", "major", "minor", "patch", "modified",
        "created"
    )
    search_fields = ("product__name", "product__vendor__name", "raw_version")
    list_filter = (
        AutocompleteFilterFactory("Product", "product"),
        AutocompleteFilterFactory("Vendor", "product__vendor")
    )
    raw_id_fields = ("product",)
    inlines = (ProductVersionCVEsInline,)

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            vulnerable=Exists(CVE.objects.filter(product_version=OuterRef("pk")))
        ).select_related("product", "product__vendor")

    def product_name(self, obj):
        return obj.product.name
    product_name.admin_order_field = "product__name"
    product_name.short_description = "Product Name"

    def product_vendor_name(self, obj):
        return obj.product.vendor.name
    product_vendor_name.admin_order_field = "product__vendor__name"
    product_vendor_name.short_description = "Vendor Name"

    def is_vulnerable(self, obj):
        return obj.vulnerable
    is_vulnerable.short_description = "Vulnerable"
    is_vulnerable.boolean = True
    is_vulnerable.admin_order_field = "vulnerable"


class InstalledProductVersionInline(admin.TabularInline):
    model = InstalledProductVersion
    extra = 0
    raw_id_fields = ("product_version",)
    fields = ("product_version", "signature")
    readonly_fields = ("product_version", "signature")

    def get_queryset(self, request):
        """Optimize queryset to prevent N+1 queries"""
        return super().get_queryset(request).select_related(
            'product_version',
            'product_version__product',
            'product_version__product__vendor',
            'signature',
            'signature__product',
            'installed_product',
            'installed_product__app_install',
            'installed_product__app_install__app_user'
        )


@admin.register(InstalledProduct)
class InstalledProductAdmin(admin.ModelAdmin):
    raw_id_fields = ("app_install",)
    fields = ("app_install", "hash", "modified", "created")
    list_display = ("get_app_user_info", "display_product_versions_count", "created", "modified")
    readonly_fields = ("hash", "modified", "created")
    search_fields = ("app_install__serial_number", "app_install__device_id", "app_install__app_user__email", "app_install__app_user__uuid", "hash")
    list_filter = (
        AutocompleteFilterFactory(
            "Partner", "app_install__app_user__organisation__partner"
        ),
        AutocompleteFilterFactory(
            "Organisation", "app_install__app_user__organisation"
        ),
        AutocompleteFilterFactory("App User", "app_install__app_user"),
        AutocompleteFilterFactory("App Install", "app_install"),
        AutocompleteFilterFactory("Product Versions", "product_versions")
    )
    inlines = (InstalledProductVersionInline,)

    def get_app_user_info(self, obj):
        """Display app user email and device information"""
        if obj.app_install and obj.app_install.app_user:
            email = obj.app_install.app_user.email
            device_id = obj.app_install.device_id or "No Device ID"
            return f"{email} (Device: {device_id})"
        return "-"
    get_app_user_info.short_description = "App User & Device"
    get_app_user_info.admin_order_field = "app_install__app_user__email"

    def get_organisation_info(self, obj):
        """Display organisation name"""
        if obj.app_install and obj.app_install.app_user and obj.app_install.app_user.organisation:
            return obj.app_install.app_user.organisation.name
        return "-"
    get_organisation_info.short_description = "Organisation"
    get_organisation_info.admin_order_field = "app_install__app_user__organisation__name"

    def display_product_versions_count(self, obj):
        """Display the count of product versions"""
        count = obj.product_versions_count
        return f"{count} products"
    display_product_versions_count.short_description = "Products Count"
    display_product_versions_count.admin_order_field = "product_versions_count"


    def get_queryset(self, request):
        """Optimize queryset to prefetch related data and annotate with counts"""
        return super().get_queryset(request).select_related(
            'app_install',
            'app_install__app_user',
            'app_install__app_user__organisation'
        ).prefetch_related(
            'product_versions',
            'product_versions__product',
            'product_versions__product__vendor',
            'installed_product_versions__product_version',
            'installed_product_versions__product_version__product',
            'installed_product_versions__product_version__product__vendor',
            'installed_product_versions__signature'
        ).annotate(
            product_versions_count=Count('product_versions', distinct=True)
        )


@admin.register(CVE)
class CVEAdmin(admin.ModelAdmin):
    list_display = ("cve_id", "opswat_id", "severity", "severity_index", "modified", "created")
    ordering = ("cve_id", "opswat_id", "severity", "severity_index", "modified", "created")
    readonly_fields = ("opswat_id",)
    search_fields = ("cve_id", "opswat_id", "severity", "severity_index")
    list_filter = (
        "severity",
        AutocompleteFilterFactory("Product Version", "product_version"),
        "severity_index"
    )
    raw_id_fields = ("product_version",)


@admin.register(InstalledProductVersion)
class InstalledProductVersionAdmin(admin.ModelAdmin):
    list_display = ("get_user_device_info", "product_version", "display_signature", "created", "modified")
    list_filter = (
        AutocompleteFilterFactory(
            "Organisation", "installed_product__app_install__app_user__organisation"
        ),
        AutocompleteFilterFactory(
            "Partner", "installed_product__app_install__app_user__organisation__partner"
        ),
        AutocompleteFilterFactory("Installed Product", "installed_product"),
        AutocompleteFilterFactory("Product Version", "product_version"),
        AutocompleteFilterFactory("Product", "product_version__product"),
        AutocompleteFilterFactory("Vendor", "product_version__product__vendor"),
        AutocompleteFilterFactory("Signature", "signature")
    )
    search_fields = (
        "installed_product__app_install__hostname",
        "installed_product__app_install__device_id",
        "installed_product__app_install__app_user__email",
        "product_version__product__name",
        "product_version__product__vendor__name",
        "product_version__raw_version",
    )

    def get_user_device_info(self, obj):
        """Display user email and device information"""
        if obj.installed_product and obj.installed_product.app_install and obj.installed_product.app_install.app_user:
            email = obj.installed_product.app_install.app_user.email
            device_id = obj.installed_product.app_install.device_id or "No Device ID"
            return f"{email} (Device: {device_id})"
        return "-"
    get_user_device_info.short_description = "User & Device"
    get_user_device_info.admin_order_field = "installed_product__app_install__app_user__email"

    def display_signature(self, obj):
        """Display signature ID and name if available"""
        if obj.signature:
            return f"{obj.signature.id} ({obj.signature.name})"
        return "-"
    display_signature.short_description = "Signature"
    raw_id_fields = ("installed_product", "product_version", "signature")
    readonly_fields = ("created", "modified")

    def get_queryset(self, request):
        """Optimize queryset to prefetch related data"""
        return super().get_queryset(request).select_related(
            'installed_product__app_install__app_user',
            'product_version__product__vendor',
            'signature'
        )


@admin.register(ProductSignature)
class ProductSignatureAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'opswat_id', 'product', 'support_3rd_party_patch')
    search_fields = ('id', 'name', 'opswat_id', 'product__name', 'product__vendor__name')
    list_filter = (AutocompleteFilterFactory("Product", "product"), 'support_3rd_party_patch')
    raw_id_fields = ('product',)
    readonly_fields = ('opswat_id', 'created', 'modified')


@admin.register(OpswatOperatingSystem)
class OpswatOperatingSystemAdmin(admin.ModelAdmin):
    list_display = ('app_install', 'os_id', 'os_type', 'architecture', 'created', 'modified')
    search_fields = (
        'app_install__device_id',
        'app_install__serial_number',
        'app_install__hostname',
        'app_install__app_user__email',
        'os_id'
    )
    list_filter = (
        'os_type',
        'architecture',
        'created',
        AutocompleteFilterFactory("App Install", "app_install"),
        AutocompleteFilterFactory("Organisation", "app_install__app_user__organisation")
    )
    raw_id_fields = ('app_install',)
    readonly_fields = ('created', 'modified')
