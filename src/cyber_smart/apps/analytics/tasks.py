import uuid

from celery import shared_task as task
from celery.utils.log import get_task_logger
from django.contrib.auth import get_user_model
from django.db import DatabaseError
from django.db.models import Count, Sum, Case, When
from django.dispatch import receiver
from django.forms.models import model_to_dict
from django.utils import timezone

from appusers.models import (
    AppUser, AppReport, AppInstall, CheckManualFix, CheckAutoFix
)
from billing.providers.chargebee.plans import REGULAR_IDS, PRO_IDS
from beta_features.utils import is_any_beta_feature_enabled
from common.signals import model_crud
from common.utils import median_value
from distributors.models import Distributor
from opswat.utils import get_installed_software_status
from organisations.models import Organisation, OrganisationCertification
from partners.models import Partner, PartnerUser
from rulebook.models import (
    IssuedCertification, CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS, IASME_GOVERNANCE, GDPR, CYBERSMART_COMPLETE,
)
from .models import (
    PartnerAnalytics, DistributorAnalytics, OrganisationAnalytics,
    AppUserAnalytics, AppInstallAnalytics, ManagementAnalytics
)
from .utils import update_app_report_calculated_fields

logger = get_task_logger(__name__)
User = get_user_model()


@task
def calculate_organisation_analytics(organisation_pk: int, skip_partner_analytics: bool = False) -> None:
    """
    Calculates organisation analytics.
    :param organisation_pk: organisation primary key
    :type organisation_pk: int
    :param skip_partner_analytics: if we want to skip the update for partner analytics as part of this
    :type skip_partner_analytics: bool
    :return: nothing
    :rtype: None
    """
    try:
        organisation = Organisation.objects.get(pk=organisation_pk)
    except Organisation.DoesNotExist:
        logger.error(f"No Organisation Found ID: [{organisation_pk}]")
        return

    agreed, read, not_agreed = organisation.get_policies_percentage()

    software_status = get_installed_software_status(organisation.pk, "organisation")
    total_packages = software_status["total"]
    vulnerable_packages = software_status["vulnerable"]
    safe_packages = software_status["safe"]

    organisation_pass_percentage = organisation.get_pass_percentage()
    data = {
        "pass_percentage": organisation_pass_percentage,
        "certifications_pass_percentage": organisation.get_certification_pass_percentage(),
        "cyber_essentials_pass_percentage": organisation.get_certification_pass_percentage(
            certification_type=CYBER_ESSENTIALS
        ),
        "cyber_essentials_plus_pass_percentage": organisation.get_certification_pass_percentage(
            certification_type=CYBER_ESSENTIALS_PLUS
        ),
        "iasme_pass_percentage": organisation.get_certification_pass_percentage(
            certification_type=IASME_GOVERNANCE
        ),
        "gdpr_pass_percentage": organisation.get_certification_pass_percentage(
            certification_type=GDPR
        ),
        "secure_percentage": organisation_pass_percentage if organisation.installed_devices_count else None,
        "insecure_percentage": (
            100 - organisation_pass_percentage
        ) if organisation.installed_devices_count else None,
        "policies_agreed_percentage": agreed,
        "policies_read_percentage": read,
        "policies_not_agreed_percentage": not_agreed,
        "total_packages_count": total_packages,
        "vulnerable_packages_count": vulnerable_packages,
        "safe_packages_count": safe_packages,
    }
    _is_any_beta_feature_enabled = is_any_beta_feature_enabled()

    devices_fields = [
        "installed_devices_count", "secure_devices_count", "insecure_devices_count", "vulnerable_devices_count",
        "device_owners_count", "failed_checks_count", "active_devices_30d_count",
        "mobile_devices_count", "macos_devices_count",
        "windows_devices_count",
    ]
    if _is_any_beta_feature_enabled:
        devices_fields.extend([
            "installed_beta_apps_count",
            "beta_secure_devices_count", "beta_active_devices_30d_count", "beta_mobile_devices_count",
            "beta_macos_devices_count", "beta_windows_devices_count"
        ])

    users_fields = [
        "total_users_count", "installed_users_count", "secure_users_count",
        "insecure_users_count", "academy_not_completed_users_count",
        "policies_pending_users_count", "not_installed_users_count",
        "active_users_30d_count",
    ]
    if _is_any_beta_feature_enabled:
        users_fields.extend([
            "installed_beta_users_count", "total_beta_users_count",
            "secure_beta_users_count", "insecure_beta_users_count"
        ])


    beta_deductions = {}
    if _is_any_beta_feature_enabled:
        # fields that need to be deducted from beta apps analytics
        beta_deductions = {
            "installed_devices_count": "installed_beta_apps_count",
            "secure_devices_count": "beta_secure_devices_count",
            "insecure_devices_count": "beta_insecure_devices_count",
            "active_devices_30d_count": "beta_active_devices_30d_count",
            "macos_devices_count": "beta_macos_devices_count",
            "windows_devices_count": "beta_windows_devices_count",
            "mobile_devices_count": "beta_mobile_devices_count",
            "total_users_count": "total_beta_users_count",
            "installed_users_count": "installed_beta_users_count",
            "secure_users_count": "secure_beta_users_count",
            "insecure_users_count": "insecure_beta_users_count"
        }

    for field in devices_fields + users_fields:
        if field in beta_deductions:
            data[field] = getattr(organisation, field) - getattr(organisation, beta_deductions[field])
            continue
        data[field] = getattr(organisation, field)

    analytic = OrganisationAnalytics.objects.get_or_create(organisation=organisation)[0]
    # check if the data has changed to reduce excess and cascading queries
    # get existing data as a dictionary
    saved_data = model_to_dict(analytic)
    # preview what the final data will look like after an update (copy data items to saved_data)
    merged_data = saved_data | data
    # if there's no change, exit
    if saved_data == merged_data:
        return

    analytic.__dict__.update(data)
    analytic.save()
    logger.info(f"Calculated Organisation Analytics ID [{analytic.pk}]")

    # update partner analytics
    if not skip_partner_analytics:
        update_partner_analytics.delay(organisation.partner.pk)


def set_passing_and_failing_dates(instance, field_name, pass_percentage):
    """
    Sets date_started_passing and date_started_failing for passed model instance.
    Instance type can be either AppInstallAnalytics or AppUserAnalytics.
    Returns True if instance has changed otherwise returns False
    :param instance: analytic model instance
    :type instance: AppInstallAnalytics or AppUserAnalytics
    :param field_name: instance field name to which overall pass percentage will be set
    :type field_name: str
    :param pass_percentage: instance pass percentage
    :type pass_percentage: float
    :return: True or False
    :rtype: bool
    """
    saved_instance = model_to_dict(instance)
    setattr(instance, field_name, pass_percentage)
    # check if instance has changed
    if (model_to_dict(instance) != saved_instance):
        if pass_percentage == 100:
            if not getattr(instance, "date_started_passing"):
                # set started passing date
                setattr(instance, "date_started_passing", timezone.now())
            if getattr(instance, "date_started_failing"):
                # remove failing date if an app is secure
                setattr(instance, "date_started_failing", None)
        else:
            if not getattr(instance, "date_started_failing"):
                # set started failing date
                setattr(instance, "date_started_failing", timezone.now())
            if getattr(instance, "date_started_passing"):
                # remove passing date if an app is not secure
                setattr(instance, "date_started_passing", None)
        return True, instance
    else:
        return False, instance


@task
def calculate_app_user_analytics(app_users_pk: list[int]) -> None:
    """
    Calculate app user analytics.
    :param app_users_pk: list of app user primary key
    :type app_users_pk: list of ints
    """
    app_users = AppUser.objects.filter(pk__in=app_users_pk)
    for index, app_user in enumerate(app_users):
        analytic = AppUserAnalytics.objects.get_or_create(appuser=app_user)[0]
        saved_analytics = model_to_dict(analytic)

        analytic.active_policies_count = app_user.active_policies_count
        analytic.agreed_policies_count = app_user.agreed_policies_count
        analytic.read_policies_count = app_user.read_policies_count
        analytic.failed_checks_count = app_user.failed_checks_count

        # todo: academy numbers

        has_updated_analytics = (model_to_dict(analytic) != saved_analytics)

        has_updated_dates, analytic = set_passing_and_failing_dates(
            instance=analytic,
            field_name="pass_percentage",
            pass_percentage=app_user.pass_percentage
        )
        # call only if analytics for any app user has been updated
        if has_updated_analytics or has_updated_dates:
            analytic.save()
            logger.info(f"Calculated AppUser Analytics ID: [{analytic.pk}]")
            # calculate organisation analytics
            calculate_organisation_analytics.delay(app_users[0].organisation.pk)


@task
def calculate_app_install_analytics(app_install_pk):
    """
    Calculates app install analytics.
    :param app_install_pk: app install primary key
    :type app_install_pk: int
    :return: nothing
    :rtype: None
    """
    app_install = AppInstall.objects.filter(pk=app_install_pk).first()
    if not app_install:
        logger.error(f"No AppInstall Found ID: [{app_install_pk}]")
    else:
        analytic = AppInstallAnalytics.objects.get_or_create(app_install=app_install)[0]

        software_status = get_installed_software_status(app_install.pk, "app_install")
        new_data = {
            "installed_software_count": software_status['total'],
            "vulnerable_software_count": software_status['vulnerable'],
            "safe_software_count": software_status['safe'],
        }

        analytic_updated = analytic.update_if_has_new_data(new_data, only_set_attrs=True)

        has_updated_dates, analytic = set_passing_and_failing_dates(
            instance=analytic,
            field_name="latest_pass_percentage",
            pass_percentage=app_install.get_pass_percentage
        )

        # in case the user has many devices, this can be a new device or a new check-in with an existing device
        multiple_devices = False
        # skip if user is main app user in case of bulk
        is_bulk_app_user = all([
            app_install.app_user.organisation.bulk_install,
            app_install.app_user == app_install.app_user.organisation.main_app_user
        ])
        if not is_bulk_app_user and app_install.app_user.active_installs.count() > 1:
            multiple_devices = True

        if analytic_updated or has_updated_dates:
            analytic.save()
            logger.info(f"Calculated AppInstall Analytics ID: [{analytic.pk}]")
            # calculate app user analytics
            calculate_app_user_analytics.delay(app_users_pk=[app_install.app_user.pk])
        elif multiple_devices:
            # calculate app user analytics, and update query will only happen if there is a change
            calculate_app_user_analytics.delay(app_users_pk=[app_install.app_user.pk])


@task
def calculate_app_report(device_report_pk, report_archive=None, checks_created=True):
    """
    Calculates app install report numbers and saves them.
    Then calculates app install analytics based on saved numbers.
    :param device_report_pk: report primary key
    :type device_report_pk: int
    :param checks_created: if AppChecks were created that came from the report
    :type checks_created: bool
    :return: nothing
    :rtype: None
    """
    try:
        device_report = AppReport.objects.get(pk=device_report_pk)
    except AppReport.DoesNotExist:
        logger.error(f"No AppReport Found ID: [{device_report_pk}]")
    else:
        if checks_created:
            update_app_report_calculated_fields(device_report, logger, report_archive)
            calculate_app_install_analytics.delay(device_report.app_install.pk)


@task
def delete_old_app_reports():
    # cascade deletes older app reports apart from the latest
    latest = AppReport.objects.all().order_by('app_install_id', '-id').values_list('pk').distinct('app_install_id')
    old_reports = AppReport.objects.exclude(pk__in=latest)
    old_reports.delete()


def _handle_manual_or_auto_fix_crud(instance, **kwargs):
    """
    Helper function to handle both manual and auto fix CRUD operations.
    Updates responses for which the fix was intended.

    :param instance: Fix instance (CheckManualFix or CheckAutoFix)
    :return: None
    """
    if not (kwargs['post_create'] or kwargs['post_update'] or kwargs['post_delete']):
        return

    last_report = instance.app_install.get_latest_report
    if not last_report:
        return

    app_check_filter = {}
    if instance.app_check:
        app_check_filter['app_check'] = instance.app_check

    query = last_report.check_results.filter(
        response=False,
        app_check__active=True,
        **app_check_filter
    )

    for response in query:
        try:
            if response.fixed != response.is_fixed():
                response.fixed = response.is_fixed()
                response.save(update_fields=['fixed', 'modified'])
        except DatabaseError:
            # due to race conditions, the response might have been already updated and would raise an exception,
            # so we skip it
            pass

    instance.app_install.save()
    calculate_app_report.delay(last_report.pk)

@receiver(model_crud, sender=CheckManualFix, dispatch_uid=uuid.uuid4().hex)
def manual_fix_crud(sender, instance, **kwargs):
    """
    Detects creating, updating, deleting for CheckManualFix.
    Updates a response for which CheckManualFix intended as fixed/unfixed.
    """
    _handle_manual_or_auto_fix_crud(instance, **kwargs)

@receiver(model_crud, sender=CheckAutoFix, dispatch_uid=uuid.uuid4().hex)
def auto_fix_crud(sender, instance, **kwargs):
    """
    Detects creating, updating, deleting for CheckAutoFix.
    Updates a response for which CheckAutoFix intended as fixed/unfixed.
    """
    _handle_manual_or_auto_fix_crud(instance, **kwargs)


@task
def update_partner_analytics(partner_pk, skip_distributor_analytics=False):
    """
    Updates partner analytics.
    """
    try:
        partner = Partner.objects.get(pk=partner_pk)
    except Partner.DoesNotExist:
        pass
    else:
        analytics = PartnerAnalytics.objects.get_or_create(partner=partner)[0]

        # get existing data as a dictionary
        saved_analytics = model_to_dict(analytics)

        analytics.issued_certificates_count = partner.issued_certificates_count

        _is_any_beta_feature_enabled = is_any_beta_feature_enabled()
        organisations_fields = [
            "organisations_count", "secure_organisations_count", "insecure_organisations_count",
            "average_smart_score_percentage", "certified_organisations_count", "expiring_soon_organisations_count",
            "average_smart_score_percentage"
        ]
        if _is_any_beta_feature_enabled:
            organisations_fields.extend([
                "beta_organisations_count", "secure_beta_organisations_count"
            ])

        devices_fields = [
            "installed_devices_count", "secure_devices_count", "insecure_devices_count", "vulnerable_devices_count",
            "device_owners_count", "failed_checks_count", "active_devices_30d_count",
            "mobile_devices_count", "macos_devices_count",
            "windows_devices_count", "installed_beta_apps_count",
        ]
        if _is_any_beta_feature_enabled:
            devices_fields.extend([
                "installed_beta_apps_count", "beta_secure_devices_count", "beta_active_devices_30d_count",
                "beta_mobile_devices_count", "beta_macos_devices_count", "beta_windows_devices_count"
            ])

        certifications_fields = [
            {
                "analytic_fields": [
                    "certifications_count",
                    "certifications_ce_count",
                    "certifications_ce_plus_count",
                    "certifications_gdpr_count",
                    "certifications_csc_count",
                ],
                "partner_method": "get_certifications_count",
                "method_args": {"cert_type": [
                    None, CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS, GDPR, CYBERSMART_COMPLETE
                ], "status": None}
            },
            {
                "analytic_fields": [
                    "certifications_certified_count",
                    "certifications_certified_ce_count",
                    "certifications_certified_ce_plus_count",
                    "certifications_certified_gdpr_count",
                    "certifications_certified_csc_count",
                ],
                "partner_method": "get_certifications_count",
                "method_args": {"cert_type": [
                    None, CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS, GDPR, CYBERSMART_COMPLETE
                ], "status": OrganisationCertification.CERTIFIED_STATUSES}
            },
            {
                "analytic_fields": [
                    "certifications_in_progress_count",
                    "certifications_in_progress_ce_count",
                    "certifications_in_progress_ce_plus_count",
                    "certifications_in_progress_gdpr_count",
                    "certifications_in_progress_csc_count",
                ],
                "partner_method": "get_certifications_count",
                "method_args": {"cert_type": [
                    None, CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS, GDPR, CYBERSMART_COMPLETE
                ], "status": OrganisationCertification.IN_PROGRESS_STATUSES}
            },
            {
                "analytic_fields": [
                    "certifications_ready_for_assessment_count",
                    "certifications_ready_for_assessment_ce_count",
                    "certifications_ready_for_assessment_ce_plus_count",
                    "certifications_ready_for_assessment_gdpr_count",
                    "certifications_ready_for_assessment_csc_count",
                ],
                "partner_method": "get_certifications_count",
                "method_args": {"cert_type": [
                    None, CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS, GDPR, CYBERSMART_COMPLETE
                ], "status": OrganisationCertification.READY_FOR_ASSESSMENT_STATUSES}
            }
        ]

        # fields that need to be deducted from beta apps analytics
        beta_deductions = {}
        if  _is_any_beta_feature_enabled:
            beta_deductions = {
                "installed_devices_count": "installed_beta_apps_count",
                "secure_devices_count": "beta_secure_devices_count",
                "insecure_devices_count": "beta_insecure_devices_count",
                "active_devices_30d_count": "beta_active_devices_30d_count",
                "macos_devices_count": "beta_macos_devices_count",
                "windows_devices_count": "beta_windows_devices_count",
                "mobile_devices_count": "beta_mobile_devices_count",
                "organisations_count": "beta_organisations_count",
                "secure_organisations_count": "secure_beta_organisations_count"
            }

        # sets a partner property return value to the analytics model field with the same name
        # example: analytics.installed_devices_count = partner.installed_devices_count
        for field in devices_fields + organisations_fields + certifications_fields:
            if isinstance(field, str):
                if field in beta_deductions:
                    value = getattr(partner, field) - getattr(partner, beta_deductions[field])
                else:
                    value = getattr(partner, field)
                setattr(analytics, field, value)
            else:
                # for certification fields
                # example:
                # analytics.certifications_count = partner.get_certifications_count(
                #   cert_type=None, status=None
                # )
                # analytics.certifications_certified_ce_count = partner.get_certifications_count(
                #   cert_type=CYBER_ESSENTIALS, status=[OrganisationCertification.CERTIFIED]
                # )
                for index, analytic_field in enumerate(field["analytic_fields"]):
                    # {"cert_type": [None, CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS, GDPR]}
                    # become {"cert_type": CYBER_ESSENTIALS} or any other type of certification
                    args = field["method_args"].copy()
                    args["cert_type"] = args["cert_type"][index]
                    # set method execution result to the analytics model field
                    setattr(
                        analytics, analytic_field, getattr(partner, field["partner_method"])(**args)
                    )

        has_updated_analytics = (model_to_dict(analytics) != saved_analytics)

        if has_updated_analytics:
            analytics.save()
            if not skip_distributor_analytics:
                # update analytics for partner's distributor
                update_distributor_analytics.delay(partner.distributor.pk)


@task
def update_distributor_analytics(distributor_pk):
    try:
        distributor = Distributor.objects.get(pk=distributor_pk)
    except Distributor.DoesNotExist:
        return
    analytics, created = DistributorAnalytics.objects.get_or_create(distributor=distributor)
    # get existing data as a dictionary
    saved_analytics = model_to_dict(analytics)
    installs_without_deprecated_duplicates = (
        AppInstall.objects.installs_without_deprecated_duplicates_for_distributor(
            distributor
        ).values_list("pk", flat=True)
    )
    active_installs_filter = Count(
        Case(
            When(
                app_users__active=True,
                app_users__installs__inactive=False,
                app_users__organisation__is_test=False,
                app_users__installs__in=installs_without_deprecated_duplicates,
                then=1,
            )
        )
    )
    organisations = Organisation.objects.filter(
        partner__distributor=distributor
    ).annotate(active_installs=active_installs_filter)

    # total
    analytics.total_devices_count = organisations.aggregate(
        total=Sum('active_installs')
    )['total'] or 0
    analytics.total_issued_certifications_count = organisations.aggregate(
        total=Count('certifications__issued_certification')
    )['total'] or 0

    # test organisations
    analytics.test_organisations_count = organisations.filter(is_test=True).count()

    # trial organisations
    analytics.trial_organisations_count = organisations.filter(partner__trial_account=True).count()

    # annual organisations
    organisations_annual = organisations.filter(
        pricing_band__in=[Organisation.PRICING_ANNUAL, Organisation.PRICING_ANNUAL_V2]
    ).annotate(active_installs=active_installs_filter)
    analytics.annual_count = organisations_annual.count()
    analytics.annual_devices_count = organisations_annual.aggregate(
        total=Sum('active_installs')
    )['total'] or 0
    analytics.annual_issued_certifications_count = organisations_annual.aggregate(
        total=Count('certifications__issued_certification')
    )['total'] or 0

    # monthly organisations
    organisations_monthly = organisations.filter(
        pricing_band__in=[Organisation.PRICING_MONTHLY, Organisation.PRICING_MONTHLY_V4]
    ).annotate(active_installs=active_installs_filter)
    analytics.monthly_count = organisations_monthly.count()
    analytics.monthly_devices_count = organisations_monthly.aggregate(
        total=Sum('active_installs')
    )['total'] or 0
    analytics.monthly_issued_certifications_count = organisations_monthly.aggregate(
        total=Count('certifications__issued_certification')
    )['total'] or 0

    organisations_fields = [
        "organisations_count", "secure_organisations_count", "insecure_organisations_count",
        "average_smart_score_percentage", "certified_organisations_count", "expiring_soon_organisations_count"
    ]

    # sets a distributor property return value to the analytics model field with the same name
    # example: analytics.secure_organisations_count = distributor.secure_organisations_count
    for field in organisations_fields:
        if isinstance(field, str):
            setattr(analytics, field, getattr(distributor, field))

    has_updated_analytics = (model_to_dict(analytics) != saved_analytics)

    if has_updated_analytics:
        analytics.save()


def update_management_analytics():
    # todo: not used currently
    if ManagementAnalytics.objects.exists():
        analytics = ManagementAnalytics.objects.all().first()
        # created = False
    else:
        analytics = ManagementAnalytics.objects.create()
        # created = True
    #  update analytics not more than once per minute
    # if created or (analytics.date_modified + timedelta(minutes=1)) < timezone.now():
    if True:

        # organisations
        organisations = Organisation.objects.filter(is_test=False)
        analytics.organisations.set(organisations, clear=True)
        analytics.organisations_count = organisations.count()

        # test organisations
        test_organisations = Organisation.objects.filter(is_test=True)
        analytics.test_organisations.set(test_organisations, clear=True)
        analytics.test_organisations_count = test_organisations.count()

        # individual enrollment organisations
        individual_enrollment_organisations = Organisation.objects.filter(bulk_install=False, is_test=False)
        analytics.individual_enrollment_organisations.set(individual_enrollment_organisations, clear=True)
        analytics.individual_enrollment_organisations_count = individual_enrollment_organisations.count()

        # bulk enrollment organisations
        bulk_enrollment_organisations = Organisation.objects.filter(bulk_install=True, is_test=False)
        analytics.bulk_enrollment_organisations.set(bulk_enrollment_organisations, clear=True)
        analytics.bulk_enrollment_organisations_count = bulk_enrollment_organisations.count()

        # iasme organisations
        iasme_organisations = Organisation.objects.filter(
            is_test=False,
            pk__in=[p.organisations.all()[0].pk for p in Partner.objects.filter(
                iasme_cb=True
            ) if p.organisations.all().count()]
        )
        analytics.iasme_organisations.set(iasme_organisations, clear=True)
        analytics.iasme_organisations_count = iasme_organisations.count()

        # iasme clients organisations
        iasme_clients_organisations = Organisation.objects.filter(
            is_test=False,
            partner__iasme_cb=True
        ).exclude(pk__in=iasme_organisations)
        analytics.iasme_clients_organisations.set(iasme_clients_organisations, clear=True)
        analytics.iasme_clients_organisations_count = iasme_clients_organisations.count()

        # chargebee organisations
        chargebee_organisations = Organisation.objects.filter(
            is_test=False,
            customer__subscriptions__isnull=False
        ).distinct()
        analytics.chargebee_organisations.set(chargebee_organisations, clear=True)
        analytics.chargebee_organisations_count = chargebee_organisations.count()

        # chargebee basic organisations
        chargebee_basic_organisations = Organisation.objects.filter(
            is_test=False,
            customer__subscriptions__addon_id__in=REGULAR_IDS
        ).distinct()
        analytics.chargebee_basic_organisations.set(chargebee_basic_organisations, clear=True)
        analytics.chargebee_basic_organisations_count = chargebee_basic_organisations.count()

        # chargebee pro organisations
        chargebee_pro_organisations = Organisation.objects.filter(
            is_test=False,
            customer__subscriptions__addon_id__in=PRO_IDS
        ).distinct()
        analytics.chargebee_pro_organisations.set(chargebee_pro_organisations, clear=True)
        analytics.chargebee_pro_organisations_count = chargebee_pro_organisations.count()

        # issued organisations
        issued_organisations = Organisation.objects.filter(
            is_test=False,
            certifications__issued_certification__isnull=False
        )
        analytics.issued_organisations.set(issued_organisations, clear=True)
        analytics.issued_organisations_count = issued_organisations.count()

        # issued certifications
        issued_certifications = IssuedCertification.objects.filter(
            certificate__organisation__is_test=False
        )
        analytics.issued_certifications.set(issued_certifications, clear=True)
        analytics.issued_certifications_count = issued_certifications.count()

        # issued cyber essentials certifications from cybersmart
        cs_cyber_essentials_certifications = IssuedCertification.objects.filter(
            certificate__organisation__is_test=False,
            custom_cert_sent=True,
            certificate__version__type__type__in=[CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS]
        )
        analytics.cs_cyber_essentials_certifications.set(cs_cyber_essentials_certifications, clear=True)
        analytics.cs_cyber_essentials_certifications_count = cs_cyber_essentials_certifications.count()

        # issued gdpr certifications from cybersmart
        cs_gdpr_certifications = IssuedCertification.objects.filter(
            certificate__organisation__is_test=False,
            custom_cert_sent=True,
            certificate__version__type__type__in=[IASME_GOVERNANCE, GDPR]
        )
        analytics.cs_gdpr_certifications.set(cs_gdpr_certifications, clear=True)
        analytics.cs_gdpr_certifications_count = cs_gdpr_certifications.count()

        # issued cyber essentials certifications from pervade api
        api_cyber_essentials_certifications = IssuedCertification.objects.filter(
            certificate__organisation__is_test=False,
            custom_cert_sent=False,
            certificate__version__type__type__in=[CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS]
        )
        analytics.api_cyber_essentials_certifications.set(api_cyber_essentials_certifications, clear=True)
        analytics.api_cyber_essentials_certifications_count = api_cyber_essentials_certifications.count()

        # issued gdpr certifications from pervade api
        api_gdpr_certifications = IssuedCertification.objects.filter(
            certificate__organisation__is_test=False,
            custom_cert_sent=False,
            certificate__version__type__type__in=[IASME_GOVERNANCE, GDPR]
        )
        analytics.api_gdpr_certifications.set(api_gdpr_certifications, clear=True)
        analytics.api_gdpr_certifications_count = api_gdpr_certifications.count()

        # partners
        partners = Partner.objects.all()
        analytics.partners.set(partners, clear=True)
        analytics.partners_count = partners.count()

        # partner users
        partner_users = PartnerUser.objects.all()
        analytics.partner_users.set(partner_users, clear=True)
        analytics.partner_users_count = partner_users.count()

        # partner organisations
        partner_organisations = Organisation.objects.filter(
            is_test=False,
            partner__isnull=False
        )
        analytics.partner_organisations.set(partner_organisations, clear=True)
        analytics.partner_organisations_count = partner_organisations.count()

        # iasme partners
        iasme_partners = Partner.objects.filter(iasme_cb=True)
        analytics.iasme_partners.set(iasme_partners, clear=True)
        analytics.iasme_partners_count = iasme_partners.count()

        # partners with pervade token
        partners_with_pervade_token = Partner.objects.filter(
            users__pervade_token__isnull=False, iasme_cb=True
        ).distinct('pk')
        analytics.partners_with_pervade_token.set(partners_with_pervade_token, clear=True)
        analytics.partners_with_pervade_token_count = partners_with_pervade_token.count()

        # app installs
        app_installs = AppInstall.objects.active().installs_without_deprecated_duplicates().filter(
            app_user__organisation__is_test=False,
        )
        analytics.app_installs.set(app_installs, clear=True)
        analytics.app_installs_count = app_installs.count()

        # inactive app installs
        app_installs_inactive = AppInstall.objects.installs_without_deprecated_duplicates().filter(
            app_user__organisation__is_test=False, inactive=True
        )
        analytics.app_installs_inactive.set(app_installs_inactive, clear=True)
        analytics.app_installs_inactive_count = app_installs_inactive.count()

        # app installs with no reports
        app_installs_no_reports = AppInstall.objects.active().installs_without_deprecated_duplicates().filter(
            app_user__organisation__is_test=False,
            reports__isnull=True,
        )
        analytics.app_installs_no_reports.set(app_installs_no_reports, clear=True)
        analytics.app_installs_no_reports_count = app_installs_no_reports.count()

        # app installs average and median counters
        analytics.app_installs_average_count = app_installs.count() / organisations.count()
        analytics.app_installs_median_count = median_value(
            list(
                Organisation.objects.all().annotate(
                    installs=Count(
                        Case(
                            When(
                                app_users__active=True,
                                app_users__installs__inactive=False,
                                then=1
                            )
                        )
                    )
                ).exclude(
                    is_test=True
                ).exclude(
                    installs=0
                ).values_list(
                    'installs', flat=True
                )
            )
        )

        # app users
        app_users = AppUser.objects.filter(organisation__is_test=False)
        analytics.app_users.set(app_users, clear=True)
        analytics.app_users_count = app_users.count()

        # enrolled app users
        app_users_enrolled = AppUser.objects.filter(organisation__is_test=False)
        analytics.app_users_enrolled.set(app_users_enrolled, clear=True)
        analytics.app_users_enrolled_count = app_users_enrolled.count()

        # active app users
        app_users_active = AppUser.objects.filter(organisation__is_test=False, active=True)
        analytics.app_users_active.set(app_users_active, clear=True)
        analytics.app_users_active_count = app_users_active.count()

        # stripe users
        stripe_users = User.objects.filter(
            user_stripe__isnull=False
        ).order_by('user_stripe').distinct('user_stripe')
        analytics.stripe_users.set(stripe_users, clear=True)
        analytics.stripe_users_count = stripe_users.count()

        # percentages
        certifications_pass_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            certifications_pass_percentage__isnull=False
        ).values_list('certifications_pass_percentage', flat=True))
        analytics.certifications_pass_percentage = sum(
            certifications_pass_percentage_list
        ) / len(
            certifications_pass_percentage_list
        )

        cyber_essentials_pass_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            cyber_essentials_pass_percentage__isnull=False
        ).values_list('cyber_essentials_pass_percentage', flat=True))
        analytics.cyber_essentials_pass_percentage = sum(
            cyber_essentials_pass_percentage_list
        ) / len(
            cyber_essentials_pass_percentage_list
        )

        cyber_essentials_plus_pass_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            cyber_essentials_plus_pass_percentage__isnull=False
        ).values_list('cyber_essentials_plus_pass_percentage', flat=True))
        analytics.cyber_essentials_plus_pass_percentage = sum(
            cyber_essentials_plus_pass_percentage_list
        ) / len(
            cyber_essentials_plus_pass_percentage_list
        )

        iasme_pass_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            iasme_pass_percentage__isnull=False
        ).values_list('iasme_pass_percentage', flat=True))
        analytics.iasme_pass_percentage = sum(
            iasme_pass_percentage_list
        ) / len(
            iasme_pass_percentage_list
        )

        gdpr_pass_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            gdpr_pass_percentage__isnull=False
        ).values_list('gdpr_pass_percentage', flat=True))
        analytics.gdpr_pass_percentage = sum(
            gdpr_pass_percentage_list
        ) / len(
            gdpr_pass_percentage_list
        )

        secure_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            secure_percentage__isnull=False
        ).values_list('secure_percentage', flat=True))
        analytics.secure_percentage = sum(
            secure_percentage_list
        ) / len(
            secure_percentage_list
        )

        insecure_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            insecure_percentage__isnull=False
        ).values_list('insecure_percentage', flat=True))
        analytics.insecure_percentage = sum(
            insecure_percentage_list
        ) / len(
            insecure_percentage_list
        )

        policies_agreed_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            policies_agreed_percentage__isnull=False
        ).values_list('policies_agreed_percentage', flat=True))
        analytics.policies_agreed_percentage = sum(
            policies_agreed_percentage_list
        ) / len(
            policies_agreed_percentage_list
        )

        policies_read_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            policies_read_percentage__isnull=False
        ).values_list('policies_read_percentage', flat=True))
        analytics.policies_read_percentage = sum(
            policies_read_percentage_list
        ) / len(
            policies_read_percentage_list
        )

        policies_not_agreed_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            policies_not_agreed_percentage__isnull=False
        ).values_list('policies_not_agreed_percentage', flat=True))
        analytics.policies_not_agreed_percentage = sum(
            policies_not_agreed_percentage_list
        ) / len(
            policies_not_agreed_percentage_list
        )

        # save
        analytics.save()


@task
def record_report_download_usage(entity_pk: int, level: str, report_name: str, file_format: str) -> None:
    """
    Record report (CSV, XLSX) download usage for the given entity.
    :param entity_pk: The primary key of the entity to record the download for.
    :param level: The level of the entity. It can be either `organisation`, `partner` or `distributor`.
    :param report_name: The name of the report.
    :param file_format: The report file format of the download. It can be either `csv` or `xlsx`.
    """
    if level == "organisation":
        entity = Organisation.objects.get(pk=entity_pk)
    elif level == "partner":
        entity = Partner.objects.get(pk=entity_pk)
    elif level == "distributor":
        entity = Distributor.objects.get(pk=entity_pk)
    else:
        logger.error(f"Invalid level `{level}` provided for report download usage recording.")
        return

    # valid file formats are `csv` and `xlsx`
    if file_format not in ["csv", "xlsx"]:
        logger.error(f"Invalid file format `{file_format}` provided for report download usage recording.")
        return

    analytic = getattr(entity, "analytics", None)
    if not analytic:
        logger.error(f"No analytic found for entity `{entity}` for report download usage recording.")
        return

    analytic.add_report_download_usage(report_name, file_format)
