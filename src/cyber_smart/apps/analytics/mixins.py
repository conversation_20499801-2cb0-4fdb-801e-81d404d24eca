from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models

from beta_features.mixins import BetaDevicesMetricsMixin, BetaOrganisationsMetricsMixin, BetaUsersMetricsMixin


class ReportDownloadUsageMixin(models.Model):
    """
    Mixin to add report download usage to a model
    """
    report_download_usage = models.JSONField(
        verbose_name="Report Download Usage", default=dict,
        help_text="Stores the number of times a report has been downloaded in a given format"
    )

    class Meta:
        abstract = True

    def add_report_download_usage(self, report_name: str, file_format: str) -> None:
        """
        Adds a report download usage to the report_download_usage field
        """
        if report_name not in self.report_download_usage:
            self.report_download_usage[report_name] = {}
        if file_format not in self.report_download_usage[report_name]:
            self.report_download_usage[report_name][file_format] = 0
        self.report_download_usage[report_name][file_format] += 1
        self.save(update_fields=['report_download_usage'])

    def get_report_download_usage(self, report_name: str, file_format: str) -> int:
        """
        Returns the number of times a report has been downloaded in a given format
        """
        return self.report_download_usage.get(report_name, {}).get(file_format, 0)

    def get_report_download_usage_total(self, report_name: str) -> int:
        """
        Returns the total number of times a report has been downloaded
        """
        return sum(self.report_download_usage.get(report_name, {}).values())


class DevicesMetricsMixin(BetaDevicesMetricsMixin):
    """
    A mixin model intended to store metrics related to devices.
    """
    installed_devices_count = models.IntegerField(verbose_name="Total devices count", default=0)
    secure_devices_count = models.IntegerField(
        verbose_name="Secure devices count", help_text="Devices that have passed all checks", default=0
    )
    insecure_devices_count = models.IntegerField(
        verbose_name="Insecure devices count", help_text="Devices that haven't passed all checks", default=0
    )
    vulnerable_devices_count = models.IntegerField(
        verbose_name="Vulnerable devices count", help_text="Devices that have vulnerabilities detected", default=0
    )
    device_owners_count = models.IntegerField(
        verbose_name="Device owners count", help_text="App users that have at least one app install", default=0
    )
    failed_checks_count = models.IntegerField(
        verbose_name="Failed checks count", help_text="Failed checks of all devices under partner", default=0
    )
    active_devices_24h_count = models.IntegerField(
        verbose_name="Active devices [24h] count",
        help_text="Devices that have reported their status in the last 24 hours",
        default=0
    )
    active_devices_30d_count = models.IntegerField(
        verbose_name="Active devices [30d] count",
        help_text="Devices that have reported their status in the last 30 days",
        default=0
    )
    active_devices_60d_count = models.IntegerField(
        verbose_name="Active devices [60d] count",
        help_text="Devices that have reported their status in the last 60 days",
        default=0
    )
    desktop_devices_count = models.IntegerField(
        verbose_name="Desktop devices count", help_text="Devices that are running desktop operating systems", default=0
    )
    mobile_devices_count = models.IntegerField(
        verbose_name="Mobile devices count", help_text="Devices that are running mobile operating systems", default=0
    )
    macos_devices_count = models.IntegerField(
        verbose_name="macOS devices count", help_text="Devices that are running MacOS", default=0
    )
    windows_devices_count = models.IntegerField(
        verbose_name="Windows devices count", help_text="Devices that are running Windows", default=0
    )
    android_devices_count = models.IntegerField(
        verbose_name="Android devices count", help_text="Devices that are running Android", default=0
    )
    ios_devices_count = models.IntegerField(
        verbose_name="iOS devices count", help_text="Devices that are running iOS", default=0
    )

    class Meta:
        abstract = True

    def get_device_metrics_fields(self) -> list:
        """
        Returns a list of field names defined in the mixin that end with '_count'.
        """
        return [
            field.name for field in DevicesMetricsMixin._meta.local_fields
            if field.name.endswith("_count")
        ]

    @property
    def devices_analytic_is_ready(self) -> bool:
        """
        Checks if any device metrics count fields have a non-zero value.
        """
        return any(getattr(self, field) for field in self.get_device_metrics_fields())


class UsersMetricsMixin(BetaUsersMetricsMixin):
    """
    This model is intended to store users metric.
    """
    total_users_count = models.IntegerField(
        verbose_name="Total users count", help_text="Active users count", default=0
    )
    installed_users_count = models.IntegerField(
        verbose_name="Installed users count", help_text="Users that have installed CAP", default=0
    )
    secure_users_count = models.IntegerField(
        verbose_name="Secure users count", help_text="Users that have passed all checks", default=0
    )
    insecure_users_count = models.IntegerField(
        verbose_name="Insecure users count", help_text="Users that haven't passed all checks", default=0
    )
    academy_completed_users_count = models.IntegerField(
        verbose_name="Academy completed users count",
        help_text="Users that have completed all academy courses (modules)", default=0
    )
    academy_not_completed_users_count = models.IntegerField(
        verbose_name="Academy not completed users count",
        help_text="Users that haven't completed all academy courses (modules)", default=0
    )
    policies_agreed_users_count = models.IntegerField(
        verbose_name="Policies accepted users count", help_text="Users that have accepted all policies", default=0
    )
    policies_pending_users_count = models.IntegerField(
        verbose_name="Policies pending users count",
        help_text="Users that haven't accepted all policies", default=0
    )
    not_installed_users_count = models.IntegerField(
        verbose_name="Not installed users count", help_text="Enrolled users that haven't installed CAP", default=0
    )
    active_users_30d_count = models.IntegerField(
        verbose_name="Active users [30d] count",
        help_text="Users that have reported their status in the last 30 days",
        default=0
    )

    class Meta:
        abstract = True

    @property
    def users_analytic_is_ready(self):
        """
        Returns True if users analytics is ready otherwise returns False.
        """
        return any([
            self.total_users_count,
            self.secure_users_count,
            self.insecure_users_count,
            self.academy_completed_users_count,
            self.academy_not_completed_users_count,
            self.policies_agreed_users_count,
            self.policies_pending_users_count,
            self.not_installed_users_count,
            self.active_users_30d_count
        ])


class OrganisationsMetricsMixin(BetaOrganisationsMetricsMixin):
    """
    This model is intended to store partner organisations metric.
    """
    organisations_count = models.IntegerField(verbose_name="Total organisations count", default=0)
    secure_organisations_count = models.IntegerField(
        verbose_name="Secure organisations count", help_text="Organisations where all devices have passed all checks",
        default=0
    )
    insecure_organisations_count = models.IntegerField(
        verbose_name="Insecure organisations count", help_text="Organisations where devices haven't passed all checks",
        default=0
    )
    average_smart_score_percentage = models.FloatField(
        verbose_name="Average Smart Score percentage",
        help_text="Average smart score percentage of all organisations",
        validators=[MinValueValidator(0), MaxValueValidator(100)], default=0
    )
    certified_organisations_count = models.IntegerField(
        verbose_name="Certified organisations count", help_text="Organisations that have been certified", default=0
    )
    expiring_soon_organisations_count = models.IntegerField(
        verbose_name="Expiring soon organisations count",
        help_text="Organisations that have certificates that will expire soon",
        default=0
    )

    class Meta:
        abstract = True


class PartnerCertificationsMixin(models.Model):
    """
    This model is intended to store partner certifications metric.
    """
    certifications_count = models.IntegerField(verbose_name="Total certifications count", default=0)
    certifications_certified_count = models.IntegerField(verbose_name="Certified certifications count", default=0)
    certifications_in_progress_count = models.IntegerField(verbose_name="In progress certifications count", default=0)
    certifications_ready_for_assessment_count = models.IntegerField(
        verbose_name="Ready for assessment certifications count", default=0
    )
    certifications_ce_count = models.IntegerField(verbose_name="CE certifications count", default=0)
    certifications_certified_ce_count = models.IntegerField(
        verbose_name="CE certified certifications count", default=0
    )
    certifications_in_progress_ce_count = models.IntegerField(
        verbose_name="CE in progress certifications count", default=0
    )
    certifications_ready_for_assessment_ce_count = models.IntegerField(
        verbose_name="CE ready for assessment certifications count", default=0
    )
    certifications_ce_plus_count = models.IntegerField(verbose_name="CE+ certifications count", default=0)
    certifications_certified_ce_plus_count = models.IntegerField(
        verbose_name="CE+ certified certifications count", default=0
    )
    certifications_in_progress_ce_plus_count = models.IntegerField(
        verbose_name="CE+ in progress certifications count", default=0
    )
    certifications_ready_for_assessment_ce_plus_count = models.IntegerField(
        verbose_name="CE+ ready for assessment certifications count", default=0
    )
    certifications_gdpr_count = models.IntegerField(verbose_name="GDPR certifications count", default=0)
    certifications_certified_gdpr_count = models.IntegerField(
        verbose_name="GDPR certified certifications count", default=0
    )
    certifications_in_progress_gdpr_count = models.IntegerField(
        verbose_name="GDPR in progress certifications count", default=0
    )
    certifications_ready_for_assessment_gdpr_count = models.IntegerField(
        verbose_name="GDPR ready for assessment certifications count", default=0
    )
    # CyberSmart Complete certificates
    certifications_csc_count = models.IntegerField(verbose_name="CSC certifications count", default=0)
    certifications_certified_csc_count = models.IntegerField(
        verbose_name="CSC certified certifications count", default=0
    )
    certifications_in_progress_csc_count = models.IntegerField(
        verbose_name="CSC in progress certifications count", default=0
    )
    certifications_ready_for_assessment_csc_count = models.IntegerField(
        verbose_name="CSC ready for assessment certifications count", default=0
    )
    issued_certificates_count = models.IntegerField(verbose_name="Total issued certifications count", default=0)

    class Meta:
        abstract = True
