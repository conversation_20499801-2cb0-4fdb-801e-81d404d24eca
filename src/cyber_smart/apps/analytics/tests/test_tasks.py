from django.test import TestCase
from mock import patch
from waffle.testutils import override_switch

from analytics.models import OrganisationAnalytics
from analytics.tasks import calculate_organisation_analytics
from appusers.models.checks import CheckAutoFix, CheckManualFix
from appusers.models.checks import CheckResult, AppReport
from appusers.models.factories import AppInstallFactory, AppReportFactory, CheckResultFactory
from beta_features.models import CAP_V_FIVE_BETA_SWITCH
from common.base_tests import BaseTestCase
from rulebook.factories import AppCheckFactory
from rulebook.models import AppCheck
from software_inventory.models import InstalledSoftwareOrganisationIndividual


class CalculateOrganisationAnalyticsTestCase(BaseTestCase, TestCase):
    def setUp(self) -> None:
        self.core_setting_up()
        self.creating_app_user(with_app_install=False)
        self.setup_software_reports()

    def tearDown(self) -> None:
        self.deleting_software_reports()
        self.deleting_app_user()
        self.core_tearing_down()

    @patch("analytics.tasks.logger")
    def test_calculate_organisation_analytics_organisation_does_not_exist(
        self, logger_mock
    ) -> None:
        calculate_organisation_analytics(organisation_pk=self.organisation.pk + 1)
        self.assertEqual(logger_mock.error.call_count, 1)
        self.assertRegex(
            logger_mock.error.call_args.args[0], "No Organisation Found ID"
        )

    @patch("analytics.tasks.logger")
    def test_calculate_organisation_analytics_with_no_analytics_creates_one(
        self, logger_mock
    ) -> None:
        self.assertEqual(
            OrganisationAnalytics.objects.filter(
                organisation=self.organisation
            ).exists(),
            False,
        )
        InstalledSoftwareOrganisationIndividual.refresh()
        calculate_organisation_analytics(
            organisation_pk=self.organisation.pk, skip_partner_analytics=True
        )
        self.assertEqual(logger_mock.info.call_count, 1)
        self.assertRegex(
            logger_mock.info.call_args.args[0], "Calculated Organisation Analytics"
        )
        organisation_analytics = OrganisationAnalytics.objects.get(
            organisation=self.organisation
        )

        self.assertIsInstance(
            organisation_analytics,
            OrganisationAnalytics,
        )

        self.assertEqual(organisation_analytics.installed_devices_count, 2)
        self.assertEqual(organisation_analytics.secure_devices_count, 1)
        self.assertEqual(organisation_analytics.insecure_devices_count, 1)
        self.assertEqual(organisation_analytics.vulnerable_devices_count, 0)
        self.assertEqual(organisation_analytics.device_owners_count, 1)
        self.assertEqual(organisation_analytics.failed_checks_count, 1)
        self.assertEqual(organisation_analytics.active_devices_30d_count, 2)
        self.assertEqual(organisation_analytics.mobile_devices_count, 0)
        self.assertEqual(organisation_analytics.macos_devices_count, 0)
        self.assertEqual(organisation_analytics.windows_devices_count, 2)

        # unusef fields, default to 0
        self.assertEqual(organisation_analytics.active_devices_60d_count, 0)
        self.assertEqual(organisation_analytics.ios_devices_count, 0)
        self.assertEqual(organisation_analytics.active_devices_24h_count, 0)
        self.assertEqual(organisation_analytics.desktop_devices_count, 0)
        self.assertEqual(organisation_analytics.android_devices_count, 0)

        self.assertEqual(organisation_analytics.total_users_count, 1)
        self.assertEqual(organisation_analytics.secure_users_count, 0)
        self.assertEqual(organisation_analytics.insecure_users_count, 0)
        self.assertEqual(organisation_analytics.academy_completed_users_count, 0)
        self.assertEqual(organisation_analytics.academy_not_completed_users_count, 0)
        self.assertEqual(organisation_analytics.policies_agreed_users_count, 0)
        self.assertEqual(organisation_analytics.policies_pending_users_count, 0)
        self.assertEqual(organisation_analytics.not_installed_users_count, 0)
        self.assertEqual(organisation_analytics.active_users_30d_count, 1)

        self.assertEqual(organisation_analytics.pass_percentage, 0.0)
        self.assertEqual(organisation_analytics.certifications_pass_percentage, 0.0)
        self.assertEqual(organisation_analytics.cyber_essentials_pass_percentage, 0.0)
        self.assertEqual(
            organisation_analytics.cyber_essentials_plus_pass_percentage, 0.0
        )
        self.assertEqual(organisation_analytics.iasme_pass_percentage, 0.0)
        self.assertEqual(organisation_analytics.gdpr_pass_percentage, 0.0)
        self.assertEqual(organisation_analytics.secure_percentage, 0.0)
        self.assertEqual(organisation_analytics.insecure_percentage, 100.0)
        self.assertEqual(organisation_analytics.policies_agreed_percentage, None)
        self.assertEqual(organisation_analytics.policies_read_percentage, None)
        self.assertEqual(organisation_analytics.policies_not_agreed_percentage, None)
        self.assertEqual(
            organisation_analytics.security_awareness_training_percentage, None
        )

        self.assertEqual(organisation_analytics.total_packages_count, 3)
        self.assertEqual(organisation_analytics.safe_packages_count, 2)
        self.assertEqual(organisation_analytics.vulnerable_packages_count, 1)

    @patch("analytics.tasks.logger")
    def test_calculate_organisation_analytics_with_no_changes_does_not_update_existing(
        self, logger_mock
    ) -> None:
        calculate_organisation_analytics(
            organisation_pk=self.organisation.pk, skip_partner_analytics=True
        )
        self.assertEqual(logger_mock.info.call_count, 1)
        calculate_organisation_analytics(
            organisation_pk=self.organisation.pk, skip_partner_analytics=True
        )
        self.assertEqual(logger_mock.info.call_count, 1)

    @override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
    def test_calculate_organisation_analytics_with_beta_enabled_reduces_some_analytics(
        self,
    ) -> None:
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        InstalledSoftwareOrganisationIndividual.refresh()
        calculate_organisation_analytics(
            organisation_pk=self.organisation.pk, skip_partner_analytics=True
        )

        organisation_analytics = OrganisationAnalytics.objects.get(
            organisation=self.organisation
        )
        self.assertEqual(organisation_analytics.installed_devices_count, 1)  # was 2
        self.assertEqual(organisation_analytics.secure_devices_count, 0)  # was 1
        self.assertEqual(
            organisation_analytics.insecure_devices_count, 1
        )  # non-beta device insecure

        # meterialized views are not aware of beta devices logic,
        # so the counters for software packages are not affected and stay the same
        self.assertEqual(organisation_analytics.total_packages_count, 3)
        self.assertEqual(
            organisation_analytics.safe_packages_count, 2
        )  # only package with CVE was a on beta device
        self.assertEqual(organisation_analytics.vulnerable_packages_count, 1)


class TestAutoFixCrud(BaseTestCase, TestCase):
    def setUp(self) -> None:
        self.core_setting_up()
        self.creating_app_user(with_app_install=False)
        self.setup_software_reports()

    def _create_check_result(self, report: AppReport, app_check: AppCheck) -> CheckResult:
        """Helper method to create a check result"""
        return CheckResult.objects.create(
            report=report,
            app_check=app_check,
            response=False,
            response_text="User is in admin group",
            fixed=False
        )

    def test_auto_fix_crud_handles_duplicate_check_results(self) -> None:
        """Test that auto_fix_crud properly handles duplicate CheckResult entries"""
        # Create initial app install and report
        app_install = AppInstallFactory(app_user=self.app_user)
        report = AppReportFactory(app_install=app_install)

        # Create app checks
        app_check = AppCheckFactory(active=True)
        app_check_2 = AppCheckFactory(active=True)

        # Create initial check results
        check_result = CheckResultFactory(
            report=report,
            app_check=app_check,
            response=False,
            response_text="User is in admin group",
            fixed=False
        )
        check_result_2 = self._create_check_result(report, app_check)

        # Create additional check results for app_check_2
        for _ in range(3):
            self._create_check_result(report, app_check_2)

        # Create auto fix
        CheckAutoFix.objects.create(
            app_install=app_install,
            app_check=app_check,
            user=None,
        )

        # Create manual fix
        CheckManualFix.objects.create(
            app_install=app_install,
            app_check=app_check,
            user=None,
            reason="Test"
        )

        # Verify results are fixed
        check_result.refresh_from_db()
        check_result_2.refresh_from_db()
        self.assertEqual(check_result.fixed, True)
        self.assertEqual(check_result_2.fixed, True)

    def test_manual_fix_deletion_unfixes_check_results(self) -> None:
        """Test that deleting a manual fix causes check results to become unfixed"""
        # Create initial app install and report
        app_install = AppInstallFactory(app_user=self.app_user)
        report = AppReportFactory(app_install=app_install)
        app_check = AppCheckFactory(active=True)

        # Create check results
        check_result = CheckResultFactory(
            report=report,
            app_check=app_check,
            response=False,
            response_text="User is in admin group",
            fixed=False
        )
        check_result_2 = self._create_check_result(report, app_check)

        # Create manual fix
        manual_fix = CheckManualFix.objects.create(
            app_install=app_install,
            app_check=app_check,
            user=None,
            reason="Test"
        )

        # Verify results are fixed
        check_result.refresh_from_db()
        check_result_2.refresh_from_db()
        self.assertEqual(check_result.fixed, True)
        self.assertEqual(check_result_2.fixed, True)

        # Delete manual fix and verify results are unfixed
        manual_fix.delete()
        check_result.refresh_from_db()
        check_result_2.refresh_from_db()
        self.assertEqual(check_result.fixed, False)
        self.assertEqual(check_result_2.fixed, False)
