import csv
import json
import re

from admin_auto_filters.filters import AutocompleteFilterFactory
from django import forms
from django.contrib import admin, messages
from django.contrib.admin import SimpleListFilter
from django.core.exceptions import ValidationError
from django.db.models import Q
from django.http import HttpResponse, HttpResponseRedirect
from django.urls import path, reverse
from django.utils import timezone
from django.utils.html import format_html
from import_export.admin import ImportExportModelAdmin, ExportActionModelAdmin, ImportMixin, ImportExportMixin
from import_export.formats.base_formats import JSO<PERSON>
from modeltranslation.admin import TranslationAdmin

from common.forms import HtmlEditor
from common.models import SimpleHistoryAdminCustom
from rulebook.jotform.utils import convert_devices_for_cep_jotform
from rulebook.models import (
    OSPlatformRelease, OperatingSystem, CertificationVersion, Command, AppCheck, CertificationSurvey,
    SurveyQuestionChoices, SurveyQuestionFailedChoices, SurveyQuestionTopic, SurveyQuestion, SurveyDeclaration,
    BulkSolution, IssuedCertification, OSCheckRule, OSCheck, CertificateType, SurveyFailedResponse,
    SurveyResponse, AssessorNote, SurveyAssessment, FixGuide,
)
from rulebook.questions import (
    QUESTION_CODE_EQUIPMENT_DESKTOP,
    QUESTION_CODE_EQUIPMENT_MOBILE,
    CE_EQUIPMENT_SCOPE_QUESTION_CODES,
)
from rulebook.resources import OSCheckResource


class OSPlatformReleaseInline(admin.StackedInline):
    model = OSPlatformRelease
    extra = 1
    verbose_name_plural = 'OS\'s Platform and Releases'


@admin.register(OperatingSystem)
class OperatingSystemAdmin(admin.ModelAdmin):
    list_display = ('os_id', 'title', 'is_supported', 'created')
    search_fields = ['os_id', 'title']
    readonly_fields = ['created', 'modified']
    inlines = (OSPlatformReleaseInline,)


@admin.register(CertificationVersion)
class CertificationVersionAdmin(admin.ModelAdmin):
    change_form_template = "admin/download_certification_version.html"
    change_list_template = "admin/upload_certification_version.html"
    list_display = ('version_number', 'type', 'default', 'release_date')
    list_filter = ['version_number', 'type']
    readonly_fields = ['created', 'modified']

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser

    def get_urls(self):
        urls = super().get_urls()
        my_urls = [
            path(
                "<int:version_id>/add_relatives/",
                self.admin_site.admin_view(self.add_relatives),
                name="rulebook_certificationversion_add_relatives",
            ),
            path(
                "<int:version_id>/add_equivalents/",
                self.admin_site.admin_view(self.add_equivalents),
                name="rulebook_certificationversion_add_equivalents",
            )
        ]
        return my_urls + urls

    @staticmethod
    def validate_versions(cert_version, header):
        errors = []
        try:
            if int(cert_version.version_number) != int(header[0]):
                errors.append("version number in the file (1st cell) doesn't match the version number of the current certificate")
        except ValueError:
            errors.append("version numbers are not an integer number")
        return errors

    def validate_request(self, request, version_id):
        failed_validation_return = (False, None, None, None)
        if request.method != "POST":
            return failed_validation_return
        cert_version = self.model.objects.get(pk=version_id)
        csv_file = request.FILES.get('csv_file')
        if not csv_file:
            self.message_user(request, "No file uploaded", messages.ERROR)
            return failed_validation_return
        try:
            reader = csv.reader(csv_file.read().decode('utf-8').splitlines())
        except csv.Error as e:
            self.message_user(request, f"Error reading CSV file: {e}", messages.ERROR)
            return failed_validation_return
        # first row is the header
        header = next(reader)
        if errors := self.validate_versions(cert_version, header):
            self.message_user(request, f"Error: {','.join(errors)}", messages.ERROR)
            return failed_validation_return
        return (True, header, reader, cert_version)


    def add_equivalents(self, request, version_id):
        validation_passed, header, reader, cert_version = self.validate_request(request, version_id)
        if not validation_passed:
            return self.response_change(request, version_id)

        latest_choices = SurveyQuestionChoices.objects.filter(question__version=cert_version)

        previous_version_number = header[1]
        equivalent_choices = SurveyQuestionChoices.objects.filter(question__version__version_number=float(previous_version_number))
        for row in reader:
            latest_choice_record = row[0].split(" ")
            previous_choice_record = row[1].split(" ")
            latest_pervade_number = latest_choice_record[0]
            latest_choice_option = latest_choice_record[1]
            previous_pervade_number = previous_choice_record[0]
            previous_choice_option = previous_choice_record[1]
            latest_choice = latest_choices.filter(question__pervade_title__regex=rf"^{re.escape(latest_pervade_number)}(\s|$)", value_text__startswith=latest_choice_option).first()

            equivalent_choice = equivalent_choices.filter(
                question__pervade_title__regex=rf"^{re.escape(previous_pervade_number)}(\s|$)",
                value_text__startswith=previous_choice_option
            ).first()
            if not previous_choice_record or not latest_choice or not equivalent_choice:
                continue
            latest_choice.equivalents.add(equivalent_choice)
        self.message_user(request, "successfully added equivalents", messages.SUCCESS)
        return self.response_change(request, version_id)

    def add_relatives(self, request, version_id):
        validation_passed, header, reader, cert_version = self.validate_request(request, version_id)
        if not validation_passed:
            return self.response_change(request, version_id)

        questions = SurveyQuestion.objects.filter(version=cert_version)
        for row in reader:
            latest_pervade_number = row[0]
            previous_pervade_number = row[1]
            previous_version_number = header[1]
            question = questions.filter(pervade_title__regex=rf"^{re.escape(latest_pervade_number)}(\s|$)").first()
            relative_question = SurveyQuestion.objects.filter(
                Q(version__version_number=float(previous_version_number)) &
                Q(pervade_title__regex=rf"^{re.escape(previous_pervade_number)}(\s|$)")
            ).first()
            if not previous_pervade_number or not question or not relative_question:
                continue
            question.relatives.add(relative_question)
        self.message_user(request, "successfully added relatives", messages.SUCCESS)
        return self.response_change(request, version_id)


class CommandInline(admin.StackedInline):
    model = Command
    extra = 1
    verbose_name_plural = 'Question\'s Commands'
    raw_id_fields = ['os']


@admin.register(Command)
class CommandAdmin(ImportExportModelAdmin):
    list_display = ('app_check', 'os', 'expected_response', 'created', 'modified', 'min_app_version')
    list_filter = (
        AutocompleteFilterFactory('App Check', 'app_check'), AutocompleteFilterFactory("Operating System", "os")
    )
    readonly_fields = ['created', 'modified']
    raw_id_fields = ['app_check', 'os']


@admin.register(AppCheck)
class AppCheckAdmin(ImportExportMixin, TranslationAdmin):
    list_display = ('title', 'order', 'active', 'code', 'qtype', 'versions', 'platform_type', 'oss', 'created')
    ordering = ('order',)
    list_filter = ['version', 'commands__os']
    search_fields = ['title']
    readonly_fields = ['created', 'modified', 'active']
    actions = ['mark_active', 'mark_inactive']

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('version', 'commands__os')
        return queryset

    def get_inlines(self, request, obj):
        # AppChecks for Trustd mobile app do not have Commands
        if obj.is_trustd_check:
            return ()
        return (CommandInline,)

    @admin.action(permissions=['change'], description='Mark as active (only when submitting mobile app for review)')
    def mark_active(self, request, queryset):
        updated = queryset.update(active=True)
        self.message_user(request, f'{updated} App Checks made active', messages.SUCCESS)

    @admin.action(permissions=['change'], description='Mark as inactive (only when submitting mobile app for review)')
    def mark_inactive(self, request, queryset):
        updated = queryset.update(active=False)
        self.message_user(
            request,
            f'{updated} App Checks made inactive (please mark them active after the revision of submitted mobile app)',
            messages.SUCCESS
        )

    def versions(self, obj):
        return ", ".join([str(v.version_number) for v in obj.version.all()])

    def oss(self, obj):
        return ", ".join([str(o.os.os_id) for o in obj.commands.all()])


@admin.register(BulkSolution)
class BulkSolutionAdmin(admin.ModelAdmin):
    list_display = ('app_check', 'fix_choice', 'oss')
    list_filter = ['fix_choice']
    search_fields = ['app_check__title']
    readonly_fields = ['created', 'modified']
    raw_id_fields = ['app_check']

    def oss(self, obj):
        return obj.app_check


class SurveyChoicesVersionFilter(SimpleListFilter):
    # Minor SQL optimisation for list_filter
    title = 'Version'
    parameter_name = 'question__version'

    def lookups(self, request, model_admin):
        return [
            (
                version.pk, version
            ) for version in CertificationVersion.objects.all().prefetch_related('type')
        ]

    def queryset(self, request, queryset):
        filters = {}
        if self.value():
            filters['question__version'] = self.value()
        return queryset.filter(**filters)


class SurveyResponseVersionFilter(SimpleListFilter):
    title = 'Certification Version'
    parameter_name = 'survey__certificate__version'

    def lookups(self, request, model_admin):
        return [
            (
                version.pk, version
            ) for version in CertificationVersion.objects.all().prefetch_related('type')
        ]

    def queryset(self, request, queryset):
        filters = {}
        if self.value():
            filters['survey__certificate__version'] = self.value()
        return queryset.filter(**filters)


class SurveyAnswerCustomFilter(SimpleListFilter):
    """
    Custom filters for survey answers.
    """
    CEP_DEVICE_INFO = "cep_device_info"
    title = "Custom Filters"
    parameter_name = "custom_filter"

    def lookups(self, request, model_admin):
        return [(self.CEP_DEVICE_INFO, "CEP Device Information")]

    def queryset(self, request, queryset):
        filters = {}
        if self.value() and self.value() == self.CEP_DEVICE_INFO:
            filters["question__code__in"] = CE_EQUIPMENT_SCOPE_QUESTION_CODES
        return queryset.filter(**filters)


@admin.register(SurveyResponse)
class SurveyResponseAdmin(admin.ModelAdmin):
    list_display = ('answer', "question__pervade_title", "code", "question__topic", 'question', "question__insurer", 'created')
    search_fields = [
        'survey__certificate__organisation__name', 'value_text', "question__pervade_title", "question__pervade_id"
    ]
    list_filter = (
        AutocompleteFilterFactory('Certification Survey', 'survey'),
        AutocompleteFilterFactory("Topic", "question__topic"),
        SurveyAnswerCustomFilter,
        SurveyResponseVersionFilter,
        "question__pervade_title"
    )
    raw_id_fields = ['survey', 'question', 'choice']
    readonly_fields = ("a2_4_compiled_data",)

    class Media:
        css = {
            "all": ("css/admin.css",)
        }

        js = ("js/admin.js",)

    def get_fields(self, request, obj=None):
        fields = super().get_fields(request, obj)
        if obj and obj.question and obj.question.code in [
            QUESTION_CODE_EQUIPMENT_DESKTOP, QUESTION_CODE_EQUIPMENT_MOBILE
        ]:
            # insert a2_4_compiled_data field right after value_text field
            fields.pop(fields.index("a2_4_compiled_data"))
            fields.insert(fields.index("value_text") + 1, "a2_4_compiled_data")
        return fields

    @admin.display(
        ordering="question__pervade_title"
    )
    def question__pervade_title(self, obj):
        return obj.question.pervade_title

    @admin.display(
        ordering="question__topic"
    )
    def question__topic(self, obj):
        return obj.question.topic.title

    @admin.display(
        ordering="question__insurer"
    )
    def question__insurer(self, obj):
        return obj.question.get_insurer_display()

    def get_queryset(self, request):
        return super(SurveyResponseAdmin, self).get_queryset(request).prefetch_related(
            *self.raw_id_fields
        ).prefetch_related(
            'question', 'question__version', 'question__version__type', 'question__topic__version',
            'question__topic__version__type', 'choice'
        )

    @admin.display(
        description="A2.4/A2.6 Compiled Data"
    )
    def a2_4_compiled_data(self, obj):
        compiled_data = convert_devices_for_cep_jotform(obj.value_text)
        if compiled_data:
            display_string = ""
            for key, value in compiled_data.items():
                display_string += f"{key[0]} - {key[1]} - x{value}\n"
        else:
            display_string = "It's either impossible to parse CE A2.4 data as it has invalid format or it's empty"
        return format_html(
            u'<textarea cols="40" rows="10" class="vLargeTextField" readonly>{0}</textarea>', display_string
        )


    def answer(self, obj):
        color = 'green'
        if not obj.not_applicable:
            if obj.value:
                color = 'green'
            else:
                color = 'red'
        elif obj.not_applicable and obj.value_boolean is None:
            color = 'silver'
        elif obj.not_applicable and obj.value_boolean is not None:
            color = 'red'
        return format_html(u'<span style="color: {0}">{1}</span>', color, obj.get_value)



@admin.register(SurveyFailedResponse)
class SurveyFailedResponseAdmin(admin.ModelAdmin):
    list_display = ('survey', 'answer', 'question', 'created')
    search_fields = ['survey__certificate__organisation__name', 'survey__certificate__id', 'value_text']
    list_filter = (AutocompleteFilterFactory('Survey', 'survey'), SurveyResponseVersionFilter)
    raw_id_fields = ['survey', 'question', 'choice']

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('survey__certificate__version', 'survey__certificate__version__type')
        return queryset

    def answer(self, obj):
        return obj.get_value


class SurveyQuestionChoiceInline(admin.TabularInline):
    model = SurveyQuestionChoices
    extra = 1
    raw_id_fields = ("question", "equivalents")


class SurveyQuestionFailedChoiceInline(admin.TabularInline):
    model = SurveyQuestionFailedChoices
    extra = 1
    raw_id_fields = ("question", "equivalents")


class SurveyQuestionTopicFilter(SimpleListFilter):
    title = 'topic'
    parameter_name = 'topic'

    def lookups(self, request, model_admin):
        return [
            (
                topic.pk, topic
            ) for topic in SurveyQuestionTopic.objects.all().prefetch_related('version', 'version__type')
        ]

    def queryset(self, request, queryset):
        filters = {}
        if self.value():
            filters['topic_id'] = self.value()
        return queryset.filter(**filters)


class SurveyQuestionVersionFilter(SimpleListFilter):
    title = 'version'
    parameter_name = 'version'

    def lookups(self, request, model_admin):
        return [
            (
                version.pk, version
            ) for version in CertificationVersion.objects.all().prefetch_related('type')
        ]

    def queryset(self, request, queryset):
        filters = {}
        if self.value():
            filters['version_id'] = self.value()
        return queryset.filter(**filters)


@admin.register(SurveyQuestion)
class SurveyQuestionAdmin(admin.ModelAdmin):
    resource_class = SurveyQuestion
    list_display = (
        '__unicode__', 'version', 'topic', 'pervade_title', "code",
        'order', 'insurer', 'pervade_id',
        'pervade_save_applicant_notes', 'response_type', 'widget', 'ranking', 'created'
    )
    list_filter = (SurveyQuestionVersionFilter, 'insurer', 'ranking', 'response_type', SurveyQuestionTopicFilter,
                   "widget")
    search_fields = ('title', "code", "pervade_title", "pervade_id")
    filter_horizontal = ('relatives',)
    list_editable = ['pervade_title', 'pervade_id', 'order', 'pervade_save_applicant_notes']
    inlines = (SurveyQuestionChoiceInline, SurveyQuestionFailedChoiceInline)
    readonly_fields = ['created', 'modified']
    raw_id_fields = (
        'parent', 'parent_2', 'topic', 'version', 'related_check', 'auto_answer_survey_question',
        'mandatory_for_parent_choices', 'auto_answer_survey_question_choice', 'auto_answer_current_question_choice',
        'auto_answer_choice'
    )

    def get_field_queryset(self, db, db_field, request):
        if db_field.name == 'relatives':
            return SurveyQuestion.objects.all().prefetch_related('version', 'version__type')
        return super(SurveyQuestionAdmin, self).get_field_queryset(db, db_field, request)

    class Media:
        css = {
            'all': ('css/resize-widget.css',)
        }

    def get_queryset(self, request):
        return super(SurveyQuestionAdmin, self).get_queryset(request).prefetch_related(
            *self.raw_id_fields
        ).prefetch_related(
            'version__type', 'topic__version', 'topic__version__type'
        )


@admin.register(CertificationSurvey)
class CertificationSurveyAdmin(SimpleHistoryAdminCustom):
    list_display = ('certificate', 'datetime_started', 'created', 'modified')
    search_fields = ['certificate__organisation__name', 'certificate__id']
    list_filter = ['certificate__version', "migrated_to_iasme_insurance"]
    readonly_fields = ['created', 'modified']
    raw_id_fields = ['certificate']

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('certificate__version', 'certificate__version__type')
        return queryset


class DeclarationReportForm(forms.ModelForm):
    model = SurveyDeclaration

    class Meta:
        fields = '__all__'
        widgets = {
            'html_report': HtmlEditor(),
        }


@admin.register(SurveyDeclaration)
class SurveyDeclarationAdmin(admin.ModelAdmin):
    list_display = (
        'survey', 'signature_tick', 'declaration_date', 'declaration_name', 'declaration_email',
        'declaration_secondary_email', 'modified'
    )
    search_fields = ['survey__certificate__organisation__name', 'survey__certificate__id']
    list_filter = ['survey__certificate__version']
    raw_id_fields = ['survey']
    form = DeclarationReportForm
    fields = [
        'survey', 'declaration_date', 'declaration_name', 'declaration_job', 'declaration_email',
        'declaration_secondary_email', 'ready_to_be_signed', 'signature', 'pdf', 'date_signed',
        'show_button', 'created', 'modified'
    ]

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related(
            'survey__certificate__version', 'survey__certificate__version__type'
            )
        return queryset

    def get_readonly_fields(self, request, obj=None):
        if request.user.is_superuser:
            return ['created', 'modified', 'show_button']
        readonly_fields = set(
            [field.name for field in self.opts.local_fields] +
            [field.name for field in self.opts.local_many_to_many] +
            ['show_button']
        )
        return readonly_fields.difference(['id', 'pdf'])

    @admin.display(
        description=''
    )
    def show_button(self, obj):
        return format_html('<a target="_blank" href="{0}">SHOW HTML REPORT</a>', obj.html_report_url)

    @admin.display(
        description='Signature'
    )
    def signature_tick(self, obj):
        return format_html('<img src="/static/admin/img/icon-{0}.svg" alt="True">', ('yes' if obj.signature else 'no'))



@admin.register(IssuedCertification)
class IssuedCertificationAdmin(admin.ModelAdmin):
    list_display = (
        'certificate', 'date', 'number', 'created', 'modified', 'custom_cert_sent', 'custom_cert_date'
    )
    search_fields = ['certificate__organisation__name', 'certificate__id']
    list_filter = [AutocompleteFilterFactory('Distributor', 'certificate__organisation__partner__distributor'),
                   AutocompleteFilterFactory('Partner', 'certificate__organisation__partner'),
                   'certificate__version', 'certificate__pervade_assessor']
    raw_id_fields = ('certificate',)
    fieldsets = (
        (None, {'fields': (
            'certificate', 'date', 'number', 'insurance_number', 'created', 'modified', 'blockmark_certificate_registration_url',
            'blockmark_certificate_forwarding_url', 'blockmark_evidence_of_insurance_registration_url',
            'blockmark_evidence_of_insurance_forwarding_url')}),
        ('PDF reports', {'fields': ('certificate_file', 'report_file', 'insurance_file', 'evidence_of_insurance_file')}),
        ('Customer Cert Sent Track', {'fields': ('custom_cert_sent', 'custom_cert_date')})
    )
    readonly_fields = ['created', 'modified', 'number', 'insurance_number']

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('certificate__version', 'certificate__version__type')
        return queryset

    def get_form(self, request, obj=None, change=False, **kwargs):
        form = super().get_form(request, obj, change, **kwargs)

        def clean_date(self):
            cleaned_date = self.cleaned_data["date"]
            if not cleaned_date:
                raise forms.ValidationError("Date cannot be empty")
            if cleaned_date > timezone.now():
                raise forms.ValidationError("Date cannot be in the future")
            return cleaned_date

        form.clean_date = clean_date
        return form


@admin.register(SurveyQuestionTopic)
class SurveyQuestionTopicAdmin(admin.ModelAdmin):
    list_display = ('title', 'order', "iasme_number", 'version', 'created', 'modified', 'description')
    search_fields = ('title', 'order', "iasme_number", 'version__version_number', 'version__type__type', 'created',
                     'modified', 'description')
    list_filter = (
        'title', "iasme_number", 'version', 'created', 'modified', 'version__type'
    )
    readonly_fields = ['created', 'modified']
    raw_id_fields = ['version']

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('version', 'version__type')
        return queryset


@admin.register(SurveyQuestionChoices)
class SurveyQuestionChoicesAdmin(admin.ModelAdmin):
    list_filter = (SurveyChoicesVersionFilter,)
    raw_id_fields = ['question']
    search_fields = ("value_text", "question__pervade_title")
    filter_horizontal = ('equivalents',)

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('question__version', 'question__version__type')
        return queryset

    def get_field_queryset(self, db, db_field, request):
        if db_field.name == 'equivalents':
            return SurveyQuestionChoices.objects.select_related('question').all()
        return super(SurveyQuestionChoicesAdmin, self).get_field_queryset(db, db_field, request)


@admin.register(SurveyQuestionFailedChoices)
class SurveyQuestionFailedChoicesAdmin(admin.ModelAdmin):
    list_filter = (SurveyChoicesVersionFilter,)
    raw_id_fields = ['question']
    filter_horizontal = ('equivalents',)

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('question__version', 'question__version__type')
        return queryset

    def get_field_queryset(self, db, db_field, request):
        if db_field.name == 'equivalents':
            return SurveyQuestionFailedChoices.objects.select_related('question').all()
        return super(SurveyQuestionFailedChoicesAdmin, self).get_field_queryset(db, db_field, request)


@admin.register(SurveyAssessment)
class SurveyAssessmentAdmin(SimpleHistoryAdminCustom):
    search_fields = ['survey__certificate__organisation__name']
    list_filter = [AutocompleteFilterFactory('Organisation', 'survey__certificate__organisation')]
    raw_id_fields = ['survey']

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related(
            'survey__certificate__organisation', 'survey__certificate__version', 'survey__certificate__version__type'
            )
        return queryset


@admin.register(AssessorNote)
class AssessorNoteAdmin(admin.ModelAdmin):
    list_display = ['assessment', 'created']
    list_filter = [AutocompleteFilterFactory('Survey', 'assessment__survey')]
    raw_id_fields = ['assessment', 'topic', 'question']
    readonly_fields = ['created', 'modified']

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related(
            'assessment__survey__certificate__version', 'assessment__survey__certificate__version__type'
            )
        return queryset


class OSCheckRuleInline(admin.StackedInline):
    model = OSCheckRule
    extra = 1
    verbose_name_plural = 'Rules for this OS check'


@admin.register(OSCheck)
class OSCheckAdmin(ImportMixin, ExportActionModelAdmin):
    list_display = ('name', 'created', 'supported')
    readonly_fields = ['created']
    inlines = (OSCheckRuleInline,)
    # import/export related fields
    formats = [JSON]
    resource_class = OSCheckResource
    import_template_name = 'admin/import_export/oscheck_import.html'


class OperatingSystemMultipleChoiceField(forms.ModelMultipleChoiceField):
    def label_from_instance(self, obj):
        """ The os_id contains the platform (e.g. android_14) therefore we add it to the display."""
        return f'{obj.title} ({obj.os_id})'


class FixGuideForm(forms.ModelForm, TranslationAdmin):
    operating_systems = OperatingSystemMultipleChoiceField(
        queryset=OperatingSystem.objects.all(),
        widget=admin.widgets.FilteredSelectMultiple('Operating Systems', is_stacked=False)
    )

    class Meta:
        model = FixGuide
        fields = '__all__'

    def clean(self):
        """ Override merely to check if a FixGuide with the same AppCheck and OperatingSystem
        combination already exists. Or integration error will be raised due to enforce_fix_guide_unique_combination."""
        # if editing an AppCheck, exclude it from the validation.
        excludes = {'pk': self.instance.pk} if self.instance.pk else {}
        if FixGuide.objects.filter(
                app_check=self.cleaned_data.get('app_check'),
                operating_systems__in=self.cleaned_data.get('operating_systems')
        ).exclude(**excludes).exists():
            raise ValidationError(
                'A FixGuide with this AppCheck and OperatingSystem combination already exists.'
            )


@admin.register(FixGuide)
class FixGuideAdmin(TranslationAdmin):
    form = FixGuideForm
    list_filter = [AutocompleteFilterFactory('AppCheck', 'app_check')]
    list_display = ('app_check', 'get_operating_systems', 'created', 'modified')
    raw_id_fields = ["app_check"]
    readonly_fields = ('modified', 'created')
    actions = ['download_json']
    change_list_template = 'admin/rulebook/upload_fix_guides.html'

    def get_urls(self):
        urls = super().get_urls()
        my_urls = [
            path(
                "upload_json/",
                self.admin_site.admin_view(self.upload_json),
                name="rulebook_fixguides_upload_json",
            ),
        ]
        return my_urls + urls

    @admin.display(description='Operating Systems')
    def get_operating_systems(self, obj):
        return ', '.join(obj.operating_systems.values_list('title', flat=True))

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('app_check')
        queryset = queryset.prefetch_related('operating_systems')
        return queryset

    @admin.action(permissions=['view'], description='Download JSON to upload in new env')
    def download_json(self, request, queryset):
        fix_guides = queryset.prefetch_related('operating_systems').all()

        fix_guides_list = []
        for fix_guide in fix_guides:
            os_ids = ','.join([str(os.os_id) for os in fix_guide.operating_systems.all()])
            fix_guides_list.append({'app_check_code': fix_guide.app_check.code, 'os_ids': os_ids, 'steps_to_fix': fix_guide.steps_to_fix})

        response = HttpResponse(content_type='application/json')
        response['Content-Disposition'] = 'attachment; filename="fix_guides.json"'
        response.write(json.dumps(fix_guides_list, indent=4))
        return response

    def upload_json(self, request):
        json_file = request.FILES.get('json_file')
        change_url = reverse('admin:rulebook_fixguide_changelist')
        if not json_file:
            self.message_user(request, "No file uploaded", messages.ERROR)
            return HttpResponseRedirect(change_url)

        data = json_file.read().decode('utf-8')
        try:
            fix_guides_data = json.loads(data)
        except json.JSONDecodeError as e:
            self.message_user(request, f"Error reading JSON file: {e}", messages.ERROR)
            return HttpResponseRedirect(change_url)

        created_count = 0
        for item in fix_guides_data:
            app_check_code = item.get('app_check_code')
            os_ids = [os_id.strip() for os_id in item.get('os_ids', '').split(',') if os_id.strip()]
            steps_to_fix = item.get('steps_to_fix', '')

            try:
                app_check = AppCheck.objects.get(code=app_check_code)
            except AppCheck.DoesNotExist:
                self.message_user(request, f"AppCheck with code {app_check_code} does not exist", messages.ERROR)
                continue

            existing_fix_guide = FixGuide.objects.filter(
                app_check=app_check,
                operating_systems__os_id__in=os_ids
            ).exists()
            if existing_fix_guide:
                self.message_user(request, f"FixGuide for AppCheck {app_check_code} and some of OS IDs {os_ids} already exists, please delete if you want to add fresh", messages.WARNING)
                continue

            fix_guide = FixGuide.objects.create(
                app_check=app_check,
                steps_to_fix=steps_to_fix
            )
            created_count += 1
            operating_systems = OperatingSystem.objects.filter(os_id__in=os_ids)
            if not operating_systems.exists():
                self.message_user(request, f"No Operating Systems found for os_ids: {os_ids}", messages.ERROR)
                continue

            fix_guide.operating_systems.set(operating_systems)

        self.message_user(request, f"successfully created {created_count} fix guides", messages.SUCCESS)
        return HttpResponseRedirect(change_url)



admin.site.register(OSPlatformRelease)
admin.site.register(CertificateType)
