import logging
import re
from collections import defaultdict
from typing import Dict, <PERSON><PERSON>, Any

from django.conf import settings
from packaging.version import parse as parse_version

logger = logging.getLogger(__name__)


def convert_device_for_cep_jotform(os_release: str) -> Tuple[Tuple[str, str], int]:
    """
    Return Key and Quantity of parsed record.
    If it can't parse, raise an exception.
    """
    # check for [x{digit}] in the devices_info string
    if re.search(r"\[x\d+\]", os_release):
        # split each entry into name, release, manufacturer and quantity
        name, release, *manufacturer_and_quantity = os_release.strip().split(" - ")
        try:
            release = str(parse_version(release).major)
        except Exception as e:
            logger.debug(f"Failed to parse major version of release for JotForm URL {os_release}. {e}")
        # extract quantity from the manufacturer_and_quantity string
        quantity_pattern = r"\[x(\d+)"
        if match := re.search(quantity_pattern, "".join(manufacturer_and_quantity)):
            quantity = int(match.group(1))
        else:
            raise Exception(f"Quantity not found in {os_release}.")
        # create a map key which consists from a tuple of name and release
    else:
        # fallback to the old way of parsing if there's no x[digit] in the string
        # extract quantity and device_full_string, only matching the first x
        quantity, device_full_string = os_release.strip().split("x", 1)
        quantity = int(quantity.strip())

        # extract name and release from the quantity which is either Android.. or iOS..
        name, release = re.sub(r"^.*?(Android|iOS)", r"\1", device_full_string).strip().split(" ", 1)

        # finally, get only the major version using parse_version
        release = str(parse_version(release).major)
    if name is None or quantity is None:
        raise Exception(f"Failed to parse {os_release}. name={name}, quantity={quantity}")
    key = (name, release)
    return key, quantity


def convert_devices_for_cep_jotform(devices_info: Any) -> Dict[Tuple[str, str], int]:
    """
    Takes a string of devices information from the question A2.4 or A2.6 of the Cyber Essentials questionnaire
    and converts it to a dictionary where the key is a tuple of OS name and release and the value is
    the quantity of devices with that OS name and release.
    Then it will be used to build a JotForm URL where devices information will be passed as a query parameter.
    Example:
    Input: "Windows 10 - 10H2 - Dell [x1],
            Windows 10 - 10H2 - Dell [x1],
            Windows 11 - 20H2 - Asus [x1],
            Windows 11 - 20H2 - Microsoft [x1]"
    Output: {("Windows 10", "10H2"): 2, ("Windows 11", "20H2"): 2}
    Example 2:
    Input: "1x iPhone 11 Pro iOS 16.5.1,
            1x Pixel 6 Android 13.1"
    Output: {("iOS", "16"): 1, ("Android", "13"): 1}
    """
    os_map = defaultdict(int)
    if not (isinstance(devices_info, (str, list))):
        return os_map

    if isinstance(devices_info, list):
        # if we get device data from the new widget which stores data in json format,
        # just convert it to the old string format to not change the existing logic
        os_releases = [f"{device['os']} - {device['version']} - {device['make']} [x{device['quantity']}]"
                       for device in devices_info]
    else:
        if re.search(r"\[x\d+\]", devices_info):
            # Use a regular expression to match the pattern of device information
            # This pattern assumes that the quantity is always in the format [x{quantity}]
            # and tries to account for commas within the device names by not splitting on them directly.
            # This prevents cases like Microsoft Windows 10 Pro - 22H2 - VMware, Inc. [x1]  breaking the parsing
            pattern = r'(.+?\s*-\s*.+?\s*-\s*.*?\s*\[x\d+\])(, )?'
            matches = re.findall(pattern, devices_info)
            os_releases = [match[0] for match in matches]
        else:
            # Support legacy format without - [x\d+] in names
            os_releases = devices_info.split(", ")

    for os_release in os_releases:
        # check for [x{digit}] in the devices_info string
        try:
            key, quantity = convert_device_for_cep_jotform(os_release)
            os_map[key] += int(quantity)
        except Exception as e:
            logger.debug(f"Failed to parse device info for JotForm URL {os_release} ({e})")
    return os_map


def get_cep_jotform_id() -> str:
    """
    Returns JotForm ID for Cyber Essentials Plus audit.
    """
    match = re.search(r'/(\d+)/?$', settings.JOTFORM_URL)
    number = match.group(1) if match else None
    if not number:
        logger.error("Failed to get JotForm ID for CE+, check JOTFORM_URL environment variable.")
    return number
