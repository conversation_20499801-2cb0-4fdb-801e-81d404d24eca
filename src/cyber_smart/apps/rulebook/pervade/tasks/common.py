from __future__ import absolute_import, unicode_literals

import logging
import time
from datetime import datetime, timedelta

import sentry_sdk
from celery import shared_task as task
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.files.base import ContentFile

from common.tasks import ResultEnabledTask
from organisations.models import OrganisationCertification
from rulebook.generate_declaration import GenerateDeclaration
from rulebook.models import (
    SurveyDeclaration, CertificationVersion, SurveyQuestion, IssuedCertification,
)
from rulebook.pervade.api import PervadeAPIWrapper
from .jobs import (
    PervadeContributorJob, PervadeAnswersUpdateJob, PervadeAnswersSubmitJob, PervadeCertificatePreviewJob,
    PervadeCertificateIssueJob,
)

logger = logging.getLogger(__name__)
User = get_user_model()


@task
def update_answers_task(certification_pk: int, assessor_pk: int) -> None:
    """
    Updates answers in Pervade.
    """
    PervadeContributorJob(certification_pk, assessor_pk).execute()
    PervadeAnswersUpdateJob(certification_pk, assessor_pk).execute()


@task
def preview_report_task(certification_pk: int, assessor_pk: int) -> None:
    """
    Downloads a preview of the report from pervade and saves it to the cache for 5 minutes.
    """
    PervadeContributorJob(certification_pk, assessor_pk).execute()
    PervadeAnswersUpdateJob(certification_pk, assessor_pk).execute()
    PervadeCertificatePreviewJob(certification_pk, assessor_pk).execute()


@task
def submit_answers_and_issue_certificate_task(certification_pk: int, assessor_pk: int) -> None:
    """
    Submits the certification to Pervade for issuing.
    """
    PervadeAnswersSubmitJob(certification_pk, assessor_pk).execute()
    issue_certificate_task.delay(certification_pk, assessor_pk)


@task(bind=True, base=ResultEnabledTask, retry_kwargs={'max_retries': 3, 'default_retry_delay': 4 * 60})
def issue_certificate_task(self, certification_pk: int, assessor_pk: int) -> None:
    """
    This task is split and retries a few times with delay to allow Pervade to generate things on their side before retrying.
    """
    ret = PervadeCertificateIssueJob(certification_pk, assessor_pk).execute()
    certification = OrganisationCertification.objects.get(pk=certification_pk)
    if ret:
        certification.survey.assessment.set_certificate_download()
    # Reset the cache key to indicate the certificate issuing task is complete
    certification.reset_certification_issuing_job_status()


@task
def generate_declaration_pdf_task(declaration_pk):
    try:
        declaration = SurveyDeclaration.objects.get(pk=declaration_pk)
    except SurveyDeclaration.DoesNotExist:
        with sentry_sdk.push_scope() as scope:
            scope.set_extra('Declaration pk', declaration_pk)
            sentry_sdk.capture_message(
                message='No Declaration Found for pk [{0}]'.format(declaration_pk), level='error'
            )
    else:
        try:
            insurance = declaration.survey.certificate.insurance_opt_in_survey_value
        except OrganisationCertification.DoesNotExist:
            insurance = False
        GenerateDeclaration(declaration, insurance).run()


@task
def update_compliance_fields(cert_version: CertificationVersion):
    api = PervadeAPIWrapper()
    api_response: dict = api.get_questions(cert_version.pervade_title)
    if api_response:
        if "questions" in api_response["data"]:
            for question_id, question_data in api_response["data"]["questions"].items():
                survey_question = SurveyQuestion.objects.filter(pervade_id=question_id).first()
                if survey_question:
                    if "compliancefields" in question_data:
                        compliance_fields = question_data["compliancefields"]
                        if compliance_fields:
                            survey_question.pervade_compliance_fields = compliance_fields
                            survey_question.save(update_fields=["pervade_compliance_fields", "modified"])
                            print(f"Compliance fields were updated for question {survey_question.pervade_title}")
        else:
            logger.error("Unable to update compliance fields", extra={
                'stack': True,
                "data": api_response,
            })


@task
def save_postmark_certificate_task(certification_pk: int, certificate_name: str) -> None:
    """
    Over the next five minutes checks if IssuedCertification is already created and if so, saves the certificate to it.

    Note: make sure to only do partial updates (save with update_fields) to IssuedCertification, otherwise the data might be lost in the caller process which is doing similar updates.
    """
    end_time = datetime.now() + timedelta(minutes=5)
    try:
        certification = OrganisationCertification.objects.get(pk=certification_pk)
    except OrganisationCertification.DoesNotExist:
        logger.error(f"Unable to find OrganisationCertification with pk {certification_pk}")
    else:
        cached_certificate_content = cache.get(f"blockmark_certificate_{certification.pk}")
        cached_evidence_of_insurance_content = cache.get(f"blockmark_evidence_of_insurance_{certification.pk}")
        if cached_certificate_content or cached_evidence_of_insurance_content:
            while end_time >= datetime.now():
                if hasattr(certification, "issued_certification"):
                    issued_certification = certification.issued_certification
                    if cached_certificate_content:
                        issued_certification.certificate_file.save(
                            certificate_name, ContentFile(cached_certificate_content),
                            save=False
                        )
                        issued_certification.save(update_fields=["certificate_file"])
                    if cached_evidence_of_insurance_content:
                        issued_certification.evidence_of_insurance_file.save(
                            certificate_name, ContentFile(cached_evidence_of_insurance_content),
                            save=False
                        )
                        issued_certification.save(update_fields=["evidence_of_insurance_file"])
                    break
                else:
                    time.sleep(5)
                    certification.refresh_from_db()
                    continue
            else:
                # if we get here, it means that the IssuedCertification was not created in 5 minutes by CertOS
                # and we have to create it here at least to save the files
                issued_certification = IssuedCertification.objects.get_or_create(
                    certificate_id=certification_pk
                )[0]
                if cached_certificate_content:
                    issued_certification.certificate_file.save(
                        certificate_name, ContentFile(cached_certificate_content),
                        save=False
                    )
                    issued_certification.save(update_fields=["certificate_file"])
                if cached_evidence_of_insurance_content:
                    issued_certification.evidence_of_insurance_file.save(
                        certificate_name, ContentFile(cached_evidence_of_insurance_content),
                        save=False
                    )
                    issued_certification.save(update_fields=["evidence_of_insurance_file"])
            if cached_certificate_content:
                cache.delete(f"blockmark_certificate_{certification.pk}")
            if cached_evidence_of_insurance_content:
                cache.delete(f"blockmark_evidence_of_insurance_{certification.pk}")
        else:
            logger.error(
                f"Unable to find certificate or evidence of insurance content in cache for OrganisationCertification with pk {certification_pk}"
            )
