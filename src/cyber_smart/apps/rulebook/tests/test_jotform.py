from datetime import datetime
from unittest.mock import patch, Mock
from urllib.parse import urlencode, urlparse, parse_qs, urljoin

from django.test import TestCase

from accounts.factories import UserFactory
from distributors.factories import DistributorUserFactory
from organisations.factories import OrganisationFactory, OrganisationCertificationFactory
from partners.factories import PartnerUserFactory
from rulebook.admin import SurveyAnswerCustomFilter
from rulebook.factories import CertificationVersionFactory, CertificateTypeFactory, CertificationSurveyFactory
from rulebook.jotform.integration import JotformURLBuilder
from rulebook.jotform.utils import convert_device_for_cep_jotform, convert_devices_for_cep_jotform, get_cep_jotform_id
from rulebook.models import CYBER_ESSENTIALS


class ConvertDevicesForCepJotformTestCase(TestCase):
    """
    Tests for convert_devices_for_cep_jotform function.

    This textfield gets filled in actualize_iniital_responses
    """
    def test_invalid_format(self):
        self.assertEqual(convert_devices_for_cep_jotform(""), {})
        self.assertEqual(convert_devices_for_cep_jotform("invalid format"), {})
        self.assertEqual(convert_devices_for_cep_jotform(5), {})
        self.assertEqual(convert_devices_for_cep_jotform([]), {})
        self.assertEqual(convert_devices_for_cep_jotform({}), {})

    def test_valid_format(self):
        self.assertEqual(
            convert_devices_for_cep_jotform("Windows 10 - 10H2 - Dell [x1]"),
            {("Windows 10", "10H2"): 1}
        )
        self.assertEqual(
            convert_devices_for_cep_jotform([
                {'os': 'Windows 11', 'make': 'Dell', 'version': '22H2', 'quantity': 2},
                {'os': 'Windows 11', 'make': 'HP', 'version': '22H2', 'quantity': 1}]
            ),
            {("Windows 11", "22H2"): 3}
        )

    def test_valid_format_invalid_quantity(self):
        self.assertEqual(convert_devices_for_cep_jotform("Windows 10 - 10H2 - Dell [x]"), {})
        self.assertEqual(convert_devices_for_cep_jotform(
            [{'os': 'Windows 11', 'make': 'HP', 'version': '22H2', 'quantity': "asdf"}]
        ), {})

    def test_invalid_format_does_not_raise(self):
        self.assertEqual(convert_devices_for_cep_jotform("7 x Sonoma (Mac)\r\n5 x Windows 11 (Dell)"), {})

    def test_case_with_comma_in_name(self):
        os_list = "Microsoft Windows 10 Pro - 22H2 - System manufacturer [x1], Microsoft Windows 10 Pro - 22H2 - VMware, Inc. [x1]"
        correct_result = {
            ("Microsoft Windows 10 Pro", "22H2"): 2
        }
        self.assertEqual(convert_devices_for_cep_jotform(os_list), correct_result)

    def test_valid_format_valid_quantity(self):
        os_list = [
            "Microsoft Windows 10 Pro - 22H2 - Acer[x2]",
            "Microsoft Windows 10 Pro - 22H2 - Dell Inc.[x1]",
            "Microsoft Windows 10 Pro - 22H2 - Hewlett - Packard[x1]",
            "Microsoft Windows 10 Pro - 22H2 - HP[x6]",
            "Microsoft Windows 11 Home - 22H2 - HP[x1]",
            "Microsoft Windows 11 Home - 22H2 - Acer[x3]",
            "Microsoft Windows 11 Pro - 22H2 - Acer[x59]",
            "Microsoft Windows 11 Pro - 22H2 - HP[x11]",
            "Microsoft Windows 11 Pro - 22H2 - LENOVO[x1]",
            "Microsoft Windows 11 Pro - 23H2 - LENOVO[x1]",
            "Microsoft Windows 11 Pro - 23H2 - HP[x1]",
            "Microsoft Windows 11 Business - 22H2 - Microsoft Corporation[x1]",
            "Microsoft Windows 11 Business - 22H2 - LENOVO[x11]",
            "Microsoft Windows 11 Business - 23H2 - Microsoft Corporation[x2]",
            "macOS Ventura - 13.1 - Apple[x1]",
            "macOS Sonoma - 14.0 - Apple[x1]",
            "macOS Sonoma - 14.1.1 - Apple[x3]",
            "macOS Sonoma - 14.1.2 - Apple[x2]",
            "macOS Sonoma - 14.1.2 - Apple[x1]",
            "macOS Sonoma - 14.2.1 - Apple[x4]",
            "Microsoft Windows 10 Pro - 22H2 - System manufacturer [x1]",
            "Microsoft Windows 10 Pro - 22H2 - VMware, Inc. [x1]"
        ]
        correct_result = {
            ("Microsoft Windows 10 Pro", "22H2"): 12,
            ("Microsoft Windows 11 Home", "22H2"): 4,
            ("Microsoft Windows 11 Pro", "22H2"): 71,
            ("Microsoft Windows 11 Pro", "23H2"): 2,
            ("Microsoft Windows 11 Business", "22H2"): 12,
            ("Microsoft Windows 11 Business", "23H2"): 2,
            ("macOS Ventura", "13"): 1,
            ("macOS Sonoma", "14"): 11,
        }

        func_result = convert_devices_for_cep_jotform(", ".join(os_list))
        self.assertEqual(len(func_result), len(correct_result))
        for key, value in func_result.items():
            self.assertEqual(correct_result[key], value)

    def test_mobile_parsing(self):
        os_list = "1x Pixel 6 Android 13, 1x iPhone 11 Pro iOS 16.5.1, 2x iPhone 10 Pro iOS 15"
        correct_result = {
            ("iOS", "16"): 1,
            ("Android", "13"): 1,
            ("iOS", "15"): 2,
        }
        self.assertEqual(convert_devices_for_cep_jotform(os_list), correct_result)

    def test_convert_device_for_cep_jotform(self):
        os_releases = [
            ("Android - 10 - HUAWEI [x12]", (("Android", "10"), 12)),
            ("Android - 10 - HUAWEI [x1]", (("Android", "10"), 1)),
            ("Android - 14 - google[x1]", (("Android", "14"), 1)),
            ("Android - 14 - Vendor ZVSU205 [x1]", (("Android", "14"), 1)),
            ("iOS - 16.5.1 - Apple [x1]", (("iOS", "16"), 1)),
            ("1x VOG-L29 Android 13.4", (("Android", "13"), 1)),
            ("5x VOG Android 10.1", (("Android", "10"), 5)),
            ("1x iPhone 11 Pro iOS 16.5.1", (("iOS", "16"), 1)),
            ("2x iPhone 10 Pro iOS 15", (("iOS", "15"), 2)),
            ("1x Pixel 6 Android 13", (("Android", "13"), 1)),
            ("macOS Sonoma - 14.1.1 - Apple [x1]", (("macOS Sonoma", "14"), 1)),
            ("1x Unknown model Android 15.3.2", (("Android", "15"), 1)),
        ]
        for os_release, expected in os_releases:
            self.assertEqual(
                convert_device_for_cep_jotform(os_release),
                expected
            )

    def test_convert_devices_for_cep_jotform_real(self):
        _input = """
            macOS Monterey - 12.5.1 - Apple [x1], macOS Monterey - 12.6 - Apple [x1], macOS Ventura - 13.0.1 - Apple [x2], macOS Ventura - 13.1 - Apple [x1], macOS Ventura - 13.3 - Apple [x1], macOS Ventura - 13.3.1 - Apple [x1], macOS Ventura - 13.5 - Apple [x1], macOS Ventura - 13.5.2 - Apple [x1], macOS Ventura - 13.6 - Apple [x2], macOS Ventura - 13.6.3 - Apple [x2], macOS Ventura - 13.6.4 - Apple [x1], macOS Sonoma - 14.0 - Apple [x8], macOS Sonoma - 14.1.2 - Apple [x5], macOS Sonoma - 14.2.1 - Apple [x13], macOS Sonoma - 14.3 - Apple [x1], macOS Sonoma - 14.3.1 - Apple [x16], macOS Sonoma - 14.4 - Apple [x15], macOS Sonoma - 14.4.1 - Apple [x23], 10.0.19044 - 21H2 - HP [x1], Windows 10 Enterprise - 21H2 -  [x1], macOS Ventura - 23.6.0 - Apple [x1], 10.0.19045 - 22H2 - HP [x1], 10.0.19045 - 22H2 - innotek GmbH [x1], 10.0.19045 - 22H2 - System manufacturer [x1], 10.0.19045 - 22H2 - VMware, Inc. [x3], Microsoft Windows 10 Pro - 22H2 - HP [x1], Microsoft Windows 10 Pro - 22H2 - System manufacturer [x1], Microsoft Windows 10 Pro - 22H2 - VMware, Inc. [x1], Microsoft Windows 11 Pro - 22H2 - HP [x1], macOS Sonoma - 23.1.0 - Apple [x2], macOS Sonoma - 23.2.0 - Apple [x1], macOS Sonoma - 23.3.0 - Apple [x1], macOS Sonoma - 23.4.0 - Apple [x1], 11.0.22631 - 23H2 - LENOVO [x3], Microsoft Windows 11 Pro - 23H2 - HP [x1], Microsoft Windows 11 Pro - 23H2 - LENOVO [x1]
         """
        func_result = convert_devices_for_cep_jotform(_input)
        self.assertEqual(len(func_result), 12)
        self.assertIn(("macOS Monterey", "12"), func_result)
        self.assertEqual(func_result[("macOS Monterey", "12")], 2)
        self.assertIn(("macOS Ventura", "13"), func_result)
        self.assertEqual(func_result[("macOS Ventura", "13")], 12)
        self.assertIn(("macOS Sonoma", "14"), func_result)
        self.assertEqual(func_result[("macOS Sonoma", "14")], 81)
        self.assertIn(("10.0.19044", "21H2"), func_result)
        self.assertEqual(func_result[("10.0.19044", "21H2")], 1)
        self.assertIn(("Windows 10 Enterprise", "21H2"), func_result)
        self.assertEqual(func_result[("Windows 10 Enterprise", "21H2")], 1)
        self.assertIn(("10.0.19045", "22H2"), func_result)
        self.assertEqual(func_result[("10.0.19045", "22H2")], 6)
        self.assertIn(("Microsoft Windows 10 Pro", "22H2"), func_result)
        self.assertEqual(func_result[("Microsoft Windows 10 Pro", "22H2")], 3)
        self.assertIn(("Microsoft Windows 11 Pro", "22H2"), func_result)
        self.assertEqual(func_result[("Microsoft Windows 11 Pro", "22H2")], 1)
        self.assertIn(("macOS Sonoma", "23"), func_result)
        self.assertEqual(func_result[("macOS Sonoma", "23")], 5)
        self.assertIn(("11.0.22631", "23H2"), func_result)
        self.assertEqual(func_result[("11.0.22631", "23H2")], 3)
        self.assertIn(("Microsoft Windows 11 Pro", "23H2"), func_result)
        self.assertEqual(func_result[("Microsoft Windows 11 Pro", "23H2")], 2)
        self.assertIn(("macOS Ventura", "23"), func_result)
        self.assertEqual(func_result[("macOS Ventura", "23")], 1)


class TestJotformURLBuilder(TestCase):
    maxDiff = None

    def setUp(self):
        self.user = UserFactory(email="<EMAIL>", first_name="John", last_name="Doe")
        self.organisation = OrganisationFactory(name="Jotform Org", pk=456)
        self.ce_v2024 = CertificationVersionFactory(
            type=CertificateTypeFactory(type=CYBER_ESSENTIALS),
            version_number=2024.0,
        )
        self.ce = OrganisationCertificationFactory(
            organisation=self.organisation,
            version=self.ce_v2024,
            free_of_charge=False,
            pk=144,
            renewal_start_date=datetime(2022, 5, 15)
        )
        CertificationSurveyFactory(certificate=self.ce)

    def test_init(self):
        """
        Test the initialization of JotformURLBuilder.
        """
        self.assertRaises(ValueError, JotformURLBuilder, None, None)
        self.assertRaises(ValueError, JotformURLBuilder, self.user, None)
        self.assertRaises(ValueError, JotformURLBuilder, None, self.organisation)
        builder = JotformURLBuilder(self.user, self.organisation)
        self.assertEqual(builder.user, self.user)
        self.assertEqual(builder.organisation, self.organisation)
        self.assertEqual(builder.ce, self.ce)
        self.organisation.ce_certification.delete()
        builder = JotformURLBuilder(self.user, self.organisation)
        self.assertIsNone(builder.ce)

    def test_get_jotform_quantity_option_value(self):
        builder = JotformURLBuilder(self.user, self.organisation)
        self.assertEqual(builder._get_jotform_quantity_option_value(1), "1")
        self.assertEqual(builder._get_jotform_quantity_option_value(3), "2-5")
        self.assertEqual(builder._get_jotform_quantity_option_value(10), "6-19")
        self.assertEqual(builder._get_jotform_quantity_option_value(30), "20-60")
        self.assertEqual(builder._get_jotform_quantity_option_value(100), "more than 60")
        self.assertEqual(builder._get_jotform_quantity_option_value(19), "6-19")
        self.assertEqual(builder._get_jotform_quantity_option_value(20), "20-60")
        self.assertEqual(builder._get_jotform_quantity_option_value(60), "20-60")

    def test_organisation_params(self):
        builder = JotformURLBuilder(self.user, self.organisation)
        params = builder._organisation_params
        self.assertEqual(params["organisationName"], "Jotform Org")
        self.assertEqual(params["orgid"], "GBR-ORG-456")
        self.assertEqual(params["partnerid"], f"GBR-PAR-{self.organisation.partner_id}")
        self.assertEqual(params["distributorid"], f"GBR-DIS-{self.organisation.partner.distributor_id}")
        self.assertFalse(params["orgHas"])
        with patch("organisations.models.Organisation.has_software_support", return_value=True):
            builder = JotformURLBuilder(self.user, self.organisation)
            params = builder._organisation_params
            self.assertTrue(params["orgHas"])
        self.assertEqual(params["doesThe"], "Yes, from CyberSmart")
        self.ce.free_of_charge = True
        self.ce.save()
        builder = JotformURLBuilder(self.user, self.organisation)
        params = builder._organisation_params
        self.assertEqual(params["doesThe"], "Yes, from another provider")
        self.assertEqual(params["certificateid"], "GBR-CER-144")

    def test_user_params(self):
        builder = JotformURLBuilder(self.user, self.organisation)
        params = builder._user_params
        self.assertEqual(params["emailAddress"], "<EMAIL>")
        self.assertEqual(params["nameOf98[first]"], "John")
        self.assertEqual(params["nameOf98[last]"], "Doe")
        self.assertEqual(params["contactid"], f"GBR-CON-{self.user.id}")
        self.assertEqual(params["accountid"], "GBR-ORG-456")

    def test_user_params_for_partner_user(self):
        PartnerUserFactory(user=self.user, partner=self.organisation.partner)
        builder = JotformURLBuilder(self.user, self.organisation)
        params = builder._user_params
        self.assertEqual(params["accountid"], f"GBR-PAR-{self.organisation.partner_id}")

    def test_user_params_for_distributor_user(self):
        DistributorUserFactory(user=self.user, distributor=self.organisation.partner.distributor)
        builder = JotformURLBuilder(self.user, self.organisation)
        params = builder._user_params
        self.assertEqual(params["accountid"], f"GBR-DIS-{self.organisation.partner.distributor_id}")

    def test_cyber_essentials_params_no_ce_certification(self):
        Mock(spec=self.organisation.ce_certification, return_value=None)
        builder = JotformURLBuilder(self.user, self.organisation)
        params = builder._cyber_essentials_params
        self.assertEqual(params, {})

    def test_equipment_url_params(self):
        builder = JotformURLBuilder(self.user, self.organisation)
        params = builder._equipment_url_params
        self.assertIn("OrganisationDeviceURL", params)
        url = params["OrganisationDeviceURL"]
        query_params = parse_qs(urlparse(url).query)

        # Check for the custom filter parameter
        self.assertIn(SurveyAnswerCustomFilter.parameter_name, query_params)
        self.assertEqual(
            query_params[SurveyAnswerCustomFilter.parameter_name][0],
            SurveyAnswerCustomFilter.CEP_DEVICE_INFO
        )

        # Check for the survey parameter
        self.assertIn("survey", query_params)
        self.assertEqual(query_params["survey"][0], str(self.ce.survey.id))


    def test_cyber_essentials_params_with_ce_certification(self):
        ce_mock = Mock(spec=self.ce)
        ce_mock.certified_date = datetime(2022, 5, 15)  # Assuming CE certification date
        ce_mock.is_version_2024_or_newer = True
        ce_mock.free_of_charge = False
        builder = JotformURLBuilder(self.user, self.organisation)
        builder.ce = ce_mock
        params = builder._cyber_essentials_params
        self.assertEqual(params["dateCyber[month]"], 5)
        self.assertEqual(params["dateCyber[day]"], 15)
        self.assertEqual(params["dateCyber[year]"], 2022)
        self.assertTrue(params["ceAfter2024"])
        self.assertTrue(params["isCybersmartIssued"])
        ce_mock.is_version_2024_or_newer = False
        ce_mock.free_of_charge = True
        params = builder._cyber_essentials_params
        self.assertFalse(params["ceAfter2024"])
        self.assertFalse(params["isCybersmartIssued"])

    @patch("django.conf.settings.JOTFORM_URL", "https://example.com")
    def test_build(self):
        builder = JotformURLBuilder(self.user, self.organisation)
        ce_mock = Mock(spec=self.ce)
        ce_mock.pk = 144
        ce_mock.certified_date = datetime(2022, 5, 15)
        ce_mock.is_version_2024_or_newer = True
        ce_mock.free_of_charge = False
        builder.ce = ce_mock
        builder._get_devices_in_scope = Mock(return_value={
            "numberOfDesktop": 4,
            "numberOfMobile": 3,
            "numberOfOther": 2,
            "numberOfThin": 1
        })

        jotform_url = builder.build()

        expected_params = {
            "organisationName": "Jotform Org",
            "orgid": "GBR-ORG-456",
            "orgHas": False,
            "partnerid": f"GBR-PAR-{self.organisation.partner_id}",
            "distributorid": f"GBR-DIS-{self.organisation.partner.distributor_id}",
            "doesThe": "Yes, from CyberSmart",
            "certificateid": "GBR-CER-144",
            "emailAddress": "<EMAIL>",
            "nameOf98[first]": "John",
            "nameOf98[last]": "Doe",
            "contactid": f"GBR-CON-{self.user.id}",
            "accountid": f"GBR-ORG-{self.organisation.id}",
            "dateCyber[month]": 5,
            "dateCyber[day]": 15,
            "dateCyber[year]": 2022,
            "ceAfter2024": True,
            "isCybersmartIssued": True
        }
        expected_params.update(builder._equipment_url_params)
        self.assertDictEqual(builder._build_dict(), expected_params)

        expected_url = urljoin('https://example.com', f"?{urlencode(expected_params)}")
        self.assertEqual(jotform_url, expected_url)

    def test_get_cep_jotform_id(self):
        """
        Test the get_cep_jotform_id function.
        """
        with patch("django.conf.settings.JOTFORM_URL", "https://example.com/*********"):
            self.assertEqual(get_cep_jotform_id(), "*********")

        with patch("django.conf.settings.JOTFORM_URL", "https://example-something.co.uk/344455/"):
            self.assertEqual(get_cep_jotform_id(), "344455")
