import logging
import os
import shutil
import subprocess
import traceback
from io import BytesIO
from shutil import rmtree
from typing import List, Tuple
import sentry_sdk
from django.conf import settings
from django.contrib.staticfiles import finders
from django.core.files import File
from fdfgen import forge_fdf
from pypdf import Pdf<PERSON>eader, PdfWriter
from reportlab.lib.utils import ImageReader
from reportlab.pdfgen import canvas

logger = logging.getLogger(__name__)


class PDFGenerationError(Exception):
    """Custom exception for PDF generation errors"""
    pass


class GenerateDeclaration:
    """
    Generates a declaration PDF for a given survey declaration and insurance status.
    """
    INSURANCE_TEMPLATE = "pdf/insurance_declaration2018c.pdf"
    NO_INSURANCE_TEMPLATE = "pdf/no_insurance_declaration2018b.pdf"

    def __init__(self, declaration, insurance: bool):
        self.declaration = declaration
        self.insurance = insurance
        self.external_uuid = declaration.survey.certificate.organisation.external_uuid
        template_name = self.INSURANCE_TEMPLATE if insurance else self.NO_INSURANCE_TEMPLATE
        self.pdf_template = finders.find(template_name)
        if not self.pdf_template:
            raise PDFGenerationError(f"PDF template not found: {template_name}")

        # Setup unique data directory
        self.data_dir = os.path.join(settings.STATIC_ROOT, 'pdf', 'data', str(self.external_uuid))
        os.makedirs(self.data_dir, exist_ok=True)

    @staticmethod
    def _check_dependencies() -> None:
        if not shutil.which('pdftk'):
            raise PDFGenerationError('Pdftk server is not installed. Cannot generate PDF.')

    def generate_file_paths(self) -> dict:
        """Generate all file paths needed for PDF generation"""
        base_name = f'declaration-{self.external_uuid}'
        return {
            'fdf': os.path.join(self.data_dir, f'form-{base_name}.fdf'),
            'pre_final': os.path.join(self.data_dir, f'{base_name}-without-signature.pdf'),
            'final': os.path.join(self.data_dir, f'{base_name}-signed.pdf')
        }

    def create_signature_canvas(self) -> BytesIO:
        img_buffer = BytesIO()
        try:
            with self.declaration.signature.file.open('rb') as signature_file:
                img_canvas = canvas.Canvas(img_buffer)
                self.draw_signature_on_canvas(img_canvas, signature_file)
                img_buffer.seek(0)
                return img_buffer
        except Exception as e:
            img_buffer.close()
            raise PDFGenerationError("Failed to create signature canvas") from e

    def add_signature_to_pdf(self, pre_final_pdf_path: str, final_pdf_path: str) -> None:
        if not self.declaration.signature:
            raise PDFGenerationError(f'Declaration {self.declaration.id} has no signature')

        # Try up to 2 times to create signature canvas
        img_buffer = None
        last_exc = None
        for attempt in range(2):
            try:
                img_buffer = self.create_signature_canvas()
                break
            except PDFGenerationError as e:
                last_exc = e
                img_buffer = None
        else:
            # All attempts failed
            raise PDFGenerationError("Failed to generate signature image after retries") from last_exc

        try:
            self.merge_signature_with_pdf(pre_final_pdf_path, final_pdf_path, img_buffer)
        finally:
            img_buffer.close()

    def draw_signature_on_canvas(self, img_canvas: canvas.Canvas, signature_file) -> None:
        """Draw signature on canvas using an open file object"""
        img_path = ImageReader(signature_file)
        height = 190 if self.insurance else 257
        img_canvas.drawImage(img_path, 120, height, 200, 60)
        img_canvas.save()

    @staticmethod
    def merge_signature_with_pdf(pre_final_pdf_path: str, final_pdf_path: str, img_buffer: BytesIO) -> None:
        """Merge signature with PDF using in-memory approach to avoid stream closure issues"""
        pdf_data = None
        try:
            with open(pre_final_pdf_path, 'rb') as pdf_file:
                pdf_data = BytesIO(pdf_file.read())

            pdf_reader = PdfReader(pdf_data)
            page = pdf_reader.pages[0]
            img_buffer.seek(0)

            overlay_reader = PdfReader(img_buffer)
            overlay = overlay_reader.pages[0]
            page.merge_page(overlay)

            output = PdfWriter()
            output.add_page(page)

            with open(final_pdf_path, 'wb') as output_file:
                output.write(output_file)

        except Exception as e:
            raise PDFGenerationError(f"Failed to merge signature with PDF: {e}")
        finally:
            if pdf_data:
                pdf_data.close()

    def get_fields(self) -> List[Tuple[str, str]]:
        """
        Returns the fields that will be filled in the PDF.
        """
        return [
            ('company_name', self.declaration.survey.certificate.organisation.name),
            ('signer_name', self.declaration.declaration_name),
            ('signer_title', self.declaration.declaration_job),
            ('date', str(self.declaration.declaration_date))
        ]

    def create_fdf_file(self, fdf_path: str) -> None:
        fields = self.get_fields()
        fdf = forge_fdf('', fields, [], [], [])
        with open(fdf_path, 'wb') as fdf_file:
            fdf_file.write(fdf)

    def fill_pdf_form(self, fdf_path: str, pre_final_pdf_path: str) -> None:
        command = ['pdftk', self.pdf_template, 'fill_form', fdf_path, 'output', pre_final_pdf_path, 'flatten']
        status = subprocess.call(command)
        if status != 0:
            raise PDFGenerationError(f'pdftk server exit with status code: {status}')

    def save_pdf_to_declaration(self, final_pdf_path: str) -> None:
        if not os.path.exists(final_pdf_path):
            raise PDFGenerationError(f"Final PDF file not found: {final_pdf_path}")

        try:
            with open(final_pdf_path, 'rb') as pdf_file:
                self.declaration.pdf.save(
                    f'declaration-{self.external_uuid}.pdf',
                    File(pdf_file),
                    save=True
                )
        except Exception as e:
            raise PDFGenerationError(f"Failed to save PDF to declaration: {e}")

    def run(self) -> None:
        """Main method to generate the declaration PDF"""
        file_paths = None
        try:
            self._check_dependencies()
            file_paths = self.generate_file_paths()
            self.create_fdf_file(file_paths['fdf'])
            self.fill_pdf_form(file_paths['fdf'], file_paths['pre_final'])
            # Add signature if required
            if self.declaration.survey.certificate.version.declaration:
                self.add_signature_to_pdf(file_paths['pre_final'], file_paths['final'])
                final_path = file_paths['final']
            else:
                final_path = file_paths['pre_final']

            self.save_pdf_to_declaration(final_path)

        except PDFGenerationError:
            raise
        except Exception as error:
            error_msg = f'Cannot generate declaration pdf for id: {self.declaration.pk}. ({error})'
            sentry_sdk.set_extra("stack_trace", traceback.format_exc())
            if file_paths:
                sentry_sdk.set_extra("file_paths", file_paths)
            sentry_sdk.capture_exception(error)
            raise PDFGenerationError(error_msg)
        finally:
            if os.path.exists(self.data_dir):
                rmtree(self.data_dir, ignore_errors=True)
