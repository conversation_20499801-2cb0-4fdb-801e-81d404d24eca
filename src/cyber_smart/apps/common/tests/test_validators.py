from unittest.mock import patch

from django import forms
from django.test import TestCase

from common.validators import pervade_safe_char_validator, safe_char_validator, PERVADE_SAFE_CHARACTERS_VERBOSE


class SafeCharValidatorTestCase(TestCase):
    SPECIAL_CHARACTERS = "åäöąćęłńóśźżüßàâæçéèêëîïôœùûÿïëöìíò"
    """
    Tests the behavior of the safe character validator.

    - In EU regions, all Unicode characters (except control characters) are allowed.
    - In non-EU regions, only letters, digits, and a specific set of safe characters are allowed.
    """
    @staticmethod
    def create_test_form(*args, **kwargs):
        """
        Helper method to create a test form with the character validators.
        """
        class TestForm(forms.Form):
            pervade_input = forms.CharField(validators=[pervade_safe_char_validator])
            any_text_input = forms.CharField(validators=[safe_char_validator])

        return TestForm(*args, **kwargs)

    @patch("common.validators.is_eu_geo", return_value=False)
    def test_safe_char_validator_non_eu_environment(self, _):
        """
        Test safe char validator in non EU environment.
        """
        form = self.create_test_form(data={"pervade_input": self.SPECIAL_CHARACTERS, "any_text_input": self.SPECIAL_CHARACTERS})
        self.assertFalse(form.is_valid())
        self.assertIn(PERVADE_SAFE_CHARACTERS_VERBOSE, form.errors["pervade_input"][0])

    @patch("common.validators.is_eu_geo", return_value=True)
    def test_safe_char_validator_eu_environment(self, _):
        """
        Test safe char validator in EU environment.
        """
        form = self.create_test_form(data={"pervade_input": self.SPECIAL_CHARACTERS, "any_text_input": self.SPECIAL_CHARACTERS})
        self.assertTrue(form.is_valid())

    @patch("common.validators.is_eu_geo", return_value=False)
    def test_safe_char_validator_special_chars(self, _):
        texts = [
            ('(such as !@#$^&*);', False),
            ('(!@#$^&*); (bar)', False),
            ('(foo)_', False),
            ('[foo]_', False),
            ('(foo) bar', True),
            ('(foo)bar;', True),
            ('foo bar', True),
            ('foo bar [something] !@#$%^&*_+{}|:"<>?=', True),
            ('^(foo) bar;', True),
        ]
        for text, expected_validity in texts:
            with self.subTest(text):
                form = self.create_test_form(data={"pervade_input": text, "any_text_input": "any"})
                self.assertEqual(form.is_valid(), expected_validity)
                if not expected_validity:
                    self.assertIn(PERVADE_SAFE_CHARACTERS_VERBOSE, form.errors["pervade_input"][0])
