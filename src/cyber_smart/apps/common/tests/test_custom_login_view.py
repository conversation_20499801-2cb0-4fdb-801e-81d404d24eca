from django.conf import settings
from django.test import TestCase, Client, RequestFactory
from django.urls import reverse
from django.contrib.auth import get_user_model

from common.views import csrf_failure_view

User = get_user_model()


class RedirectIfAuthenticatedLoginViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.login_url = reverse('account_login')
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user.profile.onboarding_completed = True
        self.user.profile.skip_payment = True
        self.user.profile.save()

    def test_unauthenticated_user_sees_login_form(self):
        """Test that unauthenticated users can access the login page"""
        response = self.client.get(self.login_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Log in')

    def test_authenticated_user_redirected_with_message(self):
        """Test that authenticated users are redirected with a message"""
        # Log in the user
        self.client.force_login(self.user)

        # Try to access login page
        response = self.client.get(self.login_url, follow=True)

        # Should be redirected eventually to home page or dashboard
        # The first redirect is from login to '/', then middleware might redirect further
        self.assertTrue(len(response.redirect_chain) > 0)
        self.assertEqual(response.redirect_chain[0][0], '/')  # First redirect should be to home

        # Check for the message
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), f'You are logged in as {self.user.email}. To use a different account, please logout first.')
        self.assertIn('info', messages[0].level_tag)

    def test_authenticated_user_post_to_login_redirected(self):
        """Test that authenticated users posting to login are redirected"""
        # Log in the user
        self.client.force_login(self.user)

        # Try to POST to login page (simulating form submission)
        response = self.client.post(self.login_url, {
            'login': '<EMAIL>',
            'password': 'testpass123'
        })

        # Should be redirected to home page
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, '/')


class LoginPageSecurityTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.login_url = reverse('account_login')

    def test_login_page_contains_csrf_token(self):
        """Test that the login page contains a CSRF token"""
        response = self.client.get(self.login_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'csrfmiddlewaretoken')


    def test_multi_tab_scenario_graceful_handling(self):
        """Test that multi-tab scenarios are handled gracefully"""
        # Simulate Tab 1: Get login page
        tab1_client = Client()
        tab1_response = tab1_client.get(self.login_url)
        self.assertEqual(tab1_response.status_code, 200)

        # Simulate Tab 2: Get login page, login, then logout
        tab2_client = Client()
        user = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123'
        )

        # Tab 2 login and logout
        tab2_client.force_login(user)
        tab2_client.logout()

        # Tab 1 should still be able to access the login page
        # (CSRF failures will be handled gracefully by our custom view)
        tab1_response_after = tab1_client.get(self.login_url)
        self.assertEqual(tab1_response_after.status_code, 200)
        self.assertContains(tab1_response_after, 'csrfmiddlewaretoken')


class CSRFFailureViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.login_url = reverse('account_login')

    def test_csrf_failure_handling_setup(self):
        """Test that our CSRF failure view is properly configured"""
        self.assertEqual(settings.CSRF_FAILURE_VIEW, 'common.views.csrf_failure_view')

    def test_csrf_failure_view_function_logic(self):
        """Test the logic of our CSRF failure view function"""
        factory = RequestFactory()

        # Test login page path
        request = factory.post('/login/')
        response = csrf_failure_view(request, reason="CSRF token missing")
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, '/login/')

        # Test other page path - should use default CSRF failure
        request = factory.post('/some-other-page/')
        response = csrf_failure_view(request, reason="CSRF token missing")
        self.assertEqual(response.status_code, 403)
