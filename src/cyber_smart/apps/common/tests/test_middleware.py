from unittest.mock import patch, Mock

from django.conf import settings
from django.db.migrations.executor import MigrationExecutor
from django.http import HttpResponse, HttpRequest
from django.test import TestCase, RequestFactory
from django.urls import reverse
from django.utils import translation

from accounts.factories import UserFactory
from common.middleware import HealthCheckMiddleware, CustomSilkyMiddleware, CustomLocaleMiddleware
from common.models import ProjectSettings
from organisations.factories import OrganisationAdminFactory, OrganisationFactory
from partners.factories import PartnerUserFactory


class HealthCheckMiddlewareTest(TestCase):
    def setUp(self) -> None:
        self.factory = RequestFactory()
        self.middleware = HealthCheckMiddleware(lambda req: HttpResponse('next'))

    def test_pending_migrations(self) -> None:
        request: HttpRequest = self.factory.get('/health-check')
        with patch.object(MigrationExecutor, "migration_plan", return_value=[('migration',)]):
            response = self.middleware(request)
        self.assertEqual(response.status_code, 503)
        self.assertEqual(response.content, b'Pending migrations')

    def test_no_pending_migrations(self) -> None:
        request: HttpRequest = self.factory.get('/health-check')
        with patch.object(MigrationExecutor, "migration_plan", return_value=[]):
            response = self.middleware(request)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content, b'ok')

@patch.object(CustomSilkyMiddleware, 'is_api_request_logging_enabled', return_value=True)
class CustomSilkyMiddlewareTest(TestCase):
    def setUp(self) -> None:
        self.factory = RequestFactory()
        self.get_response_mock = Mock(return_value=HttpResponse('Ok', status=200))
        with patch.object(settings, 'SILKY_ENABLED', True):
            self.middleware = CustomSilkyMiddleware(self.get_response_mock)
        ProjectSettings.objects.create(record_api_requests=True)
        self.request = self.factory.get(reverse('api:dashboard:manage-users', kwargs={'org_id': '00000000-0000-0000-0000-000000000000', 'pk': '1'}))

    @patch.object(CustomSilkyMiddleware, 'process_request')
    @patch.object(CustomSilkyMiddleware, 'process_response')
    def test_request_processed_once(self, mock_process_response, mock_process_request, _is_api_request_logging_enabled) -> None:
        response = self.middleware(self.request)
        self.assertEqual(response.status_code, 200)
        mock_process_request.assert_called_once_with(self.request)
        mock_process_response.assert_called_once_with(self.request, response)

    @patch.object(CustomSilkyMiddleware, 'process_request')
    @patch.object(CustomSilkyMiddleware, 'process_response')
    def test_request_not_processed_for_successful_response_on_production(self, mock_process_response, mock_process_request, _is_api_request_logging_enabled) -> None:
        self.get_response_mock.return_value = HttpResponse('ok', status=200)
        with patch.object(settings, 'IS_PROD', False):
            response = self.middleware(self.request)
            self.assertEqual(response.status_code, 200)
            mock_process_request.assert_called_once_with(self.request)
            mock_process_response.assert_called_once_with(self.request, response)


class CustomLocaleMiddlewareTest(TestCase):
    def setUp(self) -> None:
        self.factory = RequestFactory()
        self.middleware = CustomLocaleMiddleware(lambda req: HttpResponse('next'))
        self.request = self.factory.get('/')
        self.user = UserFactory()
        self.request.user = self.user

    def test_process_request_org_admin(self) -> None:
        # Simulate a request from an organisation admin that had their default language changed
        admin = OrganisationAdminFactory(user=self.user)
        self.middleware.process_request(self.request)
        # first it should match default language
        self.assertEqual(self.request.LANGUAGE_CODE, settings.LANGUAGE_CODE)
        self.assertEqual(translation.get_language(), settings.LANGUAGE_CODE)
        translation.get_language_from_request(self.request, check_path=True)
        new_language = 'sv'
        admin.organisation.settings.default_language = new_language
        admin.organisation.settings.save()
        self.middleware.process_request(self.request)
        # then it should match the organisation's default language
        self.assertEqual(self.request.LANGUAGE_CODE, new_language)

    def test_process_request_partner_user(self):
        # Simulate a request from a partner user that had their default language changed
        partner_user = PartnerUserFactory(user=self.user)
        self.middleware.process_request(self.request)
        # first it should match default language
        self.assertEqual(self.request.LANGUAGE_CODE, settings.LANGUAGE_CODE)
        self.assertEqual(translation.get_language(), settings.LANGUAGE_CODE)
        new_language = 'sv'
        partner_user.partner.default_language = new_language
        partner_user.partner.save()
        self.middleware.process_request(self.request)
        # then it should match the partner's default language
        self.assertEqual(self.request.LANGUAGE_CODE, new_language)

    def test_process_request_partner_user_diff_org_languages(self):
        # Simulate a request from a partner user that has their own organisation with a different default language
        # than the partner default language
        partner_user = PartnerUserFactory(user=self.user)
        org_1 = OrganisationFactory(
            partner=partner_user.partner,
            is_partner_org=True,
        )
        org_2 = OrganisationFactory(
            partner=partner_user.partner,
        )
        org_1.settings.default_language = 'it'
        org_1.settings.save()
        org_2.settings.default_language = settings.LANGUAGE_CODE
        org_2.settings.save()
        partner_user.partner.default_language = 'sv'
        partner_user.partner.save()
        self.middleware.process_request(self.request)
        # it should match the partner's default language even if the partner's own org has a different language
        self.assertEqual(self.request.LANGUAGE_CODE, partner_user.partner.default_language)

    def test_process_request_impersonating(self):
        """ When impersonating we want the language to be the same as their browser default """
        impersonator_language = 'sv'
        admin = OrganisationAdminFactory(user=self.user)
        self.assertNotEqual(impersonator_language, admin.organisation.settings.default_language)
        self.request.META['HTTP_ACCEPT_LANGUAGE'] = impersonator_language
        self.request.user.is_impersonate = True
        self.middleware.process_request(self.request)
        # then it should match the impersonator's default language
        self.assertEqual(self.request.LANGUAGE_CODE, impersonator_language)

    def test_process_request_impersonating_no_header(self):
        """ If there is no header, then it should be default platform language """
        admin = OrganisationAdminFactory(user=self.user)
        admin.organisation.settings.default_language = 'sv'
        admin.organisation.settings.save()
        self.request.META['HTTP_ACCEPT_LANGUAGE'] = ''
        self.request.user.is_impersonate = True
        self.middleware.process_request(self.request)
        # then it should match the platform's default language
        self.assertEqual(self.request.LANGUAGE_CODE, settings.LANGUAGE_CODE)
