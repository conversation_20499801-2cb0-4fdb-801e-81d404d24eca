import unittest
from unittest.mock import patch
from django.test import TestCase
from django.conf import settings


from common.views import PostmarkWebHookView
from organisations.factories import OrganisationCertificationFactory

class TestPostmarkWebHookViewProductionLegacy(unittest.TestCase):

    def setUp(self) -> None:
        self.postmark_view = PostmarkWebHookView()
        self.html_body = """
        <html xmlns="http://www.w3.org/1999/xhtml">
            <head>
                <title>BlockMark Registry</title>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
                <meta http-equiv="X-UA-Compatible" content="IE=edge" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <meta http-equiv="imagetoolbar" content="no"/>
                <style type="text/css">
                    a {
                        color: #686868;
                    }
                </style>
            </head>
            <body id="body" style="margin: 0; padding: 0; margin-top:30px; font-family:'Montserrat', Helvetica, sans-serif; background-color: #f2f2f2;">
                <a href="https://registry.blockmarktech.com/on-boarding/router/?key=test123-324f" class="button is-primary">Register to Manage Your Certificate</a>
                    <p align=3D"justify">
        The buttons/links on this message are associated with your email ad=
dress, so please do not forward this message to anyone else. If you are not=
 the holder of the certificate=20
        (for example you are a colleague or consultant) please use the forw=
arding button/link below.
    </p>
        <a style=3D"margin-top: 10px;" href=3D"http://url4562.iasme.co.uk/l=
s/click?upn=3Du0" class=3D"button is-primary">Forward this Certificate to A=
nother Email</a>
        <p>or forward this certificate by clicking:</p>
        <p>
            <a href=3D"http://url4562.iasme.co.uk/ls/click?upn=3D">https://registry.blockmarktech.com/on-boarding/forward-email/?key=3p23zU</a>
        </p>
            </body>
        </html>
        """

    def test_get_blockmark_registration_url(self) -> None:
        expected_url = "https://registry.blockmarktech.com/on-boarding/router/?key=test123-324f"
        actual_url = self.postmark_view.get_blockmark_registration_url(self.html_body)
        self.assertEqual(actual_url, expected_url)

    def test_get_blockmark_forwarding_url(self) -> None:
        expected_url = "https://registry.blockmarktech.com/on-boarding/forward-email/?key=3p23zU"
        actual_url = self.postmark_view.get_blockmark_forwarding_url(self.html_body)
        self.assertEqual(actual_url, expected_url)


class TestPostmarkWebHookViewStaging(unittest.TestCase):

    def setUp(self) -> None:
        self.postmark_view = PostmarkWebHookView()
        self.html_body = """
        <html xmlns="http://www.w3.org/1999/xhtml">
            <head>
                <title>BlockMark Registry</title>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
                <meta http-equiv="X-UA-Compatible" content="IE=edge" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <meta http-equiv="imagetoolbar" content="no"/>
                <style type="text/css">
                    a {
                        color: #686868;
                    }
                </style>
            </head>
            <body id="body" style="margin: 0; padding: 0; margin-top:30px; font-family:'Montserrat', Helvetica, sans-serif; background-color: #f2f2f2;">
                <a href="https://wyche.registry.blockmarktech.com/on-boarding/router/?key=test123-324f" class="button is-primary">Register to Manage Your Certificate</a>
                    <p align=3D"justify">
        The buttons/links on this message are associated with your email ad=
dress, so please do not forward this message to anyone else. If you are not=
 the holder of the certificate=20
        (for example you are a colleague or consultant) please use the forw=
arding button/link below.
    </p>
        <a style=3D"margin-top: 10px;" href=3D"http://url4562.iasme.co.uk/l=
s/click?upn=3Du0" class=3D"button is-primary">Forward this Certificate to A=
nother Email</a>
        <p>or forward this certificate by clicking:</p>
        <p>
            <a href=3D"http://url4562.iasme.co.uk/ls/click?upn=3D">https://wyche.registry.blockmarktech.com/on-boarding/forward-email/?key=3p23zU</a>
        </p>
            </body>
        </html>
        """

    def test_get_blockmark_registration_url(self) -> None:
        expected_url = "https://wyche.registry.blockmarktech.com/on-boarding/router/?key=test123-324f"
        actual_url = self.postmark_view.get_blockmark_registration_url(self.html_body)
        self.assertEqual(actual_url, expected_url)

    def test_get_blockmark_forwarding_url(self) -> None:
        expected_url = "https://wyche.registry.blockmarktech.com/on-boarding/forward-email/?key=3p23zU"
        actual_url = self.postmark_view.get_blockmark_forwarding_url(self.html_body)
        self.assertEqual(actual_url, expected_url)


class TestSaveBlockmarkUrls(TestCase):
    def setUp(self):
        self.postmark_view = PostmarkWebHookView()
        self.html_body = """
        <html>
            <body>
                <a href="https://registry.blockmarktech.com/on-boarding/router/?key=test123">Register Certificate</a>
                <a href="https://registry.blockmarktech.com/on-boarding/forward-email/?key=test456">Forward Certificate</a>
            </body>
        </html>
        """
        self.certification = OrganisationCertificationFactory()

    def test_save_blockmark_urls_certificate(self):
        self.postmark_view.save_blockmark_urls(self.certification, self.html_body, certification_number="test123")

        self.certification.refresh_from_db()
        issued_certification = self.certification.issued_certification

        self.assertEqual(
            issued_certification.blockmark_certificate_registration_url,
            "https://registry.blockmarktech.com/on-boarding/router/?key=test123"
        )
        self.assertEqual(
            issued_certification.blockmark_certificate_forwarding_url,
            "https://registry.blockmarktech.com/on-boarding/forward-email/?key=test456"
        )
        self.assertEqual(issued_certification.number, "test123")

    def test_save_blockmark_urls_insurance(self):
        self.postmark_view.save_blockmark_urls(self.certification, self.html_body, certification_number="test123", is_insurance=True)

        self.certification.refresh_from_db()
        issued_certification = self.certification.issued_certification

        self.assertEqual(
            issued_certification.blockmark_evidence_of_insurance_registration_url,
            "https://registry.blockmarktech.com/on-boarding/router/?key=test123"
        )
        self.assertEqual(
            issued_certification.blockmark_evidence_of_insurance_forwarding_url,
            "https://registry.blockmarktech.com/on-boarding/forward-email/?key=test456"
        )
        self.assertEqual(issued_certification.insurance_number, "test123")

    @patch('common.views.logger.error')
    def test_save_blockmark_urls_no_urls(self, mock_logger):
        html_body_no_urls = "<html><body>No URLs here</body></html>"
        self.postmark_view.save_blockmark_urls(self.certification, html_body_no_urls, certification_number="test123")

        self.certification.refresh_from_db()
        issued_certification = self.certification.issued_certification

        self.assertEqual(issued_certification.blockmark_certificate_registration_url, "")
        self.assertEqual(issued_certification.blockmark_certificate_forwarding_url, "")
        expected_calls = [
            "Postmark webhook failed to retrieve certificate registration URL. "
            f"Certification: {self.certification.pk}. Maybe something in the inbound email template changed.",
            "Postmark webhook failed to retrieve certificate forwarding URL. "
            f"Certification: {self.certification.pk}. Maybe something in the inbound email template changed."
        ]

        actual_calls = [call.args[0] for call in mock_logger.call_args_list]

        self.assertCountEqual(actual_calls, expected_calls)
        self.assertEqual(mock_logger.call_count, 2)

class TestPostmarkWebHookViewProduction(unittest.TestCase):

    def setUp(self) -> None:
        self.postmark_view = PostmarkWebHookView()
        self.html_body = """
        <html xmlns="http://www.w3.org/1999/xhtml">
            <head>
                <title>BlockMark Registry</title>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
                <meta http-equiv="X-UA-Compatible" content="IE=edge" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <meta http-equiv="imagetoolbar" content="no"/>
                <style type="text/css">
                    a {
                        color: #686868;
                    }
                </style>
            </head>
            <body id="body" style="margin: 0; padding: 0; margin-top:30px; font-family:'Montserrat', Helvetica, sans-serif; background-color: #f2f2f2;">
                <a style="margin-top: 10px; border-radius: 20px; text-transform: none; font-size: 16px;"
                   href="http://url4562.iasme.co.uk/ls/click?upn=3Du001"
                   class="button is-primary">Register to Manage Your Certificate</a>
                <hr style="margin: 1.5rem 0; border-top: 0 solid">
                <p style="text-align: justify; margin-top: 0;">
                    The buttons/links on this message are associated with your email address, so please do not forward this
                    message to anyone else. If you are not the holder of the certificate
                    (for example you are a colleague or consultant) please use the forwarding button/link below.
                </p>
                <a style="margin-top: 10px; margin-bottom: 1.5rem; border-radius: 20px; text-transform: none; font-size: 16px;"
                   href="http://url4562.iasme.co.uk/ls/click?upn=3Du002"
                   class="button is-primary">Forward this Certificate to Another Email</a>
            </body>
        </html>
        """

    def test_get_blockmark_registration_url(self) -> None:
        expected_url = "http://url4562.iasme.co.uk/ls/click?upn=3Du001"
        actual_url = self.postmark_view.get_blockmark_registration_url(self.html_body)
        self.assertEqual(actual_url, expected_url)

    def test_get_blockmark_forwarding_url(self) -> None:
        expected_url = "http://url4562.iasme.co.uk/ls/click?upn=3Du002"
        actual_url = self.postmark_view.get_blockmark_forwarding_url(self.html_body)
        self.assertEqual(actual_url, expected_url)


    def test_is_certificate_email_for_cyber_essentials(self) -> None:
        # The 1 before the @ is the certificate type
        to_email = "<EMAIL>"
        self.assertTrue(self.postmark_view._is_certificate_email(
            "Blockmark Registry | cyber e test", to_email
        ))
        self.assertTrue(self.postmark_view._is_certificate_email(
            "Blockmark Registry | ce test", to_email
        ))

        self.assertTrue(self.postmark_view._is_certificate_email(
            "Blockmark Registry | Cyber Essentials certificate", to_email
        ))
        self.assertTrue(self.postmark_view._is_certificate_email(
            "Cyber Essentials certificate", to_email
        ))

        # Test invalid certificate email subjects
        self.assertFalse(self.postmark_view._is_certificate_email(
            "Blockmark Registry | evidence of insurance", to_email
        ))
        self.assertFalse(self.postmark_view._is_certificate_email(
            "some other subject", to_email
        ))

    def test_is_certificate_email_for_cyber_essentials_plus(self) -> None:
        # Notice the 2 in the email address as this is the Cyber Essentials Plus certificate
        to_email = "<EMAIL>"
        self.assertTrue(self.postmark_view._is_certificate_email(
            "Cyber Essentials Plus certificate", to_email
        ))
        self.assertTrue(self.postmark_view._is_certificate_email(
            "Blockmark Registry | Cyber Essentials Plus certificate", to_email
        ))

    def test_is_insurance_email(self) -> None:
        # Test valid insurance email subjects
        self.assertTrue(self.postmark_view._is_insurance_email(
            "BlockMark Registry | Evidence of Insurance for Eligible Cyber Essentials Certificate Holders certificate"
        ))
        self.assertTrue(self.postmark_view._is_insurance_email(
            "Evidence of Insurance for Eligible Cyber Essentials Certificate Holders certificate"
        ))

        # Test invalid insurance email subjects
        self.assertFalse(self.postmark_view._is_insurance_email(
            "Blockmark Registry | cyber e test"
        ))
        self.assertFalse(self.postmark_view._is_insurance_email(
            "some other subject"
        ))

    def test_parse_contributor_email(self):
        parameters = [
            (
                "<EMAIL>",
                ("1b9b9221-db51-4d66-8939-00ac98549904", "2024.0", 1)
            ),
            (
                "<EMAIL>, <EMAIL>",
                ("17b43bb9-3711-4893-9eed-2c53db5d7c03", "2024.0", 2)
            ),
            (
                '"<EMAIL>" <<EMAIL>>',
                ("1b9b9221-db51-4d66-8939-00ac98549904", "2024.0", 1)
            ),
        ]
        for contributor_email, expected_result in parameters:
            with self.subTest(contributor_email=contributor_email):
                org_external_uuid, version_number, certification_type = self.postmark_view.parse_contributor_email(contributor_email)
                self.assertEqual(org_external_uuid, expected_result[0])
                self.assertEqual(version_number, expected_result[1])
                self.assertEqual(certification_type, expected_result[2])

    def test_parse_contributor_email_invalid(self):
        contributor_email = "invalid-email-format"
        with self.assertRaises(ValueError):
            self.postmark_view.parse_contributor_email(contributor_email)


class TestPostmarkWebHookViewForInsuranceProduction(unittest.TestCase):

    def setUp(self) -> None:
        self.postmark_view = PostmarkWebHookView()
        with open(f'{settings.SRC_DIR}/cyber_smart/apps/common/tests/insurance_email.html', 'r') as f:
            self.html_body = f.read()

    def test_get_blockmark_registration_url(self) -> None:
        expected_url = "http://url4562.iasme.co.uk/ls/click?upn=u001.Y8xUkrZpaqklG1-2FV-2FMo7MlnNN7VlzIQZCu2rM4uZxLjwYSZjQDMqNvHQrPj2LfjiNp40g7BcBOfJIRcVX7Toe07j5iFP-2B3rYeDV2TfR3CDA657q6Si7fKaZWWHir8bZudmOaYj4aenRgjMvFRpTbnuEwqWBuk0EDku-2F6IOAWn3A-3DY8h7_wxPcbYw-2BP0Kqu-2Bwqr2ka-2BroPJdJVoUJRA2AYaKB-2BXE12h5knCOlf12VyihvAXw2gkCvQ9q9SSX4WfpDIV1ej-2B5PgYQU7AwOfeCSJfhcJjXUzDkvIcDLz3dkTkUvTwLTNPR1IDhciDHzpVZMHSIVeSJ3Z-2BgrZVaD4B6O-2FZT6jHj0-2FDN5gHDgu6kOa95MNnMOmua1zvxFU-2FoEp19BwroOMFx9uCFki4Q3J-2BWl8gPcFemSH-2BfvcWpY9zlHdjnDB4fTnCqlNejKb-2BXARZPjzGjvuED-2BlZdM8MUtSVI1iCH67xf2LJjfgnedW4iLkixF47HaH3NTQMaFHybh4Y0VZsMZ3Z7-2FpAmhE9AUm0EhGG61r0bHyIIfpDpIgGsp3oVoalqsN"
        self.assertEqual(self.postmark_view.get_blockmark_registration_url(self.html_body), expected_url)

    def test_get_blockmark_forwarding_url(self) -> None:
        expected_url = "http://url4562.iasme.co.uk/ls/click?upn=u001.Y8xUkrZpaqklG1-2FV-2FMo7MlnNN7VlzIQZCu2rM4uZxLjwYSZjQDMqNvHQrPj2LfjifL4GCAUIKC9DRXPgMmFI5aoKuxFLu4CcQEs-2B1tZ-2FrR1CdqjOuXqYNiCF3YQ2yl5H1DpEaH5JM2YbjHLYnmDtqggqlFwY5-2BTJa-2FrMi8Ts4HU-3DrBvY_wxPcbYw-2BP0Kqu-2Bwqr2ka-2BroPJdJVoUJRA2AYaKB-2BXE12h5knCOlf12VyihvAXw2gkCvQ9q9SSX4WfpDIV1ej-2B5PgYQU7AwOfeCSJfhcJjXUzDkvIcDLz3dkTkUvTwLTNPR1IDhciDHzpVZMHSIVeSJ3Z-2BgrZVaD4B6O-2FZT6jHj0-2FDN5gHDgu6kOa95MNnMOmua1zvxFU-2FoEp19BwroOMFx9uCFki4Q3J-2BWl8gPcFemR2EXG10oCzgLRMZFMWPpxBeeILw2yFN8-2BtXnuGCVBC-2FmKwPU6tT98U8KMWa1W3xbi7XocqijkQJAKkffp52942EW8MkxHQOJ8vGiZKMTUc7jD-2BKf33uBt-2BCZco9YFxDY3GHNiWJXRiWHnScn-2BV1TYQ"
        self.assertEqual(self.postmark_view.get_blockmark_forwarding_url(self.html_body), expected_url)
