
from django.conf import settings
from django.contrib.sessions.models import Session

from accounts.models import Profile
from accounts.permissions import (
    DEVICES_PERMISSION, PEOPLE_AND_ORGANISATION_PERMISSION,
    CERTIFICATES_AND_INSURANCE_PERMISSION,
)
from beta_features.models import CAP_V_FIVE_BETA_SWITCH, BetaFeature, TRUSTD_BETA_SWITCH
from billing.providers.chargebee import plans
from notifications.models import MarketingBanner
from partners.models import Partner
from regions.utils import get_regional_certificate_name_settings, is_eu_geo
from rulebook.jotform.utils import get_cep_jotform_id
from rulebook.models import (
    CYBERSMART_COMPLETE, CYBERSMART_COMPLETE_VERBOSE, CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS,
    CYBER_ESSENTIALS_PLUS_VERBOSE, CYBER_ESSENTIALS_VERBOSE, GDPR, GDPR_VERBOSE,
    HEALTH_CHECK, HEALTH_CHECK_VERBOSE, IASME_CYBER_ASSURANCE,
    IASME_CYBER_ASSURANCE_VERBOSE, IASME_GOVERNANCE, IASME_GOVERNANCE_VERBOSE,
    CERTIFICATE_SHORT_TYPES, ESSENTIAL_EIGHT, NIS2,
)
from rulebook.utils import get_latest_version
from vulnerabilities.utils import OPSWAT_SOURCE, REGULAR_SOURCE
from .models import ProjectSettings
from .utils import can_add_fakes


def app_settings(request):

    partners_context = {
        'header_background_color': Partner.CYBERSMART_COLOR,
        'header_font_color': Partner.CYBERSMART_FONT_COLOR
    }

    marketing_banner = None
    if request.user.is_authenticated:
        profile = Profile.objects.filter(user_id=request.user.id).\
            select_related("user__partner__partner__distributor", "user__distributor").\
            first()

        is_partner = bool(hasattr(profile.user, 'partner') and profile.user.partner)
        is_distributor = bool(hasattr(profile.user, 'distributor') and profile.user.distributor)
        partners_context = {
            'is_partner': is_partner,
            'is_direct_partner': (profile.user.partner.partner.is_direct_partner if is_partner else False),
            'is_channel_partner': profile.is_channel_partner,
            'is_iasme_cb_partner': bool(is_partner and profile.user.partner.partner.iasme_cb),
            'has_partner': profile.has_partner,
            'is_distributor': is_distributor,
            'distributor_is_cybersmart': profile.distributor_is_cybersmart,
            'partner': (profile.user.partner if is_partner else None),
            'distributor': (profile.user.distributor if is_distributor else None),
            'organisation': profile.organisation,
            'header_background_color': profile.header_background_color,
            'header_font_color': profile.header_font_color,
            'is_impersonating': (hasattr(request.user, 'is_impersonate') and request.user.is_impersonate),
            "beta_features": BetaFeature.objects.all(),
            "hide_nav_bar": profile.is_circle_community_user
        }
        partners_context['has_insurer_distributor'] = (
            is_distributor and partners_context["distributor"].distributor.is_insurer or
            is_partner and partners_context["partner"].partner.distributor.is_insurer)

        marketing_banners = MarketingBanner.active_objects.for_profile(profile, is_distributor, is_partner)
        marketing_banner = marketing_banners.first()

    user_agent = request.headers.get('user-agent')
    is_bot = True if user_agent and 'Detectify' in user_agent else False
    is_auto_test = True if user_agent and 'Selenium' in user_agent else False
    is_bot_or_auto_test = is_bot or is_auto_test

    context = {
        "ss": Session.objects.filter(session_key=request.session.session_key).first(),
        "cs_sidenav": request.session.get("cs-sidenav") if hasattr(request, "session") else "maximized",
        'debug': settings.DEBUG,
        'is_develop': settings.IS_DEVELOP,
        'is_stage': settings.IS_STAGE,
        'is_prod': settings.IS_PROD,
        'is_bot': is_bot,
        'is_bot_or_auto_test': is_bot_or_auto_test,
        'LEAD_REGISTRATION_URL': settings.LEAD_REGISTRATION_URL,
        'ENVIRONMENT_NAME': settings.ENVIRONMENT_NAME,
        'ENVIRONMENT_COLOR': settings.ENVIRONMENT_COLOR,
        # certification standards
        'CYBER_ESSENTIALS': CYBER_ESSENTIALS,
        'CYBER_ESSENTIALS_PLUS': CYBER_ESSENTIALS_PLUS,
        'IASME_GOVERNANCE': IASME_GOVERNANCE,
        'GDPR': GDPR,
        "HEALTH_CHECK": HEALTH_CHECK,
        "CYBERSMART_COMPLETE": CYBERSMART_COMPLETE,
        "ESSENTIAL_EIGHT": ESSENTIAL_EIGHT,
        "IASME_CYBER_ASSURANCE": IASME_CYBER_ASSURANCE,
        "NIS2": NIS2,
        # get latest default IASME cyber assurance version if it exists
        'cyber_assurance_version': get_latest_version(IASME_CYBER_ASSURANCE),
        'chargebee_site_name': settings.CHARGEBEE.get('SITE_NAME'),

        'CYBER_ESSENTIALS_VERBOSE': CYBER_ESSENTIALS_VERBOSE,
        'CYBER_ESSENTIALS_PLUS_VERBOSE': CYBER_ESSENTIALS_PLUS_VERBOSE,
        'IASME_GOVERNANCE_VERBOSE': IASME_GOVERNANCE_VERBOSE,
        'GDPR_VERBOSE': GDPR_VERBOSE,
        "HEALTH_CHECK_VERBOSE": HEALTH_CHECK_VERBOSE,
        "CYBERSMART_COMPLETE_VERBOSE": CYBERSMART_COMPLETE_VERBOSE,
        "IASME_CYBER_ASSURANCE_VERBOSE": IASME_CYBER_ASSURANCE_VERBOSE,
        "ps": ProjectSettings.objects.first(),
        'CS_ADDON_ANNUAL_ID': plans.ADDON_ANNUAL_ID,
        'CS_ADDON_MONTHLY_ID': plans.ADDON_MONTHLY_ID,
        'CS_PRO_ADDON_MONTHLY_ID': plans.ADDON_PRO_MONTHLY_ID,
        'CS_PRO_ADDON_ANNUAL_ID': plans.ADDON_PRO_ANNUAL_ID,
        'CE_PLAN_MONTHLY_ID': plans.CE_PLAN_MONTHLY_ID,
        'CE_PLAN_ANNUAL_ID': plans.CE_PLAN_ANNUAL_ID,
        'GDPR_PLAN_MONTHLY_ID': plans.GDPR_PLAN_MONTHLY_ID,
        'GDPR_PLAN_ANNUAL_ID': plans.GDPR_PLAN_ANNUAL_ID,
        'CE_GDPR_PLAN_MONTHLY_ID': plans.CE_GDPR_PLAN_MONTHLY_ID,
        'CE_GDPR_PLAN_ANNUAL_ID': plans.CE_GDPR_PLAN_ANNUAL_ID,
        'SESSION_EXPIRY_TIME': settings.SESSION_COOKIE_AGE,
        'knowledge_base_url': settings.KNOWLEDGE_BASE_URL,
        "CERTIFICATE_SHORT_TYPES": CERTIFICATE_SHORT_TYPES,
        "marketing_banner": marketing_banner,
        "CAP_V_FIVE_BETA_SWITCH": CAP_V_FIVE_BETA_SWITCH,
        "TRUSTD_BETA_SWITCH": TRUSTD_BETA_SWITCH,
        "POSTHOG_API_KEY": settings.POSTHOG_API_KEY,
        "MARKETING_WEBSITE_URL": settings.MARKETING_WEBSITE_URL,
        "IS_EU_GEO": is_eu_geo(),
        "SOURCE_OPSWAT": OPSWAT_SOURCE,
        "SOURCE_REGULAR": REGULAR_SOURCE,
        "CEP_JOTFORM_ID": get_cep_jotform_id(),
    }
    context.update(get_regional_certificate_name_settings())

    # restricted add fake functionality
    context['can_add_fakes'] = can_add_fakes(request)

    # Due to the `magic` done here we do not know if the organisation is a real organisation or the
    # organisation of the partner/distributor. This will use existing context to get the current organisation
    # without replacing the actual view context.
    if request.resolver_match and request.resolver_match.kwargs.get('org_id'):
        context['current_org'] = True

    context.update(partners_context)
    # add permissions to context
    context.update({
        'DEVICES_PERMISSION': DEVICES_PERMISSION,
        'PEOPLE_AND_ORGANISATION_PERMISSION': PEOPLE_AND_ORGANISATION_PERMISSION,
        'CERTIFICATES_AND_INSURANCE_PERMISSION': CERTIFICATES_AND_INSURANCE_PERMISSION,
    })

    return context
