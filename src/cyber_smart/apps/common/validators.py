import re

import regex
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _


from regions.utils import is_eu_geo

UNICODE_SAFE_REGEX = r'^[\p{L}\p{M}\p{N} \r\n\t0-9._~={}()\[\]\'\\`“”"|!*:@,;+?&%$£€¥#’</–®-]*$'


SAFE_TEXT_CHARACTERS = r'^[0-9a-zA-Z- ._~={}()\[\]\'\\\r\n`“”"|!*:@,;+?&%$£€¥#’</–®\t]*$'
SAFE_TEXT_CHARACTERS_VERBOSE = r"""Digits, letters and the following symbols:
. _ ~ = { } ( ) [ ] ' \ \r\n ` “ ” &quot; | ! * : @ , ; + ? & % $ £ € ¥ # ’ < / - – ®"""
SAFE_NAME_CHARACTERS = r'[0-9a-zA-Z ]'
# no special char after brackets and only characters in U+0020–U+00BF
PERVADE_SAFE_CHARACTERS = r'^(?!.*[)\]][^A-Za-z0-9 ])[\u0020-\u00BF]+$'
PERVADE_INVALID_CHARACTERS = r'[\)\]][^A-Za-z0-9 ]|[^\u0020-\u00BF]'
PERVADE_SAFE_CHARACTERS_VERBOSE = r"""Digits, letters and the following symbols:
_ ‘ ’ ' &quot; ® @ & ( ) . - > $ ? ! * , / : ; + = £ [ ] # ^; symbols after brackets are not allowed."""
DOMAIN_NAME_REGEX = re.compile(
    r'^(?:(([a-zA-Z]{1})|([a-zA-Z]{1}[a-zA-Z]{1})|'
    r'([a-zA-Z]{1}[0-9]{1})|([0-9]{1}[a-zA-Z]{1})|'
    r'([a-zA-Z0-9][-_a-zA-Z0-9]{0,61}[a-zA-Z0-9]))\.)+'
    r'([a-zA-Z]{2,13}|(xn--[a-zA-Z0-9]{2,30}))$'
)


class RegexValidatorSentry(RegexValidator):

    def __init__(self, regex=None, message=None, code=None, inverse_match=None, flags=None, invalid_regex=None):
        """ Override to add invalid regex handling """
        self.invalid_regex = invalid_regex
        super().__init__(regex, message, code, inverse_match, flags)

    def __call__(self, value):
        """
        Validate that the input contains a match for the regular expression
        if inverse_match is False, otherwise raise ValidationError.
        """
        regex_pattern = self.regex
        if isinstance(value, int):
            value = f'{value}'

        if is_eu_geo():
            # In EU regions, all Unicode characters (except control characters) are allowed
            regex_pattern = regex.compile(UNICODE_SAFE_REGEX)

        if not (self.inverse_match is not bool(regex_pattern.search(value))):
            # Find the first invalid character and raise an exception with details
            invalid_regex = regex_pattern
            if self.invalid_regex:
                invalid_regex = regex.compile(self.invalid_regex)
            invalid_character = self.find_invalid_character(value, invalid_regex)
            if invalid_character:
                raise ValidationError(
                    self.message + self.forbidden_character_html(invalid_character),
                    code=self.code
                )

    def forbidden_character_html(self, char) -> str:
        """
        Return the HTML for the forbidden character.
        """
        return ("<span class='text-danger m-l-5 m-r-5'>Forbidden characters:"
                f" <span style='display: inline-block' class='heart-beating m-l-10 font-bold'>"
                f"{char}</span></span>{', '.join(self.unicode_code_link(c) for c in char)}")

    @staticmethod
    def unicode_code_link(value) -> str:
        """
        Returns the given value with control characters highlighted.
        """
        return (f"<span class='unicode-code'>"
                f"<a href='https://en.wikipedia.org/wiki/U+{ord(value):04X}'>U+{ord(value):04X}</a></span>")

    @staticmethod
    def find_invalid_character(value, regex_pattern: regex) -> str|None:
        """
        Find the first invalid character in the input value.
        """
        invalid_chars = [m.group() for m in regex_pattern.finditer(value)]
        if invalid_chars:
            return invalid_chars[0]
        return None


DEFAULT_INVALID_CHAR_MESSAGE =  _("""
Invalid characters. You can use only letters, digits and other
<b class="text-success" style="text-decoration: underline" data-toggle="tooltip"
data-placement="bottom" title="{0}">safe characters</b>
""")

DEFAULT_INVALID_CHAR_CODE = "invalid_safe_char"


safe_char_validator = RegexValidatorSentry(
    SAFE_TEXT_CHARACTERS,
    DEFAULT_INVALID_CHAR_MESSAGE.format(SAFE_TEXT_CHARACTERS_VERBOSE),
    code=DEFAULT_INVALID_CHAR_CODE
)

pervade_safe_char_validator = RegexValidatorSentry(
    PERVADE_SAFE_CHARACTERS,
    DEFAULT_INVALID_CHAR_MESSAGE.format(PERVADE_SAFE_CHARACTERS_VERBOSE),
    code=DEFAULT_INVALID_CHAR_CODE,
    invalid_regex=PERVADE_INVALID_CHARACTERS
)


safe_name_validator = RegexValidatorSentry(
    SAFE_NAME_CHARACTERS,
     _('Invalid characters. You can use only letters, digits and spaces'),
    code='invalid_name_char'
)

domain_name_validator = RegexValidator(
    DOMAIN_NAME_REGEX,
     _('Domain name is not valid'),
    code='invalid_domain_name'
)


def post_code_validator(value):
    """
    UK postcode validator
    :param value: post code
    :type value: str
    :return: True or raises an error
    :rtype: bool, Exception
    """
    pattern = regex.compile(
        r"([Gg][Ii][Rr] 0[Aa]{2})|((([A-Za-z][0-9]{1,2})|(([A-Za-z][A-Ha-hJ-Yj-y][0-9]{1,2})|(([A-Za-z][0-9]"
        r"[A-Za-z])|([A-Za-z][A-Ha-hJ-Yj-y][0-9][A-Za-z]?))))\s?[0-9][A-Za-z]{2})"
    )
    if pattern.match(value):
        return True
    else:
        raise ValidationError("This is not valid UK postcode")
