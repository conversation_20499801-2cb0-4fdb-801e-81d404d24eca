from urllib.parse import urlencode

from django.conf import settings
from django.contrib.auth.models import User
from django.core.cache import cache
from django.core.exceptions import MiddlewareNotUsed
from django.db import DEFAULT_DB_ALIAS, connections
from django.db.migrations.executor import MigrationExecutor
from django.http import HttpResponse, HttpRequest
from django.shortcuts import redirect, render
from django.urls import resolve, Resolver404
from django.utils import translation
from django.utils.deprecation import MiddlewareMixin
from django.utils.translation.trans_real import parse_accept_lang_header, language_code_re

# newrelic is only deployed on AWS
try:
    from newrelic import agent
except ModuleNotFoundError:
    pass
from sesame.middleware import AuthenticationMiddleware
from sesame.utils import TOKEN_NAME
from silk.middleware import SilkyMiddleware
from allauth_2fa.middleware import BaseRequire2FAMiddleware

from common.models import ProjectSettings

from impersonate.middleware import ImpersonateMiddleware


def show_toolbar(request):
    """
    Default function to determine whether to show the toolbar on a given page.
    """
    # if hasattr(settings, 'INTERNAL_IPS'):
    #     if request.META.get('REMOTE_ADDR', None) not in settings.INTERNAL_IPS:
    #         return False

    if settings.DEBUG:
        return True

    if not settings.IS_PROD and request.user.is_superuser:
        return True

    if hasattr(settings, 'DJANGO_TOOLBAR_PROD'):
        if request.user.is_authenticated:
            return request.user.email in settings.DJANGO_TOOLBAR_PROD

    return False


class TokenAuthenticationMiddleware(AuthenticationMiddleware):
    def get_redirect(self, request):
        """
        Create a HTTP redirect response that removes the token from the URL.
        """
        params = request.GET.copy()
        params.pop(TOKEN_NAME)
        params.update({"authorized_by_token": "yes"})
        url = request.path
        if params:
            url += "?" + urlencode(params)
        return redirect(url)


class CustomSilkyMiddleware(SilkyMiddleware):
    """
    This is a custom Silk middleware that can be enabled/disabled in
    ProjectSettings model.
    """
    def __init__(self, get_response):
        """
        This code will be executed only once when the web server starts.
        Here we check if silk is enabled in django settings and if not
        we ignore the whole middleware.
        """

        if not settings.SILKY_ENABLED:
            raise MiddlewareNotUsed
        super().__init__(get_response)

    def __call__(self, request):
        """
        Process view request and response,
        and log if it matches the logging criteria.
        :param request: http request
        :type request: HttpRequest
        :return: http response
        :rtype: HttpResponse
        """
        if not self.is_api_request_logging_enabled:
            return self.get_response(request)

        request.silk_filters = {}

        # buffer the request body
        request_body = request.body
        request._body = request_body

        # Call the actual view
        response = self.get_response(request)

        lower_bound_codes = 200
        if settings.IS_PROD:
            # Only log 4xx/5xx requests in production
            lower_bound_codes = 400
        if lower_bound_codes <= response.status_code < 600:
            # Call Silk to log the request/response
            self.process_request(request)
            self.process_response(request, response)

        return response

    @property
    def is_api_request_logging_enabled(self) -> bool:
        """
        Determines whether logging of API requests is enabled in the ProjectSettings.
        """
        return getattr(ProjectSettings.objects.first(), "record_api_requests", False)


class CustomImpersonateMiddleware(ImpersonateMiddleware):
    # https://hg.code.netlandish.com/~petersanchez/django-impersonate/browse/impersonate/middleware.py?rev=tip
    def process_request(self, request):
        # custom READ_ONLY implementation
        if '_impersonate' in request.session and request.user.is_authenticated:
            if not can_user_modify(request) and request.method not in ['GET', 'HEAD']:
                return render(request, '405-demo.html', status=405)
            return super().process_request(request)


def can_user_impersonate(request):
    # All users with django "is_staff" can impersonate
    if request.user.is_staff:
        return True
    # All partners can impersonate - see restrictions on who in users_impersonable
    if request.user.profile.is_partner:
        return True
    return False


def can_user_modify(request):
    # Only users with django "is_staff" can modify
    if request.user.is_staff:
        return True
    return False


def users_impersonable(request):
    # Staff can impersonate all users (except superusers)
    if request.user.is_staff:
        return User.objects.all()
    # Parners can impersonate select users
    if request.user.profile.is_partner:
        return User.objects.filter(groups__name='Demo Accounts')
    return None


class HealthCheckMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request: HttpRequest) -> HttpResponse:
        if request.path in ['/health-check', '/api/health-check']:

            # Check if there are any pending database migrations
            # and if there are, return unavailable status
            executor = MigrationExecutor(connections[DEFAULT_DB_ALIAS])
            plan = executor.migration_plan(executor.loader.graph.leaf_nodes())
            if plan:
                return HttpResponse('Pending migrations', status=503)

            # newrelic is only deployed on AWS
            try:
                agent.ignore_transaction(flag=True)
            except NameError:
                pass
            return HttpResponse('ok')
        return self.get_response(request)


class Enforce2FAMiddleware(BaseRequire2FAMiddleware):
    # List of URL names that the user should still be allowed to access.
    allowed_pages = [
        # They should still be able to log out or change password.
        "account_change_password",
        "account_logout",
        "account_reset_password",
        # URLs required to set up two-factor
        "two-factor-setup",
        "two-factor-email-setup",
        "two-factor-auth-setup",
        "otp-verify-email",
    ]

    def on_require_2fa(self, request: HttpRequest) -> HttpResponse:
        # Redirect user to two-factor setup page.
        return redirect("two-factor-setup")

    def _can_skip_2fa(self, request: HttpRequest) -> bool:
        profile = request.user.profile
        skip = request.GET.get('skip_2fa', False)

        if request.session.get('skip_2fa', False):
            return True
        elif skip and profile.num_of_skip_mfa < 3:
            profile.num_of_skip_mfa += 1
            profile.save()
            request.session['skip_2fa'] = True

            return True
        else:
            return False

    def require_2fa(self, request: HttpRequest) -> bool:
        user = request.user
        if hasattr(user, 'is_impersonate') and user.is_impersonate:
            return False

        profile = user.profile
        if organisation := profile.organisation:
            if organisation.settings.enforce_multi_factor_authentication:
                return not self._can_skip_2fa(request)
        return False


class CustomMiddleware:
    """ Middleware to keep track of the organisation related pages the user is trying to access """
    def __init__(self, get_response):
        self.get_response = get_response

    @staticmethod
    def __get_org_id(request, kwargs: dict) -> str:
        """
        Get the organisation ID from the URL kwargs if available.
        In case we have URLs with no organisation ID, we will return the already cached organisation ID.
        """
        org_id = kwargs.get("org_id", kwargs.get("org_uuid", kwargs.get("organisation_id", "")))
        if not org_id:
            org_id = cache.get(f'current_org_id:{request.user.id}')
        return org_id

    def __call__(self, request):
        try:
            _, _, kwargs = resolve(request.path)
        except Resolver404:
            kwargs = {}
        cache.set(f'current_org_id:{request.user.id}', self.__get_org_id(request, kwargs), 120)
        response = self.get_response(request)
        return response


class CustomLocaleMiddleware(MiddlewareMixin):
    """
    Set the default language for the user.
    """

    def process_request(self, request):
        default_language = settings.LANGUAGE_CODE
        if not request.user.is_authenticated:
            self._set_language(request, default_language)
            return
        user_browser_language = self._get_language_from_headers(request)
        # Check if the user is impersonating another user and set the language accordingly
        if hasattr(request.user, 'is_impersonate') and request.user.is_impersonate:
            # language coming from the user's browser settings takes priority
            language = user_browser_language or default_language
            self._set_language(request, language)
            return
        # If the user is not impersonating, check respective default language
        if distributor_user := request.user.profile.distributor:
            default_language = distributor_user.distributor.default_language
        elif partner_user := request.user.profile.partner_user:
            default_language = partner_user.partner.default_language
        elif organisation := request.user.profile.organisation:
            default_language = organisation.settings.default_language
        # language coming from the user's browser settings takes priority
        language = user_browser_language or default_language
        self._set_language(request, language)

    @staticmethod
    def _set_language(request, language):
        """ Helper method to set the language in the request. """
        translation.activate(language)
        request.LANGUAGE_CODE = translation.get_language()

    @staticmethod
    def _get_language_from_headers(request) -> str | None:
        """ Get preferred language from the user's browser settings """
        accept = request.META.get("HTTP_ACCEPT_LANGUAGE", "")
        for accept_lang, unused in parse_accept_lang_header(accept):
            if accept_lang == "*":
                break

            if not language_code_re.search(accept_lang):
                continue

            try:
                return translation.get_supported_language_variant(accept_lang)
            except LookupError:
                continue
        return None
