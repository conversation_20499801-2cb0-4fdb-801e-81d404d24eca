import copy
import waffle
import datetime
import json
import logging
import re
from typing import Optional
from uuid import UUID
from bs4 import BeautifulSoup

from allauth.account import signals, app_settings
from allauth.account.utils import perform_login
from allauth.account.views import PasswordResetView, PasswordResetFromKeyView, LoginView
from django.contrib import messages
from django.core.cache import cache
from django.core.exceptions import PermissionDenied
from django.db.models import OuterRef, Prefetch, Q, Subquery, Count, Case, When, F, Value, CharField
from django.db.models.functions import Concat
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect, HttpResponseBadRequest
from django.shortcuts import render, get_object_or_404, redirect

from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.csrf import csrf_failure
from django.views.generic import ListView, View, TemplateView
from postmark_inbound import PostmarkInbound
from rest_framework.views import APIView

from analytics.models import OrganisationAnalytics
from analytics.tasks import record_report_download_usage
from api.v2.partner_dashboard.mixins import AnnotateUserHasAccessMixin
from appusers.models import AppFile, AppInstall, AppReport, UserLoginHistory
from beta_features.mixins import BetaViewMixin
from beta_features.models import BetaFeature, CAP_V_FIVE_BETA_SWITCH, TRUSTD_BETA_SWITCH
from beta_features.utils import get_app_install_cap_v_five_beta_q_object, get_app_install_trustd_beta_q_object
from billing.tasks import upgrade_direct_customers_quantity, upgrade_direct_customers_quantity_v6
from common.mixins import StaffRequiredMixin
from common.models import CSVFile
from dashboard.mixins import CSVReportMixin
from emails.tasks import (
    certification_billing_renewal_report_emails
)
from notifications.serializers import OrganisationSerializer
from organisations.models import Organisation
from organisations.models import OrganisationCertification
from organisations.models import get_organisations
from partners.models import Partner
from rulebook.models import (
    CertificationVersion, CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS, GDPR,
    IssuedCertification
)
from rulebook.pervade.tasks.common import save_postmark_certificate_task
from signup.mixins import SignupMixin
from common.mixins import FilteringViewMixin, OrderingViewMixin, SearchViewMixin
from common.utils import SafePaginator

logger = logging.getLogger(__name__)


def home(request, template='home_landing.html'):
    site_context = {}
    return render(request, template, site_context)


class InactivityView(View):
    """
    View that returns True or False depends on user inactivity.
    If user inactivity time is more then specified in settings returns True otherwise returns False.
    """
    http_method_names = ['get']

    @staticmethod
    def get(request):
        return JsonResponse({'inactivity': request.session.get('logged_out_due_to_inactivity', False)})


class PasswordResetViewCustom(PasswordResetView):
    def form_invalid(self, form):
        return HttpResponseRedirect(self.get_success_url())


class PasswordResetFromKeyCustomView(PasswordResetFromKeyView):
    redirect_field_name = "next"

    def form_valid(self, form):
        form.save()
        # get_adapter(self.request).add_message(
        #     self.request,
        #     messages.SUCCESS,
        #     'account/messages/password_changed.txt')
        signals.password_reset.send(sender=self.reset_user.__class__,
                                    request=self.request,
                                    user=self.reset_user)

        if app_settings.LOGIN_ON_PASSWORD_RESET:
            return perform_login(
                self.request, self.reset_user,
                email_verification=app_settings.EMAIL_VERIFICATION)

        return HttpResponseRedirect(self.get_success_url())


class TriggerBillingRenewalEmailsView(View):
    """
    Just for developers
    """
    http_method_names = ['get']

    @staticmethod
    def get(request):
        logger.info(f'Billing renewal emails triggered by {request.user}')
        if request.GET.get('distributor_pk'):
            distributor_pk = int(request.GET.get('distributor_pk'))
            certification_billing_renewal_report_emails.delay(distributor_pk=distributor_pk)
            messages.success(request, _(f"Billing renewal emails triggered to distributor id: {distributor_pk}"))
        elif request.GET.get('partner_pk'):
            partner_pk = int(request.GET.get('partner_pk'))
            certification_billing_renewal_report_emails.delay(partner_pk=partner_pk)
            messages.success(request, _(f"Billing renewal emails triggered to partner id: {partner_pk}"))
        else:
            certification_billing_renewal_report_emails.delay()
            messages.success(request, _("Billing renewal emails triggered to all distributors and partners"))
        return HttpResponseRedirect('/')


class PlanTransitionsTriggerView(View):
    """
    Just for developers
    """
    http_method_names = ['get']

    @staticmethod
    def get(request):
        upgrade_direct_customers_quantity.delay()
        upgrade_direct_customers_quantity_v6.delay()
        messages.success(request, _("Triggered!"))
        return HttpResponseRedirect('/')


def bad_request(request, exception):
    return render(request, '400.html', status=400)


def permission_denied(request, exception):
    return render(request, '403.html', status=403)


def page_not_found(request, exception):
    return render(request, '404.html', status=404)


def server_error(request):
    return render(request, '500.html', status=500)


class SideNavigationStateView(View):
    """
    Saves side navigation state to the sessions.
    """
    http_method_names = ["post"]

    @staticmethod
    def post(request):
        response = {"success": False}
        if hasattr(request, "session"):
            state = request.POST.get("cs-sidenav")
            if state in ["minimized", "maximized"]:
                request.session["cs-sidenav"] = state
                response["success"] = True
        return JsonResponse(response)


class PostmarkWebHookView(APIView):
    """
    Parses inbound email
    """
    @staticmethod
    def parse_contributor_email(email: str) -> tuple[str, str, int] | None:
        """
        Parses contributor email and returns tuple of (organisation UUID, cert version, cert type)
        """
        # email example
        # <EMAIL>
        match = re.search(r'([a-f0-9\-]{36})--([\d\.]+)--(\d+)@', email, re.IGNORECASE)
        if not match:
            raise ValueError(f"Invalid email format: {email}")
        # c47b53b3-31d5-461f-b0ba-e640e4e3d33b
        org_external_uuid = str(UUID(match.group(1)))
        # 2022.0
        version_number = match.group(2)
        # 1
        certification_type = int(match.group(3))
        return org_external_uuid, version_number, certification_type

    def identify_certification(self, to: str) -> Optional[OrganisationCertification]:
        """
        Identifies certification by email address.
        """
        try:
            org_external_uuid, version_number, certification_type = self.parse_contributor_email(email=to)
            # lookup the certification
            version_match = CertificationVersion.objects.get(
                type__type=certification_type,
                version_number=version_number
            )
            certification_match = OrganisationCertification.objects.get(
                organisation__external_uuid=org_external_uuid,
                version=version_match
            )
        except Exception as e:
            logger.error(f"Failed to identify certification: {e}")
            return None
        else:
            return certification_match

    def is_main_certificate(self, to: str, subject: str) -> bool:
        """
        Checks if cert type in contributor email is the same as in email subject.
        If it's not equal then it's not the main cert and better just to ignore it.
        For GDPR, Blockmark sends two emails, the first one with the main GDPR cert,
        and the second one is Cyber Essentials cert. We should ignore any not main certs,
        as they can overwrite the main cert in the backend.
        """
        _, _, certification_type = self.parse_contributor_email(email=to)
        cert_aliases = {
            CYBER_ESSENTIALS: ["cyber e", "ce test"],
            CYBER_ESSENTIALS_PLUS: ["cyber essentials plus", "ce plus test"],
            GDPR: ["iasme governance", "iasme governance test"],
        }[certification_type]

        for alias in cert_aliases:
            if alias in subject:
                return True
        return False

    def _is_certificate_email(self, subject: str, to_email: str) -> bool:
        subject_lower = subject.lower()
        allowed_subjects = [
            "blockmark registry | cyber e",
            "blockmark registry | ce",
            "cyber essentials certificate",
            "cyber essentials plus certificate",
            "cyber e test certificate",  # IASME staging
            "ce test certificate",  # IASME staging
            "ce plus test",  # IASME staging
        ]
        return any(subject_lower.startswith(subject) for subject in allowed_subjects) and self.is_main_certificate(
            to_email, subject_lower
        )

    def _is_insurance_email(self, subject: str) -> bool:
        subject_lower = subject.lower()
        allowed_subjects = [
            "blockmark registry | evidence of insurance",
            "blockmark registry | test insurance",
            "evidence of insurance for eligible",
            "test insurance",  # IASME staging
        ]
        return any(subject_lower.startswith(subject) for subject in allowed_subjects)

    def _is_email_that_can_be_ignored(self, subject: str) -> bool:
        subject_lower = subject.lower()
        allowed_subjects = [
            'assessment report',
            'assessment submission',
            'blockmark registry | welcome to',
            'blockmark registry | email address verified',
        ]
        return any(subject_lower.startswith(subject) for subject in allowed_subjects)

    @staticmethod
    def _extract_url_by_text(html_body: str, link_text: str) -> str:
        """Extract URL from a link with specific text content."""
        soup = BeautifulSoup(html_body, 'html.parser')
        link = soup.find('a', string=lambda text: text and link_text.lower() in text.lower())
        return link.get('href', '') if link else ''

    @staticmethod
    def _extract_url_by_pattern(html_body: str, url_pattern: str, alt_url_pattern: str) -> str:
        """Extract URL using regex patterns."""
        if match := re.search(url_pattern, html_body):
            return match.group(1)

        alt_match = re.search(alt_url_pattern, html_body)
        return alt_match.group(1) if alt_match else ""

    @classmethod
    def get_blockmark_registration_url(cls, html_body: str) -> str:
        # Try text-based extraction first
        url = cls._extract_url_by_text(html_body, "Register to Manage Your Certificate")
        if url:
            return url

        # Fall back to pattern-based extraction
        return cls._extract_url_by_pattern(
            html_body,
            r'(https://registry\.blockmarktech\.com/on-boarding/router/\?key=[^<>"\s]*)',
            r'(https://wyche\.registry\.blockmarktech\.com/on-boarding/router/\?key=[^<>"\s]*)'
        )

    @classmethod
    def get_blockmark_forwarding_url(cls, html_body: str) -> str:
        # Try text-based extraction first
        url = cls._extract_url_by_text(html_body, "Forward this Certificate to Another Email")
        if url:
            return url

        # Fall back to pattern-based extraction
        return cls._extract_url_by_pattern(
            html_body,
            r'(https://registry\.blockmarktech\.com/on-boarding/forward-email/\?key=[^<>"\s]*)',
            r'(https://wyche\.registry\.blockmarktech\.com/on-boarding/forward-email/\?key=[^<>"\s]*)'
        )

    @classmethod
    def get_blockmark_attributes(cls, is_insurance: bool) -> tuple[str, str, str]:
        if is_insurance:
            url_type = "insurance"
            registration_attr = "blockmark_evidence_of_insurance_registration_url"
            forwarding_attr = "blockmark_evidence_of_insurance_forwarding_url"
        else:
            url_type = "certificate"
            registration_attr = "blockmark_certificate_registration_url"
            forwarding_attr = "blockmark_certificate_forwarding_url"
        return url_type, registration_attr, forwarding_attr

    @classmethod
    def set_blockmark_attributes(
            cls,
            issued_certification: IssuedCertification,
            url_type: str,
            attr_to_set: str,
            attr_value: str,
    ):
        attr_display_name = attr_to_set.split("_")[-2]
        if not attr_value:
            logger.error(f"Postmark webhook failed to retrieve {url_type} {attr_display_name} URL. Certification: {issued_certification.certificate.pk}. Maybe something in the inbound email template changed.")
            return

        setattr(issued_certification, attr_to_set, attr_value)

    def save_blockmark_urls(self, certification: OrganisationCertification, html_body: str, certification_number: str, is_insurance: bool = False):
        registration_url = self.get_blockmark_registration_url(html_body)
        forwarding_url = self.get_blockmark_forwarding_url(html_body)

        if not hasattr(certification, 'issued_certification'):
            IssuedCertification.objects.get_or_create(certificate=certification)
            certification.refresh_from_db()

        fields_to_update = []
        issued_certification = certification.issued_certification
        if is_insurance:
            issued_certification.insurance_number = certification_number
            fields_to_update.append('insurance_number')
        else:
            issued_certification.number = certification_number
            fields_to_update.append('number')
        url_type, registration_attr, forwarding_attr = self.get_blockmark_attributes(is_insurance)
        for attr_to_set, attr_value in [(registration_attr, registration_url), (forwarding_attr, forwarding_url)]:
            self.set_blockmark_attributes(issued_certification, url_type, attr_to_set, attr_value)
            fields_to_update.append(attr_to_set)
        issued_certification.save(update_fields=fields_to_update)

    def _get_certification_number(self, first_attachment_name: str) -> str:
        # file name looks like d6151d8a-8cca-4f90-8a76-c0cd721e4536_certificate.pdf
        # the number is the first part of the file name
        return first_attachment_name.split('_')[0]

    def post(self, request, *args, **kwargs):
        try:
            inbound = PostmarkInbound(json=json.dumps(request.data))
        except TypeError:
            logger.error("Postmark webhook failed to parse inbound email")
            return HttpResponseBadRequest()

        certification = self.identify_certification(inbound.source.get("To"))
        if not certification:
            logger.error("Postmark webhook failed to identify certification")
            return HttpResponseBadRequest()

        html_body = inbound.html_body()

        subject = inbound.subject()
        if self._is_certificate_email(subject, inbound.source.get("To")):
            try:
                attachments = inbound.attachments()
                first_attachment = attachments[0]
            except (AttributeError, IndexError) as e:
                logger.error(f"Postmark webhook failed to retrieve email attachment: {str(e)}")
                return HttpResponseBadRequest()
            # save certificate content to the cache for 7 minutes
            # it will be picked up and saved later on by the task below
            first_attachment_content = first_attachment.read()
            if first_attachment_content:
                cache.set(f"blockmark_certificate_{certification.pk}", first_attachment_content, 420)  # 7 minutes
                save_postmark_certificate_task.delay(certification.pk, first_attachment.name())
            else:
                if waffle.switch_is_active('allow_postmark_email_upload_without_attachment_content'):
                    # can be useful to recover already processed emails as they will not have attachments saved in Postmark
                    pass
                else:
                    return HttpResponseBadRequest()
            certification_number = self._get_certification_number(first_attachment.name())
            self.save_blockmark_urls(certification, html_body, certification_number, is_insurance=False)
        elif self._is_insurance_email(subject):
            try:
                attachments = inbound.attachments()
                first_attachment = attachments[0]
            except (AttributeError, IndexError) as e:
                logger.error(f"Postmark webhook failed to retrieve email attachment: {str(e)}")
                return HttpResponseBadRequest()
            first_attachment_content = first_attachment.read()
            if first_attachment_content:
                cache.set(f"blockmark_evidence_of_insurance_{certification.pk}", first_attachment.read(), 420)  # 7 minutes
                save_postmark_certificate_task.delay(certification.pk, first_attachment.name())
            else:
                if waffle.switch_is_active('allow_postmark_email_upload_without_attachment_content'):
                    # can be useful to recover already processed emails as they will not have attachments saved in Postmark
                    pass
                else:
                    return HttpResponseBadRequest()
            evidence_of_insurance_number = self._get_certification_number(first_attachment.name())
            self.save_blockmark_urls(certification, html_body, evidence_of_insurance_number, is_insurance=True)
        elif (
            self._is_email_that_can_be_ignored(subject)
        ):
            pass
        else:
            # Return 400 back to Postmark so we can later retry this email
            return HttpResponseBadRequest()
        response = {"success": True}
        return JsonResponse(response)


class BaseOrganisationsView(SignupMixin, AnnotateUserHasAccessMixin,
                            OrderingViewMixin, FilteringViewMixin, SearchViewMixin, ListView):
    http_method_names = ("get", "post")
    template_name = None
    context_object_name = "organisations"
    paginate_by = 15
    paginator_class = SafePaginator
    ordering_fields_map = {
        "name": ["name"],
        "deployment": ["bulk_install"],
        "created": ["created"],
        "smart_score": ["smart_score__overall"],
        "devices": ["analytics__installed_devices_count"],
        "secured": ["analytics__pass_percentage"],
    }
    search_fields = [
        "name"
    ]

    def get_object_id(self):
        """ Returns the id of the object that is being used to filter the queryset in filtering_queryset """
        raise NotImplementedError

    def get_model_filter_name(self):
        """ Returns the filter name to use in get_secure_analytics_queryset """
        raise NotImplementedError

    def get_queryset(self):
        return self.ordering_queryset(
            self.search_queryset(
                self.filtering_queryset(self.default_queryset)
            )
        ).annotate(**self.get_annotate_params(prefix_query_param=''))

    @property
    def default_queryset(self):
        """
        Returns organisations queryset.
        """
        return Organisation.objects.filter(
            **{self.__model_filter_name: self.__filter_object_id}
        ).annotate(
            has_gdpr=Count(Case(When(certifications__version__type__type=GDPR, then=1))),
        ).prefetch_related(
            "certifications", "certifications__version", "certifications__version__type",
            "certifications__survey", "smart_score", "analytics"
        ).order_by("-pk")

    @property
    def __filter_object_id(self):
        return self.get_object_id()

    @property
    def __model_filter_name(self):
        return self.get_model_filter_name()

    def filtering_queryset(self, queryset):
        """
        Filters queryset by filter param.
        """
        queryset_filter = self.request.GET.get("filter")
        if queryset_filter:
            if queryset_filter.lower() == "no_security_issues":
                queryset = queryset.filter(
                    pk__in=OrganisationAnalytics.get_secure_analytics_queryset(
                        self.__filter_object_id, model_filter_name=self.__model_filter_name
                    ).values_list("organisation__pk", flat=True)
                )
            elif queryset_filter.lower() == "security_issues":
                queryset = queryset.exclude(
                    pk__in=OrganisationAnalytics.get_secure_analytics_queryset(
                        self.__filter_object_id, model_filter_name=self.__model_filter_name
                    ).values_list("organisation__pk", flat=True)
                )
            elif queryset_filter.lower() == "certified":
                queryset = queryset.filter(
                    certifications__status__in=OrganisationCertification.CERTIFIED_STATUSES,
                ).distinct()
            elif queryset_filter.lower() == "expiring_soon":
                queryset = queryset.filter(
                    certifications__status=OrganisationCertification.EXPIRING,
                ).distinct()
            elif queryset_filter.lower() == "trial_account":
                queryset = queryset.filter(is_trial=True)
            elif queryset_filter.lower() in BetaFeature.objects.values_list("kind", flat=True):
                queryset = queryset.filter(beta_enrolment__feature__kind=queryset_filter.lower())
        return queryset


class BaseDevicesView(
    SignupMixin, AnnotateUserHasAccessMixin, SearchViewMixin, OrderingViewMixin, FilteringViewMixin, ListView, BetaViewMixin
):
    context_object_name = "devices"
    paginate_by = 15
    query_params = ["name", "report"]
    search_fields = [
        "hostname", "app_user__organisation__name", "os__title", "caption",
        "app_user__first_name", "app_user__last_name", "app_user__email",
        "last_hist_login_username", "last_hist_login_domain", {
            "keyword": "physical",
            "field": "device_type",
            "field_values": [
                AppInstall.DESKTOP, AppInstall.MOBILE, AppInstall.HEALTH_CHECK, AppInstall.UNKNOWN, AppInstall.SERVER
            ]
        },
        {
            "keyword": "virtual",
            "field": "device_type",
            "field_values": [AppInstall.VIRTUAL_DESKTOP, AppInstall.VIRTUAL_SERVER]
        }
    ]
    specific_filter_cases = {
        "unnamed": (Q(**{"last_hist_login_username": ""}) & Q(**{"last_hist_login_domain": ""})),
    }
    ordering_fields_map = {
        "host": ["hostname"],
        "org": ["app_user__organisation__name"],
        "os": ["os__id"],
        "last_check_in": ["reports__created"],
        "security": ["analytics__latest_pass_percentage"],
        "software": ["analytics__vulnerable_software_count"],
        "version": ["version__major", "version__minor", "version__patch"],
        "device_id": ["device_id"]
    }

    DEVICE_TYPE = 'device_type'
    STATUS = 'status'
    OS_PLATFORM = 'os_platform'
    OS_RELEASE = 'os_release'
    APP_VERSION = 'app_version'
    BETA = 'beta'

    filtering_fields: list[str] = [DEVICE_TYPE, STATUS, OS_PLATFORM, OS_RELEASE, APP_VERSION, BETA]
    filtering_query_param: None = None

    def __init__(self):
        super().__init__()
        self.organisations_dropdown_list = []
        self.eol_os_releases_dropdown_list = []
        self.app_versions_dropdown_list = []
        self.operating_systems_dropdown_list = []
        self.applied_filters = []
        self.main_entity = None

    def get_main_entity(self):
        raise NotImplementedError

    def get_view_url(self):
        raise NotImplementedError

    def get_filtering_display_dict(self):
        display_dict = {
            'virtual_desktop': _('Virtual Desktop'),
            'virtual_server': _('Virtual Server'),
            'awaiting_reports': _('Install pending'),
            CAP_V_FIVE_BETA_SWITCH: _('Desktop beta'),
            TRUSTD_BETA_SWITCH: _('Mobile beta'),
        }
        org_list = self.organisations_dropdown_list
        # match the organisation id to the organisation name when displaying applied filters
        for org_details in org_list:
            display_dict[str(org_details['id'])] = org_details['text']
        # match the end of life operating system id to the operating system name when displaying applied filters
        for os_release in self.eol_os_releases_dropdown_list:
            display_dict['eolpk_' + str(os_release[0])] = str(os_release[1]) + ' - ' + str(os_release[2])
        return display_dict

    @staticmethod
    def get_operating_systems_dropdown_list(queryset):
        """ This list will be user on the frontend to populate the dropdown list of operating system platforms """
        return list(queryset.values_list('platform', flat=True).distinct())

    @staticmethod
    def get_organisations_dropdown_list(queryset):
        """ This list will be user on the frontend to populate the dropdown list of organisations """
        return OrganisationSerializer(Organisation.objects.filter(
            id__in=queryset.values_list('app_user__organisation', flat=True).distinct()
        ), many=True).data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["view_url"] = self.get_view_url()
        context["desktop_appfile"] = AppFile.get_desktop_app_file()

        if self.filtered_by_active_only():
            context["filtered_by_active_only"] = True

        if self.main_entity and hasattr(self.main_entity, "analytics"):
            if not self.main_entity.analytics.devices_analytic_is_ready:
                self.main_entity.analytics.trigger_update(synchronously=True)
            context["analytic"] = self.main_entity.analytics

        # data for dropdowns
        context["os_platform"] = self.operating_systems_dropdown_list
        context["eol_os_releases"] = self.eol_os_releases_dropdown_list
        context["app_versions"] = self.app_versions_dropdown_list
        # in case the user is in the partner devices page, then we can filter by organisation
        if isinstance(self.main_entity, Partner):
            # build organisation list for dropdown
            context["organisations_list"] = self.organisations_dropdown_list

        context["device_ids"] = list(self.object_list.values_list('id', flat=True))
        is_partner = bool(hasattr(self.request.user.profile, 'partner') and self.request.user.profile.partner)
        is_current_partner_user = False
        if is_partner and isinstance(self.main_entity, Partner):
            # check if user is partner user for current partner
            is_current_partner_user = context["device_ids"] \
                                      and self.request.user.profile.partner.id == self.main_entity.id
        is_staff = self.request.user.is_staff and context["device_ids"]
        is_organisation_admin = False
        if isinstance(self.main_entity, Organisation):
            # check if user is organisation admin for current organisation
            is_organisation_admin = self.request.user.profile.is_organisation_admin(self.main_entity.id) and \
                                    context["device_ids"]
        context["can_download_csv"] = is_current_partner_user or is_staff or is_organisation_admin
        context["main_entity_name"] = self.main_entity.__class__.__name__.lower()
        context["device_count"] = len(context["device_ids"])
        context["applied_filters"] = self.applied_filters
        context["filters_display_dict"] = self.get_filtering_display_dict()

        beta_context = self.get_beta_context(self.main_entity)
        return {**context, **beta_context}

    @staticmethod
    def get_status_filter_query(filter_type):
        filter_queries = {
            'awaiting_reports': Q(reports__isnull=True),
            'active': Q(inactive=False),
            'inactive': Q(inactive=True),
        }
        return filter_queries.get(filter_type, Q())

    def set_dropdowns(self, queryset):
        """ Sets dropdowns data for the frontend """
        cache_key = f'{self.request.user.profile.uuid}_dropdowns'
        org_id = self.kwargs.get('org_id')
        if org_id:
            cache_key = f'{org_id}_dropdowns'
        cache_result = cache.get(cache_key)
        if cache_result:
            self.operating_systems_dropdown_list = cache_result['operating_systems_dropdown_list']
            self.eol_os_releases_dropdown_list = cache_result['eol_os_releases_dropdown_list']
            self.app_versions_dropdown_list = cache_result['app_versions_dropdown_list']
            # in case the user is in the partner devices page, then we can filter by organisation
            if isinstance(self.main_entity, Partner):
                # build organisation list for dropdown
                self.organisations_dropdown_list = cache_result['organisations_dropdown_list']
            return
        self.operating_systems_dropdown_list = self.get_operating_systems_dropdown_list(queryset)
        # create list of end of life os releases for display
        self.eol_os_releases_dropdown_list = list(queryset.filter(end_of_life__isnull=False).values_list(
                'end_of_life__pk', 'end_of_life__base_operating_system', 'end_of_life__cycle').distinct())
        # sort alphabetically on a concat string e.g. [windows] + [10, version 22H2 (W)]
        self.eol_os_releases_dropdown_list = sorted(self.eol_os_releases_dropdown_list, key=lambda x: x[1] + x[2])
        # create list of app versions for display and sort in reverse order
        ordering = map(lambda x: "-" + x, self.get_ordering_fields_map()["version"])
        self.app_versions_dropdown_list = list(queryset.order_by(*ordering).values_list('version_annotation', flat=True).distinct())
        # in case the user is in the partner devices page, then we can filter by organisation
        if isinstance(self.main_entity, Partner):
            # build organisation list for dropdown
            self.organisations_dropdown_list = self.get_organisations_dropdown_list(queryset)
        to_cache = {
            'operating_systems_dropdown_list': self.operating_systems_dropdown_list,
            'eol_os_releases_dropdown_list': self.eol_os_releases_dropdown_list,
            'app_versions_dropdown_list': self.app_versions_dropdown_list,
            'organisations_dropdown_list': self.organisations_dropdown_list,
        }
        cache.set(cache_key, to_cache, timeout=300)  # 5 minutes

    def filtering_queryset(self, queryset):
        self.set_dropdowns(queryset)
        queryset_is_secure_filter = self.request.GET.get("secure")
        if queryset_is_secure_filter:
            if queryset_is_secure_filter.lower() == 'true':
                queryset = self.main_entity.secure_devices
            elif queryset_is_secure_filter.lower() == 'false':
                queryset = self.main_entity.insecure_devices
        if self.filtered_by_active_only():
            queryset = queryset.filter(
                reports__created__gte=timezone.now() - datetime.timedelta(days=30),
                reports__isnull=False
            )
        queryset_filters = copy.deepcopy(self.request.GET)
        # remove search if performed as it's not a filter
        if 'search' in queryset_filters:
            queryset_filters.pop('search')
        # remove order_by if performed as it's not a filter
        if 'order_by' in queryset_filters:
            queryset_filters.pop('order_by')
        # remove page if performed as it's not a filter
        if 'page' in queryset_filters:
            queryset_filters.pop('page')

        applicable_filters = {}

        # By default, hide all beta devices while we are in the Beta rollout period.
        applicable_filters['beta'] = ~get_app_install_cap_v_five_beta_q_object() & ~get_app_install_trustd_beta_q_object()

        if queryset_filters:
            for filter_type in queryset_filters.keys():
                # get the filter_type and start building the query
                if filter_type not in applicable_filters:
                    applicable_filters[filter_type] = Q()
                if filter_type == 'beta':
                    # Only one Beta filter can be active at a time.
                    applicable_filters[filter_type] = Q()

                # get the filter values and add them to the query for respective filter type
                for value in queryset_filters.getlist(filter_type):
                    # applied filters are used in the template to display the applied filters
                    self.applied_filters.append(([filter_type, value]))
                    # build the query for the filter type
                    if filter_type == self.DEVICE_TYPE:
                        applicable_filters[filter_type] |= Q(device_type=getattr(AppInstall, value.upper()))
                    elif filter_type == self.STATUS:
                        applicable_filters[filter_type] |= self.get_status_filter_query(value)
                    elif filter_type == self.OS_PLATFORM:
                        applicable_filters[filter_type] |= Q(platform=value)
                    elif filter_type == self.OS_RELEASE:
                        value = value.replace('eolpk_', '')
                        # lookup os_release by the pk
                        applicable_filters[filter_type] |= Q(end_of_life__pk=value)
                    elif filter_type == self.APP_VERSION:
                        applicable_filters[filter_type] |= Q(version_annotation=value)
                    elif filter_type == self.BETA:
                        if value == CAP_V_FIVE_BETA_SWITCH:
                            applicable_filters[filter_type] |= get_app_install_cap_v_five_beta_q_object() & ~get_app_install_trustd_beta_q_object()
                        if value == TRUSTD_BETA_SWITCH:
                            applicable_filters[filter_type] |= get_app_install_trustd_beta_q_object()
                    # in case the user is in the partner devices page, then we can filter by organisation
                    if isinstance(self.main_entity, Partner) and filter_type == 'organisation':
                        applicable_filters[filter_type] |= Q(app_user__organisation=value)
        # gather the filters to apply to the queryset
        built_filters = Q()
        for query in applicable_filters.values():
            built_filters &= query
        # apply the filters to the queryset
        queryset = queryset.filter(built_filters)
        return queryset

    def filtered_by_active_only(self):
        queryset_is_active_only_filter = self.request.GET.get("active-only")
        is_active_only = queryset_is_active_only_filter and queryset_is_active_only_filter.lower() == 'true'
        return is_active_only

    def search_queryset(self, queryset):
        """ Override search method in order to annotate the queryset before searching is done,
        this will allow to search by the last login record only, instead of all. """
        queryset = queryset.annotate(
            last_hist_login_username=Subquery(UserLoginHistory.objects.filter(
                app_install__pk=OuterRef("pk")
            ).order_by("-created").values('user__username')[:1]),
        ).annotate(
            last_hist_login_domain=Subquery(UserLoginHistory.objects.filter(
                app_install__pk=OuterRef("pk")
            ).order_by("-created").values('user__domain')[:1]),
        )
        return super().search_queryset(queryset)

    def get_queryset(self):
        self.main_entity = self.get_main_entity()
        return self.ordering_queryset(
            self.search_queryset(
                self.filtering_queryset(
                    AppInstall.objects.filter(
                        **{self.main_entity.main_entity_filter_name: self.main_entity},
                        app_user__active=True,
                        id__in=Subquery(AppInstall.objects.installs_without_deprecated_duplicates(**{self.main_entity.main_entity_filter_name: self.main_entity}).values_list('id', flat=True))
                    ).annotate(
                        version_annotation=Concat(
                            F('version__major'), Value('.'), F('version__minor'), Value('.'), F('version__patch'),
                            output_field=CharField()
                        )
                    )
                )
            )
        ).select_related(
            "app_user", "app_user__organisation", "analytics", "trustd_device", "version"
        ).prefetch_related("os", "analytics", "app_user").prefetch_related(
            Prefetch("reports", queryset=AppReport.objects.filter(pk__in=Subquery(
                AppReport.objects.filter(
                    app_install__pk=OuterRef("app_install__pk"), total_responses__isnull=False
                ).order_by("-created").values_list("pk", flat=True)[:1]
            )).prefetch_related("installed_software", "installed_software__software"))
        ).distinct().annotate(**self.get_annotate_params(prefix_query_param='app_user__organisation__'))


class BaseCSVReportView(CSVReportMixin, SignupMixin, View):
    """ Base class to handle CSV generation views for partners."""

    type_of_csv = CSVFile.TYPES_OF_CSV.UNKNOWN
    objects_name = ''
    main_entity_name = ''
    csv_generation_task = None

    ORGANISATION_LEVEL = 'organisation'
    PARTNER_LEVEL = 'partner'

    def get_file_name(self) -> str:
        """
        Returns the name of the XLSX file to be generated.
        """
        return (f"{CSVFile.TYPES_OF_CSV[self.type_of_csv].lower().replace(' ', '_')}"
                f"_{timezone.now().strftime('%Y-%m-%d_%H-%M-%S')}")

    def _get_level(self):
        level = self.ORGANISATION_LEVEL if "organisation" in self.main_entity_name else self.PARTNER_LEVEL
        return level

    def _check_permissions(self, user, main_entity_id):
        """
        If user has no permissions for main_entity object, raise PermissionDenied exception.
        CSV generation tasks ensure the main_entity object id is used in the filtering of records exported.
        """
        level = self._get_level()
        if not main_entity_id:
            raise PermissionDenied()
        if level == self.ORGANISATION_LEVEL:
            organisation_id = int(main_entity_id)
            org = get_object_or_404(Organisation, pk=organisation_id)
            org = get_organisations(user, org.secure_id)
            if not org:
                raise PermissionDenied()
        elif level == self.PARTNER_LEVEL:
            partner_id = int(main_entity_id)
            if not user.profile.partner or user.profile.partner.id != partner_id:
                raise PermissionDenied()
        else:
            raise PermissionDenied('No such level supported in CSV export.')

    def get(self, request, **kwargs):
        main_entity_id = request.GET.get(self.main_entity_name)
        self._check_permissions(request.user, main_entity_id)

        # only download if file has been generated and ready
        filters = {self.main_entity_name: main_entity_id, 'type_of_csv': self.type_of_csv}
        csv_file = CSVFile.objects.filter(**filters).last()
        level = self._get_level()

        if csv_file and csv_file.is_complete:
            if self.request.GET.get("format") == "xlsx_link":
                # return link to xlsx file
                return JsonResponse({"location": self.request.get_full_path().replace('format=xlsx_link', 'format=xlsx')})
            elif self.request.GET.get("format") == "xlsx":
                # record download usage
                record_report_download_usage.delay(
                    entity_pk=main_entity_id,
                    level=level,
                    report_name=CSVFile.TYPES_OF_CSV[self.type_of_csv].lower().replace(' ', '_'),
                    file_format="xlsx"
                )
                response = HttpResponse()
                # first write csv to the response
                response.write(csv_file.located_at.read())
                # then convert csv to xlsx
                return self.get_xlsx_http_response(response)
            else:
                # record download usage
                record_report_download_usage.delay(
                    entity_pk=main_entity_id,
                    level=level,
                    report_name=CSVFile.TYPES_OF_CSV[self.type_of_csv].lower().replace(' ', '_'),
                    file_format="csv"
                )
                return JsonResponse({'location': csv_file.located_at.url})
        elif csv_file and csv_file.is_in_progress:
            return JsonResponse({'message': _('Processing... Please return in a couple of minutes to download the file.')})
        return JsonResponse({'error': _('Please regenerate CSV file.')})

    def post(self, request, **kwargs):
        data = json.loads(request.body)
        main_entity_id = data.get(self.main_entity_name)
        self._check_permissions(request.user, main_entity_id)
        objects_ids = json.loads(data.get(self.objects_name))
        filters = {self.main_entity_name: main_entity_id, 'type_of_csv': self.type_of_csv}
        csv_file = CSVFile.objects.filter(**filters).last()
        task_kwargs = {self.main_entity_name: main_entity_id, 'objects_ids': objects_ids}
        if csv_file and csv_file.is_complete:
            self.csv_generation_task.delay(**task_kwargs)
        elif not csv_file:
            self.csv_generation_task.delay(**task_kwargs)
        return JsonResponse({'message': _('Processing... Please return in a couple of minutes to download the file.')})


class FEStoryBook(StaffRequiredMixin, TemplateView):
    template_name = "admin/story-book/main.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data()
        context['hide_nav_bar'] = True
        context['organisation'] = {
            "secure_id": "94d351ad-5297-4575-a0ee-eb15e7389bfa"
        }
        context['selectable_row_items'] = [
            {"pk": 111, "name": "Name 1"},
            {"pk": 222, "name": "Name 2"}
        ]

        return context


def csrf_failure_view(request, reason=""):
    """
    Custom CSRF failure view that handles login page CSRF failures gracefully.
    """
    # If this is a CSRF failure on the login page, redirect silently
    # This prevents users seeing CSRF failure page if they
    # open 2 login pages at the same time, and
    # login on the first one, and then login on the 2nd one.
    if request.path == '/login/':
        return redirect('/login/')

    # For other pages, use the default CSRF failure response
    return csrf_failure(request, reason)


class RedirectIfAuthenticatedLoginView(LoginView):
    """
    Custom login view that checks if user is already authenticated
    to prevent CSRF token issues with password managers.
    """

    def dispatch(self, request, *args, **kwargs):
        # If user is already authenticated, redirect to home with a message
        # This prevents CSRF token issues when users try to login
        # from stale tabs while already authenticated.
        if request.user.is_authenticated:
            message_part1 = _('You are logged in as')
            message_part2 = _('To use a different account, please logout first.')
            message = f'{message_part1} {request.user.email}. {message_part2}'
            messages.info(request, message)
            return redirect('/')
        return super().dispatch(request, *args, **kwargs)



