import datetime
import decimal
import json
import os
import re
import uuid
from collections.abc import Iterable
from decimal import Decimal
from logging.handlers import RotatingFileHandler
from urllib.parse import urljoin
from uuid import UUID

import qrcode
import six
import waffle
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.paginator import EmptyPage, Paginator
from django.core.validators import validate_email
from django.forms import ValidationError
from django.shortcuts import reverse
from django.test import RequestFactory
from django.utils import timezone
from django.utils.html import strip_tags
from django.utils.http import urlsafe_base64_decode
from PIL import Image


class DeferredFileHandler(RotatingFileHandler):
    """
    Custom filehandler that uses the LOG_ROOT setting to determine the folder
    to store log files in.
    """

    def __init__(self, filename, *args, **kwargs):
        # We have to do some tricky stuff to avoid circular imports.  To this end,
        # we pass /dev/null to the parent handler but specify opening to be delayed.
        # Then when we try to first open the file, we join the LOG_ROOT with the
        # passed filename.
        self.filename = filename
        kwargs['delay'] = True
        RotatingFileHandler.__init__(self, "/dev/null", *args, **kwargs)

    def _open(self):
        # We import settings here to avoid a circular reference as this module
        # will be imported when settings.py is executed.
        from django.conf import settings
        self.baseFilename = os.path.join(settings.LOG_ROOT, self.filename)
        return RotatingFileHandler._open(self)


class UUIDEncoder(json.JSONEncoder):

    def default(self, obj):  # pylint: disable=E0202
        if isinstance(obj, UUID):
            # if the obj is uuid, we simply return the value of uuid
            return str(obj)  # '12345678-1234-5678-1234-************'
        return json.JSONEncoder.default(self, obj)


def divide(value, arg):
    if value == 0 or arg == 0 or not isinstance(value, (int, float)) or not isinstance(arg, (int, float)):
        return 0
    try:
        if int(value) <= int(arg):
            return int(round((float(value) / int(arg) * 100)))
        else:
            return int(round((float(arg) / int(value) * 100)))
    except (ValueError, ZeroDivisionError):
        return 0


def percent(total, passs):
    if total == 0 or passs == 0 or not isinstance(total, (int, float)) or not isinstance(passs, (int, float)):
        return 0
    try:
        if int(total) <= int(passs):
            return int(round((float(passs) / int(passs) * 100)))
        else:
            return int(round((float(passs) / int(total) * 100)))
    except (ValueError, ZeroDivisionError):
        return 0


def has_completed_onboarding(user):
    # 1. Created Orgnaisation (its me or many) (org)
    # 2. added users (sync)
    # 3. paymnet skiped
    # 4. enroll
    pass


def generate_email_chart(total_data, last_week_data, titles):
    """
    Generates html chart.
    :param total_data: total counters excluded last week
    :type total_data: list[int]
    :param last_week_data: counters for last week
    :type last_week_data: list[int]
    :param titles: data titles
    :type titles: list
    :return: image
    :rtype: image object
    """
    # # style theme
    # plt.style.use(json.load(open(join(settings.BASE_DIR, 'matplotlib_theme.json'))))
    #
    # # chart size
    # y_pos = np.arange(len(titles))
    # fig = plt.figure(figsize=(10, 8))
    # ax = fig.add_subplot(111)
    # # padding
    # plt.subplots_adjust(left=0.2, right=0.9, top=0.9, bottom=0.1)
    #
    # # total bar
    # total_bar = ax.barh(
    #     y_pos,
    #     total_data,
    #     color=['#4f2671'] * (len(titles) - 2) + ['#0ead69', '#e03c4c'],
    #     align='center',
    #     height=0.35,
    #     edgecolor=['#8258A6'] * (len(titles) - 2) + ['green', '#a51c30'],
    #     linewidth=[2] * len(titles)
    # )
    # # counter values for total bar
    # for rect in total_bar:
    #     if rect.get_width():
    #         ax.text(
    #             rect.get_x() + rect.get_width() / 2.,
    #             rect.get_y() + rect.get_height() / 2.,
    #             '{0}'.format(rect.get_width()),
    #             ha='center',
    #             va='center',
    #             color='white'
    #         )
    #
    # # last week bar
    # line_width = [2 if val else 0 for val in last_week_data]
    # last_week_bar = ax.barh(
    #     y_pos,
    #     last_week_data,
    #     color='#084887',
    #     align='center',
    #     left=total_data,
    #     height=0.35,
    #     edgecolor=['#3e8cd8'] * len(titles),
    #     linewidth=line_width
    # )
    # # counter values for last week bar
    # for rect in last_week_bar:
    #     if rect.get_width():
    #         ax.text(
    #             rect.get_x() + rect.get_width() / 2,
    #             rect.get_y() + rect.get_height() / 2.,
    #             '+{0}'.format(rect.get_width()),
    #             ha='center',
    #             va='center',
    #             color='white'
    #         )
    #
    # # labels and legends
    # ax.set_yticks(y_pos)
    # ax.set_yticklabels(titles, {'color': '#74787e', 'family': 'Arial'}, family='Arial')
    # # ax.set_xlabel('Partners / End Users')
    # ax.set_title('What\'s changed last week', {
    #     'fontsize': 15,
    #     'verticalalignment': 'baseline',
    #     'horizontalalignment': 'center',
    #     'color': '#4f2671',
    #     'y': 1.04,
    #     'x': 0.44,
    # }, fontproperties=FontProperties(family='Arial', weight='demi', size='large'))
    # ax.legend((total_bar[0], last_week_bar[0]), ('Total', 'Last Week'))
    # # force tick labels to integers
    # ax.xaxis.set_major_locator(MaxNLocator(integer=True))
    #
    # ax.set_facecolor('#EDEFF2')
    #
    # # generate base64 image
    # img = StringIO()
    # ax.get_figure().savefig(img, format='png')
    # return img.getvalue()


def get_http_protocol_url_name():
    """
    Returns http protocol name for url.
    :return: protocol name
    :rtype: str
    """
    return 'https' if settings.IS_PROD or settings.IS_STAGE or settings.IS_DEVELOP else 'http'


def get_appusers_status_data(org):
    datetime_object = datetime.datetime.now()

    # Decide whether email should show devices or users
    if org.is_bulk_enrollment_type:
        # Devices

        installs_total = org.installed_devices_count
        if installs_total:
            # Installed devices
            installs_last_week = org.installed_devices.filter(
                created__gt=datetime_object - datetime.timedelta(weeks=1)
            ).count()

            # Passed devices
            passed_total = org.secure_devices_count
            passed_added_last_week = org.secure_devices.filter(
                created__gt=datetime_object - datetime.timedelta(weeks=1)
            ).count()
            started_passing_last_week = org.secure_devices.filter(
                analytics__date_started_passing__gt=datetime_object - datetime.timedelta(weeks=1)
            ).count()

            # Manual fixes
            manual_fixes = org.secure_devices.filter(
                manual_fix__app_check__active=True
            ).distinct().count()

            # Failed devices
            failed_total = org.insecure_devices_count
            failed_added_last_week = org.insecure_devices.filter(
                created__gt=datetime_object - datetime.timedelta(weeks=1)
            ).count()
            started_failing_last_week = org.insecure_devices.filter(
                analytics__date_started_failing__gt=datetime_object - datetime.timedelta(weeks=1)
            ).count()

            # Removed devices
            removed_total = org.removed_devices_count
            removed_last_week = org.removed_devices.filter(
                date_removed__gt=datetime_object - datetime.timedelta(weeks=1)
            ).count()

            # Recent Check-ins
            recent_total = org.analytics.active_devices_30d_count

        else:
            installs_total = 0
            installs_last_week = 0
            passed_total = 0
            passed_added_last_week = 0
            started_passing_last_week = 0
            failed_total = 0
            failed_added_last_week = 0
            started_failing_last_week = 0
            removed_total = 0
            removed_last_week = 0
            manual_fixes = 0
            recent_total = 0

        return {
            'failing': {
                'title': 'Devices Failing',
                'total': failed_total,
                'last_week': started_failing_last_week,
                'added_last_week': failed_added_last_week
            },
            'passing': {
                'title': 'Devices Passing',
                'total': passed_total,
                'last_week': started_passing_last_week,
                'added_last_week': passed_added_last_week
            },
            'manualfix': {
                'title': 'Devices with Manual Fixes',
                'total': manual_fixes
            },
            'recentcheckin': {
                'title': 'Recent Check-ins',
                'total': recent_total
            },
            'installed': {'title': 'Devices Installed', 'total': installs_total, 'last_week': installs_last_week},
            'removed': {'title': 'Devices Removed', 'total': removed_total, 'last_week': removed_last_week}
        }
    else:
        # Users

        # Recent Check-ins
        recent_total = org.analytics.active_users_30d_count

        # Enrolled users
        enrolled_total = org.enrolled_users.count()
        enrolled_last_week = org.enrolled_users.filter(
            created__gt=datetime_object - datetime.timedelta(weeks=1)
        ).count()

        # Users with at least one app installed
        installs_total = org.installed_users_count
        installs_last_week = org.installed_users.filter(
            created__gt=datetime_object - datetime.timedelta(weeks=1)
        ).count()

        # Passed users
        passed_total = org.secure_users_count
        passed_added_last_week = org.passed_users.filter(
            created__gt=datetime_object - datetime.timedelta(weeks=1)
        ).count()
        started_passing_last_week = org.passed_users.filter(
            analytics__date_started_passing__gt=datetime_object - datetime.timedelta(weeks=1)
        ).count()

        # Manual fixes
        manual_fixes = org.passed_users.filter(
            installs__manual_fix__app_check__active=True
        ).distinct().count()

        # Failed users
        failed_total = org.insecure_users_count
        failed_added_last_week = org.failed_users.filter(
            created__gt=datetime_object - datetime.timedelta(weeks=1)
        ).count()
        started_failing_last_week = org.failed_users.filter(
            analytics__date_started_failing__gt=datetime_object - datetime.timedelta(weeks=1)
        ).count()

        return {
            'failing': {
                'title': 'Users Failing',
                'total': failed_total,
                'last_week': started_failing_last_week,
                'added_last_week': failed_added_last_week
            },
            'passing': {
                'title': 'Users Passing',
                'total': passed_total,
                'last_week': started_passing_last_week,
                'added_last_week': passed_added_last_week
            },
            'manualfix': {
                'title': 'Users with Manual Fixes',
                'total': manual_fixes
            },
            'recentcheckin': {
                'title': 'Recent Check-ins',
                'total': recent_total
            },
            'installed': {'title': 'Users Installed', 'total': installs_total, 'last_week': installs_last_week},
            'enrolled': {'title': 'Users Enrolled', 'total': enrolled_total, 'last_week': enrolled_last_week}
        }


def strip_control_characters(raw_string):
    try:
        c = unichr
    except NameError:
        c = chr

    if raw_string:
        # unicode invalid characters
        RE_XML_ILLEGAL = u'([\u0000-\u0008\u000b-\u000c\u000e-\u001f\ufffe-\uffff])' + \
                         u'|' + \
                         u'([%s-%s][^%s-%s])|([^%s-%s][%s-%s])|([%s-%s]$)|(^[%s-%s])' % \
                         (c(0xd800), c(0xdbff), c(0xdc00), c(0xdfff),
                          c(0xd800), c(0xdbff), c(0xdc00), c(0xdfff),
                          c(0xd800), c(0xdbff), c(0xdc00), c(0xdfff),
                          )
        raw_string = re.sub(RE_XML_ILLEGAL, "", raw_string)

        # ascii control characters
        raw_string = re.sub(r"[\x01-\x1F\x7F]", "", raw_string)

    return raw_string


def median_value(values):
    """
    Returns median value of passed sequence.
    :param values: sequence of integers
    :type values: list
    :return: median value
    :rtype: int
    """
    count = len(values)
    values.sort()
    if count % 2 == 1:
        return int(values[int(round(count/2))])
    else:
        return int(sum(values[int(count/2-1):int(count/2+1)])/Decimal(2.0))


def remove_special_characters_from_filename(filename):
    """
    Removes special characters that may lead to access problems from passed filename.
    :param filename: file name
    :type filename: str or unicode
    :return: filename where all special characters replaced to empty string
    :rtype: str
    """
    return filename.replace(
        '\\', ''
    ).replace(
        '/', ''
    ).replace(
        ':', ''
    ).replace(
        '*', ''
    ).replace(
        '"', ''
    ).replace(
        '<', ''
    ).replace(
        '>', ''
    ).replace(
        '|', ''
    )


def decode_base64_to_string(base64_string):
    """
    Decodes base64 string utf-8 string.
    :param base64_string: base64 string
    :type base64_string: base64
    :return: decoded to utf-8 string or None
    :rtype: unicode or str or None
    """
    if not base64_string:
        return None
    try:
        base64_string = urlsafe_base64_decode(base64_string)
    except Exception:
        return None
    else:
        try:
            base64_string = base64_string.decode('utf-8')
        except UnicodeDecodeError:
            return None
        else:
            return base64_string


def clean_serial_number(serial_number: str) -> str:
    """
    Removes quotation marks from a serial number to prevent duplicate records.

    :param serial_number: The serial number that might contain quotation marks
    :type serial_number: str
    :return: The serial number with quotation marks removed
    :rtype: str
    """
    if not serial_number:
        return serial_number

    return serial_number.replace('"', '').replace("'", '')


def escape_csv(cell):
    """
    Escapes dangerous symbols from csv cell.
    :param cell: csv cell
    :type cell: str or unicode
    :return: escaped csv cell
    :rtype: str or unicode
    """
    if cell and isinstance(cell, six.string_types):
        cell.encode('utf-8')
    return cell


def handle_user_input(user_input):
    """
    This function is intended to be applied to all inputs from users before saving content to the database.
    :param user_input: user input
    :type user_input: str or unicode
    :return: handled input
    :rtype: str or unicode
    """
    # first check if user input is string
    if isinstance(user_input, six.string_types):
        # remove html tags
        user_input = strip_tags(user_input)
        # convert " to ''
        user_input = user_input.replace("\"", "''")
    # if user input is not string just return passed value
    return user_input


def uuid_is_valid(raw_uuid):
    """
    Checks if passed UUID is valid.
    :param raw_uuid: UUID
    :type raw_uuid: uuid.UUID
    :return: True or False
    :rtype: bool
    """
    try:
        uuid.UUID(str(raw_uuid))
    except ValueError:
        return False
    else:
        return True


def is_customer_success(user):
    return user.groups.filter(name='Customer Success').exists()


def is_technical_support(user):
    return user.groups.filter(name='Technical Support').exists()


def add_user(user, organisation, admin_user):
    from appusers.models import AppUser
    from dashboard.utils import set_dashboard_admin

    is_admin = user.get('is_admin')
    if not organisation.has_software_support:
        # for cert only organisations added user will be always dashboard admin
        # since they don't use applications
        is_admin = True

    app_user, created = AppUser.objects.get_or_create(
        organisation=organisation,
        email=handle_user_input(user['email']),
        defaults={
            "first_name": handle_user_input(user['first_name']),
            "last_name": handle_user_input(user['last_name']),
            "is_admin": is_admin
        }
    )
    if not created and app_user.active:
        # Skip existing users
        return None, False

    if not created and user.get('is_social', False) and hasattr(organisation, 'organisationusersync'):
        organisation.organisationusersync.set_user_as_enrolled(user['email'])

    if not app_user.active:
        # if app user was removed then make it active again and update its info
        app_user.active = True
        app_user.first_name = handle_user_input(user['first_name'])
        app_user.last_name = handle_user_input(user['last_name'])
        app_user.is_admin = is_admin
        app_user.save()

    if is_admin:
        set_dashboard_admin(app_user, admin_user)
    return app_user.uuid, True


def is_valid_email(email):
    try:
        validate_email(email)
    except ValidationError:
        return False
    else:
        return True


def get_invalid_emails(users):
    invalid_emails = []
    for user in users:
        email = user.get("email", "")
        if not is_valid_email(email):
            invalid_emails.append(email)
    return invalid_emails


def flatten(lis):
    for item in lis:
        if isinstance(item, Iterable) and not isinstance(item, str):
            for x in flatten(item):
                yield x
        else:
            yield item


def create_secret_pair(value, lifetime=datetime.timedelta(days=365)):
    """
    Creates a secret pair for passed value.
    :param value: string no longer than 255 characters
    :type value: str
    :param lifetime: pair lifetime
    :type lifetime: timedelta
    :return: pair key
    :rtype: str
    """
    if isinstance(value, str):
        from .models import SecretPair
        return SecretPair.objects.create(value=value[:255], lifetime=lifetime).key


def get_secret_pair_value(key):
    """
    Returns secret pair value for passed key.
    :param key: pair key
    :type key: str
    :return: pair value
    :rtype: str
    """
    if isinstance(key, str):
        from .models import SecretPair
        pair = SecretPair.objects.filter(key=key).first()
        if pair:
            # check if this pair didn't expire
            if timezone.now() <= pair.created + pair.lifetime:
                return pair.value


def reverse_absolute(view_name, kwargs):
    """
    Returns reversed absolute URL including scheme and domain name.
    :return: absolute url
    :rtype: str
    """
    from django.contrib.sites.models import Site
    return urljoin(
        "{0}://{1}".format(get_http_protocol_url_name(), Site.objects.get_current().domain),
        reverse(view_name, kwargs=kwargs)
    )

def get_absolute_site_url():
    """
    Returns absolute site URL including scheme and domain name.
    """
    from django.contrib.sites.models import Site
    return "{0}://{1}".format(get_http_protocol_url_name(), Site.objects.get_current().domain)


class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, decimal.Decimal):
            return str(o)
        return super(DecimalEncoder, self).default(o)


class SafePaginator(Paginator):
    """
    Paginator that returns last page if requested page is empty.
    """
    def validate_number(self, number):
        """
        Validates the given 1-based page number.
        """
        try:
            return super(SafePaginator, self).validate_number(number)
        except EmptyPage:
            if number > 1:
                return self.num_pages
            else:
                raise


def get_full_name_or_email(user):
    """ Given an instance of User or AppUser it will return full name or email """
    if user.first_name and user.last_name:
        return f'{user.first_name} {user.last_name}'
    return str(user.email)


def get_full_name_or_email_from_values(email, first_name, last_name):
    """ Given the values of email, first_name and last_name, return full name or email """
    if first_name and last_name:
        return f'{first_name} {last_name}'
    return str(email)


def can_add_fakes(request):
    """ Checks if user is allowed to add any fake data.
    User is allowed to add fakes if:
        * env is not PROD
        * must be staff user and is impersonating a "Demo Accounts" group user
    """
    if not settings.IS_PROD:
        return True
    if request.user.is_authenticated:
        if hasattr(request, 'impersonator') and getattr(request.impersonator, 'is_staff', request.user.is_staff) and \
                request.user.groups.filter(name='Demo Accounts').exists():
            return True
    return False


def build_qr_code(unique_id, url, cs_logo=True):
    '''
    :param unique_id: string identifier for the QR code
    :param url: URL to be encoded
    :param cs_logo: boolean to add CS logo in the middle of the QR code
    :return: QR code image URL
    '''
    basewidth = 70
    if cs_logo:
        # get CS logo to put in the middle of the QR code
        logo = Image.open(os.path.join(settings.BASE_DIR, 'static/icons/cs_icon.png'))
        # adjust image size
        wpercent = (basewidth / float(logo.size[0]))
        hsize = int((float(logo.size[1]) * float(wpercent)))
        logo = logo.resize((basewidth, hsize), Image.LANCZOS)

    qr_obj = qrcode.QRCode(
        error_correction=qrcode.constants.ERROR_CORRECT_L,
    )
    # embed deep link into QR code
    qr_obj.add_data(url)
    # generate QR code
    qr_obj.make()
    # adding color to QR code
    qr_image = qr_obj.make_image(
        fill_color='#344054',
        back_color="white",
    ).convert('RGB')
    if cs_logo:
        # set size of QR code logo
        pos = ((qr_image.size[0] - logo.size[0]) // 2,
               (qr_image.size[1] - logo.size[1]) // 2)
        # add logo into QR code
        qr_image.paste(logo, pos)

    # save the generated QR code
    with default_storage.open(f'qr_codes/{unique_id}.png', 'wb') as image:
        qr_image.save(image, "PNG")
    image_path = os.path.join(settings.MEDIA_URL, f'qr_codes/{unique_id}.png')
    if not settings.IS_DEV:
        image_path = f'https://{settings.AWS_S3_CUSTOM_DOMAIN}/qr_codes/{unique_id}.png'
    return image_path


def waffle_flag_is_active_for_user(user, flag):
    '''
    Waffle does not have a native way to check if a flag is active for a user.
    This function is a workaround to check if a flag is active for a user by mocking a request.
    See: https://github.com/jazzband/django-waffle/issues/154
    '''
    rf = RequestFactory()
    fake_req = rf.get('/')
    fake_req.user = user
    return waffle.flag_is_active(fake_req, flag)


def save_django_admin_shell_command_execution(data):
    from common.models import DjangoAdminShellRecord
    user = data['user']
    code = data['code']
    response = data.get('response')
    if response:
        result = response.get('out', '')[:5000]
    DjangoAdminShellRecord.objects.create(user=user, code=code, result=result)


def _get_first_business_day_of_month(today: timezone.localdate = None) -> datetime.date:
    if today is None:
        today = timezone.localdate()
    start_of_month = today.replace(day=1)
    start_of_month_weekday = start_of_month.weekday()
    if start_of_month_weekday < 5:
        return start_of_month
    return start_of_month + datetime.timedelta(days=7 - start_of_month_weekday)


def is_month_first_business_day() -> bool:
    today = timezone.localdate()
    return today == _get_first_business_day_of_month(today)


def _get_last_business_day_of_month(today: timezone.localdate = None) -> datetime.date:
    if today is None:
        today = timezone.localdate()
    # Get first day of next month
    if today.month == 12:
        next_month = today.replace(year=today.year + 1, month=1, day=1)
    else:
        next_month = today.replace(month=today.month + 1, day=1)
    # Get last day of current month
    end_of_month = next_month - datetime.timedelta(days=1)
    end_of_month_weekday = end_of_month.weekday()
    if end_of_month_weekday < 5:
        return end_of_month
    return end_of_month - datetime.timedelta(days=end_of_month_weekday - 4)


def is_month_last_business_day() -> bool:
    today = timezone.localdate()
    return today == _get_last_business_day_of_month(today)


def get_table_cache_key(db_alias, table):
    """
    Generates a cache key from a SQL table.
    Modified to the alias and table name instead of a hash.

    :arg db_alias: Alias of the used database
    :type db_alias: str or unicode
    :arg table: Name of the SQL table
    :type table: str or unicode
    :return: A cache key
    :rtype: str
    """
    cache_key = f'cachalot_tables:{db_alias}:{table}'
    return cache_key


def get_query_cache_key(compiler):
    from cachalot.utils import check_parameter_types
    from hashlib import sha1
    """
    Generates a cache key from a SQLCompiler.
    Modified to include the alias and first FROM table name as well as the query hash.

    This cache key is specific to the SQL query and its context
    (which database is used).  The same query in the same context
    (= the same database) must generate the same cache key.

    :arg compiler: A SQLCompiler that will generate the SQL query
    :type compiler: django.db.models.sql.compiler.SQLCompiler
    :return: A cache key
    :rtype: string
    """
    sql, params = compiler.as_sql()
    check_parameter_types(params)
    cache_key = f'{compiler.using}:{sql}:{[str(p) for p in params]}'
    # Set attribute on compiler for later access
    # to the generated SQL. This prevents another as_sql() call!
    compiler.__cachalot_generated_sql = sql.lower()

    # extract first from table name in the sql query
    match = re.search(r'\bFROM\s+["`]?([\w\.]+)["`]?', sql, flags=re.IGNORECASE)
    table_name = match.group(1) if match else ''

    # generate cache key
    cache_key = f"cachalot_queries:{compiler.using}:{table_name}:{sha1(cache_key.encode('utf-8')).hexdigest()}"
    return cache_key
