import base64
import logging
import random
import re
import string
from secrets import choice
from typing import Optional, <PERSON><PERSON>

import pdfkit
from django.db.models import Count, F, TextField, Value, query
from django.db.models.functions import Cast, Coalesce
from django.template.loader import render_to_string
from django.utils import timezone
from semantic_version import Version

from appusers.models import AppUser, AppVersion
from beta_features.models import CAP_V_FIVE_BETA_SWITCH, TRUSTD_BETA_SWITCH, STABLE_VERSION_KIND, \
    STABLE_MOBILE_VERSION_KIND
from beta_features.utils import is_cap_v_five_ga_enabled
from emails.tasks import send_download_app_email, send_cap_v_five_pipeline_version_email
from organisations.models import OrganisationPolicy
from rulebook.models import AppCheck

PLATFORM_UNKNOWN = ""
PLATFORM_WIN32 = "win32"
PLATFORM_DARWIN = "darwin"
PLATFORM_IOS = "ios"
PLATFORM_IPADOS = "ipados"
PLATFORM_ANDROID = "android"

MOBILE_PLATFORMS = (PLATFORM_IOS, PLATFORM_ANDROID, PLATFORM_IPADOS)
MOBILE_PLATFORMS_DISPLAY = {
    PLATFORM_IOS: "iOS",
    PLATFORM_ANDROID: "Android",
    PLATFORM_IPADOS: "iPadOS"
}
DESKTOP_PLATFORMS = (PLATFORM_DARWIN, PLATFORM_WIN32)

PLATFORMS = MOBILE_PLATFORMS + DESKTOP_PLATFORMS

logger = logging.getLogger(__name__)


def send_apps(organisation, uuid_list=None, partner_user_name=None, first_email=False, reminder=False):
    """
    In case of all enrollment types it sends emails with download links to all enrolled app users.
    :param organisation: send emails to this organisation
    :type organisation: organisations.Organisation
    :param uuid_list: if passed sends email only for passed app users
    :type uuid_list: list
    :param partner_user_name: partner user name
    :type partner_user_name: str or None
    :param reminder: if this emails is supposed to be a reminder (when user has been emailed before already)
    :type reminder: bool
    :return: nothing
    :rtype: None
    """
    send_emails_to_users_with_kind(
        organisation, uuid_list, kind=STABLE_VERSION_KIND, first_email=first_email,
        partner_user_name=partner_user_name, reminder=reminder
    )

def send_emails_to_users_with_kind(
    organisation,
    uuid_list=None,
    kind: str = STABLE_VERSION_KIND,
    first_email=False,
    partner_user_name=None,
    reminder=False
):
    """
    :param uuid_list:
    :type uuid_list: list of strings or queryset or None

    :return: True if async jobs were scheduled, False if not
    :rtype: bool
    """
    if not uuid_list and not first_email:
        logger.debug("No app_users to send email to, and not first_email=True. Skipping.")
        return False

    # for UBA organisations we do allow sending emails only if it is to install Trustd mobile app
    if organisation.bulk_install and not first_email and not (organisation.is_uba_type and kind == TRUSTD_BETA_SWITCH):
        logger.debug("Not sending emails to bulk_installed organisations that are not first_email.")
        return False

    arr_app_users = uuid_list
    if uuid_list is None:
        arr_app_users = list(organisation.app_users.filter(active=True).values_list('uuid', flat=True))
    elif isinstance(uuid_list, query.QuerySet) and uuid_list.model is AppUser:
        arr_app_users = list(uuid_list.values_list('uuid', flat=True))

    if kind not in [STABLE_VERSION_KIND, STABLE_MOBILE_VERSION_KIND, CAP_V_FIVE_BETA_SWITCH, TRUSTD_BETA_SWITCH]:
        raise ValueError('Invalid email kind')

    if kind == STABLE_VERSION_KIND:
        if is_cap_v_five_ga_enabled():
            send_cap_v_five_pipeline_version_email.delay(arr_app_users)
        else:
            send_download_app_email.delay(arr_app_users, partner_user_name=partner_user_name, reminder=reminder)
    elif kind == CAP_V_FIVE_BETA_SWITCH:
        send_cap_v_five_pipeline_version_email.delay(arr_app_users)
    elif kind == TRUSTD_BETA_SWITCH or kind == STABLE_MOBILE_VERSION_KIND:
        from trustd.tasks import enrol_users_with_trustd
        enrol_users_with_trustd.delay(arr_app_users)

    return True


def get_steps_to_secure_pdf(app_install, request):

    """
    Returns pdf file with steps to secure for passed app install.
    :param app_install: app install
    :type app_install: AppInstall instance
    :param request: request
    :type: request: django.http.request
    :return: pdf file
    :rtype: pdf
    """
    from dashboard.views import device_detail_context

    context = device_detail_context(app_install, request)
    template = render_to_string('dashboard/device_detail_export_pdf.html', context, request=request)

    return pdfkit.from_string(
        template,
        False,
        options={
            'page-size': 'A4',
            '--enable-local-file-access': None,
            'quiet': ''
        }
    )


def determine_device_type(app_install):
    """
    Determines app install type either Mobile, Desktop or HealthCheck.
    :param app_install: app install
    :type app_install: appusers.AppInstall
    :return: device type
    :rtype: int
    """
    if app_install.app_user.health_check_user:
        return app_install.HEALTH_CHECK
    elif app_install._is_server:
        return app_install.SERVER
    elif app_install._is_virtual_server:
        return app_install.VIRTUAL_SERVER
    elif app_install._is_virtual_desktop:
        return app_install.VIRTUAL_DESKTOP
    elif app_install.platform.lower() in DESKTOP_PLATFORMS:
        return app_install.DESKTOP
    elif app_install.platform.lower() in MOBILE_PLATFORMS:
        return app_install.MOBILE
    else:
        return app_install.UNKNOWN


class DeviceType:
    """
    Identifies device type based on information from device.
    """
    def __init__(
            self,
            health_check_app_user: bool,
            os_release: str,
            platform: str,
            caption: str,
            machine_vendor: str,
            machine_model: str
    ):
        """
        :param health_check_app_user: is app user health check user
        :param os_release: operating system release
        :param platform: operating system platform
        :param caption: device caption
        :param machine_vendor: device vendor
        :param machine_model: device model
        """
        self.health_check_app_user = health_check_app_user
        self.os_release = os_release
        self.platform = platform
        self.caption = caption
        self.machine_vendor = machine_vendor
        self.machine_model = machine_model

    def __call__(self):
        """
        Returns device type.
        """
        from appusers.models import AppInstall

        if self.health_check_app_user:
            return AppInstall.HEALTH_CHECK
        elif self._is_server:
            return AppInstall.SERVER
        elif self._is_virtual_server:
            return AppInstall.VIRTUAL_SERVER
        elif self._is_virtual_desktop:
            return AppInstall.VIRTUAL_DESKTOP
        elif self.platform.lower() in DESKTOP_PLATFORMS:
            return AppInstall.DESKTOP
        elif self.platform.lower() in MOBILE_PLATFORMS:
            return AppInstall.MOBILE
        else:
            return AppInstall.UNKNOWN

    def __find_in_machine_info(self, software_name: str) -> bool:
        """
        Returns True if device is virtual desktop otherwise returns False.
        """
        return software_name in (
            self.machine_model or ""
        ).lower() or software_name in (
            self.machine_vendor or ""
        ).lower()

    @property
    def _is_server(self) -> bool:
        """
        Returns True if device is running on server OS otherwise returns False.
        """
        from operating_systems.models import OSBuild
        from operating_systems.utils import get_os_version_and_build

        _, build = get_os_version_and_build(self.os_release)
        return self.caption and "server" in self.caption.lower() and OSBuild.objects.filter(
            build=build, version__release_name__icontains="server"
        ).exists()

    @property
    def _is_virtual_server(self) -> bool:
        """
        Returns True if device is running on virtual server OS otherwise returns False.
        """
        return any([
            all([self.__find_in_machine_info("vmware"), self.__find_in_machine_info("Virtual Platform")]),
            all([self.__find_in_machine_info("vmware"), self.caption and "server" in self.caption.lower()])
        ])

    @property
    def _is_virtual_desktop(self) -> bool:
        """
        Returns True if device is running on virtual desktop OS otherwise returns False.
        """
        return any([
            self.is_vmware_virtual_desktop, self.is_virtualbox_virtual_desktop, self.is_parallels_virtual_desktop,
            self.is_qemu_virtual_desktop, self.is_hyper_v_virtual_desktop
        ])

    @property
    def is_vmware_virtual_desktop(self) -> bool:
        """
        Returns True if device is virtual desktop and running by vmware software otherwise returns False.
        """
        return self.__find_in_machine_info("vmware")

    @property
    def is_virtualbox_virtual_desktop(self) -> bool:
        """
        Returns True if device is virtual desktop and running by virtualbox software otherwise returns False.
        """
        return self.__find_in_machine_info("virtualbox")

    @property
    def is_parallels_virtual_desktop(self) -> bool:
        """
        Returns True if device is virtual desktop and running by parallels software otherwise returns False.
        """
        return self.__find_in_machine_info("parallels")

    @property
    def is_qemu_virtual_desktop(self) -> bool:
        """
        Returns True if device is virtual desktop and running by qemu software otherwise returns False.
        """
        return self.__find_in_machine_info("qemu")

    @property
    def is_hyper_v_virtual_desktop(self) -> bool:
        """
        Returns True if device is virtual desktop and running by hyper-v software otherwise returns False.
        """
        return self.__find_in_machine_info("virtual machine") and self.__find_in_machine_info("microsoft corporation")


def get_device_platform(app_install):
    """
    Returns app install platform.
    :param app_install: app install
    :type app_install: appusers.AppInstall
    :return: device platform
    :rtype: str
    """
    platform = app_install.platform.lower() if app_install.platform else ""
    if platform not in PLATFORMS:
        return PLATFORM_UNKNOWN

    return platform


def generate_app_registration_token(length=100):
    """
    Generates AppInstall unique registration token.
    :param length: token length
    :type length: int
    :returns: generated token
    :rtype: str
    """
    return ''.join([choice(string.ascii_uppercase + string.digits) for _ in range(length)])


def api_debug_get_checks(app_install, last_report) -> list[dict]:
    """
    Returns last report checks if there is last report
    otherwise generates random checks result.
    """
    if last_report:
        # take check results from the last app report
        # and put them as API response
        return [{
            "question_id": r.app_check.pk,
            "report_only": r.report_only,
            "response": r.response,
            "response_text": r.response_text
        } for r in last_report.check_results.all()]
    else:
        # otherwise generate random checks result
        return [{
            "question_id": check.pk,
            "report_only": random.choice([True, False]),
            "response": random.choice([True, False]),
            "response_text": "response"
        } for check in AppCheck.objects.filter(
            commands__isnull=False, commands__os_id=app_install.os_id, active=True,
            commands__min_app_version__lte=app_install.get_valid_app_version()
        ).distinct()]


def api_debug_get_network_interfaces(app_install):
    """
    Returns application network interfaces.
    :param app_install: installed application
    :type app_install: appusers.AppInstall
    :return: app network interfaces
    :rtype: list
    """
    return [{
        "local_ip": interface.local_ip,
        "public_ip": interface.public_ip,
        "mac_address": interface.mac_address,
        "gateway_ip": interface.gateway_ip,
        "gateway_mac": interface.gateway_mac,
        "cidr": "cidr"
    } for interface in app_install.network_interfaces.all()]


def api_debug_get_policies(app_install):
    """
    Returns application policies.
    :param app_install: installed application
    :type app_install: appusers.AppInstall
    :return: app policies
    :rtype: list
    """

    groups = app_install.app_user.groups

    if groups.exists():
        # if a user is assigned to any group,
        # we need to check for at least one group match and include such policies
        filters = {
            "groups__in": groups.all()
        }
    else:
        # if a user is not assigned to any group,
        # we need to only include policies that are not assigned as
        filters = {
            "groups__isnull": True
        }

    return [{
        "id": p.pk,
        "version_id": p.latest_version.pk,
        "name": p.name,
        "version": p.latest_version.version,
        "read": False,
        "agreed": False,
        "link": p.latest_version.absolute_document_url,
        "date": str(timezone.now())
    } for p in OrganisationPolicy.objects.annotate(versions_count=Count('versions')).filter(
        organisation=app_install.app_user.organisation,
        active=True,
        versions_count__gt=0,
        versions__active=True,
        **filters
    )]


def api_debug_get_installed_applications(last_report):
    """
    Returns installed applications for passed device.
    :param last_report: last application report
    :type last_report: appusers.AppReport
    :return: device installed applications
    :rtype: list
    """
    return list(last_report.installed_software.values(
        appname=F('software__product'),
        appversion=F('software__version'),
        appinstalleddate=Cast('date_installed', TextField()),
        appvendor=Coalesce(F('software__vendor'), Value('vendor')),
        mobileapp=F('software__mobile_app')
    )) if last_report else []


def api_debug_get_inapp_responses(last_report):
    """
    Returns internal app responses.
    :param last_report: last application report
    :type last_report: appusers.AppReport
    :return: internal app responses
    :rtype: list
    """
    return [{
        "question_id": r.app_check.pk,
        "product_name": r.product_name,
        "product_state": r.product_state,
        "product_status": r.product_status,
        "product_state_timestamp": r.product_state_timestamp,
        "product_path": r.product_path
    } for r in last_report.in_app_check_results.exclude(
        product_name=""
    ).exclude(
        product_state=""
    ).exclude(
        product_status=""
    ).exclude(
        product_path=""
    ).exclude(
        product_state_timestamp=""
    )] if last_report else []


def generate_app_last_api_report(app_install):
    """
    Generates and returns app's last API report data.
    :param app_install: installed application object
    :type app_install: appusers.AppInstall
    :return: last API report data
    :rtype: dict
    """
    try:
        os_user = app_install.latest_local_user
        last_report = app_install.get_latest_report

        report = {
            "uuid": str(app_install.app_user.uuid),
            "app_version": app_install.app_version,
            "user": os_user.username if os_user else "",
            "domain": os_user.domain if os_user else "",
            "is_admin_account": os_user.is_admin_account if os_user else "",
            "network_interfaces": api_debug_get_network_interfaces(app_install),
            "policies": api_debug_get_policies(app_install),
            "installed_applications": api_debug_get_installed_applications(last_report),
            "device_id": app_install.device_id,
            "serial_number": app_install.serial_number,
            "response": api_debug_get_checks(app_install, last_report),
            "inapp_response": api_debug_get_inapp_responses(last_report),
            "systeminfo": base64.b64encode(
                app_install.system_info.encode("utf-8")
            ).decode("utf-8") if app_install.system_info else None,
        }
    except Exception:
        return {}
    else:
        return report


VENDOR_PATTERN = re.compile(r'[a-z]+')

def clean_vendor(vendor):
    """
    Cleans vendor name from unnecessary information.
    :return: vendor name
    :rtype: str or unicode
    """
    vendor = vendor.lower()
    vendor = vendor.split()[0]

    cleaned_vendor = VENDOR_PATTERN.search(vendor)
    if cleaned_vendor:
        return cleaned_vendor.group()

    return vendor

VERSION_PATTERN = re.compile(r'.+(?=\s\d+\.\d+(\.\d+)?)')
DASH_PATTERN = re.compile(r'.+(?=\s\-)')
PARENTHESES_PATTERN = re.compile(r'.+(?=\s\(.*\))')

# TODO: cover espcial cases for Microsoft
# (sp1, 15.0:2345.5, home, professional)
def clean_product(vendor, product):
    """
    Cleans product name from unnecessary information.
    This function assumes that vendor is already cleaned.

    :return: product name
    :rtype: str or unicode
    """
    name = product.lower()

    # remove version from product name
    name_without_version = VERSION_PATTERN.search(name)
    if name_without_version:
        name = name_without_version.group()

    # remove dashes if the product name has
    name_without_dashes = DASH_PATTERN.search(name)
    if name_without_dashes:
        name = name_without_dashes.group()

    # remove date and version with parentheses
    name_without_parentheses = PARENTHESES_PATTERN.search(name)
    if name_without_parentheses:
        name = name_without_parentheses.group()

    # remove vendor name
    if vendor and vendor in name:
        # if product name is empty after striping vendor
        # just add back the vendor
        name = name.replace(vendor, '').strip() or vendor

    return name

CLEAN_VERSION_PATTERN = re.compile(r'\d{1,6}\.\d{1,6}(?:\.\d{1,6})?(?:\.\d{1,6})?')
def clean_version(version):
    """
    Cleans product version from unnecessary information.
    :return: product version
    :rtype: str or unicode
    """
    cleaned_version = CLEAN_VERSION_PATTERN.search(version)
    if cleaned_version:
        return cleaned_version.group()
    return version


def parse_version(version: str) -> Optional[Tuple[int, int, int, str]]:
    """
    Parses the version string and returns a tuple of (major, minor, patch, channel).
    If the version does not match the expected pattern, return None.

    Supported formats:
    - Semantic versioning: "1.2.3"
    - Versions with channels: "1.2.3-beta.1", "1.2-beta"
    - Short versions: "13.2" (assumes patch=0)
    """
    try:
        version_info = Version.coerce(version)

        # Extract the main version components
        major = version_info.major
        minor = version_info.minor
        patch = version_info.patch

        # Determine the channel from the prerelease part
        if version_info.prerelease:
            channel = version_info.prerelease[0]
        else:
            channel = "stable"

        return major, minor, patch, channel
    except (ValueError, TypeError):
        return None


def get_version_type_by_device_type(device_type: int) -> int:
    """
    Returns the AppVersion device type based on the AppInstall device type.
    :type device_type: int (AppInstall.DEVICE_TYPES)
    """
    from appusers.models import AppInstall

    return {
        AppInstall.MOBILE: AppVersion.DEVICE_TYPE_MOBILE,
    }.get(device_type, AppVersion.DEVICE_TYPE_DESKTOP)


def get_app_version(version: str, app_install_type: int) -> AppVersion:
    """
    Returns the AppVersion object based on the version string.
    :param version: app version in string representation
    :type version: str
    :param app_install_type: app install device type
    :type app_install_type: int (AppInstall.DEVICES_TYPES)
    """
    version_type = get_version_type_by_device_type(app_install_type)
    if parsed_version := parse_version(version):
        major, minor, patch, channel = parsed_version
        return AppVersion.objects.get_or_create(
            major=major,
            minor=minor,
            patch=patch,
            device_type=version_type,
        )[0]
    return AppVersion.get_default_version(device_type=version_type)
