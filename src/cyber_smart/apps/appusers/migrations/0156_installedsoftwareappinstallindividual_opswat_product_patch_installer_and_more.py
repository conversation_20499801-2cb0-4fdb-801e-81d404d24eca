# Generated by Django 5.1.11 on 2025-07-21 20:32

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("appusers", "0155_delete_installedsoftwareappinstallsummary_and_more"),
        (
            "opswat_patch",
            "0018_opswatpatchattempt_opswatpatcheventlog_patch_attempt_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="installedsoftwareappinstallindividual",
            name="opswat_product_patch_installer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="opswat_patch.opswatproductpatchinstaller",
            ),
        ),
        migrations.AddField(
            model_name="installedsoftwareappinstallindividual",
            name="patch_detected_date",
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
