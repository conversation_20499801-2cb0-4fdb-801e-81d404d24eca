import logging

from celery import shared_task as task
from chargebee import InvalidRequestError
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db.models import Q
from django.urls import reverse
from django.utils import timezone

from appusers.models import (
    AppFile, AppInstall, AppInstallPolicyAgreement,
    AppUser, SoftwarePackage, SoftwarePackageCPE,
    SoftwarePackageCVEs
)
from appusers.templatetags.appusers import app_install_url
from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from beta_features.utils import CAP_V_FIVE_BETA_PREFIX
from billing.models import DirectSubscription
from billing.providers.chargebee import SUBSCRIPTION_ACTIVE
from billing.providers.chargebee.exceptions import CardError, ChargebeeError
from common.tasks import GlobalRateLimitTask
from common.utils import get_full_name_or_email_from_values
from notifications.handlers import NotificationHandler
from organisations.models import Organisation
from vulnerabilities.hooks import (
    CVEHookInternalServerError, CVEHookServiceUnavailable,
    call_sync_hook, is_hook_reliable
)
from vulnerabilities.matcher import get_cpe_for_product, get_cve_for_product
from vulnerabilities.models import CVERepository
from vulnerabilities.utils import get_actual_cves_data_from_microsoft_security_release_notes
from dashboard.tasks import calculate_app_install_analytics

User = get_user_model()

logger = logging.getLogger(__name__)


@task(rate_limit="100/m", queue="chargebee")
def chargebee_daily_update():
    """
    Update chargebee with current usage data
    :return: nothing
    :rtype: None
    """
    subscriptions_ids = DirectSubscription.objects.filter(
        Q(plan_id='app_monthly') | Q(plan_id='app_annual'),
        status=SUBSCRIPTION_ACTIVE).values_list('id', flat=True)

    for subscription_id in subscriptions_ids:
        update_subscription_addon_quantity.delay(subscription_id)


@task(rate_limit="100/m", queue="chargebee")
def update_subscription_addon_quantity(subscription_id):
    subscription = DirectSubscription.objects.get(id=subscription_id)
    try:
        subscription.update_addon_quantity(quantity=subscription.addon_quantity)
    except (CardError, ChargebeeError):
        pass
    except InvalidRequestError as e:
        if settings.IS_PROD:
            raise e
        else:
            logger.debug(f"Chargebee call failed: {e}")


def filter_and_send_notification(message_type, last_check_in_days=None, filter_by_app_version=True):
    """ Filter AppInstall and send notification if required.
     Args:
         message_type          - notification message type, see NotificationHandler.
         last_check_in_days    - if a number is provided it will filter by devices that have not checked in
                                    for at least the provided number days.
         filter_by_app_version - if True it will filter by devices that have an older version of active protect.
    """
    filters = Q()
    if last_check_in_days:
        current_date = timezone.now().date()
        date_x_days_ago = current_date - relativedelta(days=last_check_in_days)
        filters &= Q(reports__created__lt=date_x_days_ago, reports__isnull=False)

    if filter_by_app_version:
        latest_desktop_version = '.'.join(AppFile.get_desktop_version().split('.')[:3])
        filters &= (Q(device_type=AppInstall.DESKTOP) & Q(app_version__lt=latest_desktop_version))

    outdated_devices = AppInstall.objects.active().filter(
        filters
    ).exclude(
        app_user__organisation__partner__name__in=settings.CANCELLED_PARTNER_NAMES
    ).distinct().values(
        'app_user__first_name', 'app_user__last_name', 'app_user__email', 'app_user__organisation_id', 'device_id',
        'app_user__organisation__secure_id', 'app_user__uuid', 'caption', 'app_user__organisation__name', 'os__title',
        'serial_number'
    )
    for device_dict in outdated_devices:
        user_name = get_full_name_or_email_from_values(
            device_dict.get('app_user__email'),
            device_dict.get('app_user__first_name'),
            device_dict.get('app_user__last_name')
        )
        caption = device_dict.get('caption')
        app_install_caption = device_dict.get('os__title') if (caption == 'Unknown' or not caption) else caption
        url = app_install_url(
            device_dict.get('app_user__organisation__secure_id'),
            device_dict.get('app_user__uuid'),
            device_dict.get('device_id'),
            device_dict.get('serial_number', '')
        )
        kwargs = {
            'user_name': user_name,
            'organisation_id': device_dict.get('app_user__organisation_id'),
            'organisation_name': device_dict.get('app_user__organisation__name'),
            'app_install_caption': app_install_caption or 'Unknown',
            'url': url
        }
        NotificationHandler.send_notification(message_type=message_type, **kwargs)


@task
def send_notification_about_device_has_outdated_version():
    """ Sends notifications about app installs that have an outdated version """
    filter_and_send_notification(message_type='device_has_outdated_version')


@task
def send_notification_about_device_has_outdated_version_and_not_checkedin():
    """ Sends notifications about app installs that have an outdated version
    and have not checked-in for over 7 days """
    filter_and_send_notification(
        message_type='device_not_checked_in_and_outdated_version',
        last_check_in_days=7,
    )


@task
def send_notification_about_device_has_not_checkedin():
    """ Sends notifications about app installs that have not checked-in for over 14 days """
    filter_and_send_notification(
        message_type='device_not_checked_in_for_over_14_days',
        last_check_in_days=14,
        filter_by_app_version=False,
    )


@task
def send_notification_about_user_has_unagreed_policies():
    """ Sends notifications about app users that have unagreed policies """
    policies = AppInstallPolicyAgreement.objects.filter(
        agreed_date__isnull=True,
        version__policy__active=True,
        version__active=True,
        version__main=True,
        app_install__app_user__active=True,
        app_install__inactive=False,
    ).exclude(
        app_install__app_user__organisation__partner__name__in=settings.CANCELLED_PARTNER_NAMES
    ).distinct('version__policy', 'app_install__app_user')\
        .values('app_install__app_user__first_name', 'app_install__app_user__last_name',
                'app_install__app_user__email', 'app_install__app_user__organisation__name',
                'version__policy__name', 'app_install__app_user__organisation__id', 'app_install__device_id',
                'app_install__app_user__organisation__secure_id', 'app_install__app_user__uuid',
                'app_install__serial_number')
    for policy in policies:
        user_name = get_full_name_or_email_from_values(
            policy.get('app_install__app_user__email'),
            policy.get('app_install__app_user__first_name'),
            policy.get('app_install__app_user__last_name')
        )
        url = app_install_url(
            policy.get('app_install__app_user__organisation__secure_id'),
            policy.get('app_install__app_user__uuid'),
            policy.get('app_install__device_id'),
            policy.get('app_install__serial_number', '')
        )
        kwargs = {
            'user_name': user_name,
            'organisation_id': policy.get('app_install__app_user__organisation__id'),
            'organisation_name': policy.get('app_install__app_user__organisation__name'),
            'policy_name': policy.get('version__policy__name'),
            'url': url
        }
        NotificationHandler.send_notification(message_type='user_has_unagreed_policies', **kwargs)


@task
def send_notification_about_user_has_enrolled_but_cap_not_installed():
    """ Sends notifications about app users that have been enrolled
    to install Active Protect but have not yet installed it """
    app_users = AppUser.objects.filter(
        active=True,
        organisation__software_support=True,
        organisation__is_test=False,
        organisation__is_trial=False,
        installs__isnull=True
    ).exclude(
        organisation__partner__name__in=settings.CANCELLED_PARTNER_NAMES
    ).values('first_name', 'last_name', 'email', 'organisation__name', 'organisation__id', 'organisation__secure_id')
    for app_user in app_users:
        user_name = get_full_name_or_email_from_values(
            app_user.get('email'),
            app_user.get('first_name'),
            app_user.get('last_name')
        )
        kwargs = {
            'user_name': user_name,
            'organisation_id': app_user.get('organisation__id'),
            'organisation_name': app_user.get('organisation__name'),
            'url': reverse('dashboard:organisation', kwargs={
                'org_id': app_user.get('organisation__secure_id'),
            })
        }
        NotificationHandler.send_notification(message_type='user_has_not_installed_app', **kwargs)


@task(queue="cve")
def check_new_software_for_vulnerabilities():
    """ Check new software for vulnerabilities """

    # call sync endpoint to update cve feed
    call_sync_hook()

    software = SoftwarePackage.objects.filter(
        cpe__isnull=True,
        platform__isnull=False,
        created__gt=timezone.now() - timezone.timedelta(minutes=16)
    )

    for product in software:
        if cpe := get_cpe_for_product(
            product.vendor, product.product,
            product.version, product.get_target_sw()
        ):
            SoftwarePackageCPE.objects.update_or_create(
                software_id=product.id,
                defaults={"cpe": cpe}
            )

            check_for_vulnerabilities(software_id=product.id, cpe=cpe)


# for admin panel
@task(queue="cve")
def generate_cpe_and_check_for_cves(software_ids):
    """
    Generate CPE for software and check for CVE.
    """
    software = SoftwarePackage.objects.filter(id__in=software_ids)
    for product in software:
        if cpe := get_cpe_for_product(
            product.vendor, product.product,
            product.version, product.get_target_sw()
        ):
            SoftwarePackageCPE.objects.update_or_create(
                software_id=product.id,
                defaults={"cpe": cpe}
            )

            check_for_vulnerabilities(software_id=product.id, cpe=cpe)


# runs once a day before midnight
@task(queue="cve")
def check_software_for_recent_vulnerabilities():
    """
    Check software for recent vulnerabilities.
    """

    # check if the cve-matcher reliable
    if not is_hook_reliable():
        return

    # call sync endpoint to update cve feed
    call_sync_hook()

    cpes = SoftwarePackageCPE.objects.filter(
        software__platform__isnull=False)

    for cpe in cpes:
        check_for_recent_vulnerabilities.delay(
            software_id=cpe.software_id,
            cpe=cpe.cpe
        )

def _check_for_vulnerabilities(software_id, cpe, recent):
    try:
        cves = get_cve_for_product(cpe=cpe, recent=recent)
    except (CVEHookInternalServerError, CVEHookServiceUnavailable):
        SoftwarePackageCPE.objects.filter(
            software_id=software_id
        ).update(
            cve_matcher_failed=True
        )
        return

    software_package = SoftwarePackage.objects.get(id=software_id)
    if software_package.vendor == 'microsoft' and software_package.platform == 'win32':
        name = software_package.product
        version = software_package.version
        cves = get_actual_cves_data_from_microsoft_security_release_notes(name, version, cves)

    if not cves:
        if not recent:
            # No CVEs are found for this record,
            # So delete the related SoftwarePackageCVE if it exists.
            SoftwarePackageCVEs.objects.filter(software_id=software_id).delete()
            return
        else:
            # If only recent set is fetched, do nothing as there is
            # nothing to update.
            return

    obj, created = SoftwarePackageCVEs.objects.get_or_create(
        software_id=software_id,
        defaults={'cves': list(cves)}
    )
    if created:
        cve_repositories_to_add = CVERepository.objects.filter(
            cve_id__in=cves
        )
        obj.cves_fk.add(*cve_repositories_to_add)
        return

    if recent:
        # For recent fetching, not all CVEs might be present in the response.
        # So take an union of the existing and new cves, and don't delete old ones.
        all_cves = list(set(cves).union(obj.cves))
        cve_repositories_to_add = CVERepository.objects.filter(
            cve_id__in=all_cves
        )
    else:
        all_cves = list(set(cves))
        cve_repositories_to_remove = obj.cves_fk.exclude(
            cve_id__in=all_cves
        )
        obj.cves_fk.remove(*cve_repositories_to_remove)

    cve_repositories_to_add = CVERepository.objects.filter(
        cve_id__in=all_cves
    )
    obj.cves_fk.add(*cve_repositories_to_add)
    obj.cves = all_cves
    obj.save(update_fields=['cves'])


@task(base=GlobalRateLimitTask, max_retries=1, queue='cve', rate_limit='1500/m')
def check_for_recent_vulnerabilities(software_id, cpe):
    """
    Check for recent vulnerabilities for a specific software package.
    """
    _check_for_vulnerabilities(software_id, cpe, recent=True)


@task(base=GlobalRateLimitTask, max_retries=1, queue='cve', rate_limit='50/m')
def check_for_vulnerabilities(software_id, cpe):
    """
    Check for vulnerabilities for a specific software package.
    """
    _check_for_vulnerabilities(software_id, cpe, recent=False)


@task
def cleanup_acceptance_test_users():
    """
    Remove users that get created during acceptance test.
    """
    if settings.IS_PROD:
        logger.debug('Not running cleanup_acceptance_test_users in production')
        return

    if not (settings.IS_DEVELOP or settings.IS_STAGE):
        logger.debug('Not running cleanup acceptance tests if not in develop or stage')
        return

    current_year = timezone.now().year
    User.objects.filter(
        Q(email__startswith=f'test{current_year}')
        & (
            Q(email__endswith='@example.com') | Q(email__endswith='@quwij3il.mailosaur.net')
        )
    ).delete()


@task
def update_real_device_id(app_install_ids):
    """ Used in populate_real_device_id management command. """
    from django.db.models import Value
    from django.db.models.functions import Replace
    from beta_features.utils import CAP_V_FIVE_BETA_PREFIX

    AppInstall.objects.filter(id__in=app_install_ids).update(
        real_device_id=Replace(
            'device_id', Value(CAP_V_FIVE_BETA_PREFIX), Value('')
        )
    )


@task
def populate_app_version_task(app_install_ids: list[int]) -> None:
    """
    Used in populate_app_version management command.
    """
    from appusers.utils import get_app_version
    updated_version = []

    for app in AppInstall.objects.filter(id__in=app_install_ids).values("id", "app_version", "device_type"):
        updated_version.append(AppInstall(
            id=app["id"],
            version=get_app_version(version=app["app_version"], app_install_type=app["device_type"])
        ))
    AppInstall.objects.bulk_update(updated_version, ["version"])


@task
def migrate_v5_related_models_to_main_app_install():
    """
    Continuously look for v5-prefixed app installs and copy related models to the main app install.
    Only copies models that do not already exist in the main app install.
    """
    from appusers.models.app_installs import AppInstall, AppInstallConfirmedEmail
    from appusers.models.checks import CheckAutoFix, CheckManualFix

    # Find all v5 prefixed app installs
    qs = AppInstall.objects.filter(device_id__istartswith=CAP_V_FIVE_BETA_PREFIX)
    v5_prefixed_app_installs = qs.filter(
        Q(auto_fix__isnull=False) |
        Q(manual_fix__isnull=False) |
        Q(confirmed_emails__isnull=False)
    ).distinct('id')

    for v5_prefixed_app_install in v5_prefixed_app_installs:
        main_app_install = v5_prefixed_app_install.get_main_install()
        if not main_app_install:
            continue

        # Copy confirmed emails
        confirmed_emails = AppInstallConfirmedEmail.objects.filter(app_install_id=v5_prefixed_app_install.id)
        for email in confirmed_emails:
            existing_emails = AppInstallConfirmedEmail.objects.filter(
                app_install=main_app_install,
                email=email.email
            )

            if not existing_emails.exists():
                AppInstallConfirmedEmail.objects.create(
                    app_install=main_app_install,
                    email=email.email,
                    created=email.created,
                    modified=email.modified
                )

        # Copy manual fixes
        manual_fixes = CheckManualFix.objects.filter(app_install_id=v5_prefixed_app_install.id)
        for fix in manual_fixes:
            existing_fixes = CheckManualFix.objects.filter(
                app_install=main_app_install,
                app_check_id=fix.app_check_id,
                bulk_fix=fix.bulk_fix,
                reason=fix.reason,
                user_id=fix.user_id
            )

            if not existing_fixes.exists():
                CheckManualFix.objects.create(
                    app_install=main_app_install,
                    app_check_id=fix.app_check_id,
                    bulk_fix=fix.bulk_fix,
                    reason=fix.reason,
                    user_id=fix.user_id,
                    created=fix.created,
                    modified=fix.modified
                )

        # Copy auto fixes
        auto_fixes = CheckAutoFix.objects.filter(app_install_id=v5_prefixed_app_install.id)
        for fix in auto_fixes:
            existing_fixes = CheckAutoFix.objects.filter(
                app_install=main_app_install,
                app_check_id=fix.app_check_id,
                bulk_fix=fix.bulk_fix,
                user_id=fix.user_id
            )

            if not existing_fixes.exists():
                CheckAutoFix.objects.create(
                    app_install=main_app_install,
                    app_check_id=fix.app_check_id,
                    bulk_fix=fix.bulk_fix,
                    user_id=fix.user_id,
                    created=fix.created,
                    modified=fix.modified
                )

    logger.info(f"Processed {v5_prefixed_app_installs.count()} v5-prefixed app installs")


@task
def update_installed_software_individual(app_install_id):
    """
    Update installed software records for a specific app_install.
    """
    InstalledSoftwareAppInstallIndividual.update_individual_record(
        app_install_id=app_install_id,
    )
    calculate_app_install_analytics.delay(app_install_id)

@task
def update_installed_software_individual_for_organisation(organisation_id):
    """
    Update all installed software records for a specific organisation.
    """
    InstalledSoftwareAppInstallIndividual.update_individual_record(
        organisation_id=organisation_id
    )

@task
def update_installed_software_individual_for_all_organisations(is_async=True):
    """
    Update all installed software records for all organisations.
    Batches per organisation.
    """
    qs = Organisation.objects.exclude(partner__name__in=[settings.CS_PARTNER_CANCELLED_DIRECT_CUSTOMER_NAME, settings.CS_PARTNER_CANCELLED_PARTNER_CUSTOMER_NAME])
    for org in qs:
        if is_async:
            update_installed_software_individual_for_organisation.delay(org.id)
        else:
            update_installed_software_individual_for_organisation(org.id)


@task
def update_installed_software_individual_for_all_records(is_async=True):
    """
    Update all installed software records for all records.
    """
    qs = AppInstall.objects.exclude(app_user__organisation__partner__name__in=[settings.CS_PARTNER_CANCELLED_DIRECT_CUSTOMER_NAME, settings.CS_PARTNER_CANCELLED_PARTNER_CUSTOMER_NAME])
    for app_install in qs:
        if is_async:
            update_installed_software_individual.delay(app_install.id)
        else:
            update_installed_software_individual(app_install.id)
