from datetime import datetime, timedelta
from appusers.models.factories import (
    AppReportFactory, SoftwarePackageFactory, AppOSInstalledSoftware,
    AppUserFactory, AppInstallFactory, AppInstallOSUserFactory
)
from appusers.models import AppInstall
from appusers.models.app_installs import SoftwarePackageCVEs
from opswat.models import InstalledProductVersion
from opswat_patch.factories import OpswatPatchInstallerFactory, OpswatProductPatchInstallerFactory
from appusers.models.materialized_views import InstalledSoftwarePartnerSummary
from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from appusers.tasks import update_installed_software_individual_for_all_organisations
from opswat.factories import (
    ProductVendorFactory, ProductFactory, ProductVersionFactory, CVEFactory,
    InstalledProductFactory, ProductCategoryFactory, ProductSignatureFactory
)
from opswat.models import CVE
from organisations.factories import OrganisationFactory
from organisations.models import Organisation
from partners.factories import PartnerFactory
from partners.models import Partner
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from vulnerabilities.factories import CVERepositoryFactory
from vulnerabilities.utils import REGULAR_SOURCE, OPSWAT_SOURCE
from .test_constants import (
    AI1_ACTIVE_PROTECT, AI1_EDGE, AI1_SAFARI, AI1_PACKAGE,
    AI2_EDGE, AI2_SAFARI, AI2_PACKAGE,
    AI3_ACTIVE_PROTECT, AI3_PACKAGE,
    AI4_PYCHARM_OPSWAT, AI4_PYCHARM_REGULAR_1, AI4_PYCHARM_REGULAR_2,
    AI1_CYBERSMART, AI1_MICROSOFT, AI1_APPLE, AI1_COMMON,
    AI2_MICROSOFT, AI2_APPLE, AI2_COMMON,
    AI3_CYBERSMART, AI3_COMMON,
    AI4_JETBRAINS_OPSWAT, AI4_JETBRAINS_REGULAR_1, AI4_JETBRAINS_REGULAR_2,
    VERSION_5_0_0, VERSION_1_0_0, VERSION_2_0_0, VERSION_2022_1_1,
    VERSION_120_0_6099_129, VERSION_121_0, VERSION_1_85_1, VERSION_9_0_0, VERSION_4_0_0, VERSION_3_0_0,
    VERSION_8_0_0, VERSION_7_0_0,
    # Product name constants
    CHROME, FIREFOX, VSCODE, ACTIVE_PROTECT, EDGE, SAFARI, PYCHARM, ZOOM, SLACK, SPOTIFY, VLC, STEAM,
    # Vendor name constants
    GOOGLE, MOZILLA, MICROSOFT, CYBERSMART, APPLE, ZOOM_VIDEO, SLACK_TECH, SPOTIFY_AB, VIDEOLAN, VALVE,
)


class SoftwarePackageHelperTestMixin:

    @staticmethod
    def _add_software_package_from_regular_scanner(app_install, product, vendor, version, cves=None, app_report=None):
        # Create or use existing app report
        if app_report is None:
            app_report = AppReportFactory(app_install=app_install)

        # Create the software package
        software_package = SoftwarePackageFactory(product=product, vendor=vendor, version=version)

        # Link to app install via AppOSInstalledSoftware
        AppOSInstalledSoftware.objects.create(
            report=app_report,
            software=software_package
        )

        # Create associated CVEs if any
        if not cves:
            return software_package

        sp_cves, _ = SoftwarePackageCVEs.objects.get_or_create(software=software_package)

        # Create CVERepository records and link them to sp_cves
        for cve_data in cves:
            cve_repo = CVERepositoryFactory(
                cve_id=cve_data['id'],
                base_severity=cve_data['severity'],
                base_score=cve_data.get("base_score", 7.5),
                published_at=datetime.now() - timedelta(days=30)
            )
            # Create the relationship using the correct field cves_fk
            sp_cves.cves_fk.add(cve_repo)

        return software_package

    def _add_software_package_from_opswat_scanner(
            self, app_install, product, vendor, version, cves=None, patch_available=False
    ):
        # Create the product category, vendor and product
        product_category = ProductCategoryFactory()

        product_vendor, _ = ProductVendorFactory.get_or_create(
            name=vendor,
            opswat_id=f"opswat-{vendor.lower().replace(' ', '-')}",
        )

        product_obj, _ = ProductFactory.get_or_create(
            name=product,
            vendor=product_vendor,
            opswat_id=f"opswat-{product.lower().replace(' ', '-')}",
        )

        product_obj.categories.add(product_category)

        product_version, _ = ProductVersionFactory.get_or_create(
            product=product_obj,
            raw_version=version,
            defaults=dict(
                major=version.split(".")[0],
                minor=version.split(".")[1],
                patch=version.split(".")[2],
                version_channel="stable"
            )
        )
        # Create associated CVEs if any
        if cves:
            for cve_data in cves:
                cve = CVEFactory(
                    opswat_id=f"opswat-{cve_data['id'].lower()}",
                    cve_id=cve_data['id'],
                    severity=cve_data['severity'],
                    severity_index=cve_data.get("severity_index", 90),
                    published_at=datetime.now() - timedelta(days=30),
                    details={"affected_versions": f"<={version}"}
                )
                cve.product_version.add(product_version)

        installed_products = InstalledProductFactory(
            app_install=app_install
        )
        installed_products.product_versions.add(product_version)

        if patch_available:
            self.__add_patch_installer_to_product(product_version, installed_products)

        return product_version

    def __add_patch_installer_to_product(self, product_version, installed_products):
        """
        Add a patch installer to the given product version.
        """
        self.signature = ProductSignatureFactory(
            product=product_version.product,
            name="Product Signature",
        )
        self.patch_installer = OpswatPatchInstallerFactory(
            latest_version="101.0.0"
        )
        self.product_patch_installer = OpswatProductPatchInstallerFactory(
            product=product_version.product,
            patch_installer=self.patch_installer,
            title="Product Patch Installer",
        )
        self.product_patch_installer.signatures.add(self.signature)

        installed_product_version = InstalledProductVersion.objects.get(
            installed_product=installed_products,
            product_version=product_version,
        )

        installed_product_version.signature = self.signature
        installed_product_version.save()

    @staticmethod
    def _refresh_summaries():
        InstalledSoftwareAppInstallIndividual.refresh()
        InstalledSoftwareOrganisationIndividual.refresh()
        InstalledSoftwarePartnerSummary.refresh()

    def _create_packages_with_cves(self, app_install, packages_data):
        """Create packages with their associated CVEs based on source."""
        app_report = None  # For regular scanner packages
        opswat_versions = []  # Initialize for OPSWAT packages

        for package_info in packages_data:
            product = package_info["product"]
            vendor = package_info["vendor"]
            version = package_info["version"]
            source = package_info["source"]
            cves = package_info.get("cves", [])

            if source == OPSWAT_SOURCE:
                # Use OPSWAT helper and collect product versions
                product_version = self._add_software_package_from_opswat_scanner(
                    app_install, product, vendor, version, cves
                )
                opswat_versions.append(product_version)

            elif source == REGULAR_SOURCE:
                # Create regular scanner package
                if app_report is None:
                    app_report = AppReportFactory(app_install=app_install)

                self._add_software_package_from_regular_scanner(
                    app_install, product, vendor, version, cves, app_report
                )

    @staticmethod
    def _get_summary(instance):
        if isinstance(instance, Partner):
            return InstalledSoftwarePartnerSummary.objects.filter(partner=instance)
        elif isinstance(instance, Organisation):
            return InstalledSoftwareOrganisationIndividual.objects.filter(organisation=instance)
        elif isinstance(instance, AppInstall):
            return InstalledSoftwareAppInstallIndividual.objects.filter(app_install=instance)
        else:
            raise ValueError("Invalid instance type")

    def _create_test_packages(self, app_install: AppInstall) -> None:
        self.packages = [
            # Critical vulnerabilities (8.1-10.0)
            {
                "product": ACTIVE_PROTECT,
                "vendor": CYBERSMART,
                "version": VERSION_5_0_0,
                "source": OPSWAT_SOURCE,
                "cves": [{"id": "CVE-2023-001", "severity": CVE.SEVERITY_CRITICAL, "severity_index": 95}]
            },
            {
                "product": EDGE,
                "vendor": MICROSOFT,
                "version": VERSION_1_0_0,
                "source": OPSWAT_SOURCE,
                "cves": [{"id": "CVE-2023-002", "severity": CVE.SEVERITY_CRITICAL, "severity_index": 98}]
            },
            # High vulnerabilities (6.1-8.0)
            {
                "product": SAFARI,
                "vendor": APPLE,
                "version": VERSION_2_0_0,
                "source": OPSWAT_SOURCE,
                "cves": [{"id": "CVE-2023-003", "severity": CVE.SEVERITY_IMPORTANT, "severity_index": 75}]
            },
            {
                "product": CHROME,
                "vendor": GOOGLE,
                "version": VERSION_120_0_6099_129,
                "source": OPSWAT_SOURCE,
                "cves": [{"id": "CVE-2023-004", "severity": CVE.SEVERITY_IMPORTANT, "severity_index": 78}]
            },
            # Medium vulnerabilities (4.1-6.0)
            {
                "product": FIREFOX,
                "vendor": MOZILLA,
                "version": VERSION_121_0,
                "source": OPSWAT_SOURCE,
                "cves": [{"id": "CVE-2023-005", "severity": CVE.SEVERITY_MODERATE, "severity_index": 55}]
            },
            {
                "product": PYCHARM,
                "vendor": MICROSOFT,
                "version": VERSION_1_85_1,
                "source": OPSWAT_SOURCE,
                "cves": [{"id": "CVE-2023-006", "severity": CVE.SEVERITY_MODERATE, "severity_index": 42}]
            },
            # Low vulnerabilities (0.1-4.0)
            {
                "product": ZOOM,
                "vendor": ZOOM_VIDEO,
                "version": VERSION_9_0_0,
                "source": OPSWAT_SOURCE,
                "cves": [{"id": "CVE-2023-007", "severity": CVE.SEVERITY_LOW, "severity_index": 35}]
            },
            {
                "product": SLACK,
                "vendor": SLACK_TECH,
                "version": VERSION_4_0_0,
                "source": OPSWAT_SOURCE,
                "cves": [{"id": "CVE-2023-008", "severity": CVE.SEVERITY_LOW, "severity_index": 21}]
            },
            {
                "product": SPOTIFY,
                "vendor": SPOTIFY_AB,
                "version": VERSION_3_0_0,
                "source": OPSWAT_SOURCE,
                "cves": [{"id": "CVE-2023-009", "severity": CVE.SEVERITY_UNKNOWN, "severity_index": 15}]
            },
            {
                "product": VLC,
                "vendor": VIDEOLAN,
                "version": VERSION_8_0_0,
                "source": OPSWAT_SOURCE,
                "cves": [{"id": "CVE-2023-010", "severity": CVE.SEVERITY_UNKNOWN, "severity_index": 5}]
            },
            # Info vulnerabilities (0.0 OPSWAT source)
            {
                "product": STEAM,
                "vendor": VALVE,
                "version": VERSION_7_0_0,
                "source": OPSWAT_SOURCE,
                "cves": []
            },
            # Unknown vulnerabilities (0.0 REGULAR source)
            {
                "product": VSCODE,
                "vendor": MICROSOFT,
                "version": VERSION_1_0_0,
                "source": REGULAR_SOURCE,
                "cves": []
            }
        ]
        self._create_packages_with_cves(app_install, self.packages)
        InstalledSoftwareOrganisationIndividual.refresh()

    def setUpTwoOrganisationsFourAppInstalls(self):
        """
        Data:
        - One Partner
        - Two Organisations
        - Four App Installs
        - Thirteen Software Packages

        Organisation 1:
            App Install 1:
            - `CyberSmart` `Active Protect` `5.0.0` (Non-vulnerable) [opswat]
            - `microsoft` `edge` `1.0.0` (Vulnerable) [regular]
            - `apple` `safari` `2.0.0` (Non-vulnerable) [regular]
            - `common` `package` `1.0.0` (Non-vulnerable) [regular]

            App Install 2:
            - `Microsoft Corporation` `Edge` `1.0.0` (Vulnerable) [opswat]
            - `Apple` `Safari` `2.0.0` (Non-vulnerable) [opswat]
            - `Common` `Package` `1.0.0` (Non-vulnerable) [opswat]

        Organisation 2:
            App Install 3:
            - `CyberSmart` `Active Protect` `5.0.0` (Non-vulnerable) [opswat]
            - `CyberSmart` `Active Protect` `5.0.0` (Non-vulnerable) [opswat] [another OS User]
            - `COMMON` `PACKAGE` `1.0.0` (Non-vulnerable) [regular]

            App Install 4:
            - `JetBrains` `PyCharm` `2022.1.1` (Non-vulnerable) [opswat]
            - `jetbrains company` `pycharm` `2022.1.1` (vulnerable) [regular]
            - `Jetbrain` `PycharM` `2022.1.1` (vulnerable) [regular]

        Expected Results with the current merging and deduplication logic:

        AppInstall 1:
        - 4 total
        - 4 unique
        - 0 vulnerable
        - 4 safe
        // Microsoft Edge won't be counted as vulnerable because
        // if App Install has data from Opswat, we ignore vulnerability status from regular scanner

        AppInstall 2:
        - 3 total
        - 3 unique
        - 1 vulnerable
        - 2 safe

        AppInstall 3:
        - 3 total
        - 2 unique
        - 0 vulnerable
        - 2 safe
        // We have two Active Protects with the same version, but installed by different OS users
        // They will be counted as one unique record and frequency will be 1.
        // All records are grouped on the app install level, not OS user level.

        AppInstall 4:
        - 3 total
        - 1 unique
        - 0 vulnerable
        - 1 safe
        // We have three PyCharm versions with different casing, but they will be counted as one unique record
        // because we ignore vendor name and casing when calculating uniqueness.
        // So eventually there will be just one unique record from opswat.
        // As all those packages installed by one OS user, frequency will be 1, because we consider the rest as noise.

        Organisation 1:
        - 7 total
        - 4 unique
        - 1 vulnerable
        - 3 safe
        // CyberSmart Active Protect is the only one unique record with frequency 1.
        // And `edge`, `safari` and `package` installed in both App Installs, so they will be counted as one unique record
        // with frequency 2.

        Organisation 2:
        - 6 total
        - 3 unique
        - 0 vulnerable
        - 3 safe
        // There are two CyberSmart Active Protects with the same version, so it will be counted as one unique record
        // with frequency 1 (as one app install has two OS users and we count on the app install level).
        // Also there are three PyCharm versions with different casing, but they will be counted as one unique record
        // with frequency 1 because the rest two are considered as noise.
        // And `package` is the only one unique record with frequency 1.

        Partner:
        - 13 total
        - 5 unique
            - CyberSmart Active Protect, frequency 2
            - Microsoft Edge
            - Apple Safari
            - Common Package
            - JetBrains PyCharm

        - 1 vulnerable
            - Microsoft Edge
        - 4 safe
            - CyberSmart Active Protect
            - Apple Safari
            - Common Package
            - JetBrains PyCharm
        """
        self.created_package_ids = {}  # Store created package IDs for verification
        self.partner = PartnerFactory(name="Test Partner")
        self.org1 = OrganisationFactory(name="Org 1", partner=self.partner)
        self.org1_app_user = AppUserFactory(organisation=self.org1)
        self.org2 = OrganisationFactory(name="Org 2", partner=self.partner)
        self.org2_app_user = AppUserFactory(organisation=self.org2)

        self.app_install_1 = AppInstallFactory(app_user=self.org1_app_user)
        AppInstallOSUserFactory(app_install=self.app_install_1)

        self.app_install_2 = AppInstallFactory(app_user=self.org1_app_user)
        AppInstallOSUserFactory(app_install=self.app_install_2)

        self.app_install_3 = AppInstallFactory(app_user=self.org2_app_user)
        self.os_user_1 = AppInstallOSUserFactory(app_install=self.app_install_3)
        self.os_user_2 = AppInstallOSUserFactory(app_install=self.app_install_3)

        self.app_install_4 = AppInstallFactory(app_user=self.org2_app_user)
        AppInstallOSUserFactory(app_install=self.app_install_4)

        # ORG 1
        self.app_install_1_packages = [
            {"product": AI1_ACTIVE_PROTECT, "vendor": AI1_CYBERSMART, "version": VERSION_5_0_0,
             "source": OPSWAT_SOURCE, "cves": []},
            {"product": AI1_EDGE, "vendor": AI1_MICROSOFT, "version": VERSION_1_0_0,
             "source": REGULAR_SOURCE, "cves": [{"id": "CVE-REG-EDGE-1", "severity": "high"}]},
            {"product": AI1_SAFARI, "vendor": AI1_APPLE, "version": VERSION_2_0_0,
             "source": REGULAR_SOURCE, "cves": []},
            {"product": AI1_PACKAGE, "vendor": AI1_COMMON, "version": VERSION_1_0_0,
             "source": REGULAR_SOURCE, "cves": []},
        ]
        self.app_install_2_packages = [
            {"product": AI2_EDGE, "vendor": AI2_MICROSOFT, "version": VERSION_1_0_0,
             "source": OPSWAT_SOURCE, "cves": [{"id": "CVE-OPS-EDGE-1", "severity": CVE.SEVERITY_CRITICAL, "severity_index": 100}]},
            {"product": AI2_SAFARI, "vendor": AI2_APPLE, "version": VERSION_2_0_0,
             "source": OPSWAT_SOURCE, "cves": []},
            {"product": AI2_PACKAGE, "vendor": AI2_COMMON, "version": VERSION_1_0_0,
             "source": OPSWAT_SOURCE, "cves": [{"id": "CVE-OPS-PKG-1", "severity": CVE.SEVERITY_LOW, "severity_index": 20}]},
        ]

        # ORG 2
        self.app_install_3_packages = [
            {"product": AI3_ACTIVE_PROTECT, "vendor": AI3_CYBERSMART, "version": VERSION_5_0_0,
             "source": OPSWAT_SOURCE, "cves": []},
            {"product": AI3_PACKAGE, "vendor": AI3_COMMON, "version": VERSION_1_0_0,
             "source": REGULAR_SOURCE, "cves": [{"id": "CVE-REG-PKG3-1", "severity": "high"}]},
        ]
        self.app_install_3_second_os_user_packages = [
            {"product": AI3_ACTIVE_PROTECT, "vendor": AI3_CYBERSMART, "version": VERSION_5_0_0,
             "source": OPSWAT_SOURCE, "cves": []},
        ]

        self.app_install_4_packages = [
            {"product": AI4_PYCHARM_OPSWAT, "vendor": AI4_JETBRAINS_OPSWAT, "version": VERSION_2022_1_1,
             "source": OPSWAT_SOURCE, "cves": []},
            {"product": AI4_PYCHARM_REGULAR_1, "vendor": AI4_JETBRAINS_REGULAR_1, "version": VERSION_2022_1_1,
             "source": REGULAR_SOURCE, "cves": [{"id": "CVE-REG-PYC1-1", "severity": "critical"}]},
            {"product": AI4_PYCHARM_REGULAR_2, "vendor": AI4_JETBRAINS_REGULAR_2, "version": VERSION_2022_1_1,
             "source": REGULAR_SOURCE, "cves": [{"id": "CVE-REG-PYC2-1", "severity": "medium"}]},
        ]

        # Organisation 1
        self._create_packages_with_cves(self.app_install_1, self.app_install_1_packages)
        self._create_packages_with_cves(self.app_install_2, self.app_install_2_packages)

        # Organisation 2
        self._create_packages_with_cves(self.app_install_3, self.app_install_3_packages)
        self._create_packages_with_cves(self.app_install_3, self.app_install_3_second_os_user_packages)
        self._create_packages_with_cves(self.app_install_4, self.app_install_4_packages)

        update_installed_software_individual_for_all_organisations(is_async=False)
        InstalledSoftwareOrganisationIndividual.refresh()
        InstalledSoftwarePartnerSummary.refresh()
