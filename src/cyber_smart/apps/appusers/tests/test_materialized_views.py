from datetime import datetime, timed<PERSON>ta

from django.test import TestCase

from appusers.models.factories import (
    AppUserFactory, AppInstallFactory, AppInstallOSUserFactory, AppOSInstalledSoftware
)
from appusers.models import AppInstall
from appusers.models.factories import AppReportFactory, SoftwarePackageFactory
from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from appusers.tests.helpers import SoftwarePackageHelperTestMixin
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from organisations.factories import OrganisationFactory
from opswat.factories import (
    ProductCategoryFactory, ProductVendorFactory, ProductFactory, ProductVersionFactory, CVEFactory,
    InstalledProductFactory
)
from opswat.models import CVE, ProductVersion
from vulnerabilities.utils import REGULAR_SOURCE, OPSWAT_SOURCE
from appusers.tasks import update_installed_software_individual_for_all_organisations, update_installed_software_individual_for_all_records
from .test_constants import (
    AI1_ACTIVE_PROTECT, AI1_ED<PERSON>, AI1_SA<PERSON><PERSON>, AI1_PACKAGE,
    AI2_EDGE, AI2_SAFARI, AI2_PACKAGE,
    AI3_ACTIVE_PROTECT, AI3_PACKAGE,
    AI4_PYCHARM_OPSWAT, VERSION_5_0_0, VERSION_1_0_0, VERSION_2_0_0, VERSION_2022_1_1
)



class InstalledSoftwareAppInstallIndividualTestCase(TestCase):
    """
    Tests for InstalledSoftwareAppInstallIndividual model's behavior.
    """

    def setUp(self):
        """
        Set up common test data.
        """
        self.app_user = AppUserFactory()
        self.app_install = AppInstallFactory(app_user=self.app_user)
        self.os_user = AppInstallOSUserFactory(app_install=self.app_install, username="yaro", domain="")

        self.category = ProductCategoryFactory(opswat_id="18", name="Unique Category")
        self.vendor = ProductVendorFactory(opswat_id="100634", name="Software Vendor")

        self.product = ProductFactory(opswat_id="1000974", name="Unique Product", vendor=self.vendor)
        self.product.categories.add(self.category)

    def create_product_versions(self):
        """
        Create vulnerable and non-vulnerable product versions.
        """
        self.non_vulnerable_version = ProductVersionFactory(
            product=self.product, raw_version="4.0.0", major=4, minor=0, patch=0, version_channel="stable"
        )
        self.vulnerable_version = ProductVersionFactory(
            product=self.product, raw_version="1.0.4", major=1, minor=0, patch=4, version_channel="stable"
        )
        # Add critical vulnerable version for severity testing
        self.critical_vulnerable_version = ProductVersionFactory(
            product=self.product, raw_version="2.0.0", major=2, minor=0, patch=0, version_channel="beta"
        )

    def create_cve(self):
        """
        Create a CVE and associate it with the vulnerable version.
        """
        cve = CVEFactory(
            opswat_id="100001",
            cve_id="CVE-2023-1234",
            severity=CVE.SEVERITY_CRITICAL,
            severity_index=95,
            description="Remote code execution vulnerability in TeamViewer before 15.18.5",
            published_at=datetime.now() - timedelta(days=30),
            details={
                "affected_versions": "<=15.18.4",
                "attack_vector": "NETWORK",
                "impact": "Remote code execution"
            }
        )
        cve.product_version.add(self.vulnerable_version)

        # Add additional CVEs for severity testing
        cve_moderate = CVEFactory(
            opswat_id="100002",
            cve_id="CVE-2023-5678",
            severity=CVE.SEVERITY_MODERATE,
            severity_index=50,
            description="Information disclosure vulnerability",
            published_at=datetime.now() - timedelta(days=20),
            details={"affected_versions": "<=1.0.4"}
        )
        cve_moderate.product_version.add(self.vulnerable_version)

        cve_critical = CVEFactory(
            opswat_id="100003",
            cve_id="CVE-2024-1111",
            severity=CVE.SEVERITY_CRITICAL,
            severity_index=95,
            description="Critical vulnerability",
            published_at=datetime.now() - timedelta(days=10),
            details={"affected_versions": "<=2.0.0"}
        )
        cve_critical.product_version.add(self.critical_vulnerable_version)

    def create_installed_software(self):
        """
        Create installed software and associate product versions.
        """
        installed_products = InstalledProductFactory(
            app_install=self.app_install
        )
        installed_products.product_versions.add(
            self.vulnerable_version, self.non_vulnerable_version, self.critical_vulnerable_version
        )

    def test_installed_software_summary_with_vulnerable_and_non_vulnerable_versions(self):
        """
        Verify that InstalledSoftwareAppInstallIndividual correctly classifies software as vulnerable or not.
        """
        self.create_product_versions()
        self.create_cve()
        self.create_installed_software()

        for is_org_aggregation in [True, False]:
            with self.subTest(is_org_aggregation=is_org_aggregation):
                if is_org_aggregation:
                    update_installed_software_individual_for_all_organisations(is_async=False)
                else:
                    update_installed_software_individual_for_all_records(is_async=False)

                self.assertEqual(InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install).count(), 3)

                for version, expected_vulnerability in [
                    (self.vulnerable_version, True),
                    (self.non_vulnerable_version, False),
                    (self.critical_vulnerable_version, True),
                ]:
                    with self.subTest(version=version.raw_version, is_vulnerable=expected_vulnerability):
                        exists = InstalledSoftwareAppInstallIndividual.objects.filter(
                            app_install=self.app_install,
                            version=version.raw_version,
                            is_vulnerable=expected_vulnerability
                        ).exists()
                        self.assertTrue(
                            exists,
                            f"Version {version.raw_version} expected to have is_vulnerable={expected_vulnerability}"
                        )

    def test_installed_software_summary_cve_count_and_severity(self):
        """
        Verify that InstalledSoftwareAppInstallIndividual correctly reports CVE count and highest severity.
        """
        self.create_product_versions()
        self.create_cve()
        self.create_installed_software()

        for is_org_aggregation in [True, False]:
            with self.subTest(is_org_aggregation=is_org_aggregation):
                if is_org_aggregation:
                    update_installed_software_individual_for_all_organisations(is_async=False)
                else:
                    update_installed_software_individual_for_all_records(is_async=False)

                summary_qs = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install)
                self.assertEqual(summary_qs.count(), 3)  # One entry per product version

                # Check vulnerable version with moderate CVE
                vulnerable_summary = summary_qs.get(version=self.vulnerable_version.raw_version)
                self.assertEqual(vulnerable_summary.cve_count, 2, "Expected 2 CVEs for vulnerable version")
                self.assertEqual(vulnerable_summary.highest_severity, 9.5,
                                "Expected moderate severity for vulnerable version")

                # Check non-vulnerable version with no CVEs
                non_vulnerable_summary = summary_qs.get(version=self.non_vulnerable_version.raw_version)
                self.assertEqual(non_vulnerable_summary.cve_count, 0, "Expected 0 CVEs for non-vulnerable version")
                self.assertEqual(non_vulnerable_summary.highest_severity, CVE.SEVERITY_UNKNOWN,
                                "Expected unknown severity for non-vulnerable version")

                # Check critical vulnerable version
                critical_summary = summary_qs.get(version=self.critical_vulnerable_version.raw_version)
                self.assertEqual(critical_summary.cve_count, 1, "Expected 1 CVE for critical vulnerable version")
                self.assertEqual(critical_summary.highest_severity, 9.5,
                                "Expected critical severity for critical vulnerable version")

    def test_update_installed_software_individual_with_existing_conflicting_record(self):
        """
        Test that reproduces the duplicate key constraint violation.
        This simulates the exact scenario from production where a record already exists
        with one ID, then the SQL query returns the same software with a different ID.
        """
        # First, manually create a record with a specific ID format
        # This simulates a record that was created in a previous run
        # The key is that the ID doesn't match what the SQL query will generate
        existing_id = f"{self.app_install.id}:regular:12345"
        InstalledSoftwareAppInstallIndividual.objects.create(
            id=existing_id,
            app_install=self.app_install,
            source=REGULAR_SOURCE,
            source_id="12345",
            vendor="Mozilla",
            product="Firefox",
            version="136.0.4",
            product_lc="firefox",
            version_lc="136.0.4",
            mobile_app=False,
            is_vulnerable=False,
            freq=1,
            cve_count=0,
            highest_severity=0.0,
            signatures=[]
        )

        # Now set up data that would cause the SQL query to return the same software
        # but with a different ID (simulating OPSWAT finding the same Firefox)

        # Create regular scanner data
        report = AppReportFactory(app_install=self.app_install)

        # Create Firefox in SoftwarePackage (lowercase to match what SQL would return)
        software_package = SoftwarePackageFactory(
            vendor="mozilla",  # lowercase
            product="firefox",  # lowercase
            version="136.0.4",
            mobile_app=False
        )

        AppOSInstalledSoftware.objects.create(
            report=report,
            software=software_package
        )

        # Also create OPSWAT data for Firefox
        # Use the existing product/version we already created in setUp
        firefox_product = ProductFactory(
            name="firefox",  # lowercase to match
            vendor=self.vendor
        )
        firefox_version = ProductVersionFactory(
            product=firefox_product,
            raw_version="136.0.4"
        )

        installed_product = InstalledProductFactory(
            app_install=self.app_install
        )
        installed_product.product_versions.add(firefox_version)

        # Check what records exist before the update
        before_count = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install
        ).count()
        self.assertEqual(before_count, 1, "Should have one existing record")

        # Now when we run the update, let's see what happens
        # If the code is working correctly, it might not raise an error
        # But let's check if it creates duplicate attempts
        update_installed_software_individual_for_all_records(is_async=False)
        # Check how many records we have after
        after_count = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install
        ).count()

        self.assertEqual(after_count, 1, "Should have one record after update")

        # If we get here without error, let's see what happened
        installed_software = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install,
            product_lc="firefox",
            version_lc="136.0.4"
        )
        self.assertEqual(installed_software.count(), 1, "Should have one record after update")
        self.assertNotEqual(installed_software.first().id, existing_id, "Should have a different ID, as it was newly created.")
        self.assertEqual(installed_software.first().id, f"{self.app_install.id}:opswat:{firefox_version.id}")



class UniquenessAndFrequencyViewTestCase(SoftwarePackageHelperTestMixin, TestCase):
    """
    This test case verifies the accurate calculation of uniqueness and frequency across all three views:
    - InstalledSoftwareAppInstallIndividual
    - InstalledSoftwareOrganisationIndividual
    - InstalledSoftwarePartnerSummary
    """
    def setUp(self):
        self.setUpTwoOrganisationsFourAppInstalls()

    def _check_app_install_initial_data(self, app_install, packages: list):
        regular_scanner_packages = AppOSInstalledSoftware.objects.filter(
            report__app_install=app_install
        )
        self.assertEqual(regular_scanner_packages.count(), len([package for package in packages if package["source"] == REGULAR_SOURCE]))
        opswat_packages = ProductVersion.objects.filter(user_installs__app_install=app_install)
        self.assertEqual(opswat_packages.count(), len([package for package in packages if package["source"] == OPSWAT_SOURCE]))
        for package in packages:
            if package["source"] == REGULAR_SOURCE:
                with self.subTest(package=package):
                    package_in_db = regular_scanner_packages.filter(
                        software__product=package["product"],
                        software__vendor=package["vendor"],
                        software__version=package["version"],
                    ).first()
                    # Check if CVEs exist based on the test data's 'is_vulnerable' flag
                    expected_vulnerable = bool(len(package["cves"]))
                    actual_vulnerable = package_in_db.software.related_cves.cves_fk.exists() if hasattr(package_in_db.software, 'related_cves') else False
                    self.assertEqual(expected_vulnerable, actual_vulnerable)
                    self.assertTrue(package_in_db, f"Package {package} is missing")
            elif package["source"] == OPSWAT_SOURCE:
                with self.subTest(package=package):
                    exists = opswat_packages.filter(
                        product__name=package["product"],
                        product__vendor__name=package["vendor"],
                        raw_version=package["version"],
                    ).exists()

                    self.assertTrue(exists, f"Package {package} is missing")

    def _check_summarized_data(self, instance, total, vulnerable, safe):
        packages = self._get_summary(instance)
        self.assertEqual(packages.count(), total)
        self.assertEqual(packages.filter(is_vulnerable=True).count(), vulnerable)
        self.assertEqual(packages.filter(is_vulnerable=False).count(), safe)

    def _check_summarized_record_frequency(self, instance, product, version, frequency):
        package = self._get_summary(instance).get(
            product__iexact=product,
            version__iexact=version
        )
        # freq for app install level and device_count for org level
        count = getattr(package, "freq", None) or getattr(package, "device_count", None)
        self.assertEqual(count, frequency)

    def _check_vulnerability_details(self, instance, product, version, expected_vulnerable, expected_cve_count, expected_severity):
        """Check vulnerability details (is_vulnerable, cve_count, highest_severity) for a specific product."""
        package = self._get_summary(instance).get(
            product__iexact=product,
            version__iexact=version
        )
        with self.subTest(f"{instance} - {product}/{version} - vulnerability status"):
            self.assertEqual(package.is_vulnerable, expected_vulnerable)

        with self.subTest(f"{instance} - {product}/{version} - CVE count"):
            self.assertEqual(package.cve_count, expected_cve_count)

        with self.subTest(f"{instance} - {product}/{version} - highest severity"):
            self.assertEqual(package.highest_severity, expected_severity)

    def _check_id_format(self, instance, product, version, app_install_id, source, source_id):
        """
        Check that the ID in the materialized view is correctly formatted.
        For REGULAR_SOURCE: 'app_install_id:{REGULAR_SOURCE}:software_package_id'
        For OPSWAT_SOURCE: 'app_install_id:{OPSWAT_SOURCE}:product_version_id'
        """
        package = self._get_summary(instance).get(
            app_install_id=app_install_id,
            product__iexact=product,
            version__iexact=version
        )

        expected_id = f"{app_install_id}:{source}:{source_id}"
        self.assertIn(expected_id, package.id.split(','))

    def test_app_install_initial_data(self):
        self._check_app_install_initial_data(self.app_install_1, self.app_install_1_packages)
        self._check_app_install_initial_data(self.app_install_2, self.app_install_2_packages)
        self._check_app_install_initial_data(
            self.app_install_3, self.app_install_3_packages + self.app_install_3_second_os_user_packages
        )
        self._check_app_install_initial_data(self.app_install_4, self.app_install_4_packages)

    def test_summarized_app_install_level(self):
        self._check_summarized_data(self.app_install_1, total=4, vulnerable=0, safe=4)
        self._check_summarized_data(self.app_install_2, total=3, vulnerable=2, safe=1)
        self._check_summarized_data(self.app_install_3, total=2, vulnerable=0, safe=2)
        self._check_summarized_data(self.app_install_4, total=1, vulnerable=0, safe=1)

        # Test vulnerability details at app install level
        # App Install 1: Regular scanner CVEs should be ignored due to OPSWAT presence
        self._check_vulnerability_details(
            self.app_install_1, AI1_EDGE, VERSION_1_0_0,
            expected_vulnerable=False, expected_cve_count=0, expected_severity=CVE.SEVERITY_UNKNOWN
        )

        # App Install 2: OPSWAT critical CVE for Edge
        self._check_vulnerability_details(
            self.app_install_2, AI2_EDGE, VERSION_1_0_0,
            expected_vulnerable=True, expected_cve_count=1, expected_severity=10.0
        )
        # App Install 2: OPSWAT low CVE for Package
        self._check_vulnerability_details(
            self.app_install_2, AI2_PACKAGE, VERSION_1_0_0,
            expected_vulnerable=True, expected_cve_count=1, expected_severity=2.0
        )

        # App Install 3: Regular scanner CVEs ignored
        self._check_vulnerability_details(
            self.app_install_3, AI3_PACKAGE, VERSION_1_0_0,
            expected_vulnerable=False, expected_cve_count=0, expected_severity=CVE.SEVERITY_UNKNOWN
        )

        # App Install 4: Regular scanner CVEs ignored, OPSWAT has no CVEs
        self._check_vulnerability_details(
            self.app_install_4, AI4_PYCHARM_OPSWAT, VERSION_2022_1_1,
            expected_vulnerable=False, expected_cve_count=0, expected_severity=CVE.SEVERITY_UNKNOWN
        )

    def test_summarized_app_install_frequency(self):
        self._check_summarized_record_frequency(
            # If 2 app os users have the same software installed,
            # the frequency is counted on the app install so frequency is 1
            self.app_install_3, AI3_ACTIVE_PROTECT, VERSION_5_0_0, 1
        )
        self._check_summarized_record_frequency(
            self.app_install_4, AI4_PYCHARM_OPSWAT, VERSION_2022_1_1, 1
        )

    def test_summarized_organisation_level(self):
        self._check_summarized_data(self.org1, total=4, vulnerable=2, safe=2)
        self._check_summarized_data(self.org2, total=3, vulnerable=0, safe=3)

        # Test app_install_ids are correctly populated and unique for org1
        org1_packages = InstalledSoftwareOrganisationIndividual.objects.filter(organisation=self.org1)
        for package in org1_packages:
            with self.subTest(f"Org1 - {package.product}/{package.version} - app_install_ids"):
                # Check that app_install_ids is not empty
                self.assertGreater(len(package.app_install_ids), 0, "app_install_ids should not be empty")

                # Check that there are no duplicates
                self.assertEqual(
                    len(package.app_install_ids),
                    len(set(package.app_install_ids)),
                    f"app_install_ids should not contain duplicates for {package.product}/{package.version}"
                )

                # Check that all app_install_ids belong to the correct organization
                for app_install_id in package.app_install_ids:
                    app_install = AppInstall.objects.get(id=app_install_id)
                    self.assertEqual(
                        app_install.app_user.organisation_id,
                        self.org1.id,
                        f"app_install_id {app_install_id} should belong to org1"
                    )

        # Test vulnerability details at organisation level
        # Org 1: Edge from App Install 2 (OPSWAT) with Critical CVE
        self._check_vulnerability_details(
            self.org1, AI1_EDGE, VERSION_1_0_0,
            expected_vulnerable=True, expected_cve_count=1, expected_severity=10.0
        )

        # Org 1: Package from App Install 2 (OPSWAT) with Low CVE
        self._check_vulnerability_details(
            self.org1, AI1_PACKAGE, VERSION_1_0_0,
            expected_vulnerable=True, expected_cve_count=1, expected_severity=2.0
        )

        # Org 2: Package with Regular scanner CVEs ignored
        self._check_vulnerability_details(
            self.org2, AI3_PACKAGE, VERSION_1_0_0,
            expected_vulnerable=False, expected_cve_count=0, expected_severity=CVE.SEVERITY_UNKNOWN
        )

    def test_summarized_organisation_frequency(self):
        self._check_summarized_record_frequency(
            self.org1, AI1_ACTIVE_PROTECT, VERSION_5_0_0, 1
        )
        self._check_summarized_record_frequency(
            self.org1, AI1_EDGE, VERSION_1_0_0, 2
        )
        self._check_summarized_record_frequency(
            self.org1, AI1_SAFARI, VERSION_2_0_0, 2
        )
        self._check_summarized_record_frequency(
            self.org1, AI1_PACKAGE, VERSION_1_0_0, 2
        )

        self._check_summarized_record_frequency(
            self.org2, AI3_ACTIVE_PROTECT, VERSION_5_0_0, 1
        )
        self._check_summarized_record_frequency(
            self.org2, AI4_PYCHARM_OPSWAT, VERSION_2022_1_1, 1
        )
        self._check_summarized_record_frequency(
            self.org2, AI3_PACKAGE, VERSION_1_0_0, 1
        )

    def test_organisation_app_install_ids_multiple_elements(self):
        """Test that packages installed on multiple app_installs have correct app_install_ids arrays."""
        # Test Edge in org1 which is installed on both app_install_1 and app_install_2
        edge_package = InstalledSoftwareOrganisationIndividual.objects.get(
            organisation=self.org1,
            product__iexact=AI1_EDGE,
            version__iexact=VERSION_1_0_0
        )

        # Should have exactly 2 app_install_ids
        self.assertEqual(
            len(edge_package.app_install_ids),
            2,
            "Edge in org1 should have 2 app_install_ids"
        )

        # Should contain both app_install_1 and app_install_2
        self.assertEqual(
            set(edge_package.app_install_ids),
            {self.app_install_1.id, self.app_install_2.id},
            "Edge in org1 should have app_install_ids from both app_installs"
        )

    def test_summarized_partner_level(self):
        self._check_summarized_data(self.partner, total=5, vulnerable=2, safe=3)

        # Test vulnerability details at partner level
        # Partner: Edge should use OPSWAT data from Org 1 / App Install 2
        self._check_vulnerability_details(
            self.partner, AI1_EDGE, VERSION_1_0_0,
            expected_vulnerable=True, expected_cve_count=1, expected_severity=10.0
        )

        # Partner: Package should use OPSWAT data from Org 1 / App Install 2
        self._check_vulnerability_details(
            self.partner, AI1_PACKAGE, VERSION_1_0_0,
            expected_vulnerable=True, expected_cve_count=1, expected_severity=2.0
        )

        # Partner: PyCharm should have no CVEs
        self._check_vulnerability_details(
            self.partner, AI4_PYCHARM_OPSWAT, VERSION_2022_1_1,
            expected_vulnerable=False, expected_cve_count=0, expected_severity=CVE.SEVERITY_UNKNOWN
        )

    def test_summarized_partner_frequency(self):
        self._check_summarized_record_frequency(
            self.partner, AI1_ACTIVE_PROTECT, VERSION_5_0_0, 2
        )
        self._check_summarized_record_frequency(
            self.partner, AI1_EDGE, VERSION_1_0_0, 2
        )
        self._check_summarized_record_frequency(
            self.partner, AI1_SAFARI, VERSION_2_0_0, 2
        )
        self._check_summarized_record_frequency(
            self.partner, AI1_PACKAGE, VERSION_1_0_0, 3
        )
        self._check_summarized_record_frequency(
            self.partner, AI4_PYCHARM_OPSWAT, VERSION_2022_1_1, 1
        )

    def test_app_install_id_format(self):
        """Test that IDs are correctly formatted at AppInstall level."""
        # Get the packages from app_install_1
        packages = self._get_summary(self.app_install_1)

        # Get expected IDs
        active_protect_id = str(ProductVersion.objects.get(
            product__name=AI1_ACTIVE_PROTECT,
            raw_version=VERSION_5_0_0
        ).id)
        edge_id = str(AppOSInstalledSoftware.objects.get(
            software__product=AI1_EDGE,
            software__version=VERSION_1_0_0
        ).software.id)
        safari_id = str(AppOSInstalledSoftware.objects.get(
            software__product=AI1_SAFARI,
            software__version=VERSION_2_0_0
        ).software.id)
        package_id = str(AppOSInstalledSoftware.objects.get(
            software__product=AI1_PACKAGE,
            software__version=VERSION_1_0_0,
            report__app_install=self.app_install_1
        ).software.id)

        # Check each package's ID
        active_protect = packages.get(product=AI1_ACTIVE_PROTECT, version=VERSION_5_0_0)
        self.assertEqual(active_protect.id, f"{self.app_install_1.id}:{OPSWAT_SOURCE}:{active_protect_id}")

        edge = packages.get(product=AI1_EDGE, version=VERSION_1_0_0)
        self.assertEqual(edge.id, f"{self.app_install_1.id}:{REGULAR_SOURCE}:{edge_id}")

        safari = packages.get(product=AI1_SAFARI, version=VERSION_2_0_0)
        self.assertEqual(safari.id, f"{self.app_install_1.id}:{REGULAR_SOURCE}:{safari_id}")

        package = packages.get(product=AI1_PACKAGE, version=VERSION_1_0_0)
        self.assertEqual(package.id, f"{self.app_install_1.id}:{REGULAR_SOURCE}:{package_id}")

        # Verify total number of packages
        self.assertEqual(packages.count(), 4)

    def test_organisation_id_format(self):
        """Test that IDs are correctly formatted at Organisation level."""
        # Get the packages from org1
        org1_packages = self._get_summary(self.org1)

        # Get expected IDs for org1
        edge_opswat_id = str(ProductVersion.objects.get(
            product__name=AI2_EDGE,
            raw_version=VERSION_1_0_0
        ).id)
        edge_regular_id = str(AppOSInstalledSoftware.objects.get(
            software__product=AI1_EDGE,
            software__version=VERSION_1_0_0
        ).software.id)
        safari_opswat_id = str(ProductVersion.objects.get(
            product__name=AI2_SAFARI,
            raw_version=VERSION_2_0_0
        ).id)
        safari_regular_id = str(AppOSInstalledSoftware.objects.get(
            software__product=AI1_SAFARI,
            software__version=VERSION_2_0_0
        ).software.id)
        package_opswat_id = str(ProductVersion.objects.get(
            product__name=AI2_PACKAGE,
            raw_version=VERSION_1_0_0
        ).id)
        package_regular_id = str(AppOSInstalledSoftware.objects.get(
            software__product=AI1_PACKAGE,
            software__version=VERSION_1_0_0,
            report__app_install=self.app_install_1
        ).software.id)
        active_protect_id = str(ProductVersion.objects.get(
            product__name=AI1_ACTIVE_PROTECT,
            raw_version=VERSION_5_0_0
        ).id)

        # Check each package's ID in org1
        edge = org1_packages.get(product__iexact=AI1_EDGE, version=VERSION_1_0_0)
        self.assertEqual(
            set(edge.id.split(',')),
            {f"{self.org1.id}:{OPSWAT_SOURCE}:{edge_opswat_id}", f"{self.org1.id}:{REGULAR_SOURCE}:{edge_regular_id}"}
        )

        safari = org1_packages.get(product__iexact=AI1_SAFARI, version=VERSION_2_0_0)
        self.assertEqual(
            set(safari.id.split(',')),
            {f"{self.org1.id}:{OPSWAT_SOURCE}:{safari_opswat_id}", f"{self.org1.id}:{REGULAR_SOURCE}:{safari_regular_id}"}
        )

        package = org1_packages.get(product__iexact=AI1_PACKAGE, version=VERSION_1_0_0)
        self.assertEqual(
            set(package.id.split(',')),
            {f"{self.org1.id}:{OPSWAT_SOURCE}:{package_opswat_id}", f"{self.org1.id}:{REGULAR_SOURCE}:{package_regular_id}"}
        )

        active_protect = org1_packages.get(product__iexact=AI1_ACTIVE_PROTECT, version=VERSION_5_0_0)
        self.assertEqual(
            set(active_protect.id.split(',')),
            {f"{self.org1.id}:{OPSWAT_SOURCE}:{active_protect_id}"}
        )

        # Get the packages from org2
        org2_packages = self._get_summary(self.org2)

        # Get expected IDs for org2
        active_protect_id = str(ProductVersion.objects.get(
            product__name=AI3_ACTIVE_PROTECT,
            raw_version=VERSION_5_0_0
        ).id)
        pycharm_opswat_id = str(ProductVersion.objects.get(
            product__name=AI4_PYCHARM_OPSWAT,
            raw_version=VERSION_2022_1_1
        ).id)
        package_regular_id = str(AppOSInstalledSoftware.objects.get(
            software__product=AI3_PACKAGE,
            software__version=VERSION_1_0_0,
            report__app_install=self.app_install_3
        ).software.id)

        # Check each package's ID in org2
        active_protect = org2_packages.get(product__iexact=AI3_ACTIVE_PROTECT, version=VERSION_5_0_0)
        self.assertEqual(
            set(active_protect.id.split(',')),
            {f"{self.org2.id}:{OPSWAT_SOURCE}:{active_protect_id}"}
        )

        pycharm = org2_packages.get(product__iexact=AI4_PYCHARM_OPSWAT, version=VERSION_2022_1_1)
        self.assertEqual(
            set(pycharm.id.split(',')),
            {f"{self.org2.id}:{OPSWAT_SOURCE}:{pycharm_opswat_id}"}
        )

        package = org2_packages.get(product__iexact=AI3_PACKAGE, version=VERSION_1_0_0)
        self.assertEqual(
            set(package.id.split(',')),
            {f"{self.org2.id}:{REGULAR_SOURCE}:{package_regular_id}"}
        )

    def test_partner_id_format(self):
        """Test that IDs are correctly formatted at Partner level."""
        # Get all packages at partner level
        partner_packages = self._get_summary(self.partner)

        # Get expected IDs
        active_protect_id = str(ProductVersion.objects.get(
            product__name=AI1_ACTIVE_PROTECT,
            raw_version=VERSION_5_0_0
        ).id)
        edge_opswat_id = str(ProductVersion.objects.get(
            product__name=AI2_EDGE,
            raw_version=VERSION_1_0_0
        ).id)
        edge_regular_id = str(AppOSInstalledSoftware.objects.get(
            software__product=AI1_EDGE,
            software__version=VERSION_1_0_0
        ).software.id)
        safari_opswat_id = str(ProductVersion.objects.get(
            product__name=AI2_SAFARI,
            raw_version=VERSION_2_0_0
        ).id)
        safari_regular_id = str(AppOSInstalledSoftware.objects.get(
            software__product=AI1_SAFARI,
            software__version=VERSION_2_0_0
        ).software.id)
        pycharm_opswat_id = str(ProductVersion.objects.get(
            product__name=AI4_PYCHARM_OPSWAT,
            raw_version=VERSION_2022_1_1
        ).id)
        package_opswat_id = str(ProductVersion.objects.get(
            product__name=AI2_PACKAGE,
            raw_version=VERSION_1_0_0
        ).id)
        package_regular_id_1 = str(AppOSInstalledSoftware.objects.get(
            software__product=AI1_PACKAGE,
            software__version=VERSION_1_0_0,
            report__app_install=self.app_install_1
        ).software.id)
        package_regular_id_2 = str(AppOSInstalledSoftware.objects.get(
            software__product=AI3_PACKAGE,
            software__version=VERSION_1_0_0,
            report__app_install=self.app_install_3
        ).software.id)

        # Check Active Protect - should have 1 ID (OPSWAT only)
        active_protect = partner_packages.get(product__iexact=AI1_ACTIVE_PROTECT, version=VERSION_5_0_0)
        active_protect_ids = set(active_protect.id.split(','))
        self.assertEqual(len(active_protect_ids), 1)
        self.assertEqual(
            active_protect_ids,
            {f"{self.partner.id}:{OPSWAT_SOURCE}:{active_protect_id}"}
        )

        # Check Edge - should have 2 IDs (1 OPSWAT, 1 Regular)
        edge = partner_packages.get(product__iexact=AI1_EDGE, version=VERSION_1_0_0)
        edge_ids = set(edge.id.split(','))
        self.assertEqual(len(edge_ids), 2)
        self.assertEqual(
            edge_ids,
            {
                f"{self.partner.id}:{OPSWAT_SOURCE}:{edge_opswat_id}",
                f"{self.partner.id}:{REGULAR_SOURCE}:{edge_regular_id}"
            }
        )

        # Check Safari - should have 2 IDs (1 OPSWAT, 1 Regular)
        safari = partner_packages.get(product__iexact=AI1_SAFARI, version=VERSION_2_0_0)
        safari_ids = set(safari.id.split(','))
        self.assertEqual(len(safari_ids), 2)
        self.assertEqual(
            safari_ids,
            {
                f"{self.partner.id}:{OPSWAT_SOURCE}:{safari_opswat_id}",
                f"{self.partner.id}:{REGULAR_SOURCE}:{safari_regular_id}"
            }
        )

        # Check PyCharm - should have 1 ID as all on the same app install
        pycharm = partner_packages.get(product__iexact=AI4_PYCHARM_OPSWAT, version=VERSION_2022_1_1)
        pycharm_ids = set(pycharm.id.split(','))
        self.assertEqual(len(pycharm_ids), 1)
        self.assertEqual(
            pycharm_ids,
            {
                f"{self.partner.id}:{OPSWAT_SOURCE}:{pycharm_opswat_id}",
            }
        )

        # Check Package - should have 3 IDs (1 OPSWAT, 2 Regular)
        package = partner_packages.get(product__iexact=AI1_PACKAGE, version=VERSION_1_0_0)
        package_ids = set(package.id.split(','))
        self.assertEqual(len(package_ids), 3)
        self.assertEqual(
            package_ids,
            {
                f"{self.partner.id}:{OPSWAT_SOURCE}:{package_opswat_id}",
                f"{self.partner.id}:{REGULAR_SOURCE}:{package_regular_id_1}",
                f"{self.partner.id}:{REGULAR_SOURCE}:{package_regular_id_2}"
            }
        )

        # Verify total number of packages
        self.assertEqual(partner_packages.count(), 5)

class DuplicatePackagesTestCase(SoftwarePackageHelperTestMixin, TestCase):
    def test_software_uniqueness_ignores_source_id_and_vendor(self):
        """
        Verifies that software packages with identical product names, versions, and sources
        are counted as a single unique package, even if they have different `source_id`
        or variations in the `vendor` name.

        Scenario:
        - Two packages exist with the same product (`viber`), version (`17.4.0.482`),
          and source (`regular`), but different `source_id` values and vendor names:
            1. source_id: 112, vendor: "viber"
            2. source_id: 12324, vendor: "2010-2022"
        - Uniqueness calculation ignores `source_id`, vendor name differences, and casing.

        Expected Outcome:
        - Both packages should be considered as one unique record.
        - The final package count should be 1.
        """
        app_user = AppUserFactory()
        app_install = AppInstallFactory(app_user=app_user)
        packages = [
            {
                "product": "viber", "vendor": "viber", "version": "17.4.0.482", "is_vulnerable": False,
                "source": REGULAR_SOURCE, "cves": []
            },
            {
                "product": "viber", "vendor": "2010-2022", "version": "17.4.0.482", "is_vulnerable": False,
                "source": REGULAR_SOURCE, "cves": []
            },
        ]
        self._create_packages_with_cves(app_install, packages)
        update_installed_software_individual_for_all_organisations(is_async=False)
        packages = self._get_summary(app_install)
        self.assertEqual(packages.count(), 1)


class AggregatedProductVersionIdsTestCase(SoftwarePackageHelperTestMixin, TestCase):
    """Test that aggregated_installed_product_version_ids field is populated correctly."""

    def test_aggregated_installed_product_version_ids_for_opswat_source(self):
        """Test that aggregated_installed_product_version_ids are collected correctly for OPSWAT source."""
        from opswat.factories import InstalledProductVersionFactory

        # Create app users and installs
        org = OrganisationFactory()
        user1 = AppUserFactory(organisation=org)
        user2 = AppUserFactory(organisation=org)
        app_install1 = AppInstallFactory(app_user=user1)
        app_install2 = AppInstallFactory(app_user=user2)

        # Create OPSWAT product and versions
        vendor = ProductVendorFactory(name="Test Vendor")
        product = ProductFactory(name="Test Product", vendor=vendor)
        version = ProductVersionFactory(product=product, raw_version="1.0.0")

        # Create installed products with versions
        installed_product1 = InstalledProductFactory(app_install=app_install1)
        ipv1 = InstalledProductVersionFactory(
            installed_product=installed_product1,
            product_version=version
        )
        # Create a second installed product for the same app install to have multiple IPVs
        installed_product1b = InstalledProductFactory(app_install=app_install1)
        ipv2 = InstalledProductVersionFactory(
            installed_product=installed_product1b,
            product_version=version
        )

        installed_product2 = InstalledProductFactory(app_install=app_install2)
        ipv3 = InstalledProductVersionFactory(
            installed_product=installed_product2,
            product_version=version
        )

        # Update the organisation individual records
        InstalledSoftwareOrganisationIndividual.update_individual_record(organisation_id=org.id)

        # Get the created record
        record = InstalledSoftwareOrganisationIndividual.objects.get(
            organisation=org,
            product="Test Product",
            version="1.0.0"
        )

        # Check aggregated_installed_product_version_ids
        self.assertIsNotNone(record.aggregated_installed_product_version_ids)
        self.assertEqual(len(record.aggregated_installed_product_version_ids), 3)
        expected_ids = {ipv1.id, ipv2.id, ipv3.id}
        self.assertEqual(set(record.aggregated_installed_product_version_ids), expected_ids)

    def test_aggregated_installed_product_version_ids_empty_for_regular_source(self):
        """Test that aggregated_installed_product_version_ids is empty for regular source."""
        org = OrganisationFactory()
        app_user = AppUserFactory(organisation=org)
        app_install = AppInstallFactory(app_user=app_user)

        # Create regular package
        packages = [{
            "product": "Regular Product",
            "vendor": "Regular Vendor",
            "version": "3.0.0",
            "is_vulnerable": False,
            "source": REGULAR_SOURCE,
            "cves": []
        }]
        self._create_packages_with_cves(app_install, packages)

        # Update the organisation individual records
        InstalledSoftwareOrganisationIndividual.update_individual_record(organisation_id=org.id)

        # Get the created record
        record = InstalledSoftwareOrganisationIndividual.objects.get(
            organisation=org,
            product="Regular Product",
            version="3.0.0"
        )

        # aggregated_installed_product_version_ids should be empty for regular source
        self.assertIsNotNone(record.aggregated_installed_product_version_ids)
        self.assertEqual(len(record.aggregated_installed_product_version_ids), 0)
