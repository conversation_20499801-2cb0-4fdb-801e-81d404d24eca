from datetime import datetime, timed<PERSON>ta

from django.test import TestCase

from appusers.models import (
    AppOSInstalledSoftware, SoftwarePackage, SoftwarePackageCVEs,
    InstalledSoftwarePartnerSummary
)
from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from appusers.models.factories import (
    AppInstallFactory, AppReportFactory, AppOSInstalledSoftwareFactory,
    SoftwarePackageCVEsFactory, SoftwarePackageFactory, AppInstallOSUserFactory
)
from appusers.models.factories import AppUserFactory
from appusers.tests.helpers import SoftwarePackageHelperTestMixin
from common.base_tests import BaseTestCase
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from opswat.factories import ProductVendorFactory, ProductFactory, ProductVersionFactory, \
    CVEFactory, InstalledProductFactory
from opswat.models import CVE, ProductCategory, Product
from opswat.utils import get_installed_software_status
from organisations.factories import OrganisationFactory
from vulnerabilities.factories import CVERepositoryFactory
from vulnerabilities.models import CVERepository
from vulnerabilities.utils import OPSWAT_SOURCE, REGULAR_SOURCE


class TestAppInstallVulnerabilities(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_app_user()
        self.app_install = AppInstallFactory(app_user=self.app_user)
        self.app_report = AppReportFactory(app_install=self.app_install)

        # Existing_cve - a CVE which exist in CVERepository.
        # We call other CVEs "False positives" and they should not be counted as vulnerable.
        existing_cve = 'CVE-2021-1234'
        existing_cve_1 = 'CVE-2020-1235'
        non_existent_cve = 'NON_EXISTENT_CVE'
        non_existent_cve_1 = 'NON_EXISTENT_CVE'

        CVERepositoryFactory(cve_id=existing_cve)
        CVERepositoryFactory(cve_id=existing_cve_1)
        self.cves_list = [
            # First value in tuple: expected value when ignoring false positives
            # Second value in tuple: list of CVEs
            (True,    [existing_cve,   non_existent_cve]), # should be vulnerable as has 0 existing_cve
            (True,    [existing_cve_1, non_existent_cve]), # should be vulnerable as has 1 existing_cve
            (True,    [non_existent_cve, existing_cve_1]), # should be vulnerable as has 1 existing_cve
            (True,    [non_existent_cve_1, existing_cve]), # should be vulnerable as has 1 existing_cve
            (True,    [existing_cve, existing_cve_1]), # should be vulnerable as has 2 existing_cve
            (True,    [existing_cve, existing_cve_1, non_existent_cve]), # should be vulnerable as has 2 existing_cve
            (False,   [non_existent_cve]), # should be safe as no CVEs are present in CVERepository
            (False,   [non_existent_cve, non_existent_cve_1]), # should be safe as no CVEs are present in CVERepository
            (False,   []), # should be safe as no CVEs are present in the CVERepository
            (False,   None), # should be safe as no SoftwarePackageCVEs are attached
        ]

        self.existent_cve = existing_cve
        self.non_existent_cve = non_existent_cve

    def test_installed_software_status(self):
        for i, (_, cves) in enumerate(self.cves_list):
            software_package = SoftwarePackageFactory(product=f'product_{i}')
            app_os_installed_software = AppOSInstalledSoftwareFactory(
                report=self.app_report,
                software=software_package
            )
            if cves is None:
                continue
            software_package_cves = SoftwarePackageCVEsFactory(
                software=app_os_installed_software.software,
                cves=cves
            )
            software_package_cves.cves_fk.add(*CVERepository.objects.filter(cve_id__in=cves))

        self.assertEqual(SoftwarePackageCVEs.objects.count(), 9)
        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software_status = get_installed_software_status(self.app_install.id, "app_install")
        self.assertEqual(installed_software_status['vulnerable'], 6)
        self.assertEqual(installed_software_status['safe'], 4)
        self.assertEqual(installed_software_status['total'], 10)

    def test_set_is_vulnerable(self):
        self.assertFalse(self.app_install.is_vulnerable)
        for i, (expected, cves) in enumerate(self.cves_list):
            app_report = AppReportFactory(app_install=self.app_install)
            software_package = SoftwarePackageFactory(product=f'product_{i}')
            app_os_installed_software = AppOSInstalledSoftwareFactory(
                report=app_report,
                software=software_package
            )
            if cves is not None:
                software_package_cves = SoftwarePackageCVEsFactory(
                    software=app_os_installed_software.software,
                    cves=cves
                )
                software_package_cves.cves_fk.add(*CVERepository.objects.filter(cve_id__in=cves))
            self.app_install.set_is_vulnerable(app_report)
            self.app_install.refresh_from_db()
            try:
                self.assertEqual(self.app_install.is_vulnerable, expected)
            except AssertionError as e:
                raise AssertionError(f'Failed app_install on {i} iteration (expected={expected}, cves={cves})') from e
            try:
                self.assertEqual(software_package.is_vulnerable, expected)
            except AssertionError as e:
                raise AssertionError(f'Failed software_package on {i} iteration (expected={expected}, returned={software_package.is_vulnerable}. cves={cves})') from e

    def test_AppOSInstalledSoftwareManager(self):
        """
        Validate the AppOSInstalledSoftwareManager.
        Prove that two different reports are not confused in the AppOSInstalledSoftwareManager.
        """

        report_0 = AppReportFactory(app_install=self.app_install)
        report_1 = AppReportFactory(app_install=self.app_install)
        software_package_0 = SoftwarePackageFactory(product='product1')
        software_package_1 = SoftwarePackageFactory(product='product2')
        app_os_0 = AppOSInstalledSoftwareFactory(
            report=report_0,
            software=software_package_0
        )
        software_package_cves = SoftwarePackageCVEsFactory(
            software=app_os_0.software,
            cves=[self.existent_cve, self.non_existent_cve]
        )
        software_package_cves.cves_fk.add(CVERepository.objects.get(cve_id=self.existent_cve))
        app_os_1 = AppOSInstalledSoftwareFactory(
            report=report_1,
            software=software_package_1
        )
        SoftwarePackageCVEsFactory(
            software=app_os_1.software,
            cves=[]
        )

        self.assertEqual(AppOSInstalledSoftware.objects.vulnerable_objects(report_0).count(), 1)
        self.assertEqual(AppOSInstalledSoftware.objects.vulnerable_objects(report_1).count(), 0)

        self.assertEqual(AppOSInstalledSoftware.objects.filter(report=report_0).count(), 1)
        self.assertEqual(AppOSInstalledSoftware.objects.filter(report=report_1).count(), 1)


    def create_records(self):
        # Create records for first org
        for i, (_, cves) in enumerate(self.cves_list):
            software_package = SoftwarePackageFactory(product=f'product_{i}')
            app_os_installed_software = AppOSInstalledSoftwareFactory(
                report=self.app_report,
                software=software_package
            )
            if cves is None:
                continue
            software_package_cves = SoftwarePackageCVEsFactory(
                software=app_os_installed_software.software,
                cves=cves
            )
            software_package_cves.cves_fk.add(*CVERepository.objects.filter(cve_id__in=cves))

        # Create second org with different app user and install
        self.org2 = OrganisationFactory()
        self.app_user2 = AppUserFactory(organisation=self.org2)
        self.app_install2 = AppInstallFactory(app_user=self.app_user2)
        self.app_report2 = AppReportFactory(app_install=self.app_install2)

        # Create different records for second org
        for i, (_, cves) in enumerate(self.cves_list):
            software_package = SoftwarePackageFactory(product=f'product2_{i}')
            app_os_installed_software = AppOSInstalledSoftwareFactory(
                report=self.app_report2,
                software=software_package
            )
            if cves is None:
                continue
            software_package_cves = SoftwarePackageCVEsFactory(
                software=app_os_installed_software.software,
                cves=cves
            )
            software_package_cves.cves_fk.add(*CVERepository.objects.filter(cve_id__in=cves))


    def create_opswat_records(self):
        # Create OPSWAT data for vulnerable software
        self.vulnerable_category = ProductCategory.objects.get_or_create(
            opswat_id="16",
            defaults={"name": "Remote Desktop Control"}
        )[0]
        self.vulnerable_vendor = ProductVendorFactory(
            opswat_id="100020",
            name="TeamViewer GmbH"
        )
        self.vulnerable_product = Product.objects.get_or_create(
            opswat_id="100026",
            defaults={
                "name": "TeamViewer",
                "vendor": self.vulnerable_vendor
            }
        )[0]
        self.vulnerable_product.categories.add(self.vulnerable_category)

        self.vulnerable_version = ProductVersionFactory(
            product=self.vulnerable_product,
            raw_version="15.18.4",
            major=15,
            minor=18,
            patch=4,
            version_channel="stable"
        )

        # Create CVE for vulnerable version
        self.cve = CVEFactory(
            opswat_id="100001",
            cve_id="CVE-2023-1234",
            severity=CVE.SEVERITY_CRITICAL,
            severity_index=95,
            description="Remote code execution vulnerability in TeamViewer before 15.18.5",
            published_at=datetime.now() - timedelta(days=30),
            details={
                "affected_versions": "<=15.18.4",
                "attack_vector": "NETWORK",
                "impact": "Remote code execution"
            }
        )
        self.cve.product_version.add(self.vulnerable_version)

        # Create OPSWAT data for safe software
        self.safe_category = ProductCategory.objects.get_or_create(
            opswat_id="5",
            defaults={"name": "Antimalware"}
        )[0]
        self.safe_vendor = ProductVendorFactory(
            opswat_id="100013",
            name="Apple Inc."
        )
        self.safe_product = ProductFactory(
            opswat_id="100130",
            name="Apple Music",
            vendor=self.safe_vendor
        )
        self.safe_product.categories.add(self.safe_category)

        self.safe_version = ProductVersionFactory(
            product=self.safe_product,
            raw_version="1.3.3",
            major=1,
            minor=3,
            patch=3,
            version_channel="stable"
        )

        self.os_user = AppInstallOSUserFactory(
            app_install=self.app_install,
            username='testuser',
            domain='testdomain'
        )
        self.os_user = self.app_install.os_users.first()

        # Create software packages using factories
        self.installed_products = InstalledProductFactory(
            app_install=self.app_install
        )
        self.installed_products.product_versions.add(self.vulnerable_version)
        self.installed_products.product_versions.add(self.safe_version)



    def test_InstalledSoftwareSummary(self):
        self.create_records()
        InstalledSoftwareOrganisationIndividual.refresh()

        # Check basic model fields
        summary = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=self.app_user.organisation
        ).first()
        self.assertEqual(summary.organisation, self.app_user.organisation)
        self.assertEqual(summary.device_count, 1)  # Each software installed once per app_install

        # Extract numeric ID from composite ID for regular software
        if summary.source == 'regular':
            software_id = int(summary.source_id)
            software = SoftwarePackage.objects.get(id=software_id)
            self.assertEqual(summary.vendor, software.vendor)
            self.assertEqual(summary.product, software.product)
            self.assertEqual(summary.version, software.version)
            self.assertEqual(summary.mobile_app, software.mobile_app)

        # Verify only first org's software packages are included
        self.assertEqual(
            InstalledSoftwareOrganisationIndividual.objects.filter(
                organisation=self.app_user.organisation
            ).count(),
            len(self.cves_list)
        )

        org1_records = InstalledSoftwareOrganisationIndividual.objects.filter(organisation=self.app_user.organisation)
        vulnerable_software = set([x.product for x in org1_records if x.is_vulnerable])
        self.assertCountEqual(
            vulnerable_software,
            {'product_0', 'product_1', 'product_2', 'product_3', 'product_4', 'product_5'}
        )

        # Verify inactive app_installs are excluded
        self.app_install.inactive = True
        self.app_install.save()
        InstalledSoftwareOrganisationIndividual.refresh()
        self.assertEqual(InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=self.app_user.organisation
        ).count(), 0)

        # Verify inactive app_users are excluded
        self.app_install.inactive = False
        self.app_install.save()
        self.app_user.active = False
        self.app_user.save()
        InstalledSoftwareOrganisationIndividual.refresh()
        self.assertEqual(InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=self.app_user.organisation
        ).count(), 0)


    def test_InstalledSoftwareSummary_with_opswat(self):
        # Prepare both regular and OPSWAT test data
        self.create_records()  # Creates regular software records
        self.create_opswat_records()  # Creates OPSWAT software records

        InstalledSoftwareOrganisationIndividual.refresh()
        InstalledSoftwarePartnerSummary.refresh()

        # Get all records for the organization
        org_summary_records = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=self.app_user.organisation
        ).order_by('product')

        partner_summary_records = InstalledSoftwarePartnerSummary.objects.filter(
            partner=self.app_user.organisation.partner
        ).order_by('product')

        # Test OPSWAT software is included
        opswat_software = org_summary_records.filter(product__in=['TeamViewer', 'Apple Music'])
        self.assertEqual(opswat_software.count(), 2)

        # Verify TeamViewer details
        teamviewer = opswat_software.get(product='TeamViewer')
        self.assertEqual(teamviewer.vendor, 'TeamViewer GmbH')
        self.assertEqual(teamviewer.version, '15.18.4')
        self.assertTrue(teamviewer.is_vulnerable)
        self.assertEqual(teamviewer.device_count, 1)

        # Verify Apple Music details
        apple_music = opswat_software.get(product='Apple Music')
        self.assertEqual(apple_music.vendor, 'Apple Inc.')
        self.assertEqual(apple_music.version, '1.3.3')
        self.assertFalse(apple_music.is_vulnerable)
        self.assertEqual(apple_music.device_count, 1)

        # Verify regular software records still exist
        # is opswat data is provided we ignore regular software vulnerabilities
        regular_software = org_summary_records.filter(product__in=['product_0', 'product_1', 'product_2', 'product_3', 'product_4', 'product_5'])
        self.assertEqual(
            regular_software.filter(is_vulnerable=True).count(),
            0
        )
        regular_software_partner = partner_summary_records.filter(product__in=['product_0', 'product_1', 'product_2', 'product_3', 'product_4', 'product_5'])
        self.assertEqual(
            regular_software_partner.filter(is_vulnerable=True).count(),
            0
        )

        # Verify total counts
        self.assertEqual(
            org_summary_records.filter(is_vulnerable=True).count(),
            1  # only TeamViewer from opswat
        )
        self.assertEqual(
            partner_summary_records.filter(is_vulnerable=True).count(),
            1  # only TeamViewer from opswat
        )
        self.assertEqual(
            org_summary_records.count(),
            len(self.cves_list) + 2  # Regular software + TeamViewer and Apple Music
        )
        self.assertEqual(
            partner_summary_records.count(),
            len(self.cves_list) + 2  # Regular software + TeamViewer and Apple Music
        )

        # Test inactive app_install excludes OPSWAT software too
        self.app_install.inactive = True
        self.app_install.save()
        InstalledSoftwareOrganisationIndividual.refresh()
        self.assertEqual(
            InstalledSoftwareOrganisationIndividual.objects.filter(
                organisation=self.app_user.organisation
            ).count(),
            0
        )

        # Test inactive app_user excludes OPSWAT software too
        self.app_install.inactive = False
        self.app_install.save()
        self.app_user.active = False
        self.app_user.save()
        InstalledSoftwareOrganisationIndividual.refresh()
        self.assertEqual(
            InstalledSoftwareOrganisationIndividual.objects.filter(
                organisation=self.app_user.organisation
            ).count(),
            0
        )

    def test_InstalledSoftwareSummary_with_duplicate_opswat_entries(self):
        """Test that verifies behavior when same OPSWAT product is installed multiple times."""
        # Create initial data
        self.create_records()
        self.create_opswat_records()

        # Install same OPSWAT product
        duplicate_installed_products = InstalledProductFactory(
            app_install=self.app_install
        )
        duplicate_installed_products.product_versions.add(self.vulnerable_version)
        duplicate_installed_products.product_versions.add(self.safe_version)

        InstalledSoftwareOrganisationIndividual.refresh()

        # Get all records for the organization
        all_records = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=self.app_user.organisation
        ).order_by('product')

        # Test OPSWAT software is included and frequencies are correct
        opswat_software = all_records.filter(product__in=['TeamViewer', 'Apple Music'])
        self.assertEqual(opswat_software.count(), 2)  # Should still be 2 unique products

        # Verify TeamViewer details - freq should be 2 now
        teamviewer = opswat_software.get(product='TeamViewer')
        self.assertEqual(teamviewer.device_count, 1)  # Installed for both OS users, but since they are on the same app install, the frequency is 1

        # Verify Apple Music details - freq should be 2 now
        apple_music = opswat_software.get(product='Apple Music')
        self.assertEqual(apple_music.device_count, 1)  # Installed for both OS users, but since they are on the same app install, the frequency is 1


class InstalledSoftwareSummariesLogicTestCase(SoftwarePackageHelperTestMixin, TestCase):
    """
    Device with Both OPSWAT and Regular Scanner Data
        Packages: Both OPSWAT and Regular Scanner packages should be displayed.
        Vulnerabilities: Only OPSWAT vulnerabilities should be displayed.

    Device with Only Regular Scanner Data
        Packages: Regular Scanner packages should be displayed.
        Vulnerabilities: Regular Scanner vulnerabilities should be displayed.

    Device with Only OPSWAT Data
        Packages: OPSWAT packages should be displayed.
        Vulnerabilities: OPSWAT vulnerabilities should be displayed.

    Merging Logic
        A unique product is recognized by its `vendor`, `product name`, and `version`.
        If a product exists in both OPSWAT and Regular Scanner data, the OPSWAT record is preferred.

    Expected Behavior
        When both OPSWAT and Regular Scanner data are available, vulnerabilities from the Regular Scanner
        should be excluded from the results.
        Packages should always be included from both sources, even if vulnerabilities are filtered.
    """
    def setUp(self):
        self.app_user = AppUserFactory(organisation=OrganisationFactory())
        self.app_install_1 = AppInstallFactory(app_user=self.app_user)
        self.app_install_2 = AppInstallFactory(app_user=self.app_user)
        self.os_user = AppInstallOSUserFactory(
            app_install=self.app_install_1,
            username='testuser',
            domain='testdomain'
        )


    def __check_summary_stats(
            self, summary, expected_total, expected_regular, expected_opswat, expected_vulnerable, expected_safe
    ):
        self.assertEqual(summary.count(), expected_total)
        self.assertEqual(summary.filter(source__contains=REGULAR_SOURCE).count(), expected_regular)
        self.assertEqual(summary.filter(source__contains=OPSWAT_SOURCE).count(), expected_opswat)
        self.assertEqual(summary.filter(is_vulnerable=True).count(), expected_vulnerable)
        self.assertEqual(summary.filter(is_vulnerable=False).count(), expected_safe)

    def __create_regular_scanner_packages(self):
        # App Install 1 with `Firefox` and `Word`
        self._add_software_package_from_regular_scanner(
            self.app_install_1, "firefox", "mozilla", "132.0.1", cves=[{"id": "CVE-2023-1111", "severity": "HIGH"}]
        )
        self._add_software_package_from_regular_scanner(
            self.app_install_1, "word", "microsoft", "18.3.7", cves=[]
        )

        # App Install 2 with `Pycharm` and `Active protect`
        self._add_software_package_from_regular_scanner(
            self.app_install_2, "pycharm", "jetbrains", "2024.3.3.1", cves=[{"id": "CVE-2023-2222", "severity": "MEDIUM"}]
        )
        self._add_software_package_from_regular_scanner(
            self.app_install_2, "active protect", "cybersmart", "5.0.4", cves=[]
        )

    def __create_opswat_scanner_packages(self):
        # App Install 1 with `Firefox` and `Pycharm`
        self._add_software_package_from_opswat_scanner(
            self.app_install_1, "Firefox", "Mozilla", "132.0.1", cves=[{"id": "CVE-2023-1111", "severity": CVE.SEVERITY_CRITICAL}]
        )
        self._add_software_package_from_opswat_scanner(
            self.app_install_1, "Pycharm", "JetBrains", "2024.3.3.1", cves=[]
        )


    def test_installed_software_from_regular_scanner_only(self):
        self.__create_regular_scanner_packages()
        self._refresh_summaries()

        # check org summary
        self.__check_summary_stats(
            summary=self._get_summary(self.app_user.organisation),
            expected_total=4,
            expected_regular=4,
            expected_opswat=0,
            expected_vulnerable=2,
            expected_safe=2
        )

        # check app install 1 summary
        self.__check_summary_stats(
            summary=self._get_summary(self.app_install_1),
            expected_total=2,
            expected_regular=2,
            expected_opswat=0,
            expected_vulnerable=1,
            expected_safe=1
        )

        # check app install 2 summary
        self.__check_summary_stats(
            summary=self._get_summary(self.app_install_2),
            expected_total=2,
            expected_regular=2,
            expected_opswat=0,
            expected_vulnerable=1,
            expected_safe=1
        )

    def test_installed_software_from_regular_scanner_and_opswat(self):
        self.__create_regular_scanner_packages()
        self.__create_opswat_scanner_packages()
        self._refresh_summaries()

        """
        Summary should be the next:

        source   source_id   vendor      product                    version          is_vulnerable
        -----------------------------------------------------------------------------------------
        opswat   236        113        Mozilla Firefox             132.0.1          True
        opswat   236        114        JetBrains Pycharm           2024.3.3.1       False
        regular  236        722        Microsoft Word              18.3.7           False
        regular  237        724        Cybersmart Active Protect   5.0.4            False


        Same versions of Pycharm are presented twice in the summary because they belong to different app installs.
        """

        # check org summary
        self.__check_summary_stats(
            summary=self._get_summary(self.app_user.organisation),
            expected_total=4,
            expected_regular=3,
            expected_opswat=2,
            expected_vulnerable=1,
            expected_safe=3
        )

        # check app install 1 summary
        self.__check_summary_stats(
            summary=self._get_summary(self.app_install_1),
            expected_total=3,
            expected_regular=1,
            expected_opswat=2,
            expected_vulnerable=1,
            expected_safe=2
        )

        # check app install 2 summary
        self.__check_summary_stats(
            summary=self._get_summary(self.app_install_2),
            expected_total=2,
            expected_regular=2,
            expected_opswat=0,
            expected_vulnerable=1,
            expected_safe=1
        )

    def test_installed_software_from_regular_scanner_and_opswat_different_vendor(self):
        """
        Test that when the same product with same version exists in both OPSWAT and Regular Scanner,
        but with different vendors, the OPSWAT record is preferred and shown.
        """
        self._add_software_package_from_regular_scanner(
            self.app_install_1, "Safari", "Apple", "132.0.1", cves=[{"id": "CVE-2023-3333", "severity": "CRITICAL"}]
        )

        self._add_software_package_from_opswat_scanner(
            self.app_install_1, "SAFARI", "Apple Inc.", "132.0.1", cves=[]
        )

        self._refresh_summaries()

        summary = self._get_summary(self.app_install_1)

        # Should only have one record (the OPSWAT one)
        self.assertEqual(summary.count(), 1)
        self.assertEqual(summary.filter(product="SAFARI").count(), 1)
        self.assertEqual(summary.filter(product="Safari").count(), 0)

        # Verify it's the OPSWAT record
        safari = summary.first()
        self.assertEqual(safari.product, "SAFARI")
        self.assertEqual(safari.version, "132.0.1")
        self.assertEqual(safari.vendor, "Apple Inc.")  # OPSWAT vendor, not "Old Vendor"
        self.assertEqual(safari.source, OPSWAT_SOURCE)
        self.assertFalse(safari.is_vulnerable)  # Using OPSWAT vulnerability status
