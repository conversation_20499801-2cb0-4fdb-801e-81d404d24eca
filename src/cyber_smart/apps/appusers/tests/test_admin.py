from unittest import mock

from django.test import TestCase

from appusers.admin import AppFileAdmin
from appusers.appbuild_admin import AppBuildVersionForm
from appusers.models.app_installs import AppFile
from appusers.models.factories import AppFileFactory


class TestAppBuildVersionForm(TestCase):
    def setUp(self):
        AppFile.objects.all().delete()

    @mock.patch('appusers.appbuild_admin.AppBuildVersionForm._update_latest_version_in_s3')
    @mock.patch('appusers.appbuild_admin.AppBuildVersionForm._get_latest_version_from_s3')
    @mock.patch('appusers.appbuild_admin.AppBuildVersionForm._get_builds_from_s3')
    def test_app_build_version_form_creates_new_app_files(self, mock_get_builds_from_s3, mock_get_latest_version_from_s3, mock_update_latest_version_in_s3):
        """
        Test the Admin App Builds page.
        Show that changes on that form, create or update AppFile objects.
        Also show that saving that form updates the latest JSON version in S3,
        which later gets used by the CAP online installers.
        """
        mock_get_builds_from_s3.return_value = {
            'windows': [
                (
                    {
                        'buildDate': '2023/09/08 08:36:46',
                        'build': 984,
                        'version': '5.0.0'
                    },
                    '5.0.0.984 - 2023/09/08 08:36:46'
                ),
                (
                    {
                        'buildDate': '2023/09/08 09:58:40',
                        'build': 988,
                        'version': '5.0.0'
                    },
                    '5.0.0.988 - 2023/09/08 09:58:40'
                )
            ],
            'macos': [
                (
                    {
                        'buildDate': '2023/09/08 08:36:46',
                        'build': 984,
                        'version': '5.0.0'
                    },
                    '5.0.0.984 - 2023/09/08 08:36:46'
                ),
            ]
        }
        mock_get_latest_version_from_s3.return_value = {
            'buildDate': '2024/02/16 14:59:48', 'build': 2024, 'version': '5.0.0'
        }
        form_data = {
            'windows_download_version': {'buildDate': '2023/09/08 08:36:46', 'build': 984, 'version': '5.0.0'},
            'windows_update_version': {'buildDate': '2023/09/08 08:36:46', 'build': 984, 'version': '5.0.0'},
            'macos_download_version': {'buildDate': '2023/09/08 08:36:46', 'build': 984, 'version': '5.0.0'},
            'macos_update_version': {'buildDate': '2023/09/08 08:36:46', 'build': 984, 'version': '5.0.0'}
        }

        self.assertEqual(AppFile.objects.count(), 0)

        form = AppBuildVersionForm(data=form_data)
        self.assertTrue(form.is_valid())
        form.save()

        self.assertEqual(AppFile.objects.count(), 4)

        self.assertTrue(AppFile.objects.filter(**{'type': AppFile.WINDOWS_MSI, 'version': '5.0.0.984', 'is_download_version': True,  'cd_pipeline_version': AppFile.CAP_V5_PIPELINE}).exists())
        self.assertTrue(AppFile.objects.filter(**{'type': AppFile.WINDOWS_MSI, 'version': '5.0.0.984', 'is_download_version': False,  'cd_pipeline_version': AppFile.CAP_V5_PIPELINE}).exists())
        self.assertTrue(AppFile.objects.filter(**{'type': AppFile.MACOS, 'version': '5.0.0.984', 'is_download_version': True,  'cd_pipeline_version': AppFile.CAP_V5_PIPELINE}).exists())
        self.assertTrue(AppFile.objects.filter(**{'type': AppFile.MACOS, 'version': '5.0.0.984', 'is_download_version': False,  'cd_pipeline_version': AppFile.CAP_V5_PIPELINE}).exists())

        self.assertEqual(mock_update_latest_version_in_s3.call_count, 4)
        mock_update_latest_version_in_s3.assert_any_call('windows', {'buildDate': '2023/09/08 08:36:46', 'build': 984, 'version': '5.0.0'}, 'download')

        # now update windows_download_version and save form again
        form_data['windows_download_version'] = {'buildDate': '2023/09/08 09:58:40', 'build': 988, 'version': '5.0.0'}
        form = AppBuildVersionForm(data=form_data)
        self.assertTrue(form.is_valid())
        form.save()

        self.assertEqual(AppFile.objects.count(), 4)
        self.assertTrue(AppFile.objects.filter(**{'type': AppFile.WINDOWS_MSI, 'version': '5.0.0.988', 'is_download_version': True,  'cd_pipeline_version': AppFile.CAP_V5_PIPELINE}).exists())


class TestAppFileAdmin(TestCase):
    def setUp(self):
        AppFile.objects.all().delete()
        self.windows_data = {
            'type': AppFile.WINDOWS_MSI,
            'version': '1.0.0',
            'url': 'https://example.com/app.msi',
            'release_notes': 'Windows MSI release notes',
            'cd_pipeline_version': AppFile.CAP_V4_PIPELINE,
            'is_download_version': True,
            'installation_instructions': '<p>Windows install instructions</p>',
            'installation_instructions_de': '<p>Windows Installationsanweisungen</p>',
            'installation_instructions_fr': '<p>Instructions d\'installation Windows</p>',
        }

        self.trustd_ios_data = {
            'type': AppFile.TRUSTD_IOS,
            'version': '13.0.0',
            'release_notes': 'iOS TrustD version with security updates',
            'cd_pipeline_version': AppFile.CAP_V4_PIPELINE,
            'is_download_version': True,
            'mobile_intune_installation_instructions': '<p>iOS Intune setup instructions</p>',
            'mobile_intune_installation_instructions_sv': '<p>iOS Intune installationsinstruktioner</p>',
            'mobile_mdm_installation_instructions': '<p>iOS MDM configuration steps</p>',
            'mobile_mdm_installation_instructions_fr': '<p>Étapes de configuration iOS MDM</p>',
        }

        self.macos_data = {
            'type': AppFile.MACOS,
            'version': '2.5.1',
            'url': 'https://example.com/app.dmg',
            'release_notes': 'version with bug fixes',
            'cd_pipeline_version': AppFile.CAP_V5_PIPELINE,
            'is_download_version': False,
        }

        self.data = [self.windows_data, self.trustd_ios_data, self.macos_data]

    def test_create_app_files(self):
        created, updated = AppFileAdmin.create_app_files(self.data)

        self.assertEqual(created, 3)
        self.assertEqual(updated, 0)
        self.assertEqual(AppFile.objects.count(), 3)

        windows_app = AppFile.objects.get(type=AppFile.WINDOWS_MSI)
        self.assertEqual(windows_app.version, self.windows_data['version'])
        self.assertEqual(windows_app.url, self.windows_data['url'])
        self.assertEqual(windows_app.release_notes, self.windows_data['release_notes'])
        self.assertEqual(windows_app.cd_pipeline_version, self.windows_data['cd_pipeline_version'])
        self.assertEqual(windows_app.is_download_version, self.windows_data['is_download_version'])
        self.assertEqual(windows_app.installation_instructions, self.windows_data['installation_instructions'])
        self.assertEqual(windows_app.installation_instructions_de, self.windows_data['installation_instructions_de'])
        self.assertEqual(windows_app.installation_instructions_fr, self.windows_data['installation_instructions_fr'])

        macos_app = AppFile.objects.get(type=AppFile.MACOS)
        self.assertEqual(macos_app.version, self.macos_data['version'])
        self.assertEqual(macos_app.url, self.macos_data['url'])
        self.assertEqual(macos_app.release_notes, self.macos_data['release_notes'])
        self.assertEqual(macos_app.cd_pipeline_version, self.macos_data['cd_pipeline_version'])
        self.assertEqual(macos_app.is_download_version, self.macos_data['is_download_version'])

        trustd_app = AppFile.objects.get(type=AppFile.TRUSTD_IOS)
        self.assertEqual(trustd_app.version, self.trustd_ios_data['version'])
        self.assertEqual(trustd_app.release_notes, self.trustd_ios_data['release_notes'])
        self.assertEqual(trustd_app.cd_pipeline_version, self.trustd_ios_data['cd_pipeline_version'])
        self.assertEqual(trustd_app.is_download_version, self.trustd_ios_data['is_download_version'])
        self.assertEqual(trustd_app.mobile_intune_installation_instructions,
                         self.trustd_ios_data['mobile_intune_installation_instructions'])
        self.assertEqual(trustd_app.mobile_intune_installation_instructions_sv,
                         self.trustd_ios_data['mobile_intune_installation_instructions_sv'])
        self.assertEqual(trustd_app.mobile_mdm_installation_instructions,
                         self.trustd_ios_data['mobile_mdm_installation_instructions'])
        self.assertEqual(trustd_app.mobile_mdm_installation_instructions_fr,
                         self.trustd_ios_data['mobile_mdm_installation_instructions_fr'])

    def test_get_app_files(self):
        AppFileAdmin.create_app_files(self.data)
        queryset = AppFile.objects.all().order_by('type')
        result = AppFileAdmin.get_app_files_details(queryset)

        self.assertEqual(len(result), 3)

        windows_data = next(item for item in result if item['type'] == AppFile.WINDOWS_MSI)
        self.assertEqual(windows_data['version'], self.windows_data['version'])
        self.assertEqual(windows_data['url'], self.windows_data['url'])
        self.assertEqual(windows_data['release_notes'], self.windows_data['release_notes'])
        self.assertEqual(windows_data['installation_instructions'], self.windows_data['installation_instructions'])
        self.assertEqual(windows_data['installation_instructions_de'],
                         self.windows_data['installation_instructions_de'])

        trustd_data = next(item for item in result if item['type'] == AppFile.TRUSTD_IOS)
        self.assertEqual(trustd_data['version'], self.trustd_ios_data['version'])
        self.assertEqual(trustd_data['mobile_intune_installation_instructions'],
                         self.trustd_ios_data['mobile_intune_installation_instructions'])
        self.assertEqual(trustd_data['mobile_intune_installation_instructions_sv'],
                         self.trustd_ios_data['mobile_intune_installation_instructions_sv'])
        self.assertEqual(trustd_data['mobile_mdm_installation_instructions'],
                         self.trustd_ios_data['mobile_mdm_installation_instructions'])
        self.assertEqual(trustd_data['mobile_mdm_installation_instructions_fr'],
                         self.trustd_ios_data['mobile_mdm_installation_instructions_fr'])
        for item in result:
            self.assertNotIn('id', item)
            self.assertNotIn('_state', item)
            self.assertNotIn('created', item)
            self.assertNotIn('modified', item)

    def test_create_and_update(self):
        AppFileFactory(
            type=AppFile.MACOS,
            version='1.0.0',
            cd_pipeline_version=AppFile.CAP_V5_PIPELINE,
            is_download_version=False
        )
        data_file_1 = {
            'type': AppFile.MACOS,
            'version': '2.0.0',
            'url': 'https://example.com/updated-macos.dmg',
            'cd_pipeline_version': AppFile.CAP_V5_PIPELINE,
            'is_download_version': False,
            'installation_instructions': '<p>Updated MACOS instructions</p>',
            'installation_instructions_de': '<p>Aktualisierte MACOS Anweisungen</p>',
        }
        data_file_2 = {
            'type': AppFile.TRUSTD_ANDROID,
            'version': '12.5.0',
            'mobile_intune_installation_instructions': '<p>New Intune setup</p>',
            'mobile_mdm_installation_instructions_sv': '<p>Ny MDM-konfiguration</p>',
        }
        mixed_data = [data_file_1, data_file_2]
        created, updated = AppFileAdmin.create_app_files(mixed_data)

        self.assertEqual(created, 1)
        self.assertEqual(updated, 1)
        self.assertEqual(AppFile.objects.count(), 2)
        macos_app = AppFile.objects.get(type=AppFile.MACOS)
        self.assertEqual(macos_app.version, data_file_1['version'])
        self.assertEqual(macos_app.url, data_file_1['url'])
        self.assertEqual(macos_app.cd_pipeline_version, data_file_1['cd_pipeline_version'])
        self.assertEqual(macos_app.is_download_version, data_file_1['is_download_version'])
        self.assertEqual(macos_app.installation_instructions, data_file_1['installation_instructions'])
        self.assertEqual(macos_app.installation_instructions_de, data_file_1['installation_instructions_de'])
        android_app = AppFile.objects.get(type=AppFile.TRUSTD_ANDROID)
        self.assertEqual(android_app.version, data_file_2['version'])
        self.assertEqual(android_app.mobile_intune_installation_instructions,
                         data_file_2['mobile_intune_installation_instructions'])
        self.assertEqual(android_app.mobile_mdm_installation_instructions_sv,
                         data_file_2['mobile_mdm_installation_instructions_sv'])
        self.assertEqual(android_app.cd_pipeline_version, AppFile.CAP_V4_PIPELINE)
        self.assertTrue(android_app.is_download_version)
