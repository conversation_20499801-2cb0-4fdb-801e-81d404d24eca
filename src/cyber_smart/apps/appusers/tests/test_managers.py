from unittest.mock import patch

from django.test import TestCase
from waffle.testutils import override_switch

from appusers.models.app_installs import AppFile, AppInstall, AppVersion
from appusers.models.factories import AppFileFactory, AppInstallFactory, AppUserFactory, OrganisationFactory
from beta_features.models import CAP_V_FIVE_BETA_SWITCH
from beta_features.utils import CAP_V_FIVE_BETA_VERSION, CAP_V_FIVE_BETA_PREFIX
from trustd.factories import TrustdDeviceFactory


class TestAppFileManager(TestCase):
    def setUp(self):
        AppFile.objects.all().delete()
        self.stable_app_file = AppFileFactory(
            version='4.0.1', cd_pipeline_version=AppFile.CAP_V4_PIPELINE)
        self.beta_app_file = AppFileFactory(
            cd_pipeline_version=AppFile.CAP_V5_PIPELINE,
            version=CAP_V_FIVE_BETA_VERSION + '.1')

    def test_get_stable_app_files_flag_not_enabled(self):
        self.assertEqual(AppFile.stable_downloadable_version_objects.count(), 2)

    def test_get_beta_app_files_flag_not_enabled(self):
        self.assertEqual(AppFile.beta_downloadable_version_objects.count(), 0)

    @override_switch(CAP_V_FIVE_BETA_SWITCH, True)
    def test_get_stable_app_files(self):
        self.assertEqual(AppFile.stable_downloadable_version_objects.count(), 2)
        self.assertIn(self.stable_app_file, AppFile.stable_downloadable_version_objects.all())

    @override_switch(CAP_V_FIVE_BETA_SWITCH, True)
    def test_get_beta_app_files(self):
        self.assertEqual(AppFile.beta_downloadable_version_objects.count(), 2)
        self.assertIn(self.beta_app_file, AppFile.beta_downloadable_version_objects.all())


class TestAppInstallDuplicatesManager(TestCase):

    def setUp(self):
        app_user = AppUserFactory()
        self.organisation = app_user.organisation
        # create some app installs with different versions
        self.app_install_v3 = AppInstallFactory(
            hostname='app_install_v3',
            app_version='3.1.2',
            app_user=app_user
        )
        self.app_install_v4 = AppInstallFactory(
            hostname='app_install_v4',
            app_version='4.5.2',
            app_user=app_user,
            device_type=AppInstall.VIRTUAL_SERVER,
            caption='Microsoft Windows Server 2019',
            machine_model='vmware',
        )
        self.app_install_v5 = AppInstallFactory(
            hostname='app_install_v5',
            app_version='5.1.0',
            app_user=AppUserFactory(organisation=self.organisation),
            device_type=AppInstall.VIRTUAL_SERVER,
            caption='Microsoft Windows Server 2019',
            machine_model='vmware',
        )
        self.app_install_v4_2 = AppInstallFactory(
            hostname='app_install_v4_2',
            app_version='4.5.1',
            app_user=AppUserFactory(organisation=self.organisation)
        )
        self.app_install_v5_2 = AppInstallFactory(
            hostname='app_install_v5_2',
            app_version='5.2.6',
            app_user=AppUserFactory(organisation=self.organisation)
        )
        # now create app installs that are considered duplicates
        app_user_2 = AppUserFactory(organisation=self.organisation)
        self.app_install_v4_duplicate = AppInstallFactory(
            hostname='app_install_v4_duplicate',
            app_user=app_user_2,
            app_version='4.5.0',
            device_type=AppInstall.VIRTUAL_DESKTOP,
            machine_model='vmware',
        )
        self.app_install_v5_duplicate = AppInstallFactory(
            hostname='app_install_v5_duplicate',
            app_user=self.app_install_v4_duplicate.app_user,
            device_id=f'{CAP_V_FIVE_BETA_PREFIX}{self.app_install_v4_duplicate.device_id}',
            real_device_id=self.app_install_v4_duplicate.real_device_id,
            serial_number=self.app_install_v4_duplicate.serial_number,
            app_version='5.2.0',
            device_type=AppInstall.VIRTUAL_DESKTOP,
            machine_model='vmware',
        )
        app_user_3 = AppUserFactory(organisation=self.organisation)
        self.app_install_v4_dup_2 = AppInstallFactory(
            hostname='app_install_v4_dup_2',
            app_user=app_user_3,
            app_version='4.5.0'
        )
        self.app_install_v5_dup_2 = AppInstallFactory(
            hostname='app_install_v5_dup_2',
            app_user=self.app_install_v4_dup_2.app_user,
            device_id=f'{CAP_V_FIVE_BETA_PREFIX}{self.app_install_v4_dup_2.device_id}',
            real_device_id=self.app_install_v4_dup_2.real_device_id,
            serial_number=self.app_install_v4_dup_2.serial_number,
            app_version='5.2.0'
        )

        # app installs from another org
        other_org = OrganisationFactory(partner=self.app_install_v4.app_user.organisation.partner)
        other_org_app_user = AppUserFactory(organisation=other_org)
        self.app_install_another_org = AppInstallFactory(
            hostname='app_install_another_org',
            app_version='5.2.6',
            app_user=other_org_app_user,
            device_type=AppInstall.VIRTUAL_DESKTOP,
        )
        self.app_install_another_org_identical_to_other_org = AppInstallFactory(
            real_device_id=self.app_install_v4_duplicate.real_device_id,
            serial_number=self.app_install_v4_duplicate.serial_number,
            hostname=self.app_install_v4.hostname,
            app_version='5.2.1',
            app_user=other_org_app_user,
            device_type=AppInstall.VIRTUAL_DESKTOP,
        )
        self.other_org_app_user_2 = AppUserFactory(organisation=other_org)
        self.app_install_other_org_dupl = AppInstallFactory(
            hostname='other_org_dupl',
            app_version='4.2.0',
            app_user=self.other_org_app_user_2,
            device_type=AppInstall.VIRTUAL_DESKTOP,
        )
        self.app_install_other_org_dupl_new = AppInstallFactory(
            hostname='other_org_dupl_new',
            device_id=f'{CAP_V_FIVE_BETA_PREFIX}{self.app_install_other_org_dupl.device_id}',
            real_device_id=self.app_install_other_org_dupl.real_device_id,
            serial_number=self.app_install_other_org_dupl.serial_number,
            app_version='5.2.0',
            app_user=self.other_org_app_user_2,
            device_type=AppInstall.VIRTUAL_DESKTOP,
        )

        # app install that is mobile
        self.mobile_app_install = AppInstallFactory(
            hostname='mobile_app_install',
            app_version='5.2.6',
            app_user=app_user,
            device_type=AppInstall.MOBILE,
            platform='iOS'
        )

    def test_installs_without_deprecated_duplicates(self):
        org = self.app_install_v4.app_user.organisation
        other_org = self.app_install_another_org.app_user.organisation

        # First test with CAP v5 enabled
        qs = AppInstall.objects.installs_without_deprecated_duplicates_for_organisation(org)
        self.assertEqual(qs.count(), 8)
        self.assertIn(self.app_install_v3, qs)
        self.assertIn(self.app_install_v4, qs)
        self.assertIn(self.app_install_v5, qs)
        self.assertIn(self.app_install_v4_2, qs)
        self.assertIn(self.app_install_v5_2, qs)
        self.assertNotIn(self.app_install_v4_duplicate, qs)
        self.assertIn(self.app_install_v5_duplicate, qs)
        self.assertNotIn(self.app_install_v4_dup_2, qs)
        self.assertIn(self.app_install_v5_dup_2, qs)
        self.assertIn(self.mobile_app_install, qs)
        self.assertNotIn(self.app_install_another_org, qs)

        # Test with CAP v5 disabled - should return all installs for org without removing duplicates
        with patch('appusers.managers.is_cap_v_five_ga_enabled', return_value=False):
            # Check first org
            qs_org1 = AppInstall.objects.installs_without_deprecated_duplicates_for_organisation(org)
            self.assertEqual(qs_org1.count(), 10)
            self.assertIn(self.app_install_v4_duplicate, qs_org1)
            self.assertIn(self.app_install_v4_dup_2, qs_org1)
            self.assertNotIn(self.app_install_another_org, qs_org1)
            self.assertNotIn(self.app_install_other_org_dupl, qs_org1)

            # Check second org
            qs_org2 = AppInstall.objects.installs_without_deprecated_duplicates_for_organisation(other_org)
            self.assertEqual(qs_org2.count(), 4)
            self.assertIn(self.app_install_another_org, qs_org2)
            self.assertIn(self.app_install_another_org_identical_to_other_org, qs_org2)
            self.assertIn(self.app_install_other_org_dupl, qs_org2)
            self.assertIn(self.app_install_other_org_dupl_new, qs_org2)
            self.assertNotIn(self.app_install_v4, qs_org2)
            self.assertNotIn(self.app_install_v5, qs_org2)

            # Check org properties
            self.assertEqual(org.installed_devices.count(), 10)
            self.assertEqual(org.installed_devices_with_deprecated_duplicates.count(), 10)
            self.assertEqual(other_org.installed_devices.count(), 4)
            self.assertEqual(other_org.installed_devices_with_deprecated_duplicates.count(), 4)

    def test_installs_only_deprecated_duplicates(self):
        org = self.app_install_v4.app_user.organisation
        qs = AppInstall.objects.installs_only_deprecated_duplicates_for_organisation(org)
        self.assertEqual(qs.count(), 2)
        self.assertNotIn(self.app_install_v3, qs)
        self.assertNotIn(self.app_install_v4, qs)
        self.assertNotIn(self.app_install_v5, qs)
        self.assertNotIn(self.app_install_v4_2, qs)
        self.assertNotIn(self.app_install_v5_2, qs)
        self.assertIn(self.app_install_v4_duplicate, qs)
        self.assertNotIn(self.app_install_v5_duplicate, qs)
        self.assertIn(self.app_install_v4_dup_2, qs)
        self.assertNotIn(self.app_install_v5_dup_2, qs)
        self.assertNotIn(self.mobile_app_install, qs)
        self.assertNotIn(self.app_install_another_org, qs)

    def test_installs_without_deprecated_duplicates_for_partner(self):
        partner = self.app_install_v4.app_user.organisation.partner
        qs = AppInstall.objects.installs_without_deprecated_duplicates_for_partner(partner)
        self.assertEqual(qs.count(), 11)
        self.assertIn(self.app_install_v3, qs)
        self.assertIn(self.app_install_v4, qs)
        self.assertIn(self.app_install_v5, qs)
        self.assertIn(self.app_install_v4_2, qs)
        self.assertIn(self.app_install_v5_2, qs)
        self.assertNotIn(self.app_install_v4_duplicate, qs)
        self.assertIn(self.app_install_v5_duplicate, qs)
        self.assertNotIn(self.app_install_v4_dup_2, qs)
        self.assertIn(self.app_install_v5_dup_2, qs)
        self.assertIn(self.mobile_app_install, qs)
        self.assertIn(self.app_install_another_org, qs)
        self.assertNotIn(self.app_install_other_org_dupl, qs)
        self.assertIn(self.app_install_other_org_dupl_new, qs)

        # Test the DevicesMetricsMethodsMixin which uses this manager
        self.assertEqual(partner.installed_devices.count(), 11)
        self.assertEqual(partner.installed_devices.count(), qs.count())
        self.assertEqual(partner.installed_devices_with_deprecated_duplicates.count(), 14)

    def test_installs_only_deprecated_duplicates_for_partner(self):
        partner = self.app_install_v4.app_user.organisation.partner
        qs = AppInstall.objects.installs_only_deprecated_duplicates_for_partner(partner)
        self.assertEqual(qs.count(), 3)
        self.assertNotIn(self.app_install_v3, qs)
        self.assertNotIn(self.app_install_v4, qs)
        self.assertNotIn(self.app_install_v5, qs)
        self.assertNotIn(self.app_install_v4_2, qs)
        self.assertNotIn(self.app_install_v5_2, qs)
        self.assertIn(self.app_install_v4_duplicate, qs)
        self.assertNotIn(self.app_install_v5_duplicate, qs)
        self.assertIn(self.app_install_v4_dup_2, qs)
        self.assertNotIn(self.app_install_v5_dup_2, qs)
        self.assertNotIn(self.mobile_app_install, qs)
        self.assertNotIn(self.app_install_another_org, qs)
        self.assertNotIn(self.app_install_another_org_identical_to_other_org, qs)
        self.assertIn(self.app_install_other_org_dupl, qs)
        self.assertNotIn(self.app_install_other_org_dupl_new, qs)

    def test_installs_without_deprecated_duplicates_for_app_user(self):
        app_user = self.other_org_app_user_2
        self.new_mobile_app_install = AppInstallFactory(
            hostname='mobile_app_install',
            app_version='5.2.6',
            app_user=app_user,
            device_type=AppInstall.MOBILE,
            platform='iOS'
        )
        qs = AppInstall.objects.installs_without_deprecated_duplicates_for_app_user(app_user)
        self.assertEqual(qs.count(), 2)
        self.assertNotIn(self.app_install_other_org_dupl, qs)
        self.assertIn(self.app_install_other_org_dupl_new, qs)
        self.assertIn(self.new_mobile_app_install, qs)

        # Test the DevicesMetricsMethodsMixin which uses this manager
        self.assertEqual(app_user.installed_devices.count(), 2)
        self.assertEqual(app_user.installed_devices.count(), qs.count())
        self.assertEqual(app_user.installed_devices_with_deprecated_duplicates.count(), 3)

    def test_active(self):
        AppInstall.objects.all().delete()
        app_user = AppUserFactory()
        app_install = AppInstallFactory(app_user=app_user)
        AppInstallFactory(app_user=app_user)

        # show that the queryset can can be chained
        self.assertEqual(AppInstall.objects.active().installs_without_deprecated_duplicates().count(), 2)
        self.assertEqual(AppInstall.objects.all().count(), 2)

        app_install.inactive = True
        app_install.save()
        self.assertEqual(AppInstall.objects.active().count(), 1)

        app_user.active = False
        app_user.save()
        self.assertEqual(AppInstall.objects.active().count(), 0)

    def test_installs_without_trustd_mobile_duplicates(self):
        # create some mobile app installs that are considered duplicates
        app_user = AppUserFactory(organisation=self.organisation)
        kwargs = {
            'app_user': app_user,
            'device_type': AppInstall.MOBILE,
            'machine_model': 'iPhone 12',
            'machine_vendor': 'Apple',
            'platform': 'ios'
        }
        # a regular app install
        AppInstallFactory(
            app_version='3.1.2',
            **kwargs
        )
        # add app_install__ prefix to kwargs
        new_kwargs = {f'app_install__{k}': v for k, v in kwargs.items()}
        # a duplicate trustd app install
        TrustdDeviceFactory(
            app_install__app_version='2.9.2',
            **new_kwargs
        )
        app_user_2 = AppUserFactory(organisation=self.organisation)
        kwargs = {
            'app_user': app_user_2,
            'device_type': AppInstall.MOBILE,
            'machine_model': 'ONEPLUS A6003',
            'machine_vendor': 'OnePlus',
            'platform': 'android'
        }
        # a regular app install
        AppInstallFactory(
            app_version='3.2',
            **kwargs
        )
        new_kwargs = {f'app_install__{k}': v for k, v in kwargs.items()}
        # a duplicate trustd app install
        TrustdDeviceFactory(
            app_install__app_version='11.0.2',
            **new_kwargs
        )
        # similar app non duplicate
        kwargs = {
            'app_user': app_user_2,
            'device_type': AppInstall.MOBILE,
            'machine_model': '8',
            'machine_vendor': 'Pixel',
            'platform': 'android'
        }
        # a regular app install
        AppInstallFactory(
            app_version='3.2',
            **kwargs
        )
        new_kwargs = {f'app_install__{k}': v for k, v in kwargs.items()}
        new_kwargs['app_install__machine_model'] = '8 pro'
        # non-duplicate trustd app install (it's considered as non-duplicate because machine_model is different)
        TrustdDeviceFactory(
            app_install__app_version='11.0.2',
            **new_kwargs
        )

        installs = AppInstall.objects.active().installs_without_trustd_mobile_duplicates()
        # 14 from setup and 6 created above (2 of them are considered as duplicates)
        self.assertEqual(installs.count(), 18)
        # check app installs linked to trustd devices are kept
        self.assertEqual(
            installs.filter(trustd_device__isnull=False).count(),
            3
        )

    def test_installs_without_trustd_mobile_duplicates_cam_case(self):
        """ Test that the manager method works with different machine_vendor and machine_model casing."""
        # create some mobile app installs that are considered duplicates
        app_user = AppUserFactory(organisation=self.organisation)
        kwargs = {
            'app_user': app_user,
            'device_type': AppInstall.MOBILE,
            'machine_model': 'Pixel 6',
            'machine_vendor': 'Google',
            'platform': 'android'
        }
        # a regular app install
        AppInstallFactory(
            app_version='3.1.2',
            **kwargs
        )
        new_kwargs = {f'app_install__{k}': v for k, v in kwargs.items()}
        # a duplicate trustd app install with lower case machine_vendor
        new_kwargs['app_install__machine_vendor'] = 'google'
        TrustdDeviceFactory(
            app_install__app_version='2.9.2',
            **new_kwargs
        )
        app_user_2 = AppUserFactory(organisation=self.organisation)
        kwargs = {
            'app_user': app_user_2,
            'device_type': AppInstall.MOBILE,
            'machine_model': 'iPhone 12',
            'machine_vendor': 'Apple',
            'platform': 'ios'
        }
        # a regular app install
        AppInstallFactory(
            app_version='3.2',
            **kwargs
        )
        new_kwargs = {f'app_install__{k}': v for k, v in kwargs.items()}
        # a duplicate trustd app install with lower case machine_model
        new_kwargs['app_install__machine_model'] = 'iphone 12'
        # a duplicate trustd app install
        TrustdDeviceFactory(
            app_install__app_version='11.0.2',
            **new_kwargs
        )
        installs = AppInstall.objects.active().installs_without_trustd_mobile_duplicates()
        # 14 from setup and 4 created above (2 of them are considered as duplicate)
        self.assertEqual(installs.count(), 16)
        # check app installs linked to trustd devices are kept
        self.assertEqual(
            installs.filter(trustd_device__isnull=False).count(),
            2
        )

class TestAppInstallDuplicatesManagerForV5PrefixRemoval(TestCase):

    def setUp(self):
        self.app_user = AppUserFactory()
        self.common_data = {
            'app_user': self.app_user,
            'real_device_id': 'device123',
            'serial_number': 'serial123',
            'device_type': AppInstall.DESKTOP,
            'machine_model': 'Model X',
            'machine_vendor': 'Vendor Y',
            'platform': 'windows'
        }

    def test_two_v_five_app_installs(self):
        test_cases = [
            # higher version, lower version
            ('5.11.0', '5.1.0'),
            ('5.12.1', '5.12.0'),
            ('5.10.1', '5.1.81'),
            ('5.12.11', '5.12.1'),
            ('5.0.10', '5.0.9'),
            ('5.10.10','5.9.0'),
            ('6.0.0',  '5.9.9'),
            ('5.2.1',  '4.3.1'),
        ]

        for higher_version, lower_version in test_cases:
            with self.subTest(f"{higher_version} vs {lower_version}"):
                AppInstallFactory(app_version=lower_version, device_id='device123', **self.common_data)
                AppInstallFactory(app_version=higher_version, device_id='V5-device123', **self.common_data)

                installs_without_duplicates = AppInstall.objects.filter(app_user=self.app_user).installs_without_deprecated_duplicates()
                self.assertEqual(installs_without_duplicates.count(), 1)
                self.assertEqual(installs_without_duplicates.first().app_version, higher_version)

                AppInstall.objects.filter(app_user=self.app_user).delete()

    def create_some_more_app_installs(self):
        AppInstallFactory(app_user=self.app_user, app_version='4.10.0')
        AppInstallFactory(app_user=self.app_user, app_version='3.1.0')
        copy_common_data = self.common_data.copy()
        copy_common_data['real_device_id'] = 'device123456'
        # create another dup to make sure it still behaves as expected
        v4_to_skip = AppInstallFactory(app_version='4.3.1', device_id='device123456', **copy_common_data)
        v5_to_keep = AppInstallFactory(app_version='5.2.1', device_id='V5-device123456', **copy_common_data)
        return v4_to_skip, v5_to_keep

    def test_two_v_five_app_installs_with_same_version(self):
        """
        In case of two app installs with the same version, the non-prefixed one should be kept.
        """
        non_prefixed = AppInstallFactory(app_version='5.0.0', device_id='device123', **self.common_data)
        AppInstallFactory(app_version='5.0.0', device_id='V5-device123', **self.common_data)
        installs_without_duplicates = AppInstall.objects.filter(app_user=self.app_user).installs_without_deprecated_duplicates()
        self.assertEqual(installs_without_duplicates.count(), 1)
        self.assertEqual(installs_without_duplicates.first().app_version, non_prefixed.app_version)

    def test_two_v_five_app_installs_with_same_version_more_data(self):
        v4_to_skip, v5_to_keep = self.create_some_more_app_installs()
        non_prefixed = AppInstallFactory(app_version='5.0.0', device_id='device123', **self.common_data)
        prefixed = AppInstallFactory(app_version='5.0.0', device_id='V5-device123', **self.common_data)
        AppInstallFactory(app_user=self.app_user, app_version='4.18.1')
        installs_without_duplicates = AppInstall.objects.filter(app_user=self.app_user).installs_without_deprecated_duplicates()
        ids = installs_without_duplicates.values_list('id', flat=True)
        self.assertEqual(installs_without_duplicates.count(), 5)
        self.assertIn(non_prefixed.id, ids)
        self.assertIn(v5_to_keep.id, ids)
        self.assertNotIn(prefixed.id, ids)
        self.assertNotIn(v4_to_skip.id, ids)

    def test_two_v_five_app_installs_with_same_version_reversed_order(self):
        # Now reverse the order to show ID does not matter
        v4_to_skip, v5_to_keep = self.create_some_more_app_installs()
        prefixed = AppInstallFactory(app_version='5.0.0', device_id='V5-device123', **self.common_data)
        non_prefixed = AppInstallFactory(app_version='5.0.0', device_id='device123', **self.common_data)
        AppInstallFactory(app_user=self.app_user, app_version='4.18.1')
        installs_without_duplicates = AppInstall.objects.filter(app_user=self.app_user).installs_without_deprecated_duplicates()
        ids = installs_without_duplicates.values_list('id', flat=True)
        self.assertEqual(installs_without_duplicates.count(), 5)
        self.assertIn(non_prefixed.id, ids)
        self.assertIn(v5_to_keep.id, ids)
        self.assertNotIn(prefixed.id, ids)
        self.assertNotIn(v4_to_skip.id, ids)


class AppInstallQuerySetTestCase(TestCase):
    """
    Tests for the AppInstallQuerySet manager methods.
    """
    def setUp(self):
        self.active_version = AppVersion.objects.create(
            major=1, minor=0, patch=0,
            support_level=AppVersion.SupportLevel.ACTIVE,
            version_channel=AppVersion.VersionChannel.STABLE
        )
        self.maintenance_version = AppVersion.objects.create(
            major=1, minor=1, patch=0,
            support_level=AppVersion.SupportLevel.MAINTENANCE,
            version_channel=AppVersion.VersionChannel.STABLE
        )
        self.deprecated_version = AppVersion.objects.create(
            major=1, minor=2, patch=0,
            support_level=AppVersion.SupportLevel.DEPRECATED,
            version_channel=AppVersion.VersionChannel.STABLE
        )
        self.end_of_life_version = AppVersion.objects.create(
            major=1, minor=3, patch=0,
            support_level=AppVersion.SupportLevel.END_OF_LIFE,
            version_channel=AppVersion.VersionChannel.STABLE
        )
        self.alpha_version = AppVersion.objects.create(
            major=2, minor=0, patch=0,
            support_level=AppVersion.SupportLevel.ACTIVE,
            version_channel=AppVersion.VersionChannel.ALPHA
        )
        self.beta_version = AppVersion.objects.create(
            major=2, minor=1, patch=0,
            support_level=AppVersion.SupportLevel.ACTIVE,
            version_channel=AppVersion.VersionChannel.BETA
        )
        self.nightly_version = AppVersion.objects.create(
            major=2, minor=2, patch=0,
            support_level=AppVersion.SupportLevel.ACTIVE,
            version_channel=AppVersion.VersionChannel.NIGHTLY
        )

        self.default_version = AppVersion.get_default_version()

        self.active_install = AppInstallFactory(
            app_version=f"{self.active_version.major}.{self.active_version.minor}.{self.active_version.patch}",
            app_user=AppUserFactory()
        )
        self.maintenance_install = AppInstallFactory(
            app_version=f"{self.maintenance_version.major}.{self.maintenance_version.minor}."
                        f"{self.maintenance_version.patch}",
            app_user=AppUserFactory()
        )
        self.deprecated_install = AppInstallFactory(
            app_version=f"{self.deprecated_version.major}.{self.deprecated_version.minor}."
                        f"{self.deprecated_version.patch}",
            app_user=AppUserFactory()
        )
        self.end_of_life_install = AppInstallFactory(
            app_version=f"{self.end_of_life_version.major}.{self.end_of_life_version.minor}."
                        f"{self.end_of_life_version.patch}",
            app_user=AppUserFactory()
        )
        self.alpha_install = AppInstallFactory(
            app_version=f"{self.alpha_version.major}.{self.alpha_version.minor}.{self.alpha_version.patch}",
            app_user=AppUserFactory()
        )
        self.beta_install = AppInstallFactory(
            app_version=f"{self.beta_version.major}.{self.beta_version.minor}.{self.beta_version.patch}",
            app_user=AppUserFactory()
        )
        self.nightly_install = AppInstallFactory(
            app_version=f"{self.nightly_version.major}.{self.nightly_version.minor}.{self.nightly_version.patch}",
            app_user=AppUserFactory()
        )
        self.default_install = AppInstallFactory(
            app_version=f"{self.default_version.major}.{self.default_version.minor}.{self.default_version.patch}",
            app_user=AppUserFactory()
        )

    def test_by_support_level(self):
        active_installs = AppInstall.versions.by_support_level(AppVersion.SupportLevel.ACTIVE)
        self.assertIn(self.active_install, active_installs)
        self.assertIn(self.alpha_install, active_installs)
        self.assertIn(self.beta_install, active_installs)
        self.assertIn(self.nightly_install, active_installs)
        self.assertNotIn(self.maintenance_install, active_installs)
        self.assertNotIn(self.deprecated_install, active_installs)
        self.assertNotIn(self.end_of_life_install, active_installs)

    def test_by_version_channel(self):
        stable_installs = AppInstall.versions.by_version_channel(AppVersion.VersionChannel.STABLE)
        self.assertIn(self.active_install, stable_installs)
        self.assertIn(self.maintenance_install, stable_installs)
        self.assertIn(self.deprecated_install, stable_installs)
        self.assertIn(self.end_of_life_install, stable_installs)
        self.assertNotIn(self.alpha_install, stable_installs)
        self.assertNotIn(self.beta_install, stable_installs)
        self.assertNotIn(self.nightly_install, stable_installs)

    def test_active(self):
        active_installs = AppInstall.versions.active()
        self.assertIn(self.active_install, active_installs)
        self.assertIn(self.alpha_install, active_installs)
        self.assertIn(self.beta_install, active_installs)
        self.assertIn(self.nightly_install, active_installs)
        self.assertNotIn(self.maintenance_install, active_installs)
        self.assertNotIn(self.deprecated_install, active_installs)
        self.assertNotIn(self.end_of_life_install, active_installs)

    def test_maintenance(self):
        maintenance_installs = AppInstall.versions.maintenance()
        self.assertIn(self.maintenance_install, maintenance_installs)
        self.assertNotIn(self.active_install, maintenance_installs)
        self.assertNotIn(self.deprecated_install, maintenance_installs)
        self.assertNotIn(self.end_of_life_install, maintenance_installs)

    def test_deprecated(self):
        deprecated_installs = AppInstall.versions.deprecated()
        self.assertIn(self.deprecated_install, deprecated_installs)
        self.assertNotIn(self.active_install, deprecated_installs)
        self.assertNotIn(self.maintenance_install, deprecated_installs)
        self.assertNotIn(self.end_of_life_install, deprecated_installs)

    def test_end_of_life(self):
        end_of_life_installs = AppInstall.versions.end_of_life()
        self.assertIn(self.end_of_life_install, end_of_life_installs)
        self.assertNotIn(self.active_install, end_of_life_installs)
        self.assertNotIn(self.maintenance_install, end_of_life_installs)
        self.assertNotIn(self.deprecated_install, end_of_life_installs)

    def test_alpha(self):
        alpha_installs = AppInstall.versions.alpha()
        self.assertIn(self.alpha_install, alpha_installs)
        self.assertNotIn(self.beta_install, alpha_installs)
        self.assertNotIn(self.nightly_install, alpha_installs)
        self.assertNotIn(self.active_install, alpha_installs)
        self.assertNotIn(self.maintenance_install, alpha_installs)
        self.assertNotIn(self.deprecated_install, alpha_installs)
        self.assertNotIn(self.end_of_life_install, alpha_installs)

    def test_beta(self):
        beta_installs = AppInstall.versions.beta()
        self.assertIn(self.beta_install, beta_installs)
        self.assertNotIn(self.alpha_install, beta_installs)
        self.assertNotIn(self.nightly_install, beta_installs)
        self.assertNotIn(self.active_install, beta_installs)
        self.assertNotIn(self.maintenance_install, beta_installs)
        self.assertNotIn(self.deprecated_install, beta_installs)
        self.assertNotIn(self.end_of_life_install, beta_installs)

    def test_nightly(self):
        nightly_installs = AppInstall.versions.nightly()
        self.assertIn(self.nightly_install, nightly_installs)
        self.assertNotIn(self.alpha_install, nightly_installs)
        self.assertNotIn(self.beta_install, nightly_installs)
        self.assertNotIn(self.active_install, nightly_installs)
        self.assertNotIn(self.maintenance_install, nightly_installs)
        self.assertNotIn(self.deprecated_install, nightly_installs)
        self.assertNotIn(self.end_of_life_install, nightly_installs)

    def test_stable(self):
        stable_installs = AppInstall.versions.stable()
        self.assertIn(self.active_install, stable_installs)
        self.assertIn(self.maintenance_install, stable_installs)
        self.assertIn(self.deprecated_install, stable_installs)
        self.assertIn(self.end_of_life_install, stable_installs)
        self.assertNotIn(self.alpha_install, stable_installs)
        self.assertNotIn(self.beta_install, stable_installs)
        self.assertNotIn(self.nightly_install, stable_installs)

    def test_unidentified(self):
        unidentified_installs = AppInstall.versions.unidentified()
        self.assertIn(self.default_install, unidentified_installs)
        self.assertNotIn(self.active_install, unidentified_installs)
        self.assertNotIn(self.maintenance_install, unidentified_installs)
        self.assertNotIn(self.deprecated_install, unidentified_installs)
        self.assertNotIn(self.end_of_life_install, unidentified_installs)
        self.assertNotIn(self.alpha_install, unidentified_installs)
        self.assertNotIn(self.beta_install, unidentified_installs)
        self.assertNotIn(self.nightly_install, unidentified_installs)
