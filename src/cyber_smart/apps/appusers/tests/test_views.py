from datetime import datetime
from mock import patch

from django.test import TestCase
from django.contrib.auth.models import User

from appusers.models.factories import AppInstallFactory
from appusers.tests.helpers import SoftwarePackageHelperTestMixin
from opswat.factories import ProductSignatureFactory
from opswat.models import InstalledProductVersion
from opswat_patch.factories import OpswatPatchInstallerFactory, OpswatProductPatchInstallerFactory
from organisations.factories import OrganisationFactory, OrganisationAdminFactory


class PackageDetailsViewTestCase(TestCase, SoftwarePackageHelperTestMixin):
    def setUp(self):
        self.organisation = OrganisationFactory()
        self.user = User.objects.create_superuser("admin", "<EMAIL>", "password")
        OrganisationAdminFactory(organisation=self.organisation, user=self.user)

        self.app_install = AppInstallFactory(app_user__organisation=self.organisation)
        self.app_install_packages = {
            "pycharm": ("PyCharm", "JetBrains", "3.4.1", "2023-11-24 12:45:00"),
            "evernote": ("Evernote", "Evernote Corporation", "10.0.0", "2023-11-24 12:45:00"),
        }

        for package_id, (product, vendor, version, patch_detected_date) in self.app_install_packages.items():
            product_version = self._add_software_package_from_opswat_scanner(
                self.app_install,
                product=product,
                vendor=vendor,
                version=version,
                cves=[],
            )

            self.signature = ProductSignatureFactory(
                product=product_version.product,
                name="Product Signature",
            )
            self.patch_installer = OpswatPatchInstallerFactory(
                latest_version="101.0.0"
            )
            self.product_patch_installer = OpswatProductPatchInstallerFactory(
                product=product_version.product,
                patch_installer=self.patch_installer,
                title="Product Patch Installer",
            )
            self.product_patch_installer.signatures.add(self.signature)

            installed_product_version = InstalledProductVersion.objects.get(
                product_version=product_version,
            )

            installed_product_version.signature = self.signature
            installed_product_version.save()

        self._refresh_summaries()

        self.client.login(username="admin", password="password")

    def test_patch_detected_date_in_template(self):
        for package in self.app_install_packages.values():
            patch_detection_date = datetime.fromisoformat(package[3])
            with patch("opswat_patch.utils.calculate_enrich.get_patch_detected_date", return_value=patch_detection_date):
                package_id = self._get_summary(self.app_install).filter(product=package[0]).first().id
                url = self.app_install.url_package_details(package_id=package_id)

                response = self.client.get(url)
                self.assertEqual(response.status_code, 200)

                # "24 Nov 2023, 12:45 p.m."
                expected_patch_detection_date = patch_detection_date.strftime("%d %b %Y, %I:%M p.m.")
                self.assertIn(
                    expected_patch_detection_date, response.content.decode(), "patch_detected_date not found in response"
                )
