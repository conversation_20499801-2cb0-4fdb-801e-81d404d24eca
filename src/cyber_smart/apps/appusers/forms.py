import random
import string
import uuid

import shortuuid
from django import forms
from django.forms import BaseFormSet
from django.forms.models import formset_factory
from django.urls import resolve
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from appusers.models import AppUser, AppInstall
from common.models import Group
from common.utils import handle_user_input
from dashboard.utils import set_dashboard_admin
from rulebook.models import OperatingSystem


class AppUserForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        # extract request from kwargs before form initialization
        self.request = kwargs.pop('request', None)
        self.organisation = kwargs.pop("organisation", None)
        super().__init__(*args, **kwargs)
        if self.groups_are_available:
            self.fields["groups"] = forms.ModelChoiceField(
                label=_('Group'), queryset=Group.objects.all().order_by('name'), required=False,
                widget=forms.Select(attrs={'class': 'form-control'}),
                help_text=_('You can select specific group. Not required')
            )
            if self.request:
                organisation_secure_id = resolve(self.request.get_full_path()).kwargs['org_id']
                self.fields["groups"].queryset = Group.objects.filter(
                    organisation__secure_id=organisation_secure_id
                ).order_by('name')

            self.fields["groups"].empty_label = _('Unassigned')

    request = None

    first_name = forms.CharField(
        label=_('First name'),
        max_length=255,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control first_nameCL',
            'placeholder': _('First Name'),
            'required': 'true'
        })
    )
    last_name = forms.CharField(
        label=_('Last name'),
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control last_nameCL',
            'placeholder': _('Last Name')
        })
    )
    email = forms.EmailField(
        label=_('Email'),
        widget=forms.EmailInput(attrs={
            'class': 'form-control emailCL',
            'placeholder': _('Email'),
            'required': 'true',
            'spellcheck': 'false',
            'data-mail': 'true'
        }),
        error_messages={'invalid': _('Enter a valid email address')}
    )
    is_social = forms.BooleanField(
        widget=forms.HiddenInput(), label='', required=False, initial=False
    )
    # when the user chooses to enrol/sync/add this user to the organisation
    sync_user = forms.BooleanField(
        widget=forms.HiddenInput(), label='', required=False, initial=False
    )

    class Meta:
        model = AppUser
        fields = ['first_name', 'last_name', 'email', 'is_admin', 'is_social']
        widgets = {
            'is_admin': forms.CheckboxInput(attrs={
                'class': 'js-switch',
                'spellcheck': 'false',
            }),
        }

    @property
    def groups_are_available(self) -> bool:
        """
        Check if groups are available for this form.
        """
        return self.organisation and all([
            not self.organisation.is_bulk_enrollment_type,
            not self.organisation.is_certificates_only
        ])

    def _get_or_create_app_user(self, organisation, is_admin):
        app_user, created = AppUser.objects.get_or_create(
            organisation=organisation,
            email=handle_user_input(self.cleaned_data['email']),
            defaults={
                "first_name": handle_user_input(self.cleaned_data['first_name']),
                "last_name": handle_user_input(self.cleaned_data.get('last_name', '')),
                "is_admin": is_admin
            }
        )

        if self.groups_are_available:
            if created and self.cleaned_data.get("groups"):
                groups = Group.objects.filter(
                    name=handle_user_input(self.cleaned_data['groups']),
                    organisation=organisation)

                app_user.groups.set(groups)

        return app_user, created

    def _get_app_user(self, organisation):
        return AppUser.objects.filter(
            organisation=organisation,
            email=handle_user_input(self.cleaned_data['email']),
        ).first()

    def update_data(self, view, organisation=None):
        if not self.cleaned_data:
            return None, False

        organisation = organisation if organisation else view.request.user.profile.get_organisations.first()
        is_admin = self.cleaned_data.get('is_admin')
        if not organisation.has_software_support:
            # for cert only organisations added user will be always dashboard admin
            # since they don't use applications
            is_admin = True

        if self.cleaned_data.get('is_social', False) and hasattr(organisation, 'organisationusersync'):
            # if user decided to remove this user from the social account imports
            if not self.cleaned_data.get('sync_user', False):
                app_user = self._get_app_user(organisation)
                # update sync users data to deactivate this user
                organisation.organisationusersync.update_user_data(self.cleaned_data['email'], active=False)
                if app_user:
                    # set this user's app user as inactive
                    app_user.active = False
                    app_user.save()
                    # deactivate app installs for this user
                    app_user.installs.update(inactive=True, modified=timezone.now())
                return None, False
            # if user wants to add/re-add this user from the social account to the org
            else:
                organisation.organisationusersync.set_user_as_enrolled(self.cleaned_data['email'])

        app_user, created = self._get_or_create_app_user(organisation, is_admin)
        if not created and app_user.active:
            # Skip existing users
            return None, False

        if not app_user.active:
            # if app user was removed then make it active again and update its info
            app_user.active = True
            app_user.first_name = handle_user_input(self.cleaned_data['first_name'])
            app_user.last_name = handle_user_input(self.cleaned_data.get('last_name', ''))

            groups = Group.objects.filter(
                name=handle_user_input(self.cleaned_data['groups']),
                organisation=organisation
            )

            app_user.groups.set(groups)

            app_user.is_admin = is_admin
            app_user.save()

        if is_admin:
            set_dashboard_admin(app_user, view.request.user)
        return app_user.uuid, True


class AppUserEnrollForm(AppUserForm):
    def __init__(self, *args, **kwargs):
        super(AppUserEnrollForm, self).__init__(*args, **kwargs)
        # had to comment it out because user's sync depends on it also is not required field
        # del self.fields['is_social']


AppUserEnrollFormset = formset_factory(AppUserEnrollForm, can_delete=False, extra=1, max_num=100)


class FakeAppInstallForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # initialize style for all fields
        for field in self.fields:
            self.fields[field].widget.attrs['class'] = 'form-control'

        random_id = ''.join(random.choices(string.ascii_uppercase + string.digits, k=7))

        self.fields["os"].choices = OperatingSystem.objects.all().values_list("pk", "title")
        self.fields["hostname"].initial = "Host {0}".format(random_id)
        self.fields["caption"].initial = "OS {0}".format(random_id)
        self.fields["platform"].initial = "win32"
        self.fields["platform"].help_text = "ios, android, win32, darwin"
        self.fields["release"].initial = "1.1.1"
        self.fields["os_release"].initial = "1.1.1"
        self.fields["os_info"].initial = None
        self.fields["base_os"].initial = "win"
        self.fields["security_patch"].initial = "12.06.2020"
        self.fields["device_id"].initial = uuid.uuid4()
        self.fields["serial_number"].initial = shortuuid.uuid()
        self.fields["installer"].initial = "msi"
        self.fields["inactive"].initial = False
        self.fields["date_removed"].initial = None
        self.fields["app_version"].initial = "4.0.4"
        self.fields["machine_vendor"].initial = "Vendor {0}".format(random_id)
        self.fields["machine_model"].initial = "Model {0}".format(random_id)
        self.fields["system_info"].initial = ""
        self.fields["device_type"].initial = AppInstall.DESKTOP
        self.fields["fake_report"].initial = True
        self.fields["osuser_username"].initial = "TestUser"
        self.fields["osuser_domain"].initial = "DOMAIN"
        self.fields["osuser_is_admin_account"].initial = False

    app_user_id = forms.UUIDField(widget=forms.HiddenInput(), label="", required=False)
    fake_report = forms.BooleanField(label="Generate fake report", required=False)
    osuser_username = forms.CharField(label="OS User - username", required=False)
    osuser_domain = forms.CharField(label="OS User - domain", required=False)
    osuser_is_admin_account = forms.BooleanField(label="OS User is admin", required=False)
    os = forms.ChoiceField()

    class Meta:
        model = AppInstall
        fields = [
            "os", "hostname", "caption", "platform", "release", "os_release", "os_info", "base_os",
            "security_patch", "device_id", "serial_number", "installer", "inactive", "date_removed",
            "app_version", "machine_vendor", "machine_model", "system_info", "device_type"
        ]

    def clean(self):
        self.cleaned_data["os"] = OperatingSystem.objects.get(pk=self.cleaned_data["os"])
        return super().clean()


class BaseAppUserFormSet(BaseFormSet):

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request')
        super().__init__(*args, **kwargs)

    def _construct_form(self, i, **kwargs):
        """have to pass organization to form"""
        kwargs['request'] = self.request
        return super()._construct_form(i, **kwargs)
