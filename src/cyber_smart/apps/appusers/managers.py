import waffle
from django.db import models
from django.db.models import OuterR<PERSON>, F, Subquery, Max, Case, When, Value, IntegerField
from django.db.models.functions import Lower

from beta_features.models import TRUSTD_LA_SWITCH
from beta_features.utils import (
    get_app_file_beta_q_object, CAP_V_FIVE_BETA_SWITCH, is_cap_v_five_ga_enabled,
    is_trustd_mobile_ga_enabled, CAP_V_FIVE_BETA_PREFIX
)
from common.mixins import DevicesMetricsMethodsMixin
from common.models import CRUDManager, CRUDQuerySet
from distributors.models import Distributor


class StableDownloadableAppFileManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_download_version=True).exclude(get_app_file_beta_q_object())


class BetaDownloadableAppFileManager(models.Manager):
    def get_queryset(self):
        cap_v5_beta_enabled = waffle.switch_is_active(CAP_V_FIVE_BETA_SWITCH)
        if not cap_v5_beta_enabled:
            return super().get_queryset().none()
        return super().get_queryset().filter(is_download_version=True).filter(get_app_file_beta_q_object())


class AppOSInstalledSoftwareManager(models.Manager):
    """
    Manager with added method to return all vulnerable OS InstalledSoftware objects.
    Vulnerable software is counted as software with at least one CVE
    that is present in the CVERepository.
    """

    def vulnerable_objects(self, report_id=None):
        qs = super().get_queryset()
        if report_id:
            qs = qs.filter(report_id=report_id)
        return qs.filter(software__related_cves__cves_fk__isnull=False).distinct()


class AppInstallDuplicatesQuerySet(CRUDQuerySet):
    def _installs_only_deprecated_duplicates_base(self, org_id=None, partner_id=None):
        """ This should return only the deprecated AppInstalls which are duplicates.
        Example:
            (app_user=2, real_device_id=2, serial_number=2, app_version=v4.5)
            (app_user=1, real_device_id=1, serial_number=1, app_version=v4.5)
            (app_user=1, real_device_id=1, serial_number=1, app_version=v5.2)
            (app_user=3, real_device_id=3, serial_number=3, app_version=v5.2)
        This should return:
            (app_user=1, real_device_id=1, serial_number=1, app_version=v4.5)
        """
        from appusers.models import AppInstall
        qs = self
        if org_id:
            qs = qs.filter(app_user__organisation_id=org_id)
        elif partner_id:
            qs = qs.filter(app_user__organisation__partner_id=partner_id)
        # Subquery that gets the maximum app_version for each group of duplicates
        subquery = AppInstall.objects.filter(
            app_user=OuterRef('app_user'),
            real_device_id=OuterRef('real_device_id'),
            serial_number=OuterRef('serial_number'),
            app_user__organisation_id=OuterRef('app_user__organisation_id'),
        ).annotate(
            is_v5_prefixed=Case(
                When(device_id__startswith=CAP_V_FIVE_BETA_PREFIX, then=Value(1)),
                default=Value(0),
                output_field=IntegerField()
            )
        ).order_by('-version__major', '-version__minor', '-version__patch', 'is_v5_prefixed', 'id')[:1]
        # Annotate each AppInstall with the maximum app_version within its group of duplicates
        qs = qs.annotate(
            max_version_major=Subquery(subquery.values("version__major")),
            max_version_minor=Subquery(subquery.values("version__minor")),
            max_version_patch=Subquery(subquery.values("version__patch")),
            max_app_version=Subquery(subquery.values("app_version")),
        )
        # Exclude AppInstalls that are not duplicates or are the highest version
        qs = qs.exclude(
            version__major=F('max_version_major'),
            version__minor=F('max_version_minor'),
            version__patch=F('max_version_patch')
        )
        return qs

    def installs_only_deprecated_duplicates_for_organisation(self, org_id):
        return self._installs_only_deprecated_duplicates_base(
            org_id=org_id
        )
    def installs_only_deprecated_duplicates_for_partner(self, partner_id):
        return self._installs_only_deprecated_duplicates_base(
            partner_id=partner_id
        )

    def active(self):
        return self.filter(inactive=False, app_user__active=True)

    def installs_without_deprecated_duplicates(self, **kwargs):
        # Apply the basic filters regardless of CAP v5 status
        extra_filter_kwargs = {}
        if DevicesMetricsMethodsMixin.ORGANISATION in kwargs:
            extra_filter_kwargs = {'app_user__organisation_id': kwargs[DevicesMetricsMethodsMixin.ORGANISATION].id}
        elif DevicesMetricsMethodsMixin.PARTNER in kwargs:
            extra_filter_kwargs = {'app_user__organisation__partner_id': kwargs[DevicesMetricsMethodsMixin.PARTNER].id}
        elif DevicesMetricsMethodsMixin.DISTRIBUTOR in kwargs:
            extra_filter_kwargs = {'app_user__organisation__partner__distributor_id': kwargs[DevicesMetricsMethodsMixin.DISTRIBUTOR].id}
        elif DevicesMetricsMethodsMixin.APP_USER in kwargs:
            extra_filter_kwargs = {'app_user_id': kwargs[DevicesMetricsMethodsMixin.APP_USER].id}

        qs = self.filter(**extra_filter_kwargs)

        # Only apply the duplicate filtering logic if CAP v5 is enabled
        if not is_cap_v_five_ga_enabled():
            return qs

        base_args = ('app_user', 'real_device_id', 'serial_number')
        qs = qs.annotate(
            is_v5_prefixed=Case(
                When(device_id__startswith=CAP_V_FIVE_BETA_PREFIX, then=Value(1)),
                default=Value(0),
                output_field=IntegerField()
            )
        ).order_by(
            *base_args,
            '-version__major',
            '-version__minor',
            '-version__patch',
            'is_v5_prefixed',
            'id'
        ).distinct(*base_args)
        return qs

    def installs_without_trustd_mobile_duplicates(self):
        """ Exclude duplicate mobile app installs (devices with CAP mobile and Trustd app installed).
        Mobile duplicates are considered to have:
            * the same app user with the same machine_vendor, machine_model and platform
        """
        if not is_trustd_mobile_ga_enabled() and not self.active().filter(
                app_user__organisation__beta_enrolment__feature__kind=TRUSTD_LA_SWITCH
        ).exists():
            # Trustd Mobile GA switch is not enabled, and the organisation is not in Trustd Mobile Limited Availability
            return self


        from appusers.models.mixins import DeviceTypeMixin

        latest_installs = self.active().filter(
            device_type=DeviceTypeMixin.MOBILE
        ).annotate(
            lower_machine_vendor=Lower('machine_vendor'),
            lower_machine_model=Lower('machine_model'),
        ).values(
            "app_user", "lower_machine_vendor", "lower_machine_model", "platform"
        ).annotate(
            latest_id=Max("id")
        ).values_list("latest_id", flat=True)

        # exclude installs that are not the latest for their respective combinations.
        # we need to keep the latest app installs because they represent the current state (trustd),
        # while the rest are considered legacy mobile installs that we want to filter out.
        return self.exclude(
            id__in=self.filter(
                device_type=DeviceTypeMixin.MOBILE
            ).exclude(
                id__in=latest_installs
            ).exclude(
                machine_vendor__isnull=True,
                machine_model__isnull=True,
            ).values_list("id", flat=True)
        )


class AppInstallDuplicatesManager(CRUDManager):
    """
    Manager to handle duplicate AppInstalls.
    A duplicate is defined as a AppInstall with the same app_user, serial_number and real_device_id.
    It is basically a device with CAP V4 and CAP V5 installed for the same user.

    For mobile, internal app and Trustd app have different device_id's, so this manager simply passes these
    records through not deduplicating any devices.
    """

    def get_queryset(self):
        return AppInstallDuplicatesQuerySet(self.model, using=self._db)

    def active(self):
        return self.get_queryset().active()

    def installs_without_deprecated_duplicates(self, **kwargs):
        return self.get_queryset().installs_without_deprecated_duplicates(**kwargs)

    def installs_without_deprecated_duplicates_for_organisation(self, organisation):
        """ This should return all AppInstalls without the deprecated (CAP V4) duplicates.
        So it includes AppInstalls that are V4 only OR V5 only AND the V5 AppInstalls that have a V4 AppInstall.
        Example:
            (app_user=2, real_device_id=2, serial_number=2, app_version=v4.5)
            (app_user=1, real_device_id=1, serial_number=1, app_version=v4.5)
            (app_user=1, real_device_id=1, serial_number=1, app_version=v5.2)
            (app_user=3, real_device_id=3, serial_number=3, app_version=v5.2)
        This should return:
            (app_user=2, real_device_id=2, serial_number=2, app_version=v4.5)
            (app_user=1, real_device_id=1, serial_number=1, app_version=v5.2)
            (app_user=3, real_device_id=3, serial_number=3, app_version=v5.2)
        """
        return self.installs_without_deprecated_duplicates(
            **{DevicesMetricsMethodsMixin.ORGANISATION: organisation}
        )

    def installs_without_deprecated_duplicates_for_partner(self, partner):
        return self.installs_without_deprecated_duplicates(
            **{DevicesMetricsMethodsMixin.PARTNER: partner}
        )

    def installs_without_deprecated_duplicates_for_app_user(self, app_user):
        return self.installs_without_deprecated_duplicates(
            **{DevicesMetricsMethodsMixin.APP_USER: app_user}
        )

    def installs_without_deprecated_duplicates_for_distributor(self, distributor: Distributor) -> AppInstallDuplicatesQuerySet:
        return self.installs_without_deprecated_duplicates(
            **{DevicesMetricsMethodsMixin.DISTRIBUTOR: distributor}
        )

    def installs_only_deprecated_duplicates_for_organisation(self, org_id):
        return self.get_queryset().installs_only_deprecated_duplicates_for_organisation(org_id)

    def installs_only_deprecated_duplicates_for_partner(self, partner_id):
        return self.get_queryset().installs_only_deprecated_duplicates_for_partner(partner_id)
