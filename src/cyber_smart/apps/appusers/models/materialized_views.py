import logging

from django_pgviews import view as pg
from django.db.utils import ConnectionRouter
from django.db import models
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.conf import settings

from vulnerabilities.utils import OPSWAT_SOURCE, REGULAR_SOURCE

# Note: if you change any of the names of these views or add new ones, you must also update the CACHALOT_UNCACHABLE_TABLES in cs_settings.py
# Django cachalot does not know about when these views are updated, so we exclude them from caching.

logger = logging.getLogger(__name__)
router = ConnectionRouter()


class MaterializedViewWithCustomConnection(pg.MaterializedView):
    """
    A custom materialized view class used primarily to increase the work_mem value
    for materialized view refresh operations.
    """
    @classmethod
    def get_materialized_view_work_mem(cls):
        """
        By observing EXPLAIN ANALYZE on the below materialized views,
        we observed a lot of disk spills (disk I/O) for the default work_mem of 4MB.
        Therefore, we increase the work_mem before performing the materialized view refresh,
        as having more memory helps with sorting and grouping operations.
        """
        if settings.IS_PROD and settings.IS_UK_GEO:
            return '1024MB'
        elif settings.IS_STAGE:
            return '100MB'
        else:
            return '20MB'


    @classmethod
    def refresh(cls, concurrently=False):
        conn = cls.get_view_connection(using=router.db_for_write(cls), restricted_mode=False)
        if not conn:
            logger.warning("Failed to find connection to refresh %s", cls)
            return

        cursor_wrapper = conn.cursor()
        cursor = cursor_wrapper.cursor
        work_mem = cls.get_materialized_view_work_mem()
        try:
            if cls._concurrent_index is not None and concurrently:
                # Use SET (session-level) because CONCURRENTLY cannot be in a transaction
                cursor.execute(f"SET work_mem = '{work_mem}';")
                logger.debug("Set work_mem to %s for CONCURRENT refresh: %s", work_mem, cls._meta.db_table)
                cursor.execute(f"REFRESH MATERIALIZED VIEW CONCURRENTLY {cls._meta.db_table}")
            else:
                # Use SET LOCAL (transaction-level) if possible
                cursor.execute(f"SET LOCAL work_mem = '{work_mem}';")
                logger.debug("Set LOCAL work_mem to %s for standard refresh: %s", work_mem, cls._meta.db_table)
                cursor.execute(f"REFRESH MATERIALIZED VIEW {cls._meta.db_table}")
        finally:
            cursor_wrapper.close()

    class Meta:
        abstract = True
        managed = False


class InstalledSoftwareAbstract(MaterializedViewWithCustomConnection):
    """
    An abstract model to store the installed software.

    The logic for this SQL query is as follows:
    Device with Both OPSWAT and Regular Scanner Data
        Packages: Both OPSWAT and Regular Scanner packages should be displayed.
        Vulnerabilities: Only OPSWAT vulnerabilities should be displayed.

    Device with Only Regular Scanner Data
        Packages: Regular Scanner packages should be displayed.
        Vulnerabilities: Regular Scanner vulnerabilities should be displayed.

    Device with Only OPSWAT Data
        Packages: OPSWAT packages should be displayed.
        Vulnerabilities: OPSWAT vulnerabilities should be displayed.

    Merging Logic
        A unique product is recognized by its `product name` and `version` on app_install level.
        If a product exists in both OPSWAT and Regular Scanner data for the same app_install,
        the OPSWAT record is preferred. No deduplication is done across app_installs.

    Expected Behavior
        When both OPSWAT and Regular Scanner data are available for an app_install,
        vulnerabilities from the Regular Scanner should be set to is_vulnerable=False.
        Packages should always be included from both sources, even if vulnerabilities are filtered.
    """
    concurrent_index: str
    with_data: bool = True

    # Composite key fields
    source = models.CharField(
        verbose_name="Source System", max_length=10, choices=[(REGULAR_SOURCE, "Regular"), (OPSWAT_SOURCE, "OPSWAT")]
    )
    source_id = models.CharField(verbose_name="Source ID", max_length=255)
    # we must set a primary key for django ORM - composite of app_install_id, source and source_id
    id = models.CharField(verbose_name='Composite ID', max_length=266, primary_key=True)

    vendor = models.CharField(verbose_name="Product Vendor", max_length=255, null=True, blank=True)
    product = models.CharField(verbose_name="Product Name", db_index=True, max_length=255)
    version = models.CharField(verbose_name="Product Version", max_length=255)

    mobile_app = models.BooleanField(verbose_name="Wrapped iOS mobile app for M1 macOS", default=False)
    is_vulnerable = models.BooleanField(verbose_name="Is Vulnerable", default=False)
    freq = models.PositiveIntegerField(verbose_name="Frequency", default=0)
    cve_count = models.PositiveIntegerField(verbose_name="CVE Count", default=0)
    highest_severity = models.FloatField(verbose_name="Highest Severity" )
    signatures = ArrayField(
        base_field=models.IntegerField(),
        verbose_name="Product Signatures", default=list, blank=True,
                                help_text="Array of signature IDs associated with this software")

    class Meta:
        abstract = True

    def __str__(self):
        return f'({self.source}:{self.source_id}, {self.product}, {self.vendor})'


class InstalledSoftwarePartnerSummary(InstalledSoftwareAbstract):
    """
    A materialized view to store the installed software summary for each partner.
    """
    concurrent_index = "partner_id, product, version"
    # below composite id = f"{partner_id}:{source}:{source_id}" to reflect the index above

    partner = models.ForeignKey(
        "partners.Partner", related_name="installed_software_summary", on_delete=models.DO_NOTHING
    )
    sql = """
WITH active_installs AS (          /* keep only active / non-inactive once */
    SELECT
        ai.id               AS app_install_id,
        org.partner_id
    FROM   appusers_appinstall      ai
    JOIN   appusers_appuser         au   ON au.id  = ai.app_user_id
    JOIN   organisations_organisation org ON org.id = au.organisation_id
    WHERE  au.active
      AND  NOT ai.inactive
),

/* OPSWAT vulnerability facts (set-based) */
opswat_cve_stats AS (
    SELECT
        cpv.productversion_id,
        COUNT(*)                    ::int      AS cve_count,
        MAX(cve.severity_index / 10.0)::float  AS highest_severity,
        TRUE                                    AS is_vulnerable
    FROM   opswat_cve_product_version cpv
    JOIN   opswat_cve                cve ON cve.id = cpv.cve_id
    GROUP  BY cpv.productversion_id
),

/* Regular vulnerability facts (set-based) */
regular_cve_stats AS (
    SELECT
        sp.id                                      AS software_id,
        COUNT(cver_repo.id)            ::int       AS cve_count,
        MAX(cver_repo.base_score)::float   AS highest_severity,
        TRUE                                       AS is_vulnerable
    FROM   appusers_softwarepackagecves               spc
    JOIN   appusers_softwarepackagecvescverepository  spcr
           ON spcr.software_package_cves_id = spc.id
    JOIN   vulnerabilities_cverepository              cver_repo
           ON cver_repo.id = spcr.cve_repository_id
    JOIN   appusers_softwarepackage                   sp
           ON sp.id = spc.software_id
    GROUP  BY sp.id
),

/* OPSWAT rows (deduped) */
opswat_rows AS (
    SELECT
        ai.app_install_id,
        'opswat'                                    AS source,
        pv.id::varchar                              AS source_id,
        (ai.partner_id || ':opswat:' || pv.id)::varchar     AS id,
        pvdr.name                                   AS vendor,
        p.name                                      AS product,
        LOWER(p.name)                               AS product_lc,
        pv.raw_version                              AS version,
        LOWER(pv.raw_version)                       AS version_lc,
        FALSE                                       AS mobile_app,
        1                                           AS freq,
        COALESCE(cs.is_vulnerable, FALSE)           AS is_vulnerable,
        COALESCE(cs.cve_count, 0)                   AS cve_count,
        COALESCE(cs.highest_severity, 0)            AS highest_severity,
        ARRAY_AGG(DISTINCT CASE WHEN ippv.signature_id IS NOT NULL THEN ippv.signature_id::integer ELSE NULL END) FILTER (WHERE ippv.signature_id IS NOT NULL) AS signatures,
        ai.partner_id
    FROM   active_installs            ai
    JOIN   opswat_installedproduct    ip   ON ip.app_install_id     = ai.app_install_id
    JOIN   opswat_installedproductversion ippv
           ON ippv.installed_product_id = ip.id
    JOIN   opswat_productversion      pv   ON pv.id                 = ippv.product_version_id
    JOIN   opswat_product             p    ON p.id                  = pv.product_id
    JOIN   opswat_productvendor       pvdr ON pvdr.id               = p.vendor_id
    LEFT   JOIN opswat_cve_stats      cs   ON cs.productversion_id = pv.id
    GROUP  BY ai.app_install_id,
             pv.id, pvdr.name, p.name, pv.raw_version,
             cs.is_vulnerable, cs.cve_count, cs.highest_severity,
             ai.partner_id
),

opswat_exists AS (                 /* flag per-install */
    SELECT DISTINCT app_install_id FROM opswat_rows
),

/* Regular rows (deduped, collision-filtered) */
regular_rows AS (
    SELECT
        ai.app_install_id,
        'regular'                                  AS source,
        sp.id::varchar                             AS source_id,
        (ai.partner_id || ':regular:' || sp.id)::varchar      AS id,
        sp.vendor                                  AS vendor,
        sp.product                                 AS product,
        LOWER(sp.product)                          AS product_lc,
        sp.version                                 AS version,
        LOWER(sp.version)                          AS version_lc,
        sp.mobile_app                              AS mobile_app,
        1                                          AS freq,
        CASE WHEN oe.app_install_id IS NOT NULL
             THEN FALSE
             ELSE COALESCE(rcs.is_vulnerable, FALSE)
        END                                        AS is_vulnerable,
        CASE WHEN oe.app_install_id IS NOT NULL
             THEN 0
             ELSE COALESCE(rcs.cve_count, 0)
        END                                        AS cve_count,
        CASE WHEN oe.app_install_id IS NOT NULL
             THEN 0
             ELSE COALESCE(rcs.highest_severity, 0)
        END                                        AS highest_severity,
        ARRAY[]::integer[]                         AS signatures,
        ai.partner_id
    FROM   active_installs               ai
    JOIN   appusers_appreport            ar   ON ar.app_install_id = ai.app_install_id
    JOIN   appusers_apposinstalledsoftware aos ON aos.report_id    = ar.id
    JOIN   appusers_softwarepackage      sp   ON sp.id             = aos.software_id
    LEFT   JOIN opswat_exists            oe   ON oe.app_install_id = ai.app_install_id
    LEFT   JOIN regular_cve_stats        rcs  ON rcs.software_id   = sp.id
    /* exclude product+version combos already present in OPSWAT for the same install */
    WHERE  NOT EXISTS (
              SELECT 1
              FROM   opswat_rows orw
              WHERE  orw.app_install_id = ai.app_install_id
                AND  orw.product_lc     = LOWER(sp.product)
                AND  orw.version_lc     = LOWER(sp.version)
           )
    GROUP  BY ai.app_install_id,
             sp.id, sp.vendor, sp.product, sp.version, sp.mobile_app,
             oe.app_install_id,
             rcs.is_vulnerable, rcs.cve_count, rcs.highest_severity,
             ai.partner_id
),

combined AS (                      /* unioned set */
    SELECT * FROM opswat_rows
    UNION ALL
    SELECT * FROM regular_rows
)

/* final partner-level aggregation */
SELECT
    STRING_AGG(DISTINCT id,          ',')             AS id,
    partner_id,
    MIN(vendor)                                       AS vendor,
    MIN(product)                                      AS product,
    MIN(version)                                      AS version,
    COUNT(*)                                          AS freq,
    STRING_AGG(DISTINCT source,    ',')               AS source,
    STRING_AGG(DISTINCT source_id, ',')               AS source_id,
    BOOL_OR(mobile_app)                               AS mobile_app,
    CASE
        WHEN BOOL_OR(source = 'opswat')
             THEN MAX(CASE WHEN source = 'opswat'
                           THEN highest_severity
                           ELSE 0 END)
        ELSE MAX(highest_severity)
    END                                               AS highest_severity,
    CASE
        WHEN BOOL_OR(source = 'opswat')
             THEN SUM(CASE WHEN source = 'opswat'
                           THEN cve_count
                           ELSE 0 END)
        ELSE SUM(cve_count)
    END                                               AS cve_count,
    CASE
        WHEN BOOL_OR(source = 'opswat')
             THEN BOOL_OR(CASE WHEN source = 'opswat'
                               THEN is_vulnerable
                               ELSE FALSE END)
        ELSE BOOL_OR(is_vulnerable)
    END                                               AS is_vulnerable,
    coalesce(min(signatures) filter(where signatures <> '{}'), '{}') as signatures
FROM   combined
GROUP  BY
    partner_id,
    product_lc,
    version_lc;

"""

    class Meta:
        app_label = "appusers"
        db_table = "appusers_installedsoftwarepartnersummary"
        managed = False
        verbose_name = "Installed Software Summary [Partner Level]"
        verbose_name_plural = "Installed Software Summaries [Partner Level]"

    def __str__(self):
        return f"({self.id}) {self.vendor}, {self.product}, {self.version}, CVEs: {self.cve_count}"
