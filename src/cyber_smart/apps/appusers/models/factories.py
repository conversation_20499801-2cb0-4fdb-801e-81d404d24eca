import uuid
import random

import factory
from factory.django import DjangoModelFactory, mute_signals

from appusers.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckResult, InstalledSoftwareAppInstallIndividual
from appusers.models.app_installs import (
    AppUser, AppInstall, AppInstallOSUser, AppInstallPolicyAgreement, AppOSInstalledSoftware,
    SoftwarePackage, SoftwarePackageCVEs, AppFile, AppInvitation, DisableSendMailUser,
    UserLoginHistory, AppInstallCheckStatus, AppVersion, OSEndOfLife
)
from appusers.utils import generate_app_registration_token, get_app_version
from common.signals import model_crud
from organisations.factories import OrganisationPolicyVersionFactory, OrganisationFactory
from rulebook.factories import OperatingSystemFactory
from appusers.utils import PLATFORM_DARWIN, PLATFORM_WIN32, PLATFORM_IOS, PLATFORM_ANDROID
from vulnerabilities.utils import OPSWAT_SOURCE


def _generate_non_beta_version_with_opswat_support():
    """
    Generate a random app version between 5.5.0 and 5.5.5 to
    support OPSWAT patching functionality (>= 5.5.0)
    """
    major = 5
    minor = 5
    patch = random.randint(0, 5)
    return f"{major}.{minor}.{patch}"


@mute_signals(model_crud)
class AppUserFactory(DjangoModelFactory):
    class Meta:
        model = AppUser

    email = factory.Sequence(lambda _id: '%<EMAIL>' % _id)
    organisation = factory.SubFactory(OrganisationFactory)
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')


class AppVersionFactory(DjangoModelFactory):
    class Meta:
        model = AppVersion


def get_platform_from_caption(caption: str) -> str:
    if not caption:
        return PLATFORM_WIN32
    if 'Windows' in caption:
        return PLATFORM_WIN32
    elif 'macOS' in caption:
        return PLATFORM_DARWIN
    elif 'iOS' in caption:
        return PLATFORM_IOS
    elif 'Android' in caption:
        return PLATFORM_ANDROID
    return PLATFORM_WIN32

def get_random_os_release(o) -> str:
    if not o.caption:
        return ''
    if 'macOS' in o.caption:
        return random.choice(['10.15.7', '11.7.10', '12.7.2', '13.6.3', '14.2.1'])
    elif 'Windows' in o.caption:
        return random.choice(['21H2', '22H2', '23H2'])
    elif 'iOS' in o.caption:
        return random.choice(['17.2.1', '17.3', '16.7.2', '16.6'])
    elif 'Android' in o.caption:
        return random.choice(['14.0', '13.0', '12.1'])
    return ''

def get_random_release(o) -> str:
    if not o.caption:
        return ''
    if 'macOS' in o.caption:
        return random.choice(['12', '13', '14', '15', '16', '17'])
    elif 'Windows' in o.caption:
        return random.choice(['10.0.22631', '10.0.22000', '10.0.22621'])
    elif 'iOS' in o.caption:
        return random.choice(['17.0', '16.0', '15.0'])
    elif 'Android' in o.caption:
        return random.choice(['14', '13', '12'])
    return ''


@mute_signals(model_crud)
class AppInstallFactory(DjangoModelFactory):
    class Meta:
        model = AppInstall

    hostname = factory.Iterator(['Host Name', 'Workstation-Alpha', 'Node-42', 'Device-XYZ'])
    caption = factory.Faker('random_element', elements=[
        # Windows
        'Microsoft Windows 10 Pro', 'Microsoft Windows 11 Pro',
        # macOS
        'macOS Sonoma', 'macOS Ventura', 'macOS Monterey',
    ])
    platform = factory.LazyAttribute(lambda o: get_platform_from_caption(o.caption))
    release = factory.LazyAttribute(get_random_release)

    app_version = factory.LazyAttribute(
        lambda _: _generate_non_beta_version_with_opswat_support())
    device_type = AppInstall.DESKTOP

    version = factory.LazyAttribute(lambda _: get_app_version(_.app_version, AppInstall.DESKTOP))
    device_id = factory.LazyFunction(lambda: str(uuid.uuid4()))
    os = factory.SubFactory(OperatingSystemFactory)
    registration_unique_token = factory.Sequence(lambda _: generate_app_registration_token())
    serial_number = factory.LazyAttribute(
        lambda _: factory.Faker('lexify', text='?' * 10).evaluate(None, None, {'locale': None}) if factory.Faker('boolean', chance_of_getting_true=90).evaluate(None, None, {'locale': None}) else ''
    )
    os_release = factory.LazyAttribute(
        get_random_os_release
    )
    app_user = factory.SubFactory(AppUserFactory)

    @classmethod
    def create_with_report(cls, **kwargs) -> AppInstall:
        """
        Create an AppInstall with an AppReport.
        """
        app_install = cls.create(**kwargs)
        AppReportFactory.create(app_install=app_install)
        return app_install


@mute_signals(model_crud)
class AppInstallPolicyAgreementFactory(DjangoModelFactory):
    version = factory.SubFactory(OrganisationPolicyVersionFactory)
    app_install = factory.SubFactory(
        AppInstallFactory,
        app_user=factory.SubFactory(AppUserFactory)
    )

    class Meta:
        model = AppInstallPolicyAgreement


class AppReportFactory(DjangoModelFactory):
    app_install = factory.SubFactory(AppInstallFactory)
    app_version = '1.0.0'
    username = 'testuser'
    domain = 'testdomain'
    total_commands = 3
    total_responses = 3
    total_passed_responses = 2
    total_failed_responses = 1
    total_manual_fix = 0
    total_manual_fix_left = 0
    total_passed = 2
    total_failed = 1
    pass_percentage = 66.0

    class Meta:
        model = AppReport


class SoftwarePackageFactory(DjangoModelFactory):
    vendor = 'google'
    product = factory.Sequence(lambda _id: f'chrome {_id}')
    platform = 'darwin'
    version = '1.0.0'
    mobile_app = False

    class Meta:
        model = SoftwarePackage


class AppOSInstalledSoftwareFactory(DjangoModelFactory):
    report = factory.SubFactory(AppReportFactory)
    software = factory.SubFactory(SoftwarePackageFactory)
    date_installed = '2020-01-01'

    class Meta:
        model = AppOSInstalledSoftware


class SoftwarePackageCVEsFactory(DjangoModelFactory):
    software = factory.SubFactory(SoftwarePackageFactory)
    cves = ['CVE-2020-1234']

    class Meta:
        model = SoftwarePackageCVEs


class AppFileFactory(DjangoModelFactory):
    type = AppFile.WINDOWS_MSI

    class Meta:
        model = AppFile


class DisableSendMailUserFactory(DjangoModelFactory):
    class Meta:
        model = DisableSendMailUser


class AppInvitationFactory(DjangoModelFactory):
    app_user = factory.SubFactory('appusers.factories.AppUserFactory')
    app_type = AppInvitation.APP_TYPES.DESKTOP

    class Meta:
        model = AppInvitation


class AppInstallOSUserFactory(DjangoModelFactory):
    username = factory.Sequence(lambda _id: f'username-{_id}')
    class Meta:
        model = AppInstallOSUser


class UserLoginHistoryFactory(DjangoModelFactory):
    class Meta:
        model = UserLoginHistory


class CheckResultFactory(DjangoModelFactory):
    class Meta:

        model = CheckResult


class AppInstallCheckStatusFactory(DjangoModelFactory):
    class Meta:
        model = AppInstallCheckStatus

    app_install = factory.SubFactory(AppInstallFactory)
    app_check = factory.SubFactory(CheckResultFactory)


class OSEndOfLifeFactory(DjangoModelFactory):

    class Meta:
        model = OSEndOfLife


class InstalledSoftwareAppInstallIndividualFactory(DjangoModelFactory):
    app_install = factory.SubFactory(AppInstallFactory)
    id  = factory.LazyAttribute(lambda o: f"{o.app_install.id}:{o.source}:{o.source_id}")
    source = OPSWAT_SOURCE
    source_id = factory.Sequence(lambda n: f'source_id_{n}')
    vendor = factory.Faker('company')
    product = factory.Faker('word')
    version = factory.Faker('numerify', text='1.0.##')
    product_lc = factory.LazyAttribute(lambda o: o.product.lower())
    version_lc = factory.LazyAttribute(lambda o: o.version.lower())

    class Meta:
        model = InstalledSoftwareAppInstallIndividual
