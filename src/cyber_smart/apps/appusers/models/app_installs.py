import datetime
import urllib
import uuid
from typing import Optional, Self
from urllib import parse as urlparse
from urllib.parse import urljoin

import numpy as np
from appusers.managers import (
    AppInstallDuplicatesManager,
    AppOSInstalledSoftwareManager,
    BetaDownloadableAppFileManager,
    StableDownloadableAppFileManager,
)
from beta_features.mixins import BetaDevicesMetricsMethodsMixin
from beta_features.utils import (
    CAP_V_FIVE_BETA_PREFIX,
    CAP_V_FIVE_BETA_VERSION,
    get_beta_app_install_queryset_filter,
    get_real_device_id,
    is_cap_v_five_ga_enabled,
    is_trustd_mobile_available_for_organisation,
)
from citext.fields import C<PERSON>mail<PERSON>ield
from ckeditor_uploader.fields import RichTextUploadingField
from common.mixins import DevicesMetricsMethodsMixin
from common.models import (
    BigPkAbstract,
    CRUDSignalMixin,
    Group,
    SecretPair,
    SemanticVersionMixin,
)
from common.signals import model_crud
from common.upload_path_creator import upload_file_securely
from common.utils import (
    create_secret_pair,
    get_full_name_or_email,
    get_http_protocol_url_name,
)
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.postgres.fields import ArrayField
from django.contrib.sites.models import Site
from django.db import models
from django.db.models import Index, Q, QuerySet
from django.db.models.functions import Lower
from django.db.utils import ProgrammingError
from django.dispatch import receiver
from django.forms.models import model_to_dict
from django.urls import reverse
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.http import urlsafe_base64_encode
from django.utils.translation import gettext_lazy
from django.utils.translation import gettext_lazy as _
from model_utils import Choices, FieldTracker
from model_utils import fields as model_utils_fields
from model_utils.models import TimeStampedModel
from operating_systems.models import OSEndOfLife
from organisations.models import Organisation, OrganisationPolicyVersion
from rulebook.models import CYBER_ESSENTIALS, AppCheck, Command, OperatingSystem
from versionfield import Version

from .managers import AppInstallVersionManager, UserPolicyAgreementManager
from .mixins import DeviceTypeMixin, DeviceUrlMixin


class AppUser(TimeStampedModel, DevicesMetricsMethodsMixin, BetaDevicesMetricsMethodsMixin):
    """
    This model represents application user.
    Think of application user as a real human who holds a phone in their hands with CyberSmart app installed and
    also have a PC/Mac at home with CyberSmart app.
    An organisation can have multiple app users.
    App users can have multiple app installs.
    """
    # app_users
    STATUS = Choices(
        (0, 'LMS_NOT_ENROLLED', _('LMS Not Enrolled')),
        (10, 'LMS_ENROLLED', _('LMS Enrolled')),
        (20, 'LMS_SKIPPED_ENROLLMENT', _('LMS Skipped Enrollment')),
    )

    organisation = models.ForeignKey(Organisation, related_name='app_users', on_delete=models.CASCADE)
    groups = models.ManyToManyField(Group, related_name='app_users', blank=True)
    uuid = models.UUIDField(unique=True, default=uuid.uuid4, editable=False)
    email = CIEmailField(db_index=True)
    first_name = models.CharField(max_length=125, blank=True)
    last_name = models.CharField(max_length=125, blank=True)
    is_admin = models.BooleanField(default=False, verbose_name=_("Dashboard Admin"))
    report = models.FileField(upload_to='app_user_report/', null=True, blank=True)
    active = models.BooleanField(default=True)
    health_check_user = models.BooleanField(verbose_name="HealthCheck user", default=False)
    lms_enrollment_status = models.PositiveSmallIntegerField(choices=STATUS, default=STATUS.LMS_NOT_ENROLLED)
    lms_enrollment_status_changed = model_utils_fields.MonitorField(monitor='lms_enrollment_status')

    class Meta:
        verbose_name = 'App User'
        verbose_name_plural = 'App Users'
        unique_together = ['organisation', 'email']
        indexes = [
            Index(fields=['id'], condition=Q(active=True), name='idx_appuser_active_id'),
        ]

    def __unicode__(self):
        return "[{}] - {}".format(self.organisation.secure_id, self.email)

    def __str__(self):
        return self.__unicode__()

    @property
    def is_bulk_deploy_user(self):
        return self.organisation.bulk_deploy_user == self

    @property
    def main_entity_filter_name(self) -> str:
        """
        Returns the name of the main entity filter for DevicesMetricsMethodsMixin.
        """
        return DevicesMetricsMethodsMixin.APP_USER

    @property
    def get_full_name(self) -> str:
        """
        Returns full name of the user.
        """
        return f"{self.first_name} {self.last_name}" if self.first_name or self.last_name else ""

    def get_download_url(self, extra_url_kwargs=None):
        """
        Returns full path with domain name to the download page.
        :return: url
        :rtype: str
        """
        if self.organisation.bulk_install:
            uuid = self.organisation.main_app_user.uuid
        else:
            uuid = self.uuid

        url = urlparse.urljoin(
            '{0}://{1}'.format(get_http_protocol_url_name(), Site.objects.get_current().domain),
            reverse('dashboard:download-links', kwargs={'app_user_uuid': uuid})
        )
        if extra_url_kwargs:
            url = f"{url}?{urlparse.urlencode(extra_url_kwargs)}"
        return url

    @property
    def edit_url(self) -> str:
        """
        Returns edit url.
        """
        return reverse("api-v2:app-user", kwargs={"org_id": self.organisation.secure_id, "uuid": self.uuid})

    @property
    def legacy_edit_url(self) -> str:
        """
        Returns legacy edit url.
        """
        return reverse("api:dashboard:manage-users", kwargs={"pk": self.pk, "org_id": self.organisation.secure_id})

    def get_absolute_deep_link_url(self, key=None):
        """
        Returns full path with domain name to the mobile application deep link.
        :arg key: secret key to build fallback URL
                in case the email click does not open CAP in the device (e.g. desktop)
        :type key: str or None
        :return: url
        :rtype: str
        """
        return self.get_deep_link_url(self.uuid, key)

    def get_group_name(self):
        if self.groups.exists():
            return self.groups.all().first().name
        else:
            return _('Unassigned')

    @classmethod
    def get_deep_link_url(cls, app_user_uuid, key=None):
        """
        Returns full path with domain name to the mobile application deep link.
        :return: url
        :rtype: str
        """
        website_domain = Site.objects.get_current().domain
        fallback_url = ''
        # fallback url is only used when creating the link for the first time,
        # if the email click does not open CAP in the device it should redirect to QR code page.
        if key:
            fallback_url = urllib.parse.quote(
                f'{get_http_protocol_url_name()}://{website_domain}/qr-code/?secret_key={key}',
                safe=''
            )
        # branch.io deep links
        if settings.IS_PROD:
            return f'https://mobile.cybersmart.co.uk/install?' \
                   f'$deeplink_path=uuid/{app_user_uuid}/url/{website_domain}/&' \
                   f'$desktop_url={fallback_url}'
        else:
            return f'https://cybersmart.test-app.link/install?' \
                   f'$deeplink_path=uuid/{app_user_uuid}/url/{website_domain}/&' \
                   f'$desktop_url={fallback_url}'

    def get_temporary_absolute_deep_link(self):
        """
        Gets a temporary depp link.
        It merely creates a secret to then be retrieved in order to check
        if this deep link has been used or has expired.
        """
        # delete previous secret if it exists
        SecretPair.objects.filter(value=str(self.uuid)).delete()
        key = create_secret_pair(value=str(self.uuid), lifetime=datetime.timedelta(days=1))
        return self.get_absolute_deep_link_url(key)

    @property
    def disable_resend_email(self):
        if self.disablesendmail_appusers.all().count():
            return True
        else:
            return False

    @property
    def pass_percentage(self):
        """
        Returns app user pass percentage.
        :return: pass percentage
        :rtype: float
        """
        from appusers.models import AppInstall, AppReport

        pass_percentages_list = (
            AppReport.objects.filter(
                app_install__app_user=self,
                app_install__inactive=False,
                total_responses__isnull=False,
            )
            .exclude(
                app_install__in=AppInstall.objects.filter(
                    get_beta_app_install_queryset_filter()
                )
            )
            .order_by("app_install", "-modified")
            .distinct("app_install")
            .values_list("pass_percentage", flat=True)
        )

        if pass_percentages_list.exists():
            try:
                list_of_percent = np.array(pass_percentages_list)
                non_none_list_of_percent = list_of_percent[list_of_percent != np.array(None)]
                return non_none_list_of_percent.mean()
            except ZeroDivisionError:
                return 0.0
        else:
            return 0.0

    @property
    def most_recent_check_in(self):
        installs = self.installed_devices.filter(reports__isnull=False, inactive=False)
        if installs:
            installs = AppInstall.objects.filter(id__in=installs.values_list('id', flat=True))
            return installs.order_by('reports__created').values('reports__created') \
                .last().get('reports__created')
        return None

    def get_smart_policies_groups_filter(
            self, assigned_filter_name: str = "groups__in", unassigned_filter_name: str = "groups__isnull"
    ) -> dict:
        """
        Return a filter that should be used every time we need to get smart policies assigned to the app user.
        :param assigned_filter_name: Name of the filter that should be used to get assigned policies
        :param unassigned_filter_name: Name of the filter that should be used to get unassigned policies
        """
        if groups := self.groups.values_list("id", flat=True):
            return {assigned_filter_name: groups}
        return {unassigned_filter_name: True}

    def active_policies(self) -> QuerySet[OrganisationPolicyVersion]:
        """
        Returns active policies (last their versions).
        """
        filters = {
            "policy__active": True,
            "active": True,
            "main": True,
            "policy__organisation": self.organisation
        }

        return OrganisationPolicyVersion.objects.filter(
            **filters | self.get_smart_policies_groups_filter(
                assigned_filter_name="policy__groups__in",
                unassigned_filter_name="policy__groups__isnull"
            )
        ).select_related("policy")

    @property
    def active_policies_count(self) -> int:
        """
        Return the number of active policies.
        """
        return self.active_policies().count()

    def agreed_policies(self) -> QuerySet["AppInstallPolicyAgreement"]:
        """
        Return app user agreed policies.
        This method considers agreed policies per app user not per app install and not per logged user.
        It includes inactive app installs, as the policy agreements are still valid even after deactivation.
        """
        filters = {
            "app_install__app_user": self,
            "agreed_date__isnull": False,
            "version__policy__active": True,
            "version__active": True,
            "version__main": True,
            "version__policy__organisation": self.organisation
        }
        return AppInstallPolicyAgreement.objects.filter(
            **filters | self.get_smart_policies_groups_filter(
                assigned_filter_name="version__policy__groups__in",
                unassigned_filter_name="version__policy__groups__isnull"
            )
        ).order_by(
            'version__policy', 'app_install__app_user'
        ).distinct('version__policy', 'app_install__app_user').select_related("version__policy", "app_install")

    @property
    def agreed_policies_count(self) -> int:
        """
        Return the number of agreed policies per app user.
        This method considers agreed policies per app user not per app install and not per logged user.
        """
        return self.agreed_policies().count()

    def read_policies(self, exclude_agreed: bool = False) -> QuerySet["AppInstallPolicyAgreement"]:
        """
        Return app user read policies.
        This method considers read policies per app user not per app install and not per logged user.
        It includes inactive app installs, as the policy has still been read, even after deactivation.
        :param exclude_agreed: Excludes agreed policies (agreed policies are always read as well)
        :type exclude_agreed: bool
        """
        filters = {
            'app_install__app_user': self,
            'read_date__isnull': False,
            'version__policy__active': True,
            'version__active': True,
            'version__main': True,
            'version__policy__organisation': self.organisation
        }
        if exclude_agreed:
            filters["agreed_date__isnull"] = True

        return AppInstallPolicyAgreement.objects.filter(
            **filters | self.get_smart_policies_groups_filter(
                assigned_filter_name="version__policy__groups__in",
                unassigned_filter_name="version__policy__groups__isnull"
            )
        ).order_by(
            'version__policy', 'app_install__app_user'
        ).distinct('version__policy', 'app_install__app_user').select_related("version__policy", "app_install")

    @property
    def read_policies_count(self) -> int:
        """
        Return the number of read policies per app user.
        This method considers read policies per app user not per app install and not per logged user.
        :return: Amount of read policies
        :rtype: int
        """
        return self.read_policies().count()

    def pending_policies(self, exclude_read=True) -> QuerySet[OrganisationPolicyVersion]:
        """
        Return pending policies per app user.
        This method considers pending policies per app user not per app install and not per logged user.
        """
        versions_pk = self.organisation.get_latest_policy_versions().values_list('pk', flat=True)
        exclude_pks = self.agreed_policies().values_list(
            'version__pk', flat=True
        )
        if exclude_read:
            exclude_pks = exclude_pks | self.read_policies(exclude_agreed=True).values_list(
                'version__pk', flat=True
            )
        filters = {
            'pk__in': versions_pk,
        }
        return OrganisationPolicyVersion.objects.filter(
            **filters | self.get_smart_policies_groups_filter(
                assigned_filter_name="policy__groups__in",
                unassigned_filter_name="policy__groups__isnull"
            )
        ).exclude(
            pk__in=exclude_pks
        ).select_related('policy')

    @property
    def pending_policies_count(self) -> int:
        """
        Return the number of pending policies per app user.
        This method considers pending policies per app user not per app install and not per logged user.
        """
        return self.pending_policies().count()

    def update_policies_counters(self) -> None:
        """
        Update policy counters.
        """
        analytics = getattr(self, "analytics", None)
        if analytics:
            # store a copy of the current data
            saved_analytics = model_to_dict(analytics)
            # get the latest data
            analytics.active_policies_count = self.active_policies().count()
            analytics.agreed_policies_count = self.agreed_policies().count()
            analytics.read_policies_count = self.read_policies().count()
            # check if changed and save fields
            if saved_analytics != model_to_dict(analytics):
                analytics.save(update_fields=[
                    "active_policies_count", "agreed_policies_count", "read_policies_count", "modified"
                ])

    @property
    def active_installs(self):
        """
        Returns app user's active installs.
        :return: active installs
        """
        return self.raw_installed_devices.filter(
            inactive=False
        )

    @property
    def active_installs_prefetched_organisation(self):
        return self.active_installs.select_related("app_user__organisation")

    @property
    def active_non_mobile_installs(self):
        """
        Returns app user's active non-mobile installs.
        :return: active non-mobile installs
        """
        return self.raw_installed_devices.filter(
            inactive=False,
        ).exclude(
            device_type=AppInstall.MOBILE
        )

    @property
    def active_mobile_installs(self):
        """
        Returns app user's active mobile installs.
        :return: active mobile installs
        """
        return self.raw_installed_devices.filter(
            inactive=False,
            device_type=AppInstall.MOBILE
        )


    def set_to_lms_enrolled(self):
        self.lms_enrollment_status = self.STATUS.LMS_ENROLLED
        self.save()

    @property
    def skipped_lms_enrollment(self):
        return self.lms_enrollment_status == self.STATUS.LMS_SKIPPED_ENROLLMENT

    @property
    def last_checked_in_device(self) -> Optional["AppInstall"]:
        """
        Returns last checked in device.
        """
        return self.installs.filter(
            id__in=self.installed_devices,
            reports__isnull=False
        ).order_by(
            "reports__created"
        ).last()

    @property
    def is_dashboard_admin(self):
        """
        Returns True if user is dashboard admin.
        """
        return get_user_model().objects.filter(email=self.email).exists()


class AppFile(TimeStampedModel):
    """
    In this model we store urls or files of CyberSmart Active Protect (CAP) application builds.
    Users are using these urls to download the app and install it.
    """

    UNKNOWN_VERSION = _(" unknown version")

    WINDOWS_MSI = 1
    MACOS = 2
    ANDROID_LEGACY = 3
    IOS_LEGACY = 4
    TRUSTD_ANDROID = 5
    TRUSTD_IOS = 6

    DESKTOP = [WINDOWS_MSI, MACOS]
    MOBILE = [ANDROID_LEGACY, IOS_LEGACY, TRUSTD_ANDROID, TRUSTD_IOS]
    TRUSTD = [TRUSTD_ANDROID, TRUSTD_IOS]

    TYPES = (
        (WINDOWS_MSI, "Windows MSI"),
        (MACOS, "MACOS"),
        (ANDROID_LEGACY, "Android Legacy"),
        (IOS_LEGACY, "iOS Legacy"),
        (TRUSTD_ANDROID, "Trustd Android"),
        (TRUSTD_IOS, "Trustd iOS"),
    )

    type = models.SmallIntegerField(verbose_name="App type", choices=TYPES)
    version = models.CharField(max_length=125)
    url = models.URLField(verbose_name="Absolute URL to the file", max_length=2000, null=True, blank=True)
    file = models.FileField(
        verbose_name="Application file", upload_to=upload_file_securely, max_length=255, blank=True, null=True
    )
    release_notes = models.TextField(blank=True, help_text='Release notes for current CAP version')

    CAP_V4_PIPELINE = 4
    CAP_V5_PIPELINE = 5
    PIPELINE_VERSIONS = (
        (CAP_V4_PIPELINE, "CAP_V4_PIPILINE"),
        (CAP_V5_PIPELINE, "CAP_V5_PIPELINE"),
    )
    cd_pipeline_version = models.SmallIntegerField(
        verbose_name="Continuous Delivery Pipeline Version",
        choices=PIPELINE_VERSIONS, default=CAP_V4_PIPELINE,
        help_text="There are differences between pipeline versions in the way they are fetching latest builds from S3. All builds >= 5 should have CAP_V5."
    )
    is_download_version = models.BooleanField(
        default=True,
        help_text="If the file is downloadable, it is shown in the user interface. Non-downloadable app files are the ones that are automatically upgraded on the device using OS application updates."
    )
    installation_instructions = RichTextUploadingField(
        verbose_name="Installation instructions", null=True, blank=True, default=None,
        config_name='html_only',
        help_text="Html text that will be rendered on the bulk installation page."
    )
    mobile_intune_installation_instructions = RichTextUploadingField(
        verbose_name="CAP mobile Intune installation instructions", null=True, blank=True, default=None,
        config_name='html_only',
        help_text="Html text that will be rendered on the bulk mobile installation page."
    )
    mobile_mdm_installation_instructions = RichTextUploadingField(
        verbose_name="CAP mobile MDM installation instructions", null=True, blank=True, default=None,
        config_name='html_only',
        help_text="Html text that will be rendered on the bulk mobile installation page."
    )

    objects = models.Manager()
    stable_downloadable_version_objects = StableDownloadableAppFileManager()
    beta_downloadable_version_objects = BetaDownloadableAppFileManager()

    class Meta:
        verbose_name = "App File"
        verbose_name_plural = "App Files"
        unique_together = ("type", "cd_pipeline_version", "is_download_version")

    @classmethod
    def get_desktop_app_file(cls) -> str:
        """
        Returns the latest desktop AppFile.
        """
        if is_cap_v_five_ga_enabled():
            return cls.stable_downloadable_version_objects.filter(
                type__in=cls.DESKTOP,
                cd_pipeline_version=cls.CAP_V5_PIPELINE,
            ).first()
        else:
            return cls.stable_downloadable_version_objects.filter(type__in=cls.DESKTOP).first()

    @classmethod
    def get_windows_app_file(cls) -> Self:
        """
        Returns the latest app_file for Windows MSI.
        """
        if is_cap_v_five_ga_enabled():
            return cls.stable_downloadable_version_objects.filter(
                type=cls.WINDOWS_MSI,
                cd_pipeline_version=cls.CAP_V5_PIPELINE,
            ).first()
        else:
            return cls.stable_downloadable_version_objects.filter(type=cls.WINDOWS_MSI).first()

    @classmethod
    def get_macos_app_file(cls) -> Self:
        """
        Returns the latest app_file for macOS.
        """
        if is_cap_v_five_ga_enabled():
            return cls.stable_downloadable_version_objects.filter(
                type=cls.MACOS,
                cd_pipeline_version=cls.CAP_V5_PIPELINE,
            ).first()
        else:
            return cls.stable_downloadable_version_objects.filter(type=cls.MACOS).first()

    @classmethod
    def get_mobile_app_file(cls) -> Self:
        """ CAP mobile powered by Trustd app file"""
        # both platforms contain the same installation instructions
        return cls.objects.filter(type__in=cls.TRUSTD).first()

    @classmethod
    def get_windows_version(cls) -> str:
        """
        Returns the version of the downloadable AppFile for Windows MSI.
        """
        instance = cls.get_windows_app_file()
        if instance:
            return instance.version
        else:
            return cls.UNKNOWN_VERSION

    @classmethod
    def get_macos_version(cls) -> str:
        """
        Returns the downloadable version of macOS.
        """
        instance = cls.get_macos_app_file()
        if instance:
            return instance.version
        else:
            return cls.UNKNOWN_VERSION

    @classmethod
    def get_desktop_version(cls) -> str:
        """
        Returns the latest version of desktop application.
        """
        return cls.get_windows_version() or cls.get_macos_version()

    @property
    def download_url(self):
        """
        Returns full path with domain name to the application file.
        :return: url
        :rtype: str
        """
        return urlparse.urljoin(
            "{0}://{1}".format(get_http_protocol_url_name(), Site.objects.get_current().domain),
            self.file.url
        ) if self.file else self.url

    @property
    def is_beta(self):
        """
        Returns True if the app file is beta otherwise returns False.
        """
        if is_cap_v_five_ga_enabled():
            return False
        else:
            return self.version.startswith(CAP_V_FIVE_BETA_VERSION)


    @classmethod
    def get_v5_windows_app_file(cls) -> Self:
        """
        Returns the latest v5 app_file for Windows MSI.
        """
        if is_cap_v_five_ga_enabled():
            return cls.stable_downloadable_version_objects.filter(
                type=cls.WINDOWS_MSI,
                cd_pipeline_version=cls.CAP_V5_PIPELINE,
            ).first()
        else:
            return cls.beta_downloadable_version_objects.filter(type=cls.WINDOWS_MSI).first()

    @classmethod
    def get_v5_macos_app_file(cls) -> Self:
        """
        Returns the latest v5 AppFile for macOS.
        """
        if is_cap_v_five_ga_enabled():
            return cls.stable_downloadable_version_objects.filter(
                type=cls.MACOS,
                cd_pipeline_version=cls.CAP_V5_PIPELINE,
            ).first()
        else:
            return cls.beta_downloadable_version_objects.filter(type=cls.MACOS).first()


    @classmethod
    def get_v5_windows_version(cls) -> str:
        """
        Returns the latest v5 version for Windows MSI.
        """
        instance = cls.get_v5_windows_app_file()
        if instance:
            return instance.version
        else:
            return cls.UNKNOWN_VERSION

    @classmethod
    def get_v5_macos_version(cls) -> str:
        """
        Returns the latest v5 macOS version.
        """
        instance = cls.get_v5_macos_app_file()
        if instance:
            return instance.version
        else:
            return cls.UNKNOWN_VERSION

    @classmethod
    def get_v5_desktop_version(cls) -> str:
        """
        Returns the latest v5 desktop version.
        """
        return cls.get_v5_windows_version() or cls.get_v5_macos_version()

    @classmethod
    def get_agent_link_for_organisation(cls, organisation: Organisation, get_v5=False) -> dict:
        """
        Returns the agent link for the organisation.
        """
        from dashboard.utils import (
            get_bulk_live_server_link,
            get_bulk_live_server_link_cap_v5,
        )

        if (organisation.is_self_enrollment_type):
            return {
                "version": cls.get_desktop_version(),
            }

        main_app_user = organisation.main_app_user

        if (not main_app_user):
            return {
                "version": cls.get_desktop_version(),
            }

        windows_app_file = cls.get_windows_app_file()
        macos_app_file = cls.get_macos_app_file()
        winmsi = get_bulk_live_server_link(settings.BUILD_TYPES.MSI, main_app_user.uuid)
        macos =  get_bulk_live_server_link(settings.BUILD_TYPES.PKG, main_app_user.uuid)
        if get_v5:
            windows_app_file = AppFile.get_v5_windows_app_file()
            macos_app_file = AppFile.get_v5_macos_app_file()
            winmsi = get_bulk_live_server_link_cap_v5(settings.BUILD_TYPES.MSI, main_app_user.uuid)
            macos = get_bulk_live_server_link_cap_v5(settings.BUILD_TYPES.PKG, main_app_user.uuid)

        interpolation_dict = {
            'uuid': main_app_user.uuid,
        }
        return {
            "winmsi": winmsi,
            "macos": macos,
            "version": cls.get_desktop_version(),
            'winmsi_version': windows_app_file.version if windows_app_file else cls.UNKNOWN_VERSION,
            'macos_version': macos_app_file.version if macos_app_file else cls.UNKNOWN_VERSION,
            'winmsi_installation_instructions': windows_app_file.installation_instructions_interpolate({**interpolation_dict, 'builds_host': settings.CAP_V5_BULK_LIVE_LINK_SERVERS[settings.BUILD_TYPES.MSI]}) if windows_app_file else None,
            'macos_installation_instructions': macos_app_file.installation_instructions_interpolate({**interpolation_dict, 'builds_host': settings.CAP_V5_BULK_LIVE_LINK_SERVERS[settings.BUILD_TYPES.PKG]}) if macos_app_file else None,
        }

    def installation_instructions_interpolate(self, interpolation_dict: dict = None) -> str:
        """
        Returns interpolated installation instructions.
        """
        if not self.installation_instructions:
            return ''

        if not interpolation_dict:
            return self.installation_instructions

        return self.installation_instructions.format(**interpolation_dict)

    def __unicode__(self):
        return f"({self.get_type_display()} {self.version} - {self.get_cd_pipeline_version_display()} - is_download={self.is_download_version})"

    def __str__(self):
        return self.__unicode__()


class DisableSendMailUser(TimeStampedModel):
    """
    Very old model. The destination of it is to prevent sending emails to users that have disabled emails.
    This models is used only in one view. I suppose we can get rid of it in the future.
    """
    app_user = models.ForeignKey(AppUser, related_name='disablesendmail_appusers', on_delete=models.CASCADE)

    class Meta:
        verbose_name = 'Disable Send Mail User'
        verbose_name_plural = 'Disable Send Mail User'

    def __unicode__(self):
        return "{}".format(self.app_user.email)

    def __str__(self):
        return self.__unicode__()


class AppVersion(SemanticVersionMixin):
    """
    Model representing an application version with detailed versioning and support status.
    """
    DEVICE_TYPE_DESKTOP = 1
    DEVICE_TYPE_MOBILE = 2

    DEVICE_TYPES = (
        (DEVICE_TYPE_DESKTOP, "Desktop"),
        (DEVICE_TYPE_MOBILE, "Mobile"),
    )

    device_type = models.SmallIntegerField(
        verbose_name="Device Type",
        choices=DEVICE_TYPES,
        default=DEVICE_TYPE_DESKTOP,
        help_text="The type of device that the app version is for."
    )

    class Meta:
        verbose_name = "App Version"
        verbose_name_plural = "App Versions"
        ordering = ["-major", "-minor", "-patch"]
        unique_together = ("major", "minor", "patch", "version_channel", "device_type")
        indexes = [
            models.Index(fields=["major", "minor", "patch"]),
            models.Index(fields=["support_level"]),
            models.Index(fields=["version_channel"]),
            models.Index(fields=["device_type"]),
            models.Index(fields=["major", "minor", "patch", "support_level", "version_channel", "device_type"]),
            models.Index(fields=["major", "minor", "patch", "device_type"]),
        ]

    @classmethod
    def get_default_version(cls, device_type: int=DEVICE_TYPE_DESKTOP) -> Self:
        """
        Returns the default version.
        """
        return cls.objects.get_or_create(major=0, minor=0, patch=0, device_type=device_type)[0]

    def supports_opswat(self) -> bool:
        """
        Returns True if the app version supports OPSWAT software scanning.
        OPSWAT is supported in versions 5.2.0 and above.
        """
        min_version = type(self)(major=5, minor=2, patch=0)
        return self >= min_version

    def supports_opswat_patch(self) -> bool:
        """
        Returns True if the app version supports OPSWAT patching functionality.
        OPSWAT patching is supported in versions 5.5.0 and above.
        """
        min_version = type(self)(major=5, minor=5, patch=0)
        return self >= min_version

    def __str__(self) -> str:
        """
        Returns the string representation of the object.
        """
        return f"{super().__str__()} ({self.get_device_type_display()})"



class AppInstall(TimeStampedModel, CRUDSignalMixin, DeviceTypeMixin, DeviceUrlMixin):
    """
    This model represents installed application (no matter where /phone/pc/mac/etc)
    """
    app_user = models.ForeignKey(AppUser, related_name='installs', on_delete=models.CASCADE)
    os = models.ForeignKey(OperatingSystem, on_delete=models.CASCADE)
    hostname = models.CharField(max_length=125, default="Unknown", null=True, blank=True)
    caption = models.CharField(max_length=125, default="Unknown", null=True, blank=True)
    platform = models.CharField(max_length=125, default="Unknown", null=True, blank=True)
    release = models.CharField(max_length=125, default="Unknown", null=True, blank=True)
    os_release = models.CharField(verbose_name='OS version', max_length=250, null=True, blank=True)
    os_info = models.JSONField(verbose_name='OS Info', null=True, blank=True)
    base_os = models.CharField(verbose_name='Base OS', max_length=250, null=True, blank=True)
    security_patch = models.CharField(verbose_name='Security patch', max_length=250, null=True, blank=True)
    device_id = models.CharField(
        max_length=125,
        help_text='This value is determined by installed application (e.g. CAP) and passed to API. '
                  'It can contain a prefix like CAP_V_FIVE_BETA_PREFIX.'
    )
    real_device_id = models.CharField(
        max_length=125,
        help_text='This is the real device id that is used to identify the device, without the prefix.'
                  'This happens since CAP V5 adds a prefix (CAP_V_FIVE_BETA_PREFIX) to the real value.',
        default='',
    )
    serial_number = models.CharField(
        verbose_name='Serial number', max_length=125, default='',
        help_text='Null values should be stored as empty strings. '
                  'This value is determined by installed application (e.g., CAP) and passed to API.'
    )
    installer = models.CharField(max_length=125, null=True, blank=True)
    inactive = models.BooleanField(verbose_name='Inactive', default=False)
    date_removed = models.DateTimeField(verbose_name='Date removed', null=True, blank=True)
    app_version = models.CharField(verbose_name='Application version', max_length=255, null=True, blank=True)
    version = models.ForeignKey(
        to=AppVersion,
        verbose_name="App Version",
        on_delete=models.DO_NOTHING,
        help_text="The version of the app that is installed on the device. "
                  "Advanced type of versioning compared to app_version field."
    )
    machine_vendor = models.CharField(max_length=125, null=True, blank=True)
    machine_model = models.CharField(max_length=125, null=True, blank=True)
    system_info = models.TextField(verbose_name='System info', null=True, blank=True)
    registration_unique_token = models.TextField(verbose_name="Registration unique token", unique=True)
    is_vulnerable = models.BooleanField(verbose_name='Is Vulnerable', default=False)
    end_of_life = models.ForeignKey(
        to=OSEndOfLife, related_name='end_of_life', on_delete=models.DO_NOTHING, null=True, blank=True
    )
    v5_migration_performed = models.BooleanField(
        verbose_name='V5 migration performed',
        default=False,
        help_text='This field is used to track if the archive data migration from beta V5 has been performed for this device.'
    )

    objects = AppInstallDuplicatesManager()
    versions = AppInstallVersionManager()

    # most efficient way to store previous values without additional DB call
    @classmethod
    def from_db(cls, db, field_names, values):
        instance = super().from_db(db, field_names, values)

        # customization to store the original field values on the instance
        db_values = dict(zip(field_names, values))

        # only store the fields we want to compare in _loaded_values
        fields_to_load = ['inactive']
        instance._loaded_values = {k: db_values[k] for k in fields_to_load if k in db_values}

        return instance

    def save(self, *args, **kwargs):
        if isinstance(kwargs.get('update_fields'), list):
            kwargs['update_fields'] += ['device_type', 'real_device_id']
        from appusers.utils import DeviceType
        # identifies device type on creation
        self.device_type = DeviceType(
            health_check_app_user=self.app_user.health_check_user,
            os_release=self.release,
            platform=self.platform,
            caption=self.caption,
            machine_vendor=self.machine_vendor,
            machine_model=self.machine_model
        )()
        self.real_device_id = get_real_device_id(self.device_id)
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = 'App Install'
        verbose_name_plural = 'App Installs'
        unique_together = ('app_user', 'device_id', 'serial_number')
        indexes = [
            models.Index(fields=['app_user', 'real_device_id', 'serial_number']),
            models.Index(fields=['app_user'], condition=Q(inactive=False), name='idx_appinstall_not_inactive'),
        ]

    def __unicode__(self):
        return f"ID{self.pk} - {self.device_id}"

    def __str__(self):
        return self.__unicode__()

    def __repr__(self):
        return f"<AppInstall: {self.device_id} ({self.get_device_type_display()})>"

    def display_os(self):
        """
        Returns OS name.
        :return: os name
        :rtype: str
        """
        display_os = self.os.title if (self.caption == 'Unknown' or not self.caption) else self.caption
        return display_os or 'Unknown'

    @property
    def full_name(self):
        """
        Returns full AppInstall name including hostname, os and app version.
        :return: full name
        :rtype: str
        """
        return "{0} [{1}] (app v{2})".format(
            self.hostname, self.display_os(), self.get_app_version()
        )

    def agreed_policies(self):
        """
        Returns app install agreed policies.
        This method considers agreed policies per logged os user not per app user.
        :return: agreed policies
        :rtype: AppInstallPolicyAgreement queryset
        """
        return self.policies.filter(
            agreed_date__isnull=False,
            version__policy__active=True,
            version__active=True,
            version__main=True
        ).distinct('version__policy', 'os_user')

    @property
    def agreed_policies_count(self):
        """
        Returns amount of agreed policies.
        This method considers agreed policies per logged os user not per app user.
        :return: amount of agreed policies
        :rtype: int
        """
        return self.agreed_policies().count()

    def read_policies(self, exclude_agreed=False):
        """
        Returns app install read policies.
        This method considers read policies per logged os user not per app user.
        :param exclude_agreed: excludes agreed policies (agreed policies are always read as well)
        :type exclude_agreed: bool
        :return: read policies
        :rtype: AppInstallPolicyAgreement queryset
        """
        filters = {
            "read_date__isnull": False,
            "version__policy__active": True,
            "version__active": True,
            "version__main": True
        }
        if exclude_agreed:
            filters['agreed_date__isnull'] = True

        return self.policies.filter(
            **filters
        ).distinct('version__policy', 'os_user')

    @property
    def read_policies_count(self):
        """
        Returns amount of read policies.
        This method considers read policies per logged os user not per app user.
        :return: amount of read policies
        :rtype: int
        """
        return self.read_policies().count()

    def pending_policies(self):
        """
        Returns pending policies.
        This method considers pending policies per logged os user not per app user.
        :return: pending policies
        :rtype: list
        """
        versions = self.app_user.organisation.get_latest_policy_versions()
        for v in versions:
            v.pending_users = self.os_users.exclude(
                policy_agreements__version=v
            )
        return [v for v in versions if v.pending_users.count()]

    @property
    def pending_policies_count(self):
        """
        Returns amount of pending policies.
        This method considers pending policies per logged os user not per app user.
        :return: amount of pending policies
        :rtype: int
        """
        return len(self.pending_policies())

    @property
    def absolute_url(self):
        """
        Returns absolute url including domain name for an organisation.
        :return: absolute url
        :rtype: str
        """
        return urljoin(
            '{0}://{1}'.format(get_http_protocol_url_name(), Site.objects.get_current().domain),
            self.url()
        )

    def url_app_install_api(self):
        """
        Returns the URL for the API app install endpoint.
        """
        return reverse('api-v2:app-install', kwargs={"org_id": self.app_user.organisation.secure_id})

    @property
    def url_encoded_serial_number(self):
        """
        Returns url encoded serial number.
        :return: url encoded serial number
        :rtype: str or None
        """
        try:
            # python 2
            return urlsafe_base64_encode((self.serial_number or '').encode('utf-8', 'ignore')).decode('utf-8')
        except AttributeError:
            # python 3
            return urlsafe_base64_encode((self.serial_number or '').encode('utf-8', 'ignore'))

    @property
    def get_uuid(self):
        return self.app_user.uuid

    def get_app_version(self):
        """
        :return: app version with major, minor and patch
        :rtype: str
        """
        if hasattr(self, 'version'):
            return self.version.get_version_string()
        return self.app_version.split("-")[0] if self.app_version else '<1.4.0'

    @property
    def get_commands(self):
        from rulebook.utils import get_latest_version
        organisation = self.app_user.organisation
        # combines all last certificates for all standards
        versions = organisation.get_latest_certificates().values_list('version', flat=True)
        if not versions:
            last_ce = get_latest_version(CYBER_ESSENTIALS)
            versions = [last_ce.pk] if last_ce else []

        commands = Command.objects.filter(
            os=self.os,
            app_check__version__in=versions,
            app_check__active=True,
            min_app_version__lte=self.get_valid_app_version()
        ).distinct('app_check', 'app_check__order')
        return commands

    @property
    def get_reports(self):
        """
        Returns app install reports without those that have no responses (checks)
        :return: app install reports
        :rtype: AppReport queryset
        """
        return self.reports.filter(total_responses__isnull=False)

    @property
    def is_beta(self) -> bool:
        """
        Tells whether an app is considered beta.
        """
        from beta_features.utils import get_beta_app_install_queryset_filter

        return AppInstall.objects.filter(
            get_beta_app_install_queryset_filter(),
            pk=self.pk
        ).exists()

    @property
    def is_trustd_app(self):
        """
        Returns True if the app install is a trustd mobile app.
        """
        from beta_features.utils import get_app_install_trustd_beta_q_object

        if is_trustd_mobile_available_for_organisation(self.app_user.organisation):
            return self.device_type == self.MOBILE and hasattr(self, 'trustd_device')
        return AppInstall.objects.filter(
            get_app_install_trustd_beta_q_object(organisation_prefix='app_user__organisation__'),
            pk=self.pk,
            device_type=self.MOBILE
        ).exists()

    @property
    def has_reports(self):
        """
        Returns True if app install has at least one report otherwise returns False.
        """
        return self.get_reports.exists()

    @property
    def get_latest_report(self):
        """
        Returns last created app install report or None if no report found
        :return: last report
        :rtype: AppReport instance
        """
        if hasattr(self, "_prefetched_objects_cache"):
            reports = self._prefetched_objects_cache.get("reports")
            if reports and len(reports) > 0:
                return reports[0]
        return self.get_reports.order_by('-created').first()

    @property
    def get_latest_report_prefetch_installed_software(self):
        """
        Returns last created app install report or None if no report found.
        Prefetches Installed Software
        :return: last report
        :rtype: AppReport instance
        """
        return self.reports.prefetch_related(
            'installed_software'
        ).filter(
            total_responses__isnull=False
        ).order_by('-created').first()

    @property
    def get_pass_percentage(self):
        try:
            return self.get_latest_report.pass_percentage
        except AttributeError:
            return 0

    @property
    def total_reports_count(self):
        """
        Returns total count of reports.
        :return: reports count
        """
        if hasattr(self, 'reports'):
            try:
                return self.reports.all().count()
            except ProgrammingError:
                return self.reports.all().count()
        else:
            return self.reports.all().count()

    @property
    def latest_local_user(self):
        """
        Returns latest created operating system user.
        :return: last os user
        :rtype: AppInstallOSUser or None
        """
        # first check if we have prefetched data and if so use it
        # instead of creating extra database requests
        if hasattr(self, "_prefetched_objects_cache"):
            users = self._prefetched_objects_cache.get("os_users")
            if users and len(users) > 0:
                return self._prefetched_objects_cache["os_users"].reverse()[0]
            else:
                return None

        # else get data from the database
        return self.os_users.last()

    @property
    def last_login_user(self) -> Optional["AppInstallOSUser"]:
        """
        Returns last login user.
        """
        login_record = self.user_login_records.first()
        if login_record:
            return login_record.user

    @property
    def last_login_username(self) -> str:
        """
        Returns latest logged in user name.
        """
        user = self.last_login_user
        if user:
            return user.name
        else:
            return "Unknown"

    @property
    def has_bulk_deploy_user(self):
        """
        Returns True if this device is linked to bulk deploy user.
        :return: True or False
        :rtype: bool
        """
        return self.app_user == self.app_user.organisation.main_app_user

    @property
    def is_assigned_to_user(self) -> bool:
        """
        Returns True if UBA is enabled for the organisation
        and the device is connected to its own app user otherwise returns False.
        """
        return self.app_user.organisation.is_uba_enabled and not self.has_bulk_deploy_user

    @property
    def is_not_assigned_to_user(self) -> bool:
        """
        Returns True if UBA is enabled for the organisation
        and the device is still connected to the organisation admin otherwise returns False.
        """
        return self.app_user.organisation.is_uba_enabled and not self.is_assigned_to_user

    def get_platform(self):
        """
        Returns device platform.
        :return: device platform
        :rtype: str
        """
        from appusers.utils import get_device_platform
        return get_device_platform(self)

    @property
    def latest_failed_check(self):
        """
        Returns latest failed check if it exists, else None
        """
        report = self.get_latest_report
        if not report:
            return None
        failed_checks = report.failed_checks
        if failed_checks:
            import operator
            results = sorted(failed_checks, key=operator.attrgetter('created'), reverse=False)
            if results:
                return results[0].app_check
        return None

    def send_notification(self, message_type, url=None):
        """ Send notification """
        from notifications.handlers import NotificationHandler
        if not url:
            url = self.url()
        kwargs = {
            'user_name': get_full_name_or_email(self.app_user),
            'organisation_id': self.app_user.organisation.id,
            'organisation_name': self.app_user.organisation.name,
            'app_install_caption': self.display_os(),
            'url': url,
        }
        NotificationHandler.send_notification(message_type=message_type, **kwargs)

    def set_is_vulnerable(self, report_id):
        is_vulnerable = AppOSInstalledSoftware.objects.vulnerable_objects(report_id).exists()
        # invert for a (potentially) more efficient query
        is_not_vulnerable = not is_vulnerable
        changed = AppInstall.objects.filter(pk=self.pk, is_vulnerable=is_not_vulnerable)
        # resolve the above query first, otherwise this creates a conditional update query
        if changed:
            changed.original_update(is_vulnerable=is_vulnerable, modified=timezone.now())
            # if app install has any vulnerable software, then send notification only if it was set as vulnerable
            if is_vulnerable:
                self.send_notification(
                    'device_has_vulnerable_software',
                    reverse('dashboard:software-report', kwargs={'org_id': self.app_user.organisation.secure_id})
                )

    def get_valid_app_version(self):
        try:
            Version(self.app_version)
        except Exception:
            return "0"
        else:
            return self.app_version

    @property
    def last_check_in(self):
        """
        Returns the latest report date and defaults to creation date
        """
        report = self.get_latest_report
        if report:
            return report.created
        else:
            return self.created

    def get_duplicate(self) -> Self | None:
        """
        Returns the "duplicate" of given app install, otherwise returns None.
        A "duplicate" is considered another app_install on same device and same user.
        """
        # we cannot match on Trustd beta, because the only common thing between the two installs is the user id
        if self.is_mobile:
            return None

        device_id = self.device_id.replace(CAP_V_FIVE_BETA_PREFIX, '')
        beta_q = Q()
        if not self.is_beta:
            device_id = f'{CAP_V_FIVE_BETA_PREFIX}{self.device_id}'
            beta_q = get_beta_app_install_queryset_filter()

        return AppInstall.objects.filter(
            beta_q,
            device_id=device_id,
            serial_number=self.serial_number,
            app_user=self.app_user,
        ).first()

    def get_main_install(self) -> Self | None:
        """
        Returns the main install of given app install, otherwise returns None.
        A "main install" is considered the app_install of the same device and inside the same AppUser's organisation which is not v5-prefixed.
        """
        if self.is_mobile:
            return None

        if not self.device_id.startswith(CAP_V_FIVE_BETA_PREFIX):
            return None

        device_id = self.device_id.replace(CAP_V_FIVE_BETA_PREFIX, '')

        return AppInstall.objects.filter(
            app_user__organisation=self.app_user.organisation,
            real_device_id=self.real_device_id,
            device_id=device_id,
            serial_number=self.serial_number,
        ).first()

    def supports_opswat(self) -> bool:
        """
        Returns True if the app install version supports opswat software scanning.
        """
        return self.version.supports_opswat()

    def supports_opswat_patch(self) -> bool:
        """
        Returns True if the app install version supports opswat patching functionality.
        """
        return self.version.supports_opswat_patch()

    @cached_property
    def has_known_opswat_os_id(self) -> bool:
        """
        Check if this app install has a known OPSWAT OS os_id information for patch filtering.

        Returns False if:
        - No opswat_os relation exists
        - opswat_os.os_id is None
        - opswat_os.os_id is -1 (unknown OS)

        Returns:
            bool: True if OPSWAT OS info is valid for filtering, False otherwise
        """
        if not hasattr(self, 'opswat_os') or not self.opswat_os:
            return False
        return self.opswat_os.has_known_os_id()


class AppInstallCheckStatus(TimeStampedModel, BigPkAbstract):
    """
    A model to store the current passing/failing status of a particular check for a
    particular AppInstall, which is updated every time the state changes.
    """

    app_install = models.ForeignKey(to=AppInstall, related_name='check_statuses', on_delete=models.CASCADE)
    app_check = models.ForeignKey(to=AppCheck, related_name='device_statuses', on_delete=models.CASCADE)
    passing = models.BooleanField(default=False)
    status_changed = model_utils_fields.MonitorField(
        monitor='passing',
        help_text='datetime when `passing` last changed'
    )

    passing_tracker = FieldTracker(fields=['passing'])

    class Meta:
        verbose_name = 'Check Status'
        verbose_name_plural = 'Check Statuses'
        unique_together = ('app_install', 'app_check',)

    def save(self, **kwargs):
        super().save(**kwargs)
        # if passing changed to False, send a notification (even when creating, which goes from None to False)
        if self.passing_tracker.has_changed('passing') and not self.passing:
            self.send_notification('device_failing_check')

    def send_notification(self, message_type):
        """ Send notification """
        from notifications.handlers import NotificationHandler
        kwargs = {
            'user_name': get_full_name_or_email(self.app_install.app_user),
            'organisation_id': self.app_install.app_user.organisation.id,
            'organisation_name': self.app_install.app_user.organisation.name,
            'app_install_caption': self.app_install.display_os(),
            'app_check_name': self.app_check.title,
            'url': self.app_install.url(),
        }
        NotificationHandler.send_notification(message_type=message_type, **kwargs)


class AppInstallOSUser(TimeStampedModel):
    """
    In this model we store all Operating System users under which the app was running.
    """
    app_install = models.ForeignKey(to=AppInstall, related_name='os_users', on_delete=models.CASCADE)
    username = models.CharField(db_index=True, verbose_name='Username', max_length=125, default='', null=True, blank=True)
    domain = models.CharField(
        db_index=True, verbose_name='Domain', default='', max_length=125, null=True, blank=True,
        # Technically, we can have the same user and username but in different domains, which we treat as different OS users.
        help_text='Domain name for Windows machines, sometimes users are logged into a specific domain like "local".'
    )
    is_admin_account = models.BooleanField(verbose_name='Is admin account', null=True, blank=True)
    agreed_policies_count = models.PositiveIntegerField(verbose_name='Agreed policies count', default=0)
    # when a mobile device is unassigned and we cannot tell the OS user, this will be their username
    UNASSIGNED = 'unassigned'
    # Non-logged-in user in Windows machines has a username 'SYSTEM'
    SYSTEM = 'SYSTEM'

    class Meta:
        verbose_name = 'OS User'
        ordering = ['created']
        verbose_name_plural = 'OS Users'
        unique_together = ('username', 'domain', 'app_install')

    def __unicode__(self):
        return 'User: {0} on machine: {1}'.format(self.username or self.domain, self.app_install)

    def __str__(self):
        return self.__unicode__()

    @property
    def name(self):
        """
        Returns os user name which can be username field or domain field.
        :return: username
        :rtype: str
        """
        return self.username or self.domain or "Unnamed"

    def update_agreements_counter(self, pre_delete=False):
        """
        Updates agreed policies counter.
        :pre_delete: if True it means that self.agreements will have the entry that will be deleted
        thus the counter will be bigger (+1) then in real, that's why we need to subtract 1 from the counter
        :type pre_delete: bool
        :return: nothing
        :rtype: None
        """
        # store a copy of the current data
        saved_agreed_policies_count = int(self.agreed_policies_count)

        self.agreed_policies_count = self.agreements.count()
        if pre_delete and self.agreed_policies_count > 0:
            self.agreed_policies_count -= 1

        # compare and save only if changed
        if saved_agreed_policies_count != self.agreed_policies_count:
            self.save(update_fields=['agreed_policies_count', 'modified'])

    @property
    def agreements(self):
        """
        Returns agreed policies.
        :return: policies agreements
        :rtype: AppInstallPolicyAgreement queryset
        """
        return self.policy_agreements.filter(
            agreed_date__isnull=False,
            version__policy__active=True,
            version__policy__organisation=self.app_install.app_user.organisation,  # pylint: disable=no-member
            version__active=True,
            version__main=True
        ).distinct('version__policy')

    @classmethod
    def get_username_from_email(cls, email):
        return email.split('@')[0]


class UserLoginHistory(TimeStampedModel):
    """
    In this model we store login history for operating system users.
    """
    app_install = models.ForeignKey(to=AppInstall, related_name='user_login_records', on_delete=models.CASCADE)
    user = models.ForeignKey(
        to=AppInstallOSUser,
        verbose_name='Logged user',
        on_delete=models.CASCADE,
        related_name='login_records'
    )

    class Meta:
        verbose_name = 'User Login History'
        verbose_name_plural = 'User Login Histories'
        ordering = ['-created']

    def __unicode__(self):
        return "{0} - {1}".format(self.user, self.created)

    def __str__(self):
        return self.__unicode__()


class AppUserLog(TimeStampedModel):
    """
    We use this model to detect new OS releases or unsupported OS.
    This model looks very strange and used only in one place.
    I think we can get rid of it.
    """
    app_user = models.ForeignKey(AppUser, related_name='logs', on_delete=models.CASCADE)
    hostname = models.CharField(max_length=125, default="Unknown")
    caption = models.CharField(max_length=125, default="Unknown")
    platform = models.CharField(max_length=125, default="Unknown")
    release = models.CharField(max_length=125, default="Unknown")
    device_id = models.CharField(max_length=125)
    has_error = models.BooleanField(default=True)
    error_text = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = 'App User Log'
        verbose_name_plural = 'App User Logs'

    def __unicode__(self):
        return "{0} {1} {2}".format(self.hostname, self.caption, self.device_id)

    def __str__(self):
        return self.__unicode__()


class SoftwarePackage(TimeStampedModel, BigPkAbstract):
    """
    In this model we store software packages installed on users machines.
    """
    vendor = models.CharField(verbose_name='Product Vendor', max_length=255, null=True, blank=True)
    product = models.CharField(verbose_name='Product Name', db_index=True, max_length=255)
    version = models.CharField(verbose_name='Product Version', max_length=255)
    mobile_app = models.BooleanField(verbose_name='Wrapped iOS mobile app for M1 macOS', default=False)
    platform = models.CharField(verbose_name='Platform', max_length=15, null=True, blank=True)

    class Meta:
        unique_together = (
            "product", "version", "vendor",
            "mobile_app", "platform"
        )
        indexes = [
            Index(Lower("product"), name="idx_lower_product"),
            Index(Lower("version"), name="idx_lower_version"),
            Index(Lower("product"), Lower("version"), name="idx_lower_product_version"),
        ]
        verbose_name = 'Software Package'
        verbose_name_plural = 'Software Packages'

    def __str__(self):
        return '{0} {1}'.format(self.product, self.version)

    @property
    def is_vulnerable(self) -> bool:
        return hasattr(self, 'related_cves') and self.related_cves.cves_fk.exists()

    def get_target_sw(self):
        target_sw_mapping = {
            'android': 'android',
            'win32': 'windows',
            'darwin': 'macos',
            'ios': 'iphone_os',
        }
        return target_sw_mapping.get(self.platform) or '*'


class SoftwarePackageCPE(TimeStampedModel):
    software = models.OneToOneField(
        SoftwarePackage, related_name='cpe', on_delete=models.CASCADE
    )
    cpe = models.CharField(
        max_length=255, help_text="CPE for software"
    )
    cve_matcher_failed = models.BooleanField(
        "Is CVE Matcher Failing", default=False)

    class Meta:
        verbose_name = 'Software Package CPE'
        verbose_name_plural = 'Software Packages CPEs'

    def __repr__(self):
        return f'SoftwarePackageCPE(Software: {self.software}, CPE: {self.cpe})'


class SoftwarePackageCVEs(TimeStampedModel):
    software = models.OneToOneField(
        SoftwarePackage, related_name='related_cves', on_delete=models.CASCADE
    )
    cves = ArrayField(
        models.CharField(verbose_name="CVE", max_length=255),
        verbose_name="CVEs", default=list,
        help_text="List of CVEs for vulnerable software. Some of the records here might not be in the CVERepository. "
        "To get the list of CVEs that are in the CVERepository, please use cves_fk field."
    )

    cves_fk = models.ManyToManyField(
        to='vulnerabilities.CVERepository',
        through='SoftwarePackageCVEsCVERepository',
        related_name='software_package_cves',
        verbose_name='CVERepository records',
        help_text='CVERepository records linked to this SoftwarePackage.'
    )

    class Meta:
        verbose_name = 'Software Package CVEs'
        verbose_name_plural = 'Software Packages CVEs'


class SoftwarePackageCVEsCVERepository(TimeStampedModel):
    software_package_cves = models.ForeignKey(
        to=SoftwarePackageCVEs,
        on_delete=models.CASCADE
    )
    cve_repository = models.ForeignKey(
        to='vulnerabilities.CVERepository',
        on_delete=models.CASCADE
    )

    class Meta:
        verbose_name = 'Software Package CVEs CVERepository'
        verbose_name_plural = 'Software Packages CVEs CVERepository'
        unique_together=('software_package_cves', 'cve_repository')


class AppOSInstalledSoftware(TimeStampedModel):
    """
    We use this model as a relation between software packages and app report.
    It represents installed software on application operating system.
    """
    report = models.ForeignKey('appusers.AppReport', related_name='installed_software', on_delete=models.CASCADE)
    software = models.ForeignKey(SoftwarePackage, related_name='user_installs', on_delete=models.CASCADE)
    date_installed = models.DateTimeField(verbose_name='Date installed', null=True, blank=True)

    objects = AppOSInstalledSoftwareManager()

    class Meta:
        verbose_name = "App OS Installed Software"
        verbose_name_plural = "App OS Installed Software"

    def __unicode__(self):
        return 'report id:{0}, software id:{1}'.format(self.report_id, self.software_id)

    def get_date_installed(self):
        """
        Returns app date installed if we have it.
        """
        return self.date_installed.strftime("%Y-%m-%d %I:%M %p") if self.date_installed else gettext_lazy("Unknown")


class AppInstallNetworkInterface(TimeStampedModel):
    """
    In this model we store network interfaces that have app install.
    """
    app_install = models.ForeignKey(AppInstall, related_name='network_interfaces', on_delete=models.CASCADE)
    local_ip = models.CharField(verbose_name='Local ip', max_length=255, null=True, blank=True)
    public_ip = models.CharField(verbose_name='Public ip', max_length=255, null=True, blank=True)
    mac_address = models.CharField(verbose_name='Mac address', max_length=255, null=True, blank=True)
    gateway_ip = models.CharField(verbose_name='Gateway ip', max_length=255, null=True, blank=True)
    gateway_mac = models.CharField(verbose_name='Gateway mac', max_length=255, null=True, blank=True)
    cidr = models.CharField(verbose_name='Classless Inter-Domain Routing', max_length=255, null=True, blank=True)

    class Meta:
        verbose_name = 'Network interface'
        verbose_name_plural = 'Network interfaces'

    def __unicode__(self):
        return '{0} | {1}'.format(self.app_install, self.mac_address)

    def __str__(self):
        return self.__unicode__()


class AppInstallPolicyAgreement(TimeStampedModel, CRUDSignalMixin):
    """
    In this model we store policy agreements.
    So every time someone agreed a policy we create an entry with this app install and policy.
    """
    version = models.ForeignKey(
        to='organisations.OrganisationPolicyVersion',
        verbose_name='Policy version',
        related_name='agreements',
        on_delete=models.CASCADE
    )
    app_install = models.ForeignKey(
        to=AppInstall,
        related_name='policies',
        null=True,
        blank=True,
        on_delete=models.CASCADE
    )
    os_user = models.ForeignKey(
        to=AppInstallOSUser,
        related_name='policy_agreements',
        null=True,
        blank=True,
        on_delete=models.CASCADE
    )
    read_date = models.DateTimeField(null=True, blank=True)
    agreed_date = models.DateTimeField(null=True, blank=True)

    objects = UserPolicyAgreementManager()

    class Meta:
        verbose_name = 'Policy agreement'
        verbose_name_plural = 'Policy agreements'

    def __unicode__(self):
        return '{0} | Agreed: {1} | Read: {2} | User: {3}'.format(self.version, self.agreed_date, self.read_date,
                                                                  self.os_user)

    def __str__(self):
        return self.__unicode__()

    def get_user(self):
        """
        Returns logged user name.
        :return: username
        :rtype: str
        """
        if self.app_install.app_user.organisation.bulk_install and not self.app_install.is_assigned_to_user:
            return self.os_user.name if self.os_user else ''  # pylint: disable=no-member
        else:
            return '{0} ({1}: {2})'.format(
                self.app_install.app_user.get_full_name,  # pylint: disable=no-member
                _("local user"),
                self.os_user.name if self.os_user else ''
            )


class DeviceManufacturer(TimeStampedModel):
    """
    In this model we store device manufacturers.
    """
    name = models.CharField(verbose_name='Manufacturer name', max_length=255, unique=True)
    is_supported = models.BooleanField(
        verbose_name='Manufacturer is supported',
        default=None,
        help_text='This field has higher priority then DeviceModel.is_supported',
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = 'Device manufacturer'
        verbose_name_plural = 'Device manufacturers'

    def __unicode__(self):
        return self.name

    def __str__(self):
        return self.__unicode__()


class DeviceModel(TimeStampedModel):
    """
    In this model we store device models.
    """
    DESKTOP = 1
    MOBILE = 2
    SERVER = 3

    DEVICE_TYPES = (
        (DESKTOP, 'Desktop'),
        (MOBILE, 'Mobile'),
        (SERVER, 'Server')
    )

    manufacturer = models.ForeignKey(
        to=DeviceManufacturer, verbose_name='Manufacturer', related_name='models', on_delete=models.CASCADE
    )
    model = models.CharField(verbose_name='Device model', max_length=255)
    device_type = models.PositiveIntegerField(verbose_name='Device type', choices=DEVICE_TYPES, default=DESKTOP)
    is_supported = models.BooleanField(verbose_name='Model is supported', default=True)

    class Meta:
        verbose_name = 'Device model'
        verbose_name_plural = 'Device models'
        unique_together = ['manufacturer', 'model']

    def __unicode__(self):
        return '{0} {1}'.format(self.manufacturer, self.model)

    def __str__(self):
        return self.__unicode__()


def update_version_fk(instance) -> None:
    """
    Updates the version foreign key for an AppInstall instance.

    This function handles two scenarios:
    1. When creating or updating a single AppInstall instance.
    2. When updating a queryset containing multiple AppInstall instances.

    Due to the limitations of Django signals with querysets, special handling is required
    to ensure the version is correctly updated in both cases.
    """
    from appusers.utils import get_app_version

    # retrieve the current app version ID and assign it to the instance's version_id
    # this is necessary for the pre_create and pre_update signals to work properly
    # when dealing with a single instance.
    instance.version_id = get_app_version(
        version=instance.app_version, app_install_type=instance.device_type
    ).id

    if instance.pk:
        # when the instance is already in the database, we need to update the version_id using a queryset
        # this is because Django's update() method operates at the database level
        # and does not trigger model instance signals.
        # directly assigning the version_id to the instance does not affect queryset updates,
        # and using instance.save() can cause errors in other unit tests
        # therefore, the only reliable approach is to perform a direct update on the queryset.
        AppInstall.objects.filter(pk=instance.pk).update(
            version_id=get_app_version(version=instance.app_version, app_install_type=instance.device_type).id,
            suppressed_signals=CRUDSignalMixin.ALL_SIGNALS
        )


@receiver(model_crud, sender=AppInstall, dispatch_uid=uuid.uuid4().hex)
def app_install_signals(sender, instance, **kwargs):
    """
    Detects creating, updating, deleting for AppInstall.
    :param sender: model sender
    :type sender: AppInstall
    :param instance: model instance
    :type instance: AppInstall instance
    :param kwargs: keyword arguments
    :type kwargs: dict
    :return: nothing
    :rtype: None
    """
    if kwargs["pre_create"]:
        # generate registration token for manually created apps
        from appusers.utils import generate_app_registration_token
        if not instance.registration_unique_token:
            instance.registration_unique_token = generate_app_registration_token()

    if kwargs['post_create']:
        organisation = instance.app_user.organisation

        # create a log about completed enrollment for the app
        instance.event_logs.create(
            user=organisation.get_organisation_creator,
            organisation=organisation,
            type=instance.event_logs.model.COMPLETED_ENROLLMENT
        )
        # update partner analytics
        if hasattr(organisation, 'partner') and organisation.partner:
            organisation.partner.update_analytics()

    if kwargs["pre_create"] or kwargs["pre_update"]:
        # we need to clean the version from trustd since it can have the suffix "_cybersmart"
        clean_version_display = instance.app_version
        if hasattr(instance, 'trustd_device'):
            from appusers.utils import parse_version
            parsed_app_version = parse_version(instance.app_version)
            clean_version_display = ".".join([str(x) for x in parsed_app_version[:3]]) if parsed_app_version else clean_version_display
        # update an app version if needed
        if not hasattr(instance, "version") or clean_version_display != instance.version.get_version_string():
            update_version_fk(instance)

    if kwargs['post_update']:
        if instance.inactive:
            if not instance.date_removed:
                instance.date_removed = timezone.now()
                instance.save()
                # if it is a trustd device, then call the API to deactivate on trustd side as well
                if hasattr(instance, 'trustd_device'):
                    from trustd.tasks import delete_trustd_device
                    delete_trustd_device.delay(instance.trustd_device.id)

        # post_update can be fired even if there are no _loaded_values
        # if exists, check if the inactive field has changed
        if not hasattr(instance, '_loaded_values') or instance.inactive != instance._loaded_values['inactive']:
            organisation = instance.app_user.organisation
            # update organisation analytics
            from analytics.tasks import calculate_organisation_analytics
            calculate_organisation_analytics.delay(organisation.pk)

            # update partner analytics
            if hasattr(organisation, 'partner') and organisation.partner:
                organisation.partner.update_analytics()

            # update app user analytics since you can inactivate via django admin
            from analytics.tasks import calculate_app_user_analytics
            calculate_app_user_analytics.delay(app_users_pk=[instance.app_user.pk])


@receiver(model_crud, sender=AppInstallPolicyAgreement, dispatch_uid=uuid.uuid4().hex)
def policy_agreement_signals(sender, instance, **kwargs):
    """
    Detects creating, updating, deleting for AppInstallPolicyAgreement.
    :param sender: model sender
    :type sender: AppInstallPolicyAgreement
    :param instance: model instance
    :type instance: AppInstallPolicyAgreement instance
    :param kwargs: keyword arguments
    :type kwargs: dict
    :return: nothing
    :rtype: None
    """
    if kwargs["post_create"] or kwargs["post_update"] or kwargs["pre_delete"]:
        if hasattr(instance, "os_user") and instance.os_user:
            instance.os_user.update_agreements_counter(pre_delete=kwargs["pre_delete"] is True)

    if kwargs["post_create"] or kwargs["post_update"] or kwargs["post_delete"]:
        # update app user policies counters
        instance.app_install.app_user.update_policies_counters()


def get_app_install(org_id, app_user_uuid, device_uuid, serial_number=None, only_active=False):
    """
    Returns app install for passed params if exists otherwise returns None.
    :param org_id: organisation secure id
    :type org_id: str
    :param app_user_uuid: app user uuid
    :type app_user_uuid: str
    :param device_uuid: app install uuid
    :type device_uuid: str
    :param serial_number: serial_number
    :type serial_number: str
    :return: app install or None
    :param only_active: only active app installs
    :type only_active: bool
    :rtype: AppInstall or None
    """
    filters = {
        'app_user__organisation__secure_id': org_id,
        'app_user__uuid': app_user_uuid,
        'device_id': device_uuid,
        'serial_number': ''
    }

    if only_active:
        # filter by only active app installs
        filters['inactive'] = False

    if serial_number:
        # try to find app install with passed serial_number
        filters['serial_number'] = serial_number
        app_install = AppInstall.objects.filter(**filters).first()
        if app_install:
            return app_install

    # if serial number is not true object return first app install found with serial number as empty string
    filters['serial_number'] = ''
    return AppInstall.objects.filter(**filters).first()


def get_app_installation(
        app_user_id: int, device_id: str, serial_number: Optional[str] = None, hostname: Optional[str] = None
):
    """
    Returns app install for given params if exists otherwise returns None.
    Hostname parameter is used as a replacement for serial number when the CAP is used on Virtual Desktop.
    """
    from api.common.utils import is_virtual_desktop_and_already_exists

    filters = {
        "app_user_id": app_user_id,
        "device_id": device_id,
        "serial_number": serial_number
    }

    # first try to find app install with given serial number
    if serial_number:
        if cap := AppInstall.objects.filter(**filters).first():
            return cap

    # if a serial number is not provided or not found, try to find app install with empty serial number
    filters["serial_number"] = ""
    if cap := AppInstall.objects.filter(**filters).first():
        return cap

    # when CAP is used on Virtual Desktop, the Virtual Desktop operating system may change serial number over time
    # and thus create duplicates in the database, so we need to check if CAP is already in db.
    # to do so, we need to check if there is a CAP with given app user uuid, device id and hostname
    # note that we ignore the serial number in this case as it's not reliable for virtual desktops,
    # and use the hostname instead
    if hostname and (cap := is_virtual_desktop_and_already_exists(app_user_id, device_id, hostname)):
        return cap


class AppInstallConfirmedEmail(TimeStampedModel):
    """
    This model is intended to store User Based Attribution confirmed emails.
    """
    app_install = models.ForeignKey(to=AppInstall, related_name="confirmed_emails", on_delete=models.CASCADE)
    email = models.EmailField(verbose_name="Email")

    class Meta:
        verbose_name = "UBA: Confirmed email"
        verbose_name_plural = "UBA: Confirmed emails"
        ordering = ["created"]
        unique_together = ("app_install", "email")

    def __unicode__(self):
        return "{0} ({1})".format(self.app_install, self.email)

    def __str__(self):
        return self.__unicode__()


class AppInstallActivationLog(TimeStampedModel):
    """
    This model is intended to store AppInstall activation logs.
    """
    STATUS = Choices(
        (0, 'FULL_MATCH', _('Full Match')), (1, 'PARTIAL_MATCH', _('Partial Match')),
        (2, 'MULTI_MATCH', _('Multi Match')),
        (3, 'NO_MATCH', _('No Match')))
    ENDPOINT = Choices(
        (0, "ACTIVATE", _('/api/v2/activate')),
        (1, 'REGISTER', _('/device/register'))
    )
    app_install = models.ForeignKey(
        to=AppInstall, related_name="activation_logs", on_delete=models.CASCADE, null=True, blank=True)
    serial_number = models.CharField(max_length=255, verbose_name="Serial number", null=True, blank=True)
    device_id = models.CharField(max_length=255, verbose_name="Device id", null=True, blank=True)
    registration_unique_token = models.TextField(verbose_name="Registration unique token", null=True, blank=True)
    status = models.PositiveSmallIntegerField(choices=STATUS, default=STATUS.FULL_MATCH)
    endpoint = models.PositiveSmallIntegerField(choices=ENDPOINT, default=ENDPOINT.ACTIVATE)
    inactive_status = models.BooleanField(null=True, blank=True)
    last_check_in_log = models.DateTimeField(
        help_text='Log of last date time when this app checked in', null=True, blank=True
    )

    class Meta:
        verbose_name = "Device Activation log"
        verbose_name_plural = "Device Activation logs"

    def __unicode__(self):
        return f"Device ID:{self.device_id}, Serial No.:{self.serial_number}"

    def __str__(self):
        return self.__unicode__()


class AppInvitation(TimeStampedModel):
    """ When an enrolled app user gets sent a CAP download link, we record it here.
    Note: currently only used in Trustd enrolment.
    """
    APP_TYPES = Choices(
        (0, "DESKTOP", _("Desktop")),
        (1, "MOBILE", _("Mobile")),
        (2, "CAP_V_FIVE", _("Desktop (Beta)")),
        (3, "TRUSTD_BETA", _("Mobile (Beta)")),
    )
    app_user = models.ForeignKey(AppUser, related_name="app_invitations", on_delete=models.CASCADE)
    app_type = models.PositiveSmallIntegerField(verbose_name='Application type', choices=APP_TYPES)
    # we need to link the trustd device upon enrolment to be able to get it later during app check-in
    trustd_device = models.OneToOneField(to="trustd.TrustdDevice", on_delete=models.CASCADE, null=True, blank=True)
    app_install = models.OneToOneField(
        AppInstall, on_delete=models.CASCADE, null=True, blank=True, help_text="Once the app is installed, this will be populated, completing the enrolment"
    )

    class Meta:
        verbose_name = "App Invitation"
        verbose_name_plural = "App Invitations"

    def __str__(self):
        return f"CAP Invitation sent to {self.app_user} for {self.get_app_type_display()}"

    @classmethod
    def get_mobile_app_type(cls, app_version: str) -> int:
        """ Return mobile app type depending on if it is CyberSmart powered by Trustd or Trustd beta """
        # CyberSmart mobile app has a suffixed version 13.2.0_cybersmart
        # During pre-enrolment sometimes we do not have the app version, so we set the default to mobile
        if not app_version or "_cybersmart" in app_version.lower():
            return cls.APP_TYPES.MOBILE
        return cls.APP_TYPES.TRUSTD_BETA
