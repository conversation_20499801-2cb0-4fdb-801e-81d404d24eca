import logging
from typing import List
from django.db import models, connection
from django.contrib.postgres.fields import ArrayField
from model_utils.models import TimeStampedModel

from appusers.models import AppInstall

from vulnerabilities.utils import OPSWAT_SOURCE, REGULAR_SOURCE
from software_inventory.models.mixins import OpswatMetadataMixin

logger = logging.getLogger(__name__)


class InstalledSoftwareAppInstallIndividual(OpswatMetadataMixin, TimeStampedModel, models.Model):
    """
    A Django model to store individual installed software records at the App Install level.

    This model uses the same query as InstalledSoftwareAppInstallSummary materialized view
    but allows for individual record updates through async jobs.

    The unique constraint is on app_install_id, product_lc, and version_lc to match
    the materialized view's grouping.
    """
    # Composite key fields
    id = models.CharField(verbose_name='ID', max_length=266, primary_key=True, unique=True, db_index=True)
    source = models.CharField(
        verbose_name="Source System", max_length=10, choices=[(REGULAR_SOURCE, "Regular"), (OPSWAT_SOURCE, "OPSWAT")]
    )
    source_id = models.CharField(verbose_name="Source ID", max_length=255)

    # Foreign key to AppInstall
    app_install = models.ForeignKey(
        "appusers.AppInstall", related_name="installed_software_individual", on_delete=models.CASCADE
    )

    # Product information
    vendor = models.CharField(verbose_name="Product Vendor", max_length=1255, null=True, blank=True)
    product = models.CharField(verbose_name="Product Name", db_index=True, max_length=1255)
    version = models.CharField(verbose_name="Product Version", max_length=255)

    # Lowercase versions for uniqueness constraint
    product_lc = models.CharField(verbose_name="Product Name Lowercase", max_length=1255, db_index=True)
    version_lc = models.CharField(verbose_name="Product Version Lowercase", max_length=1255, db_index=True)

    # Additional fields
    mobile_app = models.BooleanField(verbose_name="Wrapped iOS mobile app for M1 macOS", default=False)
    is_vulnerable = models.BooleanField(verbose_name="Is Vulnerable", default=False)
    freq = models.PositiveIntegerField(verbose_name="Frequency", default=0)
    cve_count = models.PositiveIntegerField(verbose_name="CVE Count", default=0)
    highest_severity = models.FloatField(verbose_name="Highest Severity", default=0.0)

    # Signatures field to store product signatures (array of integers)
    signatures = ArrayField(
        base_field=models.IntegerField(),
        verbose_name="Product Signatures", default=list, blank=True,
                                help_text="Array of signature IDs associated with this software")

    opswat_product_patch_installer = models.ForeignKey(
        "opswat_patch.OpswatProductPatchInstaller",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="installed_software_app_install_individual",
        help_text="The matching OPSWAT patch installer for this installed software." \
        "This can change depending on new OPSWAT data, so is recomputed after every software scan on CAP and data sync from OPSWAT."
    )
    patch_detected_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Date when this patch was first detected for this installed software. Changes dynamically similar to opswat_product_patch_installer."
    )

    class Meta:
        app_label = "appusers"
        verbose_name = "Installed Software Individual [AppInstall level]"
        verbose_name_plural = "Installed Software Individuals [AppInstall level]"
        unique_together = ['app_install', 'product_lc', 'version_lc']
        indexes = [
            models.Index(fields=['app_install', 'product_lc', 'version_lc']),
        ]

    def __str__(self):
        return f"({self.id}) {self.vendor}, {self.product}, {self.version}, CVEs: {self.cve_count}"

    @classmethod
    def refresh(cls):
        """
        Simulates the materialized view refresh process.
        Mostly for unit testing.
        """
        for app_install in AppInstall.objects.all():
            InstalledSoftwareAppInstallIndividual.update_individual_record(
                app_install.id,
            )

    @classmethod
    def refresh_async(cls):
        """
        Simulates the materialized view refresh process.
        Mostly for unit testing.
        """
        from appusers.tasks import update_installed_software_individual
        for app_install in AppInstall.objects.all():
            update_installed_software_individual.delay(app_install.id)

    @classmethod
    def update_individual_record(cls, app_install_id: int | None = None, organisation_id: int | None = None):
        """
        Update records based on AppInstall ID or Organisation ID.
        Fetches all relevant software records for the given scope (a single AppInstall or all AppInstalls
        within an Organisation) and synchronizes them with the database.
        This involves creating new records, updating existing ones if they've changed,
        and deleting records that are no longer reported for the scope.
        """
        if not (app_install_id is None) ^ (organisation_id is None): # XOR check
            raise ValueError("Either app_install_id or organisation_id must be provided, but not both.")

        # The SQL query is adapted to filter active_installs by app_install_id or organisation_id
        sql_query_template = """
        WITH active_installs AS (
            SELECT
                ai.id                AS app_install_id,
                au.organisation_id   -- kept for possible debugging; not used later
            FROM   appusers_appinstall ai
            JOIN   appusers_appuser   au ON au.id = ai.app_user_id
            WHERE  au.active
              AND  NOT ai.inactive
              {active_installs_filter_clause} -- Either app_install_id or organisation_id filter
        ),

        /* Filter OPSWAT software to only those in active_installs */
        opswat_cve_software_ids AS (
            SELECT DISTINCT ippv.product_version_id
            FROM   active_installs                    ai
            JOIN   opswat_installedproduct            ip   ON ip.app_install_id = ai.app_install_id
            JOIN   opswat_installedproductversion     ippv ON ippv.installed_product_id = ip.id
        ),

        /* OPSWAT vulnerability roll-up (per product-version) */
        opswat_cve_stats AS (
            SELECT
                cpv.productversion_id,
                COUNT(cve.id)               ::int     AS cve_count,
                MAX(cve.severity_index / 10.0)::float  AS highest_severity,
                TRUE                                     AS is_vulnerable
            FROM   opswat_cve_product_version cpv
            JOIN   opswat_cve_software_ids      ocsi ON ocsi.product_version_id = cpv.productversion_id
            JOIN   opswat_cve                  cve ON cve.id = cpv.cve_id
            GROUP  BY cpv.productversion_id
        ),

        /* Filter regular software to only those in active_installs */
        regular_cve_software_ids AS (
            SELECT DISTINCT aos.software_id
            FROM   active_installs                    ai
            JOIN   appusers_appreport                 ar  ON ar.app_install_id = ai.app_install_id
            JOIN   appusers_apposinstalledsoftware    aos ON aos.report_id = ar.id
        ),

        /* regular-source vulnerability roll-up (per software-package) */
        regular_cve_stats AS (
            SELECT
                sp.id                                    AS software_id,
                COUNT(cver_repo.id)           ::int      AS cve_count,
                MAX(cver_repo.base_score)::float   AS highest_severity,
                TRUE                                     AS is_vulnerable
            FROM   appusers_softwarepackagecves               spc
            JOIN   regular_cve_software_ids                   rcsi ON rcsi.software_id = spc.software_id
            JOIN   appusers_softwarepackagecvescverepository  spcr
                   ON spcr.software_package_cves_id = spc.id
            JOIN   vulnerabilities_cverepository              cver_repo
                   ON cver_repo.id = spcr.cve_repository_id
            JOIN   appusers_softwarepackage                   sp
                   ON sp.id = spc.software_id
            GROUP  BY sp.id
        ),

        /* one row per (install, product, version) coming from OPSWAT */
        opswat_rows AS (
            SELECT
                ai.app_install_id,
                'opswat'                                    AS source,
                pv.id::varchar                              AS source_id,
                (ai.app_install_id || ':opswat:' || pv.id)  AS id,
                pvdr.name                                   AS vendor,
                p.name                                      AS product,
                LOWER(p.name)                               AS product_lc,
                pv.raw_version                              AS version,
                LOWER(pv.raw_version)                       AS version_lc,
                FALSE                                       AS mobile_app,
                1                                           AS freq,
                COALESCE(cs.is_vulnerable, FALSE)           AS is_vulnerable,
                COALESCE(cs.cve_count, 0)                   AS cve_count,
                COALESCE(cs.highest_severity, 0)            AS highest_severity,
                ARRAY_AGG(DISTINCT CASE WHEN ippv.signature_id IS NOT NULL THEN ippv.signature_id::integer ELSE NULL END) FILTER (WHERE ippv.signature_id IS NOT NULL) AS signatures
            FROM   active_installs           ai
            JOIN   opswat_installedproduct   ip   ON ip.app_install_id      = ai.app_install_id
            JOIN   opswat_installedproductversion ippv
                   ON ippv.installed_product_id = ip.id
            JOIN   opswat_productversion     pv   ON pv.id                  = ippv.product_version_id
            JOIN   opswat_product            p    ON p.id                   = pv.product_id
            JOIN   opswat_productvendor      pvdr ON pvdr.id                = p.vendor_id
            LEFT   JOIN opswat_cve_stats     cs   ON cs.productversion_id   = pv.id
            GROUP  BY ai.app_install_id,
                     pv.id, pvdr.name, p.name, pv.raw_version,
                     cs.is_vulnerable, cs.cve_count, cs.highest_severity
        ),

        /* does a given install have *any* OPSWAT rows? */
        opswat_exists AS (
            SELECT DISTINCT app_install_id FROM opswat_rows
        ),

        /* rows coming from "regular" inventory, after collision filtering */
        regular_rows AS (
            SELECT
                ai.app_install_id,
                'regular'                                   AS source,
                sp.id::varchar                              AS source_id,
                (ai.app_install_id || ':regular:' || sp.id) AS id,
                sp.vendor                                   AS vendor,
                sp.product                                  AS product,
                LOWER(sp.product)                           AS product_lc,
                sp.version                                  AS version,
                LOWER(sp.version)                           AS version_lc,
                sp.mobile_app                               AS mobile_app,
                1                                           AS freq,
                CASE WHEN oe.app_install_id IS NOT NULL
                     THEN FALSE
                     ELSE COALESCE(rcs.is_vulnerable, FALSE)
                END                                         AS is_vulnerable,
                CASE WHEN oe.app_install_id IS NOT NULL
                     THEN 0
                     ELSE COALESCE(rcs.cve_count, 0)
                END                                         AS cve_count,
                CASE WHEN oe.app_install_id IS NOT NULL
                     THEN 0
                     ELSE COALESCE(rcs.highest_severity, 0)
                END                                         AS highest_severity,
                ARRAY[]::integer[]                          AS signatures
            FROM   active_installs                ai
            JOIN   appusers_appreport             ar   ON ar.app_install_id = ai.app_install_id
            JOIN   appusers_apposinstalledsoftware aos ON aos.report_id     = ar.id
            JOIN   appusers_softwarepackage       sp   ON sp.id            = aos.software_id
            LEFT   JOIN regular_cve_stats         rcs  ON rcs.software_id   = sp.id
            LEFT   JOIN opswat_exists             oe   ON oe.app_install_id = ai.app_install_id
            /* drop rows that EXACTLY collide with an OPSWAT product+version */
            WHERE  NOT EXISTS (
                     SELECT 1
                     FROM   opswat_rows orw
                     WHERE  orw.app_install_id = ai.app_install_id
                       AND  orw.product_lc     = LOWER(sp.product)
                       AND  orw.version_lc     = LOWER(sp.version)
                   )
            GROUP  BY ai.app_install_id,
                     sp.id, sp.vendor, sp.product, sp.version, sp.mobile_app,
                     oe.app_install_id,
                     rcs.is_vulnerable, rcs.cve_count, rcs.highest_severity
        ),

        /* union the per-row sets */
        combined AS (
            SELECT * FROM opswat_rows
            UNION ALL
            SELECT * FROM regular_rows
        )

        /* final roll-up: one line per (app_install, product, version) with proper CVE semantics */
        SELECT
            STRING_AGG(DISTINCT id, ',')                    AS id,
            app_install_id,
            MIN(vendor)                                     AS vendor,
            MIN(product)                                    AS product,
            MIN(version)                                    AS version,
            COUNT(*)                                        AS freq,          -- total hits
            STRING_AGG(DISTINCT source,    ',')             AS source,
            STRING_AGG(DISTINCT source_id, ',')             AS source_id,
            BOOL_OR(mobile_app)                             AS mobile_app,
            product_lc,
            version_lc,
            CASE
                WHEN BOOL_OR(source = 'opswat')
                     THEN MAX(CASE WHEN source = 'opswat'
                                   THEN highest_severity
                                   ELSE 0 END)
                ELSE MAX(highest_severity)
            END                                             AS highest_severity,
            CASE
                WHEN BOOL_OR(source = 'opswat')
                     THEN SUM(CASE WHEN source = 'opswat'
                                   THEN cve_count
                                   ELSE 0 END)
                ELSE SUM(cve_count)
            END                                             AS cve_count,
            CASE
                WHEN BOOL_OR(source = 'opswat')
                     THEN BOOL_OR(CASE WHEN source = 'opswat'
                                       THEN is_vulnerable
                                       ELSE FALSE END)
                ELSE BOOL_OR(is_vulnerable)
            END                                             AS is_vulnerable,
            coalesce(min(signatures) filter(where signatures <> '{{}}'), '{{}}') as signatures
        FROM   combined
        GROUP  BY
            app_install_id,
            product_lc,
            version_lc;
        """

        params = []
        active_installs_filter_sql = ""
        if app_install_id:
            active_installs_filter_sql = "AND ai.id = %s"
            params.append(app_install_id)
        elif organisation_id:
            active_installs_filter_sql = "AND au.organisation_id = %s"
            params.append(organisation_id)

        formatted_sql = sql_query_template.format(
            active_installs_filter_clause=active_installs_filter_sql,
        )

        with connection.cursor() as cursor:
            cursor.execute(formatted_sql, params)
            columns = [col[0] for col in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        # Fetch existing records for the current scope
        existing_records_qs = cls.objects.all()
        if app_install_id:
            existing_records_qs = existing_records_qs.filter(app_install_id=app_install_id)
        elif organisation_id:
            existing_records_qs = existing_records_qs.filter(app_install__app_user__organisation_id=organisation_id)

        existing_records_map = {record.id: record for record in existing_records_qs}
        # Also create a map by unique constraint fields for duplicate detection
        existing_unique_map = {
            (record.app_install_id, record.product_lc, record.version_lc): record
            for record in existing_records_qs
        }

        records_to_create_instances = []
        records_to_update_instances = []
        records_to_delete_for_id_change = []  # Track records that need deletion due to ID change

        # Fields on the model that are populated from the query data
        model_fields_from_data = [
            'app_install_id', 'source', 'source_id', 'vendor', 'product', 'version',
            'product_lc', 'version_lc', 'mobile_app', 'is_vulnerable', 'freq',
            'cve_count', 'highest_severity', 'signatures'
        ]

        query_result_ids = set()

        if not results:
            # All existing records for this scope should be deleted if query returns nothing
            if existing_records_map:
                cls.objects.filter(id__in=list(existing_records_map.keys())).delete()
            return []

        for data_row in results:
            record_id_from_query = data_row['id']
            query_result_ids.add(record_id_from_query)

            create_or_update_kwargs = {'id': record_id_from_query}
            for field_name in model_fields_from_data:
                create_or_update_kwargs[field_name] = data_row[field_name]

            # Check for existing record by unique constraint fields
            unique_key = (
                data_row['app_install_id'],
                data_row['product_lc'],
                data_row['version_lc']
            )

            existing_record = None

            if record_id_from_query in existing_records_map:
                existing_record = existing_records_map[record_id_from_query]
            elif unique_key in existing_unique_map:
                # Found existing record with same unique constraint but different ID
                existing_record = existing_unique_map[unique_key]
                # Mark old record for deletion and create new one with correct ID
                records_to_delete_for_id_change.append(existing_record.id)
                # Don't track the old ID in query_result_ids so it gets deleted
                # Create new record with the correct ID
                records_to_create_instances.append(cls(**create_or_update_kwargs))
                continue

            if existing_record:
                has_changed = False
                for field_name in model_fields_from_data:
                    if str(getattr(existing_record, field_name)) != str(data_row[field_name]):
                        setattr(existing_record, field_name, data_row[field_name])
                        has_changed = True
                if has_changed:
                    records_to_update_instances.append(existing_record)
            else:
                records_to_create_instances.append(cls(**create_or_update_kwargs))

        # Perform bulk database operations
        # IMPORTANT: Delete records first to avoid constraint violations when creating new ones
        ids_in_db_for_scope = set(existing_records_map.keys())
        ids_to_delete = ids_in_db_for_scope - query_result_ids
        # Also include records marked for deletion due to ID changes
        ids_to_delete = ids_to_delete.union(set(records_to_delete_for_id_change))
        if ids_to_delete:
            cls.objects.filter(id__in=list(ids_to_delete)).delete()

        if records_to_create_instances:
            cls.objects.bulk_create(records_to_create_instances, ignore_conflicts=False)

        if records_to_update_instances:
            cls.objects.bulk_update(records_to_update_instances, model_fields_from_data)

        all_affected_records = records_to_update_instances + records_to_create_instances

        if app_install_id:
            cls._populate_with_opswat_product_patch_installer(all_affected_records, app_install_id)
        elif organisation_id:
            # This is not a common use-case, so do it one by one
            for app_install in AppInstall.objects.filter(
                app_user__organisation_id=organisation_id,
            ):
                cls._populate_with_opswat_product_patch_installer(all_affected_records, app_install.id)


        return all_affected_records

    @classmethod
    def _populate_with_opswat_product_patch_installer(cls, records: List['InstalledSoftwareAppInstallIndividual'], app_install_id: int):
        """
        Enrich the records with OPSWAT patching data.
        """
        if not records or not app_install_id:
            return

        # Filter to only OPSWAT records as we can patch the Opswat records
        opswat_records = [r for r in records if r.source == OPSWAT_SOURCE]
        if not opswat_records:
            return

        from opswat_patch.utils import calculate_enrich_installed_software_with_installers

        enriched_records = calculate_enrich_installed_software_with_installers(opswat_records)

        records_to_update = []
        for record in enriched_records:
            # Check if record has enrichment data
            installer_data = getattr(record, 'app_install_product_installer', None)
            if not installer_data:
                # Clear existing enrichment if no installer found
                if record.opswat_product_patch_installer_id or record.patch_detected_date:
                    record.opswat_product_patch_installer = None
                    record.patch_detected_date = None
                    records_to_update.append(record)
                continue

            # Extract installer ID and patch detected date from enrichment data
            installer_id = installer_data.get('app_install_product_installer_id')
            patch_detected_date = installer_data.get('patch_detected_date')

            if (record.opswat_product_patch_installer_id == installer_id and
                record.patch_detected_date == patch_detected_date):
                continue

            record.opswat_product_patch_installer_id = installer_id
            record.patch_detected_date = patch_detected_date
            records_to_update.append(record)

        if records_to_update:
            cls.objects.bulk_update(
                records_to_update,
                ['opswat_product_patch_installer', 'patch_detected_date']
            )
