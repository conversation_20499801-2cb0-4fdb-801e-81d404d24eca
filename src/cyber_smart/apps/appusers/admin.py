import json

from admin_auto_filters.filters import AutocompleteFilterFactory
from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.db import transaction
from django.http import HttpResponse, HttpResponseRedirect
from django.template.defaultfilters import truncatechars
from django.urls import path, reverse
from more_admin_filters import MultiSelectDropdownFilter

from appusers.appbuild_admin import AppBuildVersionAdminCustom
from appusers.models import (
    AppFile, AppInstall, AppInstallActivationLog, AppInstallCheckStatus, AppInstallConfirmedEmail,
    AppInstallNetworkInterface, AppInstallOSUser, AppInstallPolicyAgreement,
    AppOSInstalledSoftware, AppReport, AppUser, AppUserLog, CheckAutoFix, CheckInAppResult,
    CheckManualFix, CheckResult, DeviceManufacturer, DeviceModel, SoftwarePackage,
    SoftwarePackageCVEs, SoftwarePackageCPE, UserLoginHistory, AppInvitation, AppVersion,
    InstalledSoftwarePartnerSummary,
    InstalledSoftwareAppInstallIndividual
)
from beta_features.admin import IsBetaFilter
from common.mixins import TurboDeleteAdminMixin, DeactivateSelectedDevicesAdminMixin
from common.models import SimpleHistoryAdminCustom, Group
from common.utils import is_technical_support
from organisations.models import Organisation
from .tasks import (
    check_for_recent_vulnerabilities,
    generate_cpe_and_check_for_cves
)


class CustomMultiSelectDropdownFilter(MultiSelectDropdownFilter):
    def choices(self, changelist):
        query_string = changelist.get_query_string({}, [self.lookup_kwarg, self.lookup_kwarg_isnull])
        yield {
            'selected': not self.lookup_vals and self.lookup_val_isnull is None,
            'query_string': query_string,
            'display': 'All',
        }
        self.lookup_choices = dict(self.field.choices).keys()
        include_none = False
        for val in self.lookup_choices:
            if val is None:
                include_none = True
                continue

            val = str(val)
            qval = self.prepare_querystring_value(val)
            yield {
                'selected': qval in self.lookup_vals,
                'query_string': query_string,
                'display': dict(AppInstall.DEVICE_TYPES)[int(val)],
                'value': val,
                'key': self.lookup_kwarg,
            }
        if include_none:
            yield {
                'selected': bool(self.lookup_val_isnull),
                'query_string': query_string,
                'display': self.empty_value_display,
                'value': 'True',
                'key': self.lookup_kwarg_isnull,
            }


class AppUserAdminForm(forms.ModelForm):
    class Meta:
        model = AppUser
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and hasattr(self.instance, "organisation"):
            self.fields["groups"].queryset = Group.objects.filter(organisation=self.instance.organisation)


@admin.register(AppUser)
class AppUserAdmin(TurboDeleteAdminMixin, SimpleHistoryAdminCustom):
    list_display = ('organisation', 'uuid', 'first_name', 'last_name', 'email',
                    'is_admin', 'active', 'created', 'modified')
    list_filter = [AutocompleteFilterFactory('Organisation', 'organisation'), 'active']
    search_fields = ['organisation__name', 'first_name', 'last_name', 'email', 'uuid']
    raw_id_fields = ['organisation']
    delete_selected_confirmation_template = 'admin/delete_selected_confirmation-custom.html'
    actions = ['skip_academy_enrollment']
    form = AppUserAdminForm

    @admin.action(description='Skips LMS academy enrollment for selected app users')
    def skip_academy_enrollment(self, request, queryset):
        org_ids = list(queryset.values_list('organisation_id', flat=True).distinct())
        # check orgs have academy enabled
        for organisation in Organisation.objects.filter(id__in=org_ids):
            if not organisation.learn_lite_tab_displayed:
                self.message_user(
                    request,
                    f"Organisation {organisation.name} does not have academy enabled, "
                    "please select users from organisations with academy fully enabled",
                    messages.ERROR
                )
                return
        updated = queryset.update(lms_enrollment_status=AppUser.STATUS.LMS_SKIPPED_ENROLLMENT)
        self.message_user(
            request,
            f"Skipped academy enrollment for {updated} app users",
            messages.SUCCESS
        )

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = ['created', 'modified', 'uuid', "lms_enrollment_status_changed"]
        if request.user.is_superuser:
            return readonly_fields

        readonly_fields = set(
            [field.name for field in self.opts.local_fields] +
            [field.name for field in self.opts.local_many_to_many]
        )
        readonly_fields = readonly_fields.difference(['id', 'email'])
        if is_technical_support(request.user):
            return readonly_fields.difference(['organisation', 'is_admin', 'active'])
        return readonly_fields


class AppInstallOSUserInline(admin.StackedInline):
    model = AppInstallOSUser
    extra = 0
    fields = ('username', 'domain', 'agreed_policies_count', 'is_admin_account')

    def get_readonly_fields(self, request, obj=None):
        return self.fields

    def has_add_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        if request.user.is_superuser:
            return True
        return False


class AppInstallConfirmedEmailInline(admin.StackedInline):
    model = AppInstallConfirmedEmail
    extra = 0
    fields = ("email",)


class OpswatOperatingSystemInline(admin.StackedInline):
    from opswat.models import OpswatOperatingSystem
    model = OpswatOperatingSystem
    extra = 0
    fields = ('os_id', 'os_type', 'architecture', 'created', 'modified')
    readonly_fields = ('created', 'modified')

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(AppInstall)
class AppInstallAdmin(TurboDeleteAdminMixin, DeactivateSelectedDevicesAdminMixin, SimpleHistoryAdminCustom):
    list_display = ('app_user', 'os', 'app_user__organisation', 'get_uuid', 'device_id', 'hostname', 'machine_vendor',
                    'machine_model', 'app_version', "version", 'serial_number', 'caption', 'platform',
                    'release', 'created', 'modified', 'last_check_in', 'inactive', 'installer', 'is_beta',
                    'supports_opswat_patch_display')
    list_filter = [AutocompleteFilterFactory('Organisation', 'app_user__organisation'),
                   AutocompleteFilterFactory('Partner', 'app_user__organisation__partner'),
                   AutocompleteFilterFactory('Distributor', 'app_user__organisation__partner__distributor'),
                   AutocompleteFilterFactory("App Version", "version"),
                   ("device_type", CustomMultiSelectDropdownFilter),
                   ('os__title', MultiSelectDropdownFilter), ('caption', MultiSelectDropdownFilter),
                   ('app_version', MultiSelectDropdownFilter),
                   'platform', 'installer', 'inactive',
                   'app_user__active', 'app_user__organisation__is_test',
                   'app_user__organisation__partner__trial_account', IsBetaFilter]
    search_fields = [
        'app_user__email', 'app_user__first_name', 'app_user__last_name', 'device_id', 'hostname', 'app_version',
        'serial_number', 'app_user__organisation__name', "network_interfaces__mac_address",
    ]
    fields = [
        'app_user', 'os', "device_type", 'os_info', 'hostname', 'caption', 'platform', 'release', 'os_release',
        'device_id', 'serial_number', 'created', 'modified', 'last_check_in', 'app_version', "version",
        'inactive', 'date_removed', 'installer', 'system_info', 'end_of_life', 'is_beta', 'real_device_id',
        'supports_opswat_patch_display'
    ]
    inlines = (AppInstallOSUserInline, AppInstallConfirmedEmailInline, OpswatOperatingSystemInline)
    raw_id_fields = ('app_user', 'os')
    # delete_selected_confirmation_template = 'admin/delete_selected_confirmation-custom.html'

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('reports').select_related('end_of_life')
        return queryset

    def app_user__organisation(self, obj):
        return obj.app_user.organisation

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = ['created', 'modified', 'date_removed', 'last_check_in', 'serial_number', 'end_of_life', 'is_beta', 'supports_opswat_patch_display']
        if is_technical_support(request.user):
            readonly_fields = set(
                [field.name for field in self.opts.local_fields] +
                [field.name for field in self.opts.local_many_to_many] +
                ['last_check_in', 'supports_opswat_patch_display']
            )
            return readonly_fields.difference(['id', 'inactive'])
        return readonly_fields

    @admin.display(
        description="Supports OPSWAT Patch",
        boolean=True
    )
    def supports_opswat_patch_display(self, obj):
        """
        Returns whether the app install supports OPSWAT patching functionality.
        """
        return obj.supports_opswat_patch()


@admin.register(AppInstallActivationLog)
class AppInstallActivationLogAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = (
        'device_id', 'serial_number', 'get_unique_token', 'app_install', 'status', 'endpoint', 'inactive_status',
        'last_check_in_log', 'created'
        )
    list_filter = [
        AutocompleteFilterFactory('App Install', 'app_install'),
        AutocompleteFilterFactory('App User', 'app_install__app_user'),
        'status', 'endpoint', 'inactive_status'
        ]
    search_fields = ['device_id', 'serial_number', 'registration_unique_token']
    readonly_fields = [field.name for field in AppInstallActivationLog._meta.fields]

    def get_unique_token(self, obj):
        return truncatechars(obj.registration_unique_token, 20)


@admin.register(AppUserLog)
class AppUserLogAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('app_user', 'device_id', 'hostname', 'caption', 'platform',
                    'release', 'created', 'has_error', 'error_text')
    list_filter = [AutocompleteFilterFactory('App User', 'app_user'), 'has_error']
    search_fields = ['app_user__email', 'app_user__first_name', 'app_user__last_name', 'device_id']
    readonly_fields = ['created', 'modified']
    raw_id_fields = ['app_user']


@admin.register(AppInstallCheckStatus)
class AppInstallCheckStatusAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('app_install', 'app_check', 'passing', 'status_changed')
    list_filter = [AutocompleteFilterFactory('App User', 'app_install__app_user'), 'passing']
    search_fields = ['app_install__device_id', 'app_install__app_user__email']
    readonly_fields = ['app_install', 'app_check', 'passing', 'status_changed', 'modified']


@admin.register(AppReport)
class AppReportAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = (
        'app_install', 'get_app_user', 'app_version', 'total_commands', 'total_responses', 'total_passed_responses',
        'total_failed_responses', 'total_manual_fix', 'total_manual_fix_left', 'total_passed',
        'total_failed', 'pass_percentage', 'created', 'modified'
    )
    list_filter = [AutocompleteFilterFactory('App User', 'app_install__app_user')]
    search_fields = ['app_install__device_id', 'app_install__app_user__email', 'app_version']
    readonly_fields = ['created', 'modified']
    raw_id_fields = ['app_install']

    @admin.display(
        description='App User'
    )
    def get_app_user(self, obj):
        return obj.app_install.app_user



@admin.register(CheckResult)
class CheckResultAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('report', 'app_check', 'report_only', 'response', 'response_text', 'created')
    list_filter = ['report_only', 'app_check']
    search_fields = ['app_check__title', 'report__app_install__device_id']
    readonly_fields = ['report', 'app_check', 'report_only', 'response_text', 'created', 'modified']


@admin.register(CheckInAppResult)
class CheckInAppResultAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('report', 'app_check', 'product_name', 'product_state',
                    'product_status', 'product_state_timestamp', 'created')
    list_filter = ['app_check']
    search_fields = ['app_check__title', 'report__app_install__device_id']
    readonly_fields = ['report', 'app_check', 'created', 'modified']


@admin.register(CheckManualFix)
class CheckManualFixAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('app_install', 'app_check', 'user', 'bulk_fix', 'created')
    list_filter = [
        AutocompleteFilterFactory('App User', 'app_install__app_user'),
        AutocompleteFilterFactory('User', 'user'),
        'bulk_fix', 'app_check'
        ]
    search_fields = ['app_install__device_id', ]
    readonly_fields = ['created', 'modified']
    raw_id_fields = ['app_install', 'app_check', 'user']


@admin.register(CheckAutoFix)
class CheckAutoFixAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('app_install', 'app_check', 'user', 'bulk_fix', 'created')
    list_filter = [
        AutocompleteFilterFactory('App User', 'app_install__app_user'),
        AutocompleteFilterFactory('User', 'user'),
        'bulk_fix', 'app_check'
        ]
    search_fields = ['app_install__device_id', ]
    readonly_fields = ['created', 'modified']
    raw_id_fields = ('app_install', 'app_check', 'user')


@admin.register(AppInstallOSUser)
class AppInstallOSUserAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('app_install', 'name', 'domain', 'is_admin_account', 'agreed_policies_count', 'created')
    list_filter = [AutocompleteFilterFactory('App User', 'app_install__app_user'), 'is_admin_account']
    search_fields = ['username', 'domain', 'app_install__device_id']
    readonly_fields = ['app_install', 'username', 'domain', 'is_admin_account', 'agreed_policies_count', 'created', 'modified']
    raw_id_fields = ('app_install',)

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(AppInstallNetworkInterface)
class AppInstallNetworkInterfaceAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('app_install', 'get_app_user', 'mac_address', 'created')
    raw_id_fields = ('app_install',)
    list_filter = [AutocompleteFilterFactory('App User', 'app_install__app_user')]
    search_fields = ['app_install__device_id', 'app_install__app_user__email']
    readonly_fields = ['created', 'modified']

    @admin.display(
        description='App User'
    )
    def get_app_user(self, obj):
        return obj.app_install.app_user



@admin.register(AppInstallPolicyAgreement)
class AppInstallPolicyAgreementAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('version', 'read_date', 'agreed_date')
    list_filter = [AutocompleteFilterFactory('App User', 'app_install__app_user')]
    search_fields = ['version__policy__name', 'app_install__device_id']
    raw_id_fields = ['version', 'app_install', 'os_user']
    readonly_fields = ['created', 'modified']


@admin.register(AppOSInstalledSoftware)
class AppOSInstalledSoftwareAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('report', 'software', 'date_installed', 'created')
    raw_id_fields = ['report', 'software']
    readonly_fields = ['created', 'modified']


@admin.register(SoftwarePackage)
class SoftwarePackageAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('product', 'vendor', 'version', 'created')
    list_filter = ('mobile_app',)
    search_fields = ['vendor', 'product', 'version']
    readonly_fields = ['created', 'modified']
    actions = ['run_cpe_and_cve_matcher']

    @admin.action(description='Run CPE & CVE matcher task for selected software.')
    def run_cpe_and_cve_matcher(self, request, queryset):
        generate_cpe_and_check_for_cves.delay(
            software_ids=list(queryset.values_list('id', flat=True))
        )

        self.message_user(
            request,
            "'CPE & CVE matcher' task successfully started.",
            messages.SUCCESS
        )


@admin.register(SoftwarePackageCPE)
class SoftwarePackageCPEAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('software', 'cpe', 'created', 'modified', 'cve_matcher_failed')
    search_fields = ['software__vendor', 'software__product', 'software__version']
    raw_id_fields = ['software']
    readonly_fields = ['created', 'modified']
    actions = ['run_check_for_vulerabilities', 'run_check_for_recent_vulerabilities']

    @admin.action(description='Check for recent vulnerabilities for selected CPEs.')
    def run_check_for_recent_vulerabilities(self, request, queryset):
        for cpe in queryset:
            check_for_recent_vulnerabilities.delay(
                software_id=cpe.software_id,
                cpe=cpe.cpe
            )

        self.message_user(
            request,
            "'Check for recent vulnerabilities' task successfully started.",
            messages.SUCCESS
        )


@admin.register(SoftwarePackageCVEs)
class SoftwarePackageCVEsAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('software', 'cves', 'created', 'modified')
    search_fields = ['software__vendor', 'software__product', 'software__version']
    raw_id_fields = ['software']
    readonly_fields = ['created', 'modified']


@admin.register(DeviceManufacturer)
class DeviceManufacturerAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('name', 'is_supported', 'created')
    list_filter = ['is_supported']
    readonly_fields = ['created', 'modified']


@admin.register(DeviceModel)
class DeviceModelAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('model', 'device_type', 'manufacturer', 'is_supported', 'created')
    list_filter = ['is_supported', 'manufacturer']
    readonly_fields = ['created', 'modified']


@admin.register(UserLoginHistory)
class UserLoginHistoryAdmin(TurboDeleteAdminMixin, admin.ModelAdmin):
    list_display = ('app_install', 'user', 'created')
    raw_id_fields = ['app_install', 'user']
    search_fields = ['app_install__device_id', 'app_install__app_user__email']
    readonly_fields = ['created', 'modified']


@admin.register(AppFile)
class AppFileAdmin(AppBuildVersionAdminCustom, TurboDeleteAdminMixin, SimpleHistoryAdminCustom):
    list_display = ("version", "type", "url", "file", "created", "is_download_version", "is_beta", "cd_pipeline_version")
    list_filter = ("type", "version", "is_download_version")
    search_fields = ("type", "version", "url")
    readonly_fields = ['created', 'modified', 'is_beta', 'cd_pipeline_version', 'is_download_version']
    actions = ['download_json']

    @staticmethod
    def get_app_files_details(queryset):
        app_files = []
        ignore_keys = ['_state', 'id', 'created', 'modified']
        for app_file in queryset.all():
            to_append = app_file.__dict__.copy()
            for key in ignore_keys:
                to_append.pop(key, None)
            if to_append.get('file'):
                to_append['file'] = app_file.file.name if app_file.file else None
            app_files.append(to_append)
        return app_files

    @admin.action(permissions=['view'], description='Download JSON to upload in new env')
    def download_json(self, request, queryset):
        app_files = self.get_app_files_details(queryset)
        response = HttpResponse(content_type='application/json')
        response['Content-Disposition'] = 'attachment; filename="app_files.json"'
        response.write(json.dumps(app_files, indent=4))
        return response

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('upload-json/', self.admin_site.admin_view(self.upload_json_view), name='appusers_appfile_upload_json'),
        ]
        return custom_urls + urls

    @staticmethod
    def create_app_files(data):
        created, updated = 0, 0
        for item in data:
            lookup = {
                'type': item['type'],
                'cd_pipeline_version': item.get('cd_pipeline_version', AppFile.CAP_V4_PIPELINE),
                'is_download_version': item.get('is_download_version', True)
            }
            obj, created_obj = AppFile.objects.update_or_create(defaults=item, **lookup)
            if created_obj:
                created += 1
            else:
                updated += 1
        return created, updated

    def upload_json_view(self, request):
        change_url = reverse('admin:appusers_appfile_changelist')
        if request.method == 'POST' and request.FILES.get('json_file'):
            json_file = request.FILES['json_file']
            try:
                data = json.load(json_file)
            except Exception as e:
                self.message_user(request, f"Error reading JSON file: {e}", messages.ERROR)
                return HttpResponseRedirect(change_url)
            try:
                with transaction.atomic():
                    created, updated = self.create_app_files(data)
                    self.message_user(request, f"Upload complete: {created} created, {updated} updated.", messages.SUCCESS)
            except Exception as e:
                self.message_user(request, f"Error creating AppFiles: {e}", messages.ERROR)
            return HttpResponseRedirect(change_url)
        return self.changelist_view(request)


@admin.register(AppInvitation)
class AppInvitationAdmin(admin.ModelAdmin):
    list_display = ('app_user', 'get_app_type_display', 'created', 'trustd_device', 'app_install')
    list_filter = [
        AutocompleteFilterFactory('AppUser', 'app_user'),
        'app_type'
    ]
    readonly_fields = ['modified', 'created']
    raw_id_fields = ['app_user', 'app_install', 'trustd_device']

    def has_change_permission(self, request, obj=None):
        return not settings.IS_PROD

    @admin.display(description="Application Type")
    def get_app_type_display(self, obj):
        return obj.get_app_type_display()


@admin.register(AppVersion)
class AppVersionAdmin(admin.ModelAdmin):
    list_display = ("join_version", "device_type", "app_install_count", "support_level", "version_channel")
    list_filter = ["device_type", "support_level", "version_channel", "major", "minor", "patch"]
    search_fields = ["major", "minor", "patch"]

    @admin.display(
        description="Version"
    )
    def join_version(self, obj) -> str:
        """
        Returns the joined version of the AppVersion object.
        """
        return f"{obj.major}.{obj.minor}.{obj.patch}"

    @admin.display(
        description="App Install Count"
    )
    def app_install_count(self, obj) -> int:
        """
        Returns the count of AppInstall objects related to the AppVersion object.
        """
        return obj.appinstall_set.count()

@admin.register(InstalledSoftwareAppInstallIndividual)
class InstalledSoftwareAppInstallIndividualAdmin(admin.ModelAdmin):
    list_display = (
        "source_id", "app_install_id", "get_app_install_device_id", "get_app_install_user_uuid",
        "get_organisation", "product", "version", "vendor", "is_vulnerable", "cve_count",
        "highest_severity", "source", "display_signatures"
    )
    list_filter = [
        AutocompleteFilterFactory("Organisation", "app_install__app_user__organisation"),
        AutocompleteFilterFactory("App Install", "app_install"),
        AutocompleteFilterFactory("App User", "app_install__app_user"),
        "is_vulnerable",
        "source",
        "highest_severity"
    ]
    search_fields = [
        "product", "vendor", "version", "app_install__device_id", "app_install__serial_number",
        "app_install__app_user__uuid", "app_install__app_user__organisation__name"
    ]

    def display_signatures(self, obj):
        """Display signatures as a comma-separated list"""
        if obj.signatures:
            return ", ".join(str(sig) for sig in obj.signatures)
        return "-"
    display_signatures.short_description = "Signatures"
    raw_id_fields = ["app_install"]

    def get_queryset(self, request):
        """Optimise by prefetching related objects to avoid N+1 queries."""
        queryset = super().get_queryset(request)
        return queryset.select_related(
            'app_install',
            'app_install__app_user',
            'app_install__app_user__organisation'
        )

    def get_app_install_device_id(self, obj) -> str:
        return obj.app_install.device_id
    get_app_install_device_id.short_description = "Device ID"

    def get_app_install_user_uuid(self, obj) -> str:
        return obj.app_install.app_user.uuid
    get_app_install_user_uuid.short_description = "App User"

    def get_organisation(self, obj) -> str:
        return obj.app_install.app_user.organisation.name
    get_organisation.short_description = "Organisation"




@admin.register(InstalledSoftwarePartnerSummary)
class InstalledSoftwarePartnerSummaryAdmin(admin.ModelAdmin):
    list_display = (
        "source_id", "partner", "product", "version", "vendor", "freq", "is_vulnerable", "cve_count",
        "highest_severity", "source", "display_signatures"
    )
    list_filter = [
        AutocompleteFilterFactory("Partner", "partner"),
        "is_vulnerable",
        "source",
        "highest_severity"
    ]
    search_fields = ["product", "vendor", "version", "partner__name"]
    raw_id_fields = ["partner"]

    def display_signatures(self, obj):
        """Display signatures as a comma-separated list"""
        if obj.signatures:
            return ", ".join(str(sig) for sig in obj.signatures)
        return "-"
    display_signatures.short_description = "Signatures"
