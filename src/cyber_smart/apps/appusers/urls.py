from django.urls import re_path as url
from django.views.decorators.csrf import csrf_exempt

from .views import (
    CreateFakeAppUsersView, FakeAppInstallCreateView, GenerateRandomAppUserView, InstalledSoftwareView,
    PackageDetailsView, PatchHistoryView, PatchSummaryView, PatchAttemptDetailsView, InstalledSoftwareExportDataView,
    PatchHistoryExportDataView
)

app_name = "appusers"

serial_segment = r"(?:/(?P<serial_number>[-A-Za-z0-9+=]{1,255}|=[^=]|={3,}))?"
device_base = r"^organisation/(?P<org_id>[0-9a-f-]{36})/(?P<user_uuid>[0-9a-f-]+)/device/(?P<device_id>[^/]+)"

urlpatterns = [
    url(r"^app-users/fake/(?P<org_id>[0-9a-f-]{36})/$",
        csrf_exempt(CreateFakeAppUsersView.as_view()),
        name="fake-app-users"),
    url(r'^app-users/random/(?P<org_id>[0-9a-f-]{36})/$',
        csrf_exempt(GenerateRandomAppUserView.as_view()),
        name="random-app-users"),
    url(r'^app-install/fake/(?P<app_user_uuid>[0-9a-f-]+)$',
        csrf_exempt(FakeAppInstallCreateView.as_view()),
        name="fake-app-install"
    ),
    url(rf'{device_base}{serial_segment}/installed-software/report/$', InstalledSoftwareExportDataView.as_view(), name='installed-software-report'),
    url(rf'{device_base}{serial_segment}/installed-software/$', InstalledSoftwareView.as_view(), name='installed-software'),
    url(rf'{device_base}{serial_segment}/patch-history/report/$', PatchHistoryExportDataView.as_view(), name='patch-history-report'),
    url(rf'{device_base}{serial_segment}/patch-history/$', PatchHistoryView.as_view(), name='patch-history'),
    url(rf'{device_base}{serial_segment}/package/(?P<package_id>[^/]+)/$', PackageDetailsView.as_view(), name='package-details'),
    url(rf'{device_base}{serial_segment}/patch-installer/(?P<attempt_id>\d+)/$', PatchAttemptDetailsView.as_view(), name='patch-installer-details'),
    url(rf'{device_base}{serial_segment}/patch-summary/$', PatchSummaryView.as_view(), name='patch-summary'),
]
