import json

import shortuuid
from django.forms import Form
from django.utils import timezone

from api.common.serializers import AppUserReportSerializer
from appusers.forms import FakeAppInstallForm
from appusers.models import (
    AppInstall,
    AppInstallOSUser,
    AppUser,
    InstalledSoftwareAppInstallIndividual,
    get_app_install,
)
from appusers.utils import generate_app_last_api_report, generate_app_registration_token
from common.utils import can_add_fakes, handle_user_input
from dashboard.utils import convert_serial_number
from django.contrib import messages
from django.core.exceptions import PermissionDenied, ValidationError
from django.core.validators import validate_email
from django.db.models.query import QuerySet
from django.http import HttpResponse, HttpResponseRedirect, JsonResponse
from django.utils.translation import gettext_lazy as _
from django.views.generic import FormView, View
from opswat_patch.models import OpswatPatchAttempt
from opswat_patch.utils import enrich_installed_software_with_installers
from organisations.models import get_organisations
from organisations.views import PatchHistoryExportDataView as OrganisationPatchHistoryExportDataView
from signup.mixins import SignupMixin
from software_inventory.forms import SchedulePatchInstallationForm
from software_inventory.views import (
    InstalledSoftwareBaseView, PackageBaseView, PatchHistoryBaseView, PatchSummaryBaseView,
    PatchAttemptDetailsBaseView, InstalledSoftwareExportDataBaseView
)
from vulnerabilities.utils import aggregate_installed_software_counts


class CreateFakeAppUsersView(SignupMixin, View):
    """
    Creates fake app users.
    """
    def post(self, request, org_id, *args, **kwargs):
        response = {
            "success": False, "error": "", "data": None
        }
        # restricted add fake functionality
        if not can_add_fakes(request):
            raise PermissionDenied()
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()

        try:
            users = json.loads(request.body)
        except ValueError:
            response["error"] = "Incorrect list of users"
        else:
            # validate email and check them for uniqueness
            for user in users:
                exists = AppUser.objects.filter(organisation=organisation, email=user["email"]).exists()
                try:
                    validate_email(user["email"])
                except ValidationError:
                    email_is_valid = False
                else:
                    email_is_valid = True

                if exists or not email_is_valid:
                    if exists:
                        response["error"] = "This email \"{0}\" is already taken".format(user["email"])
                    else:
                        response["error"] = "This email \"{0}\" is not valid".format(user["email"])
                    break

            if not response["error"]:
                # create app users
                for user in users:
                    AppUser.objects.create(
                        organisation=organisation,
                        first_name=handle_user_input(user["first_name"]),
                        last_name=handle_user_input(user["last_name"]),
                        email=handle_user_input(user["email"]),
                        health_check_user=user["health_check_user"],
                    )

                response["success"] = True
                response["data"] = ", ".join([u["email"] for u in users])
        return JsonResponse(response)


class GenerateRandomAppUserView(SignupMixin, View):
    http_method_names = ["post"]

    def post(self, request, org_id, *args, **kwargs):
        response = {"success": False, "error": None}
        # restricted add fake functionality
        if not can_add_fakes(request):
            raise PermissionDenied()
        organisation = get_organisations(request.user, org_id)
        try:
            data = json.loads(request.body)
            users_number = int(data.get("app_user_number"))
            health_check = bool(data.get("is_health_check"))
        except ValueError:
            response["error"] = "Incorrect data"
            users_number = None
            health_check = None
        if not organisation:
            response["error"] = "Organisation is not found"
            return JsonResponse(response)
        if not isinstance(users_number, int):
            response["error"] = "Input a number"
            return JsonResponse(response)
        if 1 > users_number > 1000:
            response["error"] = "The amount of users should be between 1-1000"
            return JsonResponse(response)

        for index in range(users_number):
            random_uuid = shortuuid.uuid()
            email = "test{0}@{1}.com".format(index, random_uuid)
            organisation.app_users.create(
                email=email,
                first_name="Test {0}".format(index),
                last_name="User {0}".format(index),
                is_admin=False,
                health_check_user=health_check
            )
            # app_user.installs.create(
            #     app_user=app_user,
            #     os=OperatingSystem.objects.first(),
            #     hostname="hostname {0}".format(index),
            #     caption="testOS {0}".format(index),
            #     user="user {0}".format(index),
            #     platform="win32",
            #     device_id=random_uuid,
            #     serial_number="",
            #     registration_unique_token=generate_app_registration_token()
            # )
        response["success"] = True

        return JsonResponse(response)


class FakeAppInstallCreateView(SignupMixin, FormView):
    http_method_names = ["post"]
    form_class = FakeAppInstallForm
    template_name = 'dashboard/dashboard-core.html'

    def __init__(self):
        self.new_app = None
        super().__init__()

    def post(self, request, *args, **kwargs):
        # restricted add fake functionality
        if not can_add_fakes(request):
            raise PermissionDenied()
        return super().post(request, *args, **kwargs)

    def create_fake_report(self):
        api_request_data = generate_app_last_api_report(self.new_app)
        serializer = AppUserReportSerializer(data=api_request_data)
        if serializer.is_valid():
            serializer.create(serializer.validated_data)

    def form_valid(self, form):
        data = form.cleaned_data
        fake_report = data.pop("fake_report")
        username = data.pop("osuser_username")
        domain = data.pop("osuser_domain")
        is_admin_account = data.pop("osuser_is_admin_account")
        self.new_app = AppInstall(**form.cleaned_data)
        self.new_app.app_user = AppUser.objects.get(uuid=self.kwargs.get("app_user_uuid"))
        self.new_app.registration_unique_token = generate_app_registration_token()
        self.new_app.save()
        AppInstallOSUser.objects.create(
            app_install_id=self.new_app.id,
            username=username,
            domain=domain,
            is_admin_account=is_admin_account
        )
        if fake_report:
            self.create_fake_report()
        messages.success(self.request, _("New app was successfully added"))
        return super().form_valid(form)

    def form_invalid(self, form):
        messages.error(self.request, _("Something went wrong"))
        return HttpResponseRedirect(self.get_success_url())

    def get_success_url(self):
        return self.new_app.app_user.organisation.url


class BaseAppInstallView:
    """
    Shared logic for installed software and patch history views.
    """
    view_level = "app_install"
    base_model = InstalledSoftwareAppInstallIndividual

    def dispatch(self, *args, **kwargs) -> HttpResponse:
        """
        Checks if the user has access to the app install and returns the view.
        """
        self.app_install = get_app_install(
            org_id=kwargs.get("org_id"),
            app_user_uuid=kwargs.get("user_uuid"),
            device_uuid=kwargs.get("device_id"),
            serial_number=convert_serial_number(kwargs.get("serial_number")),
            only_active=True
        )
        if not self.app_install:
            raise PermissionDenied()
        return super().dispatch(*args, **kwargs)

    def get_raw_queryset(self) -> QuerySet:
        """
        Returns queryset for the app install from the configured model.
        """
        return self.base_model.objects.filter(app_install=self.app_install).order_by("-highest_severity")

    def get_context_data(self, *args, **kwargs):
        """
        Returns context data for the template.
        """
        context = super().get_context_data(*args, **kwargs)
        context["app_install"] = self.app_install
        return context


class InstalledSoftwareView(BaseAppInstallView, InstalledSoftwareBaseView):
    """
    View that returns installed software for the app install.
    """
    def enrich_queryset(self, queryset: QuerySet) -> list:
        """
        Enriches the queryset with installers for the app install.
        """
        return enrich_installed_software_with_installers(queryset)

    def get_context_data(self, *, object_list=None, **kwargs) -> dict:
        """
        Returns context data for the template.
        """
        context = super().get_context_data(object_list=object_list, **kwargs)
        total_count, safe_count, vulnerable_count, _, regular_count = aggregate_installed_software_counts(
            InstalledSoftwareAppInstallIndividual.objects.filter(
                app_install_id=self.app_install.id
            ), include_source_counts=True
        )
        context["total_count"] = total_count
        context["safe_count"] = safe_count
        context["vulnerable_count"] = vulnerable_count
        context["other_count"] = regular_count
        context["app_install_supports_opswat"] = self.app_install.supports_opswat()
        context["app_install_supports_opswat_patch"] = self.app_install.supports_opswat_patch()
        return context

    def get_pagination_url(self) -> str:
        return self.app_install.url_installed_software()


class PackageDetailsView(BaseAppInstallView, PackageBaseView):
    """
    Manages detailed view for installed software package on the App Install level.

    This class combines functionalities from BaseAppInstallView and PackageBaseView to
    provide a detailed view of package details. It includes methods to enrich the package with
    installer details, retrieve context-specific data, and acquire raw queryset data from the
    configured model.
    """
    def _enrich_package_with_installers(self, package):
        """
        Enriches the package with installer information.
        """
        return enrich_installed_software_with_installers([package])[0]

    def get_context_data(self, **kwargs) -> dict:
        """
        Returns context data for the template.
        """
        context = super().get_context_data(**kwargs)
        context["app_install_supports_opswat"] = self.app_install.supports_opswat()
        context["app_install_supports_opswat_patch"] = self.app_install.supports_opswat_patch()
        return context

    def get_raw_queryset(self) -> QuerySet:
        """
        Returns queryset for the app install from the configured model.
        """
        return InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install)


class PatchHistoryView(BaseAppInstallView, PatchHistoryBaseView):
    """
    View that returns patch history for the app install.
    """

    def get_form_class(self) -> type[Form]:
        """
        Returns the form class to be used for scheduling patch installations.
        """
        return SchedulePatchInstallationForm

    def get_form_kwargs(self) -> dict:
        """
        Returns the keyword arguments to be passed to the form class.
        """
        return {
            "data": {"packages": self.request.POST.getlist("packages"), "app_installs": [self.app_install.id]},
            "organisation": self.organisation,
            "package_level": "app_install",
            "user": self.request.user,
        }

    def _get_vendors(self) -> list[str]:
        """
        Returns a list of distinct vendor names.
        """
        vendors = self.get_raw_queryset().values_list(
            "scheduled_product_installer__opswat_product_patch_installer__product__vendor__name", flat=True
        ).distinct()
        return list(dict.fromkeys(vendors))

    def get_context_data(self, *, object_list=None, **kwargs) -> dict:
        """
        Returns context data for the template.
        """
        context = super().get_context_data(object_list=object_list, **kwargs)
        context["app_install_supports_opswat"] = self.app_install.supports_opswat()
        context["app_install_supports_opswat_patch"] = self.app_install.supports_opswat_patch()
        return context

    def get_raw_queryset(self) -> QuerySet:
        """
        Returns queryset for the app install from the configured model.
        """
        return OpswatPatchAttempt.objects.filter(
            scheduled_product_installer__app_install_id=self.app_install.id
        ).select_related(
            "scheduled_product_installer__app_install",
            "scheduled_product_installer__opswat_product_patch_installer__product",
        ).order_by(
            "-created"
        ).distinct()

    def get_pagination_url(self) -> str:
        return self.app_install.url_patch_history()


class PatchAttemptDetailsView(BaseAppInstallView, PatchAttemptDetailsBaseView):
    """
    View that returns patch attempt details for the app install.
    """
    def get_context_data(self, **kwargs) -> dict:
        """
        Returns context data for the template.
        """
        context = super().get_context_data(**kwargs)
        context["app_install_supports_opswat"] = self.app_install.supports_opswat()
        context["app_install_supports_opswat_patch"] = self.app_install.supports_opswat_patch()
        return context

    def get_raw_queryset(self) -> QuerySet:
        return PatchAttemptDetailsBaseView.get_raw_queryset(self).filter(
            scheduled_product_installer__app_install=self.app_install
        ).distinct()


class PatchSummaryView(BaseAppInstallView, PatchSummaryBaseView):
    """
    View that returns a patch summary for the app install.
    """
    def get_context_data(self, *, object_list=None, **kwargs) -> dict:
        """
        Returns context data for the template.
        """
        context = super().get_context_data(object_list=object_list, **kwargs)
        context["app_install_supports_opswat"] = self.app_install.supports_opswat()
        context["app_install_supports_opswat_patch"] = self.app_install.supports_opswat_patch()
        return context

    def get_raw_queryset(self) -> QuerySet:
        raw_queryset = super().get_raw_queryset()
        selected_packages = self.request.POST.getlist("selected_packages")
        if selected_packages:
            raw_queryset = raw_queryset.filter(
                id__in=selected_packages
            )
        return raw_queryset.distinct()

    def get_queryset(self) -> list:
        """
        Returns enriched records from the original queryset for the patch summary.
        """
        return enrich_installed_software_with_installers(self.get_raw_queryset())


class InstalledSoftwareExportDataView(BaseAppInstallView, InstalledSoftwareExportDataBaseView):
    """
    Generates a CSV or XLSX report of installed software for a given app install.
    """
    view_level = "app_install"

    SEVERITY = _("Severity")
    NAME = _("Name")
    VERSION = _("Version")
    VENDOR = _("Vendor")
    VULNERABILITIES = _("Vulnerabilities")
    PATCH_DETECTED = _("Patch Detected")

    def get_file_name(self) -> str:
        """
        Returns the file name for the report.
        """
        return f"installed_software_{self.app_install.device_id}_{timezone.now().strftime('%Y_%m_%d_%H_%M_%S')}"

    def get_field_names(self) -> list:
        """
        Returns the field names for the report.
        """
        return [
            self.SEVERITY,
            self.NAME,
            self.VERSION,
            self.VENDOR,
            self.VULNERABILITIES,
            self.PATCH_DETECTED
        ]

    def __get_patch_detected_date(self, package) -> str:
        """
        Returns the patch-detected date for the package.
        """
        if hasattr(package, "app_install_product_installer"):
            return package.app_install_product_installer.get("patch_detected_date", "-")
        return "-"

    def write_to_csv(self, writer) -> None:
        """
        Writes the data to the CSV file.
        """
        for package in self.get_queryset():
            row = {
                self.SEVERITY: package.highest_severity,
                self.NAME: package.product,
                self.VERSION: package.version,
                self.VENDOR: package.vendor,
                self.VULNERABILITIES: package.cve_count,
                self.PATCH_DETECTED: self.__get_patch_detected_date(package)
            }
            writer.writerow(row)

    def get_object(self) -> None:
        """
        Returns the organisation object associated with the current instance.
        """
        return self.app_install

    def get_queryset(self) -> list:
        """
        Returns the queryset for the report.
        """
        return enrich_installed_software_with_installers(self.get_raw_queryset())


class PatchHistoryExportDataView(BaseAppInstallView, OrganisationPatchHistoryExportDataView):
    """
    Generates a CSV or XLSX report of patch history for a given app install.
    """
    view_level = "app_install"

    def get_object(self) -> AppInstall:
        """
        Returns the app install object associated with the current instance.
        """
        return self.app_install

    def get_field_names(self) -> list:
        """
        Returns the field names for the report.
        For app install level, we exclude the last field (Devices).
        """
        return super().get_field_names()[:-1]

    def write_to_csv(self, writer) -> None:
        """
        Writes the data to the CSV file.
        """
        for patch_attempt in self.get_queryset():
            row = {
                self.NAME: patch_attempt.product.name,
                self.VERSION: f"{patch_attempt.from_version} - {patch_attempt.to_version}",
                self.VENDOR: patch_attempt.product.vendor.name,
                self.PATCH_STATUS: patch_attempt.short_status,
                self.STARTED: patch_attempt.created,
                self.FINISHED: patch_attempt.finished_at if patch_attempt.is_finished else "-",
            }
            writer.writerow(row)

    def get_queryset(self) -> QuerySet:
        """
        Returns the queryset for the report.
        """
        return OpswatPatchAttempt.objects.filter(
            scheduled_product_installer__app_install_id=self.app_install.id
        ).select_related(
            "scheduled_product_installer__app_install",
            "scheduled_product_installer__opswat_product_patch_installer__product"
        ).order_by(
            "-created"
        ).distinct()
