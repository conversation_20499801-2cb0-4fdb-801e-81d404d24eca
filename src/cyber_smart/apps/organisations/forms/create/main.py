from django import forms
from django.conf import settings
from django.core.validators import validate_email
from django.utils.translation import gettext, gettext_lazy as _

from analytics.tasks import update_partner_analytics
from appusers.utils import send_apps
from billing.providers.chargebee.wrapper import get_coupon
from billing.utils import is_coupon_valid
from common.models import ProjectSettings
from common.utils import handle_user_input
from common.validators import domain_name_validator, safe_char_validator, pervade_safe_char_validator
from dashboard.utils import set_dashboard_admin
from emails.notifications import (send_notifications_to_iasme_cb_partners,
                                  send_organisation_created_notifications_to_distributors,
                                  send_organisation_created_notifications_to_staff)
from organisations.forms.common import CustomModelMultipleChoiceField, IndustryDescriptionMixin
from organisations.models import (
    Organisation
)
from organisations.utils import is_generic_domain
from partners.models import Partner
from .certifications import AddOrganisationCertificationsMixinForm
from .insurer_distributor import InsurerDistributorMixin


class AddOrganisationForm(AddOrganisationCertificationsMixinForm, InsurerDistributorMixin, IndustryDescriptionMixin):
    """
    Partner form for adding organisations.
    """
    def __init__(self, *args, **kwargs):
        """
        Initializes form with request object and other data.
        :param args: positional arguments
        :type args: tuple
        :param kwargs: keyword arguments
        :type: dict
        :return nothing
        :rtype: None
        """
        # extract request from kwargs before form initialization
        self.request = kwargs.pop("request")
        super().__init__(*args, **kwargs)

        # initialize admins field with existing partner admins
        self.fields["pre_populated_admins"].queryset = self.request.user.profile.get_organisations_admins.exclude(
            email=self.request.user.email
        )

        # initialize software switchers based on partner settings
        if self.request.user.profile.partner:
            default_software = self.request.user.profile.partner.default_software
            if default_software:
                # set to individual enrollment by default
                self.fields["bulk_install"].initial = False

        # specific for Brigantia distributor
        if self.request.user.profile.distributor_is_brigantia:
            self.fields["bulk_install"].label = _("Bulk deployment option")
            self.fields["bulk_install"].choices = Organisation.BULK_SELF_EDIT

        # initialize needed attributes
        self.coupon_code = None
        self.software_enabled = None
        self.organisation_name = None
        self.organisation_industry = None
        self.organisation_size = None
        self.bulk_enrollment = None
        self.individual_email_message = None
        self.purchase_order = None
        self.create_app_installs_debug = None
        # todo: fields below should be in certs mixin
        self.bundle_enabled = False
        self.custom_bundle_enabled = False
        self.custom_bundle_software_enabled = False
        self.fields['size'].choices = Organisation.get_organisation_size_choices_channel()


    name = forms.CharField(
        max_length=250,
        label=_("Organisation Name"),
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
            }
        ),
        validators=[pervade_safe_char_validator]
    )
    industry = forms.ChoiceField(
        label=_("Organisation Industry"),
        choices=Organisation.ORG_CHOICES,
        widget=forms.Select(
            attrs={
                "class": "form-control select2",
            })
    )
    size = forms.ChoiceField(
        label=_("Organisation Size"),
        widget=forms.RadioSelect()
    )
    bulk_install = forms.ChoiceField(
        required=False,
        label=_("Deployment Options"),
        choices=Organisation.APP_DEPLOYMENT_METHOD,
        widget=forms.RadioSelect(),
        help_text=_("Bulk deployment builds a single package for organisation "
                    "wide deployment. Check Yes if you have centralised device management "
                    "(via Group Policy, Mobile Device Management or RMM). Choose No if you "
                    "have user managed devices or don't have centralised management. In this "
                    "case we will build unique package for each user and email them for simple "
                    "self-enrolment."),
    )
    email_message = forms.CharField(
        required=False,
        max_length=500,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
            }
        ),
        label=_("Email message"),
        validators=[safe_char_validator]
    )
    pre_populated_admins = CustomModelMultipleChoiceField(
        label=_("Pre populated admins"), widget=forms.CheckboxSelectMultiple, queryset=None, required=False
    )

    approved_domain = forms.CharField(label="Mobile apps: approved domain", required=False)
    org_website_domain = forms.CharField(label=_("Organisation website domain"), required=False)
    coupon = forms.CharField(required=False, max_length=200)
    # Aviva External admin first_name
    first_name = forms.CharField(
        required=False,
        max_length=250,
        label=_("First Name"),
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
            }
        ),
        validators=[safe_char_validator]
    )
    # Aviva External admin last_name
    last_name = forms.CharField(
        required=False,
        max_length=250,
        label=_("Last Name"),
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
            }
        ),
        validators=[safe_char_validator]
    )
    issue_ce_certification_debug = forms.BooleanField(required=False)
    issue_ce_and_cep_certification_debug = forms.BooleanField(required=False)
    create_app_installs_debug = forms.IntegerField(required=False)

    def _validate_domain(self, domain, form_field_name="approved_domain"):
        """Validates the website domain"""
        try:
            domain_name_validator(domain)
        except forms.ValidationError:
            self.add_error(
                form_field_name, _('Selected domain "') + domain + _('" is not a valid domain name')
            )
        else:
            if is_generic_domain(domain):
                self.add_error(
                    form_field_name,
                    _("This generic domain is not allowed. Make sure to use your company’s domain. "
                      "If you do not have a company domain, contact us on live chat."))

    def _purchase_order_validation(self, cleaned_data: dict) -> None:
        """
        Validates Purchase Order fields
        """
        # if the user uploads a PO file then we require a PO number as well
        if not cleaned_data.get("po_number") and cleaned_data.get("po_file"):
            self.add_error(
                "po_number",
                _("This field is required")
            )

    def initialize_clean_fields(self, cleaned_data: dict) -> None:
        """
        Initializes clean fields for form.
        """
        super().initialize_clean_fields(cleaned_data)
        self.organisation_name = handle_user_input(cleaned_data.get("name", ""))
        self.organisation_industry = cleaned_data.get("industry", "")
        self.organisation_size = cleaned_data.get("size", "")
        self.bulk_enrollment = bool(cleaned_data.get("bulk_install") == "True")
        self.software_enabled = self.request.POST.get("software-switch") == "on"
        self.individual_email_message = cleaned_data.get("email_message")
        self.coupon_code = cleaned_data.get("coupon")
        self.create_app_installs_debug = cleaned_data.get("create_app_installs_debug")

    def run_extra_validations(self, cleaned_data: dict) -> None:
        super().run_extra_validations(cleaned_data)
        # check if organisation name is unique
        if self.request.user.profile.get_organisations.filter(name=self.organisation_name).exists():
            self.add_error(
                "name",
                _("This name is already used by another company")
            )
        # check if domain is valid and not taken
        org_website_domain = cleaned_data.get("org_website_domain")
        if org_website_domain:
            self._validate_domain(org_website_domain, 'org_website_domain')

        # check if coupon is applicable only for specific type of organisation
        if self.coupon_code:
            if not is_coupon_valid(self.coupon_code, 'random_plan'):
                self.add_error("coupon", _("Coupon with this code doesn't exist or is invalid"))
            else:
                coupon = get_coupon(self.coupon_code)
                org_type = coupon.values.get('meta_data', {}).get('org_type')
                if org_type and org_type != self.organisation_industry:
                    self.add_error(
                        "coupon",
                        _("Coupon with this code is not valid for this type of organisation industry")
                    )

        self._purchase_order_validation(cleaned_data)

        if self.software_enabled:
            if not cleaned_data.get("bulk_install"):
                self.add_error(
                    "bulk_install",
                    _("This field is required")
                )
            if not self.bulk_enrollment:
                # this validation should only run for individual enrollment
                if not cleaned_data.get("email_message"):
                    self.add_error(
                        "email_message",
                        _("This field is required")
                    )
                if self.individual_email_message:
                    if gettext("[SECRET]") in self.individual_email_message:
                        self.add_error(
                            "email_message",
                            (
                                _("Please replace the word [SECRET] for your own word "
                                  "so users can confirm the legitimacy of the email")
                            )
                        )
            else:
                domain = cleaned_data.get("approved_domain")
                # approved_domain must match chosen org_website_domain
                if domain != org_website_domain and org_website_domain:
                    self.add_error(
                        "approved_domain",
                        _("This field must be equal to the chosen organisation website domain")
                    )
                self._validate_domain(domain)
        else:
            if not hasattr(self, "certifications_enabled"):
                self.add_error(
                    "bulk_install",
                    _("CyberSmart Active Protect is required")
                )

    def clean(self):
        cleaned_data = super().clean()
        self.initialize_clean_fields(cleaned_data)
        self.run_extra_validations(cleaned_data)

        return cleaned_data

    def create_certifications(self, organisation, po_number, po_file) -> None:
        if hasattr(super(), "create_certifications"):
            super().create_certifications(organisation, po_number, po_file)

    def create_organisation(self, request):
        admins = []
        for admin_email in request.POST.getlist("custom_admins"):
            try:
                validate_email(admin_email)
            except forms.ValidationError:
                pass
            else:
                admins.append(admin_email)
        for admin_user in self.cleaned_data["pre_populated_admins"]:
            admins.append(admin_user.email)
        admins = list(set(admins))

        if self.bulk_enrollment:
            email_message = _("This is a custom bulk deployment package to be installed on all your devices")
        else:
            email_message = self.individual_email_message

        partner = request.user.profile.partner if hasattr(
            request.user, "partner"
        ) else Partner.objects.filter(name=settings.DEFAULT_CS_PARTNER_NAME).first()

        if request.user.profile.get_organisations.count():
            set_partner_org = False
        else:
            set_partner_org = True

        organisation = Organisation.objects.create(
            name=self.organisation_name,
            industry=self.cleaned_data['industry'],
            industry_description=self.cleaned_data['industry_description'],
            size=self.cleaned_data['size'],
            bulk_install=self.bulk_enrollment,
            email_message=email_message,
            software_support=self.software_enabled,
            partner=partner,
            is_partner_org=set_partner_org
        )

        if ProjectSettings.is_uba_enabled():
            # if feature is enabled make UBA enabled by default for newly created organisations by this partner
            if self.bulk_enrollment and partner.distributor.new_bulk_org_uba_only:
                organisation.settings.uba_enabled = True
                organisation.settings.save()

        # add organisation website domain that is equal to the approved domain
        if self.bulk_enrollment or self.cleaned_data.get('org_website_domain'):
            organisation.approved_domains.get_or_create(
                domain=self.cleaned_data.get('org_website_domain') or self.cleaned_data['approved_domain']
            )

        user = request.user

        if user.profile.has_insurer_distributor:
            organisation.set_organisation_as_aviva_customer()
        if user.profile.is_partner:
            partner = user.profile.partner
            organisation.partner = partner
            if self.bundle_enabled:
                if self.bundle_price_monthly:
                    organisation.pricing_band = Organisation.PRICING_BUNDLE_MONTHLY
                else:
                    organisation.pricing_band = Organisation.PRICING_BUNDLE_ANNUAL
            elif self.custom_bundle_enabled:
                if self.bundle_price_monthly:
                    organisation.pricing_band = Organisation.PRICING_CUSTOM_BUNDLE_MONTHLY
                else:
                    organisation.pricing_band = Organisation.PRICING_CUSTOM_BUNDLE_ANNUAL
            else:
                organisation.pricing_band = partner.default_pricing_band
            organisation.save()
            update_partner_analytics.delay(partner.pk)
        if not user.profile.has_insurer_distributor:
            organisation.admins.create(
                user=user,
                is_admin=True
            )

        self.create_certifications(organisation, self.po_number, self.po_file)

        # get or create admin users and set them as an administrator of just created organisation
        for admin_email in admins:
            if admin_email and admin_email != user.email:
                set_dashboard_admin(
                    None, request.user, request.POST.get('first_name', ''),
                    request.POST.get('last_name', ''), admin_email, organisation)

        if organisation.is_partner_org:
            # Create AppUser for partner in case of the distributor skipped the partner organisation creation.
            # So, the partner user will create it as the first organisation
            organisation.app_users.create(
                email=user.email,
                first_name=user.first_name,
                last_name=user.last_name,
                is_admin=True,
            )

        if self.bulk_enrollment:
            organisation.get_or_create_bulk_deploy_user()

        if not settings.IS_TEST:
            send_organisation_created_notifications_to_staff(organisation, user)
            # if it is a custom bundle with CAP, then we add that to the name
            if self.custom_bundle_software_enabled:
                self.bundle_name += ' & CyberSmart Active Protect'
            if not hasattr(self, "bundle_name"):
                self.bundle_name = "CyberSmart Active Protect"
            send_organisation_created_notifications_to_distributors(organisation, self.bundle_name)

            send_notifications_to_iasme_cb_partners(
                organisation=organisation,
                subject=f'New organisation was created {organisation.name}',
                template_id='d-cc8fb2b6018d4534be2d1459511b03c1'
            )
        # send download links to users except for the user that created this org
        uuid_list = list(organisation.app_users.filter(active=True).exclude(
            email__iexact=user.email
        ).values_list('uuid', flat=True))
        if uuid_list:
            send_apps(organisation, uuid_list=uuid_list, first_email=True)

        # create subscriptions
        partner = organisation.partner
        if partner.is_billable:
            if self.bundle_enabled:
                partner.billing.add_bundle_plan(
                    organisation=organisation,
                    po_number=self.po_number,
                    po_file=self.po_file,
                    monthly=self.bundle_price_monthly,
                    commitment=self.bundle_commitment,
                    coupon=self.coupon_code,
                    chosen_bundle=self.chosen_bundle
                )
            elif self.custom_bundle_enabled:
                partner.billing.add_custom_bundle_plan(
                    organisation=organisation, bundle_certs=self.chosen_bundle,
                    po_number=self.po_number, po_file=self.po_file, monthly=self.bundle_price_monthly,
                    commitment=self.bundle_commitment, price=self.custom_bundle_price,
                    licenses=self.custom_bundle_software_licenses, coupon=self.coupon_code
                )
            elif organisation.software_support:
                po_kwargs = {'po_number': self.po_number, 'po_file': self.po_file}
                if self.purchase_order:
                    po_kwargs['po_file'] = self.purchase_order.file
                partner.billing.add_application_plan(organisation, coupon=self.coupon_code, **po_kwargs)

        if not settings.IS_PROD and self.create_app_installs_debug:
            self._create_app_installs_debug(self.create_app_installs_debug, organisation)
        return organisation

    def _create_app_installs_debug(self, count, organisation):
        """
        Helper function to create app_installs and related models
        to enable creating larger fake organisations.
        """
        from organisations.tasks import populate_organisation_with_fake_data
        if settings.IS_TEST:
            populate_organisation_with_fake_data(organisation.id, count)
        else:
            populate_organisation_with_fake_data.delay(organisation.id, count)
