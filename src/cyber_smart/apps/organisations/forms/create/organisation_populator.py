import factory.random

import random
from datetime import datetime, timedelta

from django.conf import settings
from django.db import transaction, IntegrityError
from django.contrib.auth import get_user_model

from analytics.models import AppInstallAnalytics, AppUserAnalytics
from analytics.factories import AppInstallAnalyticsFactory, AppUserAnalyticsFactory
from appusers.models import <PERSON>pp<PERSON>nstall, AppReport, AppInstallOSUser, UserLoginHistory, CheckResult, AppCheck, AppInstallCheckStatus, AppOSInstalledSoftware, SoftwarePackage, AppInstallPolicyAgreement
from appusers.tasks import update_installed_software_individual_for_organisation
from software_inventory.tasks import update_installed_software_organisation_individual
from appusers.models.factories import AppUserFactory, AppInstallFactory, AppInstallOSUserFactory, AppReportFactory, OperatingSystemFactory, UserLoginHistoryFactory, CheckResultFactory, AppInstallCheckStatusFactory, AppOSInstalledSoftwareFactory, AppInstallPolicyAgreementFactory, SoftwarePackageFactory
from operating_systems.factories import OSEndOfLifeFactory
from operating_systems.models import OSEndOfLife
from organisations.factories import OrganisationPolicyFactory, OrganisationPolicyVersionFactory
from appusers.utils import PLATFORM_IOS, PLATFORM_DARWIN
from beta_features.utils import CAP_V_FIVE_BETA_PREFIX
from common.factories import GroupFactory
from trustd.factories import TrustdDeviceFactory
from trustd.models import TrustdDevice, TrustdCustomer
from opswat.models import ProductCategory, ProductVendor, Product, ProductVersion, InstalledProduct, InstalledProductVersion, CVE, ProductSignature
from opswat_patch.models import (
    OpswatProductPatchInstaller, OpswatScheduledProductInstaller,
    OpswatPatchEventLog, OpswatPatchAttempt, OpswatPatchJob
)
from opswat_patch.models.constants import (
    PENDING, SCHEDULED, IN_PROGRESS, INSTALLED, WAITING_FOR_RESTART,
    RETRYING, COMPLETE, TIMED_OUT, ROLLED_BACK, ERROR, SKIPPED_PATCH_VERSION_LOWER
)

User = get_user_model()


class OrganisationPopulator:
    # Note: this runs in a celery task, so if you update it, restart celery to pick up changes.

    def __init__(self, organisation, count):
        self.organisation = organisation
        self.count = count
        self.app_installs = []
        self.app_reports = []
        self.os_users = []
        self.user_login_histories = []
        self.app_install_analytics = []
        self.check_results = []
        self.app_install_check_statuses = []
        self.app_user_analytics = []
        self.trustd_devices = []
        self.app_os_installed_softwares = []
        self.os_end_of_lifes = []
        self.policy_agreements = []
        self.installed_products = []
        self.installed_product_versions = []
        self.scheduled_installers = []
        self.patch_attempts = []
        self.patch_jobs = []
        self.event_logs = []
        self.product_patch_installers = []
        self.software_packages = []  # Track software packages to bulk create


    def create_opswat_software_packages(self):
        # Use existing OPSWAT data for vulnerable software (Google Chrome)
        # In test environment, we may need to create the category with proper name
        self.vulnerable_category, _ = ProductCategory.objects.get_or_create(
            opswat_id="1",
            defaults={"name": "Browser"}
        )
        self.vulnerable_vendor, _ = ProductVendor.objects.get_or_create(
            opswat_id="36",
            defaults={"name": "Google Inc."}
        )
        self.vulnerable_product, _ = Product.objects.get_or_create(
            opswat_id="39",
            defaults={
                "name": "Google Chrome",
                "vendor": self.vulnerable_vendor,
            }
        )

        # Use existing signature or create if in test environment
        self.vulnerable_signature, _ = ProductSignature.objects.get_or_create(
            opswat_id="41",
            defaults={
                "name": "Google Chrome",
                "support_3rd_party_patch": True,
                "product": self.vulnerable_product
            }
        )
        # Ensure signature is linked to product
        if not self.vulnerable_product.signatures.filter(id=self.vulnerable_signature.id).exists():
            self.vulnerable_product.signatures.add(self.vulnerable_signature)
        # Ensure category is linked to product
        if not self.vulnerable_product.categories.filter(id=self.vulnerable_category.id).exists():
            self.vulnerable_product.categories.add(self.vulnerable_category)

        self.vulnerable_version, _ = ProductVersion.objects.get_or_create(
            product=self.vulnerable_product,
            raw_version="133.0.6943.142",
            defaults={
                "major": 133,
                "minor": 0,
                "patch": 6943,
                "version_channel": "stable"
            }
        )

        # We'll use existing patch installers from the database
        self.cve, _ = CVE.objects.get_or_create(
            cve_id="CVE-2024-7971",
            defaults={
                "opswat_id": "cve-2024-7971-placeholder",
                "severity": CVE.SEVERITY_IMPORTANT,
                "severity_index": 80,
                "description": "Vulnerability in Google Chrome " + self.vulnerable_version.raw_version,
                "published_at": datetime.now() - timedelta(days=10),
                "details": {
                    "references": [{"url": "https://chromereleases.googleblog.com/"}],
                }
            }
        )
        self.cve.product_version.add(self.vulnerable_version)

        # Find existing Chrome product patch installer association
        chrome_ppi = OpswatProductPatchInstaller.objects.filter(
            product=self.vulnerable_product
        ).first()
        if chrome_ppi:
            self.chrome_ppi = chrome_ppi

        # Use existing OPSWAT data for safe software
        # In test environment, we may need to create/update the category
        self.safe_category, _ = ProductCategory.objects.get_or_create(
            opswat_id="5",
            defaults={"name": "Antimalware"}
        )
        self.safe_vendor, _ = ProductVendor.objects.get_or_create(
            opswat_id="37",
            defaults={"name": "Apple Inc."}
        )
        self.safe_product, _ = Product.objects.get_or_create(
            opswat_id="415",
            defaults={
                "name": "iTunes",
                "vendor": self.safe_vendor,
            }
        )
        # Ensure category is linked to product
        if not self.safe_product.categories.filter(id=self.safe_category.id).exists():
            self.safe_product.categories.add(self.safe_category)

        # Use existing signature or create if in test environment
        self.safe_signature, _ = ProductSignature.objects.get_or_create(
            opswat_id="419",
            defaults={
                "name": "iTunes",
                "support_3rd_party_patch": False,
                "product": self.safe_product
            }
        )
        # Ensure signature is linked to product
        if not self.safe_product.signatures.filter(id=self.safe_signature.id).exists():
            self.safe_product.signatures.add(self.safe_signature)

        self.safe_version, _ = ProductVersion.objects.get_or_create(
            product=self.safe_product,
            raw_version="1.3.3",
            defaults={
                "major": 1,
                "minor": 3,
                "patch": 3,
                "version_channel": "stable"
            }
        )

    def create_additional_software_packages(self):
        """Use existing software packages from database - no creation of new OPSWAT data."""
        # Only fetch existing products from the database using real IDs

        # We'll use existing patch installers from the database

        # Adobe Acrobat Reader DC Continuous (ID: 2966 from database)
        self.adobe_product = Product.objects.filter(opswat_id="2966").first()
        if self.adobe_product:
            self.adobe_vendor = self.adobe_product.vendor
            self.adobe_signature = self.adobe_product.signatures.first()
            # Get or create a version for testing
            self.adobe_version, _ = ProductVersion.objects.get_or_create(
                product=self.adobe_product,
                raw_version="2023.003.20284",
                defaults={
                    "major": 2023,
                    "minor": 3,
                    "patch": 20284,
                    "version_channel": "stable"
                }
            )

            # Find existing patch installer association for Adobe
            adobe_ppi = OpswatProductPatchInstaller.objects.filter(
                product=self.adobe_product
            ).first()
            if adobe_ppi:
                self.adobe_ppi = adobe_ppi

        # Microsoft Excel (ID: 1199 from database)
        self.excel_product = Product.objects.filter(opswat_id="1199").first()
        if self.excel_product:
            self.excel_vendor = self.excel_product.vendor
            self.excel_signature = self.excel_product.signatures.first()
            self.excel_version, _ = ProductVersion.objects.get_or_create(
                product=self.excel_product,
                raw_version="16.0.17328.20162",
                defaults={
                    "major": 16,
                    "minor": 0,
                    "patch": 17328,
                    "version_channel": "stable"
                }
            )

            # Find existing patch installer association for Excel
            excel_ppi = OpswatProductPatchInstaller.objects.filter(
                product=self.excel_product
            ).first()
            if excel_ppi:
                self.excel_ppi = excel_ppi

        # Zoom (ID: 100168 from database)
        self.zoom_product = Product.objects.filter(opswat_id="100168").first()
        if self.zoom_product:
            self.zoom_vendor = self.zoom_product.vendor
            self.zoom_signature = self.zoom_product.signatures.first()
            self.zoom_version, _ = ProductVersion.objects.get_or_create(
                product=self.zoom_product,
                raw_version="5.16.10",
                defaults={
                    "major": 5,
                    "minor": 16,
                    "patch": 10,
                    "version_channel": "stable"
                }
            )

            # Find existing patch installer association for Zoom
            zoom_ppi = OpswatProductPatchInstaller.objects.filter(
                product=self.zoom_product
            ).first()
            if zoom_ppi:
                self.zoom_ppi = zoom_ppi

        # Slack (ID: 100167 from database)
        self.slack_product = Product.objects.filter(opswat_id="100167").first()
        if self.slack_product:
            self.slack_vendor = self.slack_product.vendor
            self.slack_signature = self.slack_product.signatures.first()
            self.slack_version, _ = ProductVersion.objects.get_or_create(
                product=self.slack_product,
                raw_version="4.35.126",
                defaults={
                    "major": 4,
                    "minor": 35,
                    "patch": 126,
                    "version_channel": "stable"
                }
            )

            # Find existing patch installer association for Slack
            slack_ppi = OpswatProductPatchInstaller.objects.filter(
                product=self.slack_product
            ).first()
            if slack_ppi:
                self.slack_ppi = slack_ppi

        # VLC Media Player (ID: 100232 from database)
        self.vlc_product = Product.objects.filter(opswat_id="100232").first()
        if self.vlc_product:
            self.vlc_vendor = self.vlc_product.vendor
            self.vlc_signature = self.vlc_product.signatures.first()
            self.vlc_version, _ = ProductVersion.objects.get_or_create(
                product=self.vlc_product,
                raw_version="3.0.20",
                defaults={
                    "major": 3,
                    "minor": 0,
                    "patch": 20,
                    "version_channel": "stable"
                }
            )

            # Find existing patch installer association for VLC
            vlc_ppi = OpswatProductPatchInstaller.objects.filter(
                product=self.vlc_product
            ).first()
            if vlc_ppi:
                self.vlc_ppi = vlc_ppi

        # Firefox (ID: 100135 from database)
        self.firefox_product = Product.objects.filter(opswat_id="100135").first()
        if self.firefox_product:
            self.firefox_vendor = self.firefox_product.vendor
            self.firefox_signature = self.firefox_product.signatures.first()
            self.firefox_version, _ = ProductVersion.objects.get_or_create(
                product=self.firefox_product,
                raw_version="120.0.1",
                defaults={
                    "major": 120,
                    "minor": 0,
                    "patch": 1,
                    "version_channel": "stable"
                }
            )

            # Find existing patch installer association for Firefox
            firefox_ppi = OpswatProductPatchInstaller.objects.filter(
                product=self.firefox_product
            ).first()
            if firefox_ppi:
                self.firefox_ppi = firefox_ppi

    def create_teamviewer_software_packages(self):
        """Create TeamViewer product with multiple signatures for testing patching."""
        # Create TeamViewer vendor and product
        self.teamviewer_vendor, _ = ProductVendor.objects.get_or_create(
            opswat_id="152",
            defaults={"name": "TeamViewer GmbH"}
        )
        self.teamviewer_product, _ = Product.objects.get_or_create(
            opswat_id="173",
            defaults={
                "name": "TeamViewer",
                "vendor": self.teamviewer_vendor,
            }
        )

        # Create TeamViewer signatures based on the provided data
        self.teamviewer_signature, _ = ProductSignature.objects.get_or_create(
            opswat_id="177",
            defaults={
                "name": "TeamViewer",
                "support_3rd_party_patch": False,
                "product": self.teamviewer_product
            }
        )

        self.teamviewer_11_signature, _ = ProductSignature.objects.get_or_create(
            opswat_id="3342",
            defaults={
                "name": "TeamViewer 11",
                "support_3rd_party_patch": True,
                "fresh_installable": 1,
                "product": self.teamviewer_product
            }
        )

        self.teamviewer_12_signature, _ = ProductSignature.objects.get_or_create(
            opswat_id="3343",
            defaults={
                "name": "TeamViewer 12",
                "support_3rd_party_patch": True,
                "fresh_installable": 1,
                "product": self.teamviewer_product
            }
        )

        # Add signatures to product if they don't exist
        if not self.teamviewer_product.signatures.filter(id=self.teamviewer_signature.id).exists():
            self.teamviewer_product.signatures.add(self.teamviewer_signature)
        if not self.teamviewer_product.signatures.filter(id=self.teamviewer_11_signature.id).exists():
            self.teamviewer_product.signatures.add(self.teamviewer_11_signature)
        if not self.teamviewer_product.signatures.filter(id=self.teamviewer_12_signature.id).exists():
            self.teamviewer_product.signatures.add(self.teamviewer_12_signature)

        # Create TeamViewer versions
        self.teamviewer_v11, _ = ProductVersion.objects.get_or_create(
            product=self.teamviewer_product,
            raw_version="11.0.0",
            defaults={
                "major": 11,
                "minor": 0,
                "patch": 0,
                "version_channel": "stable"
            }
        )

        self.teamviewer_v12, _ = ProductVersion.objects.get_or_create(
            product=self.teamviewer_product,
            raw_version="12.0.0",
            defaults={
                "major": 12,
                "minor": 0,
                "patch": 0,
                "version_channel": "stable"
            }
        )

        # We'll use existing patch installers from the database

        # Find existing TeamViewer patch installer associations
        tv_ppis = OpswatProductPatchInstaller.objects.filter(
            product=self.teamviewer_product
        )
        for tv_ppi in tv_ppis:
            # Check which signature this PPI has
            if tv_ppi.signatures.filter(id=self.teamviewer_11_signature.id).exists():
                self.teamviewer_11_ppi = tv_ppi
            elif tv_ppi.signatures.filter(id=self.teamviewer_signature.id).exists():
                self.teamviewer_15_ppi = tv_ppi

    def create_software_packages(self):
        self.create_opswat_software_packages()
        self.create_additional_software_packages()
        self.create_teamviewer_software_packages()

        self.software_package_with_vulnerabilities = SoftwarePackage.objects.filter(
            vendor='google',
            product='chrome',
            version='133.0.6943.142'
        ).first()
        if not self.software_package_with_vulnerabilities:
            self.software_package_with_vulnerabilities = SoftwarePackage.objects.create(
                vendor='google',
                product='chrome',
                version='133.0.6943.142',
                mobile_app=False,
                platform=PLATFORM_DARWIN
            )

        self.software_package_without_vulnerabilities = SoftwarePackage.objects.filter(
            vendor='apple',
            product='music',
            version='1.3.3'
        ).first()
        if not self.software_package_without_vulnerabilities:
            self.software_package_without_vulnerabilities = SoftwarePackage.objects.create(
                vendor='apple',
                product='music',
                version='1.3.3',
                mobile_app=True,
                platform=PLATFORM_IOS
            )

    def create_app_install(self, app_install_kwargs, app_user, operating_system, pass_percentage, is_admin_account, is_mobile=False):
        # Ensure serial number is always populated
        if 'serial_number' not in app_install_kwargs:
            app_install_kwargs['serial_number'] = f"SN{random.randint(100000, 999999)}{app_user.id}"
        os_end_of_life_date = datetime.now() + timedelta(days=365)
        if pass_percentage != 100:
            os_end_of_life_date = datetime.now() - timedelta(weeks=2)
        os_end_of_life = OSEndOfLifeFactory.build(
            end_of_life_date=os_end_of_life_date,
        )
        self.os_end_of_lifes.append(os_end_of_life)

        app_install = AppInstallFactory.build(
            app_user=app_user, os=operating_system,
            end_of_life=os_end_of_life,
            **app_install_kwargs
        )
        app_report = AppReportFactory.build(app_install=app_install, pass_percentage=pass_percentage)

        # Check if this is a CAP version that supports OPSWAT patching (>= 5.5.0)
        is_cap_v5_5_or_higher = False
        if 'app_version' in app_install_kwargs:
            version_parts = app_install_kwargs['app_version'].split('.')
            if len(version_parts) >= 2:
                major = int(version_parts[0])
                minor = int(version_parts[1]) if version_parts[1].isdigit() else 0
                is_cap_v5_5_or_higher = (major > 5) or (major == 5 and minor >= 5)

        # Determine which software package and product version to use
        software_package = self.software_package_without_vulnerabilities
        product_version = self.safe_version
        signature = self.safe_signature

        # For CAP 5.5.0+, use TeamViewer with specific signature
        if is_cap_v5_5_or_higher and not is_mobile:
            # Always use TeamViewer for CAP 5.5.0+ non-mobile installs
            product_version = self.teamviewer_v11
            signature = self.teamviewer_11_signature  # Use TeamViewer 11 signature
        elif pass_percentage != 100:
            software_package = self.software_package_with_vulnerabilities
            product_version = self.vulnerable_version
            signature = self.vulnerable_signature

        # Also randomly assign one of the real products from database
        real_products = []
        if hasattr(self, 'adobe_version') and self.adobe_version and self.adobe_signature:
            real_products.append((self.adobe_version, self.adobe_signature))
        if hasattr(self, 'excel_version') and self.excel_version and self.excel_signature:
            real_products.append((self.excel_version, self.excel_signature))
        if hasattr(self, 'zoom_version') and self.zoom_version and self.zoom_signature:
            real_products.append((self.zoom_version, self.zoom_signature))
        if hasattr(self, 'slack_version') and self.slack_version and self.slack_signature:
            real_products.append((self.slack_version, self.slack_signature))
        if hasattr(self, 'vlc_version') and self.vlc_version and self.vlc_signature:
            real_products.append((self.vlc_version, self.vlc_signature))
        if hasattr(self, 'firefox_version') and self.firefox_version and self.firefox_signature:
            real_products.append((self.firefox_version, self.firefox_signature))

        app_os_installed_software = AppOSInstalledSoftwareFactory.build(report=app_report, software=software_package)
        self.app_os_installed_softwares.append(app_os_installed_software)

        # Add additional random software to make it more realistic
        additional_software_count = random.randint(3, 10)
        for i in range(additional_software_count):
            # Create random software packages
            vendor_names = ['microsoft', 'adobe', 'mozilla', 'videolan', 'zoom', 'slack', 'jetbrains', 'docker', 'git']
            product_names = {
                'microsoft': ['office', 'teams', 'edge', 'visual-studio-code', 'powershell'],
                'adobe': ['acrobat-reader', 'creative-cloud', 'photoshop', 'premiere-pro'],
                'mozilla': ['firefox', 'thunderbird'],
                'videolan': ['vlc-media-player'],
                'zoom': ['zoom-client', 'zoom-rooms'],
                'slack': ['slack-desktop'],
                'jetbrains': ['intellij-idea', 'pycharm', 'webstorm'],
                'docker': ['docker-desktop'],
                'git': ['git-for-windows', 'git']
            }

            vendor = random.choice(vendor_names)
            product = random.choice(product_names.get(vendor, ['unknown']))
            major_version = random.randint(1, 25)
            minor_version = i  # Use loop index to ensure uniqueness
            patch_version = random.randint(0, 999)
            version = f"{major_version}.{minor_version}.{patch_version}"

            additional_software = SoftwarePackageFactory.build(
                vendor=vendor,
                product=product,
                version=version,
                mobile_app=False,
                platform=app_install.platform if hasattr(app_install, 'platform') else PLATFORM_DARWIN
            )
            self.software_packages.append(additional_software)

            additional_os_software = AppOSInstalledSoftwareFactory.build(
                report=app_report,
                software=additional_software
            )
            self.app_os_installed_softwares.append(additional_os_software)

        os_user = AppInstallOSUserFactory.build(
            app_install=app_install, is_admin_account=is_admin_account)
        self.os_users.append(os_user)
        self.app_reports.append(app_report)
        self.app_installs.append(app_install)

        # Create installed product record
        installed_product = InstalledProduct(app_install=app_install)
        self.installed_products.append(installed_product)

        # Keep a mapping of installed_product to product_version and signature
        self.installed_product_versions.append((installed_product, product_version, signature))

        # Also add some real products from database as installed products
        if real_products:
            # Pick 1-3 random real products to install
            num_real_products = min(random.randint(1, 3), len(real_products))
            selected_products = random.sample(real_products, num_real_products)

            for real_version, real_signature in selected_products:
                real_installed_product = InstalledProduct(app_install=app_install)
                self.installed_products.append(real_installed_product)
                self.installed_product_versions.append((real_installed_product, real_version, real_signature))

                # Also create scheduled installers and event logs for these real products
                if is_cap_v5_5_or_higher and not is_mobile:
                    self._create_scheduled_installers_and_events(app_install, real_version, real_signature)

        self.app_install_analytics.append(AppInstallAnalyticsFactory.build(app_install=app_install, latest_pass_percentage=pass_percentage))
        self.user_login_histories.append(
                UserLoginHistoryFactory.build(app_install=app_install, user=os_user))
        app_checks = self.mobile_app_checks if is_mobile else self.desktop_app_checks
        for app_check in app_checks:
            # Use deterministic response based on pass_percentage
            # If pass_percentage is 100, all checks should pass
            # Otherwise, make some checks fail based on app_check.id
            response = True if pass_percentage == 100 else (app_check.id % 2 == 0)
            self.check_results.append(CheckResultFactory.build(
                report=app_report, app_check=app_check,
                response=response
            ))
            self.app_install_check_statuses.append(
                AppInstallCheckStatusFactory.build(
                    app_install=app_install, app_check=app_check)
            )

        # Create scheduled installers and event logs for CAP 5.5.0+ installs
        if is_cap_v5_5_or_higher and not is_mobile:
            self._create_scheduled_installers_and_events(app_install, product_version, signature)

        return app_install

    def _create_scheduled_installers_and_events(self, app_install, product_version, signature):
        """Create scheduled installers and event logs for testing OPSWAT patch functionality."""
        # Always create event logs for devices with patch software
        # (Previously was random with 50% chance)

        # Find or create OpswatProductPatchInstaller
        product_patch_installer = None

        # First check if we have a pre-created PPI for this product
        if hasattr(self, 'chrome_ppi') and product_version.product == self.vulnerable_product:
            product_patch_installer = self.chrome_ppi
        elif hasattr(self, 'adobe_ppi') and hasattr(self, 'adobe_product') and product_version.product == self.adobe_product:
            product_patch_installer = self.adobe_ppi
        elif hasattr(self, 'zoom_ppi') and hasattr(self, 'zoom_product') and product_version.product == self.zoom_product:
            product_patch_installer = self.zoom_ppi
        elif hasattr(self, 'slack_ppi') and hasattr(self, 'slack_product') and product_version.product == self.slack_product:
            product_patch_installer = self.slack_ppi
        elif hasattr(self, 'vlc_ppi') and hasattr(self, 'vlc_product') and product_version.product == self.vlc_product:
            product_patch_installer = self.vlc_ppi
        elif hasattr(self, 'firefox_ppi') and hasattr(self, 'firefox_product') and product_version.product == self.firefox_product:
            product_patch_installer = self.firefox_ppi
        elif hasattr(self, 'excel_ppi') and hasattr(self, 'excel_product') and product_version.product == self.excel_product:
            product_patch_installer = self.excel_ppi
        elif hasattr(self, 'teamviewer_product') and product_version.product == self.teamviewer_product:
            # For TeamViewer, select the appropriate PPI based on signature
            if hasattr(self, 'teamviewer_11_ppi') and signature == self.teamviewer_11_signature:
                product_patch_installer = self.teamviewer_11_ppi
            elif hasattr(self, 'teamviewer_15_ppi') and signature == self.teamviewer_signature:
                product_patch_installer = self.teamviewer_15_ppi
        else:
            # Fall back to searching for existing PPIs
            for ppi in OpswatProductPatchInstaller.objects.filter(product=product_version.product):
                if ppi.patch_installer and hasattr(signature, 'id'):
                    # Check if this installer has the signature
                    if ppi.signatures.filter(id=signature.id).exists():
                        product_patch_installer = ppi
                        break

        if not product_patch_installer:
            # Skip if no matching product patch installer found
            return

        creator = User.objects.filter(organisation_admins__organisation=app_install.app_user.organisation).first()
        if not creator:
            # Skip if no user found
            return

        scheduled_installer = OpswatScheduledProductInstaller(
            opswat_product_patch_installer=product_patch_installer,
            signature=signature,
            app_install=app_install,
            creator=creator,
            status=PENDING
        )

        patch_job = OpswatPatchJob(
            organisation=app_install.app_user.organisation,
            initiated_by=creator
        )

        patch_attempt = OpswatPatchAttempt(
            scheduled_product_installer=scheduled_installer,
            patch_job=patch_job,
            initiated_by=creator,
            from_version=product_version.raw_version,
            to_version=product_patch_installer.patch_installer.latest_version
        )

        self.scheduled_installers.append(scheduled_installer)
        self.patch_jobs.append(patch_job)
        self.patch_attempts.append(patch_attempt)

        # Create various event logs to simulate different patch states
        event_statuses = [
            (PENDING, None, "Patch scheduled for installation"),
            (SCHEDULED, None, "Patch scheduled on device successfully"),
            (IN_PROGRESS, None, "Starting patch installation"),
            (INSTALLED, None, "Patch files downloaded and installed"),
            (WAITING_FOR_RESTART, None, "Installation complete, restart required"),
            (COMPLETE, None, "Patch installation completed successfully"),
            (ERROR, "ERR_DOWNLOAD_FAILED", "Failed to download patch files"),
            (RETRYING, "ERR_NETWORK", "Network error, retrying installation"),
            (TIMED_OUT, None, "Installation timed out after 30 minutes"),
            (ROLLED_BACK, "ERR_VALIDATION", "Patch validation failed, rolled back"),
            (SKIPPED_PATCH_VERSION_LOWER, None, "Device already has newer version installed")
        ]

        # Pick a random status journey
        status_choice = random.choice([
            # Successful installation
            [PENDING, SCHEDULED, IN_PROGRESS, INSTALLED, COMPLETE],
            # Installation with restart
            [PENDING, SCHEDULED, IN_PROGRESS, INSTALLED, WAITING_FOR_RESTART, COMPLETE],
            # Failed installation
            [PENDING, SCHEDULED, IN_PROGRESS, ERROR],
            # Retry then success
            [PENDING, SCHEDULED, IN_PROGRESS, RETRYING, IN_PROGRESS, COMPLETE],
            # Timed out
            [PENDING, SCHEDULED, IN_PROGRESS, TIMED_OUT],
            # Skipped
            [PENDING, SKIPPED_PATCH_VERSION_LOWER],
            # Rolled back
            [PENDING, SCHEDULED, IN_PROGRESS, INSTALLED, ROLLED_BACK]
        ])

        # Create event logs for the chosen journey
        base_time = datetime.now() - timedelta(hours=random.randint(1, 72))
        for i, status in enumerate(status_choice):
            # Find matching event data
            event_data = next((e for e in event_statuses if e[0] == status), None)
            if event_data:
                event_time = base_time + timedelta(minutes=i * random.randint(5, 30))
                event_log = OpswatPatchEventLog(
                    opswat_scheduled_product_installer=scheduled_installer,
                    status=event_data[0],
                    error_code=event_data[1] or "",
                    details_public_facing=event_data[2],
                    details=f"Internal: {event_data[2]} at {event_time}"
                )
                # Manually set created time
                event_log.created = event_time
                self.event_logs.append(event_log)

        # Update scheduled installer's final status
        if status_choice:
            scheduled_installer.status = status_choice[-1]
            if status_choice[-1] == RETRYING:
                scheduled_installer.retry_count = random.randint(1, 3)

    def populate_data(self):
        """
        Helper function to create app_installs and related models
        to enable creating larger fake organisations.
        """
        assert settings.IS_PROD is False

        # For reproducibility
        factory.random.reseed_random('fixed_seed_for_reproducibility')

        self.create_software_packages()
        self.group = GroupFactory(organisation=self.organisation)
        app_users = AppUserFactory.create_batch(self.count, organisation=self.organisation)

        operating_system = OperatingSystemFactory()
        self.desktop_app_checks = AppCheck.objects.filter(order__isnull=False, active=True, platform_type=AppCheck.DESKTOP)
        self.mobile_app_checks = AppCheck.objects.filter(active=True, platform_type=AppCheck.MOBILE)
        try:
            with transaction.atomic():
                if not hasattr(self.organisation, 'trustd_customer'):
                    self.trustd_customer, _ = TrustdCustomer.objects.get_or_create(organisation=self.organisation)
        except IntegrityError:
            pass
        self.trustd_customer = self.organisation.trustd_customer

        organisation_policy = OrganisationPolicyFactory(organisation=self.organisation)
        organisation_policy.groups.add(self.group)
        organisation_policy.save()
        organisation_policy_version = OrganisationPolicyVersionFactory(version=1, policy=organisation_policy)
        for i, app_user in enumerate(app_users):

            # Use deterministic pass percentage instead of random
            pass_percentage = 100 if i % 3 == 0 else 75
            is_admin_account = True if i % 2 == 0 else False

            if i !=0 and i % 5 == 0:
                # Create only some V5 app installs
                app_install_kwargs = {
                    'device_type': AppInstall.DESKTOP,
                    # Use fixed version 5.5.0 to support OPSWAT
                    'app_version': '5.5.0',
                    'os_info': {"build": "24C5079e", "kernel": "24.2.0", "release": "macOS Sequoia", "version": "15.2", "platform": "darwin"},
                }
                self.create_app_install(app_install_kwargs, app_user, operating_system, pass_percentage, is_admin_account)
                self.app_user_analytics.append(AppUserAnalyticsFactory.build(appuser=app_user, pass_percentage=pass_percentage))
                continue

            # Use fixed version 4.2.0
            app_install_kwargs = {'app_version': '4.2.0', 'device_type': AppInstall.DESKTOP}
            app_install_v4 = self.create_app_install(app_install_kwargs, app_user, operating_system, pass_percentage, is_admin_account)
            if i % 2 == 0:
                self.policy_agreements.append(AppInstallPolicyAgreementFactory.build(app_install=app_install_v4, version=organisation_policy_version, read_date=datetime.now()))
            else:
                self.policy_agreements.append(AppInstallPolicyAgreementFactory.build(app_install=app_install_v4, version=organisation_policy_version, agreed_date=datetime.now()))

            if i % 2 == 0:
                # Create a V5 app_install
                # Use fixed version 5.5.0 to support OPSWAT
                app_version = '5.5.0'
                app_install_kwargs = {
                    'app_version': app_version,
                    'device_type': AppInstall.DESKTOP,
                    'device_id': f"{CAP_V_FIVE_BETA_PREFIX}{app_install_v4.device_id}",
                    'serial_number': app_install_v4.serial_number,
                    'os_info': {"build": "24H2", "kernel": "26100", "release": "Microsoft Windows 11 Pro", "version": "10.0.26100", "platform": "win32"}
                }
                self.create_app_install(
                    app_install_kwargs, app_user, operating_system, pass_percentage, is_admin_account,
                )

                # Create a mobile app install
                app_version = '3.2.0'
                app_install_kwargs = {
                    'app_version': app_version,
                    'device_type': AppInstall.MOBILE,
                    'platform': PLATFORM_IOS,
                    'serial_number': app_install_v4.serial_number + 'mobile_old',
                    'os_info': {"build": "", "release": "android 15", "version": "15", "platform": "android"}
                }
                self.create_app_install(
                    app_install_kwargs, app_user, operating_system, pass_percentage, is_admin_account,
                    is_mobile=True
                )

                # Create a trustd app install
                app_version = f'3.{random.randint(0, 9)}.{random.randint(0, 9)}'
                app_install_kwargs = {
                    'app_version': app_version,
                    'device_type': AppInstall.MOBILE,
                    'platform': PLATFORM_IOS,
                    'serial_number': app_install_v4.serial_number + 'trustd',
                    'os_info': {"build": "", "release": "android 15", "version": "15", "platform": "android"}
                }
                trustd_app_install = self.create_app_install(
                    app_install_kwargs, app_user, operating_system, pass_percentage, is_admin_account,
                    is_mobile=True
                )
                self.trustd_devices.append(TrustdDeviceFactory.build(app_install=trustd_app_install, customer=self.trustd_customer))

            self.app_user_analytics.append(AppUserAnalyticsFactory.build(appuser=app_user, pass_percentage=pass_percentage))

        self.bulk_create_all()
        self.group.app_users.add(*app_users)
        self._update_app_reports_date_created(self.app_reports)


    def bulk_create_all(self):
        OSEndOfLife.objects.bulk_create(self.os_end_of_lifes)
        AppInstall.objects.bulk_create(self.app_installs)
        AppReport.objects.bulk_create(self.app_reports)

        # Bulk create software packages before AppOSInstalledSoftware
        SoftwarePackage.objects.bulk_create(self.software_packages)

        AppOSInstalledSoftware.objects.bulk_create(self.app_os_installed_softwares)
        AppInstallOSUser.objects.bulk_create(self.os_users)
        AppInstallAnalytics.objects.bulk_create(self.app_install_analytics)
        UserLoginHistory.objects.bulk_create(self.user_login_histories)
        CheckResult.objects.bulk_create(self.check_results)
        AppInstallCheckStatus.objects.bulk_create(self.app_install_check_statuses)
        AppUserAnalytics.objects.bulk_create(self.app_user_analytics)
        TrustdDevice.objects.bulk_create(self.trustd_devices)
        AppInstallPolicyAgreement.objects.bulk_create(self.policy_agreements)

        # Bulk create installed products first
        InstalledProduct.objects.bulk_create(self.installed_products)

        # Now create InstalledProductVersion objects with signatures
        installed_product_version_objects = []
        for installed_product, product_version, signature in self.installed_product_versions:
            # Create the through model with the signature
            ipv = InstalledProductVersion(
                installed_product=installed_product,
                product_version=product_version,
                signature=signature
            )
            installed_product_version_objects.append(ipv)

        # Bulk create the InstalledProductVersion objects
        InstalledProductVersion.objects.bulk_create(installed_product_version_objects)

        # Bulk create scheduled installers and event logs
        if self.scheduled_installers:
            OpswatScheduledProductInstaller.objects.bulk_create(self.scheduled_installers)

        if self.patch_jobs:
            OpswatPatchJob.objects.bulk_create(self.patch_jobs)

        if self.patch_attempts:
            OpswatPatchAttempt.objects.bulk_create(self.patch_attempts)

        if self.event_logs:
            OpswatPatchEventLog.objects.bulk_create(self.event_logs)

        # Skip this in tests as it causes issues with Organisation object serialization
        if not getattr(settings, 'IS_TEST', False):
            update_installed_software_individual_for_organisation(self.organisation.id)
            update_installed_software_organisation_individual(self.organisation.id)

        self._update_app_reports_date_created(self.app_reports)

    def _update_app_reports_date_created(self, app_reports):
        updates = []
        time_deltas = [1, 7]  # days for yesterday and a week ago
        ten_percent_count = int(len(app_reports) * 0.1)
        for i, days in enumerate(time_deltas):
            base_time = datetime.now() - timedelta(days=days)
            start_index = i * ten_percent_count
            end_index = (i + 1) * ten_percent_count
            updates.extend([(app_report.pk, base_time) for app_report in app_reports[start_index:end_index]])

        AppReport.objects.bulk_update(
            [AppReport(pk=pk, created=timestamp) for pk, timestamp in updates],
            ['created']
        )
