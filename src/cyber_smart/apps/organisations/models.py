from __future__ import annotations

import datetime
import json
import logging
import os
import uuid
from datetime import timedelta
from typing import Optional, Union
from urllib.parse import urljoin

import waffle
from allauth.socialaccount.models import SocialToken
from citext.fields import CICharField
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import User, Permission
from django.contrib.sites.models import Site
from django.core.cache import cache
from django.core.files import File
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models, transaction
from django.db.models import Avg, Case, Count, F, Q, QuerySet, When, Subquery, OuterRef, Exists
from django.db.models.fields.files import FieldFile
from django.http import HttpRequest
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from model_utils import Choices
from model_utils.models import TimeStampedModel
from simple_history.models import HistoricalRecords
from upload_validator import FileTypeValidator

from accounts.templatetags.check_permissions import (
    has_access_to_dashboard_access_page, has_access_to_manage_app_users_page,
)
from beta_features.mixins import BetaDevicesMetricsMethodsMixin
from beta_features.models import BetaFeature, BETA_FEATURES_REQUIRING_SOFTWARE_SUPPORT, TRUSTD_LA_SWITCH
from beta_features.utils import get_beta_app_install_queryset_filter
from billing.mixins import BillingEnabledModelMixin
from billing.providers.chargebee import plans
from billing.providers.chargebee.models import SUBSCRIPTION_ACTIVE, Subscription
from billing.providers.chargebee.plans import ALL_BUNDLE_PLANS
from billing.utils import get_plan_object_from_cache, has_active_subscriptions
from common.mixins import DevicesMetricsMethodsMixin
from common.models import (
    CRUDManager, CRUDSignalMixin, ProjectSettings, SecureIDMixin, Group,
    StaticFiles,
)
from common.upload_path_creator import upload_file_securely, upload_to_policies
from common.utils import get_http_protocol_url_name
from common.validators import domain_name_validator
from insurance.insurers.superscript.models import SuperscriptAccountReferenceMixin
from insurance.insurers.sutcliffe import SUTCLIFFE_SWITCH
from insurance.models import InsurerMixin, InsuranceOptIn
from insurance.utils import get_opt_in_survey_response
from organisations.managers import OrganisationQuerySet
from organisations.mixins import OrganisationFeatures
from organisations.utils import is_generic_domain
from partners import DIRECT_CUSTOMER, get_organisation_partner_type, DISTRIBUTOR_PARTNER, DISTRIBUTOR_PARTNER_CUSTOMER
from rulebook.models import (
    AppCheck, CERTIFICATES, CERTIFICATE_SHORT_TYPES, CYBERSMART_COMPLETE, CYBER_ESSENTIALS,
    CYBER_ESSENTIALS_PLUS, CertificationVersion, GDPR, IASME_CYBER_ASSURANCE,
    IASME_GOVERNANCE, MIGRATION_ALLOWED_TYPES, ESSENTIAL_EIGHT,
    NIS2,
    SurveyDeclaration,
)
from smart_policies.utils import ALLOWED_EXTENSIONS, ALLOWED_TYPES
from smart_score.models import OrganisationSmartScore, SmartScoreComponent

DEFAULT_ROLE_NAME = 'Full Dashboard Admin'
ABSOLUTE_URL_FORMAT = '{protocol}://{domain}'

logger = logging.getLogger(__name__)

class Organisation(
    TimeStampedModel, SecureIDMixin, SuperscriptAccountReferenceMixin, CRUDSignalMixin, BillingEnabledModelMixin,
    DevicesMetricsMethodsMixin, BetaDevicesMetricsMethodsMixin
):
    """
    This model represents an organisation.
    """
    ORG_CHOICES = (
        ('', ''),
        ('APRE', _('Academia - Pre Schools')),
        ('APRI', _('Academia - Primary Schools')),
        ('ASEC', _('Academia - Secondary Schools')),
        ('AACA', _('Academia - Academies')),
        ('ACOL', _('Academia - Colleges')),
        ('AUNI', _('Academia - Universities')),
        ('AERO', _('Aerospace')),
        ('AFAF', _('Agriculture, Forestry and Fishing')),
        ('AUTO', _('Automotive')),
        ('CHAR', _('Charities')),
        ('CHEM', _('Chemicals')),
        ('CNUC', _('Civil Nuclear')),
        ('CONS', _('Construction')),
        ('CONU', _('Consultancy')),
        ('DEFE', _('Defence')),
        ('DIPL', _('Diplomacy')),
        ('ESER', _('Emergency Services')),
        ('EELE', _('Energy - Electricity')),
        ('EGAS', _('Energy - Gas')),
        ('EOIL', _('Energy - Oil')),
        ('ENGI', _('Engineering')),
        ('ENVI', _('Environmental')),
        ('FINA', _('Finance')),
        ('FOOD', _('Food')),
        ('GOVE', _("Government")),
        ('HEAL', _('Health')),
        ('HFOO', _('Hospitality - Food')),
        ('HACC', _('Hospitality - Accomodation')),
        ('HHOT', _('Hospitality - Hotels')),
        ('ITEC', _('IT')),
        ('INTE', _('Intelligence')),
        ('LEGA', _('Legal')),
        ('LESU', _('Leisure')),
        ('MSIT', _('Managed Services - IT Managed Services')),
        ('MSOM', _('Managed Services - Other Managed Services')),
        ('MANU', _('Manufacturing')),
        ('MEDI', _('Media')),
        ('MORG', _('Membership Organisations')),
        ('MINI', _('Mining')),
        ('PHAR', _('Pharmaceuticals')),
        ('POLI', _('Political')),
        ('PSER', _('Postal Services')),
        ('PROP', _('Property')),
        ('RAAD', _('R&D')),
        ('RETA', _('Retail')),
        ('TELE', _('Telecoms')),
        ('TAVI', _('Transport - Aviation')),
        ('TRAI', _('Transport - Rail')),
        ('TROA', _('Transport - Road')),
        ('TOTH', _('Transport - Maritime')),
        ('WMAN', _('Waste Management')),
        ('WATE', _('Water')),
        ('OVER', _('Overseas')),
        ('OTHE', _('Other (please describe)')),
        ("LESOC", _("Law Enforcement (Serious & Organised Crime)")),
    )

    TYPE_OF_CUSTOMER_CHOICES = Choices(
        (0, 'NONE', 'None'),
        (1, 'AVIVA', 'Aviva'),
        (2, 'SUPERSCRIPT', 'Superscript'),
        (3, 'STARLING', 'Starling')
    )

    # Note: if you remove any of these Organisation sizes or add new ones
    # notify RevOps team (managing SalesForce and ETL)
    # as it might break their ETL pipeline.
    ORGANISATION_SIZE_0 = "0 EMPLOYEES"
    ORGANISATION_SIZE_1 = "1 EMPLOYEE"
    ORGANISATION_SIZE_2_4 = "2-4 EMPLOYEES"
    ORGANISATION_SIZE_5_9 = "5-9 EMPLOYEES"
    ORGANISATION_SIZE_10_19 = "10-19 EMPLOYEES"
    ORGANISATION_SIZE_20_34 = "20-34 EMPLOYEES"
    ORGANISATION_SIZE_35_49 = "35-49 EMPLOYEES"
    ORGANISATION_SIZE_50_99 = "50-99 EMPLOYEES"
    ORGANISATION_SIZE_100_249 = "100-249 EMPLOYEES"
    ORGANISATION_SIZE_250_499 = "250-499 EMPLOYEES"
    ORGANISATION_SIZE_500_AND_MORE = "500 OR MORE EMPLOYEES"

    ORGANISATION_SIZE_10_19 = "10-19 EMPLOYEES"
    ORGANISATION_SIZE_50_99 = "50-99 EMPLOYEES"
    ORGANISATION_SIZE_100_249 = "100-249 EMPLOYEES"
    ORGANISATION_SIZE_250_499 = "250-499 EMPLOYEES"
    ORGANISATION_SIZE_500_749 = "500-749 EMPLOYEES"
    ORGANISATION_SIZE_750_999 = "750-999 EMPLOYEES"
    ORGANISATION_SIZE_1000_1499 = "1000-1499 EMPLOYEES"
    ORGANISATION_SIZE_1499_AND_MORE = "1499 OR MORE EMPLOYEES"

    ORGANISATION_SIZE_RANGE = {
        ORGANISATION_SIZE_0: range(0, 1),
        ORGANISATION_SIZE_1: range(1, 2),
        ORGANISATION_SIZE_2_4: range(2, 5),
        ORGANISATION_SIZE_5_9: range(5, 10),
        ORGANISATION_SIZE_10_19: range(10, 20),
        ORGANISATION_SIZE_20_34: range(20, 35),
        ORGANISATION_SIZE_35_49: range(35, 50),
        ORGANISATION_SIZE_50_99: range(50, 100),
        ORGANISATION_SIZE_100_249: range(100, 250),
        ORGANISATION_SIZE_250_499: range(250, 500),
        ORGANISATION_SIZE_500_AND_MORE: range(500, 1000000)
    }

    ORGANISATION_SIZE_CHOICES_DIRECT = (
        (ORGANISATION_SIZE_1, _("It's just me!")),
        (ORGANISATION_SIZE_2_4, _("Approx 2-4 staff")),
        (ORGANISATION_SIZE_5_9, _("Approx 5-9 staff")),
        (ORGANISATION_SIZE_10_19, _("Approx 10-19 staff")),
        (ORGANISATION_SIZE_20_34, _("Approx 20-34 staff")),
        (ORGANISATION_SIZE_35_49, _("Approx 35-49 staff")),
        (ORGANISATION_SIZE_50_99, _("Approx 50-99 staff")),
        (ORGANISATION_SIZE_100_249, _("Approx 100-249 staff")),
        (ORGANISATION_SIZE_250_499, _("Approx 250-499 staff")),
        (ORGANISATION_SIZE_500_AND_MORE, _("More than 500 staff"))
    )

    ORGANISATION_SIZE_CHOICES_CHANNEL = (
        (ORGANISATION_SIZE_2_4, _("Approx 2-4 staff")),
        (ORGANISATION_SIZE_5_9, _("Approx 5-9 staff")),
        (ORGANISATION_SIZE_10_19, _("Approx 10-19 staff")),
        (ORGANISATION_SIZE_20_34, _("Approx 20-34 staff")),
        (ORGANISATION_SIZE_35_49, _("Approx 35-49 staff")),
        (ORGANISATION_SIZE_50_99, _("Approx 50-99 staff")),
        (ORGANISATION_SIZE_100_249, _("Approx 100-249 staff")),
        (ORGANISATION_SIZE_250_499, _("Approx 250-499 staff")),
        (ORGANISATION_SIZE_500_749, _("Approx 500-749 staff")),
        (ORGANISATION_SIZE_750_999, _("Approx 750-999 staff")),
        (ORGANISATION_SIZE_1000_1499, _("Approx 1000-1499 staff")),
        (ORGANISATION_SIZE_1499_AND_MORE, _("More than 1499 staff"))
    )
    ORGANISATION_SIZE_CHOICES = tuple(
        dict.fromkeys(ORGANISATION_SIZE_CHOICES_DIRECT + ORGANISATION_SIZE_CHOICES_CHANNEL)
    )

    @classmethod
    def get_organisation_size_choices_channel(cls) -> tuple[tuple[str, str]]:
        return cls.ORGANISATION_SIZE_CHOICES_CHANNEL

    @classmethod
    def get_organisation_size_channel_default_2_4(cls) -> str:
        return cls.ORGANISATION_SIZE_2_4

    @classmethod
    def get_organisation_size_channel_default_1(cls) -> str:
        return cls.ORGANISATION_SIZE_1

    MAX_CAP_USERS_1 = 1
    MAX_CAP_USERS_4 = 4
    MAX_CAP_USERS_9 = 9
    MAX_CAP_USERS_19 = 19
    MAX_CAP_USERS_34 = 34
    MAX_CAP_USERS_49 = 49
    MAX_CAP_USERS_99 = 99
    MAX_CAP_USERS_249 = 249
    MAX_CAP_USERS_499 = 499
    MAX_CAP_USERS_CHOICES_DIRECT = (
        (MAX_CAP_USERS_1, MAX_CAP_USERS_1),
        (MAX_CAP_USERS_4, MAX_CAP_USERS_4),
        (MAX_CAP_USERS_9, MAX_CAP_USERS_9),
        (MAX_CAP_USERS_19, MAX_CAP_USERS_19),
        (MAX_CAP_USERS_34, MAX_CAP_USERS_34),
        (MAX_CAP_USERS_49, MAX_CAP_USERS_49),
        (MAX_CAP_USERS_99, MAX_CAP_USERS_99),
        (MAX_CAP_USERS_249, MAX_CAP_USERS_249),
        (MAX_CAP_USERS_499, MAX_CAP_USERS_499),
    )

    MAX_CAP_USERS_749 = 749
    MAX_CAP_USERS_999 = 999
    MAX_CAP_USERS_1499 = 1499

    MAX_CAP_USERS_CHOICES_CHANNEL = (
        (MAX_CAP_USERS_9, MAX_CAP_USERS_9),
        (MAX_CAP_USERS_19, MAX_CAP_USERS_19),
        (MAX_CAP_USERS_49, MAX_CAP_USERS_49),
        (MAX_CAP_USERS_99, MAX_CAP_USERS_99),
        (MAX_CAP_USERS_249, MAX_CAP_USERS_249),
        (MAX_CAP_USERS_499, MAX_CAP_USERS_499),
        (MAX_CAP_USERS_749, MAX_CAP_USERS_749),
        (MAX_CAP_USERS_999, MAX_CAP_USERS_999),
        (MAX_CAP_USERS_1499, MAX_CAP_USERS_1499),
    )

    MAX_CAP_USERS_CHOICES = (
            MAX_CAP_USERS_CHOICES_DIRECT + MAX_CAP_USERS_CHOICES_CHANNEL
    )

    YES_NO = (
        (True, _('Yes')),
        (False, _('No'))
    )
    BULK_SELF = (
        (False, _('Individual enrolment')),
        (True, _('Bulk deployment')),
    )
    BULK_SELF_EDIT = (
        (False, _('Individual enrolment')),
        (True, _('Bulk deployment [advanced]')),
    )
    APP_DEPLOYMENT_METHOD = (
        (False, _('Individual')),
        (True, _('Bulk (advanced)'))
    )
    SEF_CHOICES = (
        ('daily', _('Daily')),
        ('weekly', _('Weekly')),
        ('monthly', _('Monthly'))
    )
    PRICING_ANNUAL = 0
    PRICING_MONTHLY = 1
    PRICING_POOLED = 2
    PRICING_ANNUAL_V2 = 3
    DIRECT_CUSTOMER_V4_V5_PRICING_ANNUAL = 4
    DIRECT_CUSTOMER_V4_V5_PRICING_MONTHLY = 5
    PRICING_MONTHLY_V4 = 6
    PRICING_CUSTOM_BUNDLE_ANNUAL = 7
    PRICING_CUSTOM_BUNDLE_MONTHLY = 8
    PRICING_BUNDLE_ANNUAL = 9
    PRICING_BUNDLE_MONTHLY = 10
    DIRECT_CUSTOMER_V6_PRICING_MONTHLY = 11
    PRICING_BAND = (
        (PRICING_MONTHLY_V4, 'Partner: Monthly v4'),
        (PRICING_CUSTOM_BUNDLE_ANNUAL, 'Partner: Custom Bundle Annual v4'),
        (PRICING_CUSTOM_BUNDLE_MONTHLY, 'Partner: Custom Bundle Monthly v4'),
        (PRICING_BUNDLE_ANNUAL, 'Partner: Bundle Annual v5'),
        (PRICING_BUNDLE_MONTHLY, 'Partner: Bundle Monthly v5'),
        (PRICING_ANNUAL_V2, 'Partner: Annual v2'),
        (PRICING_ANNUAL, 'Direct: Annual v3'),
        (PRICING_MONTHLY, 'Direct: Monthly v3'),
        (DIRECT_CUSTOMER_V4_V5_PRICING_ANNUAL, 'Direct: Annual v4/v5'),
        (DIRECT_CUSTOMER_V4_V5_PRICING_MONTHLY, 'Direct: Monthly v4/v5'),
        (DIRECT_CUSTOMER_V6_PRICING_MONTHLY, 'Direct: Monthly v6'),

    )
    PRICING_BAND_DISTRIBUTOR = (
        (PRICING_MONTHLY_V4, 'Monthly v4'),
        (PRICING_ANNUAL_V2, 'Annual v2')
    )

    # BUNDLES
    # These constants here look like Certification types, but actually are bundles (plans) that are
    # applied to organisations. They are connected to billing.
    CYBER_ESSENTIALS = 1
    CYBER_ESSENTIALS__CYBER_ESSENTIALS_PLUS = 2
    CYBER_ESSENTIALS__GDPR = 3
    CYBER_ESSENTIALS__CYBER_ESSENTIALS_PLUS__GDPR = 4
    CYBER_SMART_COMPLETE = 5
    ESSENTIAL_EIGHT = 10
    NIS2 = 11
    BUNDLES = (
        (CYBER_ESSENTIALS, "Cyber Essentials & CyberSmart Active Protect"),
        (CYBER_SMART_COMPLETE, "CyberSmart Complete"),
    )
    CUSTOM_BUNDLES = (
        (CYBER_ESSENTIALS, "Cyber Essentials"),
        (CYBER_ESSENTIALS__CYBER_ESSENTIALS_PLUS, 'Cyber Essentials & Cyber Essentials Plus'),
        (CYBER_ESSENTIALS__GDPR, 'Cyber Essentials & Privacy Toolbox'),
        (CYBER_ESSENTIALS__CYBER_ESSENTIALS_PLUS__GDPR, 'Cyber Essentials, Cyber Essentials Plus & Privacy Toolbox'),
    )

    # BUNDLES COMMITMENT
    ONE_YEAR_COMMITMENT = 1
    TWO_YEAR_COMMITMENT = 2
    BUNDLE_COMMITMENTS = [
        (ONE_YEAR_COMMITMENT, _('1 Year')),
        # infrequently used so commented out for now
        # (TWO_YEAR_COMMITMENT, '2 Year')
    ]

    name = models.CharField(max_length=250)
    industry = models.CharField(max_length=255, choices=ORG_CHOICES)
    industry_description = models.TextField(blank=True, default="")
    # "no_of_staff" is legacy field
    # we now use "size" field
    no_of_staff = models.CharField(
        verbose_name=_("Legacy"), max_length=255, null=True, blank=True
    )
    size = models.CharField(
        verbose_name=_("Organisation size (number of employees)"), max_length=255, choices=ORGANISATION_SIZE_CHOICES
    )
    max_cap_users = models.PositiveIntegerField(
        help_text='Maximum number of CyberSmart Active Protect (CAP) user licenses',
        blank=True, null=True, choices=MAX_CAP_USERS_CHOICES
    )
    email_message = models.TextField(blank=True, null=True)
    bulk_install = models.BooleanField(default=False, choices=BULK_SELF)
    showmodal = models.BooleanField(default=False)
    security_emails_frequency = models.CharField(default='weekly', max_length=7, choices=SEF_CHOICES)
    partner = models.ForeignKey('partners.Partner', related_name='organisations', on_delete=models.CASCADE)
    is_partner_org = models.BooleanField(verbose_name='Is Partner Organisation', default=False)
    software_support = models.BooleanField(verbose_name='Enable software', default=False)
    data_privacy_support = models.BooleanField(verbose_name='Enable Data Privacy feature', default=False)
    pricing_band = models.IntegerField(verbose_name='Pricing band', choices=PRICING_BAND, default=PRICING_ANNUAL)
    license_price = models.DecimalField(verbose_name='Per License Price', max_digits=6, decimal_places=2, blank=True,
                                        null=True)
    ce_price = models.DecimalField(verbose_name='Cyber Essentials Price', max_digits=6, decimal_places=2, blank=True,
                                   null=True)
    gdpr_price = models.DecimalField(verbose_name='GDPR Price', max_digits=6, decimal_places=2, blank=True, null=True)
    external_uuid = models.UUIDField(unique=True, default=uuid.uuid4, editable=False)
    is_test = models.BooleanField(verbose_name='Test organisation', default=False)
    is_trial = models.BooleanField(verbose_name="Trial organisation", default=False)
    disable_user_fix = models.BooleanField(verbose_name='Disable user fix', default=True)
    legal_company_name = models.CharField(verbose_name='Legal company name', max_length=255, null=True, blank=True)
    legal_company_number = models.CharField(verbose_name='Legal company number', max_length=255, null=True, blank=True)
    email = models.EmailField(verbose_name='Company email', null=True, blank=True)
    phone = models.CharField(verbose_name='Phone', max_length=255, null=True, blank=True)
    marketplace_tile_enabled = models.BooleanField(verbose_name='Starling Marketplace Tile enabled', default=False)
    type_of_customer = models.PositiveSmallIntegerField(verbose_name='Type of customer',
                                                        choices=TYPE_OF_CUSTOMER_CHOICES,
                                                        default=TYPE_OF_CUSTOMER_CHOICES.NONE)
    main_distributor_org = models.BooleanField(verbose_name='Main distributor organisation', default=False)
    company_relationship = models.CharField(
        verbose_name="Company relationship", max_length=255, null=True, blank=True, editable=False
    )
    # Note: this field for testing environment only.
    # we're using it to simulate large number of app installs for testing billing
    simulate_installed_devices_count = models.IntegerField(
        verbose_name='Simulate Installed Devices Count',
        help_text=' This field value will override the number of App installed.', null=True, blank=True)
    # Stores the selected chargebee plan id while onbaording direct customer.
    plans_ids = models.CharField(verbose_name="Chosen chargebee plans", max_length=255, null=True, blank=True)
    user_journey_passed = models.DateTimeField(null=True, blank=True)
    trainings_tab_enabled = models.BooleanField(verbose_name="No longer used", null=True, blank=True, default=False)
    enable_onboarding_email = models.BooleanField(default=True)  # temporary field
    coupon_code = models.CharField(
        verbose_name='Coupon code',
        help_text='Coupon code to be used for subscriptions created later in time (e.g. new certificate started)',
        max_length=255, null=True, blank=True
    )
    cep_audit_request_date = models.DateTimeField(
        verbose_name="The date at which CE+ audit was booked.",
        help_text="It is the date when the request was submitted (not when the audit will happen). This values gets reset (set to None) after a CE+ audit is finished.",
        null=True, blank=True, default=None
    )
    partner_before_cancellation = models.ForeignKey(
        'partners.Partner',
        verbose_name='Partner before cancellation',
        help_text='The partner this organisation was assigned to before cancellation',
        related_name='cancelled_organisations', on_delete=models.CASCADE,
        null=True, blank=True
    )

    objects = OrganisationQuerySet.as_manager()

    # this needs to be registered here otherwise tests fail
    history = HistoricalRecords(
        app='history', history_user_id_field=models.IntegerField(null=True, blank=True)
    )

    class Meta:
        verbose_name = 'Organisation'
        verbose_name_plural = 'Organisations'

    bulk_install.boolean = True

    def __unicode__(self):
        return "{}".format(self.name)

    def __str__(self):
        return self.__unicode__()

    @property
    def has_ce_100k_r_and_r_toolbox_insurance(self):
        """
        Returns True if organisation has CE 100k superscript insurance
        with respective ransomware and recovery toolbox subscription.
        :return: True or False
        :rtype: bool
        """
        if self.ce_certification and self.ce_certification.has_100k_active_insurance and self.has_r_and_r_toolbox_subscription:
            return True
        return False

    @property
    def can_upgrade_to_100k_r_and_r_toolbox_insurance(self) -> bool:
        """
        Returns True if organisation is eligible for upgrading to 100k insurance with ransomware and recovery toolbox.
        """
        return self.is_eligible_for_r_and_r_toolbox and not self.has_ce_100k_r_and_r_toolbox_insurance

    @property
    def is_eligible_for_r_and_r_toolbox(self) -> bool:
        """
        Returns True if organisation is eligible for ransomware and recovery toolbox.
        """
        return not settings.IS_AUS_GEO and self.is_direct_customer

    @property
    def is_eligible_for_100k_insurance(self) -> bool:
        """
        Returns True if organisation is eligible for upgrading to 100k insurance.
        """
        return self.eligible_for_100k_insurance()

    def eligible_for_100k_insurance(self, with_issued_cert=True) -> bool:
        """
        Returns True if organisation is eligible for upgrading to 100k insurance.
        :param with_issued_cert: If True, check pass an additional check for issued cert.
        :type with_issued_cert: bool
        """
        ce_certification = self.ce_certification

        if not ce_certification or self.partner.iasme_cb:
            return False

        if with_issued_cert and not ce_certification.has_issued_certifications:
            return False

        # Check if the organisation is a partner's own or we have enabled 100k for partner regardless of bundle
        if self.is_partner_org or self.partner.include_100k_insurance:
            return True
        # Otherwise, check the bundle includes software support
        return self.has_any_bundle and (not self.is_custom_bundle or self.has_software_support)

    @property
    def is_eligible_for_250k_insurance(self):
        """
        Returns True if organisation is eligible for upgrading to 250k insurance.
        """
        return self.eligible_for_250k_insurance()

    def eligible_for_250k_insurance(self, with_issued_cert=True) -> bool:
        """
        Returns True if organisation is eligible for upgrading to 250k insurance.
        :param with_issued_cert: If True, check pass an additional check for issued cert.
        :type with_issued_cert: bool
        """
        ce_certification = self.ce_certification

        if not ce_certification or self.partner.iasme_cb:
            return False

        if with_issued_cert and not ce_certification.has_issued_certifications:
            return False

        if not self.is_partner_org:
            return self.has_subscription_that_enables_250k_insurance

        subscriptions = self.get_subscriptions_queryset()
        if not subscriptions:
            return False

        return subscriptions.filter(plan_id__in=plans.ALL_CEP_PLANS).exists()

    @property
    def maximum_eligible_insurance_upgrade_pre_issued_certificate(self):
        """
        Returns the maximum insurance upgrade amount if possible, otherwise None.
        """
        if self.eligible_for_250k_insurance(with_issued_cert=False):
            return InsuranceOptIn.COVERAGE.CE_250K
        if self.eligible_for_100k_insurance(with_issued_cert=False):
            return InsuranceOptIn.COVERAGE.CE_100K
        return None

    @property
    def maximum_eligible_insurance_upgrade_pre_issued_certificate_int(self) -> Optional[str]:
        """
        Returns the maximum insurance upgrade amount if possible, otherwise None.
        """
        if not self.maximum_eligible_insurance_upgrade_pre_issued_certificate:
            return None
        return InsuranceOptIn.COVERAGE_TO_AMOUNT[self.maximum_eligible_insurance_upgrade_pre_issued_certificate]

    @property
    def maximum_eligible_insurance_upgrade_pre_issued_certificate_long(self) -> Optional[str]:
        """
        Returns the maximum insurance upgrade amount if possible, otherwise None.
        """
        if not self.maximum_eligible_insurance_upgrade_pre_issued_certificate:
            return None
        return f'£{self.maximum_eligible_insurance_upgrade_pre_issued_certificate_int:,}'

    @property
    def has_subscription_that_enables_250k_insurance(self):
        subscriptions = self.get_subscriptions_queryset()
        if not subscriptions:
            return False
        return subscriptions.live_subscriptions().filter(plan_id__in=plans.PARTNER_250K_INSURANCE_PLANS).exists()

    @cached_property
    def maximum_insurance_coverage_according_to_subscriptions(self):
        """
        Returns the maximum subscription amount according to the subscriptions.
        """
        if self.has_subscription_that_enables_250k_insurance:
            return InsuranceOptIn.COVERAGE.CE_250K
        return InsuranceOptIn.COVERAGE.CE_100K

    @cached_property
    def maximum_insurance_coverage_according_to_subscriptions_int(self):
        """
        Returns the maximum subscription amount according to the subscriptions.
        """
        return InsuranceOptIn.COVERAGE_TO_AMOUNT[self.maximum_insurance_coverage_according_to_subscriptions]

    @cached_property
    def maximum_insurance_coverage_according_to_subscriptions_long(self) -> str:
        """
        Returns the maximum subscription amount according to the subscriptions
        formatted as a long amount: e.g. £100,000
        """
        return f'£{self.maximum_insurance_coverage_according_to_subscriptions_int:,}'

    @property
    def main_entity_filter_name(self) -> str:
        """
        Returns the name of the main entity filter for DevicesMetricsMethodsMixin.
        """
        return DevicesMetricsMethodsMixin.ORGANISATION

    def set_organisation_as_aviva_customer(self):
        self.type_of_customer = self.TYPE_OF_CUSTOMER_CHOICES.AVIVA
        self.save(update_fields=['type_of_customer'])

    @property
    def is_aviva_customer(self):
        return self.type_of_customer == self.TYPE_OF_CUSTOMER_CHOICES.AVIVA

    @property
    def is_superscript_customer(self):
        return self.type_of_customer == self.TYPE_OF_CUSTOMER_CHOICES.SUPERSCRIPT

    @property
    def is_starling_customer(self):
        return self.type_of_customer == self.TYPE_OF_CUSTOMER_CHOICES.STARLING

    def get_initial_quantity(self):
        if self.size == self.ORGANISATION_SIZE_1:
            quantity = 1
        elif self.bulk_install:
            quantity = 0
        else:
            quantity = self.app_users.filter(active=True).count()

        return quantity

    @property
    def can_change_deployment_type(self):
        """ Check if the deployment type can be changed from individual to bulk.
        * the organisation is in individual
        * the partner does not have UBA enabled
        * the distributor does not have new bulk organisations UBA only
        """
        return (
                not self.bulk_install and
                not self.partner.default_uba_enabled and
                not self.partner.distributor.new_bulk_org_uba_only
        )

    @classmethod
    def get_max_cap_users_dict_by_org_size(cls):
        """
        Returns the dict with max cap users for each org size.
        """
        return {
            cls.ORGANISATION_SIZE_1: cls.MAX_CAP_USERS_1,
            cls.ORGANISATION_SIZE_2_4: cls.MAX_CAP_USERS_4,
            cls.ORGANISATION_SIZE_5_9: cls.MAX_CAP_USERS_9,
            cls.ORGANISATION_SIZE_10_19: cls.MAX_CAP_USERS_19,
            cls.ORGANISATION_SIZE_20_34: cls.MAX_CAP_USERS_34,
            cls.ORGANISATION_SIZE_35_49: cls.MAX_CAP_USERS_49,
            cls.ORGANISATION_SIZE_50_99: cls.MAX_CAP_USERS_99,
            cls.ORGANISATION_SIZE_100_249: cls.MAX_CAP_USERS_249,
            cls.ORGANISATION_SIZE_250_499: cls.MAX_CAP_USERS_499,
            cls.ORGANISATION_SIZE_500_749: cls.MAX_CAP_USERS_749,
            cls.ORGANISATION_SIZE_750_999: cls.MAX_CAP_USERS_999,
            cls.ORGANISATION_SIZE_1000_1499: cls.MAX_CAP_USERS_1499,
        }

    def get_app_users(self):
        """ Gets active app users """
        if self.is_uba_enabled:
            app_users = self.user_attribution_appusers
        else:
            app_users = self.app_users.filter(active=True)
        return app_users

    @property
    def total_users_count(self):
        """
        Returns the total number of active users in the organisation.
        """
        return self.get_app_users().count()

    @property
    def total_beta_users_count(self) -> int:
        """
        Returns the total number of active beta users in the organisation.
        """
        return self._get_beta_app_users(self.get_app_users()).count()

    @property
    def has_max_cap_users(self):
        """ Simply to check if the organisation has max_cap_users set """
        if self.max_cap_users is not None and self.max_cap_users >= 0:
            return True
        return False

    def get_nr_new_users_allowed(self):
        """
        Returns the number of enrolled active non-admin app users that the org admin is allowed to add.
        If the organisation does not have a cap, then we return None which means there are no limits.
        """
        nr_new_users_allowed = None
        if self.has_max_cap_users:
            nr_new_users_allowed = self.max_cap_users - self.get_app_users().filter(is_admin=False).count()
            # in case the organisation is already above its limit of users, then is 0
            nr_new_users_allowed = 0 if nr_new_users_allowed < 0 else nr_new_users_allowed
        return nr_new_users_allowed

    def get_extra_app_users_id(self, app_users):
        """ Get the app users ID that we need to hide details for.
        This happens when an organisation has more users that the max CAP users,
        so we need to hide their details until the org gets upgraded """
        extra_app_users_id = []
        if self.has_max_cap_users:
            app_users_filtered = app_users.filter(is_admin=False).order_by('-id').distinct()
            nr_new_users_allowed = self.max_cap_users - app_users_filtered.count()
            if nr_new_users_allowed < 0:
                extra_users = abs(nr_new_users_allowed)
                extra_app_users_id = list(app_users_filtered[:extra_users].values_list('id', flat=True))
        return extra_app_users_id

    def get_company_quantity(self, size=None):
        """
        Returns company quantity converted to Chargebee value.
        Converts e.g. ORGANISATION_SIZE_5_9 to 9.
        :return: company quantity
        :rtype: int
        """
        if not size:
            size = self.size

        max_cap_dict = self.get_max_cap_users_dict_by_org_size()
        max_cap_dict.update({
            self.ORGANISATION_SIZE_0: 0,
            self.ORGANISATION_SIZE_500_AND_MORE: 500,
            self.ORGANISATION_SIZE_1499_AND_MORE: 1500,
        })
        return max_cap_dict.get(size)

    def get_company_quantity_starting_unit(self):
        """
        Returns company quantity starting unit.
        :return: company quantity
        :rtype: int
        """
        return {
            self.ORGANISATION_SIZE_0: 0,
            self.ORGANISATION_SIZE_1: 1,
            self.ORGANISATION_SIZE_2_4: 2,
            self.ORGANISATION_SIZE_5_9: 5,
            self.ORGANISATION_SIZE_10_19: 10,
            self.ORGANISATION_SIZE_20_34: 20,
            self.ORGANISATION_SIZE_35_49: 35,
            self.ORGANISATION_SIZE_50_99: 50,
            self.ORGANISATION_SIZE_100_249: 100,
            self.ORGANISATION_SIZE_250_499: 250,
            self.ORGANISATION_SIZE_500_AND_MORE: 500,
            self.ORGANISATION_SIZE_500_749: 500,
            self.ORGANISATION_SIZE_750_999: 750,
            self.ORGANISATION_SIZE_1000_1499: 1000,
            self.ORGANISATION_SIZE_1499_AND_MORE: 1500
        }[self.size]

    @classmethod
    def get_pervade_organisation_size_dict(cls):
        return {
            cls.ORGANISATION_SIZE_0: "Micro",
            cls.ORGANISATION_SIZE_1: "Micro",
            cls.ORGANISATION_SIZE_2_4: "Micro",
            cls.ORGANISATION_SIZE_5_9: "Micro",
            cls.ORGANISATION_SIZE_10_19: "Small",
            cls.ORGANISATION_SIZE_20_34: "Small",
            cls.ORGANISATION_SIZE_35_49: "Small",
            cls.ORGANISATION_SIZE_50_99: "Medium",
            cls.ORGANISATION_SIZE_100_249: "Medium",
            cls.ORGANISATION_SIZE_250_499: "Large",
            cls.ORGANISATION_SIZE_500_AND_MORE: "Large",
            cls.ORGANISATION_SIZE_500_749: "Large",
            cls.ORGANISATION_SIZE_750_999: "Large",
            cls.ORGANISATION_SIZE_1000_1499: "Large",
            cls.ORGANISATION_SIZE_1499_AND_MORE: "Large",
        }

    def get_pervade_organisation_size(self):
        """
        Converts our internal organisation size to the special format used in pervade API.
        """
        return self.get_pervade_organisation_size_dict()[self.size].upper()

    def get_date_created_seconds(self):
        """
        Returns date created in seconds passed from Unix time.
        :return: seconds
        :rtype: int
        """
        return (self.created.replace(tzinfo=None)  # pylint: disable=E1123
                - datetime.datetime(1970, 1, 1)).total_seconds()

    def set_as_v6(self):
        """
        Currently this is only used as part of the starling onboarding, it may be used on other views in the future
        """
        self.pricing_band = Organisation.DIRECT_CUSTOMER_V6_PRICING_MONTHLY
        self.save()

    @property
    def has_v6_pricing_band(self):
        return self.pricing_band == Organisation.DIRECT_CUSTOMER_V6_PRICING_MONTHLY

    @property
    def url(self):
        return reverse('dashboard:organisation', kwargs={'org_id': self.secure_id})

    @property
    def has_legacy_stripe(self):
        """
        Returns True if this organisation has legacy stripe account connected to a user otherwise returns False.
        :return: True of False
        :rtype: bool
        """
        User = get_user_model()
        return User.objects.filter(
            organisation_admins__organisation=self,
            user_stripe__isnull=False
        ).exists()

    @property
    def plans(self):
        """
        Returns initial Chargebee plans objects that were chosen during onboarding.
        :return: plans
        :rtype: dict
        """
        # load plans
        plans = {}
        try:
            ids = json.loads(self.plans_ids)
        except (json.decoder.JSONDecodeError, TypeError):
            return plans
        else:
            for plan_id in ids:
                plan = get_plan_object_from_cache(plan_id)
                if plan:
                    plans[plan_id] = plan
        return plans

    @property
    def has_billing(self):
        """
        Returns True if this organisation has direct customer chargebee billing info otherwise returns False.
        :return: True of False
        :rtype: bool
        """
        return hasattr(self, 'customer')

    def get_billing(self):
        """ Returns DirectCustomer or PartnerBilling if they exist """
        if self.has_billing:
            return self.customer
        elif hasattr(self.partner, 'billing'):
            return self.partner.billing
        return None

    def get_subscriptions_queryset(self) -> QuerySet:
        """ Returns queryset of DirectSubscription or PartnerSubscription if they have set up billing """
        if billing := self.get_billing():
            # direct organisations have the subscriptions linked via the DirectCustomer - see DirectSubscription
            if self.is_direct_customer:
                return billing.subscriptions
            # non-direct organisations have the subscriptions directly linked - see PartnerSubscription
            elif hasattr(self, 'subscriptions'):
                return self.subscriptions
        return None

    @property
    def has_subscription(self):
        """ Check if organisation has any live subscriptions """
        if subscriptions := self.get_subscriptions_queryset():
            return subscriptions.live_subscriptions().exists()
        return False

    @property
    def eligible_for_nfr(self):
        return self.is_partner_org and self.partner.not_for_resale

    @property
    def eligible_for_evaluation(self):
        if self.partner.distributor.enable_evaluation_licenses and self.cap_licences_quantity <= 3 and \
                self.pricing_band == Organisation.PRICING_MONTHLY_V4:
            return True
        else:
            return False

    @property
    def eligible_for_freemium(self):
        if self.pricing_band == Organisation.DIRECT_CUSTOMER_V6_PRICING_MONTHLY:
            return True
        return False

    def has_multiple_versions_of(self, certification_type):
        """
        Returns True if this organisation has multiple versions of passed certification type.
        :param certification_type: certification type
        :type certification_type: int
        :return: True of False
        :rtype: bool
        """
        return self.certifications.filter(version__type__type=certification_type).count() > 1

    def get_certifications_of(self, certification_type):
        """
        Returns certifications of passed certification type.
        :param certification_type: certification type
        :type certification_type: int
        :return: certifications
        :rtype: QuerySet
        """
        return self.certifications.filter(version__type__type=certification_type).select_related(
            "version", "version__type"
        ).order_by('version__version_number')

    @property
    def has_any_certification(self):
        """
        Returns True if any certification exist otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.certifications.all().exists()

    @property
    def has_ce_certification(self):
        """
        Returns True if Cyber Essentials certification exist otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.certifications.filter(version__type__type=CYBER_ESSENTIALS).exists()

    @property
    def has_cep_certification(self):
        """
        Returns True if Cyber Essentials + certification exist otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.certifications.filter(version__type__type=CYBER_ESSENTIALS_PLUS).exists()

    @property
    def has_iasme_certification(self):
        """
        Returns True if IASME certification exist otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.certifications.filter(version__type__type=IASME_GOVERNANCE).exists()

    @property
    def has_gdpr_certification(self):
        """
        Returns True if GDPR certification exist otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.certifications.filter(version__type__type=GDPR).exists()

    @property
    def has_iasme_cyber_assurance_certification(self):
        """
        Returns True if IASME Cyber Assurance certification exist otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.certifications.filter(version__type__type=IASME_CYBER_ASSURANCE).exists()

    @property
    def has_essential_eight_certification(self) -> bool:
        """
        Returns True if Essential 8 certification exist otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.certifications.filter(version__type__type=ESSENTIAL_EIGHT).exists()

    @property
    def has_cybersmart_complete(self):
        """
        Returns True if cybersmart complete exist otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.certifications.filter(version__type__type=CYBERSMART_COMPLETE).exists()

    @property
    def has_ce_and_gdpr_single_subscription(self):
        """
        Returns True if an organisation has single subscription for both ce and gdpr otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        if subscriptions := self.get_subscriptions_queryset():
            return subscriptions.live_subscriptions().filter(plan_id__in=plans.CE_GDPR_PLANS).exists()
        else:
            return False

    @property
    def has_gdpr_annual_subscription(self):
        """
        Returns True if an organisation has annual gdpr subscription otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        if subscriptions := self.get_subscriptions_queryset():
            return subscriptions.live_subscriptions().filter(plan_id__in=plans.GDPR_ANNUAL_PLANS_IDS).exists()
        else:
            return False

    @property
    def ce_certification(self) -> Optional[OrganisationCertification]:
        """
        Returns organisation's latest Cyber Essentials certification if exists otherwise returns None
        :return: Cyber Essentials certification
        :rtype: OrganisationCertification or None
        """
        return self.certifications.filter(
            version__type__type=CYBER_ESSENTIALS
        ).order_by('version__version_number').last() if self.has_ce_certification else None

    @property
    def oldest_ce_certification(self):
        """
        Returns organisation's oldest Cyber Essentials certification if exists otherwise returns None
        :return: Cyber Essentials certification
        :rtype: OrganisationCertification or None
        """
        return self.certifications.filter(
            version__type__type=CYBER_ESSENTIALS
        ).order_by('version__version_number').first() if self.has_ce_certification else None

    @property
    def cep_certification(self):
        """
        Returns organisation's latest Cyber Essentials + certification if exists otherwise returns None
        :return: Cyber Essentials + certification
        :rtype: OrganisationCertification or None
        """
        return self.certifications.filter(
            version__type__type=CYBER_ESSENTIALS_PLUS
        ).order_by('version__version_number').last() if self.has_cep_certification else None

    @property
    def iasme_certification(self):
        """
        Returns organisation's latest IASME certification if exists otherwise returns None
        :return: IASME certification
        :rtype: OrganisationCertification or None
        """
        return self.certifications.filter(
            version__type__type=IASME_GOVERNANCE
        ).order_by('version__version_number').last() if self.has_iasme_certification else None

    @property
    def gdpr_certification(self):
        """
        Returns organisation's latest GDPR certification if exists otherwise returns None
        :return: GDPR certification
        :rtype: OrganisationCertification or None
        """
        return self.certifications.filter(
            version__type__type=GDPR
        ).order_by('version__version_number').last() if self.has_gdpr_certification else None

    @property
    def essential_eight_certification(self) -> Optional[OrganisationCertification]:
        """
        Returns organisation's latest Essential 8 certification if exists otherwise returns None
        """
        return (
            self.certifications.filter(version__type__type=ESSENTIAL_EIGHT)
            .order_by("version__version_number")
            .last()
        )

    @property
    def nis2_certification(self):
        """
        Returns organisation's latest NIS2 certification if exists otherwise returns None
        """
        return self.certifications.filter(version__type__type=NIS2).order_by("version__version_number").last()

    @property
    def cybersmart_complete(self):
        """
        Returns organisation's latest CyberSmart Complete certification if exists otherwise returns None
        :return: CyberSmart Complete certification
        :rtype: OrganisationCertification or None
        """
        return self.certifications.filter(
            version__type__type=CYBERSMART_COMPLETE
        ).order_by('version__version_number').last() if self.has_cybersmart_complete else None

    @property
    def approved_domains_url(self):
        """
        Returns a url of organisation's approved domains view.
        :return: approved domains url
        :rtype: str or unicode
        """
        return reverse('dashboard:approved-domains', kwargs={'org_id': self.secure_id})

    @property
    def absolute_url(self):
        """
        Returns absolute url including domain name for an organisation.
        :return: absolute url
        :rtype: str
        """
        return urljoin(
            ABSOLUTE_URL_FORMAT.format(protocol=get_http_protocol_url_name(), domain=Site.objects.get_current().domain),
            reverse('dashboard:organisation', kwargs={'org_id': self.secure_id})
        )

    @property
    def absolute_trustd_registration_url(self) -> str:
        """
        Returns absolute trustd registration url including domain name for an organisation.
        :return: absolute url
        """
        return urljoin(
            ABSOLUTE_URL_FORMAT.format(protocol=get_http_protocol_url_name(), domain=Site.objects.get_current().domain),
            reverse('trustd:mobile-registration', kwargs={'org_id': self.secure_id})
        )

    def get_absolute_trustd_enrolment_url(self, token) -> str:
        """
        Returns absolute trustd enrolment url including domain name for an organisation with unique token for connection.
        :return: absolute url
        """
        return urljoin(
            ABSOLUTE_URL_FORMAT.format(protocol=get_http_protocol_url_name(), domain=Site.objects.get_current().domain),
            reverse('trustd:mobile-enrolment', kwargs={'org_id': self.secure_id})
        ) + f'?{token}'

    @property
    def absolute_policies_url(self):
        """
        Returns absolute policies url including domain name for an organisation.
        :return: absolute url
        :rtype: str
        """
        return urljoin(
            ABSOLUTE_URL_FORMAT.format(protocol=get_http_protocol_url_name(), domain=Site.objects.get_current().domain),
            reverse('smart_policies:main', kwargs={'org_id': self.secure_id})
        )

    @property
    def appusers_url(self):
        return reverse('dashboard:add-users', kwargs={'org_id': self.secure_id})

    @property
    def users_and_groups_url(self):
        return reverse('organisations:manage-users', kwargs={'org_id': self.secure_id})

    @property
    def checkreport_url(self):
        return urljoin(
            ABSOLUTE_URL_FORMAT.format(protocol=get_http_protocol_url_name(), domain=Site.objects.get_current().domain),
            reverse('dashboard:check_report', kwargs={'org_id': self.secure_id})
        )

    @property
    def policies_report_url(self):
        return urljoin(
            ABSOLUTE_URL_FORMAT.format(protocol=get_http_protocol_url_name(), domain=Site.objects.get_current().domain),
            reverse('smart_policies:policies-report', kwargs={'org_id': self.secure_id})
        )

    @property
    def bulkreport_url(self):
        return reverse('dashboard:bulk_check_report', kwargs={'org_id': self.secure_id})

    @property
    def billing_url(self):
        return reverse('dashboard:manage-organisation-billing', kwargs={'org_id': self.secure_id})

    @property
    def resend_email_to_user_url(self):
        return reverse('api-v2:send-app', kwargs={'org_id': self.secure_id})

    @property
    def manageorganisation_url(self):
        return reverse('dashboard:manage-organisation', kwargs={'org_id': self.secure_id})

    @property
    def chart_image_url(self):
        """
        Returns full path with domain name to the organisation statistic chart.
        :return: chart image
        :rtype: image
        """
        return urljoin(
            ABSOLUTE_URL_FORMAT.format(protocol=get_http_protocol_url_name(), domain=Site.objects.get_current().domain),
            reverse('dashboard:generate-chart-image', kwargs={
                'org_id': self.secure_id,
                'date': '{0}'.format(datetime.datetime.now().strftime('%Y-%m-%d'))
            })
        )

    @property
    def get_all_users(self):
        User = get_user_model()
        return User.objects.filter(
            organisation_admins__organisation=self
        )

    @property
    def get_admin_users(self):
        """
        Returns organisations administrators.
        :return: organisation administrators
        :rtype: User queryset
        """
        User = get_user_model()
        return User.objects.filter(
            organisation_admins__organisation=self,
            organisation_admins__is_admin=True
        )

    @property
    def get_subscribed_admin_users(self):
        """
        Returns admin users that subscribed to weekly email report.
        :return: subscribed users
        :rtype: OrganisationAdmin QuerySet
        """
        return self.admins.filter(
            organisation=self,
            is_admin=True,
            subscribed=True
        )

    @property
    def get_organisation_creator(self):
        """
        Returns organisation's creator
        :return: user
        :rtype: get_user_model() instance
        """
        return self.get_admin_users.first()

    @property
    def has_partner(self):
        """
        Returns True if organisation has a partner otherwise returns False.
        NOT to be confused with is_partner_org, which is only True if the organisation is the partner itself.
        :return: True or False
        :rtype: bool
        """
        return hasattr(self, 'partner') and self.partner

    @property
    def has_distributor(self):
        return self.partner and self.partner.distributor

    @property
    def partner_type(self):
        """
        Returns organisation partner type.
        :return: organisation partner type
        :rtype: int
        """
        return get_organisation_partner_type(self)

    def get_management_users(self, separate_by_group=False):
        """
        Returns all users that have management access to an organisation.
        It can be organisation administrators, partner users and distributor users.
        :param separate_by_group: if True returns users separated by groups
        :type separate_by_group: bool
        :return: management users
        :rtype: QuerySet or dict
        """
        admin_users = self.get_admin_users.values_list('id', flat=True)
        partner_users = self.partner.users.all().values_list(  # pylint: disable=no-member
            'user_id', flat=True
        ) if hasattr(self, 'partner') and self.partner else []
        distributor_users = self.partner.distributor.users.values_list(  # pylint: disable=no-member
            'user_id', flat=True) if hasattr(self, 'partner') and self.partner else []

        if separate_by_group:
            # separate users by groups
            return {
                'administrators': get_user_model().objects.filter(id__in=admin_users),
                'partners': get_user_model().objects.filter(id__in=partner_users),
                'distributors': get_user_model().objects.filter(id__in=distributor_users)
            }
        else:
            # combine users into one queryset
            return get_user_model().objects.filter(id__in=list(set(list(
                admin_users
            ) + list(
                partner_users
            ) + list(
                distributor_users
            ))))

    @property
    def get_admin_emails(self):

        users = None

        # if we have prefetched data use it
        if hasattr(self, '_prefetched_objects_cache'):
            if 'admins' in self._prefetched_objects_cache:
                users = self._prefetched_objects_cache['admins']

        # otherwise make additional request
        if not users:
            users = self.admins.filter(is_admin=True, user__email__isnull=False)

        if not users:
            return []
        else:
            return [u.user.email for u in users]

    @property
    def enrolled_users(self) -> QuerySet:
        """
        Returns enrolled users.
        An enrolled user should receive an email to install the CAP(CyberSmart App).
        """
        from appusers.models import AppUser

        if not hasattr(self, "app_users"):
            return AppUser.objects.none()

        query = self.app_users.filter(active=True)

        if self.bulk_deploy_user is not None:
            query = query.exclude(id=self.bulk_deploy_user.id)

        if self.is_uba_enabled:
            return self.user_attribution_appusers.filter(
                id__in=query.values_list('id', flat=True)
            )
        return query

    @property
    def enrolled_users_count(self):
        """
        Returns count of enrolled users.
        :return: count of enrolled users
        :rtype: int
        """
        return len(list(self.enrolled_users))

    def _get_beta_app_users(self, qs: QuerySet) -> QuerySet:
        """
        Given a queryset of AppUsers, applies filters to return only the beta users.
        """
        # Subquery to find users who have at least one device
        has_devices = Exists(
            self.app_installs.filter(
                app_user=OuterRef('pk'),
                inactive=False
            )
        )
        # Subquery to find users who have non-beta devices
        has_non_beta_devices = Exists(
            self.app_installs.filter(
                app_user=OuterRef('pk'),
                inactive=False
            ).exclude(
                id__in=Subquery(
                    self.app_installs.filter(
                        get_beta_app_install_queryset_filter(),
                        app_user=OuterRef('app_user'),
                        inactive=False
                    ).values('id')
                )
            )
        )
        return qs.filter(
            has_devices,
            ~has_non_beta_devices
        )

    @property
    def enrolled_beta_users(self) -> QuerySet:
        """
        Returns enrolled beta users.
        We define beta users as those that have only beta app installs.
        """
        return self._get_beta_app_users(self.enrolled_users)

    @property
    def enrolled_beta_users_count(self) -> int:
        """
        Returns count of enrolled beta users.
        """
        return self.enrolled_beta_users.count()

    @property
    def installed_users(self):
        """
        Returns installed users.
        :return: installed users
        :rtype: AppUser queryset
        """
        from appusers.models import AppUser
        active_installs = self.active_app_installs.select_related('app_user').distinct('app_user')
        return AppUser.objects.filter(id__in=active_installs.values_list('app_user_id', flat=True))

    @property
    def checked_in_users(self):
        """
        Returns users with installs that have checked-in successfully.
        This should be used to calculate the secure and insecure users counts.
        :rtype: AppUser queryset
        """
        from appusers.models import AppUser
        active_installs = self.active_app_installs.filter(
            # AppInstalls that do not have an AppReport (have not checked-in)
            # yet are not considered secure nor insecure, limbo state.
            reports__total_responses__isnull=False,
        ).select_related('app_user').distinct('app_user')
        return AppUser.objects.filter(id__in=active_installs.values_list('app_user_id', flat=True))

    @property
    def installed_users_count(self):
        """
        Returns count of installed users.
        :return: count of installed users
        :rtype: int
        """
        return len(list(self.installed_users))

    @property
    def installed_beta_users_count(self) -> int:
        """
        Returns the number of users that have installed only beta apps.
        """
        return self._get_beta_app_users(self.installed_users).count()

    @property
    def not_installed_users_count(self) -> int:
        """
        Returns count of not installed users.
        """
        return self.enrolled_users_count - self.installed_users_count

    @property
    def active_users_30d_count(self):
        """
        Returns all users that checked-in in the last 30 days.
        """
        return self.installed_users.filter(
            installs__reports__isnull=False,
            installs__reports__created__gte=timezone.now() - timedelta(days=30)
        ).distinct("id").count()

    @property
    def passed_users(self):
        """
        Returns passed users.
        This property intended for self enrollment organisations.
        Note that AppUsers that do not have an AppUserAnalytics yet are not considered secure nor insecure, limbo state.
        :return: passed users
        :rtype: AppUser queryset
        """
        from analytics.models import AppUserAnalytics
        from appusers.models import AppUser

        user_analytics_passing = AppUserAnalytics.objects.filter(
            pass_percentage__gte=100.0,
            appuser__organisation=self,
            appuser_id__in=self.checked_in_users.values_list('pk', flat=True),
            appuser__active=True
        ).values_list('appuser__pk', flat=True)

        return AppUser.objects.filter(pk__in=user_analytics_passing)

    @property
    def secure_users_count(self):
        """
        Returns secure users count.
        This property intended for self enrollment organisations.
        :return: passed users count
        :rtype: int
        """
        return len(list(self.passed_users))

    @property
    def secure_beta_users_count(self) -> int:
        """
        Returns the number of beta users that have passed the security checks.
        """
        return self._get_beta_app_users(self.passed_users).count()

    @property
    def failed_users(self):
        """
        Returns failed users.
        This property intended for self enrollment organisations.
        :return: failed users
        :rtype: AppUser queryset
        """
        # AppUsers that do not have an AppUserAnalytics yet are not considered secure nor insecure, limbo state.
        return self.checked_in_users.exclude(
            Q(analytics__isnull=True) | Q(pk__in=self.passed_users.values_list('pk', flat=True))
        )

    @property
    def insecure_users_count(self):
        """
        Returns insecure users count.
        This property intended for self enrollment organisations.
        :return: failed users count
        :rtype: int
        """
        return self.failed_users.count()

    @property
    def insecure_beta_users_count(self) -> int:
        """
        Returns the number of beta users that have failed the security checks.
        """
        return self._get_beta_app_users(self.failed_users).count()

    @property
    def all_devices(self) -> QuerySet:
        """
        Returns all organisation devices including inactive and those that belong to inactive users.
        """
        return self.app_installs.filter(
            app_user__organisation=self
        )

    @property
    def installed_devices_count(self):
        """
        Returns count of installed devices.
        :return: count of installed devices
        :rtype: int
        """

        # Note: this if statment for testing enviroment only.
        if (settings.IS_STAGE or settings.IS_DEVELOP or settings.DEBUG) and self.simulate_installed_devices_count:
            return self.simulate_installed_devices_count

        return self.installed_devices.count()

    @property
    def cap_licences_quantity(self) -> int:
        """
        Returns the number of CAP (CyberSmart App) licences that are used by the organisation.
        Notice that this property is used only for billing purposes, not for the actual number of installed devices.
        In billing, it is used to calculate the number of CAP licences to charge for.
        """
        from appusers.models import AppInstall

        if any([settings.IS_STAGE, settings.IS_DEVELOP, settings.DEBUG]) and self.simulate_installed_devices_count:
            # this is for testing purposes only
            return self.simulate_installed_devices_count

        installed_devices = self.installed_devices

        if self.partner.free_mobile_apps and self.partner.default_pricing_band == Organisation.PRICING_MONTHLY_V4:
            # exclude free mobile apps
            installed_devices = installed_devices.exclude(device_type=AppInstall.MOBILE)

        # exclude duplicate mobile devices (devices with CAP mobile and Trustd apps)
        # duplicates are considered to have the same app user with the same machine_vendor, machine_model and platform
        installed_devices = installed_devices.installs_without_trustd_mobile_duplicates()

        # exclude beta apps from the final count
        return installed_devices.difference(self.installed_beta_apps).count()

    @property
    def user_licences_quantity(self) -> int:
        """
        Returns the number of CAP (CyberSmart App) users that are enrolled in the organisation.
        Notice that this property is used only for billing purposes, not for the actual number of enrolled users.
        In billing, it is used to calculate the number of users to charge for.
        """
        return self.enrolled_users.difference(self.enrolled_beta_users).count()

    def is_enrolled_in_beta_feature(self, feature_kind: str) -> bool:
        """
        Returns True if an organisation is enrolled in a beta feature otherwise returns False.
        """
        return self.beta_enrolment.filter(feature__kind=feature_kind).exists()

    def meets_beta_feature_prerequisites(self, feature_kind: str) -> bool:
        """
        Returns True if an organisation supports a beta feature otherwise returns False.
        For some organisations, we wish to completely disable Beta Features,
        e.g. when an organisation is not paying for software support, it can't join Beta software features.
        """
        if feature_kind in BETA_FEATURES_REQUIRING_SOFTWARE_SUPPORT:
            return self.has_software_support
        if feature_kind == TRUSTD_LA_SWITCH:
            return False
        return True

    @property
    def v6_quantity(self):
        """ Only to be used for billing purposes """
        if self.bulk_install:
            return self.customer.num_of_installed_apps
        else:
            return self.customer.num_of_enrolled_users

    @property
    def removed_devices(self):
        """
        Returns removed devices.
        :return: removed devices
        :rtype: AppInstall queryset
        """
        return self.app_installs.filter(
            inactive=True
        )

    @property
    def removed_devices_count(self):
        """
        Returns count of removed devices.
        :return: count of removed devices
        :rtype: int
        """
        return len(list(self.removed_devices))

    @property
    def academy_users(self):
        """
        Returns all enrolled users that are enrolled in academy training
        :return: academy enrolled users
        :rtype: AppUser queryset
        """
        from appusers.models import AppUser
        return self.enrolled_users.filter(
            lms_enrollment_status=AppUser.STATUS.LMS_ENROLLED
        )

    @property
    def academy_completed_users_count(self) -> int:
        """
        Returns count of enrolled users that are enrolled in academy training
        and completed it
        """
        return self.academy_users.filter(
            analytics__academy_modules_completed=F("analytics__academy_modules_total")
        ).count()

    @property
    def academy_not_completed_users_count(self) -> int:
        """
        Returns count of enrolled users that are enrolled in academy training
        but not completed it
        """
        return self.academy_users.filter(
            analytics__academy_modules_completed__lt=F("analytics__academy_modules_total")
        ).count()

    @property
    def policies_agreed_users_count(self) -> int:
        """
        Returns count of enrolled users that agreed to policies
        """
        return self.enrolled_users.filter(
            analytics__agreed_policies_count=F("analytics__active_policies_count")
        ).count()

    @property
    def policies_pending_users(self):
        """
        Returns enrolled users that pending to agree to policies
        """
        return self.enrolled_users.filter(
            analytics__agreed_policies_count__lt=F("analytics__active_policies_count")
        )

    @property
    def policies_pending_users_count(self) -> int:
        """
        Returns count of enrolled users that pending to agree to policies
        """
        return self.policies_pending_users.count()

    @property
    def app_installs(self):
        from appusers.models import AppInstall
        return AppInstall.objects.installs_without_deprecated_duplicates_for_organisation(self)

    @property
    def active_app_installs(self):
        return self.app_installs.active()

    @property
    def get_all_certification(self):
        return self.certifications.order_by('-version__release_date', '-created')

    def get_latest_certificate(self, type=CYBER_ESSENTIALS, version_pk=None, version_number=None):
        """
        Returns last certificate version.
        :param type: certificate type
        :type type: int
        :param version_pk: certificate version primary key
        :type version_pk: int
        :param version_number: certification version number
        :type version_number: float
        :return: certificate
        :rtype: OrganisationCertification
        """
        if version_pk:
            filters = {
                'version__type__type': type,
                'version__pk': version_pk
            }
        elif version_number:
            filters = {
                'version__type__type': type,
                'version__version_number': version_number
            }
        else:
            filters = {
                'version__type__type': type
            }
        certifications = self.certifications.filter(**filters).order_by(
            '-version__release_date', '-created'
        ).select_related('version', 'version__type', 'survey', 'survey__declaration', 'survey__assessment').annotate(
            multiversion=Count(Case(When(
                organisation__certifications__version__type__type=F('version__type__type'), then=1
            )))
        )
        if not certifications:
            return None
        else:
            return certifications.first()

    def get_two_latest_certificates(self, type=CYBER_ESSENTIALS):
        """
        Returns previous to last and last certificates version.
        :param type: certificate type
        :type type: int
        :return: tuple or None
        :rtype: tuple(OrganisationCertification, OrganisationCertification)
        """
        certifications = self.certifications.filter(version__type__type=type).order_by(
            '-version__release_date', '-created'
        )
        return tuple([certifications[0], certifications[1]]) \
            if certifications.count() > 1 else tuple([certifications.first(), None])

    def get_latest_certificates(self):
        """
        Returns last certifications for each of standards
        :return: last certifications
        :rtype: QuerySet
        """
        return self.certifications.order_by(
            'version__type__type', '-version__release_date'
        ).distinct('version__type__type')

    def get_bundle_subscriptions(self) -> QuerySet:
        """
        Returns list of subscriptions that are a bundle plan.
        :return: Subs list
        """
        return self.subscriptions.live_subscriptions().filter(plan_id__in=ALL_BUNDLE_PLANS)

    def get_pass_percentage(self) -> float:
        """
        Returns organisation pass percentage based on app users average pass percentage.
        """
        import math
        from analytics.models import AppUserAnalytics

        pass_percentage = round(
            AppUserAnalytics.objects.filter(
                pk__in=AppUserAnalytics.objects.filter(
                    appuser__organisation=self,
                    appuser__active=True,
                    appuser__installs__isnull=False,
                    appuser__installs__inactive=False,
                )
                .exclude(appuser__in=self.enrolled_beta_users)
                .values_list("pk", flat=True)
                .distinct("pk")
            )
            .values_list("pass_percentage", flat=True)
            .aggregate(Avg("pass_percentage"))["pass_percentage__avg"]
            or 0,
            2,
        )

        if math.isnan(pass_percentage):
            return 0.0

        return pass_percentage

    @property
    def percentage(self):
        total_devices = self.installed_devices_count
        if total_devices > 0:
            total_value = 0.0
            for device in self.installed_devices:
                if device.get_latest_report:
                    total_value += device.get_latest_report.get_pass_percentage()
            return round(total_value / total_devices, 2)
        else:
            return 0

    @property
    def is_passing(self):
        if hasattr(self, 'analytics'):
            return self.analytics.pass_percentage == 100
        else:
            return False

    def get_latest_policy_versions(self):
        """
        Returns latest policies versions.
        :return: latest policies versions.
        :rtype: QuerySet
        """
        return OrganisationPolicyVersion.objects.filter(
            policy__organisation=self,
            policy__active=True,
            main=True
        ).order_by('policy').distinct('policy')

    def create_or_update_approved_domains(self):
        """
        Creates or updates organisation's approved domains
        """
        if self.bulk_install:
            for admin in self.get_admin_users:
                domain = admin.email.split('@')[1]
                if not is_generic_domain(domain):
                    self.approved_domains.get_or_create(domain=domain)

    def get_policies_percentage(self) -> tuple[Optional[int], Optional[int], Optional[int]]:
        """
        Returns agreed policies percentage.
        """
        total = agreed = read = not_agreed = 0
        policies = OrganisationPolicyVersion.objects.filter(
            policy__organisation=self,
            policy__active=True,
            main=True,
            active=True
        )
        if not policies.count():
            return None, None, None
        for policy in policies:
            agreed += policy.get_agreed().count()
            read += policy.get_read().count()
            not_agreed += policy.get_not_agreed().count()
        total = agreed + read + not_agreed

        try:
            agreed_percentage = int(100 * float(agreed) / float(total))
        except ZeroDivisionError:
            agreed_percentage = 0
        try:
            read_percentage = int(100 * float(read) / float(total))
        except ZeroDivisionError:
            read_percentage = 0
        try:
            not_agreed_percentage = int(100 * float(not_agreed) / float(total))
        except ZeroDivisionError:
            not_agreed_percentage = 0

        return agreed_percentage, read_percentage, not_agreed_percentage

    @property
    def get_ce_percentage(self):
        """
        Returns Cyber Essentials certification passed percentage if an organisation has Cyber Essentials certification
        otherwise returns zero.
        :return: Cyber Essentials passed percentage
        :rtype: float
        """
        return self.ce_certification.progress_percentage if self.has_ce_certification else 0

    @property
    def get_gdpr_percentage(self):
        """
        Returns GDPR certification passed percentage if an organisation has GDPR certification
        otherwise returns zero.
        :return: GDPR passed percentage
        :rtype: float
        """
        return self.gdpr_certification.progress_percentage if self.has_gdpr_certification else 0

    @property
    def creator_skip_payment(self):
        """
        Returns organisation creator skip payment attribute.
        :return: ski payment attribute
        :rtype: bool
        """
        org_creator = self.get_organisation_creator
        if org_creator and hasattr(org_creator, 'profile'):
            return org_creator.profile.skip_payment
        return False

    @property
    def creator_completed_onboarding(self):
        """
        Returns organisation creator onboarding completed attribute.
        :return: onboarding completed attribute
        :rtype: bool
        """

        org_creator = self.get_organisation_creator
        if org_creator and hasattr(org_creator, 'profile'):
            return org_creator.profile.onboarding_completed
        return False

    @property
    def _deprecated_main_app_user(self):
        """
        Returns main (first) organisation app user or None if no active app users exist
        Supposed to be used only for bulk/UBA deployment organisations, however it's also
        used outside of this context.

        This method is deprecated in favor of bulk_deploy_user.

        :return: main app user
        :rtype: AppUser or None
        """
        return (
            self.app_users.filter(active=True)
            .order_by("id")
            .first()
        )

    BULK_DEPLOY_PREFIX = 'bulk_deploy.'
    @property
    def bulk_deploy_user(self):
        """
        Returns the bulk deploy user for the organisation.
        This method is intended for use in bulk/UBA deployment scenarios.

        :return: The bulk deploy user or main app user
        :rtype: AppUser or None
        """
        return self.app_users.filter(active=True, email__startswith=self.BULK_DEPLOY_PREFIX).first()

    @property
    def main_app_user(self):
        """
        Use bulk_deploy_user if it exists, otherwise use _deprecated_main_app_user to support legacy behavior.
        """
        return self.bulk_deploy_user or self._deprecated_main_app_user


    def get_bulk_deploy_email(self):
        """
        Generate a bulk deploy email address for the organisation.

        This method creates a unique email address for bulk deployment purposes
        by combining a prefix, the organisation's secure ID, and a (hardcoded) domain.

        :return: A string representing the bulk deploy email address
        :rtype: str
        """
        return f'{self.BULK_DEPLOY_PREFIX}{self.secure_id}@cybersmart.co.uk'

    def get_or_create_bulk_deploy_user(self):
        """
        Get or create a bulk deploy user for the organisation.

        This method retrieves an existing AppUser instance or creates a new one
        with a unique email address for bulk deployment purposes. The email is
        created using the get_bulk_deploy_email() method, and the user is set as
        an admin.

        :return: An existing or newly created AppUser instance for bulk deployment
        :rtype: AppUser
        """
        from appusers.models import AppUser
        bulk_deploy_user = self.app_users.update_or_create(
            email=self.get_bulk_deploy_email(),
            defaults={'is_admin': True, 'lms_enrollment_status': AppUser.STATUS.LMS_SKIPPED_ENROLLMENT}
        )[0]
        return bulk_deploy_user

    @property
    def user_attribution_appusers(self):
        """
        Only for user based attribution.
        Returns all app users excluding main (first) organisation app user.
        :return: app users
        :rtype: AppUser queryset
        """
        return self.app_users.filter(active=True).exclude(pk=self.main_app_user.pk)

    @property
    def has_software_subscription(self):
        """
        Returns True if an organisation has any software plan subscription.
        :return: True or False
        :rtype: bool
        """
        if subscriptions := self.get_subscriptions_queryset():
            return subscriptions.live_subscriptions().filter(
                plan_id__in=plans.ALL_SOFTWARE_PLANS + plans.PARTNER_SOFTWARE_PLANS
            ).exists()
        return False

    @property
    def has_r_and_r_toolbox_subscription(self):
        """
        Returns True if an organisation has any Ransom and Recovery Toolbox plan subscription.
        :return: True or False
        :rtype: bool
        """
        if subscriptions := self.get_subscriptions_queryset():
            return subscriptions.live_subscriptions().filter(plan_id__in=plans.R_AND_R_TOOLBOX_PLANS).exists()
        return False

    @property
    def get_r_and_r_toolbox_subscription(self):
        """
        Returns an organisation's Ransom and Recovery Toolbox plan subscription.
        :return: subscription or None
        :rtype: Subscription
        """
        # Get the first subscription or None
        if subscriptions := self.get_subscriptions_queryset():
            return subscriptions.live_subscriptions().filter(plan_id__in=plans.R_AND_R_TOOLBOX_PLANS).first()
        return None

    @property
    def should_show_r_and_r_100k_insurance_texts(self):
        """
        Returns True if an organisation has bought Ransom and Recovery Toolbox
        with 100k insurance included before the date when it was no longer possible to buy it together.

        Historically it was possible to buy R&R Toolbox and 100k insurance together.

        Because this is no longer the case, for such customers,
        we want to show Insurance related texts on the R&R Toolbox page.
        """
        if not waffle.switch_is_active('disable_rnr_insurance_buying_together'):
            return True
        subscription = self.get_r_and_r_toolbox_subscription
        if not subscription:
            return False
        return subscription.r_n_r_toolbox_and_insurance_bought_together

    @property
    def has_software_support(self):
        """
        Returns True if an organisation has a software plan subscription.
        :return: True or False
        :rtype: bool
        """
        return self.has_software_subscription or not self.has_billing and self.software_support

    @property
    def can_upgrade_to_software_bundle(self) -> bool:
        """
        Returns True if an organisation can upgrade to a CAP bundle
        """
        return not settings.IS_AUS_GEO and not self.has_software_support

    @property
    def has_certificates_support(self) -> bool:
        """
        Checks if an organisation has certificates support.
        """
        if not ProjectSettings.is_certifications_enabled():
            return False
        return self.has_certification_subscription or not self.has_billing and self.certifications.all().exists()

    @property
    def is_cap_only(self) -> bool:
        """
        Checks if an organisation has CAP (CyberSmart Application) only support.
        """
        return self.has_software_support and not self.has_certificates_support

    @property
    def is_certificates_only(self) -> bool:
        """
        Checks if an organisation has certificates only support.
        """
        return not self.has_software_support and self.has_certificates_support

    @property
    def policies_support(self):
        return self.has_software_support

    @property
    def has_certification_subscription(self):
        """
        Returns True if an organisation has certification plan subscription.
        :return: True or False
        :rtype: bool
        """
        if subscriptions := self.get_subscriptions_queryset():
            return subscriptions.live_subscriptions().filter(
                plan_id__in=plans.ALL_CE_PLANS + plans.ALL_GDPR_PLANS + plans.CE_GDPR_PLANS + plans.ALL_CEP_PLANS
            ).exists()
        return False

    def get_most_recent_subscription(
            self, plan_ids: list[str], status: str = "live"
    ) -> Optional[Subscription]:
        """
        Returns most recent Partner Organisation subscription for a given list of plan IDs.
        :param plan_ids: list of plan IDs
        :param status: subscription status, either "live" or "cancelled"
        """
        custom_manager = getattr(self.subscriptions, f"{status}_subscriptions")
        if custom_manager:
            return custom_manager().filter(plan_id__in=plan_ids).first()
        return None

    def get_partner_ce_subscription(self, status: str = "live") -> Optional[Subscription]:
        """
        Returns most recent partner live Cyber Essentials subscription.
        """
        return self.get_most_recent_subscription(status=status, plan_ids=plans.PARTNER_CE_PLANS)

    def get_partner_gdpr_subscription(self, status: str = "live") -> Optional[Subscription]:
        """
        Returns most recent partner live GDPR subscription.
        """
        return self.get_most_recent_subscription(status=status, plan_ids=plans.PARTNER_GDPR_PLANS)

    def get_partner_cep_subscription(self, status: str = "live") -> Optional[Subscription]:
        """
        Returns most recent partner live Cyber Essentials Plus subscription.
        """
        return self.get_most_recent_subscription(status=status, plan_ids=plans.PARTNER_CEP_PLANS)

    def get_partner_software_subscription(self, status: str = "live") -> Optional[Subscription]:
        """
        Returns most recent partner live CAP subscription.
        """
        return self.get_most_recent_subscription(status=status, plan_ids=plans.PARTNER_SOFTWARE_PLANS)

    def get_partner_vss_subscription(self, status: str = "live") -> Optional[Subscription]:
        """
        Returns most recent partner live VSS subscription.
        """
        return self.get_most_recent_subscription(status=status, plan_ids=plans.PARTNER_VSS_PLANS)

    @property
    def has_certification_ce_subscription(self):
        """
        Returns True if an organisation has certification cyber essentials subscription.
        :return: True or False
        :rtype: bool
        """
        if subscriptions := self.get_subscriptions_queryset():
            return subscriptions.live_subscriptions().filter(plan_id__in=plans.ALL_CE_PLANS).exists()
        return False

    @property
    def has_certification_cep_subscription(self):
        """
        Returns True if an organisation has certification cyber essentials subscription.
        :return: True or False
        :rtype: bool
        """
        if subscriptions := self.get_subscriptions_queryset():
            return subscriptions.live_subscriptions().filter(plan_id__in=plans.ALL_CEP_PLANS).exists()
        return False

    @property
    def has_certification_gdpr_subscription(self):
        """
        Returns True if an organisation has certification gdpr subscription.
        :return: True or False
        :rtype: bool
        """
        if subscriptions := self.get_subscriptions_queryset():
            return subscriptions.live_subscriptions().filter(plan_id__in=plans.ALL_GDPR_PLANS).exists()
        return False

    @property
    def has_certification_annual_subscription(self):
        """
        Returns True if a direct organisation has certification annual subscription.
        :return: True or False
        :rtype: bool
        """
        if self.has_billing:
            return self.customer.subscriptions.live_subscriptions().filter(
                plan_id__in=plans.CERT_ANNUAL_PLANS_IDS + plans.DIRECT_CUSTOMER_V4_V5_ANNUAL_PLANS
            ).exists()
        return False

    @property
    def has_certification_monthly_subscription(self):
        """
        Returns True if a direct organisation has certification monthly subscription.
        :return: True or False
        :rtype: bool
        """
        if self.has_billing:
            return self.customer.subscriptions.live_subscriptions().filter(
                plan_id__in=plans.CERT_MONTHLY_PLANS_IDS + plans.DIRECT_CUSTOMER_V4_V5_MONTHLY_PLANS
            ).exists()
        return False

    @property
    def partner_logo(self):
        """
        Returns partner or distributor logo if attached otherwise returns None.
        Partner logo has higher priority than distributor logo.
        :return:
        """
        if hasattr(self, 'partner') and self.partner:
            partner_logo = self.partner.absolute_logo_url
            if partner_logo:
                return partner_logo
            else:
                distributor = self.partner.distributor
                if distributor:
                    distributor_logo = distributor.absolute_logo_url
                    if distributor_logo:
                        return distributor_logo
                    else:
                        return None
                else:
                    return None
        else:
            return None

    @property
    def partner_dark_logo(self):
        """
        Returns partner dark logo if attached otherwise returns None.
        """
        if hasattr(self, 'partner') and self.partner:
            partner_logo = self.partner.absolute_dark_logo_url
            if partner_logo:
                return partner_logo
        return None

    def get_certification_pass_percentage(self, certification_type=None):
        """
        Returns pass percentage for all organisation's certifications.
        if certification_type is specified returns percentage for this type.
        It skips not started certifications.
        :return: pass percentage
        :rtype: float
        """
        filters = {}
        if certification_type:
            if certification_type not in CERTIFICATES.keys():
                raise ValueError('Incorrect certification type')
            filters['version__type__type'] = certification_type
        combined = []
        seen_cert_types = []
        certifications = self.certifications.order_by(
            'version__type__type', '-id'
        ).filter(**filters).exclude(status__in=[
            OrganisationCertification.NOT_STARTED,
            OrganisationCertification.EXPIRED
        ])
        # remove GDPR certs from calculation
        certifications = certifications.exclude(version__type__type=GDPR)

        for certification in certifications.all():
            if certification.version.type.type not in seen_cert_types:
                seen_cert_types.append(certification.version.type.type)
                last_cert, previous_to_last = self.get_two_latest_certificates(type=certification.version.type.type)
                passed_percent = last_cert.progress_percentage \
                    if hasattr(certification, 'survey') and certification.survey else 0
                # if the latest cert is certified or cert is still valid, then is 100
                if last_cert.certified or previous_to_last and previous_to_last.renewal_end_date \
                        and previous_to_last.renewal_end_date > timezone.now().date():
                    passed_percent = 100
                combined.append(passed_percent)
        try:
            percentage = int(100 * float(sum(combined)) / float(100 * len(combined)))
        except ZeroDivisionError:
            return 0
        else:
            return percentage

    @property
    def count_issued_cyber_essentials(self):
        """
        Returns count of issued CE for partner billing
        """
        return self.certifications.filter(
            version__type__type=CYBER_ESSENTIALS,
            issued_certification__isnull=False
        ).count() if self.has_ce_certification else 0

    @property
    def count_issued_gdpr(self):
        """
        Returns count of issued GDPR for partner billing
        """
        return self.certifications.filter(
            version__type__type=GDPR,
            issued_certification__isnull=False
        ).count() if self.has_gdpr_certification else 0

    @property
    def count_issued_cep(self):
        """
        Returns count of issued CEP for partner billing
        """
        return self.certifications.filter(
            version__type__type=CYBER_ESSENTIALS_PLUS,
            issued_certification__isnull=False
        ).count() if self.has_cep_certification else 0

    def get_company_relationship(self):
        """
        Returns CRM compatible string for company relationship field
        :return: relationship_to_our_company
        :rtype: str
        """
        # some orgs don't have partners (manually removed)
        if self.partner:
            org_distributor_name = self.partner.distributor.name
            if org_distributor_name == settings.DEFAULT_CS_DIRECT_DIST_NAME:
                company_relationship = 'Customer'  # direct customer
            elif org_distributor_name == settings.DEFAULT_CS_DIST_NAME:
                if self.is_partner_org:
                    company_relationship = 'Partner'
                else:
                    company_relationship = 'Direct partner customer'
            else:
                if self.is_partner_org:
                    company_relationship = 'Disti partner'
                else:
                    company_relationship = 'Disti customer'
        else:
            company_relationship = 'Other'  # unsure, tests or cancelled direct or partner customers
        return company_relationship

    def get_email_audience(self) -> str:
        """
        Returns the email audience to add to our sendgrid emails for analytics
        """
        from emails.utils import EmailAudiences
        audience = EmailAudiences.UNKNOWN
        if self.partner:
            if self.is_direct_customer:
                audience = EmailAudiences.DIRECT_ORGANISATIONS
            elif self.partner.distributor.name == settings.DEFAULT_CS_DIST_NAME:
                audience = EmailAudiences.DIRECT_PARTNER
            else:
                audience = EmailAudiences.CHANNEL_PARTNER
        return audience

    @property
    def is_demo_organisation(self):
        """
        Returns True if organisation is set up as a demo organisation otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.partner.name == settings.CS_DEMO_PARTNER_NAME

    @property
    def is_direct_customer(self):
        """
        Returns True if organisation is a direct customer or an AVIVA organisations otherwise returns False.
        AVIVA organisations are treated as the direct customers under the AVIVA distributor.
        :return: True or False
        :rtype: bool
        """
        return self.partner_type == DIRECT_CUSTOMER or self.partner.distributor.is_insurer

    @property
    def is_channel_customer(self) -> bool:
        """
        Returns True if organisation is a channel customer otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.partner_type in (DISTRIBUTOR_PARTNER, DISTRIBUTOR_PARTNER_CUSTOMER)

    @property
    def is_direct_customer_v4(self):
        """
        Returns True if organisation is a direct customer with plans v4 otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.is_direct_customer and self.pricing_band in (
            self.DIRECT_CUSTOMER_V4_V5_PRICING_MONTHLY, self.DIRECT_CUSTOMER_V4_V5_PRICING_ANNUAL
        )

    @property
    def has_v4_pricing_band(self):
        return self.pricing_band in (
            self.PRICING_MONTHLY_V4,
            self.PRICING_CUSTOM_BUNDLE_ANNUAL,
            self.PRICING_CUSTOM_BUNDLE_MONTHLY,
        ) or self.is_direct_customer_v4

    @property
    def is_billed_annual(self):
        """
        Returns True if organisation is billed annual otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.pricing_band in (
            self.PRICING_ANNUAL, self.PRICING_ANNUAL_V2, self.DIRECT_CUSTOMER_V4_V5_PRICING_ANNUAL,
            self.PRICING_CUSTOM_BUNDLE_ANNUAL, self.PRICING_BUNDLE_ANNUAL
        )

    @property
    def is_billed_monthly(self):
        """
        Returns True if organisation is billed monthly otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.pricing_band in (
            self.PRICING_MONTHLY, self.DIRECT_CUSTOMER_V4_V5_PRICING_MONTHLY,
            self.PRICING_CUSTOM_BUNDLE_MONTHLY, self.PRICING_BUNDLE_MONTHLY
        )

    @property
    def is_bundle(self):
        """
        V5 bundles, always has software support
        """
        return self.pricing_band in (
            self.PRICING_BUNDLE_ANNUAL,
            self.PRICING_BUNDLE_MONTHLY
        )

    @property
    def is_custom_bundle(self):
        """
        V4 bundles, may or may not have software support
        """
        return self.pricing_band in (
            self.PRICING_CUSTOM_BUNDLE_ANNUAL,
            self.PRICING_CUSTOM_BUNDLE_MONTHLY
        )

    @property
    def has_any_bundle(self):
        return self.pricing_band in (
            self.PRICING_CUSTOM_BUNDLE_MONTHLY,
            self.PRICING_CUSTOM_BUNDLE_ANNUAL,
            self.PRICING_BUNDLE_ANNUAL,
            self.PRICING_BUNDLE_MONTHLY
        )

    def get_plans_for_cap_upgrade(self, request: HttpRequest) -> dict[str, dict]:
        """
        Provides functionality for upgrading a direct customer's subscription
        from a certification-only plan to a certification & CAP plan.
        Returns a dictionary containing plan objects suitable for upgrading,
        with each key representing the current subscription plan ID and its corresponding value
        being a plan object ready for CAP upgrade.
        """
        plans_for_upgrade: dict[str, dict] = {}
        plans_to_exclude = plans.DIRECT_CUSTOMER_V4_V5_SOFTWARE_PLANS + plans.R_AND_R_TOOLBOX_PLANS

        if self.has_billing and self.has_subscription:
            for subscription in self.customer.subscriptions.updatable_quantity_subscriptions().exclude(
                    plan_id__in=plans_to_exclude
            ):
                plan = get_plan_object_from_cache(
                    plans.get_direct_customer_cap_upgrade_plans(request).get(subscription.plan_id)
                )
                if plan:
                    plans_for_upgrade[subscription.plan_id] = plan
                else:
                    logger.error(
                        f"Plan {subscription.plan_id} not found in DIRECT_CUSTOMER_V4_UPDATE_ONLY_CERT_TO_SOFTWARE"
                    )
        return plans_for_upgrade

    @property
    def is_allowed_to_install_one_app(self):
        """
        Returns True if organisation without software support can install one application otherwise returns False.
        :return: True of False
        :rtype: bool
        """
        return all([
            not self.is_trial,
            self.is_direct_customer_v4,
            not self.has_software_subscription,
            not self.installed_devices_count
        ])

    @property
    def is_live_chat_enabled(self):
        return self.partner.distributor.enable_live_chat

    @property
    def learn_lite_switch_visible(self):
        """ Check if Learn Lite (previously Academy) switch in manage features
        for partner admins is visible. """
        return (
            self.learn_lite_enabled_globally
            and self.learn_lite_requirements_met()
            and self.partner.default_academy_enabled
            and not self.learn_switch_visible
        )

    def learn_lite_requirements_met(self) -> bool:
        """
        Check if Learn Lite requirements are met:
        - organisation has software support
        - organisation is not bulk install non-UBA
        """
        return self.has_software_support and (not self.bulk_install or self.is_uba_enabled)

    @property
    def learn_lite_enabled(self) -> bool:
        """
        When Learn Lite (previously Academy) training is enabled globally
            - see learn_lite_enabled_globally
        All must also meet the following:
            - org.has_software_software
            - org.bulk_install = False (on self-enrollment)
        """
        return (
            self.learn_lite_enabled_globally
            and self.learn_lite_requirements_met()
            and self.partner.default_academy_enabled
            and self.settings.academy_enabled
        )

    @property
    def learn_lite_enabled_globally(self) -> bool:
        """
        When Learn Lite is enabled globally (via site settings)
        """
        return ProjectSettings.is_academy_enabled()

    @property
    def learn_enabled_globally(self) -> bool:
        """
        Returns True if CyberSmart Learn is available environment wide.
        """
        return ProjectSettings.is_cybersmart_learn_enabled()

    @property
    def learn_upgrade_switch_visible(self) -> bool:
        """
        Check if CyberSmart Learn upgrade from Lite switch in manage features
        for partner admins is visible.
        """
        return (
            self.learn_enabled_globally
            and self.settings.cybersmart_learn_enablement
            and not self.settings.cybersmart_learn_enabled
            and self.learn_lite_enabled
        )

    @property
    def learn_switch_visible(self) -> bool:
        """
        Check if CyberSmart Learn switch in manage features
        for partner admins is visible.
        """
        return (
            self.learn_enabled_globally
            and self.settings.cybersmart_learn_enablement
            and not self.settings.academy_enabled
        )

    @property
    def learn_enabled(self) -> bool:
        """
        Check if CyberSmart Learn is enabled for this organisation
        """
        return self.learn_enabled_globally and self.settings.cybersmart_learn_enablement and self.settings.cybersmart_learn_enabled

    @property
    def cybersmart_learn_url(self) -> str:
        """
        Get CyberSmart Learn Portal URL if enabled for the organisation
        """
        return self.cybersmart_learn_organisation.get_cleaned_domain() \
            if self.learn_enabled and hasattr(self, 'cybersmart_learn_organisation') else ''

    @property
    def cybersmart_learn_hostname(self) -> str:
        """
        Get CyberSmart Learn Portal hostname if enabled for the organisation
        """
        return self.cybersmart_learn_organisation.get_cleaned_hostname() \
            if self.learn_enabled and hasattr(self, 'cybersmart_learn_organisation') else ''

    @property
    def cybersmart_learn_domain(self) -> str:
        """
        Get CyberSmart Learn Portal domain if configured for the organisation
        """
        return self.cybersmart_learn_organisation.get_cleaned_domain() if hasattr(self, 'cybersmart_learn_organisation') else ''


    @property
    def learn_lite_tab_displayed(self):
        """
        Check if the organisation is allowed to have the training tab or not
        :return: True if learn_lite_enabled(bool) is True and user_journey_passed(datetime) is set
        :rtype: bool
        """
        return bool(self.learn_lite_enabled and self.user_journey_passed)

    @property
    def is_uba_switch_visible(self):
        """ Check if UBA switch in manage features
        for partner admins is visible. """
        return self.bulk_install and ProjectSettings.is_uba_enabled() and not self.is_uba_enabled

    @property
    def is_uba_enabled(self):
        """
        Returns True if user based attribution is enabled for organisation otherwise returns False.
        :return: if user based attribution is enabled
        :rtype: bool
        """
        return all([
            self.bulk_install,
            ProjectSettings.is_uba_enabled(),
            self.settings.uba_enabled
        ])

    @property
    def is_self_enrollment_type(self):
        """
        Returns True if organisation is using individual enrollment process.
        :return: is organisation self enrollment
        :rtype: bool
        """
        return not self.bulk_install

    @cached_property
    def is_bulk_enrollment_type(self):
        """
        Returns True if organisation is using bulk enrollment process.
        :return: is organisation bulk enrollment
        :rtype: bool
        """
        return self.bulk_install and not self.is_uba_enabled

    @property
    def is_uba_type(self):
        """
        Return True if organisation is using bulk enrollment process
        with user based attribution enabled.
        :return: is organisation uba type
        :rtype: bool
        """
        return self.bulk_install and self.is_uba_enabled

    @property
    def insurance_tab_should_be_hidden(self):
        """
        Determines if the insurance tab should be hidden for cyber essentials certification.
        :return: should be hidden or not
        :rtype: bool
        """
        return self.is_aviva_customer

    @property
    def is_smart_score_switch_visible(self):
        """ Check if smart score switch in manage features
        for partner admins is visible. """
        return ProjectSettings.is_smart_score_enabled() and self.partner.default_smart_score_enabled

    @property
    def is_devices_cleaning_switch_visible(self) -> bool:
        """
        Check if inactive devices cleaning switch is visible
        """
        return ProjectSettings.is_devices_cleaning_enabled() and self.settings.devices_cleaning_visible

    @property
    def is_r_n_r_toolbox_switch_visible(self):
        """ Check if R&R Toolbox switch in manage features
        for partner admins is visible. """
        return ProjectSettings.is_r_n_r_toolbox_enabled() and self.partner.default_r_n_r_toolbox_enabled

    @property
    def is_cap_steps_to_fix_switch_visible(self):
        """ Check if Active protect "Steps to Fix" switch in manage features
        for partner admins is visible. Only for orgs that have active protect enabled. """
        return self.has_software_support

    @property
    def is_cap_vulnerable_software_switch_visible(self) -> bool:
        """
        Check whether "Vulnerable Software" switch should be visible in manage features.
        """
        return self.has_software_support and waffle.switch_is_active("active-protect-vulnerable-software-visibility")

    def get_smart_score(self):
        """
        Returns existing or creates a new organisation smart score and returns it.
        :return: organisation's smart score
        :rtype: OrganisationSmartScore
        """
        return OrganisationSmartScore.objects.get_or_create(
            organisation=self
        )[0]

    def update_smart_score_component(self, code: str, score: float, component_enabled: bool) -> None:
        """
        Updates or removes organisation smart score component.
        :param code: component code
        :type code: str
        :param score: component score
        :type score: float
        :param component_enabled: determines if passed component is enabled for org
        and thus will be used for calculations
        :type component_enabled: bool
        :return: nothing
        :rtype: None
        """
        smart_score = self.get_smart_score()
        if not component_enabled:
            # if given component is disabled for an organisation then just remove it and recalculate scores
            smart_score.organisation_components.filter(component__code=code).delete()
            smart_score.calculate_scores()
            return

        org_component = smart_score.get_component(code)
        if org_component:
            if org_component.component.active:
                org_component.score = round(score or 0.0, 2)
                org_component.save()
                smart_score.calculate_scores()
            else:
                org_component.delete()
        else:
            try:
                component = SmartScoreComponent.objects.get(code=code)
            except SmartScoreComponent.DoesNotExist:
                pass
            else:
                if component.active:
                    smart_score.organisation_components.get_or_create(
                        component=component,
                        defaults={
                            "score": round(score or 0.0, 2)
                        }
                    )
                    smart_score.calculate_scores()

    def is_smart_score_enabled(self):
        """
        Checks if smart score is enabled for an organisation.
        :return: True or False
        :rtype: bool
        """
        return self.settings.smart_score_enabled and ProjectSettings.is_smart_score_enabled()

    def is_devices_cleaning_enabled(self) -> bool:
        """
        Checks if inactive devices cleaning feature is enabled.
        """
        return self.settings.devices_cleaning_enabled and ProjectSettings.is_devices_cleaning_enabled()

    def is_r_n_r_toolbox_enabled(self) -> bool:
        """
        Checks if R&R Toolbox feature is enabled.
        """
        return (self.settings.r_n_r_toolbox_enabled and ProjectSettings.is_r_n_r_toolbox_enabled()
                and not self.is_starling_customer)

    @property
    def is_certificate_auto_renewal_enabled(self) -> bool:
        """
        Checks if certificates auto-renewal feature is enabled for an organisation.
        """
        return all([
            waffle.switch_is_active("distributors_and_partners_certificates_auto_renewal"),
            ProjectSettings.is_certificate_auto_renewal_enabled(),
            self.settings.certificate_auto_renewal_enabled,
            self.partner.default_certificate_auto_renewal_enabled,
            self.partner.distributor.default_certificate_auto_renewal_enabled
        ])

    @property
    def is_vodafone_customer(self):
        """
        Returns True if an organisation is vodafone customer otherwise returns False.
        :return: is vodafone customer
        :rtype: bool
        """
        from vodafone.utils import is_vodafone_customer
        return is_vodafone_customer(self)

    def disable_users_sync(self):
        if hasattr(self, 'organisationusersync'):
            self.organisationusersync.disable_sync()

    def link_to_cancelled_partner(self):
        from partners.models import Partner
        self.partner_before_cancellation = self.partner
        # change organisations' partner to the CyberSmart cancelled partner
        if self.partner.distributor.name == settings.DEFAULT_CS_DIRECT_DIST_NAME:
            self.partner = Partner.objects.get(
                name=settings.CS_PARTNER_CANCELLED_DIRECT_CUSTOMER_NAME)
        else:
            self.partner = Partner.objects.get(
                name=settings.CS_PARTNER_CANCELLED_PARTNER_CUSTOMER_NAME)

        self.save(update_fields=['partner', 'modified', 'partner_before_cancellation'])

    def delete_all_trustd_devices(self):
        from trustd.tasks import delete_trustd_devices
        # Send API calls to Trustd to delete all devices
        device_ids = list(self.app_installs.filter(trustd_device__isnull=False).values_list(
            'trustd_device__id', flat=True
        ))
        if device_ids:
            delete_trustd_devices.delay(device_ids=device_ids)

    @property
    def is_cancelled(self):
        """ Returns True if organisation is a cancelled org, else it returns False.
        * When an org is cancelled, it will get linked to a specific 'Cancelled' CS partner.
        """
        if self.partner.name in [
            settings.CS_PARTNER_CANCELLED_DIRECT_CUSTOMER_NAME,
            settings.CS_PARTNER_CANCELLED_PARTNER_CUSTOMER_NAME,
        ]:
            return True
        return False

    def delete_admin_users(self, user_ids):
        # For the given user_ids:
        # removes org roles for all user_ids profiles for current organisation
        # removes OrganisationAdmin objects for all user_ids for current organisation
        # deactivates the users if they are admin only in one organisation and are not partner or distributor users
        from accounts.models import Profile
        for profile in Profile.objects.filter(user_id__in=user_ids):
            profile.remove_org_roles(organisation=self)

        User = get_user_model()
        users = User.objects.annotate(organisations=Count('organisation_admins__organisation')).filter(
            id__in=user_ids,
            organisations__lte=1
        ).exclude(
            Q(distributor__isnull=False) |
            Q(partner__isnull=False)
        )
        users.update(is_active=False)

        # deletes OrganisationAdmin objects for current organisation
        self.admins.filter(user_id__in=user_ids, organisation=self).delete()

    def cancel(self):
        """
        Cancel the organisation by linking it
        to CyberSmart cancelled partner,
        unlink any admin user with partner or
        distributor access, disable users sync, and
        delete any other admins.
        """

        # organisation with an active subscription
        # can't be cancelled.
        assert not has_active_subscriptions(self)

        with transaction.atomic():
            # Disable organisation's users sync
            self.disable_users_sync()

            self.delete_admin_users(self.admins.values('user'))
            # set the organisations' appusers to inactive
            self.app_users.update(active=False)

            # change the assigned partner to
            # the CyberSmart cancelled partner
            self.link_to_cancelled_partner()
            # soft delete all trustd devices
            self.delete_all_trustd_devices()

    @property
    def policies_count(self) -> int:
        """
        Returns policies count.
        """
        return self.policies.filter(active=True).count()

    def all_devices_passing_check(self, app_check):
        """
        Returns whether all devices are passing given AppCheck for this Organisation
        :param app_check: AppCheck
        :return: Boolean
        """
        from appusers.models.app_installs import AppInstallCheckStatus

        failing_checks = AppInstallCheckStatus.objects.filter(
            app_install__app_user__organisation=self,
            app_install__inactive=False,
            app_install__app_user__active=True,
            app_check=app_check,
            passing=False
        ).exclude(
            app_install__app_user__organisation__permanent_fixes__app_check=app_check,
            # Remove checks with permanent fixes
        ).exclude(
            app_install__manual_fix__app_check=app_check  # Remove checks with manual fixes
        )

        return not failing_checks.exists()

    @property
    def app_install_packages(self):
        """
        Returns all app install packages for this organisation
        """
        from appusers.models import AppOSInstalledSoftware, AppReport
        return AppOSInstalledSoftware.objects.filter(report__in=AppReport.objects.filter(
            app_install__inactive=False,
            app_install__app_user__organisation=self,
            app_install__app_user__active=True,
            total_responses__isnull=False
        ).order_by("app_install", "-modified").distinct("app_install").values_list("pk", flat=True)).distinct(
            "software__product", "software__version", "software__vendor"
        )

    @property
    def beta_app_install_packages(self) -> QuerySet:
        """
        Returns all beta app install packages for this organisation
        """
        from appusers.models import AppReport

        return self.app_install_packages.filter(
            report__in=AppReport.objects.filter(
                app_install__in=self.app_installs.filter(
                    get_beta_app_install_queryset_filter()
                )
            )
        )

    @property
    def beta_app_install_packages_count(self) -> int:
        """
        Returns beta app install packages count for this organisation.
        """
        return self.beta_app_install_packages.count()

    @property
    def total_packages_count(self) -> int:
        """
        Returns total packages count that are installed on the devices.
        """
        from appusers.models import AppReport

        return self.app_install_packages.exclude(
            report__in=AppReport.objects.filter(
                app_install__in=self.app_installs.filter(
                    get_beta_app_install_queryset_filter()
                )
            )
        ).count()

    @property
    def vulnerable_packages_count(self) -> int:
        """
        Returns total packages count that are vulnerable.
        """
        from appusers.models import AppOSInstalledSoftware

        return AppOSInstalledSoftware.objects.vulnerable_objects().filter(
            pk__in=self.app_install_packages.difference(
                self.beta_app_install_packages
            ).values_list("pk", flat=True),
        ).count()

    @property
    def safe_packages_count(self) -> int:
        """
        Returns total packages count that are safe.
        """
        return self.total_packages_count - self.vulnerable_packages_count

    def has_cancelled_certification_subscription(self, cert_type: int) -> bool:
        """
        Returns True if the organisation has a cancelled subscription for the given certification type.
        """
        if cert_type == CYBER_ESSENTIALS:
            cancelled = self.get_partner_ce_subscription(status="cancelled")
            live = self.get_partner_ce_subscription(status="live")
            return cancelled is not None and live is None
        elif cert_type == CYBER_ESSENTIALS_PLUS:
            cancelled = self.get_partner_cep_subscription(status="cancelled")
            live = self.get_partner_cep_subscription(status="live")
            return cancelled is not None and live is None
        elif cert_type == GDPR:
            cancelled = self.get_partner_gdpr_subscription(status="cancelled")
            live = self.get_partner_gdpr_subscription(status="live")
            return cancelled is not None and live is None
        return False

    def has_cancelled_software_subscription(self) -> bool:
        """
        Returns True if the organisation has a cancelled subscription for the software.
        """
        cancelled = self.get_partner_software_subscription(status="cancelled")
        live = self.get_partner_software_subscription(status="live")
        return cancelled is not None and live is None

    def has_cancelled_vss_subscription(self) -> bool:
        """
        Returns True if the organisation has a cancelled subscription for the VSS.
        """
        cancelled = self.get_partner_vss_subscription(status="cancelled")
        live = self.get_partner_vss_subscription(status="live")
        return cancelled is not None and live is None

    @property
    def ce_desktop_devices_quantities(self) -> str:
        """
        Returns the CE desktop devices quantities from questions A2.4 and A2.5.
        """
        return self.get_cyber_essentials_answer("A2.4") + "\n" + self.get_cyber_essentials_answer("A2.5")

    @property
    def ce_mobile_devices_quantities(self) -> str:
        """
        Returns the CE virtual devices quantities from question A2.6.
        """
        return self.get_cyber_essentials_answer("A2.6")

    def get_cyber_essentials_answer(self, question_pervade_title: str) -> str:
        """
        Returns the answer for the given question pervade title.
        """
        ce = self.ce_certification
        if ce and hasattr(ce, "survey"):
            answer = ce.survey.responses.filter(question__pervade_title__startswith=question_pervade_title).first()
            if answer:
                return answer.value
        return ""

    @property
    def is_eligible_for_ce_plus_audit(self) -> bool:
        """
        Returns whether the organisation is eligible for CE+ audit.
        """
        cyber_essentials_issued_3_months = self.ce_certification and self.ce_certification.issued_within_last_3_months
        if not cyber_essentials_issued_3_months:
            return False

        cyber_essentials_plus_certified = self.cep_certification and self.cep_certification.certified

        if not cyber_essentials_plus_certified:
            return True
        elif self.cep_certification and self.ce_certification.version_number != self.cep_certification.version_number:
            return True
        return False

    @property
    def eligible_for_ce_plus_audit_days_left(self) -> int:
        """
        Returns days left for CE+ audit.
        """
        if self.is_eligible_for_ce_plus_audit:
            cyber_essentials = self.ce_certification
            return ((cyber_essentials.certified_date.date() + relativedelta(months=3)) - timezone.now().date()).days
        return 0

    @property
    def is_eligible_for_ce_plus_audit_booking_email(self) -> bool:
        """
        Returns whether the organisation is eligible for CE+ audit booking email to be sent.
        """
        return all([
            self.is_eligible_for_ce_plus_audit,
            self.has_certification_cep_subscription,
            not self.cep_audit_date
        ])

    @property
    def ordered_groups(self) -> QuerySet:
        """
        Returns the ordered groups for this organisation.
        """
        return self.groups.order_by("name")

    def get_manage_users_page(self, perms: tuple[str, ...]) -> str:
        """
        Returns the URL for the manage users page depending on the permissions and the organisation.
        If user does not have access to them, returns empty string.
        """
        if has_access_to_manage_app_users_page(perms, self):
            return reverse("organisations:manage-users", kwargs={"org_id": self.secure_id})
        elif has_access_to_dashboard_access_page(perms, self):
            return reverse("organisations:dashboard-access", kwargs={"org_id": self.secure_id})
        return ""

    def get_manage_users_page_title(self, perms: tuple[str, ...]) -> str:
        if has_access_to_manage_app_users_page(perms, self):
            return _("Manage Users")
        elif has_access_to_dashboard_access_page(perms, self):
            return _("Manage Dashboard Access")
        return ""

    def get_invite_users_page_url_generic(self, perms: tuple[str, ...]) -> str:
        """
        Returns the URL for the (invite users page) or (organisation dashboard page)
        depending on the organisation deployment type and permissions.
        """
        if self.is_self_enrollment_type:
            return self.get_manage_users_page(perms)
        return reverse("dashboard:organisation", kwargs={"org_id": self.secure_id})

    @property
    def cep_audit_reschedule_link(self) -> Optional[str]:
        """
        Returns the rescheduling link for the latest CE+ audit request.
        """
        if audit_request := self.certification_audits.filter(
                certificate_version__type__type=CYBER_ESSENTIALS_PLUS,
                certificate_version__version_number=self.ce_certification.version_number
        ).order_by(
            "-created"
        ).first():
            return audit_request.reschedule_link

    @property
    def is_latest_cep_audit_legacy(self) -> bool:
        """
        Determine if the latest cep_audit_date used is:
        - legacy way (meaning date on which cep audit was requested)
        or
        - new way (meaning date on which the cep audit will happen)

        Return True if it is the legacy way.
        """
        if self.cep_audit_request_date and self.cep_audit_date == self.cep_audit_request_date:
            return True
        return False

    @property
    def cep_audit_date(self) -> Optional[datetime]:
        """
        Retrieves and returns the date of the most recent CE+ audit request associated with the current CE version.
        """
        if audit_request := self.certification_audits.filter(
                certificate_version=CertificationVersion.objects.filter(
                    type__type=CYBER_ESSENTIALS_PLUS, version_number=self.ce_certification.version_number
                ).first()
        ).order_by(
            "-created"
        ).first():
            return audit_request.request_date
        else:
            # if there is no audit request in the database, return the date from the legacy field if set
            return self.cep_audit_request_date

    def get_enrolled_beta_features(self, feature_type: Optional[list] = None) -> models.QuerySet:
        """
        Returns a list of enabled beta features for this organisation.
        If feature_type is provided, it will return only the features of that type.
        """
        queryset = BetaFeature.objects.filter(enrolments__organisations=self)
        if feature_type and isinstance(feature_type, list):
            queryset = queryset.filter(kind__in=feature_type)

        # Can be enrolled only if the Beta Waffle switch is enabled globally
        queryset = queryset.filter(
            kind__in=[
                feature.kind for feature in queryset
                if waffle.switch_is_active(feature.kind)
            ]
        )
        return queryset

    def combined_deprecated_installs(self) -> QuerySet:
        """
        Returns all combined deprecated installs for this organisation.
        This queryset is identical to the queryset used on the App Version Management page,
        as it is used to determine if the App Version Management page should be shown.
        """
        from beta_features.utils import combined_deprecated_queryset
        return combined_deprecated_queryset(self)

    @property
    def has_combined_deprecated_installs(self) -> bool:
        """
        Returns True if there are active combined deprecated installs for this organisation.
        """
        return self.combined_deprecated_installs().exists()

    @property
    def should_show_superscript_texts(self) -> bool:
        """
        Checks whether the SuperScript texts should be shown in the templates.
        """
        if not hasattr(self, 'ce_certification') or self.ce_certification is None or not hasattr(self.ce_certification, 'survey'):
            if waffle.switch_is_active(SUTCLIFFE_SWITCH):
                return False
            return True

        return self.ce_certification.should_show_superscript_texts

    @property
    def is_trustd_mobile_available(self) -> bool:
        """
        Returns True if Trustd Mobile is available for the organisation either through
        Global Availability (GA) or if the organisation is enrolled in Limited Availability (LA).
        """
        from beta_features.utils import is_trustd_mobile_available_for_organisation
        return is_trustd_mobile_available_for_organisation(self)

    def v5_agent_link(self):
        from appusers.models.app_installs import AppFile
        return AppFile.get_agent_link_for_organisation(self, get_v5=True)

    @property
    def has_patch_enabled(self) -> bool:
        """
        Returns True if patching feature is enabled for the organisation.
        """
        return getattr(self.settings, "patch_enabled", False)

    @property
    def has_patch_management_multiselection_enabled(self) -> bool:
        """
        Returns True if patch management multiselection feature is enabled on software page for the organisation.
        """
        return getattr(self.settings, "patch_management_multiselection", False)


class OrganisationAdmin(TimeStampedModel, CRUDSignalMixin):
    """
    This model represents organisation admin that have access to the web dashboard.
    """
    organisation = models.ForeignKey(Organisation, related_name='admins', on_delete=models.CASCADE)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='organisation_admins')
    is_admin = models.BooleanField(default=False)
    subscribed = models.BooleanField(verbose_name='Subscribed to emails', default=True)

    class Meta:
        verbose_name = 'Organisation\'s Admin'
        verbose_name_plural = 'Organisation\'s Admins'
        unique_together = ['user', 'organisation']

    def __unicode__(self):
        return "{0} (#{1} - {2})".format(self.organisation.name, self.pk, self.user.email)

    def __str__(self):
        return self.__unicode__()

    @property
    def unsubscribe_link(self):
        """
        Returns unsubscribe link from weekly report emails.
        :return: unsubscribe link
        :rtype: str
        """
        return urljoin(
            ABSOLUTE_URL_FORMAT.format(protocol=get_http_protocol_url_name(), domain=Site.objects.get_current().domain),
            reverse('dashboard:unsubscribe-weekly', kwargs={
                'org_id': self.organisation.secure_id,
                'user': self.pk
            })
        )

    def get_role(self):
        """ Gets the role this user has for this organisation """
        return self.user.profile.roles.filter(organisation=self.organisation).first()

    def add_default_roles(self, organisation) -> None:
        """
        Adds a default FULL DASHBOARD ACCESS role to the user if it doesn't have any assigned yet.
        """
        profile = self.user.profile
        if not profile.roles.filter(organisation=organisation).exists():
            default_role = Role.objects.filter(organisation=organisation, name_en_gb=DEFAULT_ROLE_NAME)
            profile.roles.add(*default_role)




class OrganisationCertification(TimeStampedModel, InsurerMixin, CRUDSignalMixin):
    """
    This model represents organisation certifications (CE, CE+, IASME, GDPR)
    """
    NOT_STARTED = 'NS'
    IN_SURVEY = 'IS'
    SURVEY_COMPLETED = 'SC'
    AWAITING_SIGNATURE = 'AS'
    DECLARATION_SIGNED = 'DS'
    REQUIRES_ATTENTION = 'RA'
    CERTIFIED = 'CF'
    EXPIRING = 'EXP'
    EXPIRED = 'EXD'

    # Categories of certificate statuses
    CERTIFIED_STATUSES = [CERTIFIED, EXPIRING]
    FINISHED_STATUSES = CERTIFIED_STATUSES + [EXPIRED]
    IN_PROGRESS_STATUSES = [IN_SURVEY, SURVEY_COMPLETED, AWAITING_SIGNATURE, DECLARATION_SIGNED, REQUIRES_ATTENTION]
    READY_FOR_ASSESSMENT_STATUSES = [NOT_STARTED, EXPIRED]
    ALL_STATUSES = CERTIFIED_STATUSES + IN_PROGRESS_STATUSES + READY_FOR_ASSESSMENT_STATUSES

    STATUSES = (
        (EXPIRED, _('Expired')),
        (EXPIRING, _('Expiring')),
        (CERTIFIED, _('Certified')),
        (REQUIRES_ATTENTION, _('Requires attention')),
        (DECLARATION_SIGNED, _('Declaration signed')),
        (AWAITING_SIGNATURE, _('Awaiting declaration signature')),
        (SURVEY_COMPLETED, _('Survey completed')),
        (IN_SURVEY, _('In survey')),
        (NOT_STARTED, _('Not started'))
    )

    organisation = models.ForeignKey(Organisation, related_name='certifications', on_delete=models.CASCADE)
    version = models.ForeignKey(CertificationVersion, on_delete=models.PROTECT)
    pervade_user = models.CharField(verbose_name='Pervade user', max_length=255, null=True, blank=True)
    pervade_pass = models.CharField(verbose_name='Pervade password', max_length=255, null=True, blank=True)
    pervade_assessor = models.CharField(verbose_name='Pervade organisation', max_length=255, null=True, blank=True)
    pervade_report = models.FileField(
        verbose_name='Report file', upload_to=upload_file_securely, max_length=255, blank=True, null=True
    )
    renewal_start_date = models.DateField(verbose_name='Renewal start date', null=True, blank=True)
    renewal_end_date = models.DateField(verbose_name='Renewal end date', null=True, blank=True)
    status = models.CharField(verbose_name='Status', max_length=255, choices=STATUSES, default=NOT_STARTED)
    is_auto_generated = models.BooleanField(verbose_name='Is auto-generated?', default=False)
    free_of_charge = models.BooleanField(verbose_name='Not a CyberSmart issued certificate', default=False)
    renewal_certificate = models.OneToOneField(
        to="self", verbose_name="Renewal certificate", on_delete=models.SET_NULL, null=True, blank=True,
        related_name="renewed_certificate"
    )
    immediate_requested_coverage_amount = models.SmallIntegerField(
        choices=InsuranceOptIn.COVERAGE,
        verbose_name="Requested coverage amount at the time of declaration",
        default=InsuranceOptIn.COVERAGE.CE_25K
    )

    objects = CRUDManager()

    class Meta:
        verbose_name = 'Organisation\'s Certification'
        verbose_name_plural = 'Organisation\'s Certifications'
        ordering = ['version__type__type']
        unique_together = ['organisation', 'version']

    def __unicode__(self):
        return "{} {}".format(self.organisation, self.version)

    def __str__(self):
        return self.__unicode__()

    def prefetched_url(
            self, org_id: str | None = None,
            certification_type: str | None = None,
            certification_version: str | None = None
    ) -> str:
        """
        Returns a link to certification page.
        Parameters can be used in case of needed data is already prefetched, and we don't want to make extra requests.
        """
        return reverse_lazy("dashboard:certificate", kwargs={
            "org_id": str(org_id or self.organisation.secure_id),
            "type": str(certification_type or self.type),
            "version_pk": str(certification_version or self.version.pk)
        })

    @property
    def url(self) -> str:
        """
        Returns a link to certification page.
        """
        return reverse_lazy("dashboard:certificate", kwargs={
            "org_id": self.organisation.secure_id,
            "type": self.type,
            "version_pk": self.version.pk
        })

    @property
    def report_url(self) -> str:
        """
        Returns a link to certification report.
        """
        return reverse(
            "rulebook:certificate-report",
            kwargs={
                "org_id": self.organisation.secure_id,
                "type": self.type,
                "version_pk": self.version.pk,
            },
        )

    @property
    def absolute_url(self):
        """
        Returns absolute link with domain name to certification page.
        :return: certification page link
        :rtype: str
        """
        if self.is_multiple:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'type': self.type,  # pylint: disable=no-member
                'version_pk': self.version.pk
            }
        else:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'type': self.type  # pylint: disable=no-member
            }
        return urljoin(
            ABSOLUTE_URL_FORMAT.format(protocol=get_http_protocol_url_name(), domain=Site.objects.get_current().domain),
            reverse('dashboard:certificate', kwargs=kwargs)
        )

    @property
    def auto_answer_url(self):
        """
        Returns url to auto answer view.
        :return: url
        :rtype: str
        """
        if self.is_multiple:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'type': self.version.type.type,  # pylint: disable=no-member
                'version_pk': self.version.pk
            }
        else:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'type': self.version.type.type,  # pylint: disable=no-member
            }
        return reverse('dashboard:survey-auto-answer', kwargs=kwargs)

    @property
    def submit_survey_url(self):
        """
        Returns url to submit survey view.
        :return: url
        :rtype: str
        """
        if self.is_multiple:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'type': self.version.type.type,  # pylint: disable=no-member
                'version_pk': self.version.pk
            }
        else:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'type': self.version.type.type,  # pylint: disable=no-member
            }
        return reverse('dashboard:submit-survey', kwargs=kwargs)

    @property
    def start_survey_url(self):
        """
        Returns url to start survey view.
        :return: url
        :rtype: str
        """
        if self.is_multiple:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'version_pk': self.version.pk
            }
        else:
            kwargs = {
                'org_id': self.organisation.secure_id
            }
        return reverse('dashboard:start-survey', kwargs=kwargs)

    @property
    def declaration_url(self):
        """
        Returns url to declaration view.
        :return: url
        :rtype: str
        """
        if self.is_multiple:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'type': self.version.type.type,  # pylint: disable=no-member
                'version_pk': self.version.pk
            }
        else:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'type': self.version.type.type,  # pylint: disable=no-member
            }
        return reverse('dashboard:declaration', kwargs=kwargs)

    @property
    def declaration_signed_url(self):
        """
        Returns url to declaration signed view.
        :return: url
        :rtype: str
        """
        if self.is_multiple:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'certification_type': self.version.type.type,  # pylint: disable=no-member
                'version_pk': self.version.pk
            }
        else:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'certification_type': self.version.type.type,  # pylint: disable=no-member
            }
        return reverse('rulebook:declaration-signed', kwargs=kwargs)

    @property
    def external_declaration_url(self):
        """
        Returns url to external declaration view.
        :return: url
        :rtype: str
        """
        if self.is_multiple:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'type': self.version.type.type,  # pylint: disable=no-member
                'version_pk': self.version.pk
            }
        else:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'type': self.version.type.type,  # pylint: disable=no-member
            }
        return reverse('dashboard:declaration-external', kwargs=kwargs)

    @property
    def declaration_signing_url(self):
        """
        Returns url to declaration signing view.
        :return: url
        :rtype: str
        """
        if self.is_multiple:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'type': self.version.type.type,  # pylint: disable=no-member
                'version_pk': self.version.pk
            }
        else:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'type': self.version.type.type,  # pylint: disable=no-member
            }
        return reverse('dashboard:declaration-signing', kwargs=kwargs)

    @property
    def back_to_survey_url(self):
        """
        Returns url to back to survey view.
        :return: url
        :rtype: str
        """
        if self.is_multiple:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'certification_type': self.version.type.type,  # pylint: disable=no-member
                'version_pk': self.version.pk
            }
        else:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'certification_type': self.version.type.type,  # pylint: disable=no-member
            }
        return reverse('dashboard:back-to-survey', kwargs=kwargs)

    @property
    def assessment_url(self):
        """
        Returns url to CertOS dashboard.
        :return: url
        :rtype: str or unicode
        """
        ready_only_page = 'certified'
        edit_page = 'dashboard'
        if self.is_multiple:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'certification_type': self.version.type.type,  # pylint: disable=no-member
                'certification_version': self.version.pk
            }
        else:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'certification_type': self.version.type.type,  # pylint: disable=no-member
            }
        return reverse(
            f"rulebook:certos:{ready_only_page if self.certified else edit_page}", kwargs=kwargs
        )

    @property
    def survey_get_devices_url(self):
        """
        Returns url to get devices view.
        :return: url
        :rtype: str
        """
        if self.is_multiple:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'certification_type': self.version.type.type,  # pylint: disable=no-member
                'version_pk': self.version.pk
            }
        else:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'certification_type': self.version.type.type,  # pylint: disable=no-member
            }
        return reverse('rulebook:survey-get-devices', kwargs=kwargs)

    @property
    def assessment_verify_ranking_url(self):
        """
        Returns url to assessment verify ranking view.
        :return: url
        :rtype: str or unicode
        """
        if self.is_multiple:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'certification_type': self.version.type.type,  # pylint: disable=no-member
                'certification_version': self.version.pk
            }
        else:
            kwargs = {
                'org_id': self.organisation.secure_id,
                'certification_type': self.version.type.type,  # pylint: disable=no-member
            }
        return reverse('rulebook:certos:verify-ranking', kwargs=kwargs)

    @property
    def generate_report_url(self) -> str:
        """
        Returns url to generate report view.
        """
        return reverse("rulebook:certos:generate-report", kwargs={
            "org_id": self.organisation.secure_id,
            "certification_type": self.version.type.type,
            "certification_version": self.version.pk
        })

    @property
    def get_sample_report_url(self) -> str:
        """
        Returns url to get sample report view.
        """
        return reverse("rulebook:certos:get-sample-report", kwargs={
            "org_id": self.organisation.secure_id,
            "certification_type": self.version.type.type,
            "certification_version": self.version.pk
        })

    @property
    def get_report_url(self) -> str:
        """
        Returns url to get report view.
        """
        return reverse("rulebook:certos:get-report", kwargs={
            "org_id": self.organisation.secure_id,
            "certification_type": self.version.type.type,
            "certification_version": self.version.pk
        })

    @property
    def get_issue_certificate_url(self) -> str:
        """
        Returns url to issue certificate view.
        """
        return reverse("rulebook:certos:issue-certificate", kwargs={
            "org_id": self.organisation.secure_id,
            "certification_type": self.version.type.type,
            "certification_version": self.version.pk
        })

    @property
    def live_marking_scheme_url(self):
        """
        Returns url to live marking scheme view.
        :return: url
        :rtype: str or unicode
        """
        if self.is_multiple:
            kwargs = {
                "org_id": self.organisation.secure_id,
                "certification_type": self.version.type.type,  # pylint: disable=no-member
                "certification_version": self.version.pk
            }
        else:
            kwargs = {
                "org_id": self.organisation.secure_id,
                "certification_type": self.version.type.type,  # pylint: disable=no-member
            }
        return reverse("rulebook:certos:live-marking-scheme", kwargs=kwargs)

    @property
    def is_multiple(self):
        """
        Returns True if an organisation has more than one certification of the same type.
        :return: True or False
        :rtype: bool
        """
        # multiversion is annotated value to optimize amount of requests to the database
        if hasattr(self, 'multiversion'):
            return self.multiversion > 1
        else:
            return self.organisation.certifications.filter(
                version__type__type=self.version.type.type  # pylint: disable=no-member
            ).count() > 1

    @property
    def title(self):
        """
        Returns certification name.
        If an organisation has more then one certifications
        of the same type the name also include certification's version.
        :return: certification name
        """
        if self.is_multiple:
            return self.get_title()
        else:
            return self.version.type.get_type_display()  # pylint: disable=no-member

    def get_title(self):
        year, month = str(self.version.version_number).split('.')

        if month != '0':
            return f"{self.version.type.get_type_display()} {year} v2"
        return f"{self.version.type.get_type_display()} {year}"

    @property
    def show_ce_2022_info_banner(self) -> bool:
        """
        Since the Cyber Essentials (Montpelier) will be called 2023 and the current 2023
        questionnaire will be called 2022v2, we need to show an info banner for 2022v1 and 2022v2 certs.
        We should show this banner if:
        * the cert is CE 2022v1 or CE 2022v2
        * the status of the cert cannot be DECLARATION_SIGNED, CERTIFIED, EXPIRING, EXPIRED
        """
        not_allowed_statuses = [self.DECLARATION_SIGNED, self.CERTIFIED, self.EXPIRING, self.EXPIRED]
        return all([
            self.get_title().endswith('2022') or self.get_title().endswith('2022 v2'),
            self.is_cyber_essentials,
            self.status not in not_allowed_statuses
        ])

    @property
    def full_title(self):
        """
        Returns full certification title including version.
        :return: certification title
        """
        if self.type == CYBERSMART_COMPLETE:
            return self.version.type.get_type_display()
        else:
            return self.get_title()

    @property
    def short_type(self) -> str:
        """
        Returns certification type in short form.
        """
        return CERTIFICATE_SHORT_TYPES[self.type]

    @property
    def progress_percentage(self) -> int:
        """
        Returns certification progress percentage.
        """
        if not hasattr(self, "survey"):
            # if the cert has no survey, then it is 0% complete
            return 0
        if self.expired or self.renewal_end_date and self.renewal_end_date <= timezone.now().date():
            # if the cert has expired, then it is 0% complete
            return 0
        elif self.renewal_end_date and self.renewal_end_date > timezone.now().date():
            # if the cert is still valid, has not expired, then it is 100% complete
            return 100
        elif self.status in self.CERTIFIED_STATUSES:
            # if the cert is certified, then it is 100% complete
            return 100
        else:
            # if the cert is not certified, then it is the percentage of the survey completed
            return self.survey.passed_percent

    @cached_property
    def type(self) -> int:
        """
        Returns certification's type (standard).
        """
        return self.version.type.type

    @property
    def certified_date(self) -> datetime or None:
        """
        Returns the date when certification was issued.
        :return: certified date
        :rtype: datetime or None
        """
        if self.has_issued_certifications:
            return self.issued_certification.date
        else:
            if self.is_cybersmart_complete:
                if hasattr(self, "survey"):
                    return self.survey.datetime_completed
            return None

    @property
    def issued_within_last_3_months(self) -> bool:
        """
        Returns True if certification was issued less than 3 months ago.
        :return: True or False
        :rtype: bool
        """
        certified_date = self.certified_date
        if self.certified and certified_date:
            three_months_ago = (timezone.now() - relativedelta(months=3)).date()
            return certified_date.date() >= three_months_ago
        return False

    @property
    def _get_certified(self):
        """
        Returns True if certificate was certified otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        if self.type == CYBERSMART_COMPLETE:
            return self._get_survey_completed
        # specific only for IASME
        if self.type == IASME_GOVERNANCE:  # pylint: disable=no-member
            approve = self.version.auto_approve and self.organisation.get_latest_certificate(type=GDPR)
        else:
            approve = self.version.auto_approve

        return hasattr(self, 'issued_certification') or hasattr(
            self, 'survey'
        ) and hasattr(
            self.survey, 'declaration'
        ) and self.survey.declaration.ready_to_be_signed and approve

    @property
    def certified(self):
        """
        Returns True if certificate was certified otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.status in self.CERTIFIED_STATUSES

    @property
    def expiring(self):
        """
        Returns True if certificate is expiring otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.status == self.EXPIRING

    @property
    def expired(self):
        """
        Returns True if certificate has expired otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.status == self.EXPIRED

    @property
    def expiring_or_expired(self) -> bool:
        """
        Returns True if certificate is expiring or expired otherwise returns False.
        """
        return self.expiring or self.expired

    @property
    def renewed(self) -> bool:
        """
        Returns True if certificate has been renewed (if a newer certificate version already was created)
        otherwise returns False.
        """
        return self.renewal_certificate is not None

    @property
    def has_issued_certifications(self):
        """
        Returns True if certification has uploaded issued certifications.
        :return: True or False
        :rtype: bool
        """
        return hasattr(self, 'issued_certification')

    @cached_property
    def is_migration_allowed(self):
        """ Check if migration is allowed:

        * needs to be an allowed type - see MIGRATION_ALLOWED_TYPES
        * has to be in the correct status
        * a new version must exist
        """
        allowed = self.type in MIGRATION_ALLOWED_TYPES
        if allowed and self.status in [self.EXPIRING, self.EXPIRED]:
            return self.newer_version_exists
        return False

    @cached_property
    def newer_version_exists(self):
        """
        Checks if a newer version of the certification exists and it is the default one.
        """
        return CertificationVersion.objects.filter(
            type=self.version.type,
            version_number__gt=self.version.version_number,
            default=True
        ).exists()

    @property
    def new_certification_months_for_migration(self):
        """
        Return the number of months left for the new certification version
        to be created for the organisation. This is for automatic migrations.
        It will return None if we are already in the same month or has been over a year.
        Note: We replace the day to use the 1st since we only care about the month and not the nr of days.
        Returns:
            integer or None - nr of months or None
        """
        issued_certification_date = self.get_issued_certification_date()
        current_date = timezone.now().date()
        if issued_certification_date:
            # get the date of the month prior to renewal
            month_prior_to_renewal = self.renewal_start_date - relativedelta(months=1)
            # transform the current date to the beginning of the month (day 1),
            # since renewal_start_date also starts in day 1
            current_date = current_date.replace(day=1)
            diff_time = relativedelta(month_prior_to_renewal.replace(day=1), current_date)
            # only get nr of months if not the same month, or if it has not been more than 1 year
            if diff_time.months > 0 and diff_time.years == 0:
                return diff_time.months
        return None

    @cached_property
    def renewal_buttons_allowed(self) -> bool:
        """
        Determines if we can show the renewal buttons for a certification or not.
        Note that for CyberSmart direct we are hiding this functionality, since direct customers have
        automatic migrations.
        :return: True or False - True if we can show buttons
        """
        if self.is_migration_allowed and not self.organisation.is_direct_customer and not self.renewed:
            # check all condition are met in case the certification is CE
            partner_ce_sub = self.organisation.get_partner_ce_subscription()
            return partner_ce_sub is not None and partner_ce_sub.status == SUBSCRIPTION_ACTIVE and not partner_ce_sub.pause_date

        return False

    @property
    def within_10_months_before_renewal(self):
        """ Returns True if current month is the same month as 2 months before renewal """
        if not self.renewal_start_date:
            return False
        current_date = timezone.now().date().replace(day=1)
        date_2_months_before_renewal = (self.renewal_start_date - relativedelta(months=2)).replace(day=1)
        if current_date == date_2_months_before_renewal:
            return True
        return False

    @property
    def has_newest_cert_actively_in_survey(self):
        """ This is to check if the customer is actively
        progressing with the survey for the new certificate version. """
        if renewal_cert := self.renewal_certificate:
            return renewal_cert.status not in OrganisationCertification.IN_PROGRESS_STATUSES
        return False

    def get_issued_certification_date(self):
        """ Return the issued certification date.
        It will return None if it has not been certified for older versions.
        Returns:
            relativedelta or None - relativedelta time
        """
        if self.has_issued_certifications:
            # check it does not have the newest certificate version created
            return self.issued_certification.date
        return None

    @property
    def _get_survey_completed(self):
        """
        Returns True if survey was completed otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return hasattr(
            self, 'survey'
        ) and hasattr(
            self.survey, 'declaration'
        )

    @property
    def survey_completed(self):
        """
        Returns True if survey was completed otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.status == self.SURVEY_COMPLETED

    @property
    def _get_awaiting_signature(self):
        """
        Returns True if declaration is awaiting signature otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return bool(hasattr(
            self, 'survey'
        ) and hasattr(
            self.survey, 'declaration'
        ) and self.survey.declaration.ready_to_be_signed and not self.survey.declaration.signature)

    @property
    def awaiting_signature(self):
        """
        Returns True if declaration is awaiting signature otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.status == self.AWAITING_SIGNATURE

    @property
    def _get_declaration_signed(self):
        """
        Returns True if survey was completed and declaration was signed otherwise returns False
        :return: True or False
        :rtype: bool
        """
        if not hasattr(self, "survey"):
            return False
        if not hasattr(self.survey, "declaration"):
            return False
        if not self.survey.declaration.ready_to_be_signed:
            return False
        if self.version.declaration:
            if self.survey.declaration.signature:
                return True
            else:
                return False
        else:
            return True

    @property
    def declaration_signed(self):
        """
        Returns True if survey was completed and declaration was signed otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.status == self.DECLARATION_SIGNED

    @property
    def is_declared_previously(self):
        """
        Returns True if signature is present otherwise returns False.
        This check is independent of the certification statuses and covers a few of them
        (e.g., it covers REQUIRES_ATTENTION, when user has already declared, but assessor requires some follow up)
        """
        if not hasattr(self, "survey"):
            return False
        survey = self.survey
        if not hasattr(survey, "declaration"):
            return False
        declaration = survey.declaration
        if not declaration:
            return False
        if declaration.signature:
            return True
        return False

    @property
    def requires_attention(self):
        """
        Returns True if survey requires attention after assessor assessment otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.status == self.REQUIRES_ATTENTION

    @property
    def _get_requires_attention(self):
        """
        Returns True if survey requires attention after assessor assessment otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return hasattr(
            self, 'survey'
        ) and hasattr(
            self.survey, 'declaration'
        ) and hasattr(
            self.survey, 'assessment'
        ) and self.survey.assessment.is_sent_back and not self.survey.declaration.ready_to_be_signed

    @property
    def _get_in_survey(self):
        """
        Returns True if survey isn't completed yet otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return hasattr(self, 'survey')

    @property
    def in_survey(self):
        """
        Returns True if survey isn't completed yet otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.status == self.IN_SURVEY

    @property
    def _get_not_started(self):
        """
        Returns True if survey wasn't started yet otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return not hasattr(self, 'survey')

    @property
    def not_started(self) -> bool:
        """
        Returns True if survey wasn't started yet otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.status == self.NOT_STARTED

    @property
    def _get_status(self):
        """
        Returns certification status.
        :return: certification status
        :rtype: str
        """
        # we have logged this as an issue and will come back to it later
        # particularly in this property django doesn't not update object with actual database data
        # so we trigger it manually
        # only self.survey.declaration is affected
        if hasattr(self, 'survey'):
            if hasattr(self.survey, 'declaration'):
                try:
                    self.survey.declaration.refresh_from_db()
                except SurveyDeclaration.DoesNotExist:
                    # DoesNotExists error is raised when removing declaration from admin page
                    pass

        if self._get_certified:
            # certified
            return self.CERTIFIED
        elif self._get_requires_attention:
            # required attention
            return self.REQUIRES_ATTENTION
        elif self._get_declaration_signed:
            # declaration signed
            return self.DECLARATION_SIGNED
        elif self._get_awaiting_signature:
            # awaiting for declaration signature
            return self.AWAITING_SIGNATURE
        elif self._get_survey_completed:
            # survey completed
            return self.SURVEY_COMPLETED
        elif self._get_in_survey:
            # in survey
            return self.IN_SURVEY
        else:
            # not started
            return self.NOT_STARTED

    def update_status(self):
        """
        Updates certification status
        :return: nothing
        :rtype: None
        """
        self.status = self._get_status
        self.save()

    @property
    def is_cyber_essentials(self):
        """
        Returns True in case of certification has Cyber Essentials type otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.version.type.type == CYBER_ESSENTIALS

    @property
    def is_cyber_essentials_plus(self):
        """
        Returns True in case of certification has Cyber Essentials Plus type otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.version.type.type == CYBER_ESSENTIALS_PLUS

    @property
    def is_iasme(self):
        """
        Returns True in case of certification has IASME Goverment type otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.version.type.type == IASME_GOVERNANCE

    @property
    def is_gdpr(self):
        """
        Returns True in case of certification has GDPR type otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.version.type.type == GDPR

    @property
    def is_iasme_cyber_assurance(self):
        """
        Returns True in case of certification has IASME_CYBER_ASSURANCE type otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.version.type.type == IASME_CYBER_ASSURANCE

    @property
    def is_cybersmart_complete(self):
        """
        Returns True in case of certification has CyberSmart Complete type otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.version.type.type == CYBERSMART_COMPLETE

    def get_insurance_opt_in_survey_value(self, insurer):
        answer = get_opt_in_survey_response(insurer, self.survey)
        if answer:
            return answer.value_boolean
        else:
            return False

    @property
    def insurance_opt_in_survey_value(self) -> bool:
        """
        Checks whether the organisation has opted in for insurance in their Cyber Essentials survey.
        """
        return self.get_insurance_opt_in_survey_value(self.get_insurer_for_insurance_survey_question())

    @property
    def superscript_opt_in_survey_value(self) -> bool:
        """
        Checks whether the organisation has opted in for SuperScript insurance in their Cyber Essentials survey.
        Attention: if you wish to check if certificate is insured by Superscript or Sutcliffe,
        use get_insurer() method instead.
        """
        return self.get_insurance_opt_in_survey_value(self.SUPERSCRIPT_INSURER)

    @property
    def certified_with_superscript_answered(self) -> bool:
        """
        Checks if the certification is either certified or expiring/expired,
        and SuperScript insurance question was answered on the survey.
        Does not matter if the answer was True or False.
        This helps in displaying the SuperScript insurance questions on completed insurance survey.
        """
        is_certified_or_expiring = self.certified or self.expiring_or_expired
        if not is_certified_or_expiring:
            return False
        answer = get_opt_in_survey_response(self.SUPERSCRIPT_INSURER, self.survey)
        if answer is None:
            return False
        return True

    @property
    def should_show_superscript_texts(self) -> bool:
        """
        Checks whether the SuperScript texts should be shown in the templates.
        """
        if waffle.switch_is_active(SUTCLIFFE_SWITCH):
            if self.expired:
                return False

            return self.get_insurer == InsuranceOptIn.SUPERSCRIPT_INSURER

        # In case the SUTCLIFFE_SWITCH is not yet active, show the superscript texts by default.
        return True

    def get_sutcliffe_insurance_opt_in_instance(self) -> InsuranceOptIn | None:
        """ Returns the InsuranceOptIn instance for Sutcliffe insurance or None."""
        if hasattr(self, 'issued_certification'):
            return InsuranceOptIn.objects.filter(
                issued_certification=self.issued_certification,
                insurer=self.SUTCLIFFE_INSURER
            ).first()
        return None

    def has_any_insurance(self) -> bool:
        """
        Returns whether the certificate has any active or inactive insurance.
        """
        if not self.has_issued_certifications or not self.is_cyber_essentials:
            return False
        return self.insurance_opt_in_survey_value

    @property
    def has_25k_active_insurance(self) -> bool:
        """
        Returns whether the certificate has an active CE 25k insurance.
        """
        if not self.certified or not self.is_cyber_essentials:
            return False
        if hasattr(self, 'superscript_opt_in') and not hasattr(self.superscript_opt_in, 'cancellation'):
            return self.organisation.superscript_product_code == self.organisation.CE_25K
        elif insurance_opt_in := self.get_sutcliffe_insurance_opt_in_instance():
            return insurance_opt_in.coverage == InsuranceOptIn.COVERAGE.CE_25K
        return False

    @property
    def has_100k_active_insurance(self) -> bool:
        """
        Returns whether the certificate has an active CE 100k insurance.
        """
        if not self.certified or not self.is_cyber_essentials:
            return False
        if hasattr(self, 'superscript_opt_in') and not hasattr(self.superscript_opt_in, 'cancellation'):
            return self.organisation.superscript_product_code == self.organisation.CE_100K
        elif insurance_opt_in := self.get_sutcliffe_insurance_opt_in_instance():
            return insurance_opt_in.coverage == InsuranceOptIn.COVERAGE.CE_100K
        return False

    def get_active_250k_insurance_opt_in(self) -> InsuranceOptIn | None:
        """ Returns the active 250K InsuranceOptIn instance or None."""
        if not self.certified or not self.is_cyber_essentials:
            return None
        insurance_opt_in = self.get_sutcliffe_insurance_opt_in_instance()
        if not insurance_opt_in:
            return None
        if insurance_opt_in.coverage != InsuranceOptIn.COVERAGE.CE_250K:
            return None
        return insurance_opt_in

    @property
    def has_250k_active_insurance(self) -> bool:
        """
        Returns whether the certificate has an active CE 250k insurance.
        """
        return self.get_active_250k_insurance_opt_in() is not None

    @property
    def has_250k_active_insurance_upgraded_from_100k(self) -> bool:
        """
        Returns whether the certificate has an active CE 250k insurance that was upgraded from 100k.
        """
        insurance_250k_opt_in = self.get_active_250k_insurance_opt_in()
        if not insurance_250k_opt_in:
            return False
        return insurance_250k_opt_in.migrated_from_100k_insurance

    @property
    def INSURANCE_COVERAGE_250K(self) -> int:
        return InsuranceOptIn.COVERAGE.CE_250K

    @property
    def INSURANCE_COVERAGE_100K(self) -> int:
        return InsuranceOptIn.COVERAGE.CE_100K

    @property
    def is_uk_postcode_validation_needed(self):
        """
        Checks if it's required to do uk postcode validation for the certification survey.
        :return: if postcode validation is needed
        :rtype: bool
        """
        return self.is_superscript_insurance and self.insurance_opt_in_survey_value

    def is_superscript_answers_with_sutcliffe_insurer(self) -> bool:
        """
        In cases where there was a migration from Superscript to Sutcliffe,
        the organisation will have answers to Superscript insurance questions.
        But, in this case, we will check if there is an Insurance opt-in for Sutcliffe
        instance attached to the this certification, and if there is, this certification
        insurer is Sutcliffe.
        """
        issued_certification = getattr(self, 'issued_certification', None)
        return issued_certification and hasattr(issued_certification, 'insurance_opt_in') and issued_certification.insurance_opt_in.insurer == InsuranceOptIn.SUTCLIFFE_INSURER

    @cached_property
    def get_insurer(self):
        if self.organisation.partner.iasme_cb:
            return self.IASME_INSURER
        elif waffle.switch_is_active(SUTCLIFFE_SWITCH):
            if self.is_superscript_answers_with_sutcliffe_insurer():
                return InsuranceOptIn.SUTCLIFFE_INSURER
            if self.superscript_opt_in_survey_value:
                return self.SUPERSCRIPT_INSURER
            return self.SUTCLIFFE_INSURER
        else:
            return self.SUPERSCRIPT_INSURER

    def get_insurer_options(self) -> tuple[int, int]:
        """
        Gets the insurer options based on the organisation's partner status and switch status.
        This is only to be used when filtering SurveyQuestions since Certification Bodies (CB) and
        Sutcliffe use the same question set as IASME provides.
        """
        if self.certified_with_superscript_answered:
            return self.SUPERSCRIPT_INSURER, self.NO_INSURER

        if self.organisation.partner.iasme_cb or self.should_switch_to_iasme_insurer():
            return self.IASME_INSURER, self.NO_INSURER
        return self.SUPERSCRIPT_INSURER, self.NO_INSURER

    def should_switch_to_iasme_insurer(self) -> bool:
        """
        Determines if the insurer should be switched to IASME based on the status of the switch
        and the condition of the superscript insurance.
        """
        survey = getattr(self, "survey", None)
        migrated_to_iasme_insurance = getattr(survey, "migrated_to_iasme_insurance", False) if survey else False

        switch_active = waffle.switch_is_active(SUTCLIFFE_SWITCH)
        is_iasme_cb = self.organisation.partner.iasme_cb
        is_certified_or_expired = self.certified or self.expired
        has_opt_in_attached = hasattr(self, "superscript_opt_in")

        superscript_active_insurance = all([
            not is_iasme_cb,
            not migrated_to_iasme_insurance,
            is_certified_or_expired,
            has_opt_in_attached
        ])

        return switch_active and not superscript_active_insurance

    def get_insurer_for_insurance_survey_question(self):
        """ Gets the insurer for the opt-in insurance survey question. """
        return self.get_insurer_options()[0]

    def get_insurer_display(self):
        return dict(self.INSURERS_CHOICE).get(self.get_insurer)

    @property
    def version_number(self) -> float:
        """
        Returns certification version number
        """
        return self.version.version_number

    def send_notification(self, message_type, nr_days: int | None = None):
        """ Send notification """
        from notifications.handlers import NotificationHandler
        kwargs = {
            'user_name': '',
            'organisation_id': self.organisation.id,
            'organisation_name': self.organisation.name,
            'certificate_name': self.version.type.get_type_display(),
            'url': self.url,
        }
        if nr_days:
            kwargs['nr_days'] = nr_days
        NotificationHandler.send_notification(message_type=message_type, **kwargs)

    @property
    def expire_in_days(self) -> int:
        """
        Returns number of days until certificate expires.
        """
        if self.renewal_end_date:
            return (self.renewal_end_date - timezone.now().date()).days
        else:
            return 0

    @property
    def eligible_for_insurance_opting_days_left(self) -> int:
        """
        Returns days left to opt insurance within 11 month since CE issue date.
        """
        if self.certified:
            days_left = (self.issued_certification.date + relativedelta(months=11) - timezone.now()).days
            return days_left if days_left > 0 else 0
        return 0

    @property
    def is_unsupported_version(self) -> bool:
        """
        Checks if the certification is not supported by CertOS any more.
        """
        return self.is_cyber_essentials and self.version_number in [2022, 2022.5]

    @property
    def is_version_2024_or_newer(self) -> bool:
        """
        If version of the certificate version is 2024 or newer.
        This is useful for some CEP booking because of changes in the
        CE questionnaier from year to year.
        """
        return self.version_number >= 2024

    @property
    def insurance_policy_250k_file(self) -> FieldFile | None:
        """
        Returns url to download 250k insurance policy file.
        """
        file_object = StaticFiles.objects.last()
        if not file_object:
            return None
        return file_object.sutcliffe_insurance_policy_250k

    @property
    def insurance_policy_100k_file(self) -> FieldFile | None:
        """
        Returns url to download 100k insurance policy file.
        """
        file_object = StaticFiles.objects.last()
        if self.get_insurer == InsuranceOptIn.SUPERSCRIPT_INSURER:
            return file_object.insurance_policy_100k
        if waffle.switch_is_active(SUTCLIFFE_SWITCH):
            return file_object.sutcliffe_insurance_policy_100k

    @property
    def insurance_policy_25k_file(self) -> FieldFile | None:
        """
        Returns url to download 25k insurance policy file.
        If an evidence of insurance file exists in IssuedCertification, that will be returned.
        Otherwise, falls back to the static file based on insurer type.
        """
        if hasattr(self, 'issued_certification') and self.issued_certification.evidence_of_insurance_file:
            return self.issued_certification.evidence_of_insurance_file

        file_object = StaticFiles.objects.last()
        if self.get_insurer == InsuranceOptIn.SUPERSCRIPT_INSURER:
            return file_object.insurance_policy_25k
        if waffle.switch_is_active(SUTCLIFFE_SWITCH):
            return file_object.sutcliffe_insurance_policy_25k

    @property
    def has_real_25k_insurance_policy_file(self) -> bool:
        """
        Returns True if the insurance policy file is from the issued certification,
        False if it's a static example file.
        """
        if not hasattr(self, 'issued_certification'):
            return False
        return bool(self.issued_certification.evidence_of_insurance_file)

    def get_certification_issuing_job_cache_key(self) -> str:
        return f"issuing_certificate_{self.pk}"

    def set_certification_issuing_job_running(self) -> None:
        cache_key = self.get_certification_issuing_job_cache_key()
        cache.set(cache_key, True, timeout=130)

    def reset_certification_issuing_job_status(self) -> None:
        cache_key = self.get_certification_issuing_job_cache_key()
        cache.delete(cache_key)

    def is_certification_issuing_job_running(self) -> bool:
        cache_key = self.get_certification_issuing_job_cache_key()
        return cache.get(cache_key)

    ALLOWED_XLS_EXPORT_MIN_VERSION = 2023.0
    ALLOWED_XLS_EXPORT_CERTIFICATION_TYPES = [CYBER_ESSENTIALS]

    @staticmethod
    def can_import_or_export_xls(certification_type: str|int, version_number: str|float) -> bool:
        try:
            version_number = float(version_number)
        except TypeError:
            return False
        try:
            certification_type = int(certification_type)
        except TypeError:
            return False
        return certification_type in OrganisationCertification.ALLOWED_XLS_EXPORT_CERTIFICATION_TYPES and version_number >= OrganisationCertification.ALLOWED_XLS_EXPORT_MIN_VERSION

    @cached_property
    def can_export_to_xls(self) -> bool:
        """
        Only allow export to xls for Cyber Essentials v2023.0 and above.
        This is because the question set for 2023.0 and 2024.0 is the same.
        """
        return hasattr(self, 'survey') and self.can_import_or_export_xls(self.version.type.type, self.version.version_number)

    @cached_property
    def can_import_from_xls(self) -> bool:
        """
        Currently the criteria is the same for can_export_to_xls.
        """
        return self.can_export_to_xls

    @property
    def sent_back_for_re_signing(self) -> bool:
        """
        Returns True if the certification was sent back for re-signing.
        """
        return (
            hasattr(self, 'survey') and
            hasattr(self.survey, 'assessment') and
            (self.survey.assessment.is_sent_back or self.survey.assessment.is_re_answered) and
            not self.survey.declaration.signature
        )

    @property
    def has_missing_report_file(self) -> bool:
        """
        Returns True if the certificate file exists but the report file is missing.
        """
        return (
            hasattr(self, 'issued_certification') and
            self.issued_certification.certificate_file and
            not self.issued_certification.report_file
        )

    def get_retry_issue_certificate_url(self) -> str:
        """
        Returns the URL for retrying the certificate issue task.
        """
        return reverse('rulebook:certos:retry-issue-certificate', kwargs={
            'org_id': self.organisation.secure_id,
            'certification_type': self.version.type.type,
            'certification_version': self.version.pk
        })


class OrganisationCheckPermanentFix(TimeStampedModel, CRUDSignalMixin):
    """
    In this model we store organisations and checks that was permanently fixed for entire organisation.
    You can permanently fix checks on the device page.
    """
    organisation = models.ForeignKey(
        to=Organisation,
        verbose_name='Organisation',
        related_name='permanent_fixes',
        on_delete=models.CASCADE
    )
    app_check = models.ForeignKey(
        to=AppCheck,
        verbose_name='Question',
        related_name='organisation_permanent_fixes',
        on_delete=models.CASCADE
    )
    reason = models.CharField(max_length=150, blank=True, help_text='Reason for permanently resolving an AppCheck')

    objects = CRUDManager()

    class Meta:
        verbose_name = 'Organisation\'s Check Permanent Fix'
        verbose_name_plural = 'Organisation\'s Check Permanent Fixes'
        unique_together = ['organisation', 'app_check']

    def __unicode__(self):
        return '[{0}] Permanent Fix "{1}"'.format(self.organisation, self.app_check)

    def __str__(self):
        return self.__unicode__()


class OrganisationPolicy(TimeStampedModel):
    """
    This model represents uploaded policy by an organisation.
    """
    organisation = models.ForeignKey(
        to=Organisation,
        verbose_name='Organisation',
        related_name='policies',
        on_delete=models.CASCADE
    )
    name = models.CharField(verbose_name='Name', max_length=255, null=True, blank=True)
    active = models.BooleanField(verbose_name='Active', default=True)
    groups = models.ManyToManyField(to=Group, related_name="smart_policies", blank=True)

    class Meta:
        verbose_name = 'Policy'
        verbose_name_plural = 'Policies'

    def __unicode__(self):
        """
        Returns policy name.
        :return: policy name
        :rtype: str
        """
        return self.name

    def __str__(self):
        return self.__unicode__()

    @property
    def latest_version(self) -> OrganisationPolicyVersion | None:
        """
        Returns latest active policy version.
        """
        return self.get_active_versions().first()

    def get_active_versions(self):
        """
        Returns active policy versions.
        :return: active policy versions
        """
        return self.versions.filter(active=True).order_by('-version')

    def latest_uploaded_date(self) -> datetime.date | None:
        """
        Returns the latest uploaded policy date.
        It ignores inactive versions.
        """
        latest = self.latest_version
        return latest.created if latest else None

    @property
    def is_file(self):
        """
        Returns True if all attached versions are files otherwise returns False.
        :return: True of False
        :rtype: bool
        """
        return not bool(self.get_active_versions().filter(document_url__isnull=False).count())

    @property
    def is_url(self):
        """
        Returns True if all attached versions are urls otherwise returns False.
        :return: True of False
        :rtype: bool
        """
        return not bool(self.get_active_versions().filter(document__isnull=False).count())

    def reset(self):
        """
        Resets all policy agreements.
        :return: nothing
        :rtype: None
        """
        if self.latest_version:
            self.latest_version.get_read().update(read_date=None, agreed_date=None)
            self.latest_version.get_agreed().update(read_date=None, agreed_date=None)

    @property
    def update_url(self) -> str:
        """
        Returns policy update url.
        """
        return reverse("smart_policies:policies-update", kwargs={
            "org_id": self.organisation.secure_id
        })

    @property
    def update_active_url(self) -> str:
        """
        Returns policy update active url.
        """
        return reverse("smart_policies:policies-update-active", kwargs={
            "org_id": self.organisation.secure_id,
            "pk": self.pk
        })

    @property
    def delete_url(self) -> str:
        """
        Returns policy delete url.
        """
        return reverse("smart_policies:policies-delete", kwargs={
            "org_id": self.organisation.secure_id,
            "pk": self.pk
        })

    @property
    def comma_separated_groups(self) -> str:
        """
        Returns comma separated groups.
        """
        if not self.groups.exists():
            return _("Unassigned")
        else:
            return ", ".join(self.groups.all().order_by("name").values_list("name", flat=True))


class OrganisationPolicyVersion(TimeStampedModel, CRUDSignalMixin):
    """
    This model represents policy version since policies can have multiple versions.
    """
    FILE = 0
    URL = 1

    uuid = models.UUIDField(unique=True, default=uuid.uuid4, editable=False)

    policy = models.ForeignKey(
        to=OrganisationPolicy,
        verbose_name='Policy',
        related_name='versions',
        on_delete=models.CASCADE
    )
    version = models.FloatField(verbose_name='Version', null=True, blank=True)
    document = models.FileField(
        verbose_name='Policy document', upload_to=upload_to_policies, max_length=255, null=True, blank=True,
        validators=[FileTypeValidator(
            allowed_types=ALLOWED_TYPES,
            allowed_extensions=ALLOWED_EXTENSIONS
        )]
    )
    pdf_copy = models.FileField(
        verbose_name="PDF Copy", upload_to=upload_to_policies, max_length=255, null=True, blank=True,
        help_text="Automatically generated PDF copy of the policy document", editable=False
    )
    document_url = models.URLField(
        verbose_name='External document url', null=True, blank=True,
        help_text='If filled then this url will be used instead of attached document file'
    )
    active = models.BooleanField(verbose_name='Active', default=True)
    main = models.BooleanField(verbose_name='Main', default=False)
    objects = CRUDManager()

    class Meta:
        verbose_name = 'Policy version'
        verbose_name_plural = 'Policies versions'
        ordering = ['-version']

    def __unicode__(self):
        """
        Returns policy version.
        :return: policy version
        :rtype: str
        """
        return 'policy: {0} [version: {1}] [file: {2}] [main: {3}]'.format(
            self.policy, self.version, self.document_name, self.main
        )

    def __str__(self):
        return self.__unicode__()

    def get_document_file(self) -> File:
        """
        Returns either a pdf copy or a document file.
        """
        return self.pdf_copy or self.document

    @property
    def type(self):
        """
        Returns policy type
        :return: policy type
        :rtype: int
        """
        return self.FILE if self.get_document_file() else self.URL

    @property
    def is_cloud(self):
        """
        Returns True if url provided by cloud provider.
        :return: True or False
        :rtype: bool
        """
        if self.document_url:
            return 'google.com' in self.document_url
        else:
            return False

    @property
    def is_file(self):
        """
        Returns True if document is file otherwise returns False.
        :return: True of False
        :rtype: bool
        """
        return bool(self.get_document_file())

    @property
    def is_url(self):
        """
        Returns True if document is url otherwise returns False.
        :return: True of False
        :rtype: bool
        """
        return not bool(self.get_document_file())

    @property
    def original_document_absolute_url(self) -> str:
        """
        Returns absolute url to the original document.
        """
        return urljoin(
            ABSOLUTE_URL_FORMAT.format(
                protocol=get_http_protocol_url_name(),
                domain=Site.objects.get_current().domain
            ),
            self.document.url
        ) if not self.document_url else self.document_url

    @property
    def absolute_document_url(self):
        """
        Returns external link to the document if url provided otherwise returns
        absolute url including domain name to the attached document file.
        :return: absolute url
        :rtype: str
        """
        return urljoin(
            ABSOLUTE_URL_FORMAT.format(protocol=get_http_protocol_url_name(), domain=Site.objects.get_current().domain),
            self.get_document_file().url  # pylint: disable=no-member
        ) if not self.document_url else self.document_url

    @property
    def document_name(self):
        """
        Returns document name depends on document type: file or url
        :return: document name
        :rtype: str
        """
        file = self.get_document_file()
        return os.path.basename(file.name) if file else self.document_url

    @property
    def original_document_name(self) -> str:
        """
        Returns original document name.
        """
        return os.path.basename(self.document.name) if self.document else self.document_url

    @property
    def agreement_groups_filters(self) -> tuple:
        """
        Returns a tuple of Q objects to filter agreements by groups.
        """
        # subquery to get all group IDs associated with version__policy__groups
        policy_group_ids = OrganisationPolicy.objects.filter(
            pk=OuterRef('version__policy__pk')
        ).values_list('groups', flat=True)

        return (
            Q(
                # include an agreement if a policy is not assigned to any group, and
                # an app user is not assigned to any group
                version__policy__groups__isnull=True, app_install__app_user__groups__isnull=True
            ) | Q(
                # or if an app user is assigned to a group that is assigned to a policy as well
                app_install__app_user__groups__in=Subquery(policy_group_ids)
            ),
        )

    def get_agreed(self):
        """
        Returns policy agreements.
        It includes agreements from inactive app installs,
        as the policy agreements are still valid even after deactivation.
        """
        filters = {
            'agreed_date__isnull': False,
            'app_install__app_user__active': True,
            'version__policy__active': True,
            'version__active': True,
            'version__main': True,
            'app_install__app_user__organisation': self.policy.organisation
        }
        if self.policy.organisation.is_bulk_enrollment_type:
            return self.agreements.filter(
                **filters
            ).select_related('app_install').select_related(
                'app_install__app_user'
            ).select_related(
                'app_install__os'
            ).distinct('version__policy', 'os_user')
        else:
            # we remove main app user when it's UBA, since we only care about assigned devices
            excludes = {}
            if self.policy.organisation.is_uba_enabled and self.policy.organisation.main_app_user:
                excludes['app_install__app_user_id'] = self.policy.organisation.main_app_user.id

            return self.agreements.filter(
                *self.agreement_groups_filters, **filters
            ).select_related('app_install').select_related(
                'app_install__app_user'
            ).order_by(
                'version__policy', 'app_install__app_user'
            ).distinct('version__policy', 'app_install__app_user').exclude(**excludes)

    def get_read(self, exclude_agreed=True):
        """
        Returns read agreements.
        It includes agreements from inactive app installs,
        as the policy agreements are still valid even after deactivation.
        :param exclude_agreed: excludes agreed policies (agreed policies are always read as well)
        :type exclude_agreed: bool
        :return: read agreements
        :rtype: AppInstallPolicyAgreement queryset
        """
        if exclude_agreed:
            filters = {
                'app_install__app_user__active': True,
                'read_date__isnull': False,
                'agreed_date__isnull': True,
                'version__policy__active': True,
                'version__active': True,
                'version__main': True,
                'app_install__app_user__organisation': self.policy.organisation
            }
        else:
            filters = {
                'app_install__app_user__active': True,
                'read_date__isnull': False,
                'version__policy__active': True,
                'version__active': True,
                'version__main': True
            }
        if self.policy.organisation.is_bulk_enrollment_type:
            return self.agreements.filter(
                **filters
            ).select_related('app_install').select_related(
                'app_install__app_user'
            ).select_related('app_install__os').distinct('version__policy', 'os_user')
        else:
            # we remove main app user when it's UBA, since we only care about assigned devices
            excludes = {}
            if self.policy.organisation.is_uba_enabled and self.policy.organisation.main_app_user:
                excludes['app_install__app_user_id'] = self.policy.organisation.main_app_user.id
            return self.agreements.filter(
                *self.agreement_groups_filters, **filters
            ).select_related('app_install__app_user').order_by(
                'version__policy', 'app_install__app_user'
            ).distinct('version__policy', 'app_install__app_user').exclude(**excludes)

    def get_not_agreed(self):
        """
        Returns app users or app local users
        that didn't agree this policy.
        :return: users that didn't agree this policy
        :rtype: AppUser or AppInstallOSUser queryset
        """
        if self.policy.organisation.is_bulk_enrollment_type:
            from appusers.models import AppInstallOSUser
            return AppInstallOSUser.objects.filter(
                app_install__app_user__organisation=self.policy.organisation,
                app_install__app_user__active=True,
                app_install__inactive=False
            ).select_related('app_install__os').select_related('app_install__app_user').exclude(
                app_install__pk__in=self.get_agreed().values_list('app_install__pk', flat=True)
            )
        else:
            from appusers.models import AppUser
            # we remove main app user when it's UBA, since we only care about assigned devices
            excludes = {'pk__in': list(self.get_agreed().values_list('app_install__app_user__pk', flat=True))}
            if self.policy.organisation.is_uba_enabled:
                excludes['pk__in'].append(self.policy.organisation.main_app_user.id)

            if self.policy.groups.exists():
                # if policy is assigned to any group,
                # we need to check for at least one group match and include such users
                filters = {
                    "groups__in": self.policy.groups.all()
                }
            else:
                # if policy is not assigned to any group,
                # we need to only include users that are not assigned as well
                filters = {
                    "groups__isnull": True
                }

            return AppUser.objects.annotate(installs_count=Count('installs')).filter(
                organisation=self.policy.organisation,  # pylint: disable=no-member
                active=True,
                installs__inactive=False,
                installs_count__gt=0,
                **filters
            ).exclude(**excludes)

    @property
    def update_url(self) -> str:
        """
        Returns policy update url.
        """
        return reverse("smart_policies:policies-version-update", kwargs={
            "org_id": self.policy.organisation.secure_id,
            "policy_pk": self.policy.pk,
            "pk": self.pk
        })

    @property
    def rounded_version(self) -> float|None:
        """
        Returns rounded version.
        """
        version = self.version
        if not version:
            return None
        if isinstance(version, float):
            # Remove floating point errors while preserving version numbers
            truncated = int(version * 10000) / 10000  # Truncate to 4 decimal places
            return float(f'{truncated:.4f}'.rstrip('0').rstrip('.'))
        return version

    @property
    def incremental_version(self) -> float:
        """
        Returns incremental version.
        """
        version = self.version or 0
        return abs(round(version + 0.1, 1))


class OrganisationApprovedDomain(TimeStampedModel):
    """
    This model stores approved domains that can be used in email addresses when users attempt to log in to the legacy mobile app.
    While not required for the new Trustd mobile app, it is kept and
    still storing values for new organisations:
    - to not delete existing data
    - to allow for future use of domains
    """
    organisation = models.ForeignKey(
        Organisation, verbose_name='Organisation', on_delete=models.CASCADE, related_name='approved_domains'
    )
    domain = CICharField(
        verbose_name='Domain', max_length=255, validators=[domain_name_validator]
    )
    active = models.BooleanField(verbose_name='Active', default=True)

    class Meta:
        verbose_name = 'Approved Domain'
        verbose_name_plural = 'Organisation\'s Approved Domains'
        unique_together = ('organisation', 'domain')

    def __unicode__(self):
        return '{0} - {1}'.format(self.organisation.name, self.domain)

    def __str__(self):
        return self.__unicode__()


class OrganisationSettings(TimeStampedModel, OrganisationFeatures, CRUDSignalMixin):
    """
    Organisation specific settings that can be adjusted.
    """
    # this is the limit notification reminders need to comply with
    validators = [MinValueValidator(1), MaxValueValidator(720)]

    organization = models.OneToOneField(Organisation, unique=True, on_delete=models.CASCADE, related_name="settings")
    lms_send_email_notifications = models.BooleanField(default=True)

    devices_cleaning_visible = models.BooleanField(
        verbose_name="Inactive devices cleaning visibility",
        default=False,
        help_text="If organisation can see inactive devices cleaning settings"
    )
    devices_cleaning_days = models.PositiveIntegerField(
        verbose_name="Remove devices that didn't check-in for the last * days",
        default=90
    )
    devices_cleaning_include_mobile = models.BooleanField(
        verbose_name="Device cleaning, include mobile devices",
        default=False
    )
    cap_steps_to_fix_enabled = models.BooleanField(
        default=True,
        verbose_name='Active Protect Steps to Fix Enabled',
        help_text='Enable "steps to fix" guides for Active Protect users'
    )
    cap_vulnerable_software_enabled = models.BooleanField(
        default=True,
        verbose_name="Active Protect Vulnerable Software Enabled",
        help_text="Enable \"vulnerable software\" guides for Active Protect users"
    )
    cap_auto_update_enabled = models.BooleanField(
        default=True,
        verbose_name='Active Protect Auto Update Enabled',
        help_text="Enable auto update feature for CAP. If disabled, update must be done manually."
    )
    features_help_text = models.JSONField(
        default=dict,
        help_text='The help text will be shown to the user in CAP, providing more information about the feature being enabled or disabled'
    )

    # Active Protect desktop app related notifications
    app_checks_notifications = models.BooleanField(default=False, help_text='Enable app checks notifications')
    app_checks_reminders = models.PositiveIntegerField(
        blank=True,
        null=True,
        validators=validators,
        help_text='Set a follow-up reminder, in hours, for the app checks notifications'
    )
    policies_notifications = models.BooleanField(default=False, help_text='Enable policies notifications')
    policies_reminders = models.PositiveIntegerField(
        blank=True,
        null=True,
        validators=validators,
        help_text='Set a follow-up reminder, in hours, for the policies notifications'
    )
    academy_notifications = models.BooleanField(default=False, help_text='Enable academy notifications')
    academy_reminders = models.PositiveIntegerField(
        blank=True,
        null=True,
        validators=validators,
        help_text='Set a follow-up reminder, in hours, for the academy notifications'
    )
    enforce_multi_factor_authentication = models.BooleanField(
        default=False, help_text='Enforce Multi-Factor Authentication')

    cybersmart_learn_enabled = models.BooleanField(
        verbose_name="CyberSmart Learn enabled",
        default=False,
        help_text="Whether CyberSmart Learn is enabled for this organisation",
    )

    default_language = models.CharField(
        verbose_name="Default language",
        max_length=10,
        default=settings.LANGUAGE_CODE,
        help_text="Default language for the organisation",
        choices=settings.LANGUAGES,
    )

    patch_management_multiselection = models.BooleanField(
        verbose_name="Patch Management Multiselection",
        default=False,
        help_text="Enable patch management multiselection for software page",
    )

    objects = CRUDManager()

    class Meta:
        verbose_name = 'Organisation Setting'
        verbose_name_plural = 'Organisation\'s Settings'

    def __unicode__(self):
        return "{0}".format(self.organization.name)

    def __str__(self):
        return self.__unicode__()


class OrganisationUserSync(TimeStampedModel):
    """
    In this model we store information about synced users from microsoft/google
    """
    organisation = models.OneToOneField(Organisation, on_delete=models.CASCADE)
    social_token = models.OneToOneField(
        SocialToken,
        on_delete=models.CASCADE, null=True, blank=True)
    added_users = models.IntegerField('Number of New Users', default=0)
    removed_users = models.IntegerField('Number of Disabled Users', default=0)
    users_data = models.JSONField('Users data', default=dict)
    sync_enabled = models.BooleanField('Is Sync Enabled', default=False)
    auto_enroll = models.BooleanField('Automatic enroll', default=False)
    last_synced_time = models.DateTimeField(null=True, blank=True, default=None)

    PROVIDER_GOOGLE = 'google'
    PROVIDER_MICROSOFT = 'microsoft'

    class Meta:
        verbose_name = 'Users Sync'
        verbose_name_plural = 'Users Sync'

    def __unicode__(self):
        return 'Added Users: {0}, Removed Users: {1}'.format(self.added_users, self.removed_users)

    def update_user_data(self, email, active=True):
        """ Helper function to update user data for a specific user email.
        It also sets the user to active or inactive.
        """
        try:
            user_index = next(i for i, item in enumerate(self.users_data['users']) if item["email"] == email)
            users_list = self.users_data.get('users')
            if not users_list[user_index]['imported'] and active:
                users_list[user_index]['imported'] = True
                self.users_data['imported'] += 1
                self.users_data['not_imported'] -= 1
            elif not active and users_list[user_index]['active']:
                self.users_data['deactivated'] += 1

            users_list[user_index]['active'] = active
            users_list[user_index]['enrolled'] = active
        except StopIteration:
            pass
        else:
            self.last_synced_time = timezone.now()
            self.save()

    def set_user_as_enrolled(self, email):
        self.update_user_data(email)

    def enable_sync(self, social_token):
        self.social_token = social_token
        self.sync_enabled = True
        self.save()

    def enable_auto_sync(self, social_token):
        self.social_token = social_token
        self.sync_enabled = True
        self.auto_enroll = True
        self.save()

    def disable_sync(self):
        self.sync_enabled = False
        self.save()

    def sync_now(self):
        from .tasks import sync_users_for_org
        sync_users_for_org(org_id=self.organisation.id, queue=False)

    def update_users(self):
        # pylint: disable=unsubscriptable-object, unsupported-assignment-operation
        from appusers.models import AppUser

        users_list = self.users_data.get('users')
        if users_list:
            added_users = 0
            removed_users = 0
            users_uuid_list = []
            for user in users_list:
                app_user = AppUser.objects.filter(
                    organisation=self.organisation, email=user['email']).first()

                if 'suspended' in user:
                    if app_user:
                        app_user.active = False
                        app_user.save()
                        removed_users += 1
                        user['active'] = False
                        user['enrolled'] = False
                else:
                    full_name_list = user['full_name'].split()
                    first_name = full_name_list[0]
                    last_name = full_name_list[-1]

                    if not app_user:
                        app_user = AppUser.objects.create(
                            organisation=self.organisation,
                            first_name=first_name,
                            last_name=last_name,
                            email=user['email']
                        )
                        user['imported'] = True
                        user['active'] = True
                        user['enrolled'] = True
                        added_users += 1
                        self.users_data['not_imported'] -= 1
                        self.users_data['imported'] += 1
                        # append new users uuids for sending an install email.
                        users_uuid_list.append(app_user.uuid)

            if added_users or removed_users:
                self.added_users = added_users
                self.removed_users = removed_users
                self.save()

            if added_users and users_uuid_list:
                from emails.tasks import send_download_app_email
                send_download_app_email.delay(users_uuid_list)

    @property
    def next_synced(self):
        if self.last_synced_time:
            return self.last_synced_time + timezone.timedelta(hours=1)
        return timezone.now() + timezone.timedelta(hours=1)

    @property
    def provider(self):
        if self.social_token:
            return self.social_token.account.provider

    @property
    def email(self):
        if self.social_token and self.social_token.account.extra_data:
            extra_data = self.social_token.account.extra_data
            if 'email' in extra_data:
                return extra_data['email']
            elif 'mail' in extra_data:
                return extra_data['mail']


class OrganisationAddress(TimeStampedModel):
    """
    In this model we store local organisation address information
    """
    organisation = models.OneToOneField(
        to=Organisation, verbose_name='Organisation', related_name='address', on_delete=models.CASCADE
    )
    legal_company_address_line1 = models.CharField(
        verbose_name='Legal company address line 1', max_length=255, null=True, blank=True
    )
    legal_company_address_line2 = models.CharField(
        verbose_name='Legal company address line 2', max_length=255, null=True, blank=True
    )
    legal_company_address_line3 = models.CharField(
        verbose_name='Legal company address line 3', max_length=255, null=True, blank=True
    )
    legal_company_address_town = models.CharField(
        verbose_name='Legal company address town', max_length=255, null=True, blank=True
    )
    legal_company_address_postcode = models.CharField(
        verbose_name='Legal company address postcode', max_length=255, null=True, blank=True
    )
    legal_company_address_country = models.CharField(
        verbose_name='Legal company address country', max_length=255, null=True, blank=True
    )
    company_address_line1 = models.CharField(
        verbose_name='Company address line 1', max_length=255, null=True, blank=True
    )
    company_address_line2 = models.CharField(
        verbose_name='Company address line 2', max_length=255, null=True, blank=True
    )
    company_address_line3 = models.CharField(
        verbose_name='Company address line 3', max_length=255, null=True, blank=True
    )
    company_address_town = models.CharField(
        verbose_name='Company address town', max_length=255, null=True, blank=True
    )
    company_address_postcode = models.CharField(
        verbose_name='Company address postcode', max_length=255, null=True, blank=True
    )
    company_address_country = models.CharField(
        verbose_name='Company address country', max_length=255, null=True, blank=True
    )

    class Meta:
        verbose_name = 'Organisation Address'
        verbose_name_plural = 'Organisation\'s Addresses'

    def __unicode__(self):
        return self.organisation.name

    def __str__(self):
        return self.__unicode__()


class Role(TimeStampedModel):
    """
    Roles are used to group permissions together for an organisation.
    """
    name = models.CharField(verbose_name='name', max_length=150)
    organisation = models.ForeignKey(Organisation, on_delete=models.CASCADE, related_name='roles')
    permissions = models.ManyToManyField(
        to=Permission,
        related_name='roles',
        verbose_name='Permissions',
        blank=True,
    )

    class Meta:
        verbose_name = 'Role'
        verbose_name_plural = 'Roles'
        unique_together = ('name', 'organisation')

    def __str__(self):
        return self.name


class OrganisationGetter:
    """
    A layer of logic that determines which organisations user can access.
    There are three access levels:
        1. Organisation Admin
        2. Partner User
        3. Distributor User
    """

    @staticmethod
    def admin_access(user: User) -> QuerySet:
        """
        Returns a QuerySet of organisations PKs where given user is admin.
        """
        return Organisation.objects.filter(admins__user=user).values_list("pk", flat=True)

    @staticmethod
    def partner_access(user: User) -> QuerySet:
        """
        Returns a QuerySet of organisations PKs that can be accessed by a partner to which belongs given user.
        """
        if partner := user.profile.partner:  # noqa
            return Organisation.objects.filter(partner_id=partner.pk).values_list("pk", flat=True)
        else:
            return Organisation.objects.none()

    @staticmethod
    def __get_distributor_organisations(distributor: "Distributor", is_partner_org: bool) -> QuerySet:  # noqa
        """
        Returns either partner organisations or partner clients organisations for given distributor.
        is_partner_org == True == partner organisations
        is_partner_org == False == partner clients organisations
        """
        return Organisation.objects.filter(
            partner_id__in=distributor.partners.values_list("pk", flat=True),
            is_partner_org=is_partner_org
        ).values_list("pk", flat=True)

    def distributor_access(self, user: User) -> QuerySet:
        """
        Returns a QuerySet of organisations PKs that can be accessed by a distributor to which belongs given user.
        """
        if distributor := user.profile.get_distributor:  # noqa
            if distributor.access_partners_organisations and distributor.access_partners_clients:
                # return all partner and client organisations under given distributor
                return Organisation.objects.filter(partner_id__in=distributor.partners.values_list("pk", flat=True))
            elif distributor.access_partners_organisations:
                # return only partner organisations
                return self.__get_distributor_organisations(distributor=distributor, is_partner_org=True)
            elif distributor.access_partners_clients:
                # return only partner clients organisations
                return self.__get_distributor_organisations(distributor=distributor, is_partner_org=False)
            else:
                return Organisation.objects.none()
        else:
            return Organisation.objects.none()

    def get_organisations(
            self, user: User, secure_id: Optional[str] = None, as_queryset: bool = False
    ) -> Union[QuerySet, None, Organisation]:
        """
        Returns all organisations that user can access or a single one if secure_id is given.
        """
        final_queryset = Organisation.objects.filter(
            pk__in=self.admin_access(user).union(self.partner_access(user)).union(self.distributor_access(user))
        )
        if secure_id:
            return final_queryset.filter(
                secure_id=secure_id
            ) if as_queryset else final_queryset.filter(
                secure_id=secure_id
            ).first()
        else:
            return final_queryset.order_by("pk").distinct()


get_organisations = OrganisationGetter().get_organisations


class OrganisationCertificationAudit(TimeStampedModel):
    """
    This model represents certification audit request.
    """
    organisation = models.ForeignKey(
        to=Organisation,
        verbose_name="Organisation",
        related_name="certification_audits",
        on_delete=models.CASCADE
    )
    certificate_version = models.ForeignKey(
        to=CertificationVersion,
        verbose_name="Certificate version",
        related_name="certification_audits",
        on_delete=models.CASCADE
    )

    reschedule_link = models.URLField(verbose_name="Calendly reschedule link", max_length=255)
    salesforce_case_id = models.CharField(verbose_name="Salesforce Case ID", max_length=255)
    request_date = models.DateTimeField(verbose_name="Request date", help_text="Date when the audit will happen.")

    class Meta:
        verbose_name = "Certification Audit"
        verbose_name_plural = "Certification Audits"
        unique_together = ("organisation", "certificate_version")

    def __str__(self) -> str:
        """
        Returns string representation of the object.
        """
        return f"{self.organisation.name} - {self.certificate_version}"

    @property
    def certification(self) -> Optional[OrganisationCertification]:
        """
        Returns certification object.
        """
        return self.organisation.certifications.filter(version=self.certificate_version).first()


class OrganisationToken(TimeStampedModel):
    """
    This model represents a token generated specifically for an organisation.
    At the moment this is used for bulk enrolment of Trustd mobile app.
    """
    organisation = models.OneToOneField(to=Organisation, on_delete=models.CASCADE, related_name='token')
    token = models.CharField(verbose_name="Token", max_length=50)

    class Meta:
        verbose_name = "Organisation Token"
        verbose_name_plural = "Organisation's Tokens"

    def __str__(self) -> str:
        return f"Token for {self.organisation.name}"
