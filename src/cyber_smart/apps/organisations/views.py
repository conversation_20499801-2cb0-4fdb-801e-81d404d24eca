import logging
from typing import Any, Dict, List

from django.contrib import messages
from django.core.exceptions import PermissionDenied
from django.db import transaction
from django.db.models import Count, Prefetch, Q, QuerySet
from django.forms import Form
from django.forms.models import formset_factory
from django.shortcuts import HttpResponseRedirect, get_object_or_404
from django.urls import reverse
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from django.views.generic import FormView, ListView, TemplateView, View

from accounts.mixins import PermissionRoleRequiredMixin
from accounts.permissions import (
    CERTIFICATES_AND_INSURANCE_PERMISSION,
    DEVICES_PERMISSION,
    PEOPLE_AND_ORGANISATION_PERMISSION,
)
from appusers.forms import AppUserEnrollForm, BaseAppUserFormSet
from appusers.models import (
    AppFile,
    AppInstall,
    AppUser,
)
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from appusers.utils import send_emails_to_users_with_kind
from beta_features.models import (
    CAP_V_FIVE_BETA_SWITCH,
    STABLE_DESKTOP_AND_MOBILE_VERSION_KIND,
    STABLE_MOBILE_VERSION_KIND,
    STABLE_VERSION_KIND,
    TRUSTD_BETA_SWITCH,
)
from beta_features.utils import (
    available_for_upgrade_queryset,
    available_to_deactivate_queryset,
    combined_deprecated_queryset,
    is_trustd_mobile_available_for_organisation,
)
from billing.providers.chargebee.exceptions import ChargebeeError
from billing.providers.chargebee.plans import (
    DIRECT_CUSTOMER_V4_CE_ANNUAL_2024,
    DIRECT_CUSTOMER_V4_CE_MONTHLY_2024,
    DIRECT_CUSTOMER_V5_APP_CE_ANNUAL_2024,
    DIRECT_CUSTOMER_V5_APP_CE_MONTHLY_2024,
)
from common.mixins import (
    FilteringViewMixin,
    OrderingViewMixin,
    SearchViewMixin,
    StaffRequiredMixin,
)
from common.models import CSVFile, Group, StaticFiles
from common.tasks import generate_devices_csv
from common.views import BaseCSVReportView, BaseDevicesView
from dashboard.views import serialize_app_users
from distributors.models import Distributor
from insurance.utils import get_insurance_upsell_questions
from lms.tasks import add_user_handlers
from opswat.models import InstalledProductVersion
from opswat.utils import parse_composite_ids
from opswat_patch.models import OpswatPatchAttempt, OpswatPatchJob
from opswat_patch.utils import enrich_installed_software_with_installers
from organisations.forms import ToggleFeatureForm
from organisations.models import (
    DEFAULT_ROLE_NAME,
    OrganisationSettings,
    OrganisationUserSync,
)
from partners.models import Partner
from rulebook.models import CYBER_ESSENTIALS, ESSENTIAL_EIGHT, NIS2
from rulebook.utils import SurveyTopicStat, get_latest_version
from signup.mixins import SignupMixin
from software_inventory.forms import SchedulePatchInstallationForm
from software_inventory.views import (
    InstalledSoftwareBaseView, PackageBaseView, PatchHistoryBaseView, PatchSummaryBaseView,
    PatchAttemptDetailsBaseView, InstalledSoftwareExportDataBaseView
)
from .models import Organisation, OrganisationCertification, get_organisations
from .utils import get_permissions_details, get_permissions_name_display

logger = logging.getLogger(__name__)


class ToggleFeatureView(StaffRequiredMixin, FormView):
    """
    Toggles organisation features.
    """
    form_class = ToggleFeatureForm
    ignore_organisation_cascade_update = ["certificate_auto_renewal_enabled"]

    @staticmethod
    def get_cleaned_fields(form):
        return (
            form.cleaned_data["object_type"],
            form.cleaned_data["object_id"],
            form.cleaned_data["feature_field_name"],
            form.cleaned_data["feature_field_value"]
        )

    def update_partner(self, object_type, object_id, feature_field_name, feature_field_value):
        if object_type == "partner":
            try:
                p = Partner.objects.get(pk=object_id)
            except Partner.DoesNotExist:
                logger.error(f"Partner with ID:{object_id} does not exist")
                messages.error(self.request, "Such partner does not exist")
                return HttpResponseRedirect(self.request.headers.get('referer'))
            else:
                if not getattr(p.distributor, f"default_{feature_field_name}") and feature_field_value:
                    logger.error("The feature is disabled on distributor level")
                    messages.error(self.request, "The feature is disabled on distributor level")
                    return HttpResponseRedirect(self.request.headers.get('referer'))
                else:
                    setattr(p, f"default_{feature_field_name}", feature_field_value)
                    p.save()

    def update_distributor(self, object_type, object_id, feature_field_name, feature_field_value):
        if object_type == "distributor":
            try:
                d = Distributor.objects.get(pk=object_id)
            except Distributor.DoesNotExist:
                logger.error(f"Distributor with ID:{object_id} does not exist")
                messages.error(self.request, "Such distributor does not exist")
                return HttpResponseRedirect(self.request.headers.get('referer'))
            else:
                setattr(d, f"default_{feature_field_name}", feature_field_value)
                d.save()
                # also update all distributor partners
                d.partners.update(**{f"default_{feature_field_name}": feature_field_value})

    @staticmethod
    def update_organisations(object_type, object_id, feature_field_name, feature_field_value):
        # update organisations
        try:
            organisation_filter = "partner_id" if object_type == "partner" else "partner__distributor_id"
            OrganisationSettings.objects.filter(
                **{f"organization__{organisation_filter}": object_id}
            ).update(**{feature_field_name: feature_field_value})
        except Exception as error:
            logger.error(error)
            transaction.set_rollback(True)

    @transaction.atomic
    def form_valid(self, form):
        """ Any time we enable/disable on the distributor/partner admin, this will cascade down to organisations """
        object_type, object_id, feature_field_name, feature_field_value = self.get_cleaned_fields(form)

        # update partner and distributor
        for method_name in ["update_partner", "update_distributor"]:
            error_on_update = getattr(self, method_name)(
                object_type, object_id, feature_field_name, feature_field_value
            )
            if error_on_update:
                transaction.set_rollback(True)
                return error_on_update

        if feature_field_name not in self.ignore_organisation_cascade_update:
            # update organisations
            self.update_organisations(object_type, object_id, feature_field_name, feature_field_value)
        messages.success(
            self.request, f"The feature has been successfully {['disabled', 'enabled'][feature_field_value]}"
        )
        return HttpResponseRedirect(self.request.headers.get('referer'))

    def form_invalid(self, form):
        logger.error(form.errors)
        messages.error(self.request, _("Unable toggle the feature. Check sentry for errors."))
        return HttpResponseRedirect(self.request.headers.get('referer'))


class DevicesView(PermissionRoleRequiredMixin, BaseDevicesView):
    """
    Displays all devices under organisation.
    """
    template_name = "organisations/devices/main.html"
    type_of_view = 'organisation'
    permission_required = DEVICES_PERMISSION

    def get_view_url(self):
        return reverse('organisations:devices', kwargs={'org_id': self.org.secure_id})

    def get_main_entity(self) -> Organisation:
        self.org = get_organisations(self.request.user, self.kwargs["org_id"])
        if not self.org:
            raise PermissionDenied()
        else:
            return self.org

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["organisation"] = self.org
        context["devices_csv_url"] = f"/organisation/{self.org.secure_id}/devices-csv-report/"
        return context


class OrganisationDevicesCSVReportView(BaseCSVReportView):
    """ Generates CSV report with all Devices."""

    type_of_csv = CSVFile.TYPES_OF_CSV.DEVICES
    csv_generation_task = generate_devices_csv
    objects_name = 'device_ids'
    main_entity_name = 'organisation_id'


class CertificatesView(PermissionRoleRequiredMixin, SignupMixin, TemplateView):
    """
    Displays general certificates data under organisation.
    """
    template_name = "organisations/certificates/main.html"
    permission_required = CERTIFICATES_AND_INSURANCE_PERMISSION

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        self.org = get_organisations(self.request.user, self.kwargs["org_id"], as_queryset=True).prefetch_related(
            Prefetch(
                "certifications",
                queryset=OrganisationCertification.objects.filter(
                    status__in=[*OrganisationCertification.CERTIFIED_STATUSES, OrganisationCertification.EXPIRED],
                ).select_related(
                    "version", "version__type", "survey", "survey__declaration",
                    "survey__assessment", "issued_certification"
                ).order_by("-version__version_number"),
                to_attr="historical_certificates"
            )
        ).first()
        if not self.org:
            raise PermissionDenied()
        context["organisation"] = self.org
        if ce := self.org.ce_certification:
            if hasattr(ce, "survey"):
                context["assessment"] = SurveyTopicStat(ce, main_key_as_title=True).get()
        context["ESSENTIAL_EIGHT_CERTIFICATE"] = ESSENTIAL_EIGHT
        context["NIS2_CERTIFICATE"] = NIS2
        context['questions'] = get_insurance_upsell_questions(ce)
        return context


class DirectCustomerEnableCyberEssentials(SignupMixin, View):
    """
    Enables Cyber Essentials for direct customers (adds cert & creates subscription).
    """

    def get_direct_customer_ce_plan(self, request, organisation: Organisation) -> str:
        """
        Returns the CE plan for the direct customer.
        """
        if organisation.has_software_subscription:
            if organisation.pricing_band == organisation.DIRECT_CUSTOMER_V4_V5_PRICING_ANNUAL:
                return DIRECT_CUSTOMER_V5_APP_CE_ANNUAL_2024
            else:
                return DIRECT_CUSTOMER_V5_APP_CE_MONTHLY_2024
        else:
            if organisation.pricing_band == organisation.DIRECT_CUSTOMER_V4_V5_PRICING_ANNUAL:
                return DIRECT_CUSTOMER_V4_CE_ANNUAL_2024
            else:
                return DIRECT_CUSTOMER_V4_CE_MONTHLY_2024

    def post(self, request, org_id):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()

        if not organisation.is_billable:
            messages.add_message(request, messages.ERROR, _("Something went wrong, Please contact customer support."))
        elif organisation.ce_certification:
            messages.add_message(request, messages.ERROR, _("This organisation already has Cyber Essentials."))
        else:
            # add the subscription
            try:
                if organisation.is_billable:
                    organisation.customer.add_certificate_plan(self.get_direct_customer_ce_plan(request, organisation))
            except ChargebeeError:
                messages.add_message(request, messages.ERROR, _("Something went wrong, Please contact customer support."))
            else:
                # add cert
                organisation.certifications.create(
                    version=get_latest_version(type=CYBER_ESSENTIALS)
                )
                messages.add_message(
                    request, messages.SUCCESS, _("Cyber Essentials subscription was enabled for this organisation")
                )
        return HttpResponseRedirect(request.GET.get("next", organisation.url))


class DashboardAccess(PermissionRoleRequiredMixin, SignupMixin, TemplateView):
    permission_required = PEOPLE_AND_ORGANISATION_PERMISSION
    template_name = 'organisations/users-and-groups/dashboard-access/main.html'
    organisation = None

    def set_organisation(self):
        """ Helper method to set organisation or raise permission denied """
        user = self.request.user
        org_id = self.kwargs['org_id']
        organisation = get_organisations(user, org_id)
        if not organisation:
            raise PermissionDenied()
        self.organisation = organisation

    def get_context_data(self, **kwargs):
        self.set_organisation()
        context = super().get_context_data(**kwargs)
        org_id = self.kwargs['org_id']
        organisation = self.organisation
        partner_users = organisation.partner.users.all().values_list('user__id', flat=True)
        roles = organisation.roles.all().prefetch_related("permissions").annotate(
            users_count=Count("profiles", filter=~Q(profiles__user__in=partner_users))
        )
        organisation_admins = organisation.admins.exclude(user__in=partner_users)

        context['dashboard_access_page_name'] = _('Dashboard Access')
        context['organisation'] = organisation
        context['organisation_admins'] = organisation_admins
        context['org_id'] = org_id
        context['is_bulk'] = organisation.bulk_install
        context["is_cap_only"] = organisation.is_cap_only
        context["roles"] = roles
        context["dashboard_users_ids"] = list(organisation_admins.values_list('id', flat=True))
        context["dashboard_users_count"] = len(context["dashboard_users_ids"])
        context["last_default_role_user_id"] = None
        # only for direct customers
        if organisation.is_direct_customer:
            last_default_role_users_ids = list(
                organisation_admins.filter(user__profile__roles__name_en_gb=DEFAULT_ROLE_NAME).values_list('id', flat=True)
            )
            if len(last_default_role_users_ids) == 1:
                context["last_default_role_user_id"] = last_default_role_users_ids[0]
                context["dashboard_users_ids"] = list(
                    organisation_admins.exclude(user__profile__roles__name_en_gb=DEFAULT_ROLE_NAME)
                    .values_list('id', flat=True)
                )
                context["dashboard_users_count"] = context["dashboard_users_count"] - 1
        # Default role always contains all existing permissions as enabled - so it is source of truth.
        default_role = roles.filter(name_en_gb=DEFAULT_ROLE_NAME).first()
        context["all_permissions"] = default_role.permissions.all()
        context["DEFAULT_ROLE_NAME"] = default_role.name
        context["permissions_descriptions"] = get_permissions_details(organisation)
        context["permissions_name_display"] = get_permissions_name_display()
        return context


class UsersAndGroups(PermissionRoleRequiredMixin, SignupMixin, FormView):
    permission_required = PEOPLE_AND_ORGANISATION_PERMISSION
    template_name = 'organisations/users-and-groups/main.html'
    organisation = None
    app_users = None
    nr_new_users_allowed = 0
    form_class = AppUserEnrollForm

    def get(self, request, *args, **kwargs):
        """Handle GET requests: instantiate a blank version of the form."""
        return self.render_to_response(self.get_context_data())

    def get_initial(self):
        return {}

    def get_success_url(self):
        org_id = self.kwargs['org_id']
        organisation = get_object_or_404(Organisation, secure_id=org_id)
        return organisation.users_and_groups_url

    def form_valid(self, forms):
        organisation = self.organisation

        existing_emails = []
        app_users_uuids = []
        for form in forms:
            app_user_uuid, is_new = form.update_data(self, organisation)
            if app_user_uuid and is_new:
                app_users_uuids.append(app_user_uuid)
            elif not is_new and form.cleaned_data['email']:
                existing_emails.append(form.cleaned_data['email'])

        email_kind = form.data.get('email_kind', STABLE_VERSION_KIND)
        NO_EMAIL_KIND = 'no-email'
        if email_kind not in [STABLE_VERSION_KIND, STABLE_MOBILE_VERSION_KIND, STABLE_DESKTOP_AND_MOBILE_VERSION_KIND, NO_EMAIL_KIND, CAP_V_FIVE_BETA_SWITCH, TRUSTD_BETA_SWITCH]:
            raise ValueError('Invalid email kind')

        if (organisation.has_software_support and
                not organisation.is_trial and
                app_users_uuids and
                not email_kind == NO_EMAIL_KIND):
            if email_kind == STABLE_DESKTOP_AND_MOBILE_VERSION_KIND:
                send_emails_to_users_with_kind(organisation, AppUser.objects.filter(uuid__in=app_users_uuids), STABLE_VERSION_KIND)
                send_emails_to_users_with_kind(organisation, AppUser.objects.filter(uuid__in=app_users_uuids), STABLE_MOBILE_VERSION_KIND)
            else:
                send_emails_to_users_with_kind(organisation, AppUser.objects.filter(uuid__in=app_users_uuids), email_kind)

        if organisation.learn_lite_tab_displayed and app_users_uuids:
            add_user_handlers.apply_async(args=[organisation.id, app_users_uuids])

        # Show existing users
        if existing_emails:
            existing_emails_str = "<br/>".join(set(existing_emails))
            message = _("Users with the following emails exist:") + f"<br/> {existing_emails_str}"
            messages.add_message(self.request, messages.INFO, message)
        return super(UsersAndGroups, self).form_valid(forms)

    def set_organisation(self):
        """ Helper method to set organisation or raise permission denied """
        user = self.request.user
        org_id = self.kwargs['org_id']
        organisation = get_organisations(user, org_id)
        if not organisation:
            raise PermissionDenied()
        self.organisation = organisation

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs.update({'request': self.request})
        form_kwargs = kwargs.get("form_kwargs", {})
        form_kwargs.update({"organisation": self.organisation, "request": self.request})
        kwargs.update({"form_kwargs": form_kwargs})
        return kwargs

    def get_form_class(self):
        """Override form class to limit number of users the customer can add and to validate it"""
        self.set_organisation()
        self.app_users = self.organisation.get_app_users()
        #  if the org does not have a limit (>500) then just set the maximum for the form
        nr_new_users_allowed = 100
        # set this to None in order to not show the limitation info text in the front-end
        self.nr_new_users_allowed = None
        # if the org does not have a limit then we do not validate
        validate_max = False
        if self.organisation.has_max_cap_users:
            # number of enrolled app users that the org admin is allowed to add
            self.nr_new_users_allowed = self.organisation.get_nr_new_users_allowed()
            nr_new_users_allowed = self.nr_new_users_allowed
            validate_max = True
        return formset_factory(self.form_class, can_delete=False, extra=1, formset=BaseAppUserFormSet,
                               max_num=nr_new_users_allowed, validate_max=validate_max)

    def get_context_data(self, **kwargs):
        context = super(UsersAndGroups, self).get_context_data(**kwargs)
        org_id = self.kwargs['org_id']
        organisation = self.organisation
        app_users = self.app_users
        groups = Group.objects.filter(
            organisation=organisation,
        )

        enrolled_users_count = organisation.enrolled_users_count
        installed_users_count = organisation.installed_users_count
        secure_users_count = organisation.secure_users_count
        insecure_users_count = organisation.insecure_users_count

        if not hasattr(organisation, 'organisationusersync'):
            OrganisationUserSync.objects.create(organisation=organisation)

        if organisation.organisationusersync.sync_enabled:
            organisation.organisationusersync.sync_now()
            context['provider'] = organisation.organisationusersync.provider
            context['list_users'] = organisation.organisationusersync.users_data

        enrolled_beta_features = organisation.get_enrolled_beta_features()
        context['enrolled_beta_features_allowing_email'] = None
        context['default_email_kind_option'] = ("stable", _('Public Release'))
        if enrolled_beta_features.exists() and not organisation.bulk_install:
            context['enrolled_beta_features_allowing_email'] = enrolled_beta_features
        # for UBA organisations we do allow sending installation email only if it is to install Trustd mobile app in Beta
        elif organisation.is_uba_type:
            context['default_email_kind_option'] = ("no-email", _('None'))
            context['enrolled_beta_features_allowing_email'] = enrolled_beta_features.filter(kind=TRUSTD_BETA_SWITCH)

        context['manage_users_and_groups_page_name'] = _('Manage Users')
        context['app_users'] = app_users
        context['app_users_to_send_email_to'] = serialize_app_users(app_users)
        context['app_users_without_app_installs'] = self.get_users_without_app_installs(app_users)
        context['groups'] = groups
        context['users_ids'] = list(app_users.values_list('id', flat=True))
        context['users_count'] = len(context["users_ids"])
        context['organisation'] = organisation
        context['nr_new_users_allowed'] = self.nr_new_users_allowed
        context['extra_app_users_id'] = organisation.get_extra_app_users_id(app_users)
        context['org_id'] = org_id
        context['is_bulk'] = organisation.bulk_install
        context['enrolled_beta_features'] = enrolled_beta_features
        context['total_app_users'] = enrolled_users_count
        context['installed_app_users'] = installed_users_count
        context['user_passing'] = secure_users_count
        context['user_failing'] = insecure_users_count
        context['has_usable_password'] = self.request.user.has_usable_password()
        context['open_sync_users_modal'] = self.request.GET.get('open_sync_users_modal')
        return context

    def get_users_without_app_installs(self, app_users) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get a dictionary of serialized app users without installed AppInstall for each device type.

        Returns:
            Dict[str, List[Dict[str, Any]]]: A dictionary where keys are device types ('Desktop' or 'Mobile')
                                             and values are lists of serialized app users without installed AppInstall.
        """
        app_users_without_app_install = {
            STABLE_VERSION_KIND: [],
            STABLE_MOBILE_VERSION_KIND: []
        }

        # Get all app users without non-mobile installs
        users_without_non_mobile = app_users.exclude(
            id__in=AppInstall.objects.filter(
                app_user__in=app_users,
                inactive=False
            ).exclude(device_type=AppInstall.MOBILE).values_list('app_user_id', flat=True)
        )

        # Get all app users without mobile installs
        users_without_mobile = app_users.exclude(
            id__in=AppInstall.objects.filter(
                app_user__in=app_users,
                inactive=False,
                device_type=AppInstall.MOBILE
            ).values_list('app_user_id', flat=True)
        )

        app_users_without_app_install[STABLE_VERSION_KIND] = serialize_app_users(users_without_non_mobile)
        app_users_without_app_install[STABLE_MOBILE_VERSION_KIND] = serialize_app_users(users_without_mobile)

        return app_users_without_app_install


class InsuranceView(PermissionRoleRequiredMixin, SignupMixin, TemplateView):
    """
    Displays general insurance data for the organisation.
    """
    template_name = "organisations/insurance/main.html"
    permission_required = CERTIFICATES_AND_INSURANCE_PERMISSION

    def dispatch(self, request, *args, **kwargs):
        """
        Checks if user has access to the organisation.
        """
        self.organisation = get_organisations(request.user, self.kwargs["org_id"])
        if not self.organisation:
            raise PermissionDenied()
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["organisation"] = self.organisation
        context["files"] = StaticFiles.objects.last()
        return context


class DeprecatedDevicesView(PermissionRoleRequiredMixin, SignupMixin, OrderingViewMixin, SearchViewMixin, FilteringViewMixin, ListView):
    model = AppInstall
    context_object_name = "installs"
    paginate_by = 20
    template_name = "organisations/deprecated-devices/main.html"
    ordering_fields_map_used = False
    search_fields = ["app_user__first_name", "app_user__last_name", "hostname", "real_device_id", "email"]
    permission_required = DEVICES_PERMISSION

    organisation = None
    install_count_before_search = -1

    FILTER_AVAILABLE_FOR_UPGRADE = "v4"
    FILTER_AVAILABLE_TO_DEACTIVATE = "v4+v5"
    FILTER_ALL = "all"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["organisation"] = self.organisation
        context["selectable_table_enabled"] = True
        context["v5_agent_link"] = AppFile.get_agent_link_for_organisation(self.organisation, get_v5=True)
        context["install_count_before_search"] = self.install_count_before_search
        context['devices_table_instructions'] = self.get_devices_table_instructions()

        # If trustd_mobile_available is False, there will be only one filter in the frontend,
        # so we want to set it by default.
        if not self.organisation.is_trustd_mobile_available:
            context["param_filtering"] = self.FILTER_AVAILABLE_FOR_UPGRADE
        # for bulk/UBA organisations, we need to show the FILTER_AVAILABLE_TO_DEACTIVATE filter by default
        elif not self.organisation.is_self_enrollment_type and ("param_filtering" not in context):
            context["param_filtering"] = self.FILTER_AVAILABLE_TO_DEACTIVATE

        if self.organisation.is_self_enrollment_type:
            return context

        # for bulk/UBA organisations, we need to hide the CTA modal if the user has selected the v4 filter
        if (context["param_filtering"] == self.FILTER_AVAILABLE_FOR_UPGRADE):
            context["selectable_table_enabled"] = False

        if self.organisation.is_uba_type:
            context['main_app_user'] = self.organisation.main_app_user

        return context

    def get_queryset(self):
        qs = self.request_queryset()
        self.install_count_before_search = qs.count()

        return self.search_queryset(self.ordering_queryset(qs))

    def get(self, request, *args, **kwargs):
        self.organisation = get_organisations(request.user, self.kwargs.get("org_id"))
        if not self.organisation:
            raise PermissionDenied

        self.filter_applied = self.request.GET.get("filter", "").lower()
        if not self.organisation.has_combined_deprecated_installs:
            messages.success(self.request, _("This organisation no longer has any devices with legacy Active Protect Desktop installed."))
            return HttpResponseRedirect(self.organisation.url)
        return super().get(request, *args, **kwargs)

    def request_queryset(self) -> QuerySet:
        """
        Returns the queryset based on the filter applied.
        """
        if not self.filter_applied and (self.organisation.is_bulk_enrollment_type or self.organisation.is_uba_type):
            if self.organisation.is_trustd_mobile_available:
                return available_to_deactivate_queryset(self.organisation)
            else:
                return available_for_upgrade_queryset(self.organisation)
        if self.filter_applied == self.FILTER_AVAILABLE_FOR_UPGRADE:
            return available_for_upgrade_queryset(self.organisation)
        if self.filter_applied == self.FILTER_AVAILABLE_TO_DEACTIVATE:
            return available_to_deactivate_queryset(self.organisation)

        return combined_deprecated_queryset(self.organisation)

    def get_devices_table_instructions(self) -> List[str]:
        """
        Returns a list of instructions for the devices table based on the organisation type and applied filter.
        Used only as a helper for the template, as the conditional logic makes it harder to read in the template.

        :return: A list of instruction strings
        :rtype: List[str]
        """

        mobile_deactivation = _("Devices running old versions of Active Protect for Mobile will be deactivated and no longer be visible on the dashboard until the New and Improved Active Protect for Mobile is installed.")

        if not self.organisation.is_self_enrollment_type:  # UBA/Bulk case
            if self.filter_applied == self.FILTER_AVAILABLE_FOR_UPGRADE:
                return []
            if is_trustd_mobile_available_for_organisation(self.organisation):
                return [mobile_deactivation]
            else:
                return []

        # self-enrollment case
        mobile_deactivation_and_installation = _("Devices running old versions of Active Protect for Mobile will be deactivated and users will be sent an email with installation instructions to upgrade to the New and Improved Active Protect.")
        users_instruction = _("Users with devices running older versions of Active Protect for Desktop will be sent an email with installation instructions to upgrade to the New and Improved Active Protect.")
        if self.filter_applied == self.FILTER_AVAILABLE_FOR_UPGRADE:
            return [users_instruction]
        if self.filter_applied == self.FILTER_AVAILABLE_TO_DEACTIVATE:
            if is_trustd_mobile_available_for_organisation(self.organisation):
                return [mobile_deactivation_and_installation]
            else:
                return []
        if is_trustd_mobile_available_for_organisation(self.organisation):
            return [users_instruction, mobile_deactivation_and_installation]
        else:
            return [users_instruction]


class InstalledSoftwareView(InstalledSoftwareBaseView):
    """
    Displays all installed software under organisation.
    """
    view_level = "organisation"

    def enrich_queryset(self, queryset: QuerySet) -> list:
        """
        Enriches the queryset with installers for the organisation.
        """
        return enrich_installed_software_with_installers(queryset)

    def get_raw_queryset(self) -> QuerySet:
        """
        Returns a queryset of installed software for the organisation.
        """
        return InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation_id=self.organisation.id
        ).order_by("-highest_severity")

    def get_pagination_url(self) -> str:
        """
        Returns the URL to build the pagination links.
        """
        return reverse(
            "organisations:installed-software",
            kwargs={"org_id": self.organisation.secure_id}
        )


class PackageDetailsView(PackageBaseView):
    """
    Package details view.
    """
    view_level = "organisation"

    def get_raw_queryset(self) -> QuerySet:
        """
        Returns queryset for the app install from the configured model.
        """
        return InstalledSoftwareOrganisationIndividual.objects.filter(organisation=self.organisation)

    def _enrich_package_with_installers(self, package):
        """
        Enriches the package with installer information.
        """
        return enrich_installed_software_with_installers([package])[0]

class PatchHistoryView(PatchHistoryBaseView):
    """
    Displays patch history for installed software under the organisation.
    """
    view_level = "organisation"

    def get_form_class(self) -> type[Form]:
        """
        Returns the form class to be used for scheduling patch installations.
        """
        return SchedulePatchInstallationForm

    def get_form_kwargs(self) -> dict:
        """
        Returns the keyword arguments to be passed to the form class.
        """
        return {
            "data": {
                "packages": self.request.POST.getlist("packages"),
                "app_installs": self.request.POST.getlist("app_installs")
            },
            "organisation": self.organisation,
            "package_level": "organisation",
            "user": self.request.user,
        }

    def get_raw_queryset(self) -> QuerySet:
        """
        Returns queryset for the app install from the configured model.
        """
        return OpswatPatchAttempt.objects.filter(
            scheduled_product_installer__app_install__app_user__organisation_id=self.organisation.id
        ).select_related(
            "scheduled_product_installer__app_install",
            "scheduled_product_installer__opswat_product_patch_installer__product",
        ).order_by(
            "-created"
        ).distinct()

    def _get_vendors(self) -> list[str]:
        """
        Returns a list of distinct vendor names.
        """
        vendors = self.get_raw_queryset().values_list(
            "scheduled_product_installer__opswat_product_patch_installer__product__vendor__name", flat=True
        ).distinct()
        return list(dict.fromkeys(vendors))

    def get_pagination_url(self) -> str:
        """
        Returns the URL to build the pagination links.
        """
        return reverse(
            "organisations:patch-history",
            kwargs={"org_id": self.organisation.secure_id}
        )


class PatchAttemptDetailsView(PatchAttemptDetailsBaseView):
    """
    Displays patch installer details for installed software under organisation.
    """
    view_level = "organisation"

    def get_raw_queryset(self) -> QuerySet:
        return PatchAttemptDetailsBaseView.get_raw_queryset(self).filter(
            scheduled_product_installer__app_install__app_user__organisation_id=self.organisation.id
        ).distinct()


class PatchSummaryView(PatchSummaryBaseView):
    """
    View that returns a patch summary for selected package and devices.
    """
    view_level = "organisation"
    template_name = "software_inventory/components/patch_summary/level/organisation.html"
    context_object_name = "devices"

    def _get_package(self):
        """
        Returns the package object based on the URL parameters or raises 404 if not found.
        """
        return get_object_or_404(
            InstalledSoftwareOrganisationIndividual.objects.filter(organisation=self.organisation),
            id=self.request.POST.get("package_id")
        )

    def get_context_data(self, *args, **kwargs) -> dict:
        context = super().get_context_data(*args, **kwargs)
        context["package"] = enrich_installed_software_with_installers([self._get_package()])[0]
        return context

    def get_raw_queryset(self) -> QuerySet:
        """
        Returns a queryset of devices that are selected for patching.
        """
        return self.organisation.installed_devices.filter(pk__in=self.request.POST.getlist("devices_id"))


class MultiSelectionPatchSummaryView(PatchSummaryBaseView):
    """
    View for displaying a summary of selected packages to patch across multiple devices in an organisation.
    """
    view_level = "organisation"
    template_name = "software_inventory/components/patch_summary/level/organisation_multi_select.html"

    def get_context_data(self, *, object_list=None, **kwargs) -> dict:
        context = super().get_context_data(object_list=object_list, **kwargs)
        context["devices_ids"] = self._get_unique_devices_ids
        context["devices_count"] = len(self._get_unique_devices_ids)
        return context

    def _get_package_source_id(self) -> set:
        """
        Returns a set of parsed unique source IDs from the selected packages to patch.
        """
        source_ids = set()
        for org_summary_id in  self.request.POST.getlist("selected_packages"):
            product_version_ids, _ = parse_composite_ids(org_summary_id)
            if product_version_ids:
                for product_version_id in product_version_ids:
                    source_ids.add(product_version_id)
        return source_ids

    @cached_property
    def _get_unique_devices_ids(self) -> list:
        """
        Returns a list of unique device IDs that are selected for patching.
        """
        return list(InstalledProductVersion.objects.filter(
            installed_product__app_install__app_user__organisation=self.organisation,
            product_version_id__in=self._get_package_source_id(),
        ).values_list("installed_product__app_install__id", flat=True).distinct())

    def get_raw_queryset(self) -> QuerySet:
        """
        Returns a queryset of installed software packages that are selected for patching.
        This is used when multiple packages are selected for patching in the organisation.
        """
        return InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=self.organisation,
            id__in=self.request.POST.getlist("selected_packages")
        ).distinct()


class InstalledSoftwareExportDataView(InstalledSoftwareExportDataBaseView):
    """
    Generates a CSV or XLSX report of installed software for a given organisation.
    """
    view_level = "organisation"

    SEVERITY = _("Severity")
    NAME = _("Name")
    VERSION = _("Version")
    VENDOR = _("Vendor")
    VULNERABILITIES = _("Vulnerabilities")
    DEVICES = _("Devices")
    PATCH_DETECTED = _("Patch Detected")

    def get_file_name(self) -> str:
        """
        Returns the file name for the report.
        """
        return f"installed_software_{self.organisation.secure_id}_{timezone.now().strftime('%Y_%m_%d_%H_%M_%S')}"

    def get_field_names(self) -> list:
        """
        Returns the field names for the report.
        """
        return [
            self.SEVERITY,
            self.NAME,
            self.VERSION,
            self.VENDOR,
            self.VULNERABILITIES,
            self.DEVICES,
            self.PATCH_DETECTED
        ]

    def __get_patch_detected_date(self, package) -> str:
        """
        Returns the patch-detected date for the package.
        """
        if hasattr(package, "app_install_product_installer"):
            return package.app_install_product_installer.get("patch_detected_date", "-")
        return "-"

    def write_to_csv(self, writer) -> None:
        """
        Writes the data to the CSV file.
        """
        for package in self.get_queryset():
            row = {
                self.SEVERITY: package.highest_severity,
                self.NAME: package.product,
                self.VERSION: package.version,
                self.VENDOR: package.vendor,
                self.VULNERABILITIES: package.cve_count,
                self.DEVICES: package.device_count,
                self.PATCH_DETECTED: self.__get_patch_detected_date(package)
            }
            writer.writerow(row)

    def get_object(self) -> Organisation:
        """
        Returns the organisation object associated with the current instance.
        """
        return self.organisation

    def get_queryset(self) -> list:
        """
        Returns the queryset for the report.
        """
        return enrich_installed_software_with_installers(
            InstalledSoftwareOrganisationIndividual.objects.filter(organisation=self.organisation).order_by(
                "-highest_severity"
            )
        )


class PatchHistoryExportDataView(InstalledSoftwareExportDataBaseView):
    """
    Generates a CSV or XLSX report of patch history for a given organisation.
    """
    view_level = "organisation"

    NAME = _("Name")
    VERSION = _("Version")
    VENDOR = _("Vendor")
    PATCH_STATUS = _("Patch Status")
    STARTED = _("Started")
    FINISHED = _("Finished")
    DEVICES = _("Devices")

    def get_file_name(self) -> str:
        """
        Returns the file name for the report.
        """
        return f"patch_history_{self.organisation.secure_id}_{timezone.now().strftime('%Y_%m_%d_%H_%M_%S')}"

    def get_field_names(self) -> list:
        """
        Returns the field names for the report.
        """
        return [
            self.NAME,
            self.VERSION,
            self.VENDOR,
            self.PATCH_STATUS,
            self.STARTED,
            self.FINISHED,
            self.DEVICES
        ]

    def write_to_csv(self, writer) -> None:
        """
        Writes the data to the CSV file.
        """
        for patch_job in self.get_queryset():
            patch_attempt = patch_job.latest_attempt
            row = {
                self.NAME: patch_attempt.product.name,
                self.VERSION: f"{patch_attempt.from_version} - {patch_attempt.to_version}",
                self.VENDOR: patch_attempt.product.vendor.name,
                self.PATCH_STATUS: patch_job.short_status,
                self.STARTED: patch_job.created,
                self.FINISHED: patch_job.finished_at if patch_job.is_finished else "-",
                self.DEVICES: patch_job.attempts.count()
            }
            writer.writerow(row)

    def get_object(self) -> Organisation:
        """
        Returns the organisation object associated with the current instance.
        """
        return self.organisation

    def get_queryset(self) -> QuerySet:
        """
        Returns the queryset for the report.
        """
        attempts_qs = OpswatPatchAttempt.objects.select_related(
            "scheduled_product_installer__app_install",
            "scheduled_product_installer__opswat_product_patch_installer__product__vendor"
        ).prefetch_related("event_logs")

        return OpswatPatchJob.objects.filter(
            organisation=self.organisation
        ).prefetch_related(
            Prefetch("attempts", queryset=attempts_qs)
        ).order_by("-created")
