import os
import subprocess
import time
from datetime import <PERSON><PERSON><PERSON>
from urllib import parse as urlparse
from tempfile import NamedTemporaryFile

import fitz
import requests
from billiard.exceptions import WorkerLostError
from celery import shared_task as task
from celery.utils.log import get_task_logger
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.sites.models import Site
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.db import IntegrityError
from django.db.models import Q
from django.utils import timezone
from django.utils.translation import gettext as _
from requests.exceptions import SSLError
from waffle import switch_is_active

from accounts.permissions import CERTIFICATES_AND_INSURANCE_PERMISSION
from accounts.utils import (
    refresh_access_token,
    convert_social_users_to_common_format
)
from common.utils import get_http_protocol_url_name
from emails.tasks import api_template_email, UNSUBSCRIBE_GROUPS
from insurance.models import InsuranceOptIn
from organisations.models import (
    Organisation, OrganisationCertification, OrganisationSettings, OrganisationPolicyVersion, OrganisationUserSync
)
from organisations.forms.create.organisation_populator import OrganisationPopulator
from rulebook.models import CERTIFICATION_TYPES_WITH_DECLARATION_SIGNING_EMAILS, ESSENTIAL_EIGHT, SurveyDeclaration

logger = get_task_logger(__name__)


@task(
    autoretry_for=(IntegrityError, WorkerLostError, SSLError)
)
def sync_users():
    orgs = Organisation.objects.filter(organisationusersync__sync_enabled=True, bulk_install=False)
    for org in orgs:
        social_token_obj = org.organisationusersync.social_token
        if social_token_obj.token_secret:
            social_account = social_token_obj.account
            domain = social_account.extra_data.get('hd', None)
            new_access_token = refresh_access_token(
                refresh_token=social_token_obj.token_secret,
                client_id=social_token_obj.app.client_id,
                client_secret=social_token_obj.app.secret,
                provider=social_account.provider)
            import_social_users.delay(
                org_id=org.id,
                token=new_access_token,
                provider=social_account.provider,
                domain=domain)


@task
def sync_users_for_org(org_id, queue=True):
    org = Organisation.objects.filter(id=org_id).first()
    social_token_obj = org.organisationusersync.social_token
    if social_token_obj.token_secret:
        social_account = social_token_obj.account
        domain = social_account.extra_data.get('hd', None)
        new_access_token = refresh_access_token(
            refresh_token=social_token_obj.token_secret,
            client_id=social_token_obj.app.client_id,
            client_secret=social_token_obj.app.secret,
            provider=social_account.provider)
        if queue:
            import_social_users.delay(
                org_id=org.id,
                token=new_access_token,
                provider=social_account.provider,
                domain=domain)
        else:
            import_social_users(
                org_id=org.id,
                token=new_access_token,
                provider=social_account.provider,
                domain=domain)


@task
def import_social_users(org_id, token, provider, domain=None):
    organisation = Organisation.objects.get(id=org_id)
    list_users = {}
    if not domain:
        domain = ''

    provider_response = get_provider_response(provider, token, domain)
    if provider_response is None:
        logger.info(f"Social import: Provider {provider} is not supported")
        return

    response_json  = provider_response.json()
    log_errors = switch_is_active('social_users_import_log_errors')
    if 'error' in response_json:
        if log_errors:
            logger.info(
                f"Social import: Error getting social users from {provider} for {organisation.name}",
                extra={
                    "organisation_id": organisation.pk,
                    "provider": provider,
                    "response": response_json,
                },
            )
        return

    list_users = convert_social_users_to_common_format(
        users=response_json, provider=provider, organisation=organisation
    )

    if not list_users:
        return
    sync_obj = organisation.organisationusersync
    sync_obj.last_synced = timezone.now()
    sync_obj.users_data = list_users
    sync_obj.save()
    if sync_obj.auto_enroll:
        sync_obj.update_users()


def get_all_microsoft_users(token):
    """
    Get all users from Microsoft Graph API, handling pagination.
    Returns a response-like object with all users combined.
    """
    all_users = []
    # Microsoft Graph /users endpoint returns 100 users by default per page
    # Per Microsoft documentation: "For example, the GET /users endpoint returns a default of 100 results in a single page."
    # Source: https://learn.microsoft.com/en-us/graph/paging
    url = 'https://graph.microsoft.com/v1.0/users'
    headers = {'Authorization': 'Bearer ' + token}
    page_count = 0
    max_pages = 50  # Safety limit to prevent infinite loops

    while url and page_count < max_pages:
        page_count += 1
        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            return response

        try:
            data = response.json()
        except ValueError:
            # Return the original response if JSON parsing fails
            return response

        if 'value' in data:
            all_users.extend(data['value'])
            logger.info(f"Microsoft sync: Fetched page {page_count} with {len(data['value'])} users")

        # Check for next page
        url = data.get('@odata.nextLink')

    if page_count >= max_pages:
        logger.warning(f"Microsoft sync: Reached maximum page limit ({max_pages}). Some users may not have been fetched.")

    logger.info(f"Microsoft sync: Fetched total of {len(all_users)} users across {page_count} pages")

    # Create a mock response object that mimics requests.Response
    class MockResponse:
        def __init__(self, data):
            self._data = data
            self.status_code = 200

        def json(self):
            return self._data

    return MockResponse({'value': all_users})


def get_provider_response(provider, token, domain) -> requests.Response|None:
    """
    Get provider response containing social users for the given provider.
    Returns None if provider is not supported.
    """
    if provider == OrganisationUserSync.PROVIDER_GOOGLE:
        params = '?domain=' + domain + '&viewType=admin_view&projection=full' + '&maxResults=500'
        return requests.get(
            'https://content.googleapis.com/admin/directory/v1/users' + params,
            headers={'Authorization': 'Bearer ' + token}
        )
    elif provider == OrganisationUserSync.PROVIDER_MICROSOFT:
        return get_all_microsoft_users(token)

    return None

@task
def send_declaration_signing_reminder_email(declaration_id):
    """
    Sends email notification when a declaration signing required.
    :param declaration_id: The declaration id that is waiting to be signed
    :type declaration_id: Integer
    :return: nothing
    :rtype: None
    """
    declaration = SurveyDeclaration.objects.prefetch_related(
        'survey__certificate__organisation', 'survey__certificate__version__type'
    ).get(id=declaration_id)
    if declaration.survey.certificate.version.type.type in CERTIFICATION_TYPES_WITH_DECLARATION_SIGNING_EMAILS:
        organisation = declaration.survey.certificate.organisation
        substitutions = [
            {
                'id': 'requested_signer_name',
                'value': declaration.declaration_name
            },
            {
                'id': 'certification_name',
                'value': declaration.survey.certificate.title
            },
            {
                'id': 'organisation_name',
                'value': organisation.name
            },
            {
                'id': 'review_and_approve_submission',
                'value': urlparse.urljoin(
                    '{0}://{1}'.format(get_http_protocol_url_name(), Site.objects.get_current().domain),
                    declaration.survey.certificate.external_declaration_url
                )
            },
        ]
        api_template_email.delay(
            to_email=declaration.declaration_email,
            cc=declaration.declaration_secondary_email,
            subject='You have been requested to review and approve',
            template_id='d-08a3304f18d74a2c871e6c48fe7e27fb',
            substitutions=substitutions,
            custom_args=[
                {
                    'Name': 'Need Declaration Signed By The Correct Person',
                    'Category': 'Certifications',
                    'Environment': settings.ENVIRONMENT_NAME,
                    'Audience': organisation.get_email_audience(),
                }
            ],
            vodafone_customer=organisation.is_vodafone_customer,
            organisation_id=organisation.id,
        )


@task
def send_declaration_sent_out_for_signing(declaration_id):
    """
    Sends email notification when a declaration has been sent out for signing.
    :param declaration_id: The declaration id that is waiting to be signed
    :type declaration_id: Integer
    :return: nothing
    :rtype: None
    """
    declaration = SurveyDeclaration.objects.prefetch_related(
        'survey__certificate__organisation').get(id=declaration_id)
    organisation = declaration.survey.certificate.organisation
    body_text = _("Once declared, your submission will be reviewed by our assessors and some answers may be returned for clarification. This is to ensure your company meets IASME's strict standards")
    if declaration.survey.certificate.version.type.type == ESSENTIAL_EIGHT:
        body_text = _("Once signed, we will notify you and the signed status will be reflected in CyberSmart")
    for admin in organisation.get_admin_users:
        substitutions = [
            {
                'id': 'admin_name',
                'value': admin.first_name
            },
            {
                'id': 'certification_name',
                'value': declaration.survey.certificate.title
            },
            {
                'id': 'nominated_signer_name',
                'value': declaration.declaration_name
            },
            {
                'id': 'body_text',
                'value': body_text
            },
            {
                'id': 'organisation_name',
                'value': organisation.name
            }
        ]
        api_template_email.delay(
            to_email=admin.email,
            subject=f'Your {declaration.survey.certificate.title} has been sent for company approval',
            template_id='d-a1e72b510293479e80a1e3dec4a7f7e8',
            substitutions=substitutions,
            custom_args=[
                {
                    'Name': 'Your Declaration Has Been Sent Out To Be Signed',
                    'Category': 'Onboarding',
                    'Environment': settings.ENVIRONMENT_NAME,
                    'Audience': organisation.get_email_audience(),
                }
            ],
            vodafone_customer=organisation.is_vodafone_customer,
            unsubscribe_group=UNSUBSCRIBE_GROUPS['CERTIFICATES_COMPLETION'],
            organisation_id=organisation.id,
            permission=CERTIFICATES_AND_INSURANCE_PERMISSION,
        )


@task
def send_declaration_signing_reminder():
    """ Task that sends an email reminder if declaration has not been signed for over 2 week days """
    from pandas.tseries.offsets import BDay
    # get the date 2 business days ago
    date_before_48_hours = timezone.now() - BDay(2)
    declarations_ids = SurveyDeclaration.objects.filter(
        declaration_date__lte=date_before_48_hours,
        survey__certificate__status=OrganisationCertification.AWAITING_SIGNATURE,
        ready_to_be_signed=True
    ).exclude(
        declaration_email=None
    ).prefetch_related('survey__certificate').values_list('id', flat=True)
    for declaration_id in declarations_ids:
        send_declaration_signing_reminder_email.delay(declaration_id)


@task
def remove_trial_organisations():
    """
    Removes trial organisations and users if they didn't pay for more than 30 days.
    Does not remove orgs where customer made it to input their actual details during sign up.
    :return: nothing
    :rtype: None
    """
    older_than_30_days = timezone.now() - timedelta(days=30)
    organisations_to_remove = []
    users_to_remove = []
    for organisation in Organisation.objects.filter(
            is_trial=True, is_test=True, created__lt=older_than_30_days, customer__subscriptions__isnull=True,
            name__startswith='Company', email__isnull=True
    ):
        if organisation.main_app_user:
            try:
                users_to_remove.append(get_user_model().objects.get(
                    email__iexact=organisation.main_app_user.email, profile__is_trial=True
                ).pk)
            except get_user_model().DoesNotExist:
                pass
        users_to_remove.extend(list(organisation.get_admin_users.filter(profile__is_trial=True).values_list('id', flat=True)))
        organisations_to_remove.append(organisation.pk)

    if organisations_to_remove:
        Organisation.objects.filter(pk__in=list(set(organisations_to_remove))).delete()
        get_user_model().objects.filter(pk__in=list(set(users_to_remove))).delete()


@task(
    autoretry_for=(IntegrityError, WorkerLostError, SSLError)
)
def devices_cleaning():
    """
    Device cleaning feature task.
    Hides devices that didn't checked-in for a passed period of time set in org settings.
    """
    for org_settings in OrganisationSettings.objects.filter(devices_cleaning_enabled=True):

        devices = getattr(
            org_settings.organization, "non_mobile_devices"
        ) if not org_settings.devices_cleaning_include_mobile else getattr(
            org_settings.organization, "installed_devices"
        )

        allowed_check_in_date_margin = timezone.now() - timedelta(days=org_settings.devices_cleaning_days)
        devices.filter(reports__created__lt=allowed_check_in_date_margin).update(
            inactive=True, modified=timezone.now(), date_removed=timezone.now())


@task(
    autoretry_for=(IntegrityError, WorkerLostError, SSLError)
)
def generate_policy_document_pdf_copy(policy_version_id: int) -> None:
    """
    Generates a PDF copy of the policy document using LibreOffice or fitz as fallback.
    """
    try:
        policy_version = OrganisationPolicyVersion.objects.get(id=policy_version_id)
    except OrganisationPolicyVersion.DoesNotExist:
        logger.error(
            f"[Policy PDF Copy] Policy version with id '{policy_version_id}' does not exist.",
            extra={"policy_version_id": policy_version_id}
        )
        return

    document = policy_version.document
    pdf_copy = policy_version.pdf_copy
    if document and not pdf_copy:
        try:
            original_file_path = document.path
        except NotImplementedError:
            # if the document is stored in S3, we need to get the file path from the name
            original_file_path = document.name

        output_file_name = os.path.splitext(os.path.basename(original_file_path))[0] + ".pdf"
        output_file_path = os.path.join(os.path.dirname(original_file_path), output_file_name)

        try:
            # Download the file from S3 and save it to a temporary local file
            # local file needs to be used to be able to interact with libreoffice.
            with NamedTemporaryFile(delete=False, suffix=os.path.splitext(original_file_path)[1]) as temp_input_file:
                temp_input_path = temp_input_file.name
                with default_storage.open(original_file_path, 'rb') as source_file:
                    temp_input_file.write(source_file.read())

            # Create a temporary file for the output
            with NamedTemporaryFile(delete=False, suffix='.pdf') as temp_output_file:
                temp_output_path = temp_output_file.name

            tmp_pdf_file_path = os.path.splitext(temp_input_path)[0] + ".pdf"

            # Use subprocess to run LibreOffice command
            subprocess.run(['loffice', '--headless', '--convert-to', 'pdf', temp_input_path, '--outdir', os.path.dirname(tmp_pdf_file_path)], check=True)

            # Read the temporary PDF file contnt
            with open(tmp_pdf_file_path, 'rb') as pdf_file:
                pdf_content = ContentFile(pdf_file.read())

            # Save the PDF content to S3
            output_file_path = default_storage.save(output_file_path, pdf_content)

            # Clean up temporary input file
            if os.path.exists(temp_input_path):
                os.unlink(temp_input_path)
            if os.path.exists(tmp_pdf_file_path):
                os.unlink(tmp_pdf_file_path)

        except (subprocess.CalledProcessError, FileNotFoundError) as e:
            # Fallback to fitz if LibreOffice fails or is not available
            logger.error(f"Error converting file to PDF using LibreOffice: {e}")
            original_file_extension = os.path.splitext(original_file_path)[1].lower()
            try:
                with default_storage.open(original_file_path, 'rb') as file:
                    pdf_bytes = fitz.open(stream=file.read(), filetype=original_file_extension[1:]).convert_to_pdf()

                # Save the PDF bytes directly to the storage backend
                pdf_content = ContentFile(pdf_bytes)
                output_file_path = default_storage.save(output_file_path, pdf_content)
            except Exception as e:
                error_message = f"Error converting file '{original_file_path}' to PDF using fitz: {e}"
                logger.error(
                    error_message,
                    exc_info=True,
                    extra={
                        "organisation_id": policy_version.policy.organisation.id,
                        "policy_id": policy_version.policy.id,
                        "file_path": original_file_path
                    }
                )
                return

        finally:
            # Clean up the temporary file
            try:
                if 'temp_output_path' in locals():
                    os.unlink(temp_output_path)
                if 'temp_input_path' in locals():
                    os.unlink(temp_input_path)
            except Exception:
                pass

        # Update the policy version with the new PDF copy
        relative_output_path = output_file_path.replace(settings.MEDIA_ROOT + "/", "")
        policy_version.pdf_copy.name = relative_output_path
        policy_version.save(update_fields=["pdf_copy"])


@task
def cleanup_acceptance_test_orgs():
    """
    Delete acceptance test organizations in smaller chunks.
    """
    if settings.IS_PROD:
        logger.debug('Not running cleanup_acceptance_test_orgs in production')
        return
    if not (settings.IS_DEVELOP or settings.IS_STAGE):
        logger.debug('Not running cleanup_acceptance_test_orgs if not in develop or stage')
        return

    name_filter = Q(
        Q(name__startswith=settings.AUTO_TEST_ORG_PREFIX) |
        Q(name__startswith=settings.AUTO_TEST_TRUSTD_ORG_PREFIX)
    )

    while True:
        orgs_to_delete = Organisation.objects.filter(name_filter)[:10]
        if not orgs_to_delete.exists():
            break
        for org in orgs_to_delete:
            InsuranceOptIn.objects.filter(
                issued_certification__certificate__organisation=org
            ).delete()
            org.delete()
        # This delay was requested by the Trustd team
        time.sleep(1)


@task
def populate_organisation_with_fake_data(organisation_id: int, count: int):
    """
    Helper function to create app_installs and related models
    to enable creating larger fake organisations.
    """
    organisation = Organisation.objects.get(id=organisation_id)
    populator = OrganisationPopulator(organisation, count)
    populator.populate_data()
