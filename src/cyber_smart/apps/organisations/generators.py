import logging
import random
import string
from typing import List, Dict, Any, Tuple

from django.db.models import Count
from django.utils import timezone
from faker import Faker

from analytics.tasks import calculate_organisation_analytics
from api.common.serializers import AppUserReportSerializer
from appusers.models import AppInstall
from organisations.models import Organisation, OrganisationPolicy
from rulebook.models import OperatingSystem, AppCheck

logger = logging.getLogger(__name__)


class OrganisationFakeContentGenerator:
    """
    A class to generate fake content for an organisation, including policies, users, installs, and reports.
    """
    SOFTWARE_PACKAGES: List[dict] = [
        {"name": "Google Chrome", "vendor": "Google", "versions": [
            "89.0.4389.82", "91.0.4472.124", "92.0.4515.107", "94.0.4606.81"
        ]},
        {"name": "Mozilla Firefox", "vendor": "Mozilla", "versions": ["85.0", "86.0", "88.0", "89.0"]},
        {"name": "Microsoft Word", "vendor": "Microsoft", "versions": ["2016", "2019", "365", "2021"]},
        {"name": "Adobe Acrobat Reader", "vendor": "Adobe", "versions": [
            "2021.001.20145", "2020.013.20074", "2019.021.20061", "2018.011.20058"
        ]},
        {"name": "Slack", "vendor": "Slack Technologies", "versions": ["4.17.0", "4.18.0", "4.19.0", "4.20.0"]},
        {"name": "Spotify", "vendor": "Spotify Ltd.",
         "versions": ["1.1.55.498", "1.1.56.595", "1.1.57.443", "1.1.58.820"]},
        {"name": "Zoom", "vendor": "Zoom Video Communications", "versions": ["5.5.2", "5.6.1", "5.7.3", "5.8.6"]},
        {"name": "Visual Studio Code", "vendor": "Microsoft", "versions": ["1.55.0", "1.56.2", "1.57.1", "1.58.0"]},
        {"name": "7-Zip", "vendor": "Igor Pavlov", "versions": ["19.00", "20.00", "21.00", "21.01"]},
        {"name": "VLC Media Player", "vendor": "VideoLAN", "versions": ["********", "3.0.12", "3.0.14", "3.0.16"]},
        {"name": "Microsoft Excel", "vendor": "Microsoft", "versions": ["2016", "2019", "365", "2021"]},
        {"name": "Notepad++", "vendor": "Don Ho", "versions": ["7.8.9", "7.9.1", "7.9.5", "7.9.8"]},
        {"name": "PyCharm", "vendor": "JetBrains", "versions": ["2020.1", "2020.2", "2020.3", "2021.1"]},
        {"name": "IntelliJ IDEA", "vendor": "JetBrains", "versions": ["2020.1", "2020.2", "2020.3", "2021.1"]},
        {"name": "Eclipse", "vendor": "Eclipse Foundation", "versions": ["4.17", "4.18", "4.19", "4.20"]},
        {"name": "Postman", "vendor": "Postman, Inc.", "versions": ["7.32.0", "8.0.6", "8.6.2", "9.0.0"]},
        {"name": "Git", "vendor": "Software Freedom Conservancy", "versions": [
            "2.29.2", "2.30.0", "2.31.1", "2.32.0"
        ]},
        {"name": "Docker", "vendor": "Docker, Inc.", "versions": ["19.03.12", "20.10.2", "20.10.5", "20.10.7"]},
        {"name": "Skype", "vendor": "Microsoft", "versions": ["*********", "*********", "*********", "*********"]},
        {"name": "GIMP", "vendor": "The GIMP Development Team",
         "versions": ["2.10.22", "2.10.24", "2.10.28", "2.10.30"]},
        {"name": "LibreOffice", "vendor": "The Document Foundation", "versions": ["6.4.7", "7.0.4", "7.1.3", "7.2.0"]},
        {"name": "Thunderbird", "vendor": "Mozilla Foundation", "versions": ["78.6.1", "78.7.0", "78.10.0", "91.0.1"]},
        {"name": "Tor Browser", "vendor": "The Tor Project", "versions": ["10.0.10", "10.5.6", "10.5.10", "10.5.13"]},
        {"name": "WinRAR", "vendor": "win.rar GmbH", "versions": ["5.91", "6.00", "6.02", "6.10"]},
        {"name": "TeamViewer", "vendor": "TeamViewer AG", "versions": ["15.11.6", "15.12.4", "15.13.6", "15.14.5"]},
        {"name": "Microsoft PowerPoint", "vendor": "Microsoft", "versions": ["2016", "2019", "365", "2021"]},
        {"name": "Adobe Photoshop", "vendor": "Adobe", "versions": ["21.2.4", "22.0", "22.4.1", "22.5.1"]},
        {"name": "PyCharm", "vendor": "JetBrains", "versions": ["2020.1", "2020.2", "2020.3", "2021.1"]},
        {"name": "Opera", "vendor": "Opera Software", "versions": [
            "73.0.3856.257", "74.0.3911.203", "75.0.3969.171", "76.0.4017.177"
        ]},
        {"name": "Brave", "vendor": "Brave Software", "versions": ["1.19.86", "1.20.108", "1.21.77", "1.22.71"]},
        {"name": "Safari", "vendor": "Apple Inc.", "versions": ["14.0.1", "14.0.3", "14.1", "15.0"]},
        {"name": "Sublime Text", "vendor": "Sublime HQ", "versions": ["3.2.2", "4.0.0", "4.0.1", "4.1.0"]},
        {"name": "FileZilla", "vendor": "Tim Kosse", "versions": ["3.52.2", "3.53.1", "3.54.1", "3.55.0"]},
        {"name": "uTorrent", "vendor": "Rainberry, Inc.", "versions": [
            "3.5.5.45776", "3.5.5.45988", "3.5.5.46096", "3.5.5.46148"
        ]},
        {"name": "Discord", "vendor": "Discord Inc.", "versions": ["0.0.309", "0.0.310", "1.0.9003", "1.0.9004"]},
        {"name": "BitTorrent", "vendor": "Rainberry, Inc.", "versions": [
            "7.10.5.45665", "7.10.5.45785", "7.10.5.45857", "7.10.5.45967"
        ]},
        {"name": "BlueStacks", "vendor": "BlueStack Systems", "versions": [
            "4.240.20.1016", "4.250.0.1070", "5.0.0.7228", "5.1.0.1129"
        ]},
        {"name": "VirtualBox", "vendor": "Oracle Corporation", "versions": ["6.1.14", "6.1.16", "6.1.18", "6.1.22"]},
        {"name": "Cyberduck", "vendor": "iterate GmbH", "versions": ["7.7.2", "7.8.3", "7.9.0", "7.10.2"]},
        {"name": "KeePass", "vendor": "Dominik Reichl", "versions": ["2.45", "2.46", "2.47", "2.48"]},
        {"name": "Blender", "vendor": "Blender Foundation", "versions": ["2.82a", "2.83.5", "2.91.0", "2.92.0"]},
        {"name": "GOG Galaxy", "vendor": "GOG", "versions": ["2.0.36", "2.0.37", "2.0.38", "2.0.40"]},
        {"name": "Steam", "vendor": "Valve Corporation", "versions": [
            "2021-02-17", "2021-04-29", "2021-06-07", "2021-08-27"
        ]},
        {"name": "Audacity", "vendor": "The Audacity Team", "versions": ["2.4.2", "3.0.0", "3.0.2", "3.0.3"]},
        {"name": "HandBrake", "vendor": "HandBrake Team", "versions": ["1.3.3", "1.4.0", "1.4.1", "1.4.2"]},
        {"name": "Lightroom", "vendor": "Adobe", "versions": ["4.1", "4.2", "4.3", "4.4"]},
        {"name": "OBS Studio", "vendor": "OBS Project", "versions": ["25.0.8", "26.0.2", "27.0.1", "27.1.3"]},
        {"name": "Dropbox", "vendor": "Dropbox, Inc.",
         "versions": ["114.4.426", "116.4.368", "118.4.460", "120.4.4598"]},
        {"name": "OneDrive", "vendor": "Microsoft", "versions": [
            "20.201.1005.0009", "21.016.0124.0003", "21.062.0328.0001", "21.129.0627.0001"
        ]},
    ]

    OS_PLATFORMS: List[str] = ["win32", "darwin", "android", "ios"]

    OPERATING_SYSTEMS: Dict = {
        "win32": ["Windows 10", "Windows 8", "Windows 11"],
        "darwin": ["macOS Ventura", "macOS BigSur", "macOS Catalina"],
        "android": ["Android Nougat", "Android Oreo", "Android Pie", "Android Lolipop"],
        "ios": ["iOS 16", "15.0", "14.1", "iOS 13", "iOS 11", "iOS 12"]
    }

    VENDORS: List[str] = [
        "Apple", "Samsung", "Dell", "HP (Hewlett-Packard)", "Lenovo", "Asus", "Acer",
        "Microsoft", "Sony", "Toshiba", "Google", "LG", "Huawei", "Xiaomi", "OnePlus",
        "Oppo", "Vivo", "Motorola", "Nokia", "IBM", "HTC", "ZTE", "BlackBerry", "Alcatel",
        "Amazon", "Razer", "Realme", "Poco", "Honor", "Meizu", "Sharp", "Panasonic", "Wiko",
        "Fairphone", "Essential", "Nubia", "Redmi", "Cubot", "Caterpillar", "Ulefone",
        "Doogee", "Blackview"
    ]

    def __init__(
            self,
            organisation_id: str,
            policy_range: Tuple[int, int],
            app_user_range: Tuple[int, int],
            app_install_range: Tuple[int, int],
            app_install_min_secured_percentage: int = 50,
            faker: Faker = None
    ):
        """
        Initialize the generator with the organisation ID and ranges for policies, users, and installs.
        """
        self.organisation = Organisation.objects.get(secure_id=organisation_id)
        self.policy_range = policy_range
        self.app_user_range = app_user_range
        self.app_install_range = app_install_range
        self.app_install_min_secured_percentage = app_install_min_secured_percentage
        self.fake = faker or Faker()

    def create_policies(self) -> None:
        """
        Create fake policies for the organisation.
        """
        try:
            num_policies = random.randint(*self.policy_range)
            for _ in range(num_policies):
                policy_name = self.fake.catch_phrase()
                policy = self.organisation.policies.create(
                    name=policy_name,
                    active=True,
                )
                policy.versions.create(
                    version=self.fake.random_int(min=1, max=10),
                    active=True,
                    document_url=self.fake.uri(),
                    main=True
                )
            logger.info(f"Generated {num_policies} policies.")
        except Exception as e:
            logger.error(f"Error creating policies: {e}")
            raise

    def create_users(self) -> None:
        """
        Create fake CAP users for the organisation.
        """
        try:
            num_app_users = random.randint(*self.app_user_range)
            for _ in range(num_app_users):
                self.organisation.app_users.create(
                    first_name=self.fake.first_name(),
                    last_name=self.fake.last_name(),
                    email=self.fake.unique.email(),
                    active=True
                )
            logger.info(f"Generated {num_app_users} users.")
        except Exception as e:
            logger.error(f"Error creating users: {e}")
            raise

    def create_installs(self) -> None:
        """
        Create app installs for each user.
        """
        try:
            for user in self.organisation.enrolled_users:
                num_installs = random.randint(*self.app_install_range)
                for _ in range(num_installs):
                    platform = random.choice(self.OS_PLATFORMS)
                    os = None
                    while not os:
                        os = OperatingSystem.objects.filter(
                            title__in=self.OPERATING_SYSTEMS[platform]
                        ).order_by("?").first()
                    user.installs.create(
                        os=os,
                        hostname=self.fake.unique.hostname(),
                        caption=os.title,
                        platform=platform,
                        release="1.1.1",
                        os_release="1.1.1",
                        device_id=''.join(random.choice(string.ascii_letters) for _ in range(10)).lower(),
                        serial_number=''.join(random.choice(string.ascii_letters) for _ in range(10)).lower(),
                        system_info=''.join(random.choice(string.ascii_letters) for _ in range(10)).lower(),
                        app_version=f"1.{random.randint(0, 9)}.{random.randint(0, 9)}."
                                    f"{random.randint(0, 9)}",
                        machine_vendor=random.choice(self.VENDORS),
                        machine_model=random.choice(self.VENDORS),
                    )
            logger.info("Generated installs for users.")
        except Exception as e:
            logger.error(f"Error creating installs: {e}")
            raise

    def create_checks(self, app_install: AppInstall) -> List[Dict[str, Any]]:
        """
        Create random checks results for an app install.
        """
        check_status = True

        enrolled_count = self.organisation.installed_devices_count
        secured_count = self.organisation.secure_devices_count

        secured_percentage = (secured_count / enrolled_count) * 100 if enrolled_count > 0 else 0
        if secured_percentage > self.app_install_min_secured_percentage:
            check_status = False

        return [{
            "question_id": check.pk,
            "report_only": random.choice([True, False]),
            "response": check_status if check_status else random.choice([True, False]),
            "response_text": "response"
        } for check in AppCheck.objects.filter(
            commands__isnull=False, commands__os_id=app_install.os_id, active=True
        ).distinct()]

    @staticmethod
    def create_policy_report(app_install) -> List[Dict[str, Any]]:
        """
        Create a fake policy report for an app install.
        """
        read = random.choice([True, False])
        return [{
            "id": p.pk,
            "version_id": p.latest_version.pk,
            "name": p.name,
            "version": p.latest_version.version,
            "read": read,
            "agreed": False if not read else random.choice([True, False]),
            "link": p.latest_version.absolute_document_url,
            "date": str(timezone.now())
        } for p in OrganisationPolicy.objects.annotate(versions_count=Count('versions')).filter(
            organisation=app_install.app_user.organisation,
            active=True,
            versions_count__gt=0,
            versions__active=True,
        )]

    def create_installed_applications(self) -> List[Dict[str, Any]]:
        """
        Create fake installed local applications' data.
        """
        return [
            {
                "appname": software_package["name"],
                "appversion": random.choice(software_package["versions"]),
                "appinstalleddate": str(self.fake.date_this_decade()),
                "appvendor": software_package["vendor"],
                "mobileapp": random.choice([True, False]),
            } for software_package in random.sample(self.SOFTWARE_PACKAGES, random.randint(5, 30))
        ]

    def create_app_install_report(self, app_install) -> Dict[str, Any]:
        """
        Create a complete app install report.
        """
        return {
            "uuid": app_install.app_user.uuid,
            "app_version": app_install.app_version,
            "domain": "",
            "user": self.fake.unique.user_name(),
            "is_admin_account": random.choice([True, False]),
            "network_interfaces": [{
                "local_ip": self.fake.ipv4(),
                "public_ip": self.fake.ipv4(),
                "mac_address": self.fake.mac_address(),
                "gateway_ip": self.fake.ipv4(),
                "gateway_mac": self.fake.mac_address(),
                "cidr": self.fake.ipv4(),
            }],
            "policies": self.create_policy_report(app_install),
            "installed_applications": self.create_installed_applications(),
            "device_id": app_install.device_id,
            "serial_number": app_install.serial_number,
            "response": self.create_checks(app_install),
            "inapp_response": [],
            "systeminfo": app_install.system_info,
            "hostname": app_install.hostname,
            "os": {}
        }

    def create_reports(self) -> None:
        """
        Create app install reports for all devices and calculate analytics.
        """
        try:
            for device in self.organisation.installed_devices:
                report = self.create_app_install_report(device)
                serializer = AppUserReportSerializer(data=report)
                if serializer.is_valid():
                    serializer.create(serializer.validated_data)
            logger.info("Generated reports and calculated analytics.")
        except Exception as e:
            logger.error(f"Error creating reports: {e}")
            raise

    def create_all_data(self) -> None:
        """
        Create all fake content: policies, users, installs, and reports.
        """
        try:
            self.clear_existing_data()
            self.create_policies()
            self.create_users()
            self.create_installs()
            self.create_reports()
            logger.info("Generated all content.")
            calculate_organisation_analytics(self.organisation.pk)
        except Exception as e:
            logger.error(f"Error creating all content: {e}")
            raise

    def clear_existing_data(self) -> None:
        """
        Clear existing data for the organisation.
        """
        try:
            self.organisation.app_users.all().delete()
            self.organisation.app_installs.delete()
            self.organisation.policies.all().delete()
            logger.info("Cleared existing data.")
        except Exception as e:
            logger.error(f"Error clearing existing data: {e}")
            raise
