from django.urls import include
from django.urls import re_path as url
from django.views.decorators.csrf import csrf_exempt

from .views import (
    CertificatesView, DashboardAccess, DeprecatedDevicesView, DevicesView, DirectCustomerEnableCyberEssentials,
    InstalledSoftwareView, InsuranceView, OrganisationDevicesCSVReportView, PackageDetailsView, PatchHistoryView,
    PatchSummaryView, ToggleFeatureView, UsersAndGroups, PatchAttemptDetailsView,
    InstalledSoftwareExportDataView, PatchHistoryExportDataView, MultiSelectionPatchSummaryView
)

app_name = "organisations"

urlpatterns = [
    url(r'^toggle/feature/$', csrf_exempt(ToggleFeatureView.as_view()), name="toggle-feature"),
    url(r'^(?P<org_id>[0-9a-f-]{36})/',
        include([
            url(r'^devices/$', DevicesView.as_view(), name="devices"),
            url(r'^certificates/$', CertificatesView.as_view(), name="certificates"),
            # CSV generation views
            url(r'^devices-csv-report/$', OrganisationDevicesCSVReportView.as_view(), name='devices-csv-report'),
            url(r'^manage-users/', UsersAndGroups.as_view(), name='manage-users'),
            url(r'^dashboard-access/', DashboardAccess.as_view(), name='dashboard-access'),
            url(r'^cyber-insurance/$', InsuranceView.as_view(), name='cyber-insurance'),
            url(r'^deprecated-devices/$', DeprecatedDevicesView.as_view(), name='deprecated-devices'),
            url(
                r"^installed-software/report/$", InstalledSoftwareExportDataView.as_view(),
                name="installed-software-report"
            ),
            url(r"^installed-software/$", InstalledSoftwareView.as_view(), name="installed-software"),
            url(r"^patch-history/report/$", PatchHistoryExportDataView.as_view(), name="patch-history-report"),
            url(r"^patch-history/$", PatchHistoryView.as_view(), name="patch-history"),
            url(r"^package/(?P<package_id>[^/]+)/$", PackageDetailsView.as_view(), name="package-details"),
            url(r"^patch-summary/$", PatchSummaryView.as_view(), name="patch-summary"),
            url(
                r"^multi-selection-patch-summary/$", MultiSelectionPatchSummaryView.as_view(),
                name="multi-selection-patch-summary"
            ),
            url(
                r"^patch-installer/(?P<attempt_id>\d+)/$", PatchAttemptDetailsView.as_view(),
                name="patch-installer-details"
            ),
        ]),
        ),
    url(
        r"^enable-cyber-essentials/(?P<org_id>[0-9a-f-]+)/$",
        DirectCustomerEnableCyberEssentials.as_view(),
        name="enable-cyber-essentials"
    ),
]
