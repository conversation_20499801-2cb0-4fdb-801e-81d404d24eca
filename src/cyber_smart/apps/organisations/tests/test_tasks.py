from unittest.mock import MagicMock, patch, mock_open
import subprocess

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test import TestCase
from freezegun import freeze_time

from common.base_tests import BaseTestCase
from organisations.factories import OrganisationFactory, OrganisationAdminFactory
from organisations.models import Organisation, OrganisationAdmin, OrganisationPolicyVersion
from partners.models import Partner
from distributors.models import Distributor
from organisations.tasks import generate_policy_document_pdf_copy
from organisations.tasks import remove_trial_organisations


from django.test import override_settings
from django.utils import timezone

from ..tasks import cleanup_acceptance_test_orgs

User = get_user_model()


class TestRemoveTrialOrganisations(TestCase, BaseTestCase):
    def setUp(self):
        with freeze_time('2023-01-01'):
            self.org = OrganisationFactory(
                name='Company',
                email=None,
                is_trial=True, is_test=True
                )

    def test_do_not_remove_trial_organisation_admin_for_non_trial_user(self):
        '''
        Show that user which is not marked with is_trial is not removed.
        '''

        non_trial_user = User.objects.create(email='<EMAIL>')
        org_admin = OrganisationAdminFactory(organisation=self.org, user=non_trial_user)
        self.org.admins.add(org_admin)

        self.assertEqual(User.objects.count(), 1)
        self.assertEqual(OrganisationAdmin.objects.count(), 1)
        self.assertEqual(Organisation.objects.count(), 1)
        self.assertFalse(non_trial_user.profile.is_trial)

        remove_trial_organisations()

        self.assertEqual(OrganisationAdmin.objects.count(), 0)
        self.assertEqual(User.objects.count(), 1)
        self.assertEqual(Organisation.objects.count(), 0)

    def test_remove_trial_organisation_admin_for_trial_user(self):
        '''
        Show that user which is marked with is_trial is actually removed.
        '''
        trial_user = User.objects.create(email='<EMAIL>')
        trial_user.profile.is_trial = True
        trial_user.profile.save()
        org_admin = OrganisationAdminFactory(organisation=self.org, user=trial_user)
        self.org.admins.add(org_admin)

        self.assertEqual(User.objects.count(), 1)
        self.assertEqual(OrganisationAdmin.objects.count(), 1)
        self.assertEqual(Organisation.objects.count(), 1)
        self.assertTrue(trial_user.profile.is_trial)

        remove_trial_organisations()

        self.assertEqual(OrganisationAdmin.objects.count(), 0)
        self.assertEqual(User.objects.count(), 0)
        self.assertEqual(Organisation.objects.count(), 0)


class GeneratePolicyDocumentPDFCopyTests(TestCase):
    """
    Test case for generate_policy_document_pdf_copy task.
    """
    @patch("organisations.models.OrganisationPolicyVersion.objects.get")
    @patch("organisations.tasks.logger")
    def test_policy_version_does_not_exist(self, mock_logger, mock_get):
        mock_get.side_effect = OrganisationPolicyVersion.DoesNotExist
        generate_policy_document_pdf_copy(1)
        mock_logger.error.assert_called_with(
            "[Policy PDF Copy] Policy version with id '1' does not exist.",
            extra={"policy_version_id": 1}
        )

    @patch("organisations.models.OrganisationPolicyVersion.objects.get")
    def test_policy_version_exists_but_no_document(self, mock_get):
        policy_version = MagicMock()
        policy_version.document = None
        mock_get.return_value = policy_version
        generate_policy_document_pdf_copy(1)
        policy_version.save.assert_not_called()

    @patch("organisations.models.OrganisationPolicyVersion.objects.get")
    def test_document_is_already_in_pdf_format(self, mock_get):
        policy_version = MagicMock()
        policy_version.document.path = "/path/to/document.pdf"
        mock_get.return_value = policy_version
        generate_policy_document_pdf_copy(1)
        policy_version.save.assert_not_called()

    @patch("organisations.models.OrganisationPolicyVersion.objects.get")
    def test_document_exists_but_pdf_copy_already_generated(self, mock_get):
        policy_version = MagicMock()
        policy_version.document.path = "/path/to/document.pdf"
        policy_version.pdf_copy = MagicMock()
        mock_get.return_value = policy_version
        generate_policy_document_pdf_copy(1)
        policy_version.save.assert_not_called()

    @patch("organisations.models.OrganisationPolicyVersion.objects.get")
    @patch("organisations.tasks.default_storage.save")
    @patch("organisations.tasks.default_storage.open", new_callable=mock_open, read_data=b"document content")
    @patch("organisations.tasks.fitz.open")
    @patch("organisations.tasks.subprocess.run")
    def test_successful_pdf_conversion_using_fitz(self, mock_subprocess_run, mock_fitz_open, mock_storage_open, mock_storage_save, mock_get):
        policy_version = MagicMock()
        policy_version.document.path = "/path/to/document.docx"
        policy_version.pdf_copy = MagicMock()
        policy_version.pdf_copy.__bool__.return_value = False
        mock_get.return_value = policy_version

        mock_doc = MagicMock()
        mock_fitz_open.return_value = mock_doc
        mock_doc.convert_to_pdf.return_value = b"pdf_bytes"

        # Simulate LibreOffice failure to trigger fitz fallback
        mock_subprocess_run.side_effect = subprocess.CalledProcessError(1, 'cmd')

        generate_policy_document_pdf_copy(1)
        mock_storage_open.assert_called_with("/path/to/document.docx", "rb")
        mock_fitz_open.assert_called_once()

        actual_call_args = mock_storage_save.call_args

        actual_path_arg = actual_call_args[0][0]
        actual_content_arg = actual_call_args[0][1]
        pdf_path = "/path/to/document.pdf"

        self.assertEqual(actual_path_arg, pdf_path)
        self.assertEqual(actual_content_arg.read(), b"pdf_bytes")

        policy_version.pdf_copy.name = pdf_path
        policy_version.save.assert_called_once_with(update_fields=["pdf_copy"])


class TestCleanupAcceptanceTestOrgs(BaseTestCase, TestCase):
    databases = {'default', 'archive'}  # Allow the test to use the 'archive' database

    @classmethod
    def setUpTestData(cls):
        """Set up data that's shared across all test methods."""
        # Create distributors using bulk_create for better performance
        distributors = Distributor.objects.bulk_create([
            Distributor(name='CyberSmart'),
            Distributor(name='CyberSmart Direct'),
            Distributor(name='OtherDistributor'),
            Distributor(name='autoDistributor'),
            Distributor(name='Brigantia')
        ])

        cls.distributor_cybersmart = distributors[0]
        cls.distributor_cybersmart_direct = distributors[1]
        cls.distributor_other = distributors[2]
        cls.distributor_auto = distributors[3]
        cls.distributor_brigantia = distributors[4]

        # Create partners using bulk_create
        partners = Partner.objects.bulk_create([
            Partner(name='CyberSmart Partner', distributor=cls.distributor_cybersmart),
            Partner(name='CyberSmart Direct Partner', distributor=cls.distributor_cybersmart_direct),
            Partner(name='Other Partner', distributor=cls.distributor_other),
            Partner(name='auto Partner', distributor=cls.distributor_auto),
            Partner(name='Brigantia Partner', distributor=cls.distributor_brigantia)
        ])

        cls.partner_cybersmart = partners[0]
        cls.partner_cybersmart_direct = partners[1]
        cls.partner_other = partners[2]
        cls.partner_auto = partners[3]
        cls.partner_brigantia = partners[4]

    def setUp(self):
        self.year = timezone.now().year

        # Create minimal set of organizations needed for testing
        # We only need 2 of each prefix type to test the cleanup logic
        orgs_to_create = [
            # 2 AUTO_TEST_ORG_PREFIX organizations
            Organisation(
                name=f'{settings.AUTO_TEST_ORG_PREFIX}0',
                partner=self.partner_cybersmart
            ),
            Organisation(
                name=f'{settings.AUTO_TEST_ORG_PREFIX}1',
                partner=self.partner_cybersmart
            ),
            # 2 AUTO_TEST_TRUSTD_ORG_PREFIX organizations
            Organisation(
                name=f'{settings.AUTO_TEST_TRUSTD_ORG_PREFIX}0',
                partner=self.partner_brigantia
            ),
            Organisation(
                name=f'{settings.AUTO_TEST_TRUSTD_ORG_PREFIX}1',
                partner=self.partner_brigantia
            ),
            # Other test organizations
            Organisation(
                name=f'{settings.AUTO_TEST_TRUSTD_ORG_PREFIX}_cybersmart',
                partner=self.partner_cybersmart
            ),
            Organisation(
                name=f'{settings.AUTO_TEST_ORG_PREFIX}_direct',
                partner=self.partner_cybersmart_direct
            ),
            Organisation(
                name=f'{settings.AUTO_TEST_ORG_PREFIX}_not_to_delete',
                partner=self.partner_other
            ),
            Organisation(
                name=f'{settings.AUTO_TEST_ORG_PREFIX}_auto',
                partner=self.partner_auto
            ),
            Organisation(
                name='Company_not_to_delete_1',
                partner=self.partner_cybersmart_direct,
                is_trial=False,
                is_test=True
            ),
            Organisation(
                name='Company_not_to_delete_2',
                partner=self.partner_cybersmart_direct,
                is_trial=True,
                is_test=False
            ),
            Organisation(
                name='Company_not_to_delete_3',
                partner=self.partner_cybersmart_direct,
                is_trial=False,
                is_test=False
            ),
            Organisation(
                name=f'{settings.AUTO_TEST_TRUSTD_ORG_PREFIX}_not_to_delete',
                partner=self.partner_other
            )
        ]

        # Create organizations one by one to ensure signals fire properly
        for org in orgs_to_create:
            org.save()

    def test_cleanup_acceptance_test_orgs(self):
        self.assertEqual(Organisation.objects.count(), 12)  # 2+2+8 organizations

        with override_settings(IS_DEVELOP=True):
            cleanup_acceptance_test_orgs()

        self.assertEqual(Organisation.objects.count(), 3)

        self.assertFalse(Organisation.objects.filter(name__startswith=f'{settings.AUTO_TEST_TRUSTD_ORG_PREFIX}_not_to_delete').exists())
        self.assertFalse(Organisation.objects.filter(name__startswith=f'{settings.AUTO_TEST_ORG_PREFIX}_not_to_delete').exists())

        self.assertTrue(Organisation.objects.filter(name='Company_not_to_delete_1 [TEST]').exists())
        self.assertTrue(Organisation.objects.filter(name='Company_not_to_delete_2 [TEST]').exists())
        self.assertTrue(Organisation.objects.filter(name='Company_not_to_delete_3 [TEST]').exists())

    def test_does_not_delete_orgs_with_different_name(self):
        # Use class-level distributors and partners
        distributor_auto = Distributor.objects.filter(name='autoDistributor').first()
        if not distributor_auto:
            distributor_auto = Distributor.objects.create(name='autoDistributor2')
            partner_auto = Partner.objects.create(name='auto Partner 2', distributor=distributor_auto)
        else:
            partner_auto = Partner.objects.get(distributor=distributor_auto)

        # Create additional organizations one by one to ensure signals fire
        Organisation.objects.create(name='foo', partner=self.partner_cybersmart)
        Organisation.objects.create(name='foo_direct', partner=self.partner_cybersmart_direct)
        Organisation.objects.create(name='auto_foo', partner=partner_auto)
        Organisation.objects.create(name='random_name', partner=self.partner_other)
        Organisation.objects.create(name=f'{settings.AUTO_TEST_ORG_PREFIX}_foo_other', partner=self.partner_other)
        Organisation.objects.create(name=f'{settings.AUTO_TEST_TRUSTD_ORG_PREFIX}_other', partner=self.partner_other)

        self.assertEqual(Organisation.objects.count(), 18)  # 12 + 6 new ones

        with override_settings(IS_DEVELOP=True):
            cleanup_acceptance_test_orgs()

        self.assertEqual(Organisation.objects.count(), 7)

        self.assertTrue(Organisation.objects.filter(name='foo [TEST]').exists())
        self.assertTrue(Organisation.objects.filter(name='foo_direct [TEST]').exists())
        self.assertTrue(Organisation.objects.filter(name='auto_foo [TEST]').exists())
        self.assertTrue(Organisation.objects.filter(name='random_name [TEST]').exists())

        self.assertFalse(Organisation.objects.filter(name__startswith=f'{settings.AUTO_TEST_ORG_PREFIX}_foo_other').exists())
        self.assertFalse(Organisation.objects.filter(name__startswith=f'{settings.AUTO_TEST_TRUSTD_ORG_PREFIX}_other').exists())

    def test_does_not_run_in_production(self):
        self.assertEqual(Organisation.objects.count(), 12)

        with override_settings(IS_PROD=True):
            cleanup_acceptance_test_orgs()

        self.assertEqual(Organisation.objects.count(), 12)
