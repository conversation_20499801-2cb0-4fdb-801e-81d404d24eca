from unittest.mock import patch

from django.conf import settings
from django.test import TestCase
from factory.django import mute_signals

from common.signals import model_crud
from organisations.factories import RoleFactory
from organisations.models import DEFAULT_ROLE_NAME
from organisations.translation_utils import translate_default_role_name


@patch('organisations.translation_utils.DeeplTranslator')
@mute_signals(model_crud)
class TestTranslateDefaultRole(TestCase):

    def setUp(self):
        self.translation = "Translated Role"

    def test_translate_default_role_name(self, mock_deepl):
        mock_deepl_instance = mock_deepl.return_value
        mock_deepl_instance.translate.return_value = self.translation
        default_role = RoleFactory(name_en_gb=DEFAULT_ROLE_NAME)
        # roles partially translated
        not_translated_name = "Should not be translated"
        partially_translated_role = RoleFactory(
            name_en_gb=DEFAULT_ROLE_NAME,
            name_sv=not_translated_name,
        )
        partially_translated_role_2 = RoleFactory(
            name_en_gb=DEFAULT_ROLE_NAME,
            name_it=not_translated_name,
            name_sv=not_translated_name,
        )

        translate_default_role_name()

        # Check if the role has been translated into other languages
        default_role.refresh_from_db()
        partially_translated_role.refresh_from_db()
        partially_translated_role_2.refresh_from_db()
        for language_code, _ in settings.LANGUAGES:
            language_code = language_code.replace('-', '_')
            if language_code != 'en_gb':
                translated_name = getattr(default_role, f'name_{language_code}', None)
                self.assertEqual(translated_name, self.translation)
            # partially translated roles should have translations in languages other than the ones previously set
            if language_code not in ('en_gb', 'sv', 'it'):
                self.assertEqual(getattr(partially_translated_role, f'name_{language_code}', None), self.translation)
                self.assertEqual(getattr(partially_translated_role_2, f'name_{language_code}', None), self.translation)

        # partially translated role names should not be translated
        self.assertEqual(partially_translated_role.name_sv, not_translated_name)
        self.assertEqual(partially_translated_role_2.name_sv, not_translated_name)
        self.assertEqual(partially_translated_role_2.name_it, not_translated_name)

    def test_translate_non_default_role(self, mock_deepl):
        mock_deepl_instance = mock_deepl.return_value
        mock_deepl_instance.translate.return_value = self.translation
        # create non default role
        non_default_role = RoleFactory(name_en_gb="Another Role")

        for language_code, _ in settings.LANGUAGES:
            language_code = language_code.replace('-', '_')
            if language_code != 'en_gb':
                self.assertIsNone(getattr(non_default_role, f'name_{language_code}', None))

        translate_default_role_name()

        non_default_role.refresh_from_db()

        # Ensure that the non-default role is not translated
        for language_code, _ in settings.LANGUAGES:
            language_code = language_code.replace('-', '_')
            if language_code != 'en_gb':
                self.assertIsNone(getattr(non_default_role, f'name_{language_code}', None))
