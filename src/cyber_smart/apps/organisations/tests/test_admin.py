from unittest.mock import patch

from django.contrib.admin import AdminSite
from django.contrib.auth import get_user_model
from django.test import TestCase, RequestFactory
from django.urls import reverse

from appusers.models.factories import AppUserFactory, AppInstallFactory
from common.factories import GroupFactory
from organisations.admin import OrganisationModelAdmin
from organisations.factories import OrganisationPolicyFactory
from organisations.models import Organisation

User = get_user_model()


class OrganisationModelAdminTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_superuser(
            username='test user', email='<EMAIL>', password='test'
        )
        self.policy = OrganisationPolicyFactory()
        self.organisation = self.policy.organisation
        self.group_1 = GroupFactory(organisation=self.organisation)
        self.group_2 = GroupFactory(organisation=self.organisation)
        self.app_user = AppUserFactory(organisation=self.organisation, is_admin=True)
        self.app_user_2 = AppUserFactory(organisation=self.organisation)
        AppInstallFactory(app_user=self.app_user)
        AppInstallFactory(app_user=self.app_user_2)
        AppInstallFactory(app_user=AppUserFactory(organisation=self.organisation))
        # create an app install from another org
        AppInstallFactory(app_user=AppUserFactory())
        # add user to group
        self.app_user.groups.add(self.group_1)
        # add policy to group
        self.policy.groups.add(self.group_1)

        self.admin_site = AdminSite()
        self.admin = OrganisationModelAdmin(Organisation, self.admin_site)

        # Create a request factory
        self.factory = RequestFactory()
        self.data = {'change-deployment-type': True}
        self.change_url = reverse('admin:organisations_organisation_change', args=(self.organisation.pk,))

    def perform_change(self):
        with patch('organisations.admin.OrganisationModelAdmin.message_user'):
            request = self.factory.post(self.change_url, self.data)
            self.admin.change_deployment_type(request, self.organisation)

    def test_change_deployment_type(self):
        # check the deployment is individual
        self.assertFalse(self.organisation.bulk_install)
        # check the org has 2 groups
        self.assertEqual(self.organisation.groups.count(), 2)
        self.perform_change()
        self.organisation.refresh_from_db()
        self.assertTrue(self.organisation.bulk_install)
        # check the org has 0 groups
        self.assertEqual(self.organisation.groups.count(), 0)
        # check the app user and policy are no longer in the group
        self.assertFalse(self.app_user.groups.exists())
        self.assertFalse(self.organisation.groups.exists())
        # check all app installs were moved to the bulk deploy app user
        self.assertEqual(self.organisation.bulk_deploy_user.installs.count(), 3)

    def test_change_deployment_type_create_new_app_user(self):
        """ If the org does not have any app users, it should create one """
        # delete all app users
        self.organisation.app_users.all().delete()
        self.assertIsNone(self.organisation.main_app_user)
        self.perform_change()
        self.organisation.refresh_from_db()
        self.assertTrue(self.organisation.bulk_install)
        # check app user was created
        self.assertEqual(self.organisation.bulk_deploy_user.email, f'{Organisation.BULK_DEPLOY_PREFIX}{self.organisation.secure_id}@cybersmart.co.uk')

    def test_change_deployment_type_unique_constraint_violation(self):
        """ Test scenario where multiple app installs with same device_id and serial_number but with different app_users are created """
        # Create multiple app installs with the same device_id and serial_number but different app_users
        app_user_1 = AppUserFactory(organisation=self.organisation)
        app_user_2 = AppUserFactory(organisation=self.organisation)
        app_install_1 = AppInstallFactory(app_user=app_user_1, device_id='device_1', serial_number='serial_1')
        app_install_2 = AppInstallFactory(app_user=app_user_2, device_id='device_1', serial_number='serial_1')

        # Perform change and assert no change is done and correct error message is raised
        with patch('organisations.admin.OrganisationModelAdmin.message_user') as mock_message_user:
            request = self.factory.post(self.change_url, self.data)
            self.admin.change_deployment_type(request, self.organisation)
            self.organisation.refresh_from_db()
            self.assertFalse(self.organisation.bulk_install)
            mock_message_user.assert_called_once()
            # Validate that the error message contains the IDs of the violating app_install records
            error_message = mock_message_user.call_args[0][1]
            self.assertIn(str(app_install_1.id), error_message)
            self.assertIn(str(app_install_2.id), error_message)

    def submit_change_form(self, new_data):
        self.client.force_login(self.user)
        response = self.client.get(self.change_url)
        self.assertEqual(response.status_code, 200)

        form = response.context['adminform'].form
        post_data = form.initial.copy()
        post_data.update(new_data)
        post_data = {k: (v if v is not None else '') for k, v in post_data.items()}

        request = self.factory.post(self.change_url, post_data)
        request.user = self.user
        form = response.context['adminform'].form.__class__(data=post_data, instance=self.organisation)
        self.assertTrue(form.is_valid(), form.errors)
        self.admin.save_model(request, self.organisation, form, change=True)

    @patch.object(Organisation, 'get_billing', return_value=True)
    @patch('billing.tasks.update_chargebee_organisation_name.delay')
    def test_change_name(self, mock_update_chargebee_organisation_name, _):
        with patch('organisations.admin.OrganisationModelAdmin.message_user'):
            new_name = "Updated Organisation Name"
            self.assertNotEqual(new_name, self.organisation.name)
            self.submit_change_form({'name': new_name})

            self.organisation.refresh_from_db()
            self.assertEqual(self.organisation.name, new_name)
            mock_update_chargebee_organisation_name.assert_called_once_with(self.organisation.id)

    @patch.object(Organisation, 'get_billing', return_value=True)
    @patch('billing.tasks.update_chargebee_organisation_name.delay')
    def test_no_change_name(self, mock_update_chargebee_organisation_name, _):
        with patch('organisations.admin.OrganisationModelAdmin.message_user'):
            self.submit_change_form({})
            mock_update_chargebee_organisation_name.assert_not_called()
