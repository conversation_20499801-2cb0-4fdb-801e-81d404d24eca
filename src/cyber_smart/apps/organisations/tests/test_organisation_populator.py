from django.test import TestCase
from django.conf import settings
from django.contrib.auth.models import Group
from waffle.testutils import override_switch

from accounts.models import User
from organisations.factories import OrganisationFactory
from organisations.forms.create.organisation_populator import OrganisationPopulator
from organisations.models import Organisation, OrganisationAdmin
from opswat.models import ProductSignature, InstalledProductVersion, InstalledProduct, Product, ProductVersion, ProductVendor, ProductCategory, CVE
from appusers.models import <PERSON><PERSON><PERSON>nst<PERSON>, App<PERSON>nstallOSUser, AppUser, AppReport, UserLoginHistory, AppOSInstalledSoftware, OSEndOfLife, AppInstallCheckStatus
from appusers.models.checks import CheckResult
from analytics.models import AppInstallAnalytics, AppUserAnalytics
from rulebook.factories import AppCheckFactory
from rulebook.models import AppCheck
from beta_features.models import TRUSTD_BETA_SWITCH
from beta_features.utils import CAP_V_FIVE_BETA_PREFIX
from opswat_patch.models import OpswatScheduledProductInstaller, OpswatPatchEventLog, OpswatProductPatchInstaller, OpswatPatchJob, OpswatPatchAttempt
from opswat_patch.factories import OpswatPatchInstallerFactory, OpswatProductPatchInstallerFactory
from common.base_tests import BaseTestCase


class OrganisationPopulatorTestCase(BaseTestCase, TestCase):
    """Test the OrganisationPopulator class."""

    def setUp(self):
        """Set up test data."""
        # Set up core test data
        self.core_setting_up()
        self.creating_user()

        # Ensure we're not in production
        self.original_is_prod = settings.IS_PROD
        settings.IS_PROD = False

        # Create an organisation
        self.organisation = OrganisationFactory()

        # Create an admin user for the organisation (needed for creating scheduled installers)
        self.admin_user = User.objects.create(
            email=f"admin@{self.organisation.id}.test",
            username=f"admin_{self.organisation.id}"
        )
        self.admin_user.set_password("testpass123")
        self.admin_user.save()
        OrganisationAdmin.objects.create(
            user=self.admin_user,
            organisation=self.organisation
        )

        # Create app checks needed for the populator
        AppCheckFactory(active=True, order=0, platform_type=AppCheck.DESKTOP)
        AppCheckFactory(active=True, order=0, platform_type=AppCheck.MOBILE)

        # Create a small populator for testing
        self.populator = OrganisationPopulator(self.organisation, count=2)

    def tearDown(self):
        """Clean up after tests."""
        settings.IS_PROD = self.original_is_prod
        self.core_tearing_down()
        Organisation.objects.all().delete()

    def test_populate_data_creates_objects(self):
        """Test that populate_data creates the expected objects."""
        self.populator.populate_data()

        # Check that app users were created
        self.assertTrue(self.organisation.app_users.exists())

        # Check that app installs were created
        app_installs = AppInstall.objects.filter(app_user__organisation=self.organisation)
        self.assertTrue(app_installs.exists())

        # Check that installed products were created
        installed_products = InstalledProduct.objects.filter(
            app_install__app_user__organisation=self.organisation
        )
        self.assertTrue(installed_products.exists())

    def test_installed_product_versions_have_signatures(self):
        """Test that all installed product versions have signatures."""
        self.populator.populate_data()

        # Get all installed product versions
        installed_product_versions = InstalledProductVersion.objects.filter(
            installed_product__app_install__app_user__organisation=self.organisation
        )

        # Check that there are installed product versions
        self.assertTrue(installed_product_versions.exists())

        # Check that all installed product versions have signatures
        versions_without_signatures = installed_product_versions.filter(signature__isnull=True)
        self.assertEqual(versions_without_signatures.count(), 0,
                         f"Found {versions_without_signatures.count()} installed product versions without signatures")

        # Check that the signatures used are valid (have opswat_id)
        signatures = ProductSignature.objects.filter(
            installed_product_versions__installed_product__app_install__app_user__organisation=self.organisation
        ).distinct()

        self.assertTrue(signatures.exists())
        for signature in signatures:
            self.assertIsNotNone(signature.opswat_id)
            self.assertNotEqual(signature.opswat_id, "")

    def test_product_signatures_match_products(self):
        """Test that product signatures are correctly associated with their products."""
        self.populator.populate_data()

        # Test OPSWAT data creation
        self.assertTrue(ProductCategory.objects.filter(name="Browser").exists())
        self.assertTrue(ProductCategory.objects.filter(name="Antimalware").exists())

        self.assertTrue(ProductVendor.objects.filter(name="Google Inc.").exists())
        self.assertTrue(ProductVendor.objects.filter(name="Apple Inc.").exists())
        self.assertTrue(ProductVendor.objects.filter(name="TeamViewer GmbH").exists())

        chrome = Product.objects.filter(name="Google Chrome").first()
        self.assertIsNotNone(chrome)
        self.assertEqual(chrome.opswat_id, "39")
        self.assertEqual(chrome.vendor.name, "Google Inc.")

        # Verify TeamViewer product and signatures
        teamviewer = Product.objects.filter(name="TeamViewer").first()
        self.assertIsNotNone(teamviewer)
        self.assertEqual(teamviewer.opswat_id, "173")
        self.assertEqual(teamviewer.vendor.name, "TeamViewer GmbH")

        # Verify TeamViewer has multiple signatures
        teamviewer_signatures = teamviewer.signatures.all()
        self.assertGreaterEqual(teamviewer_signatures.count(), 3)

        # Verify specific TeamViewer signatures exist
        self.assertTrue(ProductSignature.objects.filter(name="TeamViewer", opswat_id="177").exists())
        self.assertTrue(ProductSignature.objects.filter(name="TeamViewer 11", opswat_id="3342").exists())
        self.assertTrue(ProductSignature.objects.filter(name="TeamViewer 12", opswat_id="3343").exists())

        itunes = Product.objects.filter(name="iTunes").first()
        self.assertIsNotNone(itunes)
        self.assertEqual(itunes.opswat_id, "415")
        self.assertEqual(itunes.vendor.name, "Apple Inc.")

        # Test product signatures
        chrome_signature = ProductSignature.objects.filter(name="Google Chrome").first()
        self.assertIsNotNone(chrome_signature)
        self.assertEqual(chrome_signature.opswat_id, "41")
        self.assertEqual(chrome_signature.product.name, "Google Chrome")

        itunes_signature = ProductSignature.objects.filter(name="iTunes").first()
        self.assertIsNotNone(itunes_signature)
        self.assertEqual(itunes_signature.opswat_id, "419")
        self.assertEqual(itunes_signature.product.name, "iTunes")

        chrome_version = ProductVersion.objects.filter(raw_version="133.0.6943.142").first()
        self.assertIsNotNone(chrome_version)
        self.assertEqual(chrome_version.major, 133)
        self.assertEqual(chrome_version.minor, 0)
        self.assertEqual(chrome_version.patch, 6943)

        apple_version = ProductVersion.objects.filter(raw_version="1.3.3").first()
        self.assertIsNotNone(apple_version)
        self.assertEqual(apple_version.major, 1)
        self.assertEqual(apple_version.minor, 3)

    def test_installed_product_versions_have_correct_signatures(self):
        """Test that installed product versions have the correct signatures."""
        self.populator.populate_data()

        # Test installed products
        installed_products = InstalledProduct.objects.filter(
            app_install__app_user__organisation=self.organisation
        )
        self.assertTrue(installed_products.exists())

        # Each OS user should have an installed product
        self.assertEqual(installed_products.count(), AppInstallOSUser.objects.filter(
            app_install__app_user__organisation=self.organisation
        ).count())

    def test_cap_v5_5_app_installs_have_teamviewer_with_correct_signature(self):
        """Test that app installs with CAP version >= 5.5.0 have TeamViewer with the correct signature."""
        # Create a populator - we don't need a large count since all CAP 5.5+ installs will have TeamViewer
        populator = OrganisationPopulator(self.organisation, count=5)
        populator.populate_data()

        # Get app installs with CAP version >= 5.5.0 that are not mobile
        cap_v5_5_app_installs = AppInstall.objects.filter(
            app_user__organisation=self.organisation,
            app_version__startswith='5.',
            device_type=AppInstall.DESKTOP
        )

        # Verify we have some app installs with CAP version >= 5.5.0
        self.assertTrue(cap_v5_5_app_installs.exists())

        # Find TeamViewer installations
        teamviewer_product = Product.objects.get(name="TeamViewer")
        teamviewer_v11_signature = ProductSignature.objects.get(name="TeamViewer 11")

        # Get installed product versions with TeamViewer and signature 11
        teamviewer_installations = InstalledProductVersion.objects.filter(
            installed_product__app_install__in=cap_v5_5_app_installs,
            product_version__product=teamviewer_product,
            signature=teamviewer_v11_signature
        )

        # Verify ALL CAP 5.5+ desktop app installs have TeamViewer with the correct signature
        self.assertEqual(teamviewer_installations.count(), cap_v5_5_app_installs.count(),
                       "All CAP v5.5+ desktop app installs should have TeamViewer with the correct signature")

    @override_switch(TRUSTD_BETA_SWITCH, active=True)
    def test_create_load_test_organisation(self):
        """
        Test the ability for users to create organisations with many app installs
        Tests OrganisationPopulator.
        """
        AppUser.objects.all().delete()
        AppInstall.objects.all().delete()
        AppCheckFactory(active=True, order=0, platform_type=AppCheck.DESKTOP)
        AppCheckFactory(active=True, order=0, platform_type=AppCheck.MOBILE)
        # Get the initial group count
        initial_group_count = Group.objects.count()
        self.assertEqual(Organisation.objects.count(), 1)

        # Create a populator with 10 users
        populator = OrganisationPopulator(self.organisation, count=10)
        populator.populate_data()

        # Test OPSWAT data creation
        self.assertTrue(ProductCategory.objects.filter(name="Browser").exists())
        self.assertTrue(ProductCategory.objects.filter(name="Antimalware").exists())

        self.assertTrue(ProductVendor.objects.filter(name="Google Inc.").exists())
        self.assertTrue(ProductVendor.objects.filter(name="Apple Inc.").exists())

        chrome = Product.objects.filter(name="Google Chrome").first()
        self.assertIsNotNone(chrome)
        self.assertEqual(chrome.opswat_id, "39")
        self.assertEqual(chrome.vendor.name, "Google Inc.")

        itunes = Product.objects.filter(name="iTunes").first()
        self.assertIsNotNone(itunes)
        self.assertEqual(itunes.opswat_id, "415")
        self.assertEqual(itunes.vendor.name, "Apple Inc.")

        # Test product signatures
        chrome_signature = ProductSignature.objects.filter(name="Google Chrome").first()
        self.assertIsNotNone(chrome_signature)
        self.assertEqual(chrome_signature.opswat_id, "41")
        self.assertEqual(chrome_signature.product.name, "Google Chrome")

        itunes_signature = ProductSignature.objects.filter(name="iTunes").first()
        self.assertIsNotNone(itunes_signature)
        self.assertEqual(itunes_signature.opswat_id, "419")
        self.assertEqual(itunes_signature.product.name, "iTunes")

        chrome_version = ProductVersion.objects.filter(raw_version="133.0.6943.142").first()
        self.assertIsNotNone(chrome_version)
        self.assertEqual(chrome_version.major, 133)
        self.assertEqual(chrome_version.minor, 0)
        self.assertEqual(chrome_version.patch, 6943)

        apple_version = ProductVersion.objects.filter(raw_version="1.3.3").first()
        self.assertIsNotNone(apple_version)
        self.assertEqual(apple_version.major, 1)
        self.assertEqual(apple_version.minor, 3)

        # Test installed products
        installed_products = InstalledProduct.objects.all()
        self.assertTrue(installed_products.exists())

        # Each OS user should have an installed product
        self.assertEqual(installed_products.count(), AppInstallOSUser.objects.count())

        # Each installed product should have a product version
        for installed_product in installed_products:
            self.assertTrue(installed_product.product_versions.exists())
            # If user has 100% pass rate, they should have iTunes installed
            # Otherwise they should have Google Chrome installed
            version = installed_product.product_versions.first()
            pass_percentage = installed_product.app_install.reports.first().pass_percentage

            # Check that the installed product version has a signature
            installed_product_version = InstalledProductVersion.objects.get(
                installed_product=installed_product,
                product_version=version
            )
            self.assertIsNotNone(installed_product_version.signature,
                                "Installed product version should have a signature")

            # Verify the signature matches the product
            self.assertEqual(installed_product_version.signature.product.id, version.product.id,
                            "Signature should be associated with the correct product")

            # Check for expected product names based on pass percentage and app version
            app_install = installed_product.app_install
            is_cap_v5_5_or_higher = False
            if app_install.app_version:
                version_parts = app_install.app_version.split('.')
                if len(version_parts) >= 2:
                    major = int(version_parts[0])
                    minor = int(version_parts[1]) if version_parts[1].isdigit() else 0
                    is_cap_v5_5_or_higher = (major > 5) or (major == 5 and minor >= 5)

            # For CAP 5.5+ desktop installs, expect TeamViewer
            if is_cap_v5_5_or_higher and app_install.device_type == AppInstall.DESKTOP:
                self.assertEqual(version.product.name, "TeamViewer")
                self.assertEqual(installed_product_version.signature.name, "TeamViewer 11")
                self.assertEqual(installed_product_version.signature.opswat_id, "3342")
            # For 100% pass rate (and not CAP 5.5+), expect iTunes
            elif pass_percentage == 100:
                self.assertEqual(version.product.name, "iTunes")
                self.assertEqual(installed_product_version.signature.name, "iTunes")
                self.assertEqual(installed_product_version.signature.opswat_id, "419")
            # For non-100% pass rate (and not CAP 5.5+), expect Google Chrome
            else:
                self.assertEqual(version.product.name, "Google Chrome")
                self.assertEqual(installed_product_version.signature.name, "Google Chrome")
                self.assertEqual(installed_product_version.signature.opswat_id, "41")

                # Test CVE data only for Google Chrome
                if version.product.name == "Google Chrome":
                    self.assertTrue(version.cves.exists())
                    cve = version.cves.first()
                    self.assertEqual(cve.cve_id, "CVE-2024-7971")
                    self.assertEqual(cve.severity, CVE.SEVERITY_IMPORTANT)
                    self.assertEqual(cve.severity_index, 80)
                    self.assertIn("Vulnerability in Google Chrome", cve.description)
                    self.assertEqual(
                        cve.details,
                        {
                            "references": [{"url": "https://chromereleases.googleblog.com/"}],
                        }
                    )

        # Test app install counts
        number_of_app_installs = AppInstall.objects.count()
        number_of_mobile_devices = AppInstall.objects.filter(device_type=AppInstall.MOBILE).count()

        # Verify app install counts
        self.assertGreaterEqual(number_of_app_installs, 20)  # At least 20 app installs
        self.assertGreaterEqual(number_of_mobile_devices, 10)  # At least 10 mobile devices
        self.assertGreaterEqual(AppInstall.objects.filter(device_id__startswith=CAP_V_FIVE_BETA_PREFIX).count(), 5)
        self.assertGreaterEqual(AppInstall.objects.filter(app_version__startswith='4').count(), 9)
        self.assertGreaterEqual(AppInstall.objects.filter(app_version__startswith='5').count(), 6)

        self.assertGreaterEqual(AppInstall.objects.filter(device_type=AppInstall.DESKTOP).distinct('app_user_id', 'real_device_id', 'serial_number').count(), 10)
        self.assertGreaterEqual(AppInstall.objects.filter(device_type=AppInstall.MOBILE).distinct('app_user_id', 'real_device_id', 'serial_number').count(), 10)

        # Enroll the organisation in the TRUSTD_BETA_SWITCH feature
        self.enroll_in_feature(self.organisation, TRUSTD_BETA_SWITCH)
        self.assertGreaterEqual(len([x for x in AppInstall.objects.filter(device_type=AppInstall.MOBILE) if x.is_trustd_app]), 5)

        # Verify counts of related objects
        self.assertEqual(AppReport.objects.count(), number_of_app_installs)
        self.assertEqual(AppUser.objects.count(), 10)
        self.assertEqual(AppInstallOSUser.objects.count(), number_of_app_installs)
        self.assertEqual(UserLoginHistory.objects.count(), number_of_app_installs)

        # Check that at least one group exists
        self.assertGreaterEqual(Group.objects.count(), initial_group_count)

        # The number of check results may vary depending on the number of app checks
        self.assertGreaterEqual(CheckResult.objects.count(), number_of_app_installs)

        self.assertEqual(AppInstallAnalytics.objects.count(), number_of_app_installs)
        self.assertGreaterEqual(AppInstallCheckStatus.objects.count(), number_of_app_installs)
        self.assertEqual(AppUserAnalytics.objects.count(), 10)
        self.assertGreaterEqual(AppOSInstalledSoftware.objects.count(), 20)
        self.assertGreaterEqual(OSEndOfLife.objects.count(), 20)


    def test_create_load_test_organisation_twice(self):
        """
        To check for any integrity errors, which could happen if objects with
        same unique fields would be created.
        """
        # Create first organization with test data
        populator1 = OrganisationPopulator(self.organisation, count=10)
        populator1.populate_data()

        # Check that signatures were created for the first organization
        self.assertTrue(ProductSignature.objects.filter(name="Google Chrome").exists())
        self.assertTrue(ProductSignature.objects.filter(name="iTunes").exists())
        self.assertTrue(ProductSignature.objects.filter(name="TeamViewer").exists())
        self.assertTrue(ProductSignature.objects.filter(name="TeamViewer 11").exists())
        self.assertTrue(ProductSignature.objects.filter(name="TeamViewer 12").exists())

        # Check that all installed product versions have signatures
        for ipv in InstalledProductVersion.objects.all():
            self.assertIsNotNone(ipv.signature, "All installed product versions should have signatures")
            self.assertEqual(ipv.signature.product.id, ipv.product_version.product.id,
                            "Signature should be associated with the correct product")

        # Create a second organization with the same test data
        org2 = OrganisationFactory()
        populator2 = OrganisationPopulator(org2, count=10)
        populator2.populate_data()

    def test_event_logs_created_for_multiple_products(self):
        """Test that event logs are created for various products, not just TeamViewer."""
        # Create a populator with more users to increase chances of event logs
        populator = OrganisationPopulator(self.organisation, count=20)
        populator.populate_data()

        # Get all scheduled installers
        scheduled_installers = OpswatScheduledProductInstaller.objects.filter(
            app_install__app_user__organisation=self.organisation
        )

        if scheduled_installers.exists():
            # Get all event logs
            event_logs = OpswatPatchEventLog.objects.filter(
                opswat_scheduled_product_installer__in=scheduled_installers
            )

            # Collect products that have event logs
            products_with_logs = {}
            for installer in scheduled_installers:
                if installer.opswat_product_patch_installer and installer.opswat_product_patch_installer.product:
                    product = installer.opswat_product_patch_installer.product
                    logs = event_logs.filter(opswat_scheduled_product_installer=installer)
                    if logs.exists():
                        products_with_logs[product.name] = logs.count()

            # We should have event logs for at least one product
            self.assertGreater(len(products_with_logs), 0,
                              f"Should have event logs for at least one product. Found: {list(products_with_logs.keys())}")


    def test_all_cap_v5_5_devices_with_patch_software_have_event_logs(self):
        """Test that all CAP 5.5.0+ devices with patch software have event logs."""
        # Create a populator with multiple users
        populator = OrganisationPopulator(self.organisation, count=10)
        populator.populate_data()

        # Get CAP 5.5.0+ desktop installs
        cap_v5_5_installs = AppInstall.objects.filter(
            app_user__organisation=self.organisation,
            app_version__gte='5.5.0',
            device_type=AppInstall.DESKTOP
        )

        # Should have some CAP 5.5.0+ installs
        self.assertGreater(cap_v5_5_installs.count(), 0,
                          "Should have at least one CAP 5.5.0+ install")

        # Check for any scheduled installers and event logs
        all_scheduled_installers = OpswatScheduledProductInstaller.objects.filter(
            app_install__in=cap_v5_5_installs
        )

        all_event_logs = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer__app_install__in=cap_v5_5_installs
        )

        # If patch installer associations exist in the database, we should have event logs
        # Note: Event logs are only created for products that have patch installer associations
        if OpswatProductPatchInstaller.objects.exists():
            self.assertGreater(all_event_logs.count(), 0,
                              "Should have at least some event logs for CAP 5.5.0+ installs when patch associations exist")

            # Check which products have event logs
            products_with_logs = set()
            for installer in all_scheduled_installers:
                if installer.opswat_product_patch_installer and installer.opswat_product_patch_installer.product:
                    logs = all_event_logs.filter(opswat_scheduled_product_installer=installer)
                    if logs.exists():
                        products_with_logs.add(installer.opswat_product_patch_installer.product.name)

    def test_creates_patch_job_attempt_when_scheduled_installer(self):
        temp_populator = OrganisationPopulator(self.organisation, count=1)
        temp_populator.populate_data()

        # Create patch installer for TeamViewer to enter the condition
        # with created scheduled product installer
        teamviewer_product = Product.objects.get(name="TeamViewer")
        patch_installer = OpswatPatchInstallerFactory(
            product_name="TeamViewer",
            latest_version="15.0.0",
            opswat_id="teamviewer_patch_123"
        )
        product_patch_installer = OpswatProductPatchInstallerFactory(
            product=teamviewer_product,
            patch_installer=patch_installer
        )
        teamviewer_signature = ProductSignature.objects.get(name="TeamViewer 11", opswat_id="3342")
        product_patch_installer.signatures.add(teamviewer_signature)

        populator = OrganisationPopulator(self.organisation, count=1)
        populator.populate_data()
        self.assertEqual(OpswatPatchJob.objects.count(), 1)
        self.assertEqual(OpswatPatchAttempt.objects.count(), 1)

    def test_event_logs_created_for_cap_v5_5_installs(self):
        """Test that event logs are created for CAP 5.5.0+ installations."""
        # Create a populator with more users to increase chances of event logs
        populator = OrganisationPopulator(self.organisation, count=10)
        populator.populate_data()

        # Get CAP 5.5.0+ installs
        cap_v5_5_installs = AppInstall.objects.filter(
            app_user__organisation=self.organisation,
            app_version__gte='5.5.0',
            device_type=AppInstall.DESKTOP
        )

        # Should have some CAP 5.5.0+ installs
        self.assertGreater(cap_v5_5_installs.count(), 0,
                          "Should have at least one CAP 5.5.0+ install")

        # Check for scheduled installers
        scheduled_installers = OpswatScheduledProductInstaller.objects.filter(
            app_install__app_user__organisation=self.organisation
        )

        # Should have some scheduled installers (not all installs get them due to randomness)
        if scheduled_installers.exists():
            # Check that event logs were created
            event_logs = OpswatPatchEventLog.objects.filter(
                opswat_scheduled_product_installer__in=scheduled_installers
            )

            self.assertGreater(event_logs.count(), 0,
                              "Should have at least one event log for scheduled installers")

            # Check that event logs have various statuses
            statuses = set(event_logs.values_list('status', flat=True))
            self.assertGreaterEqual(len(statuses), 1,
                                   "Event logs should have at least one status")

            # Check that some event logs have details
            logs_with_details = event_logs.exclude(details_public_facing='')
            self.assertGreater(logs_with_details.count(), 0,
                              "Some event logs should have public-facing details")

            # Check that scheduled installers have appropriate final statuses
            for installer in scheduled_installers:
                self.assertIsNotNone(installer.status,
                                    "Scheduled installer should have a status")

                # If there are event logs, the installer status should match the last one
                installer_logs = event_logs.filter(
                    opswat_scheduled_product_installer=installer
                ).order_by('-created')

                if installer_logs.exists():
                    last_log = installer_logs.first()
                    self.assertEqual(installer.status, last_log.status,
                                    "Installer status should match the last event log status")

            # Check that we have event logs for various products (not just TeamViewer)
            products_with_logs = set()
            for installer in scheduled_installers:
                if installer.opswat_product_patch_installer and installer.opswat_product_patch_installer.product:
                    product_name = installer.opswat_product_patch_installer.product.name
                    if event_logs.filter(opswat_scheduled_product_installer=installer).exists():
                        products_with_logs.add(product_name)

