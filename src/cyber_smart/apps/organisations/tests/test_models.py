from __future__ import unicode_literals

from datetime import datetime

from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth import get_user_model
from django.contrib.sites.models import Site
from django.shortcuts import reverse
from django.test import TestCase
from django.test.utils import override_settings
from django.utils import timezone
from freezegun import freeze_time
from mock import PropertyMock
from mock import patch

from analytics.factories import AppUserAnalyticsFactory
from appusers.models.factories import (
    AppInstallFactory, AppUserFactory, SoftwarePackageCVEsFactory,
    SoftwarePackageFactory, AppOSInstalledSoftwareFactory, AppReportFactory
)
from beta_features.factories import BetaFeatureFactory, BetaEnrolmentFactory, DesktopBetaAppInstallFactory
from beta_features.models import BETA_FEATURES_REQUIRING_SOFTWARE_SUPPORT
from billing.factories import (
    DirectCustomerFactory, DirectSubscriptionFactory, PartnerSubscriptionFactory,
    PartnerBillingFactory
)
from billing.models import DirectSubscription, PartnerSubscription
from billing.providers.chargebee import plans, SUBSCRIPTION_ACTIVE
from insurance.factories import SuperscriptOptInFactory, InsuranceOptInFactory
from insurance.models import InsuranceOptIn
from organisations.factories import (
    OrganisationFactory,
    OrganisationCertificationAuditFactory
)
from partners.factories import PartnerUserFactory
from rulebook.factories import (
    CertificationSurveyFactory, IssuedCertificationFactory
)
from trustd.factories import TrustdDeviceFactory

try:
    from urlparse import urljoin
except ImportError:
    from urllib.parse import urljoin

from waffle.testutils import override_switch
from beta_features.models import TRUSTD_BETA_SWITCH
from beta_features.utils import CAP_V_FIVE_BETA_VERSION, CAP_V_FIVE_BETA_PREFIX
from analytics.models import AppUserAnalytics, OrganisationAnalytics
from appusers.models import AppInstall, AppUser, AppReport, CheckResult, AppVersion
from appusers.models.factories import AppVersionFactory
from analytics.tasks import calculate_app_report
from appusers.utils import generate_app_registration_token
from beta_features.models import CAP_V_FIVE_BETA_SWITCH
from common.base_tests import BaseTestCase
from common.tests.utils import random_version
from common.models import StaticFiles
from distributors.factories import DistributorFactory
from distributors.models import Distributor
from partners.factories import PartnerFactory
from partners.models import Partner
from insurance.insurers.sutcliffe import SUTCLIFFE_SWITCH
from common.models import ProjectSettings
from organisations.models import (
    Organisation, OrganisationAdmin, OrganisationCertification, Role, get_organisations, DEFAULT_ROLE_NAME,
)
from organisations.factories import OrganisationCertificationFactory, CertificationVersionFactory, OrganisationPolicyVersionFactory
from rulebook.models import (
    CertificationVersion, AppCheck, IssuedCertification, SurveyDeclaration, CertificationSurvey, SurveyResponse,
    SurveyQuestion, CYBER_ESSENTIALS
)
from vulnerabilities.factories import CVERepositoryFactory


class OrganisationTestCase(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.creating_user()
        self.app_user_2 = AppUser.objects.create(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
        )
        self.app_user_3 = AppUser.objects.create(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
        )
        # create a new version
        self.new_version = CertificationVersion.objects.create(
            type=self.cert_type,
            version_number=2.0,
            default=True,
            release_date=timezone.now().date()
        )
        self.org_cert = self.organisation.certifications.first()

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        AppUser.objects.all().delete()

    def test_create(self):
        data = {
            'name': 'New Org Inc.',
            'industry': 'OVER',
            'size': Organisation.ORGANISATION_SIZE_1,
            'email_message': 'hello from the other side',
            'bulk_install': False,
            'showmodal': True,
            'security_emails_frequency': 'daily',
            'partner': self.direct_partner
        }
        new_org = Organisation.objects.create(
            **data
        )
        self.assertIn(new_org.name, [data['name'], data['name'] + ' [TEST]'])
        self.assertEqual(new_org.industry, data['industry'])
        self.assertEqual(new_org.size, data['size'])
        self.assertEqual(new_org.email_message, data['email_message'])
        self.assertEqual(new_org.bulk_install, data['bulk_install'])
        self.assertEqual(new_org.showmodal, data['showmodal'])
        self.assertEqual(new_org.security_emails_frequency, data['security_emails_frequency'])

    def test_url(self):
        self.assertEqual(
            self.organisation.url, reverse('dashboard:organisation', kwargs={'org_id': self.organisation.secure_id})
        )

    def test_absolute_url(self):
        self.assertEqual(
            self.organisation.absolute_url,
            urljoin(
                'http://{0}'.format(Site.objects.get_current().domain),
                reverse('dashboard:organisation', kwargs={'org_id': self.organisation.secure_id})
            )
        )

    def test_appusers_url(self):
        self.assertEqual(
            self.organisation.appusers_url, reverse(
                'dashboard:add-users', kwargs={'org_id': self.organisation.secure_id}
            )
        )

    def test_checkreport_url(self):
        self.assertEqual(
            self.organisation.checkreport_url,
            urljoin(
                'http://{0}'.format(Site.objects.get_current().domain),
                reverse('dashboard:check_report', kwargs={'org_id': self.organisation.secure_id})
            )
        )

    def test_bulkreport_url(self):
        self.assertEqual(
            self.organisation.bulkreport_url, reverse(
                'dashboard:bulk_check_report', kwargs={'org_id': self.organisation.secure_id}
            )
        )

    def test_resend_email_to_user_url(self):
        self.assertEqual(
            self.organisation.resend_email_to_user_url, reverse(
                'api-v2:send-app', kwargs={'org_id': self.organisation.secure_id}
            )
        )

    def test_manageorganisation_url(self):
        self.assertEqual(
            self.organisation.manageorganisation_url, reverse(
                'dashboard:manage-organisation', kwargs={'org_id': self.organisation.secure_id}
            )
        )

    def test_get_all_users(self):
        self.assertEqual(self.organisation.get_all_users.count(), 0)
        OrganisationAdmin.objects.create(
            organisation=self.organisation,
            user=self.user
        )
        self.assertEqual(self.organisation.get_all_users.count(), 1)
        self.new_user = get_user_model().objects.create_user(
            username='test', email='<EMAIL>',
            password='pisword'
        )
        OrganisationAdmin.objects.create(
            organisation=self.organisation,
            user=self.new_user,
            is_admin=True
        )
        self.assertEqual(self.organisation.get_all_users.count(), 2)

    def test_get_admin_users(self):
        self.assertEqual(self.organisation.get_admin_users.count(), 0)
        OrganisationAdmin.objects.create(
            organisation=self.organisation,
            user=self.user
        )
        self.assertEqual(self.organisation.get_admin_users.count(), 0)
        self.new_user = get_user_model().objects.create_user(
            username='test', email='<EMAIL>',
            password='pisword'
        )
        OrganisationAdmin.objects.create(
            organisation=self.organisation,
            user=self.new_user,
            is_admin=True
        )
        self.assertEqual(self.organisation.get_admin_users.count(), 1)

    def test_get_organisation_creator(self):
        self.assertEqual(self.organisation.get_organisation_creator, self.organisation.get_admin_users.first())

    def test_get_admin_emails(self):
        self.assertIn(self.organisation.get_admin_emails, ([], None))
        OrganisationAdmin.objects.create(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        )
        self.assertEqual(self.organisation.get_admin_emails, [self.user.email])
        self.new_user = get_user_model().objects.create_user(
            username='test', email='<EMAIL>',
            password='pisword'
        )
        OrganisationAdmin.objects.create(
            organisation=self.organisation,
            user=self.new_user,
            is_admin=True
        )
        self.assertIn(self.user.email, self.organisation.get_admin_emails)
        self.assertIn(self.new_user.email, self.organisation.get_admin_emails)

    def test_desktop_devices(self):
        # create mobile app install
        AppInstall.objects.create(
            app_user=self.app_user,
            os=self.operating_system,
            hostname='asdfasdfsde',
            caption='asdfasdf',
            platform='android',
            release='asdfasdfsdf',
            device_id='asdfasdfsdsf',
            inactive=False,
            device_type=AppInstall.MOBILE,
            registration_unique_token=generate_app_registration_token()
        )
        self.assertEqual(self.organisation.desktop_devices_count, 1)

    @override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
    @override_switch(TRUSTD_BETA_SWITCH, active=True)
    def test_installed_devices(self):
        self.assertEqual(self.organisation.installed_devices_count, 1)
        new_install = AppInstall.objects.create(
            app_user=self.app_user,
            os=self.operating_system,
            hostname='asdfasdf',
            caption='asdfasdf',
            platform='asdfasdf',
            release='asdfasdf',
            device_id='asdfasdf',
            inactive=True,
            registration_unique_token=generate_app_registration_token()
        )
        self.assertEqual(self.organisation.installed_devices_count, 1)
        self.app_user.active = False
        self.app_user.save()
        self.assertEqual(self.organisation.installed_devices_count, 0)
        self.app_user.active = True
        self.app_user.save()
        self.assertEqual(self.organisation.installed_devices_count, 1)
        new_install.inactive = False
        new_install.save()
        self.assertEqual(self.organisation.installed_devices_count, 2)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        AppInstallFactory(app_user=self.app_user, inactive=False, app_version='5.1.0')
        trustd_install = AppInstallFactory(
            app_user=self.app_user,
            serial_number='123456',
            inactive=False,
            app_version='3.2.1',
            registration_unique_token=generate_app_registration_token()
        )
        trustd_install.trustd_device = TrustdDeviceFactory(app_install=trustd_install)
        trustd_install.save()
        self.assertEqual(self.organisation.installed_devices_count, 4)
        self.assertEqual(self.organisation.installed_beta_apps_count, 2)

    def test_all_app_installs(self):
        self.assertEqual(self.organisation.app_installs.count(), 1)
        AppInstall.objects.create(
            app_user=self.app_user,
            os=self.operating_system,
            hostname='asdfasdf',
            caption='asdfasdf',
            platform='asdfasdf',
            release='asdfasdf',
            device_id='asdfasdf',
            inactive=True,
            registration_unique_token=generate_app_registration_token()
        )
        self.assertEqual(self.organisation.app_installs.count(), 2)

    def test_get_all_certification(self):
        self.assertEqual(self.organisation.get_all_certification.count(), 1)
        self.organisation.certifications.create(
            version=self.cert_version_cep
        )
        self.assertEqual(self.organisation.get_all_certification.count(), 2)

    def test_get_latest_certification(self):
        self.assertEqual(self.organisation.get_latest_certificate().version, self.cert_version)

    def test_get_pass_percentage(self):
        self.assertEqual(self.organisation.get_pass_percentage(), 0)
        self.new_app_user = AppUserFactory(organisation=self.organisation, is_admin=True)
        self.new_app_install = AppInstallFactory(app_user=self.new_app_user, os=self.operating_system)
        AppUserAnalyticsFactory(appuser=self.app_user, pass_percentage=70)
        AppUserAnalyticsFactory(appuser=self.new_app_user, pass_percentage=30)

        self.assertEqual(self.organisation.get_pass_percentage(), 50)
        self.app_user.active = False
        self.app_user.save()
        self.assertEqual(self.organisation.get_pass_percentage(), 30)
        self.app_user.active = True
        self.app_user.save()
        self.assertEqual(self.organisation.get_pass_percentage(), 50)
        self.new_app_install.inactive = True
        self.new_app_install.save()
        self.assertEqual(self.organisation.get_pass_percentage(), 70)
        beta_app_user = AppUserFactory(organisation=self.organisation)
        DesktopBetaAppInstallFactory(app_user=beta_app_user)
        AppUserAnalyticsFactory(appuser=beta_app_user, pass_percentage=100)
        self.assertEqual(self.organisation.get_pass_percentage(), 85)
        with (override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)):
            self.enable_cap_v5_and_trustd_beta(self.organisation)
            self.assertEqual(self.organisation.get_pass_percentage(), 70)
            AppInstallFactory(app_user=beta_app_user, app_version='4.1.0')
            self.assertEqual(self.organisation.get_pass_percentage(), 85)

    def test_percentage(self):
        self.new_organisation = Organisation.objects.create(
            name='Ara ORG',
            industry='CONS',
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=self.direct_partner
        )
        self.assertEqual(self.new_organisation.percentage, 0)
        self.new_app_user = AppUser.objects.create(
            organisation=self.new_organisation,
            email='<EMAIL>',
            first_name='gsdfg',
            last_name='sdgsdfg',
            is_admin=True
        )
        self.new_app_install = AppInstall.objects.create(
            app_user=self.new_app_user,
            os=self.operating_system,
            hostname='Host Name',
            caption='Caption',
            platform='Platform',
            release='0.01',
            device_id='44dfdd14-e3de-1111-8c11-c4c364a2dcbc',
            registration_unique_token=generate_app_registration_token()
        )
        self.new_install_report = AppReport.objects.create(
            app_install=self.new_app_install,
            pass_percentage=70
        )
        checks = []
        for i in range(0, 10):
            q = AppCheck.objects.create(
                order=i,
                code='123{0}'.format(i),
                qtype='API',
                title='asdf',
                tooltip='asdf'
            )
            checks.append(q)

        for i in range(0, 10):
            if i <= 6:
                CheckResult.objects.create(
                    report=self.new_install_report,
                    app_check=checks[i],
                    response=True
                )
            else:
                CheckResult.objects.create(
                    report=self.new_install_report,
                    app_check=checks[i],
                    response=False
                )
        calculate_app_report(self.new_install_report.id)
        self.assertEqual(self.new_organisation.percentage, 70)

    def test_is_passing(self):
        self.new_organisation = Organisation.objects.create(
            name='Ara ORG',
            industry='CONS',
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=self.direct_partner
        )
        OrganisationAnalytics.objects.create(
            organisation=self.new_organisation,
            pass_percentage=100
        )
        self.assertEqual(self.new_organisation.is_passing, True)

    def test_pass_percentage_is_null(self):
        self.new_organisation = Organisation.objects.create(
            name='Ara ORG',
            industry='CONS',
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=self.direct_partner
        )
        self.analytics = OrganisationAnalytics.objects.create(
            organisation=self.new_organisation,
            pass_percentage=None
        )
        self.assertEqual(0, self.analytics.get_pass_percentage_int())

    def create_certs(self):
        self.org_cert.status = OrganisationCertification.EXPIRING
        self.org_cert.save()
        self.survey_1 = CertificationSurveyFactory(certificate=self.org_cert, passed_percent=95)
        self.cert_2 = OrganisationCertificationFactory(
            organisation=self.organisation,
            version=self.new_version,
            status=OrganisationCertification.CERTIFIED
        )
        self.survey_2 = CertificationSurveyFactory(certificate=self.cert_2, passed_percent=50)

    def test_cert_pass_percentage(self):
        """ When a expiring cert has passed the renewal end date (expired),
        since new certified cert exists then it will be 100 """
        self.create_certs()
        pass_percent = self.organisation.get_certification_pass_percentage()
        self.assertEqual(pass_percent, 100)

    def test_cert_pass_percentage_one_cert(self):
        """ When there is only 1 cert """
        self.org_cert.status = OrganisationCertification.IN_SURVEY
        self.org_cert.save()
        self.survey_1 = CertificationSurveyFactory(certificate=self.org_cert, passed_percent=56)
        pass_percent = self.organisation.get_certification_pass_percentage()
        self.assertEqual(self.org_cert.progress_percentage, 56)
        self.assertEqual(pass_percent, 56)

    def test_cert_pass_percentage_expiring_in_survey(self):
        """ When an expiring cert is still valid, even with another cert created
         then passed percent is 100 """
        self.create_certs()
        self.cert_2.status = OrganisationCertification.IN_SURVEY
        self.cert_2.save()
        with freeze_time("2021-07-25 08:21"):
            self.org_cert.renewal_end_date = timezone.now().date()
            self.org_cert.save()
        # time before renewal end date
        with freeze_time("2021-05-25 08:21"):
            self.organisation.refresh_from_db()
            pass_percent = self.organisation.get_certification_pass_percentage()
            self.assertEqual(self.org_cert.progress_percentage, 100)
            self.assertEqual(self.cert_2.progress_percentage, 50)
            self.assertEqual(pass_percent, 100)

    def test_cert_pass_percentage_expiring_in_survey_more_certs(self):
        """ When a expiring cert is still valid, even with another cert created
         then passed percent is 100 """
        self.create_certs()
        self.org_cert.status = OrganisationCertification.EXPIRED
        self.org_cert.save()
        self.cert_2.status = OrganisationCertification.EXPIRING
        self.cert_2.save()
        self.cert_3 = OrganisationCertificationFactory(
            organisation=self.organisation,
            version=CertificationVersion.objects.create(
                type=self.cert_type,
                version_number=3.0,
                default=True,
                release_date=timezone.now().date()
            ),
            status=OrganisationCertification.IN_SURVEY
        )
        self.survey_3 = CertificationSurveyFactory(certificate=self.cert_3, passed_percent=50)
        with freeze_time("2021-07-25 08:21"):
            self.cert_2.renewal_end_date = timezone.now().date()
            self.cert_2.save()
        # time before renewal end date
        with freeze_time("2021-05-25 08:21"):
            pass_percent = self.organisation.get_certification_pass_percentage()
            self.assertEqual(self.org_cert.progress_percentage, 0)
            self.assertEqual(self.cert_2.progress_percentage, 100)
            self.assertEqual(self.cert_3.progress_percentage, 50)
            self.assertEqual(pass_percent, 100)

    def test_cert_pass_percentage_expiring_expired(self):
        """ When a expiring cert has passed the renewal end date (expired), even with another cert created
        then passed percent is 0, since no new certified cert exists """
        self.create_certs()
        self.cert_2.status = OrganisationCertification.IN_SURVEY
        self.cert_2.save()
        with freeze_time("2021-07-25 08:21"):
            self.org_cert.renewal_end_date = timezone.now().date()
            self.org_cert.save()
        # time after renewal end date
        with freeze_time("2021-08-25 08:21"):
            pass_percent = self.organisation.get_certification_pass_percentage()
            self.assertEqual(self.org_cert.progress_percentage, 0)
            self.assertEqual(self.cert_2.progress_percentage, self.survey_2.passed_percent)
            self.assertEqual(pass_percent, self.survey_2.passed_percent)

    def test_cert_pass_percentage_expired(self):
        """ When previous cert has expired and new cert is not certified, then it is 0 """
        self.create_certs()
        self.org_cert.status = OrganisationCertification.EXPIRED
        self.org_cert.save()
        self.survey_2.passed_percent = 20
        self.survey_2.save()
        self.cert_2.status = OrganisationCertification.IN_SURVEY
        self.cert_2.save()
        pass_percent = self.organisation.get_certification_pass_percentage()
        self.assertEqual(self.org_cert.progress_percentage, 0)
        self.assertEqual(self.cert_2.progress_percentage, 20)
        self.assertEqual(pass_percent, 20)

    def test_cert_pass_percentage_expired_certified(self):
        """ When previous cert has expired and new cert is certified, then it is 100 """
        self.create_certs()
        self.org_cert.status = OrganisationCertification.EXPIRED
        self.org_cert.save()
        self.cert_2.status = OrganisationCertification.CERTIFIED
        self.cert_2.save()
        pass_percent = self.organisation.get_certification_pass_percentage()
        self.assertEqual(self.org_cert.progress_percentage, 0)
        self.assertEqual(self.cert_2.progress_percentage, 100)
        self.assertEqual(pass_percent, 100)

    def test_cert_pass_percentage_expired_certified_diff_types(self):
        """ several types """
        self.create_certs()
        org_cert = OrganisationCertificationFactory(
            organisation=self.organisation,
            version=self.cert_version_cep,
            status=OrganisationCertification.EXPIRING
        )
        CertificationSurveyFactory(certificate=org_cert, passed_percent=95)
        org_cert_2 = OrganisationCertificationFactory(
            organisation=self.organisation,
            version=CertificationVersion.objects.create(
                type=self.cert_type_cep,
                version_number=2.0,
                default=True,
                release_date=timezone.now().date()
            ),
            status=OrganisationCertification.CERTIFIED
        )
        CertificationSurveyFactory(certificate=org_cert_2, passed_percent=95)
        pass_percent = self.organisation.get_certification_pass_percentage()
        self.assertEqual(pass_percent, 100)

    def test_cert_pass_percentage_expired_diff_types(self):
        """ several types but one of them is expired without new certified version """
        self.create_certs()
        org_cert = OrganisationCertificationFactory(
            organisation=self.organisation,
            version=self.cert_version_cep,
            status=OrganisationCertification.EXPIRING
        )
        CertificationSurveyFactory(certificate=org_cert, passed_percent=95)
        org_cert_2 = OrganisationCertificationFactory(
            organisation=self.organisation,
            version=CertificationVersion.objects.create(
                type=self.cert_type_cep,
                version_number=2.0,
                default=True,
                release_date=timezone.now().date()
            ),
            status=OrganisationCertification.EXPIRED
        )
        CertificationSurveyFactory(certificate=org_cert_2, passed_percent=95)
        pass_percent = self.organisation.get_certification_pass_percentage()
        self.assertEqual(pass_percent, 50)

    def test_cert_pass_percentage_expired_in_survey_diff_types(self):
        """ several types but one of them is expired without new version in survey"""
        self.create_certs()
        org_cert = OrganisationCertificationFactory(
            organisation=self.organisation,
            version=self.cert_version_cep,
            status=OrganisationCertification.EXPIRED
        )
        CertificationSurveyFactory(certificate=org_cert, passed_percent=95)
        org_cert_2 = OrganisationCertificationFactory(
            organisation=self.organisation,
            version=CertificationVersion.objects.create(
                type=self.cert_type_cep,
                version_number=2.0,
                default=True,
                release_date=timezone.now().date()
            ),
            status=OrganisationCertification.IN_SURVEY
        )
        CertificationSurveyFactory(certificate=org_cert_2, passed_percent=50)
        pass_percent = self.organisation.get_certification_pass_percentage()
        self.assertEqual(pass_percent, 75)

    def test_max_cap_users_exceeded(self):
        """ Test that if max cap users was already exceeded, then the extra app users ID list will
         be returned to hide their details in the front-end """
        self.organisation.max_cap_users = 1
        self.organisation.save()
        app_users = self.organisation.get_app_users()
        extra_app_users_id = self.organisation.get_extra_app_users_id(app_users)
        self.assertEqual(len(extra_app_users_id), 1)

    def test_max_cap_users_exceeded_by_more_than_1(self):
        """ Test that if max cap users was already exceeded, then the extra app users ID list will
         be returned to hide their details in the front-end """
        # create extra 4 users
        for i in range(4, 8):
            AppUser.objects.create(
                organisation=self.organisation,
                email=f'app_user_{i}@example.co',
                first_name='First Name AppUser',
                last_name='Last Name AppUser',
            )
        self.organisation.max_cap_users = 2
        self.organisation.save()
        app_users = self.organisation.get_app_users()
        extra_app_users_id = self.organisation.get_extra_app_users_id(app_users)
        # if max cap is 2, and we already have 2 app users, then the extra is 4
        self.assertEqual(len(extra_app_users_id), 4)

    def test_max_cap_users_not_exceeded(self):
        """ Test that if max cap users has not been reached (even if equal to max),
        then the extra app users ID list will be empty """
        self.organisation.max_cap_users = 2
        self.organisation.save()
        app_users = self.organisation.get_app_users()
        extra_app_users_id = self.organisation.get_extra_app_users_id(app_users)
        self.assertEqual(len(extra_app_users_id), 0)

    def test_max_cap_users_is_none(self):
        """ Test that if org has no max cap users, then the extra app users ID list will be empty """
        app_users = self.organisation.get_app_users()
        extra_app_users_id = self.organisation.get_extra_app_users_id(app_users)
        self.assertEqual(len(extra_app_users_id), 0)

    def test_max_cap_users_is_zero(self):
        """ Test that if org has max cap users of zero, then it is exceeded """
        self.organisation.max_cap_users = 0
        self.organisation.save()
        app_users = self.organisation.get_app_users()
        extra_app_users_id = self.organisation.get_extra_app_users_id(app_users)
        self.assertEqual(len(extra_app_users_id), 2)

    def test_new_certification_months(self):
        """ If org was certified and no new version has been started,
        then nr months left for automatic renewal is shown, also the passed nr of months
        after certification should be the expected """
        with freeze_time("2021-07-25 08:21"):
            self.org_cert.renewal_start_date = timezone.now().date().replace(year=2022)
            self.org_cert.save()
            IssuedCertificationFactory(certificate=self.org_cert, date=timezone.now())
        self.org_cert.refresh_from_db()
        # automatic creation happens 11 months after the issued date, which takes it to "2022-06-25 08:21"
        # since we check this in "2022-02-25 08:21", we still have 4 months left to migrate
        with freeze_time("2022-02-25 08:21"):
            self.assertEqual(self.org_cert.new_certification_months_for_migration, 4)

    def test_new_certification_months_none(self):
        """ If org has not been certified yet, then it should be None """
        self.assertIsNone(self.org_cert.new_certification_months_for_migration)

    def test_new_certification_months_longer_than_1_year(self):
        """ If org was certified and no new version has been started,
         but it has already been over 1 year, then we should get None
         But we will always get the certification passed months.
         """
        with freeze_time("2021-07-25 08:21"):
            self.org_cert.renewal_start_date = timezone.now().date().replace(year=2022)
            self.org_cert.save()
            IssuedCertificationFactory(certificate=self.org_cert, date=timezone.now())
        with freeze_time("2022-08-25 08:21"):
            self.assertIsNone(self.org_cert.new_certification_months_for_migration)

    def get_app_user_data(self, i):
        return {
            'organisation': self.organisation,
            'email': f'app_user_{i}@example.co',
            'first_name': f'{i}',
            'last_name': f'{i}',
            'active': True
        }

    def get_app_install_data(self, i, app_user):
        return {
            'app_user': app_user,
            'os': self.operating_system,
            'device_id': f'44dfdd14-e3de-4013-9c42_{i}',
            'registration_unique_token': generate_app_registration_token()
        }

    def get_app_report_data(self, app_install):
        return {
            'app_install': app_install,
            'total_responses': 2,
            'pass_percentage': 100
        }

    def create_data(self):
        for i in range(2):
            # active and passing apps
            app_user = AppUser.objects.create(**self.get_app_user_data(i))
            app_install = AppInstallFactory(**self.get_app_install_data(i, app_user))
            AppReport.objects.create(**self.get_app_report_data(app_install))
            AppUserAnalytics.objects.create(pass_percentage=100.0, appuser=app_user)
            # inactive apps
            data = self.get_app_install_data(f'{i}_inactive', app_user)
            data['inactive'] = True
            app_install = AppInstallFactory(**data)
            AppReport.objects.create(**self.get_app_report_data(app_install))

        # inactive app user
        data = self.get_app_user_data('8')
        data['active'] = False
        app_user = AppUser.objects.create(**data)
        app_install = AppInstallFactory(**self.get_app_install_data('8', app_user))
        AppReport.objects.create(**self.get_app_report_data(app_install))
        AppUserAnalytics.objects.create(pass_percentage=100.0, appuser=app_user)
        # no total responses
        app_user = AppUser.objects.create(**self.get_app_user_data('6'))
        app_install = AppInstallFactory(**self.get_app_install_data('6', app_user))
        data = self.get_app_report_data(app_install)
        data['total_responses'] = None
        AppReport.objects.create(**data)
        AppUserAnalytics.objects.create(pass_percentage=0, appuser=app_user)
        # no reports and not analytics
        app_user = AppUser.objects.create(**self.get_app_user_data('5'))
        AppInstallFactory(**self.get_app_install_data('5', app_user))

        # active and passing multiple apps for same user (multiple_app_installs)
        app_user = AppUser.objects.create(**self.get_app_user_data('9'))
        app_install = AppInstallFactory(**self.get_app_install_data('9', app_user))
        AppReport.objects.create(**self.get_app_report_data(app_install))
        app_install = AppInstallFactory(**self.get_app_install_data('10', app_user))
        AppReport.objects.create(**self.get_app_report_data(app_install))
        AppUserAnalytics.objects.create(pass_percentage=100.0, appuser=app_user)

        # active and passing only 90
        app_user = AppUser.objects.create(**self.get_app_user_data('11'))
        app_install = AppInstallFactory(**self.get_app_install_data('11', app_user))
        data = self.get_app_report_data(app_install)
        data['pass_percentage'] = 90
        AppReport.objects.create(**data)
        AppUserAnalytics.objects.create(pass_percentage=data['pass_percentage'], appuser=app_user)

        # devices from another org
        org = OrganisationFactory(
            industry='CONS',
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=self.direct_partner,
            software_support=True
        )
        data = self.get_app_user_data('12')
        data['organisation'] = org
        app_user = AppUser.objects.create(**data)
        app_install = AppInstallFactory(**self.get_app_install_data('12', app_user))
        AppReport.objects.create(**self.get_app_report_data(app_install))
        AppUserAnalytics.objects.create(pass_percentage=100.0, appuser=app_user)

    def test_secure_devices_count(self):
        self.create_data()
        self.assertEqual(self.organisation.secure_devices_count, 4)

    def test_insecure_devices_count(self):
        self.create_data()
        self.assertEqual(self.organisation.insecure_devices_count, 1)

    def test_insecure_devices_no_duplicate_pk(self):
        app_install_1 = AppInstallFactory(app_user=self.app_user)

        AppReportFactory(app_install=app_install_1, total_responses=5)
        AppReportFactory(app_install=app_install_1, total_responses=50)

        insecure_devices = self.organisation.insecure_devices
        pks = list(insecure_devices.values_list('pk', flat=True))

        self.assertEqual(len(pks), len(set(pks)), "There are duplicate primary keys in the insecure devices query")

    def test_secure_devices_no_duplicate_pk(self):
        app_install_1 = AppInstallFactory(app_user=self.app_user)

        AppReportFactory(app_install=app_install_1, total_responses=5, pass_percentage=100)
        AppReportFactory(app_install=app_install_1, total_responses=50, pass_percentage=100)

        secure_devices = self.organisation.secure_devices
        pks = list(secure_devices.values_list('pk', flat=True))

        self.assertEqual(len(pks), len(set(pks)), "There are duplicate primary keys in the secure devices query")
        self.assertEqual(len(pks), 1)

    def test_secure_devices_without_duplicates(self):
        app_install_1 = AppInstallFactory(app_user=self.app_user)
        app_install_2 = AppInstallFactory(app_user=self.app_user, device_id=f'{CAP_V_FIVE_BETA_PREFIX}{app_install_1.device_id}', real_device_id=app_install_1.real_device_id, serial_number=app_install_1.serial_number)

        AppReportFactory(app_install=app_install_1, total_responses=5, pass_percentage=100)
        AppReportFactory(app_install=app_install_1, total_responses=50, pass_percentage=100)
        AppReportFactory(app_install=app_install_2, total_responses=5, pass_percentage=100)
        AppReportFactory(app_install=app_install_2, total_responses=50, pass_percentage=100)

        secure_devices = self.organisation.secure_devices
        pks = list(secure_devices.values_list('pk', flat=True))

        self.assertEqual(len(pks), 1)


    def test_installed_devices_count(self):
        self.create_data()
        self.assertEqual(self.organisation.installed_devices_count, 8)

    def test_secure_users_count(self):
        self.create_data()
        # should be 3 instead of the 4 in test_secure_devices_count
        # because of the app installs for the same user search for multiple_app_installs
        self.assertEqual(self.organisation.secure_users_count, 3)

    def test_insecure_users_count(self):
        self.create_data()
        self.assertEqual(self.organisation.insecure_users_count, 1)

    def test_installed_users(self):
        self.create_data()
        # should be 7 instead of the 8 in test_secure_devices_count
        # because of the app installs for the same user search for multiple_app_installs
        self.assertEqual(self.organisation.installed_users_count, 7)

    def test_device_owners_count(self):
        self.create_data()
        self.assertEqual(self.organisation.device_owners_count, 7)

    def test_get_subscriptions_queryset_direct(self):
        DirectSubscriptionFactory(customer__organisation=self.organisation)
        subscription = self.organisation.get_subscriptions_queryset().first()
        self.assertTrue(isinstance(subscription, DirectSubscription))

    def test_get_subscriptions_queryset_non_direct(self):
        non_direct_org = OrganisationFactory()
        billing = PartnerBillingFactory(partner=non_direct_org.partner)
        PartnerSubscriptionFactory(organisation=non_direct_org, billing=billing)
        subscription = non_direct_org.get_subscriptions_queryset().first()
        self.assertTrue(isinstance(subscription, PartnerSubscription))

    def test_get_subscriptions_queryset_no_billing(self):
        PartnerSubscriptionFactory(organisation=self.organisation)
        self.assertIsNone(self.organisation.get_subscriptions_queryset())

    def test_get_enrolled_beta_features(self):
        beta_feature = BetaFeatureFactory(kind=CAP_V_FIVE_BETA_SWITCH)
        BetaEnrolmentFactory(feature=beta_feature, partner = self.direct_partner, organisations=[self.organisation])
        with override_switch(CAP_V_FIVE_BETA_SWITCH, active=True):
            self.assertEqual(self.organisation.get_enrolled_beta_features().count(), 1)
            self.assertEqual(self.organisation.get_enrolled_beta_features().first(), beta_feature)
        with override_switch(CAP_V_FIVE_BETA_SWITCH, active=False):
            self.assertEqual(self.organisation.get_enrolled_beta_features().count(), 0)

    @override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
    @override_switch(TRUSTD_BETA_SWITCH, active=True)
    def test_get_total_packages_count(self):
        cve_repository = CVERepositoryFactory(cve_id="CVE-2020-1234")
        non_beta_install = AppInstallFactory(
            app_user=self.app_user,
            inactive=False,
            app_version="4.9.1",
            serial_number="123456",
        )
        non_beta_report = AppReportFactory(app_install=non_beta_install)

        beta_install = AppInstallFactory(
            app_user=self.app_user,
            inactive=False,
            app_version="5.1.0",
            serial_number="654321",
        )
        beta_report = AppReportFactory(app_install=beta_install)

        packages = [
            SoftwarePackageFactory(),
            SoftwarePackageFactory(vendor="microsoft", product="teams"),
            SoftwarePackageFactory(vendor="microsoft", product="office"),
        ]
        AppOSInstalledSoftwareFactory(report=non_beta_report, software=packages[0])
        AppOSInstalledSoftwareFactory(report=non_beta_report, software=packages[1])
        AppOSInstalledSoftwareFactory(report=beta_report, software=packages[2])
        AppOSInstalledSoftwareFactory(report=beta_report, software=packages[1])
        software_package_cve = SoftwarePackageCVEsFactory(software=packages[2])
        software_package_cve.cves_fk.add(cve_repository)
        self.assertEqual(self.organisation.total_packages_count, 3)
        self.assertEqual(self.organisation.beta_app_install_packages_count, 0)
        self.assertEqual(self.organisation.vulnerable_packages_count, 1)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.total_packages_count, 2)
        self.assertEqual(self.organisation.beta_app_install_packages_count, 2)
        self.assertEqual(self.organisation.vulnerable_packages_count, 0)

    def test_main_app_user(self):
        self.assertEqual(self.organisation.main_app_user, self.app_user)
        self.app_user.active = False
        self.app_user.save()
        self.assertEqual(self.organisation.main_app_user, self.app_user_2)

    def test_can_upgrade_to_100k_r_and_r_toolbox_insurance_direct_customer(self):
        self.assertTrue(self.organisation.can_upgrade_to_100k_r_and_r_toolbox_insurance)
        with override_settings(IS_AUS_GEO=True):
            self.assertFalse(self.organisation.can_upgrade_to_100k_r_and_r_toolbox_insurance)

        self.organisation.superscript_product_code = Organisation.CE_100K
        self.organisation.save()
        self.assertTrue(self.organisation.can_upgrade_to_100k_r_and_r_toolbox_insurance)

        customer = DirectCustomerFactory(organisation=self.organisation)
        DirectSubscriptionFactory(customer=customer, plan_id=plans.DIRECT_R_AND_R_TOOLBOX_MONTHLY_PLAN, status=SUBSCRIPTION_ACTIVE)
        self.org_cert.status = OrganisationCertification.CERTIFIED
        self.org_cert.save()
        SuperscriptOptInFactory(certification=self.org_cert)
        self.assertFalse(self.organisation.can_upgrade_to_100k_r_and_r_toolbox_insurance)

    def test_can_upgrade_to_100k_r_and_r_toolbox_insurance_partner_customer(self):
        non_direct_org = OrganisationFactory()
        self.assertFalse(non_direct_org.can_upgrade_to_100k_r_and_r_toolbox_insurance)

    def test_is_eligible_for_r_and_r_toolbox_direct_customer(self):
        self.assertTrue(self.organisation.is_eligible_for_r_and_r_toolbox)
        with override_settings(IS_AUS_GEO=True):
            self.assertFalse(self.organisation.is_eligible_for_r_and_r_toolbox)

    def test_is_eligible_for_r_and_r_toolbox_partner_customer(self):
        non_direct_org = OrganisationFactory()
        self.assertFalse(non_direct_org.is_eligible_for_r_and_r_toolbox)

    def test_can_upgrade_to_software_bundle(self):
        fresh_org = OrganisationFactory()
        self.assertTrue(fresh_org.can_upgrade_to_software_bundle)
        with override_settings(IS_AUS_GEO=True):
            self.assertFalse(self.organisation.can_upgrade_to_software_bundle)

    def test_essential_eight_certification(self):
        self.assertFalse(self.organisation.essential_eight_certification)
        self.assertFalse(self.organisation.has_essential_eight_certification)
        OrganisationCertificationFactory(
            organisation=self.organisation,
            version=self.cert_version_e8,
            status=OrganisationCertification.CERTIFIED
        )
        self.assertTrue(self.organisation.has_essential_eight_certification)
        self.assertTrue(self.organisation.essential_eight_certification)

    def test_organisation_has_software_support_enabled_with_distributor_default_setting(self):
        """
        Test that organisation has software support enabled when the distributor has default software support setting
        Since this is done on signal level, we need to use Django CRUD
        """
        organisation_without_software_support_setting = Organisation.objects.create(
            name='Ara ORG',
            industry='CONS',
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=self.direct_partner
        )
        self.assertFalse(organisation_without_software_support_setting.software_support)

        distributor = DistributorFactory(new_organisation_software_support_only=True)
        partner = PartnerFactory(distributor=distributor)
        organisation = Organisation.objects.create(
            name='Ara ORG',
            industry='CONS',
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=partner
        )
        self.assertTrue(organisation.software_support)

    def test_organisation_combined_deprecated_installs(self):
        active_app_version = AppVersionFactory(
            major=5,
            minor=1,
            patch=0,
            support_level=AppVersion.SupportLevel.ACTIVE,
            version_channel=AppVersion.VersionChannel.STABLE,
        )
        deprecated_app_version = AppVersionFactory(
            major=4,
            minor=9,
            patch=0,
            support_level=AppVersion.SupportLevel.DEPRECATED,
            version_channel=AppVersion.VersionChannel.STABLE,
        )

        AppInstallFactory(
            app_user=self.app_user, version=active_app_version, app_version="5.1.0"
        )
        self.assertEqual(self.organisation.combined_deprecated_installs().count(), 0)
        self.assertFalse(self.organisation.has_combined_deprecated_installs)

        AppInstallFactory(
            app_user=self.app_user, version=deprecated_app_version, app_version="4.9.0"
        )
        self.assertEqual(self.organisation.combined_deprecated_installs().count(), 1)
        self.assertTrue(self.organisation.has_combined_deprecated_installs)

    def test_is_eligible_250k_insurance_partner_org_without_ce_cert(self):
        org = OrganisationFactory(is_partner_org=True)
        self.assertFalse(org.is_eligible_for_250k_insurance)

    def test_is_eligible_250k_insurance_partner_org_without_issued_cert(self):
        self.organisation.is_partner_org = True
        self.organisation.save()
        self.assertFalse(self.organisation.is_eligible_for_250k_insurance)

    def test_is_eligible_250k_insurance_partner_org_partner_iasme_cb(self):
        self.organisation.partner.iasme_cb = True
        self.organisation.partner.save()
        self.assertFalse(self.organisation.is_eligible_for_250k_insurance)
        self.assertFalse(self.organisation.is_eligible_for_100k_insurance)

    def test_is_eligible_250k_insurance_partner_org_without_cep_subscription(self):
        self.organisation.is_partner_org = True
        self.organisation.save()

        billing = PartnerBillingFactory(partner=self.organisation.partner)
        PartnerSubscriptionFactory(organisation=self.organisation, billing=billing, plan_id=plans.PARTNER_BUNDLE_CE_CAP_ANNUAL_PLAN)
        IssuedCertificationFactory(certificate=self.org_cert, date=timezone.now())

        self.assertFalse(self.organisation.is_eligible_for_250k_insurance)

    def test_is_eligible_250k_insurance_partner_org_with_all_conditions_met(self):
        self.organisation.is_partner_org = True
        self.organisation.save()

        billing = PartnerBillingFactory(partner=self.organisation.partner)
        PartnerSubscriptionFactory(organisation=self.organisation, billing=billing, plan_id=plans.PARTNER_CUSTOM_BUNDLE_CE_CEP_PLAN)
        IssuedCertificationFactory(certificate=self.org_cert, date=timezone.now())

        self.assertTrue(self.organisation.is_eligible_for_250k_insurance)


@override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
@override_switch(TRUSTD_BETA_SWITCH, active=True)
class OrganisationCAPLicencesQuantityTestCase(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user(with_app_install=False)

    def create_beta_app_installs(self):
        # create 1 trustd mobile CAP
        TrustdDeviceFactory.create(app_install=self.create_cap(
            app_user=self.app_user,
            device_type=AppInstall.MOBILE,
            app_version=random_version([2, 4], [0, 9], [0, 9]),
            platform="ios"
        ))
        # create 1 CAP v5
        self.create_cap(
            app_user=self.app_user,
            device_type=AppInstall.DESKTOP,
            app_version=random_version(5, [0, 9], [0, 9]),
            platform="darwin"
        )

    def create_non_beta_app_installs(self):
        # create 3 mobile CAP
        for _ in range(3):
            self.create_cap(
                app_user=self.app_user,
                device_type=AppInstall.MOBILE,
                app_version=random_version([2, 4], [0, 9], [0, 9]),
                platform="android"
            )
        # create 1 paid desktop CAP
        self.create_cap(
            app_user=self.app_user,
            device_type=AppInstall.DESKTOP,
            app_version=random_version([2, 4], [0, 9], [0, 9])
        )

    def create_desktop_duplicates(self):
        # create some desktop app installs that are considered duplicates
        self.app_install_v4_duplicate = AppInstallFactory(
            hostname='app_install_v4_duplicate',
            app_user=self.app_user,
            app_version='4.5',
            device_type=AppInstall.VIRTUAL_DESKTOP,
            machine_model='vmware',
        )
        self.app_install_v5_duplicate = AppInstallFactory(
            hostname='app_install_v5_duplicate',
            app_user=self.app_install_v4_duplicate.app_user,
            device_id=f'{CAP_V_FIVE_BETA_PREFIX}{self.app_install_v4_duplicate.device_id}',
            real_device_id=self.app_install_v4_duplicate.real_device_id,
            serial_number=self.app_install_v4_duplicate.serial_number,
            app_version='5.2.10',
            device_type=AppInstall.VIRTUAL_DESKTOP,
            machine_model='vmware',
        )
        app_user_2 = AppUserFactory(organisation=self.organisation)
        self.app_install_v4_dup_2 = AppInstallFactory(
            hostname='app_install_v4_dup_2',
            app_user=app_user_2,
            app_version='4.5'
        )
        self.app_install_v5_dup_2 = AppInstallFactory(
            hostname='app_install_v5_dup_2',
            app_user=self.app_install_v4_dup_2.app_user,
            device_id=f'{CAP_V_FIVE_BETA_PREFIX}{self.app_install_v4_dup_2.device_id}',
            real_device_id=self.app_install_v4_dup_2.real_device_id,
            serial_number=self.app_install_v4_dup_2.serial_number,
            app_version='5.10.1'
        )

    def tearDown(self):
        self.deleting_user()
        self.deleting_app_user()
        self.core_tearing_down()

    def test_simulate_installed_devices_count(self):
        self.assertEqual(self.organisation.cap_licences_quantity, 0)
        self.organisation.simulate_installed_devices_count = 10
        self.organisation.save()
        self.assertEqual(self.organisation.cap_licences_quantity, 0)

    @override_settings(IS_DEVELOP=True)
    def test_simulate_installed_devices_count_debug(self):
        self.assertEqual(self.organisation.cap_licences_quantity, 0)
        self.organisation.simulate_installed_devices_count = 10
        self.organisation.save()
        self.assertEqual(self.organisation.cap_licences_quantity, 10)

    def test_partner_free_mobile(self):
        self.create_non_beta_app_installs()
        self.assertEqual(self.organisation.cap_licences_quantity, 4)
        self.organisation.partner.free_mobile_apps = True
        self.organisation.partner.default_pricing_band = Organisation.PRICING_MONTHLY_V4
        self.organisation.partner.save()
        self.assertEqual(self.organisation.cap_licences_quantity, 1)

    def test_exclude_beta_apps(self):
        self.create_beta_app_installs()
        self.create_non_beta_app_installs()
        self.assertEqual(self.organisation.cap_licences_quantity, 6)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.cap_licences_quantity, 4)

    def test_exclude_beta_apps_ga_desktop(self):
        self.create_desktop_duplicates()
        self.create_beta_app_installs()
        self.create_non_beta_app_installs()
        # this should be the same as GA enabled due to is_cap_v_five_ga_enabled() returning True during tests
        with override_switch(CAP_V_FIVE_BETA_SWITCH, active=False):
            self.assertEqual(self.organisation.cap_licences_quantity, 8)
            self.organisation.bulk_install = True
            self.organisation.save()
            DirectCustomerFactory(organisation=self.organisation)
            self.assertEqual(self.organisation.v6_quantity, self.organisation.cap_licences_quantity)

    def test_exclude_beta_apps_ga_mobile(self):
        self.create_non_beta_app_installs()
        # create some mobile app installs that are considered duplicates
        kwargs = {
            'app_user': self.app_user,
            'device_type': AppInstall.MOBILE,
            'machine_model': 'iPhone 12',
            'machine_vendor': 'Apple',
            'platform': 'ios'
        }
        self.mobile_duplicate = AppInstallFactory(
            app_version='3.1.2',
            **kwargs
        )
        # add app_install__ prefix to kwargs
        new_kwargs = {f'app_install__{k}': v for k, v in kwargs.items()}
        self.mobile_duplicate_trustd = TrustdDeviceFactory(
            app_install__app_version='2.9.2',
            **new_kwargs
        )
        app_user_2 = AppUserFactory(organisation=self.organisation)
        kwargs = {
            'app_user': app_user_2,
            'device_type': AppInstall.MOBILE,
            'machine_model': 'ONEPLUS A6003',
            'machine_vendor': 'OnePlus',
            'platform': 'android'
        }
        self.mobile_duplicate_2 = AppInstallFactory(
            app_version='3.2',
            **kwargs
        )
        new_kwargs = {f'app_install__{k}': v for k, v in kwargs.items()}
        self.mobile_duplicate_trustd_2 = TrustdDeviceFactory(
            app_install__app_version='11.0.2',
            **new_kwargs
        )
        self.assertEqual(self.organisation.cap_licences_quantity, 6)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.cap_licences_quantity, 4)

        self.disable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.cap_licences_quantity, 6)
        self.organisation.bulk_install = True
        self.organisation.save()
        DirectCustomerFactory(organisation=self.organisation)
        self.assertEqual(self.organisation.v6_quantity, self.organisation.cap_licences_quantity)


@override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
@override_switch(TRUSTD_BETA_SWITCH, active=True)
class OrganisationUserLicencesQuantityTestCase(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_organisation()

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()

    def test_no_enrolled_users(self):
        self.assertEqual(self.organisation.enrolled_users.count(), 0)
        self.assertEqual(self.organisation.user_licences_quantity, 0)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.enrolled_users.count(), 0)
        self.assertEqual(self.organisation.user_licences_quantity, 0)

    def test_enrolled_user_no_cap(self):
        self.create_cap_user(
            organisation=self.organisation
        )
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.user_licences_quantity, 1)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.user_licences_quantity, 1)

    def test_enrolled_user_with_cap(self):
        user = self.create_cap_user(
            organisation=self.organisation,
        )
        self.create_cap(
            app_user=user,
            device_type=AppInstall.DESKTOP,
            app_version=random_version([2, 4], [0, 9], [0, 9])
        )
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.user_licences_quantity, 1)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.user_licences_quantity, 1)

    def test_enrolled_user_trustd_only(self):
        user = self.create_cap_user(
            organisation=self.organisation,
        )
        TrustdDeviceFactory.create(app_install=self.create_cap(
            app_user=user,
            device_type=AppInstall.MOBILE,
            platform="ios",
            app_version=random_version([2, 4], [0, 9], [0, 9])
        ))
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.user_licences_quantity, 1)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.user_licences_quantity, 0)

    def test_enrolled_user_cap_v5_only(self):
        user = self.create_cap_user(
            organisation=self.organisation,
        )
        self.create_cap(
            app_user=user,
            device_type=AppInstall.DESKTOP,
            app_version=random_version(5, [0, 9], [0, 9])
        )
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.user_licences_quantity, 1)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.user_licences_quantity, 0)

    def test_enrolled_user_trustd_and_cap_v5_and_cap_v4(self):
        user = self.create_cap_user(
            organisation=self.organisation,
        )
        TrustdDeviceFactory.create(app_install=self.create_cap(
            app_user=user,
            device_type=AppInstall.MOBILE,
            platform="ios",
            app_version=random_version(5, [0, 9], [0, 9])
        ))
        self.create_cap(
            app_user=user,
            device_type=AppInstall.DESKTOP,
            app_version=random_version(5, [0, 9], [0, 9])
        )
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.user_licences_quantity, 1)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.user_licences_quantity, 0)
        # v4
        self.create_cap(
            app_user=user,
            device_type=AppInstall.DESKTOP,
            app_version=random_version([2, 4], [0, 9], [0, 9])
        )
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.user_licences_quantity, 1)


@override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
@override_switch(TRUSTD_BETA_SWITCH, active=True)
class OrganisationEnrolledBetaUsersTestCase(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user(with_app_install=False)

    def tearDown(self):
        self.deleting_user()
        self.deleting_app_user()
        self.core_tearing_down()

    def test_no_users(self):
        AppUser.objects.all().delete()
        self.assertEqual(self.organisation.enrolled_users.count(), 0)
        self.assertEqual(self.organisation.enrolled_beta_users.count(), 0)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.enrolled_users.count(), 0)
        self.assertEqual(self.organisation.enrolled_beta_users.count(), 0)

    def test_cap_v4_non_beta_users(self):
        self.create_cap(
            app_user=self.app_user,
            app_version=random_version([2, 4], [0, 9], [0, 9]),
            device_type=AppInstall.DESKTOP
        )
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.enrolled_beta_users.count(), 0)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.enrolled_beta_users.count(), 0)

    def test_trustd_beta_users(self):
        TrustdDeviceFactory.create(app_install=self.create_cap(
            app_user=self.app_user,
            device_type=AppInstall.MOBILE,
            platform="ios",
            app_version=random_version(5, [0, 9], [0, 9])
        ))
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.enrolled_beta_users.count(), 0)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.enrolled_beta_users.count(), 1)

    def test_cap_v5_beta_users(self):
        self.create_cap(
            app_user=self.app_user,
            app_version=random_version(5, [0, 9], [0, 9])
        )
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.enrolled_beta_users.count(), 0)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.enrolled_beta_users.count(), 1)

    def test_trustd_cap_v5_beta_users(self):
        TrustdDeviceFactory.create(app_install=self.create_cap(
            app_user=self.app_user,
            device_type=AppInstall.MOBILE,
            platform="android",
            app_version=random_version(5, [0, 9], [0, 9])
        ))
        self.create_cap(
            app_user=self.app_user,
            app_version=random_version(5, [0, 9], [0, 9])
        )
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.enrolled_beta_users.count(), 0)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.enrolled_beta_users.count(), 1)

    def test_trustd_cap_v5_cap_v4_beta_users(self):
        TrustdDeviceFactory.create(app_install=self.create_cap(
            app_user=self.app_user,
            device_type=AppInstall.MOBILE,
            platform="android",
            app_version=random_version([4, 5], [0, 9], [0, 9])
        ))
        self.create_cap(
            app_user=self.app_user,
            app_version=random_version(5, [0, 9], [0, 9])
        )
        self.create_cap(
            app_user=self.app_user,
            app_version=random_version([2, 4], [0, 9], [0, 9])
        )
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.enrolled_beta_users.count(), 0)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.enrolled_users.count(), 1)
        self.assertEqual(self.organisation.enrolled_beta_users.count(), 0)


@override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
@override_switch(TRUSTD_BETA_SWITCH, active=True)
class OrganisationBetaDevicesMetricsTestCase(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user(with_app_install=False)

    def tearDown(self):
        self.deleting_user()
        self.deleting_app_user()
        self.core_tearing_down()

    def test_no_devices(self):
        self.assertEqual(self.organisation.installed_beta_apps_count, 0)
        self.assertEqual(self.organisation.beta_non_mobile_devices_count, 0)
        self.assertEqual(self.organisation.beta_desktop_devices_count, 0)
        self.assertEqual(self.organisation.beta_mobile_devices_count, 0)
        self.assertEqual(self.organisation.beta_macos_devices_count, 0)
        self.assertEqual(self.organisation.beta_windows_devices_count, 0)
        self.assertEqual(self.organisation.beta_android_devices_count, 0)
        self.assertEqual(self.organisation.beta_ios_devices_count, 0)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.installed_beta_apps_count, 0)
        self.assertEqual(self.organisation.beta_non_mobile_devices_count, 0)
        self.assertEqual(self.organisation.beta_desktop_devices_count, 0)
        self.assertEqual(self.organisation.beta_mobile_devices_count, 0)
        self.assertEqual(self.organisation.beta_macos_devices_count, 0)
        self.assertEqual(self.organisation.beta_windows_devices_count, 0)
        self.assertEqual(self.organisation.beta_android_devices_count, 0)
        self.assertEqual(self.organisation.beta_ios_devices_count, 0)

    def test_beta_devices(self):
        DesktopBetaAppInstallFactory(app_user=self.app_user, platform="win32")
        DesktopBetaAppInstallFactory(app_user=self.app_user, platform="darwin")

        TrustdDeviceFactory(app_install=AppInstallFactory(app_user=self.app_user, device_type=AppInstall.MOBILE, platform="ios"))
        TrustdDeviceFactory(app_install=AppInstallFactory(app_user=self.app_user, device_type=AppInstall.MOBILE, platform="android"))

        self.assertEqual(self.organisation.installed_beta_apps_count, 0)
        self.assertEqual(self.organisation.beta_non_mobile_devices_count, 0)
        self.assertEqual(self.organisation.beta_desktop_devices_count, 0)
        self.assertEqual(self.organisation.beta_mobile_devices_count, 0)
        self.assertEqual(self.organisation.beta_macos_devices_count, 0)
        self.assertEqual(self.organisation.beta_windows_devices_count, 0)
        self.assertEqual(self.organisation.beta_android_devices_count, 0)
        self.assertEqual(self.organisation.beta_ios_devices_count, 0)
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.assertEqual(self.organisation.installed_beta_apps_count, 4)
        self.assertEqual(self.organisation.beta_non_mobile_devices_count, 2)
        self.assertEqual(self.organisation.beta_desktop_devices_count, 2)
        self.assertEqual(self.organisation.beta_mobile_devices_count, 2)
        self.assertEqual(self.organisation.beta_macos_devices_count, 1)
        self.assertEqual(self.organisation.beta_windows_devices_count, 1)
        self.assertEqual(self.organisation.beta_android_devices_count, 1)
        self.assertEqual(self.organisation.beta_ios_devices_count, 1)


class OrganisationAdminTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_create(self):
        data = {
            'organisation': self.organisation,
            'user': self.user,
            'is_admin': True
        }
        admin = OrganisationAdmin.objects.create(
            **data
        )
        self.assertEqual(admin.organisation, data['organisation'])
        self.assertEqual(admin.user, data['user'])
        self.assertEqual(admin.is_admin, data['is_admin'])

    def test_add_roles(self):

        role = Role.objects.create(organisation=self.organisation, name=DEFAULT_ROLE_NAME)

        data = {
            'organisation': self.organisation,
            'user': self.user,
            'is_admin': True
        }
        admin = OrganisationAdmin.objects.create(
            **data
        )
        self.assertEqual(admin.user.profile.roles.first(), role)

    def test_remove_roles(self):
        Role.objects.create(organisation=self.organisation, name=DEFAULT_ROLE_NAME)

        data = {
            'organisation': self.organisation,
            'user': self.user,
            'is_admin': True
        }
        admin = OrganisationAdmin.objects.create(
            **data
        )
        profile = admin.user.profile
        admin.delete()
        self.assertFalse(profile.roles.exists())


class OrganisationCertificationTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.new_organisation = Organisation.objects.create(
            name='Ara ORG',
            industry='CONS',
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=self.direct_partner
        )
        self.org_cert = OrganisationCertification.objects.create(
            organisation=self.new_organisation,
            version=CertificationVersion.objects.filter(
                release_date__lte=timezone.now(),
                created__lte=timezone.now(),
                type__type=CYBER_ESSENTIALS
            ).order_by('-release_date').first()
        )

    def test_create(self):
        data = {
            'organisation': self.organisation,
            'version': self.cert_version_cep
        }
        cert = OrganisationCertification.objects.create(
            **data
        )
        self.assertEqual(cert.organisation, data['organisation'])
        self.assertEqual(cert.version, data['version'])

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_status(self):
        self.assertEqual(self.org_cert.status, 'NS')
        IssuedCertification.objects.create(
            certificate=self.org_cert,
            date=timezone.now(),
            number=1
        )
        self.org_cert.update_status()
        self.assertEqual(self.org_cert.status, 'CF')

    def test_is_version_2024_or_newer(self):

        self.org_cert.version = CertificationVersion(version_number=2024)
        self.assertTrue(self.org_cert.is_version_2024_or_newer)
        self.org_cert.version = CertificationVersion(version_number=2024.5)
        self.assertTrue(self.org_cert.is_version_2024_or_newer)
        self.org_cert.version = CertificationVersion(version_number=2026)
        self.assertTrue(self.org_cert.is_version_2024_or_newer)
        self.org_cert.version = CertificationVersion(version_number=2023.5)
        self.assertFalse(self.org_cert.is_version_2024_or_newer)

    def test_has_25k_active_insurance(self):
        self.assertFalse(self.org_cert.has_25k_active_insurance)
        self.assertFalse(self.org_cert.has_100k_active_insurance)
        SuperscriptOptInFactory(certification=self.org_cert)
        self.org_cert.status = OrganisationCertification.CERTIFIED
        self.org_cert.save()
        self.new_organisation.superscript_product_code = Organisation.CE_25K
        self.new_organisation.save()
        self.assertTrue(self.org_cert.has_25k_active_insurance)
        self.assertFalse(self.org_cert.has_100k_active_insurance)

    def test_has_100k_active_insurance(self):
        self.assertFalse(self.org_cert.has_25k_active_insurance)
        self.assertFalse(self.org_cert.has_100k_active_insurance)
        SuperscriptOptInFactory(certification=self.org_cert)
        self.org_cert.status = OrganisationCertification.CERTIFIED
        self.org_cert.save()
        self.new_organisation.superscript_product_code = Organisation.CE_100K
        self.new_organisation.save()
        self.org_cert.refresh_from_db()
        self.assertTrue(self.org_cert.has_100k_active_insurance)
        self.assertFalse(self.org_cert.has_25k_active_insurance)

    def test_does_not_have_active_insurance(self):
        self.assertFalse(self.org_cert.has_25k_active_insurance)
        self.assertFalse(self.org_cert.has_100k_active_insurance)
        self.org_cert.status = OrganisationCertification.CERTIFIED
        self.org_cert.save()
        self.assertFalse(self.org_cert.has_100k_active_insurance)
        self.assertFalse(self.org_cert.has_25k_active_insurance)
        self.new_organisation.superscript_product_code = Organisation.CE_25K
        self.new_organisation.save()
        self.assertFalse(self.org_cert.has_100k_active_insurance)
        self.assertFalse(self.org_cert.has_25k_active_insurance)

    def test_has_100k_active_insurance_sutcliffe(self):
        self.assertFalse(self.org_cert.has_25k_active_insurance)
        self.assertFalse(self.org_cert.has_100k_active_insurance)
        InsuranceOptInFactory(
            issued_certification__certificate=self.org_cert,
            coverage=InsuranceOptIn.COVERAGE.CE_100K
        )
        self.org_cert.status = OrganisationCertification.CERTIFIED
        self.org_cert.save()
        self.assertTrue(self.org_cert.has_100k_active_insurance)
        self.assertFalse(self.org_cert.has_25k_active_insurance)

    def test_insurance_policy_25k_file(self):
        """Test that insurance_policy_25k_file returns evidence_of_insurance_file when it exists,
        otherwise returns static file based on insurer type"""
        # Test evidence_of_insurance_file is returned when it exists
        issued_cert = IssuedCertificationFactory(certificate=self.org_cert)
        issued_cert.evidence_of_insurance_file = SimpleUploadedFile("test.pdf", b"test content")
        issued_cert.save()

        self.assertEqual(self.org_cert.insurance_policy_25k_file, issued_cert.evidence_of_insurance_file)
        self.assertTrue(self.org_cert.has_real_25k_insurance_policy_file)

        # Remove evidence file and test static files are returned
        issued_cert.evidence_of_insurance_file = None
        issued_cert.save()

        file_object = StaticFiles.objects.last()
        file_object.insurance_policy_25k = SimpleUploadedFile("test.pdf", b"test content")
        file_object.sutcliffe_insurance_policy_25k = SimpleUploadedFile("test.pdf", b"test content sutcliffe")
        file_object.save()

        SuperscriptOptInFactory(certification=self.org_cert)
        self.assertEqual(self.org_cert.insurance_policy_25k_file, file_object.insurance_policy_25k)
        self.assertFalse(self.org_cert.has_real_25k_insurance_policy_file)

class CertificationSurveyTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.new_organisation = Organisation.objects.create(
            name='Ara ORG',
            industry='CONS',
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=self.direct_partner
        )
        self.org_cert = OrganisationCertification.objects.create(
            organisation=self.new_organisation,
            version=CertificationVersion.objects.filter(
                release_date__lte=timezone.now(),
                created__lte=timezone.now(),
                type__type=CYBER_ESSENTIALS
            ).order_by('-release_date').first()
        )
        self.current_date = timezone.now()
        self.cs = CertificationSurvey.objects.create(
            certificate=self.org_cert,
            datetime_started=self.current_date
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_create(self):
        self.cs.delete()
        data = {
            'certificate': self.org_cert,
            'datetime_started': self.current_date,
        }
        survey = CertificationSurvey.objects.create(
            **data
        )
        self.assertEqual(survey.certificate, data['certificate'])
        self.assertEqual(survey.datetime_started, data['datetime_started'])

    def test_datetime_completed(self):
        self.assertEqual(self.cs.datetime_completed, self.cs.modified)
        self.sd = SurveyDeclaration.objects.create(
            survey=self.cs,
            declaration_date=self.current_date,
            declaration_name='asdf',
            declaration_job='asdf'
        )
        self.assertEqual(self.cs.datetime_completed, self.current_date)


class SurveyDeclarationTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.cs = CertificationSurvey.objects.create(
            certificate=self.organisation.get_latest_certificate(),
            datetime_started=timezone.now()
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_create(self):
        data = {
            'survey': self.cs,
            'declaration_date': timezone.now(),
            'declaration_name': 'd name',
            'declaration_job': 'd job',
        }
        declaration = SurveyDeclaration.objects.create(
            **data
        )
        self.assertEqual(declaration.survey, data['survey'])
        self.assertEqual(declaration.declaration_date, data['declaration_date'])
        self.assertEqual(declaration.declaration_name, data['declaration_name'])
        self.assertEqual(declaration.declaration_job, data['declaration_job'])


class IssuedCertificationTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_create(self):
        data = {
            'certificate': self.organisation.get_latest_certificate(),
            'date': timezone.now(),
            'number': 'one',
        }
        cert = IssuedCertification.objects.create(
            **data
        )
        self.assertEqual(cert.certificate, data['certificate'])
        self.assertEqual(cert.date, data['date'])
        self.assertEqual(cert.number, data['number'])


class SurveyResponseTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.new_organisation = Organisation.objects.create(
            name='Ara ORG',
            industry='CONS',
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=self.direct_partner
        )
        self.cs = CertificationSurvey.objects.create(
            certificate=self.organisation.get_latest_certificate(),
            datetime_started=timezone.now()
        )
        data = {
            'response_type': SurveyQuestion.TEXT,
            'topic': self.question_topic,
            'required': True,
            'version': self.cert_version,
            'parent': self.survey_question,
            'widget': SurveyQuestion.WIDGET_CHECKBOX,
            'considered_as_valid': True,
            'title': 'da',
            'moreinfo': 'good info',
            'show_children_on': True,
            'ranking': SurveyQuestion.RANKING_MAJOR
        }
        self.sq = SurveyQuestion.objects.create(**data)
        data = {
            'survey': self.cs,
            'question': self.sq,
            'choice': None,
            'value_text': 'Azaa'
        }
        self.sr = SurveyResponse.objects.create(**data)

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_create(self):
        data = {
            'survey': self.cs,
            'question': self.sq,
            'choice': None,
            'value_text': 'Azaa'
        }
        response = SurveyResponse.objects.create(
            **data
        )
        self.assertEqual(response.survey, data['survey'])
        self.assertEqual(response.question, data['question'])
        self.assertEqual(response.choice, data['choice'])
        self.assertEqual(response.value_text, data['value_text'])

    def test_get_value(self):
        self.assertEqual(self.sr.value, 'Azaa')
        self.sq.response_type = SurveyQuestion.TEXT

class OrganisationGetterTestCase(BaseTestCase, TestCase):

    @classmethod
    def setUpTestData(cls):
        """Set up data that's shared across all test methods."""
        # Create common test data once
        cls.distributor_1 = Distributor.objects.create(name="Distributor 1")

        # Create partners using bulk_create
        partners = Partner.objects.bulk_create([
            Partner(name="Partner 1", distributor=cls.distributor_1),
            Partner(name="Partner 2", distributor=cls.distributor_1)
        ])
        cls.partner_1 = partners[0]
        cls.partner_2 = partners[1]

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u="yaro-user", e="<EMAIL>")

        self.organisation_1 = Organisation.objects.create(
            name="Organisation 1",
            industry="AWP",
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=self.direct_partner,
            software_support=True
        )
        self.organisation_2 = Organisation.objects.create(
            name="Organisation 2",
            industry="AWP",
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=self.partner_1,
            software_support=True
        )
        self.organisation_3 = Organisation.objects.create(
            name="Organisation 3",
            industry="AWP",
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=self.partner_2,
            software_support=True
        )

    def test_user_does_not_have_access(self):
        self.assertNotEqual(self.organisation_1, get_organisations(self.user, self.organisation_1.secure_id))

    def test_admin_has_access(self):
        self.organisation_1.admins.create(user=self.user)
        self.assertEqual(self.organisation_1, get_organisations(self.user, self.organisation_1.secure_id))

    def test_admin_has_no_access_to_partner_org(self):
        self.organisation_1.admins.create(user=self.user)
        self.assertIn(self.organisation_1, get_organisations(self.user))
        self.assertNotIn(self.organisation_2, get_organisations(self.user))

    def test_partner_user_has_access_to_partner_org(self):
        self.organisation_1.admins.create(user=self.user)
        self.partner_1.users.create(
            user=self.user
        )
        self.assertIn(self.organisation_1, get_organisations(self.user))
        self.assertIn(self.organisation_2, get_organisations(self.user))
        self.assertNotIn(self.organisation_3, get_organisations(self.user))

    def test_user_has_no_access_to_another_partner_org(self):
        self.organisation_1.admins.create(user=self.user)
        self.partner_1.users.create(
            user=self.user
        )
        self.distributor_1.users.create(
            user=self.user
        )
        self.assertIn(self.organisation_1, get_organisations(self.user))
        self.assertIn(self.organisation_2, get_organisations(self.user))
        self.assertNotIn(self.organisation_3, get_organisations(self.user))

    def test_user_has_access_to_another_partner_org(self):
        self.organisation_1.admins.create(user=self.user)
        self.partner_1.users.create(
            user=self.user
        )
        self.distributor_1.users.create(
            user=self.user
        )
        self.distributor_1.access_partners_organisations = True
        self.distributor_1.access_partners_clients = True
        self.distributor_1.save()
        self.assertIn(self.organisation_1, get_organisations(self.user))
        self.assertIn(self.organisation_2, get_organisations(self.user))
        self.assertIn(self.organisation_3, get_organisations(self.user))


class TestCancelOrganisation(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()

    def test_cancel_normal(self):
        new_org = OrganisationFactory(
            partner=self.direct_partner
        )
        user_in_multiple_orgs = get_user_model().objects.create_user(
            username='user_in_multiple_orgs', email='<EMAIL>',
        )
        user_in_one_org = get_user_model().objects.create_user(
            username='user_in_one_org', email='<EMAIL>',
        )
        partner_user = get_user_model().objects.create_user(
            username='partner user', email='<EMAIL>')
        PartnerUserFactory(user=partner_user)
        new_org.admins.create(user=user_in_one_org, is_admin=True, subscribed=True)
        new_org.admins.create(user=user_in_multiple_orgs, is_admin=True, subscribed=True)
        new_org.admins.create(user=partner_user, is_admin=True, subscribed=True)

        other_org = OrganisationFactory()
        other_org.admins.create(user=user_in_multiple_orgs, is_admin=True)

        user_count_before_delete = get_user_model().objects.count()
        app_user = AppUserFactory(organisation=new_org)
        self.assertEqual(new_org.admins.count(), 3)
        new_org.cancel()

        self.assertEqual(new_org.partner.name, settings.CS_PARTNER_CANCELLED_DIRECT_CUSTOMER_NAME)
        self.assertEqual(new_org.partner_before_cancellation.name, self.direct_partner.name)
        self.assertFalse(new_org.admins.all())

        # show that AppUsers get inactivated
        self.assertFalse(new_org.app_users.first().active)

        # Prove that no user got deleted
        self.assertEqual(get_user_model().objects.count(), user_count_before_delete)

        # One user got "deactivated"
        user_in_one_org.refresh_from_db()
        self.assertFalse(user_in_one_org.is_active)
        # While the other is still active as they can access other organisation
        user_in_multiple_orgs.refresh_from_db()
        self.assertTrue(user_in_multiple_orgs.is_active)
        # The partner user is still active
        partner_user.refresh_from_db()
        self.assertTrue(partner_user.is_active)

        # All OrganisationAdmins got removed from the cancelled organisation
        new_org.refresh_from_db()
        self.assertEqual(new_org.admins.count(), 0)

        # The user is still an admin of the other organisation
        other_org.refresh_from_db()
        self.assertEqual(other_org.admins.count(), 1)
        self.assertEqual(other_org.admins.first().user, user_in_multiple_orgs)

        # Show that AppUsers do not get deleted, but deactivated
        app_user.refresh_from_db()
        self.assertFalse(app_user.active)

    def test_cancel_non_direct(self):
        new_org = OrganisationFactory()
        previous_partner_name = new_org.partner.name
        self.assertIsNone(new_org.partner_before_cancellation)

        new_org.cancel()

        self.assertEqual(new_org.partner.name, settings.CS_PARTNER_CANCELLED_PARTNER_CUSTOMER_NAME)
        self.assertEqual(new_org.partner_before_cancellation.name, previous_partner_name)

    def test_cancel_trustd(self):
        from trustd.factories import TrustdDeviceFactory
        app_user = AppUserFactory(organisation=OrganisationFactory())
        device_1 = TrustdDeviceFactory(app_install__app_user=app_user)
        org = app_user.organisation
        customer = device_1.customer
        device_2 = TrustdDeviceFactory(
            app_install__app_user=app_user,
            customer=customer
        )
        with patch('trustd.tasks.delete_trustd_devices.delay') as delete_trustd_devices_mock:
            org.cancel()
            delete_trustd_devices_mock.assert_called_once()
            calls = delete_trustd_devices_mock.call_args_list
            for call_args in calls:
                device_ids = call_args.kwargs.get('device_ids', [])
                self.assertEqual(set(device_ids), {device_1.id, device_2.id})


class TestCEPAuditDate(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_organisation()

    def test_is_latest_cep_audit_legacy(self):
        LEGACY_CEP_BOOKED = timezone.make_aware(datetime(2020, 1, 1))
        JOTFORM_CEP_BOOKED_FOR = timezone.make_aware(datetime(2021, 1, 1))

        org = self.organisation
        self.assertFalse(org.is_latest_cep_audit_legacy)
        org.cep_audit_request_date = LEGACY_CEP_BOOKED
        org.save()
        self.assertTrue(org.is_latest_cep_audit_legacy)
        self.assertEqual(org.cep_audit_date, LEGACY_CEP_BOOKED)

        OrganisationCertificationAuditFactory(
            organisation=org, certificate_version=self.cert_version_cep,
            request_date=JOTFORM_CEP_BOOKED_FOR)
        org.refresh_from_db()
        self.assertFalse(org.is_latest_cep_audit_legacy)
        self.assertEqual(org.cep_audit_date, JOTFORM_CEP_BOOKED_FOR)


class IsCertificateAutoRenewalEnabledTestCase(TestCase):
    """
    Test case for the is_certificate_auto_renewal_enabled property of the Organisation model.
    """
    @override_switch("distributors_and_partners_certificates_auto_renewal", active=False)
    def setUp(self):
        self.organisation = OrganisationFactory()

    @override_switch("distributors_and_partners_certificates_auto_renewal", active=True)
    def test_auto_renewal(self):
        """
        Test that the is_certificate_auto_renewal_enabled property returns the correct value based on the
        certificate_auto_renewal_enabled settings of the organisation, partner, distributor, and the waffle switch.
        """
        with self.subTest("All enabled"):
            ProjectSettings.objects.filter().update(certificate_auto_renewal_enabled=True)
            self.organisation.settings.certificate_auto_renewal_enabled = True
            self.organisation.settings.save()
            self.organisation.partner.default_certificate_auto_renewal_enabled = True
            self.organisation.partner.save()
            self.organisation.partner.distributor.default_certificate_auto_renewal_enabled = True
            self.organisation.partner.distributor.save()
            self.assertTrue(self.organisation.is_certificate_auto_renewal_enabled)
        with self.subTest("ProjectSettings disabled"):
            ProjectSettings.objects.filter().update(certificate_auto_renewal_enabled=False)
            self.assertFalse(self.organisation.is_certificate_auto_renewal_enabled)
        with self.subTest("Organisation settings disabled"):
            ProjectSettings.objects.filter().update(certificate_auto_renewal_enabled=True)
            self.organisation.settings.certificate_auto_renewal_enabled = False
            self.organisation.settings.save()
            self.assertFalse(self.organisation.is_certificate_auto_renewal_enabled)
        with self.subTest("Partner settings disabled"):
            self.organisation.settings.certificate_auto_renewal_enabled = True
            self.organisation.settings.save()
            self.organisation.partner.default_certificate_auto_renewal_enabled = False
            self.organisation.partner.save()
            self.assertFalse(self.organisation.is_certificate_auto_renewal_enabled)
        with self.subTest("Distributor settings disabled"):
            self.organisation.partner.default_certificate_auto_renewal_enabled = True
            self.organisation.partner.save()
            self.organisation.partner.distributor.default_certificate_auto_renewal_enabled = False
            self.organisation.partner.distributor.save()
            self.assertFalse(self.organisation.is_certificate_auto_renewal_enabled)
        with self.subTest("Waffle switch disabled"):
            with override_switch("distributors_and_partners_certificates_auto_renewal", active=False):
                self.organisation.partner.distributor.default_certificate_auto_renewal_enabled = False
                self.organisation.partner.distributor.save()
                self.assertFalse(self.organisation.is_certificate_auto_renewal_enabled)


class OrganisationMeetsBetaFeaturePrerequisitesTestCase(TestCase):
    """
    Test case for the meets_beta_feature_prerequisites method of the Organisation model.
    """
    def setUp(self):
        self.organisation = OrganisationFactory()
        self.non_cap_feature = "non_cap_feature"

    @patch("organisations.models.Organisation.has_software_support", new_callable=PropertyMock)
    def test_meets_beta_feature_prerequisites(self, mock_has_software_support):
        """
        Test that the meets_beta_feature_prerequisites method returns the correct value based on the feature being.
        """
        mock_has_software_support.return_value = True
        for feature_type in ["Non-CAP", "CAP"]:
            with self.subTest(f"{feature_type} feature and has software support"):
                self.assertTrue(self.organisation.meets_beta_feature_prerequisites(self.non_cap_feature))
                for feature in BETA_FEATURES_REQUIRING_SOFTWARE_SUPPORT:
                    self.assertTrue(self.organisation.meets_beta_feature_prerequisites(feature))

        mock_has_software_support.return_value = False
        for feature_type in ["Non-CAP", "CAP"]:
            with self.subTest(f"{feature_type} feature and has not software support"):
                self.assertTrue(self.organisation.meets_beta_feature_prerequisites(self.non_cap_feature))
                for feature in BETA_FEATURES_REQUIRING_SOFTWARE_SUPPORT:
                    self.assertFalse(self.organisation.meets_beta_feature_prerequisites(feature))


class OrganisationTotalBetaUsersCountTestCase(TestCase, BaseTestCase):
    """
    Test case for the total_beta_users_count property of the Organisation model.
    """
    def setUp(self):
        self.organisation = OrganisationFactory()

    @override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
    @override_switch(TRUSTD_BETA_SWITCH, active=True)
    def test_total_beta_users_count(self):
        """
        Test that the total_beta_users_count property returns the correct value based on the beta status of the users
        """
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        with self.subTest("No users"):
            self.assertEqual(self.organisation.total_beta_users_count, 0)
        with self.subTest("No beta users"):
            AppUserFactory(organisation=self.organisation)
            self.assertEqual(self.organisation.total_users_count, 1)
            self.assertEqual(self.organisation.total_beta_users_count, 0)
        with self.subTest("One regular user, one CAPv5 beta user and one Trustd beta user"):
            TrustdDeviceFactory(app_install=AppInstallFactory(app_user=AppUserFactory(organisation=self.organisation)))
            AppInstallFactory(
                app_user=AppUserFactory(organisation=self.organisation),
                app_version=f"{CAP_V_FIVE_BETA_VERSION}.0.1"
            )
            self.assertEqual(self.organisation.total_users_count, 3)
            self.assertEqual(self.organisation.total_beta_users_count, 2)
        with self.subTest("One regular user, one CAPv5 beta user and one Trustd beta user, but beta features are off"):
            self.disable_cap_v5_and_trustd_beta(self.organisation)
            self.assertEqual(self.organisation.total_users_count, 3)
            self.assertEqual(self.organisation.total_beta_users_count, 0)


class OrganisationInstalledBetaUsersCountTestCase(TestCase, BaseTestCase):
    """
    Test case for the installed_beta_users_count property of the Organisation model.
    """
    def setUp(self):
        self.organisation = OrganisationFactory()

    @override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
    @override_switch(TRUSTD_BETA_SWITCH, active=True)
    def test_installed_beta_users_count(self):
        """
        Test that the installed_beta_users_count property returns the correct value based on the beta status of the
        users
        """
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        with self.subTest("No users"):
            self.assertEqual(self.organisation.installed_beta_users_count, 0)
        with self.subTest("No beta users"):
            AppUserFactory(organisation=self.organisation)
            self.assertEqual(self.organisation.total_users_count, 1)
            self.assertEqual(self.organisation.installed_beta_users_count, 0)
        with self.subTest("User with CAPv5 and non-beta app"):
            non_beta_app_user = AppUserFactory(organisation=self.organisation)
            AppInstallFactory(app_user=non_beta_app_user, app_version='4.0.0')
            AppInstallFactory(
                app_user=non_beta_app_user,
                app_version=f"{CAP_V_FIVE_BETA_VERSION}.0.1"
            )
            self.assertEqual(self.organisation.installed_beta_users_count, 0)
            self.assertEqual(self.organisation.installed_users_count, 1)
        with self.subTest("User with Trustd and non-beta app"):
            non_beta_app_user = AppUserFactory(organisation=self.organisation)
            AppInstallFactory(app_user=non_beta_app_user, app_version='4.0.0')
            TrustdDeviceFactory(app_install=AppInstallFactory(app_user=non_beta_app_user))
            self.assertEqual(self.organisation.installed_beta_users_count, 0)
            self.assertEqual(self.organisation.installed_users_count, 2)
        with self.subTest("User with CAPv5 only"):
            beta_app_user = AppUserFactory(organisation=self.organisation)
            AppInstallFactory(
                app_user=beta_app_user,
                app_version=f"{CAP_V_FIVE_BETA_VERSION}.0.1"
            )
            self.assertEqual(self.organisation.installed_beta_users_count, 1)
            self.assertEqual(self.organisation.installed_users_count, 3)
        with self.subTest("User with Trustd only"):
            beta_app_user = AppUserFactory(organisation=self.organisation)
            TrustdDeviceFactory(app_install=AppInstallFactory(app_user=beta_app_user))
            self.assertEqual(self.organisation.installed_beta_users_count, 2)
            self.assertEqual(self.organisation.installed_users_count, 4)
        with self.subTest("User with CAPv5 and Trustd beta app"):
            beta_app_user = AppUserFactory(organisation=self.organisation)
            AppInstallFactory(
                app_user=beta_app_user,
                app_version=f"{CAP_V_FIVE_BETA_VERSION}.0.1"
            )
            TrustdDeviceFactory(app_install=AppInstallFactory(app_user=beta_app_user))
            self.assertEqual(self.organisation.installed_beta_users_count, 3)
            self.assertEqual(self.organisation.installed_users_count, 5)
        with self.subTest("Beta features are off"):
            self.disable_cap_v5_and_trustd_beta(self.organisation)
            self.assertEqual(self.organisation.installed_beta_users_count, 0)
            self.assertEqual(self.organisation.installed_users_count, 5)


class OrganisationSecureInsecureBetaUsersCountTestCase(TestCase, BaseTestCase):
    """
    Test case for the secure_beta_users_count and insecure_beta_users_count property of the Organisation model.
    """
    def setUp(self):
        self.organisation = OrganisationFactory()
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        # non-beta user with 2 apps
        self.non_beta_app_user = AppUserFactory(organisation=self.organisation)
        AppInstallFactory.create_with_report(
            app_version='4.4.0',
            app_user=self.non_beta_app_user)
        AppInstallFactory.create_with_report(
            app_version='4.4.0',
            app_user=self.non_beta_app_user)
        # CAPv5 beta user with 2 apps
        self.cap_v5_beta_app_user = AppUserFactory(organisation=self.organisation)
        AppInstallFactory.create_with_report(
            app_user=self.cap_v5_beta_app_user,
            app_version=f"{CAP_V_FIVE_BETA_VERSION}.0.1"
        )
        AppInstallFactory.create_with_report(
            app_user=self.cap_v5_beta_app_user,
            app_version=f"{CAP_V_FIVE_BETA_VERSION}.0.1"
        )
        # Trustd beta user with 2 apps
        self.trustd_beta_app_user = AppUserFactory(organisation=self.organisation)
        TrustdDeviceFactory(app_install=AppInstallFactory.create_with_report(app_user=self.trustd_beta_app_user))
        TrustdDeviceFactory(app_install=AppInstallFactory.create_with_report(app_user=self.trustd_beta_app_user))
        # a user with both CAPv5 and Trustd beta apps
        self.cap_v5_and_trustd_beta_app_user = AppUserFactory(organisation=self.organisation)
        AppInstallFactory.create_with_report(
            app_user=self.cap_v5_and_trustd_beta_app_user,
            app_version=f"{CAP_V_FIVE_BETA_VERSION}.0.1"
        )
        TrustdDeviceFactory(app_install=AppInstallFactory.create_with_report(
            app_user=self.cap_v5_and_trustd_beta_app_user)
        )

    @staticmethod
    def make_app_user_secure(app_user: AppUser) -> None:
        """
        Make the given app user secure by creating an AppUserAnalytics object with a pass percentage of 100.
        """
        if not hasattr(app_user, "analytics"):
            AppUserAnalyticsFactory(appuser=app_user, pass_percentage=100)
        else:
            app_user.analytics.pass_percentage = 100
            app_user.analytics.save()

    @staticmethod
    def make_app_user_insecure(app_user: AppUser) -> None:
        """
        Make the given app user insecure by creating an AppUserAnalytics object with a pass percentage of 10.
        """
        if not hasattr(app_user, "analytics"):
            AppUserAnalyticsFactory(appuser=app_user, pass_percentage=10)
        else:
            app_user.analytics.pass_percentage = 10
            app_user.analytics.save()

    @override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
    @override_switch(TRUSTD_BETA_SWITCH, active=True)
    def test_secure_insecure_beta_users_count(self):
        """
        Test that the secure_beta_users_count property returns the correct value based on the beta status of the users
        """
        with self.subTest("No secure beta users"):
            self.make_app_user_insecure(self.non_beta_app_user)
            self.make_app_user_insecure(self.cap_v5_beta_app_user)
            self.make_app_user_insecure(self.trustd_beta_app_user)
            self.make_app_user_insecure(self.cap_v5_and_trustd_beta_app_user)
            self.assertEqual(self.organisation.secure_users_count, 0)
            self.assertEqual(self.organisation.insecure_users_count, 4)
            self.assertEqual(self.organisation.secure_beta_users_count, 0)
            self.assertEqual(self.organisation.insecure_beta_users_count, 3)
        with self.subTest("All secure beta users"):
            self.make_app_user_secure(self.non_beta_app_user)
            self.make_app_user_secure(self.cap_v5_beta_app_user)
            self.make_app_user_secure(self.trustd_beta_app_user)
            self.make_app_user_secure(self.cap_v5_and_trustd_beta_app_user)
            self.assertEqual(self.organisation.secure_users_count, 4)
            self.assertEqual(self.organisation.insecure_users_count, 0)
            self.assertEqual(self.organisation.secure_beta_users_count, 3)
            self.assertEqual(self.organisation.insecure_beta_users_count, 0)


class OrganisationCertificationShouldSwitchToIasmeInsurerTestCase(TestCase):
    """
    Test case for the should_switch_to_iasme_insurer property of the OrganisationCertification model.
    """
    def setUp(self):
        self.organisation = OrganisationFactory()
        self.version = CertificationVersionFactory(type__type=CYBER_ESSENTIALS, version_number=2023)
        self.ce = OrganisationCertificationFactory(organisation=self.organisation, version=self.version)
        self.survey = CertificationSurveyFactory(certificate=self.ce)

    @override_switch(SUTCLIFFE_SWITCH, active=False)
    def test_switch_is_disabled(self):
        self.assertFalse(self.ce.should_switch_to_iasme_insurer())

    @override_switch(SUTCLIFFE_SWITCH, active=True)
    def test_switch_is_enabled(self):
        self.assertTrue(self.ce.should_switch_to_iasme_insurer())

    @override_switch(SUTCLIFFE_SWITCH, active=True)
    @patch("organisations.models.OrganisationCertification.expired", new_callable=PropertyMock)
    @patch("organisations.models.OrganisationCertification.certified", new_callable=PropertyMock)
    @patch("organisations.models.OrganisationCertification.superscript_opt_in", new_callable=PropertyMock)
    def test_has_active_superscript_insurance(self, mock_superscript_opt_in, mock_certified, mock_expired):
        with self.subTest("All conditions met"):
            self.organisation.partner.iasme_cb = False
            mock_certified.return_value = True
            mock_superscript_opt_in.return_value = True
            self.survey.migrated_to_iasme_insurance = False
            self.assertFalse(self.ce.should_switch_to_iasme_insurer())

        with self.subTest("Partner is not IASME CB condition is not met"):
            self.organisation.partner.iasme_cb = True
            self.assertTrue(self.ce.should_switch_to_iasme_insurer())

        with self.subTest("Certified nor Expired condition are met"):
            self.organisation.partner.iasme_cb = False
            mock_certified.return_value = False
            mock_expired.return_value = False
            self.assertTrue(self.ce.should_switch_to_iasme_insurer())

        with self.subTest("Expired condition is met along with the rest"):
            self.organisation.partner.iasme_cb = False
            mock_certified.return_value = False
            mock_expired.return_value = True
            mock_superscript_opt_in.return_value = True
            self.survey.migrated_to_iasme_insurance = False
            self.assertFalse(self.ce.should_switch_to_iasme_insurer())

        with self.subTest("Superscript opt-in condition is not met"):
            mock_certified.return_value = True
            mock_superscript_opt_in.side_effect = AttributeError
            self.assertTrue(self.ce.should_switch_to_iasme_insurer())

        with self.subTest("Survey migrated to IASME insurance condition is not met"):
            mock_superscript_opt_in.side_effect = None
            self.survey.migrated_to_iasme_insurance = True
            self.assertTrue(self.ce.should_switch_to_iasme_insurer())

        with self.subTest("All conditions are met but the switch is disabled"):
            self.survey.migrated_to_iasme_insurance = False
            with override_switch(SUTCLIFFE_SWITCH, active=False):
                self.assertFalse(self.ce.should_switch_to_iasme_insurer())


class OrganisationGetOrCreateBulkDeployUserTestCase(TestCase, BaseTestCase):
    """
    Test case for the get_or_create_bulk_deploy_user method of the Organisation model.
    """
    def setUp(self):
        self.organisation = OrganisationFactory()

    def test_get_or_create_bulk_deploy_user(self):
        # Test creating a new bulk deploy user
        bulk_deploy_user = self.organisation.get_or_create_bulk_deploy_user()
        self.assertEqual(bulk_deploy_user.email, self.organisation.get_bulk_deploy_email())
        self.assertTrue(bulk_deploy_user.is_admin)
        self.assertEqual(bulk_deploy_user.lms_enrollment_status, AppUser.STATUS.LMS_SKIPPED_ENROLLMENT)

        # Test fetching an existing bulk deploy user
        existing_user = self.organisation.get_or_create_bulk_deploy_user()
        self.assertEqual(existing_user, bulk_deploy_user)

    def test_update_existing_user_to_admin(self):
        # Create a non-admin user with bulk deploy email
        non_admin_user = AppUser.objects.create(
            email=self.organisation.get_bulk_deploy_email(),
            organisation=self.organisation,
            is_admin=False
        )

        # Call get_or_create_bulk_deploy_user to update the user
        updated_user = self.organisation.get_or_create_bulk_deploy_user()

        # Assert that the updated user is the same as the non-admin user
        self.assertEqual(updated_user, non_admin_user)

        # Assert that the user is now an admin
        self.assertTrue(updated_user.is_admin)
        self.assertEqual(updated_user.lms_enrollment_status, AppUser.STATUS.LMS_SKIPPED_ENROLLMENT)

    def test_create_new_bulk_deploy_user_with_existing_different_user(self):
        # Create a user with different email
        AppUser.objects.create(
            email="<EMAIL>",
            organisation=self.organisation,
            is_admin=False
        )
        # Call get_or_create_bulk_deploy_user to create new bulk deploy user
        bulk_deploy_user = self.organisation.get_or_create_bulk_deploy_user()

        # Assert that a new user was created with bulk deploy email
        self.assertEqual(bulk_deploy_user.email, self.organisation.get_bulk_deploy_email())
        self.assertTrue(bulk_deploy_user.is_admin)
        self.assertEqual(bulk_deploy_user.lms_enrollment_status, AppUser.STATUS.LMS_SKIPPED_ENROLLMENT)

        # Assert that both users exist
        self.assertEqual(self.organisation.app_users.count(), 2)
        self.assertTrue(AppUser.objects.filter(email="<EMAIL>").exists())


class TestOrganisationPolicyVersion(TestCase):
    """
    Test case for the version_number property of the OrganisationCertification model.
    """
    def setUp(self):
        self.organisation = OrganisationFactory()

    def test_rounded_version(self):
        # Test cases for rounded_version property
        test_cases = [
            (1.01, 1.01),
            (1.100, 1.1),
            (44.019, 44.019),
            (44.009, 44.009),
            (1.0001, 1.0001),
            (1.2345, 1.2345),
            (1.2349, 1.2349),
            (100.0, 100.0),
            (0.001, 0.001),
            (0.0001, 0.0001),
        ]

        for input_version, expected_output in test_cases:
            OrganisationPolicyVersionFactory(version=input_version)
            policy = OrganisationPolicyVersionFactory(version=input_version)
            self.assertEqual(policy.rounded_version, expected_output,
                                f"Expected {expected_output} for input {input_version}, but got {policy.rounded_version}")

        # Test with None value
        policy_none = OrganisationPolicyVersionFactory(version=None)
        self.assertEqual(policy_none.rounded_version, None)

        # Test with integer value
        policy_int = OrganisationPolicyVersionFactory(version=42)
        self.assertEqual(policy_int.rounded_version, 42)

        # Test with very large float
        policy_large = OrganisationPolicyVersionFactory(version=1234567890.123456789)
        self.assertEqual(policy_large.rounded_version, 1234567890.1234)

class CyberSmartLearnEnablementTestCase(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_app_user()
        ps = ProjectSettings.objects.get()
        ps.academy_enabled = True
        ps.save()

    @patch('common.models.model_crud')
    def test_learn_lite_enabled(self, _):
        org = self.organisation
        self.assertFalse(org.learn_lite_enabled)

        org.bulk_install = False
        org.save()
        self.assertFalse(org.learn_lite_enabled)

        org.settings.academy_enabled = True
        org.settings.save()
        org.save()
        self.assertFalse(org.learn_lite_enabled)

        org.partner.default_academy_enabled = True
        org.partner.save()
        self.assertTrue(org.learn_lite_enabled)

        org.bulk_install = True
        org.save()
        self.assertFalse(org.learn_lite_enabled)

    @patch('common.models.model_crud')
    def test_learn_lite_tab_displayed(self, _):
        org = self.organisation
        org.settings.academy_enabled = True
        org.settings.save()
        org.bulk_install = False
        org.save()
        self.assertFalse(org.learn_lite_tab_displayed)

        org.partner.default_academy_enabled = True
        org.partner.save()
        self.assertFalse(org.learn_lite_tab_displayed)

        org.user_journey_passed = timezone.now()
        org.save()
        self.assertTrue(org.learn_lite_tab_displayed)

        org.bulk_install = True
        org.save()
        self.assertFalse(org.learn_lite_tab_displayed)

    @patch('common.models.model_crud')
    def test_learn_lite_switch_visible(self, _):
        org = self.organisation
        self.assertFalse(org.learn_lite_switch_visible)

        org.partner.default_academy_enabled = True
        org.partner.save()
        self.assertTrue(org.learn_lite_switch_visible)

        org.bulk_install = True
        org.save()
        self.assertFalse(org.learn_lite_switch_visible)

        org.bulk_install = False
        org.save()
        ps = ProjectSettings.objects.get()
        ps.cybersmart_learn_enablement = True
        ps.save()
        self.assertTrue(org.learn_lite_switch_visible)

        org.settings.academy_enabled = True
        org.settings.save()
        self.assertTrue(org.learn_lite_switch_visible)

        org.settings.cybersmart_learn_enablement = True
        org.settings.save()
        self.assertTrue(org.learn_lite_switch_visible)

        org.settings.academy_enabled = False
        org.settings.save()
        self.assertFalse(org.learn_lite_switch_visible)

        org.settings.cybersmart_learn_enabled = True
        org.settings.save()
        self.assertFalse(org.learn_lite_switch_visible)

    @patch('common.models.model_crud')
    def test_learn_lite_enabled_globally(self, _):
        org = self.organisation
        self.assertTrue(org.learn_lite_enabled_globally)

        ps = ProjectSettings.objects.get()
        ps.academy_enabled = False
        ps.save()
        self.assertFalse(org.learn_lite_enabled_globally)

    @patch('common.models.model_crud')
    def test_learn_enabled_globally(self, _):
        org = self.organisation
        self.assertFalse(org.learn_enabled_globally)

        ps = ProjectSettings.objects.get()
        ps.cybersmart_learn_enablement = True
        ps.save()
        self.assertTrue(org.learn_enabled_globally)

    @patch('common.models.model_crud')
    def test_learn_enabled(self, _):
        org = self.organisation
        self.assertFalse(org.learn_enabled)

        ps = ProjectSettings.objects.get()
        ps.cybersmart_learn_enablement = True
        ps.save()
        self.assertFalse(org.learn_enabled)

        org.settings.cybersmart_learn_enablement = True
        org.settings.save()
        self.assertFalse(org.learn_enabled)

        org.settings.cybersmart_learn_enabled = True
        org.settings.save()
        self.assertTrue(org.learn_enabled)

    @patch('common.models.model_crud')
    def test_learn_switch_visible(self, _):
        org = self.organisation
        self.assertFalse(org.learn_switch_visible)

        ps = ProjectSettings.objects.get()
        ps.cybersmart_learn_enablement = True
        ps.save()
        self.assertFalse(org.learn_switch_visible)

        org.settings.cybersmart_learn_enablement = True
        org.settings.save()
        self.assertTrue(org.learn_switch_visible)

        org.settings.cybersmart_learn_enabled = True
        org.settings.save()
        self.assertTrue(org.learn_switch_visible)

        org.settings.academy_enabled = True
        org.settings.save()
        self.assertFalse(org.learn_switch_visible)

    @patch('common.models.model_crud')
    def test_learn_upgrade_switch_visible(self, _):
        org = self.organisation
        self.assertFalse(org.learn_upgrade_switch_visible)

        ps = ProjectSettings.objects.get()
        ps.cybersmart_learn_enablement = True
        ps.save()
        self.assertFalse(org.learn_upgrade_switch_visible)

        org.settings.cybersmart_learn_enablement = True
        org.settings.save()
        self.assertFalse(org.learn_upgrade_switch_visible)

        org.partner.default_academy_enabled = True
        org.partner.save()
        org.settings.academy_enabled = True
        org.settings.save()
        self.assertTrue(org.learn_upgrade_switch_visible)

        org.settings.cybersmart_learn_enabled = True
        org.settings.save()
        self.assertFalse(org.learn_upgrade_switch_visible)
