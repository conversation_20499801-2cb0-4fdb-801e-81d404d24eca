from http import HTTPStatus

from django.contrib.auth.models import User
from django.test import Client, TestCase
from django.urls import reverse
from django.utils.http import urlencode

from appusers.models import AppVersion, AppInstall
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from appusers.models.factories import AppInstallFactory, AppVersionFactory, AppUserFactory
from appusers.tests.helpers import SoftwarePackageHelperTestMixin
from beta_features.utils import CAP_V_FIVE_BETA_PREFIX
from common.base_tests import BaseTestCase
from opswat.factories import ProductSignatureFactory
from opswat.models import InstalledProductVersion
from opswat_patch.factories import OpswatPatchInstallerFactory, OpswatProductPatchInstallerFactory
from organisations.factories import OrganisationFactory, OrganisationAdminFactory
from organisations.views import DeprecatedDevicesView
from trustd.factories import TrustdDeviceFactory


class TestOrganisationsCSVReportView(BaseTestCase, TestCase):

    GU_ID = '3f3a8495-e4c8-4b4f-ac69-121be2cceb9e'
    MU_ID = 'c965a370-6c54-4aec-9afc-9dd1a5c7b21b'
    S_ID = '13dd55f8-1614-49fb-91b2-c4cbb6089440'

    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()

        self.create_onboarding()
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD})
        response = self.client.get(reverse('settings:profile'))
        self.assertEqual(response.status_code, 200)


    def tearDown(self):
        self.core_tearing_down()

    def test_organisation_user_has_access_to(self):
        self.URL = reverse("organisations:devices-csv-report", kwargs={'org_id': self.organisation.secure_id})

        response = self.client.post(
            self.URL, data={'organisation_id': self.organisation.id, 'device_ids': '[2, 3]'},
            content_type='application/json')
        self.assertEqual(response.status_code, HTTPStatus.OK)
        response = self.client.get(self.URL, {'organisation_id': self.organisation.id}, content_type='application/json')
        self.assertEqual(response.status_code, HTTPStatus.OK)

    def test_organisation_user_has_no_access_to(self):
        self.other_organisation = OrganisationFactory()
        self.URL = reverse("organisations:devices-csv-report", kwargs={'org_id': self.other_organisation.secure_id})

        response = self.client.post(
            self.URL, data={'organisation_id': self.other_organisation.id, 'device_ids': '[2, 3]'},
            content_type='application/json')
        self.assertEqual(response.status_code, HTTPStatus.FORBIDDEN)

        response = self.client.get(self.URL, {'organisation_id': self.other_organisation.id}, content_type='application/json')
        self.assertEqual(response.status_code, HTTPStatus.FORBIDDEN)


class TestDevicesView(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.create_onboarding()
        self.client = Client()

        self.login_resp = self.client.post(
            reverse('account_login'), {'login': self.USER_EMAIL, 'password': self.USER_PASSWORD})

        self.add_devices_permission_to_test_user(self.organisation)
        response = self.client.get(reverse('settings:profile'))
        self.assertEqual(response.status_code, 200)
        self.devices_url = reverse('organisations:devices', kwargs={'org_id': self.organisation.secure_id})

    def test_devices_view_app_installs_ordered_by_version(self):
        AppInstall.objects.all().delete()
        # Create 3 app installs with different versions
        app_install_1 = AppInstallFactory(app_user=self.app_user, app_version='5.1.9')
        app_install_2 = AppInstallFactory(app_user=self.app_user, app_version='5.1.13')
        app_install_3 = AppInstallFactory(app_user=self.app_user, app_version='5.1.11')

        # Get the devices view
        url = self.devices_url
        # add order by version
        url += '?order_by=version'
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)

        devices = response.context['devices']
        # Check that the app installs are ordered correctly by version
        expected_order = [app_install_2.id, app_install_3.id, app_install_1.id]
        self.assertEqual(list(devices.values_list('id', flat=True)), expected_order)

        # Now test reverse order
        url = self.devices_url
        url += '?order_by=-version'
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)

        devices = response.context['devices']
        # Check that the app installs are ordered correctly by version in reverse order
        expected_order = [app_install_1.id, app_install_3.id, app_install_2.id]
        self.assertEqual(list(devices.values_list('id', flat=True)), expected_order)

    def test_devices_view_dropdown(self):
        AppInstall.objects.all().delete()
        AppInstallFactory(app_user=self.app_user, app_version='5.1.9')
        device_1 = AppInstallFactory(app_user=self.app_user, app_version='5.1.13')
        AppInstallFactory(app_user=self.app_user, app_version='5.2.13')
        AppInstallFactory(app_user=self.app_user, app_version='7.1.3')
        AppInstallFactory(app_user=self.app_user, app_version='13.1.11_cybersmart')
        device_2 = AppInstallFactory(app_user=self.app_user, app_version='13.2.11_cybersmart')
        device_3 = AppInstallFactory(app_user=self.app_user, app_version='13.2.11_cybersmart')
        AppInstallFactory(app_user=self.app_user, app_version='1.2.11_cybersmart')

        # Get the devices view
        response = self.client.get(self.devices_url)

        self.assertEqual(response.status_code, 200)

        app_versions_dropdown = response.context['app_versions']
        self.assertEqual(app_versions_dropdown, ['13.2.11', '13.1.11', '7.1.3', '5.2.13', '5.1.13', '5.1.9', '1.2.11'])

        # Get the devices view and filter by version
        response = self.client.get(self.devices_url + '?app_version=5.1.13')
        self.assertEqual(response.status_code, 200)

        devices = response.context['devices']
        self.assertEqual(list(devices.values_list('id', flat=True)), [device_1.id])

        # Get the devices view and filter by version
        response = self.client.get(self.devices_url + '?app_version=13.2.11')
        self.assertEqual(response.status_code, 200)

        devices = response.context['devices']
        self.assertEqual(list(devices.values_list('id', flat=True)), [device_2.id, device_3.id])


class TestDeprecatedDevicesView(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()

        self.create_onboarding()
        self.add_devices_permission_to_test_user(self.organisation)
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD})
        response = self.client.get(reverse('settings:profile'))
        self.assertEqual(response.status_code, 200)
        self.deprecated_version = AppVersionFactory.create(major=4, minor=13, patch=4, support_level = AppVersion.SupportLevel.DEPRECATED)
        self.deprecated_app_version = '4.13.4'
        self.active_version = AppVersionFactory.create(major=5, minor=1, patch=0, support_level = AppVersion.SupportLevel.ACTIVE)
        self.active_app_version = '5.1.0'


    def tearDown(self):
        self.core_tearing_down()

    def test_organisation_user_has_no_access_to(self):
        self.other_organisation = OrganisationFactory()
        self.URL = reverse("organisations:deprecated-devices", kwargs={'org_id': self.other_organisation.secure_id})

        response = self.client.get(self.URL)
        self.assertEqual(response.status_code, HTTPStatus.FORBIDDEN)

    def test_organisation_user_has_access_to_with_no_deprecated_devices_will_redirect(self):
        self.URL = reverse("organisations:deprecated-devices", kwargs={'org_id': self.organisation.secure_id})

        response = self.client.get(self.URL)
        self.assertEqual(response.status_code, HTTPStatus.FOUND)
        self.assertEqual(response.url, reverse('dashboard:organisation', kwargs={'org_id': self.organisation.secure_id}))

    def __create_deprecated_install(self, app_user=None) -> AppInstall:
        """
        Create a deprecated install for the app user.
        """
        if app_user is None:
            app_user = self.app_user

        return AppInstallFactory.create(
            app_user=app_user,
            version=self.deprecated_version,
            app_version=self.deprecated_app_version,
        )

    def __create_active_install(self, device_id=None, serial_number=None, app_user=None):
        """
        Create an active install for the app user.
        """
        if app_user is None:
            app_user = self.app_user

        return AppInstallFactory.create(
            app_user=app_user,
            version=self.active_version,
            app_version=self.active_app_version,
            device_id=device_id,
            serial_number=serial_number,
        )

    def __create__deprecated_install_with_active_install(self, app_user=None):
        """
        Create a deprecated install for the app user and an active duplicate install
        """
        deprecated_install = self.__create_deprecated_install(app_user=app_user)
        active_install = self.__create_active_install(
            app_user=app_user,
            device_id=CAP_V_FIVE_BETA_PREFIX + deprecated_install.device_id,
            serial_number=deprecated_install.serial_number,
        )
        return deprecated_install, active_install

    def test_organisation_with_deprecated_devices_with_all_filter(self):
        self.URL = reverse("organisations:deprecated-devices", kwargs={'org_id': self.organisation.secure_id})

        inactive_user = AppUserFactory.create(organisation=self.organisation, active=False)

        deprecated_install_solo_1 = self.__create_deprecated_install()
        deprecated_install_solo_2 = self.__create_deprecated_install()
        deprecated_install_duplicate_1, active_install_duplicate_1 = self.__create__deprecated_install_with_active_install()
        deprecated_install_with_inactive_user = self.__create_deprecated_install(app_user=inactive_user)
        inactive_user_deprecated_install, inactive_user_active_install = self.__create__deprecated_install_with_active_install(app_user=inactive_user)

        response = self.client.get(self.URL)

        self.assertEqual(response.status_code, HTTPStatus.OK)
        self.assertIn(deprecated_install_solo_1, response.context['installs'])
        self.assertIn(deprecated_install_solo_2, response.context['installs'])
        # Don't show the deprecated install that has an active install
        self.assertNotIn(deprecated_install_duplicate_1, response.context['installs'])
        self.assertNotIn(active_install_duplicate_1, response.context['installs'])
        self.assertNotIn(deprecated_install_with_inactive_user, response.context['installs'])
        self.assertNotIn(inactive_user_deprecated_install, response.context['installs'])
        self.assertNotIn(inactive_user_active_install, response.context['installs'])

        # Selectable table should be enabled for every non-bulk org
        self.assertTrue(response.context['selectable_table_enabled'])

    def test_filtering_only_available_for_upgrade_does_not_show_available_to_deactivate(self):
        self.URL = reverse("organisations:deprecated-devices", kwargs={'org_id': self.organisation.secure_id})
        deprecated_install_solo_1 = self.__create_deprecated_install()
        deprecated_install_solo_2 = self.__create_deprecated_install()
        deprecated_install_duplicate_1, active_install_duplicate_1 = self.__create__deprecated_install_with_active_install()

        response = self.client.get(self.URL, QUERY_STRING=urlencode(
            {'filter': DeprecatedDevicesView.FILTER_AVAILABLE_FOR_UPGRADE})
        )

        self.assertEqual(response.status_code, HTTPStatus.OK)
        self.assertIn(deprecated_install_solo_1, response.context['installs'])
        self.assertIn(deprecated_install_solo_2, response.context['installs'])
        self.assertNotIn(deprecated_install_duplicate_1, response.context['installs'])
        self.assertNotIn(active_install_duplicate_1, response.context['installs'])

    def test_filtering_only_available_to_deactivate_does_not_show_available_for_upgrade(self):
        self.URL = reverse("organisations:deprecated-devices", kwargs={'org_id': self.organisation.secure_id})
        deprecated_install_solo_1 = self.__create_deprecated_install()
        deprecated_install_solo_2 = self.__create_deprecated_install()
        deprecated_install_duplicate_1, active_install_duplicate_1 = self.__create__deprecated_install_with_active_install()

        response = self.client.get(self.URL, QUERY_STRING=urlencode(
            {'filter': DeprecatedDevicesView.FILTER_AVAILABLE_TO_DEACTIVATE})
        )

        self.assertEqual(response.status_code, HTTPStatus.OK)
        self.assertNotIn(deprecated_install_solo_1, response.context['installs'])
        self.assertNotIn(deprecated_install_solo_2, response.context['installs'])
        self.assertNotIn(deprecated_install_duplicate_1, response.context['installs'])
        self.assertNotIn(active_install_duplicate_1, response.context['installs'])

    def test_bulk_organisation_shows_available_to_deactivate_by_default(self):
        self.organisation.bulk_install = True
        self.organisation.save()
        self.URL = reverse("organisations:deprecated-devices", kwargs={'org_id': self.organisation.secure_id})

        deprecated_install_solo_1 = self.__create_deprecated_install()
        deprecated_install_solo_2 = self.__create_deprecated_install()
        deprecated_install_duplicate_1, active_install_duplicate_1 = self.__create__deprecated_install_with_active_install()

        response = self.client.get(self.URL)

        self.assertEqual(response.status_code, HTTPStatus.OK)
        self.assertNotIn(deprecated_install_solo_1, response.context['installs'])
        self.assertNotIn(deprecated_install_solo_2, response.context['installs'])
        self.assertNotIn(deprecated_install_duplicate_1, response.context['installs'])
        self.assertNotIn(active_install_duplicate_1, response.context['installs'])

    def test_bulk_organisation_shows_duplicate_when_filtered(self):
        self.organisation.bulk_install = True
        self.organisation.save()

        self.URL = reverse("organisations:deprecated-devices", kwargs={'org_id': self.organisation.secure_id})

        deprecated_install_solo_1 = self.__create_deprecated_install()
        deprecated_install_solo_2 = self.__create_deprecated_install()
        deprecated_install_duplicate_1, active_install_duplicate_1 = self.__create__deprecated_install_with_active_install()

        response = self.client.get(self.URL, QUERY_STRING=urlencode(
            {'filter': DeprecatedDevicesView.FILTER_AVAILABLE_TO_DEACTIVATE})
        )

        self.assertEqual(response.status_code, HTTPStatus.OK)
        self.assertNotIn(deprecated_install_solo_1, response.context['installs'])
        self.assertNotIn(deprecated_install_solo_2, response.context['installs'])
        self.assertNotIn(deprecated_install_duplicate_1, response.context['installs'])
        self.assertNotIn(active_install_duplicate_1, response.context['installs'])

        # Selectable table should be enabled for every bulk org on filtered view
        self.assertTrue(response.context['selectable_table_enabled'])

    def test_sort_by_display_os(self):
        self.URL = reverse("organisations:deprecated-devices", kwargs={'org_id': self.organisation.secure_id})
        deprecated_install_1 = self.__create_deprecated_install()
        response = self.client.get(self.URL, QUERY_STRING=urlencode({'order_by': 'display_os'}))
        self.assertEqual(response.status_code, HTTPStatus.OK)
        self.assertIn(deprecated_install_1, response.context['installs'])

    def test_sort_by_email(self):
        self.URL = reverse("organisations:deprecated-devices", kwargs={'org_id': self.organisation.secure_id})

        deprecated_install_1 = self.__create_deprecated_install()
        app_user_2 = AppUserFactory(email='<EMAIL>', organisation=self.organisation)
        deprecated_install_2 = AppInstallFactory(
            app_user=app_user_2,
            version=self.deprecated_version,
            app_version=self.deprecated_app_version
        )
        response = self.client.get(self.URL, {'order_by': '-email'})
        self.assertEqual(response.status_code, HTTPStatus.OK)
        installs = list(response.context['installs'])
        self.assertEqual(installs[0], deprecated_install_2)
        self.assertEqual(installs[1], deprecated_install_1)

    def _create_mobile_installs(self):
        mobile_app_user = AppUserFactory(organisation=self.organisation)
        mobile_install = AppInstallFactory(
            app_user=mobile_app_user,
            version=self.deprecated_version,
            app_version=self.deprecated_app_version,
            device_type=AppInstall.MOBILE,
            machine_vendor='Apple',
            machine_model='iPhone12,1',
            platform='iOS'
        )

        trustd_install = AppInstallFactory(
            app_user=mobile_app_user,
            version=self.active_version,
            app_version=self.active_app_version,
            device_type=AppInstall.MOBILE,
            machine_vendor=mobile_install.machine_vendor,
            machine_model=mobile_install.machine_model,
            platform=mobile_install.platform
        )
        TrustdDeviceFactory(app_install=trustd_install)

        return mobile_install, trustd_install

    def test_mobile_showing_on_view_all_view(self):
        """
        Test duplicated mobile app installs show on the "All" view.
        """
        self.URL = reverse("organisations:deprecated-devices", kwargs={'org_id': self.organisation.secure_id})
        mobile_install, trustd_install = self._create_mobile_installs()

        response = self.client.get(self.URL)

        self.assertEqual(response.status_code, HTTPStatus.OK)
        installs = list(response.context['installs'])

        # The other non-trustd mobile install should be in the deprecated devices list
        # (because it's not Trustd)
        self.assertIn(mobile_install, installs)

        # The newer Trustd install should not be in the deprecated devices list
        self.assertNotIn(trustd_install, installs)

        self.assertEqual(len(installs), 1)

    def test_mobile_showing_on_available_to_deactivate_view(self):
        """
        Test duplicated mobile app installs show on the "Available to Deactivate" view.
        """
        self.URL = reverse("organisations:deprecated-devices", kwargs={'org_id': self.organisation.secure_id})
        mobile_install, trustd_install = self._create_mobile_installs()

        response = self.client.get(self.URL, {'filter': DeprecatedDevicesView.FILTER_AVAILABLE_TO_DEACTIVATE})

        self.assertEqual(response.status_code, HTTPStatus.OK)
        installs = list(response.context['installs'])

        # The mobile install should be in the filtered list
        self.assertIn(mobile_install, installs)

        # The Trustd install should not be in the filtered list
        self.assertNotIn(trustd_install, installs)

        self.assertEqual(len(installs), 1)



class MultiSelectionPatchSummaryViewTestCase(TestCase, SoftwarePackageHelperTestMixin):
    """
    Test case for the patching summary when selecting multiple packages on an organisation level.
    """
    def setUp(self):
        self.organisation = OrganisationFactory(settings__patch_enabled=True)
        self.user = User.objects.create_superuser("admin", "<EMAIL>", "password")
        OrganisationAdminFactory(organisation=self.organisation, user=self.user)

        self.client.login(username="admin", password="password")

        self.app_installs = [
            AppInstallFactory(app_user__organisation=self.organisation),
            AppInstallFactory(app_user__organisation=self.organisation),
            AppInstallFactory(app_user__organisation=self.organisation)
        ]

        self._common_packages = {
            "active_protect": ("Active Protect", "CyberSmart", "5.5.0"),
        }

        self._all_app_packages = [
            {
                "pycharm": ("PyCharm", "JetBrains", "3.4.1"),
                "evernote": ("Evernote", "Evernote Corporation", "10.0.0"),
                **self._common_packages
            },
            {
                "visual_studio_code": ("Visual Studio Code", "Microsoft", "1.2.3"),
                **self._common_packages
            },
            {
                "sublime_text": ("Sublime Text", "Sublime HQ Pty Ltd", "4.0.0"),
                "pycharm": ("PyCharm", "JetBrains", "3.4.1"),
                **self._common_packages
            },
        ]

        for app_install, packages in zip(self.app_installs, self._all_app_packages):
            self._create_packages(app_install, packages)

        self._refresh_summaries()

        self.url = reverse(
            "organisations:multi-selection-patch-summary", kwargs={"org_id": self.organisation.secure_id}
        )

    def _create_packages(self, app_install, packages):
        for product_id, (product, vendor, version) in packages.items():
            product_version = self._add_software_package_from_opswat_scanner(
                app_install, product=product, vendor=vendor, version=version, cves=[]
            )

            signature = ProductSignatureFactory(product=product_version.product, name="Product Signature")
            patch_installer = OpswatPatchInstallerFactory(latest_version="101.0.0")
            product_patch_installer = OpswatProductPatchInstallerFactory(
                product=product_version.product,
                patch_installer=patch_installer,
                title="Product Patch Installer"
            )
            product_patch_installer.signatures.add(signature)

            ipv = InstalledProductVersion.objects.get(
                installed_product__app_install=app_install,
                product_version=product_version,
            )
            ipv.signature = signature
            ipv.save()

    def test_upsell_template_rendered_when_patch_disabled(self):
        """
        Should show upsell template if patching is disabled for the organisation.
        """
        self.organisation.settings.patch_enabled = False
        self.organisation.settings.save()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(
            response, "software_inventory/components/patch_summary/level/patch_upsell.html"
        )

    def test_patch_single_package_on_single_device(self):
        """
        Should patch a single package used by only one device.
        """
        vs_code = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=self.organisation,
            product="Visual Studio Code"
        ).first()
        self.assertIsNotNone(vs_code, "Visual Studio Code summary not found")

        response = self.client.post(self.url, data={"selected_packages": [vs_code.id]})
        self.assertEqual(response.status_code, 200)
        self.assertIn("1 software version updates will be applied to 1 device.", response.content.decode())

    def test_patch_single_package_on_multiple_devices(self):
        """
        Should patch one shared package across two devices.
        """
        pycharm = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=self.organisation,
            product="PyCharm"
        ).first()
        self.assertIsNotNone(pycharm, "PyCharm summary not found")

        response = self.client.post(self.url, data={"selected_packages": [pycharm.id]})
        self.assertEqual(response.status_code, 200)
        self.assertIn("1 software version updates will be applied to 2 devices.", response.content.decode())

    def test_patch_multiple_packages_across_multiple_devices(self):
        """
        Should patch two packages used by three total devices.
        """
        active_protect = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=self.organisation,
            product="Active Protect"
        ).first()
        evernote = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=self.organisation,
            product="Evernote"
        ).first()

        self.assertTrue(active_protect and evernote, "Required software summaries not found")

        response = self.client.post(self.url, data={
            "selected_packages": [active_protect.id, evernote.id]
        })

        self.assertEqual(response.status_code, 200)
        self.assertIn("2 software version updates will be applied to 4 devices.", response.content.decode())

    def test_patch_with_multiple_sources_for_single_app_install(self) -> None:
        """
        Regression test for issue where InstalledSoftwareAppInstallIndividual has multiple OPSWAT sources
        for the same app_install (e.g., VLC media player with composite IDs like "32305:opswat:37173,32305:opswat:37181").
        This test ensures the patch summary correctly handles composite IDs with multiple source entries.
        """
        app_install = AppInstallFactory(app_user__organisation=self.organisation)
        vlc_product = self._add_software_package_from_opswat_scanner(
            app_install, product="VLC media player", vendor="VideoLAN", version="3.0.0", cves=[]
        )

        # Create a mock composite ID to test the parsing
        # Simulate the scenario where the ID contains multiple source IDs
        id_from_product_version_with_custom_opswat_id = 1234567890
        composite_id = f"{app_install.id}:opswat:{vlc_product.id},{app_install.id}:opswat:{id_from_product_version_with_custom_opswat_id}"

        # Post to patch summary with the composite ID
        # This tests that the view correctly handles composite IDs
        response = self.client.post(self.url, data={"selected_packages": [composite_id]})

        # Should successfully handle the composite IDs
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context["devices_ids"]), 1)
        self.assertEqual(response.context["devices_ids"], [app_install.id])
        self.assertEqual(response.context["devices_count"], 1)
