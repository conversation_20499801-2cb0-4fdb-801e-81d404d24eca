from unittest.mock import patch, Mock
from django.test import TestCase

from organisations.tasks import get_all_microsoft_users


class MicrosoftSyncPaginationTestCase(TestCase):

    def test_get_all_microsoft_users_single_page(self):
        """Test fetching users when there's only one page"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'value': [
                {'mail': '<EMAIL>', 'displayName': 'User 1'},
                {'mail': '<EMAIL>', 'displayName': 'User 2'},
            ]
        }

        with patch('organisations.tasks.requests.get', return_value=mock_response):
            result = get_all_microsoft_users('fake_token')

        self.assertEqual(result.status_code, 200)
        data = result.json()
        self.assertEqual(len(data['value']), 2)
        self.assertEqual(data['value'][0]['mail'], '<EMAIL>')

    def test_get_all_microsoft_users_multiple_pages(self):
        """Test fetching users across multiple pages"""
        # First page response
        mock_response1 = Mock()
        mock_response1.status_code = 200
        mock_response1.json.return_value = {
            'value': [{'mail': f'user{i}@test.com', 'displayName': f'User {i}'}
                      for i in range(1, 11)],  # 10 users
            '@odata.nextLink': 'https://graph.microsoft.com/v1.0/users?$skiptoken=next'
        }

        # Second page response
        mock_response2 = Mock()
        mock_response2.status_code = 200
        mock_response2.json.return_value = {
            'value': [{'mail': f'user{i}@test.com', 'displayName': f'User {i}'}
                      for i in range(11, 21)],  # 10 more users
            '@odata.nextLink': 'https://graph.microsoft.com/v1.0/users?$skiptoken=next2'
        }

        # Third page response (last page, no nextLink)
        mock_response3 = Mock()
        mock_response3.status_code = 200
        mock_response3.json.return_value = {
            'value': [{'mail': f'user{i}@test.com', 'displayName': f'User {i}'}
                      for i in range(21, 26)],  # 5 more users
        }

        with patch('organisations.tasks.requests.get', side_effect=[
            mock_response1, mock_response2, mock_response3
        ]):
            result = get_all_microsoft_users('fake_token')

        self.assertEqual(result.status_code, 200)
        data = result.json()
        self.assertEqual(len(data['value']), 25)  # Total users across all pages
        self.assertEqual(data['value'][0]['mail'], '<EMAIL>')
        self.assertEqual(data['value'][24]['mail'], '<EMAIL>')

    def test_get_all_microsoft_users_error_response(self):
        """Test handling of error responses"""
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.json.return_value = {'error': 'Unauthorized'}

        with patch('organisations.tasks.requests.get', return_value=mock_response):
            result = get_all_microsoft_users('invalid_token')

        self.assertEqual(result.status_code, 401)
        self.assertEqual(result.json()['error'], 'Unauthorized')
