import waffle
from admin_auto_filters.filters import AutocompleteFilterFactory
from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.forms import <PERSON><PERSON><PERSON><PERSON>
from django.http import HttpResponseRedirect
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils import timezone
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from django_json_widget.widgets import JSONEditorWidget
from modeltranslation.admin import TranslationAdmin

from appusers.models import AppInstall
from common.admin_actions import download_json_cascade
from common.models import ProjectSettings, Group
from common.models import SimpleHistoryAdminCustom
from common.utils import is_technical_support
from insurance.insurers.superscript.utils import delete_contact
from insurance.insurers.superscript.utils import get_contact_data_object
from insurance.insurers.sutcliffe import SUTCLIFFE_SWITCH
from insurance.tasks import superscript_create_or_renew_contact
from organisations.models import (
    Organisation, OrganisationAddress, OrganisationAdmin, OrganisationApprovedDomain,
    OrganisationCertification, OrganisationCheckPermanentFix, OrganisationPolicy,
    OrganisationPolicyVersion, OrganisationSettings, OrganisationUserSync, Role,
    OrganisationCertificationAudit, OrganisationToken
)


@admin.register(OrganisationApprovedDomain)
class OrganisationApprovedDomainAdmin(SimpleHistoryAdminCustom):
    list_filter = [AutocompleteFilterFactory('Organisation', 'organisation')]
    list_display = ('organisation', 'domain', 'active', )
    search_fields = ['organisation__name', 'domain']
    raw_id_fields = ['organisation']
    readonly_fields = ('created', 'modified')


@admin.action(
    description="Opt-out of weekly emails"
)
def email_opt_out(modeladmin, request, queryset):
    queryset_filtered = queryset.filter(subscribed=True)
    # must log first otherwise queryset does not log
    for obj in queryset_filtered:
        modeladmin.log_change(request, obj, 'Opted-out: ' + str(obj))
    queryset_filtered.update(subscribed=False)




@admin.register(OrganisationAdmin)
class OrganisationUserModelAdmin(SimpleHistoryAdminCustom):
    list_display = ('organisation_name', 'user_name', 'is_admin', 'subscribed')
    search_fields = ['user__email']
    list_filter = [AutocompleteFilterFactory('Partner', 'organisation__partner'),
                   AutocompleteFilterFactory('Organisation', 'organisation'), 'is_admin', 'subscribed']
    raw_id_fields = ['organisation', 'user']
    readonly_fields = ('created', 'modified')
    actions = [email_opt_out]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('organisation', 'user')

    @admin.display(
        ordering='organisation__name'
    )
    def organisation_name(self, obj):
        return obj.organisation.name


    @admin.display(
        ordering='user__username'
    )
    def user_name(self, obj):
        return obj.user.username



class OrganisationUserInline(admin.StackedInline):
    model = OrganisationAdmin
    extra = 1
    verbose_name_plural = 'Users'
    raw_id_fields = ('user', )

    def get_readonly_fields(self, request, obj=None):
        if request.user.is_superuser:
            return []
        elif is_technical_support(request.user):
            return ['user']
        return ['user', 'is_admin']


@admin.register(Organisation)
class OrganisationModelAdmin(SimpleHistoryAdminCustom):
    change_form_template = "admin/management/organisations_changeform.html"
    list_display = ('name', 'is_test', 'get_industry_verbose', 'size', 'policies_support', 'data_privacy_support',
                    'partner', 'distributor', 'get_bulk_install', 'created')
    search_fields = ['name', 'id', 'secure_id']
    list_filter = [AutocompleteFilterFactory('Partner', 'partner'), 'partner__distributor', 'bulk_install',
                   'is_test', 'size', 'is_partner_org', 'industry', 'partner__trial_account',
                   'data_privacy_support']
    raw_id_fields = ['partner']
    inlines = (OrganisationUserInline, )

    actions = ['cancel_organisations', 'enable_data_privacy']
    exclude = ['bulk_install']

    def save_model(self, request, obj, form, change):
        name_updated = False
        if 'name' in form.changed_data:
            name_updated = True
        super().save_model(request, obj, form, change)

        if name_updated:
            from billing.tasks import update_chargebee_organisation_name
            update_chargebee_organisation_name.delay(obj.id)

    def save_formset(self, request, form, formset, change):
        formset.save(commit=False)
        org = formset.instance

        # Make sure keep at least one organisation's admin.
        changed_admins_id = []
        changed_to_admins_id = []
        # formset.changed_objects Ex.: [(<OrganisationAdmin: ttt [TEST] (#125 - <EMAIL>)>, ['is_admin'])]
        for changes in formset.changed_objects:
            obj = changes[0]
            if isinstance(obj, OrganisationAdmin) and 'is_admin' in changes[1]:
                if obj.is_admin:
                    changed_to_admins_id.append(obj.id)
                else:
                    changed_admins_id.append(obj.id)
        if (changed_admins_id and
                not org.admins.filter(is_admin=True).exclude(id__in=changed_admins_id).exists() and
                not changed_to_admins_id):
            return messages.error(request, _("Please keep at least one organisation's admin."))

        # Make sure not all organisation's admins will be deleted.
        if formset.deleted_objects:
            deleted_admins_id = []
            for obj in formset.deleted_objects:
                if isinstance(obj, OrganisationAdmin):
                    deleted_admins_id.append(obj.id)
            if deleted_admins_id:
                count = org.admins.filter(is_admin=True).exclude(id__in=(deleted_admins_id + changed_admins_id)).count()
                if count < 1 and not changed_to_admins_id:
                    return messages.error(request, _("Can't remove all organisation's admins."))

        super(OrganisationModelAdmin, self).save_formset(request, form, formset, change)

    def distributor(self, obj):
        if hasattr(obj, 'partner') and obj.partner:
            return obj.partner.distributor

    @admin.display(
        description='Industry'
    )
    def get_industry_verbose(self, obj):
        return obj.industry

    @admin.display(
        description='Bulk ?',
        boolean=True,
    )
    def get_bulk_install(self, obj):
        return obj.bulk_install

    def get_readonly_fields(self, request, obj=None):
        return [
            'secure_id',
            'created',
            'modified',
            'partner_before_cancellation',
            # todo: legacy field, remove later
            'cep_audit_request_date',
            'type_of_customer',
        ]

    def changeform_view(self, request, obj_id=None, form_url='', extra_context=None):
        extra_context = extra_context or {}
        if obj_id is not None and extra_context is not None:
            organisation = Organisation.objects.get(id=obj_id)
            extra_context.update({'has_25k': False, 'has_100k': False})
            if organisation.ce_certification:
                has_25k = organisation.ce_certification.has_25k_active_insurance
                has_100k = organisation.ce_certification.has_100k_active_insurance
                extra_context.update({'has_25k': has_25k, 'has_100k': has_100k})
            extra_context['opt_in'] = organisation.superscript_opt_in
            extra_context['can_change_deployment_type'] = organisation.can_change_deployment_type
        return super().changeform_view(request, obj_id, form_url, extra_context=extra_context)

    def cancel_superscript(self, request, obj):
        success = False
        paused = True
        r_and_r_toolbox_subscription = None
        # in case the organisation has a CE 100k R&R toolbox insurance, we need to pause the subscription
        if obj.has_ce_100k_r_and_r_toolbox_insurance:
            r_and_r_toolbox_subscription = obj.get_r_and_r_toolbox_subscription
            if r_and_r_toolbox_subscription:
                paused = r_and_r_toolbox_subscription.pause()
        else:
            success = delete_contact(obj.superscript_opt_in)
        if not paused:
            self.message_user(
                request,
                _("R&R toolbox subscription scheduled pause could not be fulfilled, please contact finance."),
                level=messages.ERROR
            )
        elif paused and r_and_r_toolbox_subscription:
            self.message_user(request, _("R&R toolbox subscription pause scheduled."), level=messages.SUCCESS)
        elif success:
            self.message_user(request, _("Superscript opt-in has been cancelled"), level=messages.SUCCESS)
        else:
            self.message_user(request, _("There was an issue cancelling this Superscript opt-in"),
                              level=messages.ERROR)
        return HttpResponseRedirect(".")

    def _find_duplicate_app_installs(self, to_update, unique_fields) -> list:
        """
        Return a list of IDs of app installs that have same unique_fields.
        """
        app_user_device_combinations = {}
        violating_app_installs = []

        # First pass: collect all combinations and their corresponding app_install ids
        for app_install in to_update:
            combination = tuple(getattr(app_install, field) for field in unique_fields)
            if combination in app_user_device_combinations:
                app_user_device_combinations[combination].append(app_install.id)
            else:
                app_user_device_combinations[combination] = [app_install.id]

        # Second pass: collect all ids that have duplicates
        for ids in app_user_device_combinations.values():
            if len(ids) > 1:
                violating_app_installs.extend(ids)

        return violating_app_installs

    def change_deployment_type(self, request, organisation):
        """ This changes the organisation deployment type from individual to bulk with UBA off """
        # create an app user if there are no app users, since we need it for bulk enrolment
        created_app_user_text = ''
        if organisation.app_users.count() == 0 or not organisation.bulk_deploy_user:
            bulk_deploy_user = organisation.get_or_create_bulk_deploy_user()
            created_app_user_text = f' and created app user {bulk_deploy_user.email}'
        to_update = []
        current_datetime = timezone.now()
        # move all app installs to the bulk_deploy_user
        for app_install in AppInstall.objects.filter(
                app_user__organisation=organisation
        ):
            app_install.app_user = organisation.bulk_deploy_user
            app_install.modified = current_datetime
            to_update.append(app_install)

        unique_fields = [field for field in AppInstall._meta.unique_together[0] if field != 'app_user_id']
        violating_app_installs = self._find_duplicate_app_installs(to_update, unique_fields)
        if violating_app_installs:
            error_message = _(f"Error: Multiple devices with the same {' and '.join(unique_fields)} "
                              "would be assigned to the same app user. This violates the unique constraint. "
                              f"Violating App Install IDs: {', '.join(map(str, violating_app_installs))}. "
                              "Please delete the older duplicate App Installs manually and try again."
                              )
            self.message_user(request, error_message, level=messages.ERROR)
            return HttpResponseRedirect(".")


        # If no violations, proceed with bulk update
        AppInstall.objects.bulk_update(to_update, fields=['app_user', 'modified'])
        # update deployment method
        organisation.bulk_install = True
        organisation.save(update_fields=['bulk_install', 'modified'])
        # if the organisation has any Groups, then delete since that is not supported for bulk enrolment with UBA OFF
        if organisation.groups.exists():
            organisation.groups.all().delete()
        self.message_user(
            request,
            _(f"Changed deployment type to {organisation.get_bulk_install_display()} {created_app_user_text}"),
            level=messages.SUCCESS
        )
        return HttpResponseRedirect(".")

    def recalculate_analytics(self, request, obj):
        """
        Manually trigger the calculate_organisation_analytics task synchronously.
        This helps CX support to reset smart score.
        """
        from analytics.tasks import calculate_organisation_analytics

        try:
            # Run the task synchronously
            calculate_organisation_analytics(obj.pk)
            self.message_user(
                request,
                _("Smart score has been successfully recomputed for %s") % obj.name,
                level=messages.SUCCESS
            )
        except Exception as e:
            self.message_user(
                request,
                _("Error recalculating analytics: %s") % str(e),
                level=messages.ERROR
            )
        return HttpResponseRedirect(".")

    def response_change(self, request, obj):
        if "cancel-superscript" in request.POST:
            self.cancel_superscript(request, obj)
        if "change-deployment-type" in request.POST:
            self.change_deployment_type(request, obj)
        if "recalculate-analytics" in request.POST:
            self.recalculate_analytics(request, obj)
        return super().response_change(request, obj)

    def has_delete_permission(self, request, obj=None):
        # Disable delete
        if not settings.IS_PROD:
            return True
        return False

    @admin.action(description='Cancel the selected organisations.')
    def cancel_organisations(self, request, queryset):
        for org in queryset:
            try:
                org.cancel()
            except AssertionError:
                self.message_user(
                    request,
                    _("Can't cancel %s due to an active subscription.") % org.name,
                    messages.ERROR)
            else:
                self.message_user(
                    request,
                    _("%s has been successfully cancelled.") % org.name,
                    messages.SUCCESS)

    @admin.action(description='Enable Privacy Toolbox Feature.')
    def enable_data_privacy(self, request, queryset):
        queryset.all().update(data_privacy_support=True)
        self.message_user(
            request,
            "Enabled Privacy Toolbox Feature For Selected Organisations",
            messages.SUCCESS)


@admin.register(OrganisationUserSync)
class OrganisationUserSyncAdmin(admin.ModelAdmin):
    list_display = ('organisation', 'provider', 'email',
                    'added_users', 'removed_users', 'sync_enabled', 'auto_enroll', 'last_synced_time')
    search_fields = ['organisation__name', "organisation__secure_id"]
    raw_id_fields = ['organisation', 'social_token']
    readonly_fields = ['created', 'modified']


@admin.register(OrganisationAddress)
class OrganisationAddressAdmin(admin.ModelAdmin):
    list_display = ('organisation',)
    search_fields = ['organisation__name']
    raw_id_fields = ['organisation']
    readonly_fields = ['created', 'modified']


class StatusFilter(admin.SimpleListFilter):
    title = 'Status'
    parameter_name = 'status'

    def lookups(self, request, model_admin):
        return (
            (OrganisationCertification.EXPIRED, _('Expired')),
            (OrganisationCertification.EXPIRING, _('Expiring')),
            (OrganisationCertification.CERTIFIED, _('Certified')),
            (OrganisationCertification.DECLARATION_SIGNED, _('Declaration Signed')),
            (OrganisationCertification.REQUIRES_ATTENTION, _('Requires Attention')),
            (OrganisationCertification.AWAITING_SIGNATURE, _('Awaiting Signature')),
            (OrganisationCertification.SURVEY_COMPLETED, _('Completed Survey')),
            (OrganisationCertification.IN_SURVEY, _('In Survey')),
            (OrganisationCertification.NOT_STARTED, _('Not Started')),
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.model.objects.filter(id__in=[q.pk for q in queryset if q.status == self.value()])
        else:
            return queryset


@admin.register(OrganisationCertification)
class OrganisationCertificationAdmin(SimpleHistoryAdminCustom):
    change_form_template = "admin/management/organisationcertification_changeform.html"
    list_display = ('organisation', 'version', "renewal_certificate__version", 'status', 'created')
    search_fields = ['organisation__name', 'organisation__id', "organisation__partner__distributor__name"]
    list_editable = ['status']
    list_filter = [AutocompleteFilterFactory('Partner', 'organisation__partner'),
                   AutocompleteFilterFactory('Organisation', 'organisation'),
                   StatusFilter, 'version']
    readonly_fields = ['sutcliffe_insurance_opt_in_instance', 'created', 'modified']
    raw_id_fields = ['organisation', 'version', "renewal_certificate"]
    actions = [download_json_cascade]

    def sutcliffe_insurance_opt_in_instance(self, obj):
        if sutcliffe_opt_in := obj.get_sutcliffe_insurance_opt_in_instance():
            url = reverse('admin:insurance_insuranceoptin_change', args=[sutcliffe_opt_in.pk])
            return format_html('<a href="{}">View Sutcliffe Insurance Opt-In</a>', url)
        return "No Sutcliffe Opt-In"

    def renewal_certificate__version(self, obj):
        return obj.renewal_certificate.version if obj.renewal_certificate else None

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('version', 'version__type')
        return queryset

    @admin.display(
        description='Status'
    )
    def get_status(self, obj):
        return obj.get_status_display()


    def changeform_view(self, request, obj_id=None, form_url='', extra_context=None):
        extra_context = extra_context or {}
        if obj_id is not None and extra_context is not None:
            org_cert = OrganisationCertification.objects.get(id=obj_id)
            # check if already has insurance
            has_25k = org_cert.has_25k_active_insurance
            has_100k = org_cert.has_100k_active_insurance
            extra_context['has_25k'] = has_25k
            extra_context['has_100k'] = has_100k
            extra_context['current_product_code'] = org_cert.organisation.get_superscript_product_code_display()
            # can upgrade insurance if cert is ce, has a survey and is not already 100k
            extra_context['can_add_insurance'] = all([
                org_cert.is_cyber_essentials,
                hasattr(org_cert, 'survey'),
                not has_100k,
                # not applicable for Sutcliffe
                not waffle.switch_is_active(SUTCLIFFE_SWITCH)
            ])
        return super().changeform_view(request, obj_id, form_url, extra_context=extra_context)

    def response_change(self, request, obj):
        if "add-25k-insurance" in request.POST:
            self.add_insurance_opt_in(obj, request, Organisation.CE_25K)
        if "add-100k-insurance" in request.POST:
            self.add_insurance_opt_in(obj, request, Organisation.CE_100K)
        return super().response_change(request, obj)

    def add_insurance_opt_in(self, obj, request, product_code):
        """ Switch an org with Superscript opt-in to 25k or 100k insurance """
        old_product_code = None
        if hasattr(obj, 'superscript_opt_in') and not hasattr(obj.superscript_opt_in, 'cancellation'):
            old_product_code = obj.organisation.get_superscript_product_code_display()
            if product_code == Organisation.CE_100K:
                # delete the old SuperscriptOptIn instance, since it is an upgrade and will be handled like a renewal
                obj.superscript_opt_in.delete()
        # check for errors with Superscript contact
        try:
            get_contact_data_object(obj.survey, obj)
        except Exception as error:
            self.message_user(
                request,
                f"Superscript Error while getting contact data: {error}",
                level=messages.ERROR
            )
        else:
            # set new product code
            obj.organisation.set_new_product_code(product_code)
            # create new opt-in
            superscript_create_or_renew_contact.delay(obj.pk)
            # return feedback to admin
            obj.refresh_from_db()
            self.message_user(
                request,
                f"Upgraded insurance from {old_product_code or 'none'} to"
                f" {obj.organisation.get_superscript_product_code_display()}",
                level=messages.SUCCESS
            )


@admin.register(OrganisationCheckPermanentFix)
class OrganisationCheckPermanentFixAdmin(admin.ModelAdmin):
    list_display = ['organisation', 'app_check', 'created']
    list_filter = [AutocompleteFilterFactory('Organisation', 'organisation')]
    raw_id_fields = ['organisation', 'app_check']
    readonly_fields = ['created', 'modified']


class OrganisationSettingsForm(forms.ModelForm):
    features_help_text = JSONField(widget=JSONEditorWidget, required=False)

    class Meta:
        model = OrganisationSettings
        fields = '__all__'


@admin.register(OrganisationSettings)
class OrganisationSettingsAdmin(SimpleHistoryAdminCustom):
    form = OrganisationSettingsForm
    search_fields = ["organization__name"]
    list_filter = (AutocompleteFilterFactory('Organisation', 'organization'),
                   AutocompleteFilterFactory('Partner', 'organization__partner'),
                   AutocompleteFilterFactory('Distributor', 'organization__partner__distributor'),
                   "uba_enabled", "smart_score_enabled",
                   'web_notifications_enabled', "devices_cleaning_enabled")
    list_display = ("organization", "uba_enabled", "lms_send_email_notifications",
                    "smart_score_enabled", 'app_checks_notifications', 'devices_cleaning_enabled', 'patch_enabled')
    readonly_fields = [
        'organization',
        'created', 'modified',
        'web_notifications_enabled', 'notifications_enabled', 'devices_cleaning_enabled', 'devices_cleaning_days',
        'enforce_multi_factor_authentication'
    ]
    exclude = ["notifications_enabled", 'app_checks_notifications', 'policies_notifications',
               'academy_notifications', 'app_checks_reminders', 'policies_reminders',
               'academy_reminders']



@admin.register(OrganisationPolicy)
class OrganisationPolicyAdmin(admin.ModelAdmin):
    list_filter = [AutocompleteFilterFactory('Organisation', 'organisation'), "active"]
    list_display = ("organisation", "name", "active", 'created')
    raw_id_fields = ['organisation']
    readonly_fields = ['created', 'modified']

    def get_form(self, request, obj=None, change=False, **kwargs):
        form = super().get_form(request, obj, change, **kwargs)
        # check if this an existing object
        if obj:
            form.base_fields["groups"].queryset = Group.objects.filter(organisation=obj.organisation)
        return form


@admin.register(OrganisationPolicyVersion)
class OrganisationPolicyVersionAdmin(admin.ModelAdmin):
    list_display = ('policy__organisation', 'version', 'active', 'main', 'created', 'modified')
    search_fields = ('policy__name', 'policy__organisation__name')
    list_filter = (AutocompleteFilterFactory('Organisation', 'policy__organisation'),
                   'active', 'main', 'created', 'modified')
    readonly_fields = ['policy__organisation', 'created', 'modified', 'pdf_copy']
    raw_id_fields = ['policy']
    fields = ['policy', 'version', 'document', 'document_url', 'active', 'main', 'pdf_copy', 'created', 'modified']

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('policy__organisation')
        return queryset

    @staticmethod
    def policy__organisation(obj):
        return obj.policy.organisation


class OrganisationDefaultFeatures(admin.ModelAdmin):
    read_only_feature_fields = (
        "get_uba_enabled", "get_smart_score_enabled", "get_academy_enabled", "get_web_notifications_enabled",
        "get_r_n_r_toolbox_enabled", "get_certificate_auto_renewal_enabled"
    )

    def get_readonly_fields(self, request, obj=None):
        return super().get_readonly_fields(request, obj=None) + self.read_only_feature_fields

    def get_fieldsets(self, request, obj=None):
        fieldsets = super().get_fieldsets(request, obj=None)
        if obj:
            fieldsets += [("Organisation Features", {"fields": self.read_only_feature_fields})]
        return fieldsets

    def get_fields(self, request, obj=None):
        full_list = super().get_fields(request, obj)
        remove_from_list = list(self.read_only_feature_fields)
        return [x for x in full_list if x not in remove_from_list]

    @staticmethod
    def get_org_filter(obj):
        if obj.__class__.__name__ == "Partner":
            org_filter = {
                "partner_id": obj.id
            }
        elif obj.__class__.__name__ == "Distributor":
            org_filter = {
                "partner__distributor_id": obj.id
            }
        else:
            return {}
        return org_filter

    def get_feature_template(self, obj, field_name):
        org_filter = self.get_org_filter(obj)
        enabled = Organisation.objects.filter(
            **{**{f"settings__{field_name}": True}, **org_filter}
        ).values("name", "settings__pk")
        disabled = Organisation.objects.filter(
            **{**{f"settings__{field_name}": False}, **org_filter}
        ).values("name", "settings__pk")

        return render_to_string(template_name="admin/organisation_feature.html", context={
            "obj": obj,
            "is_enabled": getattr(obj, f"default_{field_name}"),
            "enabled": enabled,
            "disabled": disabled,
            "feature_field_name": field_name,
            "model_type": obj.__class__.__name__.lower()
        })

    @admin.display(
        description="Academy"
    )
    def get_academy_enabled(self, obj):
        if ProjectSettings.is_academy_enabled():
            return self.get_feature_template(obj, "academy_enabled")
        else:
            return "Disabled in Project Settings"


    @admin.display(
        description="Smart Score"
    )
    def get_smart_score_enabled(self, obj):
        if ProjectSettings.is_smart_score_enabled():
            return self.get_feature_template(obj, "smart_score_enabled")
        else:
            return "Disabled in Project Settings"


    @admin.display(
        description="User Based Attribution"
    )
    def get_uba_enabled(self, obj):
        if ProjectSettings.is_uba_enabled():
            return self.get_feature_template(obj, "uba_enabled")
        else:
            return "Disabled in Project Settings"


    @admin.display(
        description="Web Notifications"
    )
    def get_web_notifications_enabled(self, obj):
        if ProjectSettings.is_web_notifications_enabled():
            return self.get_feature_template(obj, "web_notifications_enabled")
        else:
            return "Disabled in Project Settings"


    @admin.display(
        description="Ransomware & Recovery Toolbox"
    )
    def get_r_n_r_toolbox_enabled(self, obj):
        if ProjectSettings.is_r_n_r_toolbox_enabled():
            return self.get_feature_template(obj, "r_n_r_toolbox_enabled")
        else:
            return "Disabled in Project Settings"


    def get_certificate_auto_renewal_enabled(self, obj):
        if ProjectSettings.is_certificate_auto_renewal_enabled():
            return self.get_feature_template(obj, "certificate_auto_renewal_enabled")
        else:
            return "Disabled in Project Settings"


@admin.register(Role)
class RoleAdmin(TranslationAdmin):
    list_display = ('name', 'organisation', 'created', 'modified')
    search_fields = ['name', 'organisation__name', "organisation__secure_id"]
    readonly_fields = ['name', 'organisation', 'permissions_display', 'permissions_name',
                       'created', 'modified']
    list_filter = (AutocompleteFilterFactory('Organisation', 'organisation'),)
    exclude = ['permissions']

    @admin.display(description='Permissions')
    def permissions_display(self, obj):
        return ', '.join(list(obj.permissions.all().values_list('name', flat=True)))

    @admin.display(description='Permissions backend name')
    def permissions_name(self, obj):
        values = obj.permissions.all().values('content_type__app_label', 'codename')
        return ', '.join([f'"{value["content_type__app_label"]}.{value["codename"]}"' for value in values])


@admin.register(OrganisationCertificationAudit)
class OrganisationCertificationAuditAdmin(admin.ModelAdmin):
    raw_id_fields = ("organisation", "certificate_version")
    list_display = (
        "organisation", "certificate_version", "reschedule_link", "salesforce_case_id", "request_date", "created"
    )
    list_filter = (
        AutocompleteFilterFactory("Organisation", "organisation"),
        AutocompleteFilterFactory("Certificate Version", "certificate_version"),
    )


@admin.register(OrganisationToken)
class OrganisationTokenAdmin(admin.ModelAdmin):
    raw_id_fields = ("organisation",)
    list_display = ("organisation",)
    list_filter = (
        AutocompleteFilterFactory("Organisation", "organisation"),
    )

    def has_change_permission(self, request, obj=None):
        return not settings.IS_PROD

    def has_add_permission(self, request):
        return not settings.IS_PROD


