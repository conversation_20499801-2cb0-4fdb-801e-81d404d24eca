import contextlib
from datetime import date, timedelta
from urllib.parse import urljoin

import waffle
from allauth.account import app_settings
from celery import shared_task as task
from celery.utils.log import get_task_logger
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.sites.models import Site
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from django.core.mail import EmailMultiAlternatives
from django.db.models import F
from django.db.models.query import QuerySet
from django.shortcuts import reverse
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.html import strip_tags
from django.utils.timezone import localtime
from django.utils.translation import gettext_lazy as _
from django_otp.plugins.otp_email.models import EmailDevice
from python_http_client.exceptions import HTTPError
from sendgrid.helpers.mail import (
    Attachment, Content, Email, Mail, Bcc, Personalization, Cc, IpPoolName,
    Substitution, To, GroupId, Asm, BypassListManagement, MailSettings, Category,
)

from accounts.permissions import (
    DEVICES_PERMISSION,
    PEOPLE_AND_ORGANISATION_PERMISSION,
    CERTIFICATES_AND_INSURANCE_PERMISSION,
)
from accounts.utils import is_trial_email
from appusers.models import AppUser
from archive.models import EmailLogArchive
from beta_features.models import CAP_V_FIVE_BETA_SWITCH
from beta_features.utils import CAP_V_FIVE_BETA_EMAIL_TEMPLATE, CAP_V_FIVE_EMAIL_TEMPLATE, CAP_V_FIVE_GA_SWITCH
from billing.models import PartnerSubscription
from billing.providers.chargebee import SUBSCRIPTION_ACTIVE
from billing.providers.chargebee.plans import ALL_CEP_PLANS, ALL_CE_PLANS, ALL_GDPR_PLANS
from billing.utils import generate_daily_subs_updates_csv_for_dist
from common.models import EmailAlreadySent, EmailLog
from common.tasks import GlobalRateLimitTask
from common.utils import get_http_protocol_url_name, reverse_absolute
from distributors.models import Distributor
from emails.decorators import vodafone_emails
from emails.sendgrid_client import CyberSmartSendGridAPIClient
from emails.utils import (
    get_weekly_security_email_dynamic_substitutions,
    get_contact_link,
    get_logo_header_html,
    get_translated_template_id,
)
from organisations.models import Organisation, OrganisationCertification
from partners.models import PartnerUser, Partner
from rulebook.models import CERTIFICATES, CYBER_ESSENTIALS, GDPR, IssuedCertification

logger = get_task_logger(__name__)

DATE_FORMAT = '%d/%m/%Y'
DATE_FORMAT_FULL_MONTH = '%d %B %Y'

RENEWAL_REPORT_URL_QUERY = '?renewals=1'

User = get_user_model()

ACTIVE_PROTECT_POLICIES_GROUP_NAME = 'ACTIVE_PROTECT_POLICIES'
BILLING_SUBSCRIPTION_CHANGE_GROUP_NAME = 'BILLING_SUBSCRIPTION_CHANGE'
COMPANY_RISK_REPORTING_GROUP_NAME = 'COMPANY_RISK_REPORTING'
CERTIFICATES_RENEWALS_GROUP_NAME = 'CERTIFICATES_RENEWALS'
CERTIFICATES_COMPLETION_GROUP_NAME = 'CERTIFICATES_COMPLETION'
ACTIVE_PROTECT_ACADEMY_GROUP_NAME = 'ACTIVE_PROTECT_ACADEMY'
ONBOARDING_GROUP_NAME = 'ONBOARDING'

UNSUBSCRIBE_GROUPS = {
    ACTIVE_PROTECT_POLICIES_GROUP_NAME: 71431,
    BILLING_SUBSCRIPTION_CHANGE_GROUP_NAME: 71428,
    COMPANY_RISK_REPORTING_GROUP_NAME: 57797,
    CERTIFICATES_RENEWALS_GROUP_NAME: 71434,
    CERTIFICATES_COMPLETION_GROUP_NAME: 71435,
    ACTIVE_PROTECT_ACADEMY_GROUP_NAME: 71432,
    ONBOARDING_GROUP_NAME: 71436,
}


def is_legacy_template(template_id):
    """
    Returns True if passed sendgrid email template id is legacy.
    :return: True or False
    :rtype: bool
    """
    return not template_id.startswith('d-')


@task(name="emails.tasks.send_html_email", queue="emails")
@vodafone_emails
def send_html_email(to_email, subject, template, context={}, **kwargs):
    html_content = render_to_string(template, context)
    text_content = strip_tags(html_content)
    msg = EmailMultiAlternatives(subject, text_content, settings.DEFAULT_FROM_EMAIL, [to_email])
    msg.ip_pool_name = 'Transactional'
    msg.categories = [Site.objects.get_current().domain,]
    msg.attach_alternative(html_content, "text/html")
    if msg.send():
        logger.info("Send HTML email to {}".format(to_email))
    else:
        logger.error("FAILED: Send HTML email to {}".format(to_email))


def get_non_empty_substitutions(substitutions):
    """
    Remove substitutions with empty values or empty strings.
    Sendgrid will not render the default value in the template substitution if the value provided is
    an empty string.
    Therefore, we filter out those substitutions before sending the request to Sendgrid.
    https://support.sendgrid.com/hc/en-us/articles/5493259652251-How-to-insert-a-recipient-s-first-name-in-your-emails
    """
    res = []
    for substitution in substitutions:
        if 'value' in substitution and substitution['value'] is not None and substitution['value'] != '':
            res.append(substitution)
    return res


@task(name="emails.tasks.api_template_email", queue="emails", bind=True, max_retries=3)
@vodafone_emails
def api_template_email(self, to_email, subject, template_id, substitutions=None, attachments=None, sender=None,
        custom_args=None, organisation_id=None, partner_id=None, unsubscribe_group=None, permission=None, **kwargs):
    if not substitutions:
        substitutions = []
    if not custom_args:
        custom_args = []

    sg = CyberSmartSendGridAPIClient(api_key=settings.SENDGRID_API_KEY)

    user = User.objects.filter(email=to_email).first()
    # skip sending emails to inactive users and trial users/organisations (created during signup and not activated)
    if user and (not user.is_active or is_trial_email(to_email)):
        return

    if organisation_id:
        organisation = Organisation.objects.get(id=organisation_id)
    if partner_id:
        partner = Partner.objects.get(id=partner_id)
    # Check user permissions
    if user and permission:
        if organisation_id:
            # set cache in order to be used by the RoleBackend to check permissions
            cache.set(f'current_org_id:{user.id}', organisation.secure_id, 120)
        if not user.has_perm(permission):
            logger.error(f"FAILED! User with email {to_email} does not have {permission} permission")
            return
    # <AUTHOR> <EMAIL>"
    if sender is None:
        send_from = Email(email=settings.DEFAULT_FROM_EMAIL)
    else:
        send_from = Email(email=sender)

    send_to = To(to_email)
    content = Content("text/html", "Hello")

    # add generic substitutions for all templates
    substitutions = substitutions + [
        {
            'id': 'contact_link',
            'value': get_contact_link()
        },
        {
            'id': 'environment_name',
            'value': settings.ENVIRONMENT_NAME
        },
    ]
    substitutions = substitutions + [
        {
            'id': 'logo_header_html',
            'value': get_logo_header_html(to_email)
        },
    ]
    substitutions = get_non_empty_substitutions(substitutions)


    if is_legacy_template(template_id):
        mail = Mail(from_email=send_from, subject=subject, to_emails=send_to, html_content=content)
        mail.template_id = template_id
        for sub in substitutions:
            mail.personalizations[0].add_substitution(Substitution(sub['id'], sub['value']))
    else:
        mail = Mail(from_email=send_from, subject=subject, html_content=content)
        if organisation_id or partner_id:
            default_language_code = organisation.settings.default_language if organisation_id else partner.default_language
            template_id = get_translated_template_id(default_language_code, template_id, sg)
        mail.template_id = template_id
        if unsubscribe_group:
            mail.asm = Asm(GroupId(unsubscribe_group))
        p = Personalization()
        p.add_to(send_to)

        bcc = kwargs.get('bcc', '')
        if bcc and to_email.lower() != bcc.lower():
            p.add_email(Bcc(bcc))

        cc = kwargs.get('cc', '')
        if cc and to_email.lower() != cc.lower():
            p.add_email(Cc(cc))

        p.dynamic_template_data = {new_dict['id']: new_dict['value'] for new_dict in substitutions}
        p.custom_args = custom_args
        mail.add_personalization(p)
    if unsubscribe_group is None:
        # Send this email even when user has unsubscribed from all emails
        mail.mail_settings = MailSettings(bypass_list_management=BypassListManagement(enable=True))
    mail.ip_pool_name = IpPoolName('Transactional')
    mail.add_category(Category(Site.objects.get_current().domain))
    if attachments and type(attachments) in (list, tuple):
        for att in attachments:
            attachment = Attachment(
                file_content=att["content"],
                file_type=att["type"],
                file_name=att["filename"],
                disposition=att["disposition"],
                content_id=att["content_id"]
            )
            mail.add_attachment(attachment)
    try:
        response = sg.send_email(mail.get())
    except HTTPError as exc:
        raise self.retry(exc=exc, countdown=2 * 60)
    else:
        if response.status_code == 202:
            newsletter_type = kwargs.get('newsletter_type', EmailLog.SENT_EMAIL)
            certification_id = kwargs.get('certification_id', None)
            EmailLog.objects.create(
                newsletter_type=newsletter_type,
                subject=subject,
                template_id=template_id,
                organisation_id=organisation_id,
                partner_id=partner_id,
                email=to_email,
                certification_id=certification_id
            )
            logger.info('''\n
            * Send template email to {0}\n
            * Template ID: \"{1}\"\n
            * Email title: \"{2}\"\n
            * Substitutions: {3}\n
            {4}'''.format(to_email, template_id, subject, substitutions, '*' * 100))
            logger.info(mail.get())
        else:
            logger.error(f"FAILED! send template email to {to_email}", extra={'error': response.body})


@task(base=GlobalRateLimitTask, max_retries=1, rate_limit='300/m')
def send_weekly_security_email(org_pk, newsletter_type):
    """ Weekly Security Admin Email sent to all Organisation Admins that are subscribed """
    organisation = Organisation.objects.get(pk=org_pk)
    substitutions = get_weekly_security_email_dynamic_substitutions(organisation)
    for org_user in organisation.get_subscribed_admin_users:
        substitutions.append({
            'id': 'first_name',
            'value': org_user.user.first_name
        })
        try:
            if newsletter_type == EmailLog.EVERYDAY_ADMIN_REPORT:
                if EmailLog.objects.filter(
                        newsletter_type=newsletter_type,
                        organisation=organisation,
                        email__iexact=org_user.user.email,
                        created__year=timezone.now().year,
                        created__month=timezone.now().month,
                        created__day=timezone.now().day,
                ).count():
                    raise EmailAlreadySent
            elif newsletter_type == EmailLog.EVERY_MONDAY_REPORT:
                if EmailLog.objects.filter(
                        newsletter_type=newsletter_type,
                        organisation=organisation,
                        email__iexact=org_user.user.email,
                        created__year=timezone.now().year,
                        created__month=timezone.now().month,
                        created__day=(timezone.now() - timedelta(
                            days=(timezone.now().weekday())
                        )).day,
                ).count():
                    raise EmailAlreadySent
            else:
                raise EmailAlreadySent
        except EmailAlreadySent:
            logger.info(f"Email already sent to {org_user.user.email}")
        else:
            subject = f'Your Security Updates: {organisation.name}'
            api_template_email.delay(
                to_email=org_user.user.email,
                subject=subject,
                template_id='d-235f2e90f5e04308a171001fa381844b',
                substitutions=substitutions,
                custom_args=[
                    {
                        'Name': 'Weekly Security Admin Email',
                        'Category': 'Weekly Email',
                        'Environment': settings.ENVIRONMENT_NAME,
                        'Audience': 'Everyone',
                    }
                ],
                organisation_id=organisation.id,
                unsubscribe_group=UNSUBSCRIBE_GROUPS['COMPANY_RISK_REPORTING'],
                permission=PEOPLE_AND_ORGANISATION_PERMISSION,
            )
            EmailLog.objects.create(
                newsletter_type=newsletter_type,
                subject=subject,
                organisation=organisation,
                email=org_user.user.email
            )


def certification_has_been_issued_recently(certification, within_days=90):
    """
    Returns True if passed certification has been issued within passed days otherwise returns False
    :param certification: organisation certification
    :type certification: organisations.OrganisationCertification
    :param within_days: days withing the certificate has been issued
    :type within_days: int
    :return: True or False
    :rtype: bool
    """
    return IssuedCertification.objects.filter(
        certificate=certification,
        date__gt=timezone.now() - timedelta(days=within_days),
    ).exists()


def check_log_and_send(users, subject, template_id, log_type, substitutions, certification):
    """
    Checks if an email was not sent today yet for specific user.
    And if so sends the email.
    :param users: organisation admins
    :type users: django.contrib.auth.User
    :param subject: email subject
    :type subject: str
    :param template_id: sendgrid email template ID
    :type template_id: str
    :param log_type: email log type
    :type log_type: int
    :param substitutions: sendgrid email template substitutions
    :type substitutions: list
    :param certification: organisation certification
    :type certification: organisations.OrganisationCertification
    :return: Nothing
    :rtype: None
    """
    for user in users:
        try:
            full_name = user.get_full_name()
            email = user.email
        except AttributeError:
            full_name = user.user.get_full_name()
            email = user.user.email

        # extend substitutions
        if log_type in (
                EmailLog.CERTIFICATION_RENEWAL_DIRECT_CUSTOMERS,
                EmailLog.CERTIFICATION_RENEWAL_DIRECT_PARTNERS
        ):
            substitutions.append(
                {'id': 'admin-name', 'value': full_name}
            )
        elif log_type == EmailLog.CERTIFICATION_RENEWAL_DIRECT_PARTNER_CUSTOMERS:
            substitutions.append(
                {'id': 'partner-admin-name', 'value': full_name}
            )
        elif log_type == EmailLog.CERTIFICATION_RENEWAL_DISTRIBUTOR_PARTNERS:
            substitutions.append(
                {'id': 'distributor-admin-name', 'value': full_name}
            )
        elif log_type == EmailLog.CERTIFICATION_RENEWAL_DISTRIBUTOR_PARTNER_CUSTOMERS:
            substitutions.append(
                {'id': 'distributor-admin-name', 'value': full_name}
            )

        # don't send anything if there is an email log which indicates
        # that this email was sent to this user today or if the certificate has been issued recently (90 days)
        if (email_has_been_sent_today(certification, email, log_type) or
                certification_has_been_issued_recently(certification=certification)):
            return

        api_template_email.delay(
            to_email=email,
            subject=subject,
            template_id=template_id,
            substitutions=substitutions,
            vodafone_customer=certification.organisation.is_vodafone_customer,
            organisation_id=certification.organisation.id
        )
        EmailLog.objects.create(
            newsletter_type=log_type,
            subject=subject,
            organisation=certification.organisation,
            certification=certification,
            email=email
        )


def email_has_been_sent_today(certification, email, log_type):
    """
    Returns True if there is an email log saved today for passed certification, email and log type
    otherwise returns False.
    :param certification: organisation certification
    :type certification: organisations.OrganisationCertification
    :param email: admin email
    :type email: str
    :param log_type: email log type
    :type log_type: int
    :return: True or False
    :rtype: bool
    """
    return EmailLog.objects.filter(
        newsletter_type=log_type,
        organisation=certification.organisation,
        certification=certification,
        email__iexact=email,
        created__year=timezone.now().year,
        created__month=timezone.now().month,
        created__day=timezone.now().day,
    ).exists()



@task
def certification_billing_renewal_report_emails(distributor_pk=None, partner_pk=None):
    """
    Sends renewal report emails to direct partners
    & distributors.
    :return: nothing
    :rtype: None
    """
    if not settings.IS_TEST:
        if distributor_pk:
            handle_distributor_reports(distributor_pk)
        elif partner_pk:
            handle_direct_partner_reports(partner_pk)
        else:
            handle_direct_partner_reports()
            handle_distributor_reports()


@task
def send_download_app_email(uuid_list, partner_user_name=None, reminder=False):
    """
    Sends an email to the download page for passed app users.
    :param uuid_list: list of app users uuid
    :type uuid_list: list
    :param reminder: if this emails is supposed to be a reminder (when user has been emailed before already)
    :type reminder: bool
    :return: nothing
    :rtype: None
    """
    if not settings.IS_TEST:
        for app_user in AppUser.objects.filter(uuid__in=uuid_list).select_related('organisation'):
            # send only for organisations that have software support
            if app_user.organisation.has_software_support:
                if app_user.organisation.is_vodafone_customer:
                    from vodafone import helpers, services
                    config = helpers.VodafoneConfig(credentials={})
                    vf_services = services.VodafoneServices(config=config)
                    vf_services.send_app_download_links([app_user])
                else:
                    # Advisor Armor specific case
                    if app_user.organisation.partner and app_user.organisation.partner.name == 'Advisor Armor':
                        from emails.notifications import send_download_app_email_to_advisor_armor
                        send_download_app_email_to_advisor_armor(app_user, partner_user_name)
                    # all other cases
                    else:
                        substitutions = [
                            {
                                'id': 'user_name',
                                'value': app_user.first_name
                            },
                            {
                                'id': 'download_active_protect',
                                'value': app_user.get_download_url()
                            },
                        ]
                        custom_args = [
                            {
                                'Name': 'Reminder email to end users to install CAP',
                                'Category': 'Onboarding',
                                'Environment': settings.ENVIRONMENT_NAME,
                                'Audience': app_user.organisation.get_email_audience(),
                            }
                        ]
                        template_id = 'd-f7f29a2b791f4a18af026ba23069ee8c'
                        subject = "Warning 🚨 You're still not protected 🚨"
                        if not reminder:
                            substitutions.append({
                                'id': 'organisation_name',
                                'value': app_user.organisation.name
                            })
                            template_id = 'd-e016636ab3094a43a99c3c03343a66f0'
                            subject = 'Protect your devices with CyberSmart Active Protect'
                            custom_args[0]['Name'] = 'CAP Email (All Enrolled Users & Admins)'
                        api_template_email.delay(
                            to_email=app_user.email,
                            subject=subject,
                            template_id=template_id,
                            substitutions=substitutions,
                            custom_args=custom_args,
                            organisation_id=app_user.organisation.id,
                        )


@task
def send_cap_v_five_pipeline_version_email(app_users):
    subject = 'Install your CyberSmart Active Protect Beta version'
    for app_user in AppUser.objects.filter(uuid__in=app_users).select_related('organisation'):
        substitutions = [
            {
                'id': 'user-name',
                'value': app_user.first_name
            },
            {
                'id': 'download-active-protect-beta',
                'value':
                    app_user.get_download_url(extra_url_kwargs={CAP_V_FIVE_BETA_SWITCH: 'true'})
                    if waffle.switch_is_active(CAP_V_FIVE_BETA_SWITCH)
                    else app_user.get_download_url()
            },
            {
                'id': 'organisation-name',
                'value': app_user.organisation.name
            },
        ]
        custom_args = [
            {
                'Name': 'Beta desktop version email to end users to install CAP',
                'Category': 'Beta Onboarding',
                'Environment': settings.ENVIRONMENT_NAME,
                'Audience': app_user.organisation.get_email_audience(),
            }
        ]
        if waffle.switch_is_active(CAP_V_FIVE_GA_SWITCH):
            email_template = CAP_V_FIVE_EMAIL_TEMPLATE
        else:
            email_template = CAP_V_FIVE_BETA_EMAIL_TEMPLATE
        api_template_email.delay(
            to_email=app_user.email,
            subject=subject,
            template_id=email_template,
            substitutions=substitutions,
            custom_args=custom_args,
            organisation_id=app_user.organisation.id,
        )


@task
def send_download_app_emails(organisation_pk, partner_user_name=None, reminder=False):
    """
    DEPRECATED: Safely remove after all messages get processed.

    Sends an email to the download page for passed organisation.
    Emails will be sent to all active users.
    :param organisation_pk: organisation primary key
    :type organisation_pk: int
    :param reminder: if this emails is supposed to be a reminder (when user has been emailed before already)
    :type reminder: bool
    :return: nothing
    :rtype: None
    """
    organisation = Organisation.objects.get(pk=organisation_pk)
    app_users = organisation.app_users.filter(active=True)
    send_download_app_email.delay(uuid_list=list(app_users.values_list('uuid', flat=True)),
                                  partner_user_name=partner_user_name, reminder=reminder)


@task
def cert_os_require_attention_status():
    """
    Sends notification email to organisation admins in case of some of their certification's survey
    needs clarification.
    :return: nothing
    :rtype: None
    """
    from emails.notifications import send_email_regarding_cert_requires_attention
    for cert in OrganisationCertification.objects.filter(
            status=OrganisationCertification.REQUIRES_ATTENTION,
    ).select_related('organisation'):
        # send to every organisation admin
        send_email_regarding_cert_requires_attention(cert.organisation.get_admin_users, cert)


def handle_distributor_reports(distributor_pk=None):
    distributors = Distributor.objects.exclude(name=settings.DEFAULT_CS_DIST_NAME)
    if distributor_pk:
        distributors = distributors.filter(pk=distributor_pk)

    for distributor in distributors:
        subscription_pks = []
        for partner in distributor.partners.all():
            if partner.is_billable:
                subscription_pks.extend(list(partner.billing.subscriptions.values_list('pk', flat=True)))

        active_subscriptions = PartnerSubscription.objects.filter(
            pk__in=subscription_pks,
            status=SUBSCRIPTION_ACTIVE,
            plan_id__in=ALL_CE_PLANS + ALL_CEP_PLANS + ALL_GDPR_PLANS,
            pause_date=None
        )
        date_range = [date.today(), date.today() + relativedelta(days=90)]
        non_bundle_subscriptions = active_subscriptions.filter(
            next_billing_at__range=date_range,
            contract_term_end_date=None,
        ).annotate(date_due=F("next_billing_at"))
        monthly_bundle_subscriptions = active_subscriptions.filter(
            contract_term_end_date__range=date_range,
            current_term_end_date__isnull=False,
        ).annotate(date_due=F("contract_term_end_date"))
        yearly_bundle_subscriptions = active_subscriptions.filter(
            current_term_end_date__range=date_range,
            contract_term_end_date=None,
        ).annotate(date_due=F("current_term_end_date"))
        subscriptions = (
                non_bundle_subscriptions | yearly_bundle_subscriptions | monthly_bundle_subscriptions
        ).distinct()

        if subscriptions:
            substitutions = get_distributor_renewal_report_data(subscriptions)
            substitutions.append({
                'id': 'billing_url',
                'value': get_report_url(reverse('distributors:subscriptions-overview'))
            })
            substitutions.append({'id': 'distributor_name', 'value': distributor.name})
            for user in distributor.users.all():
                api_template_email(
                    to_email=user.user.email,
                    substitutions=substitutions,
                    subject='It’s nearly time to renew...',
                    template_id='d-24daf20ecab74535bd386dc5ebb9de59',
                    unsubscribe_group=UNSUBSCRIBE_GROUPS['CERTIFICATES_RENEWALS'],
                )


def handle_direct_partner_reports(partner_pk=None):
    from partners.models import Partner

    direct_partners = Partner.objects.filter(distributor__name=settings.DEFAULT_CS_DIST_NAME)
    if partner_pk:
        direct_partners = direct_partners.filter(pk=partner_pk)

    for partner in direct_partners:
        if partner.is_billable:
            active_subscriptions = partner.billing.subscriptions.filter(
                status=SUBSCRIPTION_ACTIVE,
                plan_id__in=ALL_CE_PLANS + ALL_CEP_PLANS + ALL_GDPR_PLANS,
                pause_date=None
            )
            date_range = [date.today(), date.today() + relativedelta(days=60)]
            non_bundle_subscriptions = active_subscriptions.filter(
                    next_billing_at__range=date_range,
                    contract_term_end_date=None,
            ).annotate(date_due=F("next_billing_at"))
            monthly_bundle_subscriptions = active_subscriptions.filter(
                    contract_term_end_date__range=date_range,
                    current_term_end_date__isnull=False
            ).annotate(date_due=F("contract_term_end_date"))
            yearly_bundle_subscriptions = active_subscriptions.filter(
                    current_term_end_date__range=date_range,
                    contract_term_end_date=None,
            ).annotate(date_due=F("current_term_end_date"))
            subscriptions = (
                    non_bundle_subscriptions | yearly_bundle_subscriptions | monthly_bundle_subscriptions
            ).distinct()

            if subscriptions:
                substitutions = get_partner_renewal_report_data(subscriptions)
                substitutions.append({'id': 'partner_name', 'value': partner.name})
                for user in partner.users.all():
                    admin_name = user.user.first_name or "there"
                    substitutions_with_name = substitutions + [{'id': 'admin_name', 'value': admin_name}]
                    api_template_email(
                        to_email=user.user.email,
                        substitutions=substitutions_with_name,
                        subject='Update on customer subscriptions',
                        template_id='d-7270003a7f254017ad187dc340881029',
                        partner_id=partner.id,
                        permission=PEOPLE_AND_ORGANISATION_PERMISSION,
                    )


def get_report_url(url):
    return urljoin(
        '{0}://{1}'.format(get_http_protocol_url_name(), Site.objects.get_current().domain),
        url
    ) + RENEWAL_REPORT_URL_QUERY


def get_standards_from_plan_id(plan_id):
    standards = ''
    if plan_id in ALL_CE_PLANS:
        standards += str(_('Cyber Essentials, '))
    if plan_id in ALL_CEP_PLANS:
        standards += str(_('Cyber Essentials Plus, '))
    if plan_id in ALL_GDPR_PLANS:
        standards += str(_('Privacy Toolbox, '))

    return standards[:-2]


def get_partner_renewal_report_data(subscriptions):
    return [
        {
            'id': 'next_thirty',
            'value': [
                {
                    'org_name': sub.organisation.name,
                    'type': get_standards_from_plan_id(sub.plan_id),
                    'date': sub.date_due.strftime(DATE_FORMAT)
                } for sub in subscriptions.filter(
                    date_due__range=[
                        date.today(), date.today() + relativedelta(days=30)
                    ]
                ).order_by('date_due')
            ]
        },
        {
            'id': 'next_sixty',
            'value': [
                {
                    'org_name': sub.organisation.name,
                    'type': get_standards_from_plan_id(sub.plan_id),
                    'date': sub.date_due.strftime(DATE_FORMAT)
                } for sub in subscriptions.filter(
                    date_due__range=[
                        date.today() + relativedelta(days=31), date.today() + relativedelta(days=60)
                    ]
                ).order_by('date_due')
            ]
        },
    ]


def get_cert_renewal_data(certs):
    next_sixty = certs.filter(
                    renewal_end_date__range=[date.today() + relativedelta(days=31),
                                             date.today() + relativedelta(days=60)]
                ).order_by('renewal_end_date')
    return [
        {
            'id': 'next_thirty',
            'value': [
                {
                    'org_name': cert.organisation.name,
                    'type': CERTIFICATES.get(cert.type),
                    'date': cert.renewal_end_date.strftime(DATE_FORMAT)
                } for cert in certs.filter(
                    renewal_end_date__range=[date.today(),
                                             date.today() + relativedelta(days=30)]
                ).order_by('renewal_end_date')
            ]
        },
        {
            'id': 'next_sixty',
            'value': [
                {
                    'org_name': cert.organisation.name,
                    'type': CERTIFICATES.get(cert.type),
                    'date': cert.renewal_end_date.strftime(DATE_FORMAT)
                } for cert in next_sixty
            ]
        },
        {
            'id': 'total_certs_expire_next_sixty',
            'value': next_sixty.count()
        },
    ]


def get_distributor_renewal_report_data(subscriptions):
    return [
        {
            'id': 'next_thirty',
            'value': {
                'ce_count': subscriptions.filter(
                    date_due__range=[
                        date.today(), date.today() + relativedelta(days=30)
                    ],
                    plan_id__in=ALL_CE_PLANS
                ).count(),
                'cep_count': subscriptions.filter(
                    date_due__range=[
                        date.today(), date.today() + relativedelta(days=30)
                    ],
                    plan_id__in=ALL_CEP_PLANS
                ).count(),
                'gdpr_count': subscriptions.filter(
                    date_due__range=[
                        date.today(), date.today() + relativedelta(days=30)
                    ],
                    plan_id__in=ALL_GDPR_PLANS
                ).count()
            }
        },
        {
            'id': 'next_sixty',
            'value': {
                'ce_count': subscriptions.filter(
                    date_due__range=[
                        date.today() + relativedelta(days=31), date.today() + relativedelta(days=60)
                    ],
                    plan_id__in=ALL_CE_PLANS
                ).count(),
                'cep_count': subscriptions.filter(
                    date_due__range=[
                        date.today() + relativedelta(days=31), date.today() + relativedelta(days=60)
                    ],
                    plan_id__in=ALL_CEP_PLANS
                ).count(),
                'gdpr_count': subscriptions.filter(
                    date_due__range=[
                        date.today() + relativedelta(days=31), date.today() + relativedelta(days=60)
                    ],
                    plan_id__in=ALL_GDPR_PLANS
                ).count()
            }
        },
        {
            'id': 'next_ninety',
            'value': {
                'ce_count': subscriptions.filter(
                    date_due__range=[
                        date.today() + relativedelta(days=61), date.today() + relativedelta(days=90)
                    ],
                    plan_id__in=ALL_CE_PLANS
                ).count(),
                'cep_count': subscriptions.filter(
                    date_due__range=[
                        date.today() + relativedelta(days=61), date.today() + relativedelta(days=90)
                    ],
                    plan_id__in=ALL_CEP_PLANS
                ).count(),
                'gdpr_count': subscriptions.filter(
                    date_due__range=[
                        date.today() + relativedelta(days=61), date.today() + relativedelta(days=90)
                    ],
                    plan_id__in=ALL_GDPR_PLANS
                ).count()
            }
        }
    ]


def get_renewal_certs(types: list[int]) -> QuerySet[OrganisationCertification]:
    """
    Returns all certifications that are due for renewal.
    :param types: list of certification types (ex. [CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS])
    """
    return OrganisationCertification.objects.filter(
        status__in=[*OrganisationCertification.CERTIFIED_STATUSES, OrganisationCertification.EXPIRED],
        version__type__type__in=types,
        renewal_certificate__isnull=True
    )


@task
def remind_direct_orgs_of_uninstalled_cap():
    """ Send email to all direct organisation admins if they have users that have not installed CAP yet """
    if not settings.IS_TEST:
        for organisation in Organisation.objects.filter(
                partner__distributor__name=settings.DEFAULT_CS_DIRECT_DIST_NAME,
                software_support=True,
        ):
            # if there are any users that have not installed CAP then send email to each org admin
            if organisation.installed_users_count != organisation.enrolled_users_count \
                    and organisation.has_software_support and not organisation.is_bulk_enrollment_type:
                for admin in organisation.get_admin_users:
                    substitutions = [
                        {
                            'id': 'admin_name',
                            'value': admin.first_name
                        },
                        {
                            'id': 'go_to_dashboard',
                            'value': organisation.absolute_url
                        },
                    ]
                    api_template_email.delay(
                        to_email=admin.email,
                        subject='Some of your users are not protected!',
                        template_id='d-d72356d1ca72459fa0534326e6c79db2',
                        substitutions=substitutions,
                        custom_args=[
                            {
                                'Name': 'Reminder Email To Admin',
                                'Category': 'Onboarding',
                                'Environment': settings.ENVIRONMENT_NAME,
                                'Audience': 'Direct Organisations',
                            }
                        ],
                        organisation_id=organisation.id,
                        permission=DEVICES_PERMISSION,
                    )


@task
def remind_direct_orgs_of_not_started_certificates():
    """ Send email to all direct organisation admins if they have certifications that have not been started yet """
    if not settings.IS_TEST:
        types = [CYBER_ESSENTIALS]

        certificates = OrganisationCertification.objects.filter(
            organisation__partner__distributor__name=settings.DEFAULT_CS_DIRECT_DIST_NAME,
            status=OrganisationCertification.NOT_STARTED,
            version__type__type__in=types
        )

        for certificate in certificates.select_related('organisation', 'version', 'version__type'):
            organisation = certificate.organisation
            # only send email if org has subscription for the cert
            has_ce_sub = all([certificate.type == CYBER_ESSENTIALS, organisation.has_certification_ce_subscription])
            has_gdpr_sub = all([certificate.type == GDPR, organisation.has_certification_gdpr_subscription])
            if has_ce_sub or has_gdpr_sub:
                for admin in organisation.get_admin_users:
                    substitutions = [
                        {
                            'id': 'admin_name',
                            'value': admin.first_name
                        },
                        {
                            'id': 'certificate_name',
                            'value': certificate.title
                        },
                        {
                            'id': 'start_questionnaire_name',
                            'value': certificate.absolute_url
                        },
                    ]
                    api_template_email.delay(
                        to_email=admin.email,
                        subject='Time to get certified!',
                        template_id='d-106637c5912541d3b0c5bb3959ec2429',
                        substitutions=substitutions,
                        custom_args=[
                            {
                                'Name': 'Survey hasn’t been started for 3 workings days',
                                'Category': 'Onboarding',
                                'Environment': settings.ENVIRONMENT_NAME,
                                'Audience': 'Direct Organisations',
                            }
                        ],
                        organisation_id=organisation.id,
                        unsubscribe_group=UNSUBSCRIBE_GROUPS['CERTIFICATES_COMPLETION'],
                        permission=CERTIFICATES_AND_INSURANCE_PERMISSION
                    )


@task
def remind_direct_orgs_of_incomplete_survey():
    """ Send email to all direct organisation admins if they
    have surveys that have not been completed in the past 5 business days """
    if not settings.IS_TEST:
        from pandas.tseries.offsets import BDay
        # get the date 5 business days ago
        date_5_days_ago = timezone.now().date() - BDay(5)
        types = [CYBER_ESSENTIALS]

        certificates = OrganisationCertification.objects.filter(
            organisation__partner__distributor__name=settings.DEFAULT_CS_DIRECT_DIST_NAME,
            status=OrganisationCertification.IN_SURVEY,
            version__type__type__in=types,
            survey__datetime_started__lte=date_5_days_ago
        )
        for certificate in certificates.select_related('organisation', 'version', 'version__type'):
            organisation = certificate.organisation
            # only send email if org has subscription for the cert
            has_ce_sub = all([certificate.type == CYBER_ESSENTIALS, organisation.has_certification_ce_subscription])
            has_gdpr_sub = all([certificate.type == GDPR, organisation.has_certification_gdpr_subscription])
            if has_ce_sub or has_gdpr_sub:
                for admin in organisation.get_admin_users:
                    substitutions = [
                        {
                            'id': 'admin_name',
                            'value': admin.first_name
                        },
                        {
                            'id': 'certification_name',
                            'value': certificate.title
                        },
                        {
                            'id': 'complete_questionnaire',
                            'value': certificate.absolute_url
                        },
                    ]
                    api_template_email.delay(
                        to_email=admin.email,
                        subject=f'{certificate.title} needs completing',
                        template_id='d-145704d4b167460596b8aae7cca385b7',
                        substitutions=substitutions,
                        custom_args=[
                            {
                                'Name': 'Incompleted Survey',
                                'Category': 'Certifications',
                                'Environment': settings.ENVIRONMENT_NAME,
                                'Audience': 'Direct Organisations',
                            }
                        ],
                        organisation_id=organisation.id,
                        permission=CERTIFICATES_AND_INSURANCE_PERMISSION,
                    )


@task
def remind_direct_orgs_to_book_cep_audit(override_days=False):
    """ Sends an email to all the direct org admin users regarding the booking of the CEP audit after
    the organisation being CE certified and has a CEP subscription. """
    if not settings.IS_TEST:
        from emails.notifications import send_email_to_book_cep_audit
        days_to_remind = [7, 14, 21, 28, 35, 42, 49, 56, 63, 70, 77, 84]
        for certificate in OrganisationCertification.objects.filter(
                organisation__partner__distributor__name=settings.DEFAULT_CS_DIRECT_DIST_NAME,
                status__in=OrganisationCertification.CERTIFIED_STATUSES,
                version__type__type=CYBER_ESSENTIALS,
                issued_certification__date__gte=timezone.now() - relativedelta(days=90),
        ).select_related('organisation', 'issued_certification'):
            time_after_certification = certificate.issued_certification.date
            # get the number of days it has passed after certification
            diff_days = (timezone.now().date() - time_after_certification.date()).days
            organisation = certificate.organisation
            # only send if active CEP subscription and no audit date has been set
            # send weekly for 12 weeks after CE certification
            allowed_days = (diff_days in days_to_remind) or override_days
            if allowed_days and organisation.is_eligible_for_ce_plus_audit_booking_email:
                send_email_to_book_cep_audit(organisation.get_admin_users, organisation=organisation)


@task
def direct_orgs_ce_is_overdue_reminders():
    """ Sends an email to all the direct org admin users informing them
    about the CE certificate being expired, and the start of the new version is overdue"""
    if not settings.IS_TEST:
        from emails.notifications import send_email_regarding_overdue_ce
        current_date = timezone.now().date()
        days_to_renewal = [60, 30, 21, 14, 10, 7, 1]
        for days in days_to_renewal:
            date_x_days_ago = current_date - relativedelta(days=days)
            for certificate in OrganisationCertification.objects.filter(
                    organisation__partner__distributor__name=settings.DEFAULT_CS_DIRECT_DIST_NAME,
                    status=OrganisationCertification.EXPIRED,
                    version__type__type=CYBER_ESSENTIALS,
                    renewal_end_date__lte=date_x_days_ago,
                    renewal_end_date__gte=date_x_days_ago,
            ).select_related('organisation'):
                # check if the customer is already progressing with the survey, if so, then we do not send emails
                if not certificate.has_newest_cert_actively_in_survey and certificate.newer_version_exists:
                    send_email_regarding_overdue_ce(
                        certificate.organisation.get_admin_users,
                        renewal_end_date=certificate.renewal_end_date.strftime(DATE_FORMAT_FULL_MONTH),
                        # get the latest CE certificate URL in case the cert migration task has already run
                        review_questionnaire=certificate.organisation.ce_certification.absolute_url,
                        organisation=certificate.organisation
                    )


@task
def users_policies_notify(organisation=None):
    """
    Send a letter to users who have overdue policies. Triggered at the beginning of each week
    """
    if settings.IS_TEST:
        return

    partner_emails = PartnerUser.objects.all().values_list('user__email', flat=True)

    pending_users = AppUser.objects.filter(
        active=True,
        analytics__agreed_policies_count__lt=F("analytics__active_policies_count"),
        email__isnull=False
    ).exclude(
        email__in=partner_emails,
        organisation__is_partner_org=False
    ).exclude(
        email__startswith=Organisation.BULK_DEPLOY_PREFIX,
        organisation__is_partner_org=False
    ).exclude(
        organisation__bulk_install=True,
        organisation__settings__uba_enabled=False
    ).select_related('organisation')
    if organisation:
        pending_users = pending_users.filter(organisation=organisation)
    today = timezone.now().date()
    for user in pending_users:
        policies = user.pending_policies(exclude_read=False)
        if policies:
            organisation = user.organisation
            substitutions = [
                {
                    'id': 'policies',
                    'value': [
                        {
                            'name': policy_version.policy.name,
                            'days': (today - policy_version.created.date()).days
                        }
                        for policy_version in policies
                    ]
                },
                {
                    'id': 'app_user_name',
                    'value': user.first_name or "there"
                },
            ]
            api_template_email.delay(
                to_email=user.email,
                subject='You\'re behind...',
                template_id='d-bf5a3960a18f4b27ac2cef5b49deafe0',
                substitutions=substitutions,
                custom_args=[
                    {
                        'Name': 'You\'re behind...',
                        'Category': 'Active Protect',
                        'Environment': settings.ENVIRONMENT_NAME,
                        'Audience': organisation.get_email_audience(),
                    }
                ],
                vodafone_customer=organisation.is_vodafone_customer,
                organisation_id=organisation.pk,
                unsubscribe_group=UNSUBSCRIBE_GROUPS['ACTIVE_PROTECT_POLICIES'],
            )


def distributor_subscription_change(subscription, subscription_changes):
    if not settings.IS_TEST:
        organisation = subscription.organisation
        distributor = organisation.partner.distributor
        if distributor.name not in [settings.DEFAULT_CS_DIST_NAME, settings.DEFAULT_CS_DIRECT_DIST_NAME, "Brigantia"]:
            logo_url = distributor.absolute_dark_logo_url
            for user in distributor.users.all():
                substitutions = [
                    {
                        'id': 'distributor_user_name',
                        'value': user.user.first_name or "there"
                    },
                    {
                        'id': 'logo_url',
                        'value': logo_url or ''
                    },
                    {
                        'id': 'organisation_name',
                        'value': subscription.organisation.name
                    },
                    {
                        'id': 'subscription_changes',
                        'value': subscription_changes
                    },
                    {
                        'id': 'subscriptions_link',
                        'value': reverse_absolute("distributors:subscriptions-overview", None)
                    }
                ]
                api_template_email.delay(
                    to_email=user.user.email,
                    subject=f"{organisation.name}'s CyberSmart subscription has changed",
                    template_id='d-6d9e706b62cd48cfb11430269bfd7590',
                    substitutions=substitutions,
                    custom_args=[
                        {
                            'Name': f"{organisation.name}'s CyberSmart subscription has changed",
                            'Category': 'platform',
                            'Environment': settings.ENVIRONMENT_NAME,
                            'Audience': organisation.get_email_audience(),
                        }
                    ],
                    vodafone_customer=organisation.is_vodafone_customer,
                    organisation_id=organisation.pk,
                    unsubscribe_group=UNSUBSCRIBE_GROUPS['BILLING_SUBSCRIPTION_CHANGE'],
                    permission=PEOPLE_AND_ORGANISATION_PERMISSION,
                )


def partner_subscription_change(subscription, subscription_changes):
    if not settings.IS_TEST:
        organisation = subscription.organisation
        partner = organisation.partner
        if partner.is_direct_partner:
            for user in partner.users.all():
                substitutions = [
                    {
                        'id': 'partner_user_name',
                        'value': user.user.first_name or "there"
                    },
                    {
                        'id': 'organisation_name',
                        'value': subscription.organisation.name
                    },
                    {
                        'id': 'subscription_changes',
                        'value': subscription_changes
                    },
                    {
                        'id': 'subscriptions_link',
                        'value': reverse_absolute("partners:subscriptions-overview", None)
                    }
                ]
                api_template_email.delay(
                    to_email=user.user.email,
                    subject=f"{organisation.name}'s CyberSmart subscription has changed",
                    template_id='d-9af9c0ce1b424f3db5c5f9fa9bc337e9',
                    substitutions=substitutions,
                    custom_args=[
                        {
                            'Name': f"{organisation.name}'s CyberSmart subscription has changed",
                            'Category': 'platform',
                            'Environment': settings.ENVIRONMENT_NAME,
                            'Audience': organisation.get_email_audience(),
                        }
                    ],
                    vodafone_customer=organisation.is_vodafone_customer,
                    organisation_id=organisation.pk,
                    unsubscribe_group=UNSUBSCRIBE_GROUPS['BILLING_SUBSCRIPTION_CHANGE'],
                    permission=PEOPLE_AND_ORGANISATION_PERMISSION,
                )


@task
def send_challenge_email(email, confirmed=True):
    """
    Generates a random 2FA token and emails it to the user.

    :param email: user's Email to send the challenge to.
    :type email: str
    :param confirmed: whether the email is confirmed or not.
    :type confirmed: boolen

    """
    if email_device := EmailDevice.objects.filter(
        user__email=email, confirmed=confirmed).first():

        email_device.generate_token(
            valid_secs=settings.OTP_EMAIL_TOKEN_VALIDITY)

        substitutions = [
            {
                'id': 'token',
                'value': email_device.token
            }
        ]

        api_template_email(
            to_email=email,
            subject='OTP Token',
            template_id='d-9ac8eff6c2a446ea960856843b96212a',
            substitutions=substitutions
        )


@task
def delete_old_email_logs():
    """ Deletes old email logs and archives when in production environment """
    months = None
    if settings.IS_DEVELOP or settings.IS_STAGE:
        months = 1
    elif settings.IS_PROD:
        months = 3

    if months:
        old_logs = EmailLog.objects.filter(created__lt=timezone.now() - relativedelta(months=months))
        values = ['email', 'subject', 'newsletter_type', 'organisation_id', 'app_install_id',
                  'certification_id', 'partner_user_id', 'partner_id']

        # archive logs only for Prod environment
        if settings.IS_PROD:
            logs_to_archive = []
            for email_log in old_logs.values(*values):
                logs_to_archive.append(
                    EmailLogArchive(**email_log)
                )
            try:
                EmailLogArchive.objects.bulk_create(logs_to_archive)
            except Exception as ex:
                logger.error(f"Couldn't bulk create EmailLogArchive; due to {ex} ")
        # delete old logs
        old_logs.delete()

def get_base_substitutions_for_cep_audit_email(organisation, requester, is_rebooking):
    return [
        {
            'id': 'organisation-requested-name',
            'value': organisation.name
        },
        {
            'id': 'organisation-requested-size',
            'value': organisation.size
        },
        {
            'id': 'cep-request-date',
            'value': localtime(organisation.cep_audit_date).strftime(DATE_FORMAT)
        },
        {
            'id': 'rebooking-prefix',
            'value': 're-' if is_rebooking else ''
        },
        {
            'id': 'rebooking-prefix-title',
            'value': 'Re-' if is_rebooking else ''
        },
        {
            'id': 'requested-persons-name',
            'value': requester.get_full_name()
        }
    ]


def send_request_cep_audit_email_for_partner(organisation, requester_user_id, is_rebooking):
    partner = organisation.partner
    requester = User.objects.get(id=requester_user_id)
    users = partner.users.all()

    bcc = "<EMAIL>" if settings.IS_PROD else None
    substitutions = [
            {
                'id': 'partner-name',
                'value': partner.name or "there"
            }
        ]
    substitutions += get_base_substitutions_for_cep_audit_email(organisation, requester, is_rebooking)

    for user in users:

        api_template_email.delay(
            to_email=user.user.email,
            subject='CEP Audit Request',
            template_id='d-ba8480aec1414971a539a7257ef297c0',
            substitutions=substitutions,
            bcc=bcc,
            partner_id=partner.id,
            permission=CERTIFICATES_AND_INSURANCE_PERMISSION,
        )


def send_request_cep_audit_email_for_distributor(organisation, requester_user_id, is_rebooking):
    distributor = organisation.partner.distributor
    requester = User.objects.get(id=requester_user_id)
    users = distributor.users.all()
    bcc = "<EMAIL>" if settings.IS_PROD else None

    substitutions = [
            {
                'id': 'distributor-name',
                'value': distributor.name or "there"
            },
            {
                'id': 'partner-requested-name',
                'value': organisation.partner.name
            }
        ]
    substitutions += get_base_substitutions_for_cep_audit_email(organisation, requester, is_rebooking)

    for user in users:
        api_template_email.delay(
            to_email=user.user.email,
            subject='CEP Audit Request',
            template_id='d-d1e907c4451f421e9fcb72648ab4c8ec',
            substitutions=substitutions,
            bcc=bcc,
        )


@task
def send_request_cep_audit_email(org_id, requester_user_id, is_rebooking=False):
    with contextlib.suppress(ObjectDoesNotExist):
        organisation = Organisation.objects.get(id=org_id)

        send_request_cep_audit_email_for_partner(organisation, requester_user_id, is_rebooking)
        send_request_cep_audit_email_for_distributor(organisation, requester_user_id, is_rebooking)


@task
def send_distributor_subscriptions_updates_email():
    """
    Sends an email to all distributors that have opted in
    for the last 24 hours updates on subscriptions changes.
    """

    if settings.IS_TEST:
        return

    distributors = Distributor.objects.filter(
        email_subscriptions_daily_updates=True
    )

    for dist in distributors:

        file = generate_daily_subs_updates_csv_for_dist(dist.id)

        cc = "<EMAIL>" if settings.IS_PROD else None
        substitutions = [
            {
                'id': 'distributor-name',
                'value': dist.name
            },
            {
                'id': 'subscriptions-change-log',
                'value': file.full_path_url if file else ''
            }
        ]
        emails = dist.users.values_list('user__email', flat=True)

        for email in emails:
            api_template_email.delay(
                to_email=email,
                subject='Daily Subscrptions Changes',
                template_id='d-4e14f0b5288f4f9ca369d0dfb2b51357',
                substitutions=substitutions,
                cc=cc
            )

@task
def send_failed_login_notification(email: str) -> None:
    """
    Send an email notification to a user when they have failed to login after a specified number of attempts.

    Args:
        email (str): The email of the user who failed to login.
    """
    cache_key = f'failed_login_notification_{email}'

    # Do not send the notification if it has already been sent
    if cache.get(cache_key):
        return

    try:
        user = get_user_model().objects.get(email=email)
    except ObjectDoesNotExist:
        logger.error(f"User with email {email} not found.")
        return

    substitutions = [
        {
            'id': 'user_name',
            'value': user.first_name or user.email
        }
    ]

    api_template_email.delay(
        to_email=user.email,
        subject='Unsuccessful login attempts',
        template_id='d-24bff1cc90264fc7ae4e86c4a8162adb',
        substitutions=substitutions
    )

    logger.info(f"Login notification sent to user {email}.")
    cache.set(cache_key, True, timeout=app_settings.LOGIN_ATTEMPTS_TIMEOUT)
