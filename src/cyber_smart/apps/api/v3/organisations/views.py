from datetime import datetime
from typing import Optional

from django.core.exceptions import PermissionDenied
from django.http import HttpRequest
from django.utils import timezone
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from oauth2_provider.contrib.rest_framework.authentication import OAuth2Authentication
from oauth2_provider.contrib.rest_framework.permissions import TokenHasScope
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.generics import ListAPIView, GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from api.paginators import SimpleAPIViewPagination
from api.v3.organisations.serializers import (
    OrganisationCertificationSerializer, SecurityControlSerializer,
    OrganisationFeaturesSerializer
)
from api.v3.permissions import IsAppUser
from appusers.models import AppInstall, AppInstallCheckStatus, AppUser
from appusers.templatetags.appusers import app_install_url
from beta_features.utils import get_beta_app_install_queryset_filter
from dashboard.mixins import CheckReportGetterMixin
from dashboard.utils import get_applications_responses
from organisations.models import get_organisations, Organisation, OrganisationCertification
from partners.api_throttling import PartnerRateThrottle
from partners.utils import PartnerOAuthApplicationMixin
from rulebook.models import CERTIFICATE_TYPES

CERTIFICATION_STATUSES = dict(map(reversed, dict(OrganisationCertification.STATUSES).items()))
CERTIFICATION_TYPES = {value.lower(): key for key, value in dict(CERTIFICATE_TYPES).items()}


class OrganisationFailedCheckResponses(CheckReportGetterMixin, APIView):
    http_method_names = ['get']
    permission_classes = [IsAuthenticated]

    def get(self, request: HttpRequest, org_id: str, check_id: int):
        """
        Get failed responses for a given organisation and check_id, with optional device_type query param
        """
        user = request.user
        organisation = get_organisations(user, org_id)
        if not organisation:
            raise PermissionDenied()

        failed_responses = self.__get_failed_responses(
            organisation, check_id, self.get_device_type()
        )

        return Response({
            "failed_responses": failed_responses
        }, status=status.HTTP_200_OK)

    def __cleanup_response(self, response: dict) -> dict:
        """
        Cleans up response by removing unnecessary fields.
        """
        return {
            key: value
            for key, value in response.items()
            if key
            in {
                "report__app_install__caption",
                "report__app_install__os__title",
                "last_os_user_username",
                "last_os_user_domain",
                "report__app_install__app_user__email",
                "report__app_install__hostname",
                "report__created",
                "latest_status_changed",
                "app_install_url",
                "is_beta",
            }
        }

    def __prepare_responses(self, organisation: Organisation, failed_responses: list, beta_installs_ids: list[int]) -> list:
        """
        Adds additional information to failed_responses, mainly AppInstall URL.
        If beta installs are present, adds is_beta field to failed_responses.
        """
        add_beta_information = len(beta_installs_ids) != 0
        purged_responses = []
        for response in failed_responses:
            response['app_install_url'] = app_install_url(
                organisation.secure_id,
                response['report__app_install__app_user__uuid'],
                response['report__app_install__device_id'],
                response['report__app_install__serial_number'],
            )
            if add_beta_information:
                response['is_beta'] = response['report__app_install__id'] in beta_installs_ids
            purged_responses.append(self.__cleanup_response(response))

        return purged_responses

    def __get_failed_responses(self, organisation: Organisation, check_id: int, device_type: str|None) -> list:
        """
        Get failed responses for a given organisation, check_id and device_type.
        """
        installs_by_device = self.get_installs_by_device_type(
            organisation.secure_id, device_type
        ).values_list("id", flat=True)

        failed_responses = list(get_applications_responses(
            org_pk=organisation.pk,
            order=(
                "app_check__pk",
                "report__app_install__device_id",
                "report__app_install__serial_number",
                "-report__created",
            ),
            distinct=("app_check__pk", "report__app_install__device_id", "report__app_install__serial_number"),
            fail_values=(
                "pk",
                "app_check__pk",
                "report__app_install__os__title",
                "report__app_install__device_id",
                "report__app_install__app_user__email",
                "report__app_install__app_user__uuid",
                "report__app_install__serial_number",
                "report__app_install__caption",
                "report__app_install__id",
                "report__app_install__hostname",
                "last_os_user_username",
                "last_os_user_domain",
                "report__created",
                "report__app_install__id",
                "latest_status_changed",
            ),
            return_passed=False,
            report_filters={
                "app_install__app_user__active": True,
            },
            check_result_filters={"app_check__pk__in": [check_id]},
        ).filter(report__app_install__id__in=installs_by_device))

        beta_installs = (
            AppInstall.objects.filter(
                get_beta_app_install_queryset_filter(),
                app_user__organisation=organisation
            ).values_list("id", flat=True)
        )
        failed_responses = self.__prepare_responses(organisation, failed_responses, beta_installs)

        return failed_responses


class OrganisationCertificationAPIView(PartnerOAuthApplicationMixin, ListAPIView):
    search_fields = ['status', 'type', 'organisation']
    serializer_class = OrganisationCertificationSerializer
    authentication_classes = [OAuth2Authentication]
    permission_classes = [TokenHasScope]
    required_scopes = ['read']
    pagination_class = SimpleAPIViewPagination
    throttle_classes = [PartnerRateThrottle]

    def get_filtering_params(self):
        filtering_params = {}
        if cert_status := self.request._data.get('status'):
            filtering_params['status'] = cert_status
        if cert_type := self.request._data.get('type'):
            filtering_params['version__type__type'] = cert_type
        if organisation := self.request._data.get('organisation'):
            filtering_params['organisation__name__iexact'] = organisation
        return filtering_params

    def validate_query_params(self):
        statuses_options = list(CERTIFICATION_STATUSES.keys())
        cert_types_options = list(CERTIFICATION_TYPES.keys())
        cert_type_input = self.request.query_params.get('type', '')
        cert_type = CERTIFICATION_TYPES.get(cert_type_input.lower())
        status_input = self.request.query_params.get('status', '')
        cert_status = CERTIFICATION_STATUSES.get(status_input.capitalize())

        if status_input and not cert_status:
            raise ValueError(f"Invalid status: {status_input}, valid statuses: {statuses_options}")
        if cert_type_input and not cert_type:
            raise ValueError(f"Invalid type: {cert_type_input}, valid types: {cert_types_options}")
        self.request._data = {
            'status': cert_status,
            'type': cert_type,
            'organisation': self.request.query_params.get('organisation')
        }

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'status',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='Certification status',
                enum=list(CERTIFICATION_STATUSES.keys())
            ),
            openapi.Parameter(
                'type',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='Certification type',
                enum=list(CERTIFICATION_TYPES.keys())
            ),
            openapi.Parameter(
                'organisation',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='Organisation name'
            ),
            openapi.Parameter(
                'page',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=False,
                description='Page number for pagination'
            ),
            openapi.Parameter(
                'page_size',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=False,
                description='Number of items per page'
            )
        ],
        responses={
            status.HTTP_200_OK: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    "count": openapi.Schema(type=openapi.TYPE_INTEGER, description="Total number of items"),
                    "next": openapi.Schema(type=openapi.TYPE_STRING, description="URL to the next page of results"),
                    "previous": openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description="URL to the previous page of results"
                    ),
                    "results": openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "organisation": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, properties={"name": openapi.Schema(type=openapi.TYPE_STRING), "uuid": openapi.Schema(type=openapi.TYPE_STRING)}
                                ),
                                "certification": openapi.Schema(
                                    type=openapi.TYPE_STRING, description="Certification title"
                                ),
                                "status": openapi.Schema(type=openapi.TYPE_STRING, description="Certification status"),
                                "expiry_date": openapi.Schema(
                                    type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE, description="Expiry date"
                                )
                            }
                        )
                    )
                }
            ),
            status.HTTP_401_UNAUTHORIZED: "Authentication credentials were not provided.",
            status.HTTP_403_FORBIDDEN: "Invalid credentials",
            status.HTTP_400_BAD_REQUEST: "Missing field or invalid field",
            status.HTTP_429_TOO_MANY_REQUESTS: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    "detail": openapi.Schema(type=openapi.TYPE_STRING, description="Rate limit exceeded message")
                }
            )
        },
    )
    def get(self, request, *args, **kwargs):
        try:
            self.validate_query_params()
        except ValueError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        return OrganisationCertification.objects.filter(
            organisation__partner__id=self.partner.id,
            **self.get_filtering_params()
        ).select_related('organisation', 'version').order_by('organisation__name', '-version__version_number')


class SecurityControlAPIView(PartnerOAuthApplicationMixin, ListAPIView):
    """
    API view for the Security Control Check Status.
    """
    search_fields = ["organisation", "user", "app", "security_control", "status"]
    serializer_class = SecurityControlSerializer
    authentication_classes = [OAuth2Authentication]
    permission_classes = [TokenHasScope]
    required_scopes = ["read"]
    pagination_class = SimpleAPIViewPagination
    throttle_classes = [PartnerRateThrottle]

    def get_filtering_params(self) -> dict:
        """
        Returns the filtering parameters for the queryset.
        """
        filtering_params = {}
        data = self.request._data
        if passing_status := data.get("status"):
            filtering_params["passing"] = True if passing_status == SecurityControlSerializer.PASSED else False
        if organisation := data.get("organisation"):
            filtering_params["app_install__app_user__organisation__name__iexact"] = organisation
        if user := data.get("user"):
            filtering_params["app_install__app_user__email__iexact"] = user
        if app := data.get("app"):
            filtering_params["app_install__hostname__iexact"] = app
        if security_control := data.get("security_control"):
            filtering_params["app_check__title__iexact"] = security_control
        if status_changed_date := data.get("status_changed_date"):
            filtering_params["status_changed__date"] = status_changed_date
        return filtering_params

    def validate_query_params(self) -> None:
        """
        Validates the query parameters
        """
        q_params = self.request.query_params

        status_options = SecurityControlSerializer.STATUSES_OPTIONS
        status_input = q_params.get("status", "")
        status_changed_date = self._parse_status_date(q_params.get("status_changed_date", ""))

        if status_input and status_input.capitalize() not in status_options:
            raise ValueError(f"Invalid status: {status_input}, valid statuses: {status_options}")

        self.request._data = {
            "organisation": q_params.get("organisation"),
            "user": q_params.get("user"),
            "app": q_params.get("app"),
            "security_control": q_params.get("security_control"),
            "status": status_input.capitalize(),
            "status_changed_date": status_changed_date
        }

    @staticmethod
    def _parse_status_date(date: str) -> Optional[datetime.date]:
        """
        Parses the `status changed date` from the query parameter.
        """
        date = date.strip().lower()
        if not date:
            return
        elif date == "today":
            return timezone.now().date()
        elif date == "yesterday":
            return timezone.now().date() - timezone.timedelta(days=1)
        else:
            try:
                return timezone.datetime.strptime(date, "%Y-%m-%d").date()
            except ValueError:
                raise ValueError("Invalid date format. Please provide date in the format 'YYYY-MM-DD'")

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "organisation",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="Organisation name"
            ),
            openapi.Parameter(
                "user",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="User email"
            ),
            openapi.Parameter(
                "app",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="App hostname"
            ),
            openapi.Parameter(
                "security_control",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="Security control title"
            ),
            openapi.Parameter(
                "status",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="Security control status",
                enum=SecurityControlSerializer.STATUSES_OPTIONS
            ),
            openapi.Parameter(
                "status_changed_date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="Status changed date",
                format="today/yesterday/YYYY-MM-DD"
            ),
            openapi.Parameter(
                "page",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=False,
                description="Page number for pagination"
            ),
            openapi.Parameter(
                "page_size",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=False,
                description="Number of items per page"
            )
        ],
        responses={
            status.HTTP_200_OK: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    "count": openapi.Schema(type=openapi.TYPE_INTEGER, description="Total number of results"),
                    "next": openapi.Schema(
                        type=openapi.TYPE_STRING, format=openapi.FORMAT_URI, description="Next page URL"
                    ),
                    "previous": openapi.Schema(
                        type=openapi.TYPE_STRING, format=openapi.FORMAT_URI, description="Previous page URL"
                    ),
                    "results": openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "organisation": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, properties={"name": openapi.Schema(type=openapi.TYPE_STRING), "uuid": openapi.Schema(type=openapi.TYPE_STRING)}
                                ),
                                "user": openapi.Schema(type=openapi.TYPE_STRING, description="User email"),
                                "app": openapi.Schema(type=openapi.TYPE_STRING, description="App hostname"),
                                "security_control": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description="Security control title"
                                ),
                                "status": openapi.Schema(type=openapi.TYPE_STRING, description="Security control status"),
                                "status_changed": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date",
                                    description="Status changed date"
                                ),
                                "last_check_in": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    description="Last check-in date"
                                ),
                                "url": openapi.Schema(type=openapi.TYPE_STRING, description="URL of the app install")
                            }
                        )
                    )
                }
            ),
            status.HTTP_401_UNAUTHORIZED: "Authentication credentials were not provided.",
            status.HTTP_403_FORBIDDEN: "Invalid credentials",
            status.HTTP_400_BAD_REQUEST: "Missing field or invalid field",
            status.HTTP_429_TOO_MANY_REQUESTS: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    "detail": openapi.Schema(type=openapi.TYPE_STRING, description="Rate limit exceeded message")
                }
            )
        },
    )
    def get(self, request, *args, **kwargs):
        try:
            self.validate_query_params()
        except ValueError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        return AppInstallCheckStatus.objects.filter(
            app_install__app_user__organisation__partner__id=self.partner.id,
            app_install__inactive=False,
            app_install__app_user__active=True,
            **self.get_filtering_params()
        ).select_related(
            "app_install__app_user__organisation", "app_install", "app_check"
        ).order_by("app_install__hostname")



class OrganisationFeaturesAPIView(GenericAPIView):
    authentication_classes = []
    permission_classes = [IsAppUser]
    serializer_class = OrganisationFeaturesSerializer

    def get_object(self):
        app_user = AppUser.objects.get(uuid=self.request.headers.get("APPUSER-UUID"))
        return app_user.organisation

    def validate_feature(self, feature, serialized_data):
        if feature and feature not in serialized_data.keys():
            raise ValidationError(f"Feature {feature} not found")

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "feature",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="Organisation feature value to retrieve. Will return all features if not provided",
                enum=list(OrganisationFeaturesSerializer().get_fields().keys())
            ),
        ],
        responses={
            status.HTTP_200_OK: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={field_name: openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            "value": openapi.Schema(type=openapi.TYPE_BOOLEAN, description="Is this feature enabled/disabled (True/False)?"),
                            "help_text": openapi.Schema(type=openapi.TYPE_STRING, description="Provides information to the user regarding the feature bing enabled/disabled")
                        }
                    ) for field_name in OrganisationFeaturesSerializer().get_fields().keys()}
            ),
            status.HTTP_403_FORBIDDEN: "Invalid credentials",
            status.HTTP_400_BAD_REQUEST: "Invalid feature query argument",
        },
    )
    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        serialized_data = serializer.data
        feature = request.query_params.get('feature')
        self.validate_feature(feature, serialized_data)
        # filter the response based on the feature query param
        if feature:
            return Response({feature: serialized_data[feature]})
        return Response(serialized_data)
