from django.utils.decorators import method_decorator
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.generics import RetrieveAPIView, get_object_or_404, UpdateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.v3.partners.serializers import PartnerSettingsSerializer
from api.v3.permissions import IsAppUser, IsPartnerUser
from appusers.models import AppUser


LOGO_SCHEMA = openapi.Schema(
    type=openapi.TYPE_STRING,
    description='URL of the logo or empty string if not available'
)


class PartnerLogosAPIView(RetrieveAPIView):
    authentication_classes = []
    permission_classes = [IsAppUser]

    def get_partner(self):
        app_user_uuid = self.request.headers.get('APPUSER-UUID')
        app_user = get_object_or_404(
            AppUser,
            uuid=app_user_uuid
        )
        return app_user.organisation.partner

    @method_decorator(decorator=swagger_auto_schema(
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Successful response with logos",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'light_logo': LOGO_SCHEMA,
                        'dark_logo': LOGO_SCHEMA
                    },
                    example={
                        'light_logo': 'https://example.com/media/light-logo.png',
                        'dark_logo': ''
                    }
                )
            ),
            status.HTTP_403_FORBIDDEN: 'When the authorization credentials are incorrect, there is no AppUser UUID '
                                       'provided in the headers, OR the provided UUID is invalid'
                                       ' or does not exist, OR the AppUser is not active.',
        },
        manual_parameters=[
            openapi.Parameter(
                'APPUSER-UUID',
                in_=openapi.IN_HEADER,
                type=openapi.TYPE_STRING,
                required=True,
                description='The UUID of the AppUser'
            ),
        ],
    ))
    def get(self, request, *args, **kwargs):
        """ Get the logos of the partner by passing the AppUser UUID header.
        If any of the logos are not available, it will return an empty string."""
        partner = self.get_partner()
        return Response({
            'light_logo': partner.absolute_logo_url or '',
            'dark_logo': partner.absolute_dark_logo_url or '',
        })


class PartnerSettingsAPIView(UpdateAPIView):
    permission_classes = (IsAuthenticated, IsPartnerUser)
    serializer_class = PartnerSettingsSerializer

    def get_object(self):
        """ Return the partner object for the authenticated user """
        return self.request.user.partner.partner
