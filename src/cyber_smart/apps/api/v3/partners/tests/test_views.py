from django.conf import settings
from django.urls import reverse
from rest_framework.test import APITestCase

from accounts.factories import UserFactory
from appusers.models.factories import AppUserFactory
from partners.factories import PartnerUserFactory


class TestPartnerLogoAPIVIew(APITestCase):

    def setUp(self):
        self.app_user = AppUserFactory()
        self.partner = self.app_user.organisation.partner
        self.light_logo_file_name = 'light_logo.png'
        self.dark_logo_file_name = 'dark_logo.png'

    def add_logos(self, light_logo=None, dark_logo=None):
        self.partner.light_logo = light_logo
        self.partner.dark_logo = dark_logo
        self.partner.save()

    def get_partner_logos(self, app_user_uuid=None, skip_authentication=False):
        if not app_user_uuid:
            app_user_uuid = self.app_user.uuid
        if not skip_authentication:
            self.client.credentials(HTTP_APPUSER_UUID=app_user_uuid)
        return self.client.get(reverse('api-v3:partner:partner-logos'))

    def test_get_both_logos(self):
        """ Test that the API returns the logos of the partner when both available. """
        self.add_logos(light_logo=self.light_logo_file_name, dark_logo= self.dark_logo_file_name)
        response = self.get_partner_logos()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {
            'light_logo': self.partner.absolute_logo_url,
            'dark_logo': self.partner.absolute_dark_logo_url,
        })
        self.assertEqual(response.data['light_logo'], f'http://example.com/media/{self.light_logo_file_name}')

    def test_no_logos(self):
        """ Test that the API returns empty string if logos are not available. """
        response = self.get_partner_logos()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {
            'light_logo': '',
            'dark_logo': '',
        })

    def test_one_logo(self):
        """ Test that the API returns emty string if one is not available """
        self.add_logos(dark_logo=self.dark_logo_file_name)
        response = self.get_partner_logos()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {
            'light_logo': '',
            'dark_logo': self.partner.absolute_dark_logo_url,
        })
        self.assertEqual(response.data['dark_logo'], f'http://example.com/media/{self.dark_logo_file_name}')

    def test_no_valid_app_user_uuid(self):
        """ Test if app user does not exist then it will return permission denied """
        response = self.get_partner_logos(app_user_uuid='invalid-uuid')
        self.assertEqual(response.status_code, 403)

    def test_no_app_user_uuid_header(self):
        """ Test no permission if no app user UUID header is provided. """
        response = self.get_partner_logos(skip_authentication=True)
        self.assertEqual(response.status_code, 403)


class TestPartnerSettingsAPIView(APITestCase):

    def setUp(self):
        self.partner_user = PartnerUserFactory()
        self.client.force_login(self.partner_user.user)
        self.url = reverse('api-v3:partner:settings', kwargs={'partner_uuid': self.partner_user.partner.secure_id})

    def test_update_default_language(self):
        """ Test that the default language can be updated. """
        new_language = 'fr'
        self.assertEqual(self.partner_user.partner.default_language, settings.LANGUAGE_CODE)
        response = self.client.patch(self.url, {'default_language': new_language}, format='json')
        self.assertEqual(response.status_code, 200)
        self.partner_user.partner.refresh_from_db()
        self.assertEqual(self.partner_user.partner.default_language, new_language)

    def test_not_a_partner_user(self):
        """ Test that a non-partner user cannot update the settings. """
        self.client.logout()
        other_user = UserFactory()
        self.client.force_login(other_user)
        response = self.client.patch(self.url, {'default_language': 'fr'}, format='json')
        self.assertEqual(response.status_code, 403)

    def test_not_a_valid_language(self):
        """ Test that an invalid language code returns a validation error. """
        response = self.client.patch(self.url, {'default_language': 'invalid-lang'}, format='json')
        self.assertEqual(response.status_code, 400)
