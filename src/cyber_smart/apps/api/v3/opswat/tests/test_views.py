from unittest.mock import patch
import copy

from django.test import TestCase
from django.urls import reverse

from api.v3.opswat.utils import get_opswat_id_for
from appusers.models.factories import AppInstallFactory, AppUserFactory, AppInstallOSUserFactory
from archive.models import InstalledProductsArchive
from opswat.categories import OPSWAT_CATEGORY_UNCLASSIFIED
from opswat.models import ProductCategory, ProductVendor, Product, ProductVersion, InstalledProduct, ProductSignature, InstalledProductVersion, OpswatOperatingSystem


class OPSWATSoftwareViewTestCase(TestCase):
    """
    Test suite for the OPSWAT Software View.
    """
    databases = "__all__"
    @classmethod
    def setUpTestData(cls):
        cls.opswat_products_url = reverse("api-v3:opswat:software")

    def setUp(self):
        """
        Set up reusable test data.
        """
        self.app_user = AppUserFactory()
        self.app_install = AppInstallFactory(app_user=self.app_user)
        self.os_user = AppInstallOSUserFactory(app_install=self.app_install, username="yaro", domain="")
        self._create_test_signatures()
        self.avast_product_data = {
            "product": {"id": 100130, "name": "Avast Mac Security"},
            "vendor": {"id": 100013, "name": "AVAST Software a.s."},
            "categories": [5],
            "version": "1.0.0",
            "cves": [],
            "signature": {"id": 5001, "name": "Avast Mac Security Signature"}
        }
        self.vmware_fusion_data = {
            "product": {"id": 100026, "name": "VMware Fusion"},
            "vendor": {"id": 100020, "name": "VMware, Inc."},
            "categories": [14],
            "version": "4.3.0",
            "cves": [],
        }
        self.base_request_data = self._generate_request_data()

    def _create_test_signatures(self):
        """Helper method to create test signatures used by multiple tests"""

        # Create Avast signature if it doesn't exist
        if not ProductSignature.objects.filter(opswat_id="5001").exists():
            avast_product, _ = Product.objects.get_or_create(
                opswat_id="100130",
                defaults={
                    "name": "Avast Mac Security",
                    "vendor": ProductVendor.objects.get_or_create(
                        opswat_id="100013",
                        defaults={"name": "AVAST Software a.s."}
                    )[0]
                }
            )
            ProductSignature.objects.create(
                opswat_id="5001",
                name="Avast Mac Security Signature",
                product=avast_product,
                support_3rd_party_patch=True
            )

        # Create VMware signature if it doesn't exist
        if not ProductSignature.objects.filter(opswat_id="5002").exists():
            vmware_product, _ = Product.objects.get_or_create(
                opswat_id="100026",
                defaults={
                    "name": "VMware Fusion",
                    "vendor": ProductVendor.objects.get_or_create(
                        opswat_id="100020",
                        defaults={"name": "VMware, Inc."}
                    )[0]
                }
            )
            ProductSignature.objects.create(
                opswat_id="5002",
                name="VMware Fusion Signature",
                product=vmware_product,
                support_3rd_party_patch=True
            )

    def _generate_request_data(self):
        """
        Helper to generate base request data.
        """
        return {
            "device_id": self.app_install.device_id,
            "serial_number": self.app_install.serial_number,
            "app_user_uuid": str(self.app_install.app_user.uuid),
            "os_users_results": [
                {
                    "os_user_name": self.os_user.username,
                    "os_user_domain": self.os_user.domain,
                    "software": [self.avast_product_data, self.vmware_fusion_data]
                }
            ],
        }

    def _assert_created_or_used_entity(self, model, opswat_ids, expected_count):
        """
        Helper to assert created or used entities.
        """
        self.assertEqual(model.objects.count(), expected_count)
        for opswat_id in opswat_ids:
            self.assertTrue(model.objects.filter(opswat_id=opswat_id).exists())

    def test_creates_new_categories(self):
        """
        Test that new product categories are created.
        """
        response = self.client.post(self.opswat_products_url, self.base_request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self._assert_created_or_used_entity(
            ProductCategory,
            self.avast_product_data["categories"] + self.vmware_fusion_data["categories"],
            expected_count=2
        )

    def test_uses_existing_category(self):
        """
        Test that existing product categories are reused.
        """
        category = ProductCategory.objects.create(opswat_id=self.avast_product_data["categories"][0], name="Antivirus")
        response = self.client.post(self.opswat_products_url, self.base_request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self._assert_created_or_used_entity(
            ProductCategory,
            self.avast_product_data["categories"] + self.vmware_fusion_data["categories"],
            expected_count=2
        )
        self.assertEqual(ProductCategory.objects.get(
            opswat_id=self.avast_product_data["categories"][0]).name, category.name
                         )

    def test_creates_new_vendors(self):
        """
        Test that new product vendors are created.
        """
        response = self.client.post(self.opswat_products_url, self.base_request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self._assert_created_or_used_entity(
            ProductVendor,
            [self.avast_product_data["vendor"]["id"],
             self.vmware_fusion_data["vendor"]["id"]], expected_count=2
        )

    def test_uses_existing_vendor(self):
        """
        Test that existing product vendors are reused.
        """
        vendor, _ = ProductVendor.objects.get_or_create(
            opswat_id=self.avast_product_data["vendor"]["id"],
            defaults={"name": "AVAST Original Name"}
        )
        response = self.client.post(self.opswat_products_url, self.base_request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self._assert_created_or_used_entity(
            ProductVendor,
            [self.avast_product_data["vendor"]["id"], self.vmware_fusion_data["vendor"]["id"]],
            expected_count=2
        )
        self.assertEqual(ProductVendor.objects.get(
            opswat_id=self.avast_product_data["vendor"]["id"]).name, vendor.name
                         )

    def test_creates_new_products(self):
        """
        Test that new products are created.
        """
        response = self.client.post(self.opswat_products_url, self.base_request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self._assert_created_or_used_entity(
            Product,
            [self.avast_product_data["product"]["id"], self.vmware_fusion_data["product"]["id"]],
            expected_count=2
        )

        # Verify signature linking
        installed_product = InstalledProduct.objects.get(app_install=self.app_install)
        avast_product_version = ProductVersion.objects.get(
            product__opswat_id=self.avast_product_data["product"]["id"],
            raw_version=self.avast_product_data["version"]
        )
        installed_avast_version = InstalledProductVersion.objects.get(
            installed_product=installed_product,
            product_version=avast_product_version
        )
        self.assertIsNotNone(installed_avast_version.signature)
        self.assertEqual(installed_avast_version.signature.opswat_id, str(self.avast_product_data["signature"]["id"]))

    def test_uses_existing_product(self):
        """
        Test that existing products are reused.
        """
        vendor, _ = ProductVendor.objects.get_or_create(
            opswat_id=self.avast_product_data["vendor"]["id"],
            defaults={"name": "AVAST Original Name"}
        )
        product, _ = Product.objects.get_or_create(
            opswat_id=self.avast_product_data["product"]["id"],
            defaults={"name": "Avast Original Name", "vendor": vendor}
        )
        response = self.client.post(self.opswat_products_url, self.base_request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self._assert_created_or_used_entity(
            Product,
            [self.avast_product_data["product"]["id"], self.vmware_fusion_data["product"]["id"]],
            expected_count=2
        )
        self.assertEqual(Product.objects.get(opswat_id=self.avast_product_data["product"]["id"]).name, product.name)

    def test_creates_new_product_versions(self):
        """
        Test that new product versions are created.
        """
        response = self.client.post(self.opswat_products_url, self.base_request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self.assertTrue(ProductVersion.objects.filter(raw_version=self.avast_product_data["version"]).exists())
        self.assertTrue(ProductVersion.objects.filter(raw_version=self.vmware_fusion_data["version"]).exists())

    def test_uses_existing_product_version(self):
        """
        Test that existing product versions are reused.
        """
        av_version = self.avast_product_data["version"]
        vendor, _ = ProductVendor.objects.get_or_create(
            opswat_id=self.avast_product_data["vendor"]["id"],
            defaults={"name": "AVAST Original Name"}
        )
        product, _ = Product.objects.get_or_create(
            opswat_id=self.avast_product_data["product"]["id"],
            defaults={"name": "Avast Original Name", "vendor": vendor}
        )
        version, _ = ProductVersion.objects.get_or_create(
            product=product,
            raw_version=av_version,
            defaults={"major": 1, "minor": 0, "patch": 0}
        )
        response = self.client.post(self.opswat_products_url, self.base_request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self.assertTrue(ProductVersion.objects.filter(raw_version=av_version).exists())
        self.assertTrue(ProductVersion.objects.filter(raw_version=self.vmware_fusion_data["version"]).exists())
        self.assertEqual(ProductVersion.objects.get(raw_version=av_version).major, version.major)
        self.assertEqual(ProductVersion.objects.get(raw_version=av_version).minor, version.minor)
        self.assertEqual(ProductVersion.objects.get(raw_version=av_version).patch, version.patch)

    def test_parses_semantic_versions_correctly(self):
        """
        Test that semantic versions are parsed and stored correctly.
        """
        response = self.client.post(self.opswat_products_url, self.base_request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        avast_major, avast_minor, avast_patch = self.avast_product_data["version"].split(".")
        vmware_major, vmware_minor, vmware_patch = self.vmware_fusion_data["version"].split(".")
        self.assertTrue(
            ProductVersion.objects.filter(
                raw_version=self.avast_product_data["version"], major=avast_major, minor=avast_minor, patch=avast_patch
            ).exists()
        )
        self.assertTrue(
            ProductVersion.objects.filter(
                raw_version=self.vmware_fusion_data["version"],
                major=vmware_major, minor=vmware_minor, patch=vmware_patch
            ).exists()
        )

    def test_handles_non_semantic_versions(self):
        """
        Test that non-semantic versions are handled gracefully.
        """
        self.avast_product_data["version"] = "2341.0"
        self.vmware_fusion_data["version"] = "1234.25.26-alfa.123"
        response = self.client.post(self.opswat_products_url, self.base_request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self.assertTrue(ProductVersion.objects.filter(raw_version="2341.0", major=2341, minor=0, patch=0).exists())
        self.assertTrue(
            ProductVersion.objects.filter(raw_version="1234.25.26-alfa.123", major=1234, minor=25, patch=26).exists()
        )

    def test_category_is_not_required(self):
        """
        Test that the category field is not required and defaults to ID 0.
        """
        request_data = copy.deepcopy(self.base_request_data)
        request_data["os_users_results"][0]["software"][0]["product"]["id"] = 12345678
        request_data["os_users_results"][0]["software"][0]["product"]["name"] = "new software"
        request_data["os_users_results"][0]["software"][0].pop("categories")
        response = self.client.post(self.opswat_products_url, request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self.assertTrue(ProductCategory.objects.filter(opswat_id=OPSWAT_CATEGORY_UNCLASSIFIED).exists())
        product = Product.objects.get(opswat_id=request_data["os_users_results"][0]["software"][0]["product"]["id"])
        self.assertEqual(product.categories.count(), 1)
        self.assertEqual(product.categories.first().opswat_id, OPSWAT_CATEGORY_UNCLASSIFIED)

    def test_vendor_id_is_not_required(self):
        """
        Test that the vendor ID field is not required and defaults to vendor name.
        """
        request_data = copy.deepcopy(self.base_request_data)
        # Vendor ID is popped, so a new vendor should be created based on name
        popped_vendor_name = request_data["os_users_results"][0]["software"][0]["vendor"]["name"]
        request_data["os_users_results"][0]["software"][0]["vendor"].pop("id")

        vendor_name_slugify = get_opswat_id_for(ProductVendor, request_data["os_users_results"][0]["software"][0])

        response = self.client.post(self.opswat_products_url, request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)

        # Assert that a new vendor was created with the slugified name
        self.assertTrue(ProductVendor.objects.filter(opswat_id=vendor_name_slugify).exists())
        newly_created_vendor = ProductVendor.objects.get(opswat_id=vendor_name_slugify)
        self.assertEqual(newly_created_vendor.name, popped_vendor_name)

        # Assert that the existing product (Avast "100130") still uses its original vendor ("100013")
        # The serializer does not re-link existing products to newly created vendors in this scenario.
        product = Product.objects.get(opswat_id=self.avast_product_data["product"]["id"])
        original_vendor_opswat_id = self.avast_product_data["vendor"]["id"] # This is "100013"

        self.assertEqual(str(product.vendor.opswat_id), str(original_vendor_opswat_id))
        # Check that the original vendor's name is consistent (it should be from _create_test_signatures)
        self.assertEqual(product.vendor.name, "AVAST Software a.s.")

    def test_product_id_is_not_required(self):
        """
        Test that the product ID field is not required and defaults to product name.
        """
        request_data = copy.deepcopy(self.base_request_data)
        request_data["os_users_results"][0]["software"][0]["product"].pop("id")
        request_data["os_users_results"][0]["software"][0]["product"]["name"] = "New Product Name"
        product_name_slugify = get_opswat_id_for(Product, request_data["os_users_results"][0]["software"][0])
        response = self.client.post(self.opswat_products_url, request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self.assertTrue(Product.objects.filter(opswat_id=product_name_slugify).exists())
        product = Product.objects.get(opswat_id=product_name_slugify)
        self.assertEqual(product.name, request_data["os_users_results"][0]["software"][0]["product"]["name"])

    def test_cves_list_is_not_required(self):
        """
        Test that the CVEs list field is not required and defaults to an empty list.
        """
        request_data = copy.deepcopy(self.base_request_data)
        request_data["os_users_results"][0]["software"][0].pop("cves")
        response = self.client.post(self.opswat_products_url, request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)

    @patch("api.v3.opswat.serializers.create_installed_products_archive.delay")
    def test_archive_record_successfully_created(self, mock_create_archive):
        """
        Test that the archive record creation task is called.
        """
        # Initial state check
        self.assertEqual(InstalledProductsArchive.objects.count(), 0)

        # First request
        response = self.client.post(self.opswat_products_url, self.base_request_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(mock_create_archive.call_count, 1)  # Check task was called once

        # Capture the first call arguments
        first_call_args, first_call_kwargs = mock_create_archive.call_args
        first_request_hash = InstalledProduct.objects.order_by('-modified').first().hash
        # Get expected IDs from the first installed product record
        first_installed_product = InstalledProduct.objects.order_by('-modified').first()
        expected_product_version_ids = list(first_installed_product.product_versions.values_list("id", flat=True))

        # Assert arguments for the first call
        self.assertEqual(first_call_kwargs['app_install_id'], self.app_install.id)
        self.assertEqual(first_call_kwargs['organisation_id'], self.app_install.app_user.organisation.id)
        self.assertListEqual(sorted(first_call_kwargs['product_version_ids']), sorted(expected_product_version_ids))
        self.assertEqual(first_call_kwargs['hash_value'], first_request_hash)


        # Second request with updated data
        updated_data = self._generate_request_data()
        updated_data["os_users_results"][0]["software"][0]["version"] = "2.0.0"
        response = self.client.post(self.opswat_products_url, updated_data, content_type="application/json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(mock_create_archive.call_count, 2) # Check task was called again

        # Capture the second call arguments
        second_call_args, second_call_kwargs = mock_create_archive.call_args
        second_request_hash = InstalledProduct.objects.order_by('-modified').first().hash
        # Get expected IDs from the latest installed product record after the update
        latest_installed_product = InstalledProduct.objects.order_by('-modified').first()
        expected_product_version_ids_after_update = list(latest_installed_product.product_versions.values_list("id", flat=True))

        # Assert arguments for the second call
        self.assertEqual(second_call_kwargs['app_install_id'], self.app_install.id)
        self.assertEqual(second_call_kwargs['organisation_id'], self.app_install.app_user.organisation.id)
        self.assertListEqual(sorted(second_call_kwargs['product_version_ids']), sorted(expected_product_version_ids_after_update))
        self.assertEqual(second_call_kwargs['hash_value'], second_request_hash)

        # Ensure hashes are different
        self.assertNotEqual(first_request_hash, second_request_hash)

        # Ensure no archive records were created directly in the test db
        self.assertEqual(InstalledProductsArchive.objects.count(), 0)

    def test_software_updates_trigger_individual_and_organisation_updates(self):
        """
        Test that both app install and organisation-level software updates are triggered
        when OPSWAT data is posted.
        """
        # Import the module to make it available for patching
        import software_inventory.tasks  # noqa: F401

        # Patch both update tasks
        with patch("appusers.tasks.update_installed_software_individual.delay") as mock_individual_delay, \
             patch("software_inventory.tasks.update_installed_software_organisation_individual.delay") as mock_org_delay:

            response = self.client.post(self.opswat_products_url, self.base_request_data, content_type="application/json")
            self.assertEqual(response.status_code, 201)

            # Verify update_installed_software_individual was called with the app_install ID
            mock_individual_delay.assert_called_once_with(self.app_install.pk)

            # Verify update_installed_software_organisation_individual.delay was called with the organisation ID
            mock_org_delay.assert_called_once_with(self.app_install.app_user.organisation_id)


class OperatingSystemAPIViewTestCase(TestCase):
    """
    Test suite for the OperatingSystem API View.
    """
    databases = "__all__"

    @classmethod
    def setUpTestData(cls):
        cls.opswat_os_url = reverse("api-v3:opswat:operating-system")

    def setUp(self):
        self.app_user = AppUserFactory()
        self.app_install = AppInstallFactory(app_user=self.app_user)

    def test_valid_request(self):
        """Test creating OS info with valid data."""
        data = {
            "device_id": self.app_install.device_id,
            "serial_number": self.app_install.serial_number,
            "os_id": 12345,
            "os_type": 1,
            "architecture": "64-bit"
        }
        headers = {"HTTP_APPUSER_UUID": str(self.app_user.uuid)}

        response = self.client.post(
            self.opswat_os_url,
            data,
            content_type="application/json",
            **headers
        )

        self.assertEqual(response.status_code, 201)
        self.assertEqual(OpswatOperatingSystem.objects.count(), 1)

    def test_invalid_request(self):
        """Test request with invalid data."""
        data = {
            "device_id": self.app_install.device_id,
            "serial_number": self.app_install.serial_number,
            "os_id": 12345,
            "os_type": 1,
            "architecture": "invalid-arch"
        }
        headers = {"HTTP_APPUSER_UUID": str(self.app_user.uuid)}

        response = self.client.post(
            self.opswat_os_url,
            data,
            content_type="application/json",
            **headers
        )

        self.assertEqual(response.status_code, 400)

    def test_missing_authorization_header(self):
        """Test request without authorization header."""
        data = {
            "device_id": self.app_install.device_id,
            "serial_number": self.app_install.serial_number,
            "os_id": 12345,
            "os_type": 1,
            "architecture": "64-bit"
        }

        response = self.client.post(
            self.opswat_os_url,
            data,
            content_type="application/json"
        )

        self.assertEqual(response.status_code, 400)
        self.assertIn("Missing App User UUID", response.json()['detail'])

    def test_update_existing_os_info(self):
        """Test updating existing OS info, including unknown OS (-1) to known OS."""
        # First create OS info with unknown OS
        data = {
            "device_id": self.app_install.device_id,
            "serial_number": self.app_install.serial_number,
            "os_id": -1,  # Unknown OS
            "os_type": 1,
            "architecture": "64-bit"
        }
        headers = {"HTTP_APPUSER_UUID": str(self.app_user.uuid)}

        response = self.client.post(
            self.opswat_os_url,
            data,
            content_type="application/json",
            **headers
        )

        self.assertEqual(response.status_code, 201)
        self.assertEqual(OpswatOperatingSystem.objects.count(), 1)

        # Verify initial values
        os_record = OpswatOperatingSystem.objects.first()
        self.assertEqual(os_record.os_id, -1)
        self.assertEqual(os_record.os_type, 1)
        self.assertEqual(os_record.architecture, "64-bit")

        # Now update with known OS (simulating OPSWAT database update)
        data["os_id"] = 12345  # Now it's a known OS
        data["os_type"] = 4  # Changed to Mac
        data["architecture"] = "32-bit"  # Changed architecture

        response = self.client.post(
            self.opswat_os_url,
            data,
            content_type="application/json",
            **headers
        )

        # Should return 200 for update
        self.assertEqual(response.status_code, 200)
        self.assertEqual(OpswatOperatingSystem.objects.count(), 1)  # Still only one record

        # Verify updated values
        os_record.refresh_from_db()
        self.assertEqual(os_record.os_id, 12345)
        self.assertEqual(os_record.os_type, 4)
        self.assertEqual(os_record.architecture, "32-bit")
