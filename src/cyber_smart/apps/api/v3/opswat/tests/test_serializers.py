import copy
from django.test import TestCase
from rest_framework.exceptions import ValidationError
from freezegun import freeze_time

from api.v3.opswat.serializers import SoftwareAPIViewSerializer, OpswatOperatingSystemSerializer
from appusers.models.factories import AppInstallOSUserFactory, AppInstallFactory, AppUserFactory
from opswat import UNKNOWN_VENDOR, UNKNOWN_VERSION
from appusers.models import AppInstallOSUser
from opswat.models import CVE, ProductVersion, ProductSignature, ProductVendor, Product, InstalledProduct, InstalledProductVersion


class SoftwareAPIViewSerializerTestCase(TestCase):
    databases = ('default', 'archive')

    def _create_test_signatures(self):
        """Helper method to create test signatures used by multiple tests"""

        # Create Avast signature if it doesn't exist
        if not ProductSignature.objects.filter(opswat_id="5001").exists():
            avast_product, _ = Product.objects.get_or_create(
                opswat_id="100130",
                defaults={
                    "name": "Avast Mac Security",
                    "vendor": ProductVendor.objects.get_or_create(
                        opswat_id="100013",
                        defaults={"name": "AVAST Software a.s."}
                    )[0]
                }
            )
            ProductSignature.objects.create(
                opswat_id="5001",
                name="Avast Mac Security Signature",
                product=avast_product,
                support_3rd_party_patch=True
            )

        # Create VMware signature if it doesn't exist
        if not ProductSignature.objects.filter(opswat_id="5002").exists():
            vmware_product, _ = Product.objects.get_or_create(
                opswat_id="100026",
                defaults={
                    "name": "VMware Fusion",
                    "vendor": ProductVendor.objects.get_or_create(
                        opswat_id="100020",
                        defaults={"name": "VMware, Inc."}
                    )[0]
                }
            )
            ProductSignature.objects.create(
                opswat_id="5002",
                name="VMware Fusion Signature",
                product=vmware_product,
                support_3rd_party_patch=True
            )

    def setUp(self):
        self.maxDiff = None
        self.app_install_1 = AppInstallFactory(app_user=AppUserFactory(), serial_number='PF4W9KSX')
        self.os_user_admin = AppInstallOSUserFactory(username='admin', domain='admin.local', app_install=self.app_install_1)
        AppInstallOSUserFactory(username='test', domain='test', app_install=self.app_install_1)
        AppInstallOSUserFactory(username='user1', app_install=self.app_install_1)

        # Create signatures for all tests
        self._create_test_signatures()

        # Define CVE data for reuse in tests
        self.valid_cve_data = {
            "id": 12345,
            "cve_id": "CVE-2023-1234",
            "severity": "critical",
            "severity_index": 90,
            "description": "Test vulnerability description",
            "published_at": "2023-01-01T00:00:00Z",
            "details": {"additional": "test details"}
        }
        self.valid_cve_data_2 = {
            "id": 12346,
            "cve_id": "CVE-2023-5678",
            "severity": "moderate",
            "severity_index": 50,
            "description": "Second vulnerability",
            "published_at": "2023-01-02T00:00:00Z",
            "details": {"additional": "test details 2"}
        }
        self.invalid_cve_data = {
            "id": "not_an_integer",
            "cve_id": "",
            "severity": "invalid_severity",
            "severity_index": "not_an_integer",
            "description": "",
            "published_at": "invalid_date",
            "details": "not_a_json"
        }

        # Define signature data as objects with ID and name
        self.valid_signature_data = {'id': 5001, 'name': 'Avast Mac Security Signature'}
        self.valid_signature_data_2 = {'id': 5002, 'name': 'VMware Fusion Signature'}

        self.software_data = {
            'device_id': self.app_install_1.device_id,
            'serial_number': self.app_install_1.serial_number,
            'app_user_uuid': str(self.app_install_1.app_user.uuid),
            'os_users_results': [
                {
                    'os_user_name': 'admin',
                    'os_user_domain': 'admin.local',
                    'software': [
                        {
                            'product': {'id': 100130, 'name': 'Avast Mac Security'},
                            'vendor': {'id': 100013, 'name': 'AVAST Software a.s.'},
                            'categories': [5],
                            "version": "1.0.0",
                            "cves": [self.valid_cve_data],
                            "signature": self.valid_signature_data
                        },
                        {
                            'product': {'id': 100026, 'name': 'VMware Fusion'},
                            'vendor': {'id': 100020, 'name': 'VMware, Inc.'},
                            'categories': [14],
                            "version": "3.7.1",
                            "cves": [self.valid_cve_data, self.valid_cve_data_2],
                            "signature": self.valid_signature_data_2
                        },
                    ]
                },
                {
                    'os_user_name': 'test',
                    'os_user_domain': 'test',
                    'software': [
                        {
                            'product': {'id': 100130, 'name': 'Avast Mac Security'},
                            'vendor': {'id': 100013, 'name': 'AVAST Software a.s.'},
                            'categories': [5],
                            "version": "1.0.0",
                            "cves": [self.valid_cve_data],
                            "signature": self.valid_signature_data
                        }
                    ]
                },
                {
                    'os_user_name': 'user1',
                    'os_user_domain': '',
                    'software': [
                        {
                            'product': {'id': 100135, 'name': 'Firefox'},
                            'vendor': {'id': 100015, 'name': 'Mozilla'},
                            'categories': [5],
                            "version": "1.0.0",
                            "cves": [],
                            "signature": None
                        }
                    ],
                }
            ]
        }

    def test_software_api_view_serializer(self):
        serializer = SoftwareAPIViewSerializer(data=self.software_data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.errors, {})

        # Don't compare data directly since the signature format might be different
        # Just check that the key fields are present
        self.assertEqual(serializer.data['device_id'], self.software_data['device_id'])
        self.assertEqual(serializer.data['serial_number'], self.software_data['serial_number'])
        self.assertEqual(serializer.data['app_user_uuid'], self.software_data['app_user_uuid'])

    def test_software_api_view_serializer_with_domain_in_username(self):
        test_data = copy.deepcopy(self.software_data)
        test_data['os_users_results'][0]['os_user_name'] = 'admin.local/admin'
        self.os_user_admin.username = 'admin'
        self.os_user_admin.domain = 'admin.local'
        self.os_user_admin.save()

        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        self.assertEqual(serializer.errors, {})

        # Create the data to test domain/username split
        serializer.create(serializer.validated_data)

        # Verify that the os_user was found with correct domain and username
        os_user = AppInstallOSUser.objects.get(
            username='admin',
            domain='admin.local',
            app_install=self.app_install_1
        )
        self.assertEqual(os_user.username, 'admin')
        self.assertEqual(os_user.domain, 'admin.local')

    def test_software_api_view_serializer_invalid_data(self):
        test_data = copy.deepcopy(self.software_data)
        test_data['device_id'] = ''
        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertFalse(serializer.is_valid())

        # Check error codes instead of full error details
        self.assertIn('device_id', serializer.errors)
        self.assertEqual(serializer.errors['device_id'][0].code, 'blank')

        # Check OS user errors
        self.assertIn('os_users_results', serializer.errors)
        for i in range(3):
            self.assertIn(i, serializer.errors['os_users_results'])
            self.assertIn('non_field_errors', serializer.errors['os_users_results'][i])
            self.assertEqual(serializer.errors['os_users_results'][i]['non_field_errors'][0].code, 'invalid')

    def test_software_api_view_serializer_os_user_does_not_exists(self):
        test_data = copy.deepcopy(self.software_data)
        test_data['os_users_results'][0]['os_user_name'] = 'nonexistent_user'
        test_data['os_users_results'][0]['os_user_domain'] = 'nonexistent_domain'
        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertFalse(serializer.is_valid())

        # Check error structure and code
        self.assertIn('os_users_results', serializer.errors)
        self.assertIn(0, serializer.errors['os_users_results'])
        self.assertIn('non_field_errors', serializer.errors['os_users_results'][0])
        self.assertEqual(serializer.errors['os_users_results'][0]['non_field_errors'][0].code, 'invalid')

    def test_os_user_must_exist(self):
        self.os_user_admin.delete()
        serializer = SoftwareAPIViewSerializer(data=self.software_data)
        self.assertFalse(serializer.is_valid())

        # Check error structure without checking the message
        self.assertIn('os_users_results', serializer.errors)
        self.assertIn(0, serializer.errors['os_users_results'])
        self.assertIn('non_field_errors', serializer.errors['os_users_results'][0])

    def test_software_api_view_serializer_not_required_os_user_domain_field(self):
        test_data = copy.deepcopy(self.software_data)
        del test_data['os_users_results'][2]['os_user_domain']
        serializer = SoftwareAPIViewSerializer(data=test_data)
        serializer.is_valid()
        self.assertTrue(serializer.is_valid())

    def test_software_api_view_serializer_required_detected_products_field(self):
        test_data = copy.deepcopy(self.software_data)
        test_data['os_users_results'][0]['software'][0]['product']['name'] = ''
        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertFalse(serializer.is_valid())

        # Navigate to the error and check the code
        errors = serializer.errors.get('os_users_results', {}).get(0, {}).get('software', [])
        self.assertTrue(len(errors) >= 1)
        product_errors = errors[0].get('product', {})
        self.assertIn('name', product_errors)
        self.assertEqual(product_errors['name'][0].code, 'blank')

    def test_app_install_must_exist(self):
        test_data = copy.deepcopy(self.software_data)
        test_data['device_id'] = '123456'
        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertFalse(serializer.is_valid())

        # Check error codes for all OS users
        self.assertIn('os_users_results', serializer.errors)
        for i in range(3):
            self.assertIn(i, serializer.errors['os_users_results'])
            self.assertIn('non_field_errors', serializer.errors['os_users_results'][i])
            self.assertEqual(serializer.errors['os_users_results'][i]['non_field_errors'][0].code, 'invalid')

    def test_serial_number_can_be_blank(self):
        test_data = copy.deepcopy(self.software_data)
        test_data['serial_number'] = ''
        self.app_install_1.serial_number = ''
        self.app_install_1.save()
        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid())

    def test_vendor_name_can_be_blank(self):
        test_data = copy.deepcopy(self.software_data)
        test_data["os_users_results"][0]["software"][0]["vendor"]["name"] = ""
        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(
            serializer.validated_data["os_users_results"][0]["software"][0]["vendor"]["name"], UNKNOWN_VENDOR
        )

    def test_product_version_can_be_blank(self):
        test_data = copy.deepcopy(self.software_data)
        test_data["os_users_results"][0]["software"][0]["version"] = ""
        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(
            serializer.validated_data["os_users_results"][0]["software"][0]["version"], UNKNOWN_VERSION
        )

    def test_app_install_inactive_raises_exception(self):
        test_data = copy.deepcopy(self.software_data)
        self.app_install_1.inactive = True
        self.app_install_1.save()
        serializer = SoftwareAPIViewSerializer(data=test_data)
        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)
        # Check error structure without checking the exact message
        self.assertIn('non_field_errors', context.exception.detail)
        self.assertTrue(len(context.exception.detail['non_field_errors']) > 0)

    def test_app_install_user_not_active_or_enrolled_raises_exception(self):
        test_data = copy.deepcopy(self.software_data)
        self.app_install_1.inactive = False
        self.app_install_1.save()
        self.app_install_1.app_user.active = False
        self.app_install_1.app_user.save()
        serializer = SoftwareAPIViewSerializer(data=test_data)
        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)
        # Check error structure without checking the exact message
        self.assertIn('non_field_errors', context.exception.detail)
        self.assertTrue(len(context.exception.detail['non_field_errors']) > 0)

    def test_app_user_uuid_from_header(self):
        data = copy.deepcopy(self.software_data)
        del data['app_user_uuid']
        context = {'request': type('Request', (), {'headers': {'APPUSER-UUID': str(self.app_install_1.app_user.uuid)}})()}
        serializer = SoftwareAPIViewSerializer(data=data, context=context)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(str(serializer.validated_data['app_user_uuid']), str(self.app_install_1.app_user.uuid))

    def test_cve_basic_validation_and_persistence(self):
        """Test basic CVE validation and database persistence"""
        serializer = SoftwareAPIViewSerializer(data=self.software_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        # Check validation
        validated_cve = serializer.validated_data['os_users_results'][0]['software'][0]['cves'][0]
        self.assertEqual(validated_cve['cve_id'], "CVE-2023-1234")
        self.assertEqual(validated_cve['severity'], "critical")
        self.assertEqual(validated_cve['severity_index'], 90)

        # Check persistence
        serializer.create(serializer.validated_data)
        cve = CVE.objects.get(cve_id="CVE-2023-1234")
        self.assertEqual(cve.severity, CVE.SEVERITY_CRITICAL)
        self.assertEqual(cve.severity_index, 90)
        self.assertEqual(cve.description, "Test vulnerability description")
        self.assertEqual(cve.details, {"additional": "test details"})

    def test_cve_multiple_validation_and_persistence(self):
        """Test validation and persistence of multiple CVEs for a single software"""
        serializer = SoftwareAPIViewSerializer(data=self.software_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        # Check validation of multiple CVEs
        cves = serializer.validated_data['os_users_results'][0]['software'][1]['cves']
        self.assertEqual(len(cves), 2)
        self.assertEqual(cves[0]['cve_id'], "CVE-2023-1234")
        self.assertEqual(cves[1]['cve_id'], "CVE-2023-5678")

        # Check persistence of multiple CVEs
        serializer.create(serializer.validated_data)

        # Debug assertions
        self.assertTrue(ProductVersion.objects.exists(), "No ProductVersions were created")
        self.assertTrue(CVE.objects.exists(), "No CVEs were created")

        # Get and verify CVEs
        cve1 = CVE.objects.get(cve_id="CVE-2023-1234")
        cve2 = CVE.objects.get(cve_id="CVE-2023-5678")
        self.assertEqual(cve2.severity, CVE.SEVERITY_MODERATE)
        self.assertEqual(cve2.severity_index, 50)

        # Get and verify product version
        vmware_version = ProductVersion.objects.get(
            product__opswat_id=100026,
            raw_version="3.7.1"
        )

        # Check relationships
        self.assertIn(vmware_version, cve1.product_version.all())
        self.assertIn(vmware_version, cve2.product_version.all())

    def test_cve_empty_list_validation_and_persistence(self):
        """Test validation and persistence of empty CVE list"""
        serializer = SoftwareAPIViewSerializer(data=self.software_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        # Check validation
        cves = serializer.validated_data['os_users_results'][2]['software'][0]['cves']
        self.assertEqual(len(cves), 0)

        # Check persistence
        serializer.create(serializer.validated_data)
        product_version = ProductVersion.objects.get(
            product__opswat_id=100135,
            raw_version="1.0.0"
        )
        self.assertEqual(product_version.cves.count(), 0)

    def test_cve_invalid_data(self):
        """Test validation of invalid CVE data"""
        test_data = copy.deepcopy(self.software_data)
        test_data['os_users_results'][0]['software'][0]['cves'] = [self.invalid_cve_data]
        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertFalse(serializer.is_valid())
        errors = serializer.errors['os_users_results'][0]['software'][0]['cves'][0]
        self.assertIn('id', errors)
        self.assertIn('cve_id', errors)
        self.assertIn('severity_index', errors)
        self.assertIn('published_at', errors)

        # Verify no CVEs were created
        self.assertEqual(CVE.objects.count(), 0)

    def test_cve_severity_validation_and_persistence(self):
        """Test validation and persistence of all severity levels"""
        severity_mapping = {
            'unknown': CVE.SEVERITY_UNKNOWN,
            'low': CVE.SEVERITY_LOW,
            'moderate': CVE.SEVERITY_MODERATE,
            'important': CVE.SEVERITY_IMPORTANT,
            'critical': CVE.SEVERITY_CRITICAL
        }

        for i, (severity_string, expected_value) in enumerate(severity_mapping.items()):
            # Reset the database for each iteration
            CVE.objects.all().delete()

            cve_data = copy.deepcopy(self.valid_cve_data)
            cve_data['severity'] = severity_string
            cve_data['cve_id'] = f"CVE-2023-{severity_string}"  # Make unique CVE ID for each test
            cve_data['id'] = f"{i}"
            test_data = copy.deepcopy(self.software_data)
            test_data['os_users_results'][0]['software'][0]['cves'] = [cve_data]

            serializer = SoftwareAPIViewSerializer(data=test_data)
            self.assertTrue(serializer.is_valid(), serializer.errors)

            # Check validation
            self.assertEqual(
                serializer.validated_data['os_users_results'][0]['software'][0]['cves'][0]['severity'],
                severity_string
            )

            # Check persistence
            serializer.create(serializer.validated_data)
            cve = CVE.objects.get(cve_id=cve_data['cve_id'])
            self.assertEqual(cve.severity, expected_value)

    def test_cve_uniqueness_in_database(self):
        """Test CVE uniqueness constraint in database"""
        # Create initial CVE
        serializer = SoftwareAPIViewSerializer(data=self.software_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.create(serializer.validated_data)
        initial_count = CVE.objects.count()

        # Try to create duplicate CVE with different data
        duplicate_cve_data = copy.deepcopy(self.valid_cve_data)
        duplicate_cve_data['severity'] = 'low'
        duplicate_cve_data['severity_index'] = 10
        test_data = copy.deepcopy(self.software_data)
        test_data['os_users_results'][0]['software'][0]['cves'] = [duplicate_cve_data]

        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.create(serializer.validated_data)

        # Verify no new CVE was created and original wasn't modified
        self.assertEqual(CVE.objects.count(), initial_count)
        cve = CVE.objects.get(cve_id="CVE-2023-1234")
        self.assertEqual(cve.severity, CVE.SEVERITY_CRITICAL)
        self.assertEqual(cve.severity_index, 90)

    def test_cve_product_version_relationships(self):
        """Test CVE-ProductVersion relationship persistence"""
        serializer = SoftwareAPIViewSerializer(data=self.software_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.create(serializer.validated_data)

        # Debug assertions
        self.assertTrue(ProductVersion.objects.exists(), "No ProductVersions were created")
        self.assertTrue(CVE.objects.exists(), "No CVEs were created")

        # Get product versions
        avast_version = ProductVersion.objects.get(
            product__opswat_id=100130,
            raw_version="1.0.0"
        )
        vmware_version = ProductVersion.objects.get(
            product__opswat_id=100026,
            raw_version="3.7.1"
        )

        # Get CVEs
        cve1 = CVE.objects.get(cve_id="CVE-2023-1234")
        cve2 = CVE.objects.get(cve_id="CVE-2023-5678")

        # Check CVE relationships
        self.assertIn(avast_version, cve1.product_version.all())
        self.assertIn(vmware_version, cve1.product_version.all())

        # Check reverse relationships
        self.assertIn(cve1, avast_version.cves.all())
        self.assertIn(cve1, vmware_version.cves.all())

        # Check second CVE relationships
        self.assertIn(vmware_version, cve2.product_version.all())
        self.assertNotIn(avast_version, cve2.product_version.all())
        self.assertIn(cve2, vmware_version.cves.all())
        self.assertNotIn(cve2, avast_version.cves.all())

    def test_signature_validation_and_persistence(self):
        """Test signature validation and database persistence"""
        # Get the pre-created signature
        avast_signature = ProductSignature.objects.get(opswat_id="5001")

        serializer = SoftwareAPIViewSerializer(data=self.software_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        # Check validation
        validated_signature = serializer.validated_data['os_users_results'][0]['software'][0]['signature']
        self.assertEqual(validated_signature['id'], "5001")
        self.assertEqual(validated_signature['name'], "Avast Mac Security Signature")

        # Check persistence
        serializer.create(serializer.validated_data)

        # Verify signatures exist
        self.assertTrue(ProductSignature.objects.exists(), "No ProductSignatures were found")

        # Check specific signature
        avast_signature = ProductSignature.objects.get(opswat_id="5001")
        self.assertEqual(avast_signature.name, "Avast Mac Security Signature")
        self.assertEqual(avast_signature.product.opswat_id, "100130")

        # Check that the signature is associated with the installed product version
        avast_version = ProductVersion.objects.get(
            product__opswat_id="100130",
            raw_version="1.0.0"
        )

        # Get the installed product
        installed_product = InstalledProduct.objects.filter(
            app_install__device_id=self.app_install_1.device_id
        ).order_by('-modified').first()

        # Check that the installed product version has the correct signature
        ipv = InstalledProductVersion.objects.get(
            installed_product=installed_product,
            product_version=avast_version
        )
        self.assertEqual(ipv.signature, avast_signature)

    def test_signature_null_validation_and_persistence(self):
        """Test that null signature is handled correctly"""

        # Update the test data to set Firefox signature to null
        test_data = copy.deepcopy(self.software_data)
        test_data['os_users_results'][2]['software'][0]['signature'] = None

        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        # Check validation - Firefox has null signature
        self.assertIsNone(serializer.validated_data['os_users_results'][2]['software'][0]['signature'])

        # Check persistence
        serializer.create(serializer.validated_data)

        # No need to verify further as we're just testing that the serializer accepts null signatures

    def test_signature_not_provided_validation_and_persistence(self):
        """Test that missing signature field is handled correctly"""

        # Update the test data to remove the signature field completely
        test_data = copy.deepcopy(self.software_data)
        del test_data['os_users_results'][2]['software'][0]['signature']

        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        # Check validation - Firefox has no signature field
        self.assertNotIn('signature', serializer.validated_data['os_users_results'][2]['software'][0])

        # Check persistence
        serializer.create(serializer.validated_data)

        # Get the Firefox version and installed product to verify signature is None
        firefox_version = ProductVersion.objects.get(
            product__opswat_id="100135",
            raw_version="1.0.0"
        )

        installed_product = InstalledProduct.objects.filter(
            app_install__device_id=self.app_install_1.device_id
        ).order_by('-modified').first()

        # Verify the installed product version has no signature
        ipv = InstalledProductVersion.objects.get(
            installed_product=installed_product,
            product_version=firefox_version
        )
        self.assertIsNone(ipv.signature)

    def test_signature_with_empty_properties_validation_and_persistence(self):
        """Test that signature with empty properties is handled correctly"""

        # Update the test data to have signature with empty properties
        test_data = copy.deepcopy(self.software_data)
        test_data['os_users_results'][2]['software'][0]['signature'] = {}

        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        # Check validation - Firefox has signature with no properties
        self.assertEqual(serializer.validated_data['os_users_results'][2]['software'][0]['signature'],
                         {'id': None, 'name': ''})

        # Check persistence
        serializer.create(serializer.validated_data)

        # Get the Firefox version
        firefox_version = ProductVersion.objects.get(
            product__opswat_id="100135",
            raw_version="1.0.0"
        )

        # Get the installed product
        installed_product = InstalledProduct.objects.filter(
            app_install__device_id=self.app_install_1.device_id
        ).order_by('-modified').first()

        # Verify the installed product version has no signature
        ipv = InstalledProductVersion.objects.get(
            installed_product=installed_product,
            product_version=firefox_version
        )
        self.assertIsNone(ipv.signature)

    def test_case_insensitive_os_user_matching(self):
        """Test that OS user matching is case-insensitive"""
        # Make a deep copy of the data and change the case of the username
        test_data = copy.deepcopy(self.software_data)
        test_data['os_users_results'][0]['os_user_name'] = 'ADMIN'
        test_data['os_users_results'][0]['os_user_domain'] = 'ADMIN.LOCAL'

        # The database record has 'admin' and 'admin.local'
        self.os_user_admin.username = 'admin'
        self.os_user_admin.domain = 'admin.local'
        self.os_user_admin.save()

        # The serializer should find the user despite the case difference
        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        self.assertEqual(serializer.errors, {})

    def test_missing_os_user_name(self):
        """Test that a validation error is raised when both os_user_name and os_user_domain are missing"""
        # Make a deep copy of the data and remove both os_user_name and os_user_domain
        test_data = copy.deepcopy(self.software_data)
        test_data['os_users_results'][0].pop('os_user_name')
        test_data['os_users_results'][0].pop('os_user_domain')

        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertFalse(serializer.is_valid())

        # Check that the validation error is raised with correct code
        errors = serializer.errors.get('os_users_results', {}).get(0, {})
        self.assertIn('non_field_errors', errors)
        self.assertEqual(errors['non_field_errors'][0].code, 'invalid')

    def test_missing_os_user_name_with_domain(self):
        """Test that validation passes when os_user_name is missing but os_user_domain is provided"""
        # Create a user with empty username but with domain
        AppInstallOSUserFactory(
            username='',
            domain='domain_only',
            app_install=self.app_install_1
        )

        # Make a deep copy of the data and modify it
        test_data = copy.deepcopy(self.software_data)
        test_data['os_users_results'][0]['os_user_name'] = ''
        test_data['os_users_results'][0]['os_user_domain'] = 'domain_only'

        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

    def test_signature_with_id_and_name_creates_new_signature(self):
        """Test that a signature with ID and name is created if it doesn't exist"""

        # Create a copy of the data with only a non-existent signature ID
        test_data = copy.deepcopy(self.software_data)
        # Remove the second software item to simplify the test
        test_data['os_users_results'][0]['software'] = [test_data['os_users_results'][0]['software'][0]]
        test_data['os_users_results'][0]['software'][0]['signature'] = {
            'id': 9999,
            'name': 'Test Signature'
        }

        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        # This should not raise an exception now
        serializer.create(serializer.validated_data)

        # Verify the signature was created
        self.assertTrue(ProductSignature.objects.filter(opswat_id='9999').exists())
        signature = ProductSignature.objects.get(opswat_id='9999')
        self.assertEqual(signature.name, 'Test Signature')
        self.assertFalse(signature.support_3rd_party_patch)

        # Verify the signature is associated with the correct product
        avast_product = Product.objects.get(opswat_id="100130")  # This is the product in the test data
        self.assertEqual(signature.product, avast_product)

    def test_installed_product_aggregation(self):
        """Test that software from all OS users is aggregated into the most recent InstalledProduct."""
        # Create multiple OS users for the same app_install
        second_os_user = AppInstallOSUser.objects.create(
            app_install=self.app_install_1,
            username="second_user",
            domain="second_domain",
        )
        third_os_user = AppInstallOSUser.objects.create(
            app_install=self.app_install_1,
            username="third_user",
            domain="third_domain",
        )

        # Create existing InstalledProduct records
        # Oldest record, associated with os_user_admin
        with freeze_time("2023-01-01 12:00:00"):
            InstalledProduct.objects.create(
                app_install=self.app_install_1,
            )

        # Most recent record, associated with second_os_user
        with freeze_time("2023-01-01 12:00:01"):
            most_recent_product_record = InstalledProduct.objects.create(
                app_install=self.app_install_1,
            )
        initial_most_recent_modified = most_recent_product_record.modified

        # Define software data for each OS user
        admin_software = {
            'product': {'id': 100130, 'name': 'Admin Product'},
            'vendor': {'id': 100013, 'name': 'AVAST'},
            'categories': [5],
            "version": "1.0.0"
        }
        second_user_software_1 = {
            'product': {'id': 100131, 'name': 'Second User Product 1'},
            'vendor': {'id': 100013, 'name': 'AVAST'},
            'categories': [5],
            "version": "2.0.0"
        }
        # Duplicate software to test uniqueness
        second_user_software_2 = {
            'product': {'id': 100130, 'name': 'Admin Product'}, # Same as admin_software
            'vendor': {'id': 100013, 'name': 'AVAST'},
            'categories': [5],
            "version": "1.0.0"
        }
        third_user_software = {
            'product': {'id': 100132, 'name': 'Third User Product'},
            'vendor': {'id': 100013, 'name': 'AVAST'},
            'categories': [5],
            "version": "3.0.0"
        }

        # Software data for the API request
        test_data = {
            'device_id': self.app_install_1.device_id,
            'serial_number': self.app_install_1.serial_number,
            'app_user_uuid': str(self.app_install_1.app_user.uuid),
            'os_users_results': [
                {
                    'os_user_name': self.os_user_admin.username,
                    'os_user_domain': self.os_user_admin.domain,
                    'software': [admin_software]
                },
                {
                    'os_user_name': second_os_user.username,
                    'os_user_domain': second_os_user.domain,
                    'software': [second_user_software_1, second_user_software_2]
                },
                {
                    'os_user_name': third_os_user.username,
                    'os_user_domain': third_os_user.domain,
                    'software': [third_user_software]
                }
            ]
        }

        # Process the data
        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        # Simulate processing time to check if date_modified is updated
        with freeze_time("2023-01-01 12:00:02"):
            serializer.create(serializer.validated_data)

        # Verify that duplicate InstalledProduct records have been cleaned up (should be 1)
        self.assertEqual(InstalledProduct.objects.filter(app_install=self.app_install_1).count(), 1,
                         "Expected only one InstalledProduct record after cleanup")

        # Get the record that was most recently modified (it should be the one we created second)
        updated_record = InstalledProduct.objects.filter(
            app_install=self.app_install_1
        ).order_by('-modified').first()

        # Verify it's the correct record that was updated
        self.assertEqual(updated_record.id, most_recent_product_record.id,
                         "The most recently created InstalledProduct record should have been updated")
        self.assertTrue(updated_record.modified > initial_most_recent_modified,
                        "The date_modified timestamp should have been updated")

        # Verify the aggregated product versions (should be 3 unique versions)
        self.assertEqual(updated_record.product_versions.count(), 3,
                         "Expected 3 unique product versions after aggregation")

        product_ids = set(pv.product.opswat_id for pv in updated_record.product_versions.all())
        expected_ids = {'100130', '100131', '100132'}
        self.assertEqual(product_ids, expected_ids,
                         "Aggregated product versions do not match expected set")

    def test_update_installed_product_version_with_signature(self):
        """Test that an existing InstalledProductVersion without a signature is updated when a request comes with a signature."""

        avast_vendor = ProductVendor.objects.get_or_create(
            opswat_id="100013",
            name="AVAST Software a.s."
        )[0]
        avast_product = Product.objects.get_or_create(
            opswat_id="100130",
            defaults={
                "name": "Avast Mac Security",
                "vendor": avast_vendor
            }
        )[0]
        avast_version = ProductVersion.objects.get_or_create(
            product=avast_product,
            raw_version="1.0.0",
            defaults={
                "major": 1,
                "minor": 0,
                "patch": 0,
                "version_channel": "stable"
            }
        )[0]
        installed_product = InstalledProduct.objects.create(
            app_install=self.app_install_1
        )
        # Create an InstalledProductVersion WITHOUT a signature
        ipv = InstalledProductVersion.objects.create(
            installed_product=installed_product,
            product_version=avast_version,
            signature=None  # Explicitly set to None
        )

        # Verify the initial state has no signature
        self.assertIsNone(ipv.signature)

        # Create a request with the same product version but WITH a signature
        test_data = {
            'device_id': self.app_install_1.device_id,
            'serial_number': self.app_install_1.serial_number,
            'app_user_uuid': str(self.app_install_1.app_user.uuid),
            'os_users_results': [
                {
                    'os_user_name': self.os_user_admin.username,
                    'os_user_domain': self.os_user_admin.domain,
                    'software': [
                        {
                            'product': {'id': 100130, 'name': 'Avast Mac Security'},
                            'vendor': {'id': 100013, 'name': 'AVAST Software a.s.'},
                            'categories': [5],
                            "version": "1.0.0",
                            "signature": self.valid_signature_data  # 5001
                        }
                    ]
                }
            ]
        }

        # Process the data
        serializer = SoftwareAPIViewSerializer(data=test_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.create(serializer.validated_data)

        # Refresh the InstalledProductVersion from the database
        ipv.refresh_from_db()

        # Verify that the signature has been updated
        self.assertIsNotNone(ipv.signature)
        self.assertEqual(ipv.signature.opswat_id, "5001")
        self.assertEqual(ipv.signature.name, "Avast Mac Security Signature")


class OpswatOperatingSystemSerializerTestCase(TestCase):
    """
    Test suite for the OpswatOperatingSystemSerializer.
    """

    def setUp(self):
        self.app_user = AppUserFactory()
        self.app_install = AppInstallFactory(app_user=self.app_user)

    def test_valid_data_serialization(self):
        """Test serialization with valid data."""
        data = {
            'os_id': 12345,
            'os_type': 1,
            'architecture': '64-bit',
            'device_id': 'test-device-id',
            'serial_number': 'test-serial'
        }

        serializer = OpswatOperatingSystemSerializer(data=data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

    def test_invalid_data_serialization(self):
        """Test serialization with invalid data."""
        # Missing required field
        data = {
            'os_id': 12345,
            'os_type': 1,
            'device_id': 'test-device-id',
            'serial_number': 'test-serial'
        }
        serializer = OpswatOperatingSystemSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('architecture', serializer.errors)

        # Invalid architecture
        data = {
            'os_id': 12345,
            'os_type': 1,
            'architecture': 'invalid-arch',
            'device_id': 'test-device-id',
            'serial_number': 'test-serial'
        }
        serializer = OpswatOperatingSystemSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('architecture', serializer.errors)
