import logging
from typing import Any, Dict, List, Set

from django.db.transaction import atomic
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from api.common.serializers import DeviceAuthenticationSerializer, RemoveNullCharactersSerializer
from appusers.models import A<PERSON><PERSON><PERSON><PERSON>OSUser, AppInstall
from opswat import UNKNOWN_VENDOR, UNKNOWN_VERSION
from opswat.categories import OPSWAT_CATEGORY_UNCLASSIFIED
from opswat.models import ProductVendor, Product, ProductVersion, ProductCategory, InstalledProduct, CVE, ProductSignature, InstalledProductVersion, OpswatOperatingSystem
from opswat.utils import parse_semantic_version, InstalledProductsHasher
from opswat.tasks import create_installed_products_archive
from .utils import get_opswat_id_for

logger = logging.getLogger(__name__)


class ProductSerializer(serializers.Serializer):
    """
    Serializer for product data.
    """
    id = serializers.IntegerField(
        label="Product ID",
        help_text="The unique identifier for the product associated with this signature.",
        required=False
    )
    name = serializers.CharField(
        label="Product Name",
        help_text="The name of the product associated with this signature.",
        max_length=255
    )

    class Meta:
        ref_name = "OpswatProduct"


class VendorSerializer(serializers.Serializer):
    """
    Serializer for vendor data.
    """
    id = serializers.IntegerField(
        label="Vendor ID",
        help_text="The unique identifier for the vendor of this product.",
        required=False
    )
    name = serializers.CharField(
        label="Vendor Name",
        help_text="The name of the vendor of this product.",
        max_length=255,
        required=False,
        allow_blank=True
    )

    def to_internal_value(self, data):
        data = super().to_internal_value(data)
        data["name"] = data.get("name") or UNKNOWN_VENDOR
        return data


class CVESerializer(serializers.Serializer):
    """
    Serializer for Common Vulnerabilities and Exposures (CVE) entries associated with a product scan.
    """
    id = serializers.IntegerField(
        label="An OPSWAT identifier for the vulnerability.",
        help_text="The unique identifier for the vulnerability."
    )
    cve_id = serializers.CharField(
        label="CVE ID",
        help_text="CVE (Common Vulnerabilities and Exposures) identifier.",
        max_length=255
    )
    severity = serializers.CharField(
        label="Severity",
        help_text="String description of Severity level: 'low', 'moderate', 'important', 'critical', 'unknown'.",
        max_length=255
    )
    severity_index = serializers.IntegerField(
        label="Severity Index",
        help_text="Numeric representation of severity level on a scale from 0 to 100."
    )
    description = serializers.CharField(
        label="Description",
        help_text="A description providing details about the vulnerability."
    )
    published_at = serializers.DateTimeField(
        label="Published At",
        help_text="The date and time when the vulnerability was published."
    )
    details = serializers.JSONField(
        label="Details",
        help_text="Additional details about the vulnerability."
    )


class SignatureSerializer(serializers.Field):
    """
    Serializer for product signature data.
    Accepts an object where both 'id' and 'name' fields are optional.
    The field itself is also optional and can be null.
    """
    class Meta:
        swagger_schema_fields = {
            "type": "object",
            "title": "Signature Object",
            "description": "Object containing optional signature ID and name.",
            "properties": {
                "id": {
                    "type": "integer",
                    "description": "The ID of the signature. Optional."
                },
                "name": {
                    "type": "string",
                    "description": "The name of the signature. Optional."
                }
            },
        }

    def to_internal_value(self, data):
        if data is None:
            return None

        if not isinstance(data, dict):
            raise serializers.ValidationError("If provided, signature must be an object")

        # Both id and name are optional, but if id is provided, convert it to string
        signature_id = data.get('id')
        if signature_id is not None:
            signature_id = str(signature_id)

        # Return a dict with both fields, which might be None
        return {
            'id': signature_id,
            'name': data.get('name', '')
        }

    def to_representation(self, value):
        return value


class SoftwareSerializer(serializers.Serializer):
    product = ProductSerializer(
        help_text="The product associated with this signature.",
    )
    vendor = VendorSerializer(
        help_text="The vendor of the product associated with this signature."
    )
    version = serializers.CharField(
        label="Version",
        help_text="The version of the product.",
        max_length=255,
        required=False,
        allow_blank=True
    )
    categories = serializers.ListField(
        child=serializers.IntegerField(),
        help_text="The product categories identified for this signature of the product.",
        required=False
    )
    cves = CVESerializer(
        many=True, help_text="List of Common Vulnerabilities and Exposures (CVE) associated with the product.",
        required=False
    )
    signature = SignatureSerializer(
        help_text="The signature of the product.",
        required=False,
        allow_null=True
    )

    def to_internal_value(self, data):
        data = super().to_internal_value(data)
        data["version"] = data.get("version") or UNKNOWN_VERSION
        return data


def _split_domain_username(os_user_name: str, os_user_domain: str = "") -> tuple[str, str]:
    """
    TEMPORARY WORKAROUND: Split domain/username if "/" is present in username.
    This is a temporary fix until CAP deploys a proper fix for the domain/username format.
    Current format from CAP: "DOMAIN/username" - this will be fixed to send domain and username separately.

    Args:
        os_user_name: The username which may contain domain in format "domain/username"
        os_user_domain: The domain from the separate field, used if no domain in username

    Returns:
        tuple[str, str]: A tuple of (username, domain)
    """
    if "/" in os_user_name:
        domain, username = os_user_name.split("/", 1)
        return username, domain
    return os_user_name, os_user_domain


class OSUserResultsSerializer(serializers.Serializer):
    os_user_name = serializers.CharField(
        label="OS User Name",
        help_text="Name of the OS user associated with the scan.",
        max_length=255,
        required=False,
        allow_blank=True
    )
    os_user_domain = serializers.CharField(
        label="OS User Domain",
        help_text="Domain of the OS user associated with the scan.",
        max_length=255,
        required=False,
        allow_blank=True
    )
    software = SoftwareSerializer(many=True, help_text="List of software detected on the OS user.")

    def validate(self, attrs):
        """ Check if the OS user exists """
        initial_data = self.parent.parent.initial_data

        # Ensure at least one of os_user_name or os_user_domain is provided
        if not attrs.get("os_user_name") and not attrs.get("os_user_domain"):
            raise serializers.ValidationError("Either OS user name or domain must be provided.")

        username, domain = _split_domain_username(attrs.get('os_user_name', ''), attrs.get('os_user_domain', ''))
        try:
            AppInstallOSUser.objects.get(
                app_install__app_user__uuid=initial_data["app_user_uuid"],
                app_install__device_id=initial_data["device_id"],
                app_install__serial_number=initial_data["serial_number"],
                username__iexact=username,
                domain__iexact=domain
            )
        except AppInstallOSUser.DoesNotExist:
            raise serializers.ValidationError("OS user does not exist.")

        return attrs

class SoftwareAPIViewSerializer(DeviceAuthenticationSerializer, RemoveNullCharactersSerializer):
    """Serializer for the Software API View, including validation and data aggregation logic."""
    os_users_results = serializers.ListField(child=OSUserResultsSerializer())

    @staticmethod
    def get_uuid_field_name() -> str:
        return "app_user_uuid"

    def is_valid(self, *, raise_exception=False):
        if not super().is_valid(raise_exception=raise_exception):
            return False

        app_install = AppInstall.objects.filter(
            app_user__uuid=self.initial_data.get("app_user_uuid"),
            device_id=self.initial_data.get("device_id"),
            serial_number=self.initial_data.get("serial_number")
        ).select_related("app_user").first()
        if not app_install:
            if raise_exception:
                raise ValidationError({"non_field_errors": ["AppInstall does not exist."]})
            self._errors = {"non_field_errors": ["AppInstall does not exist."]}
            return False

        if app_install.inactive or not app_install.app_user.active:
            error_message = "AppInstall or app user is inactive or not enrolled."
            if raise_exception:
                raise ValidationError({"non_field_errors": [error_message]})
            self._errors = {"non_field_errors": [error_message]}
            return False

        return True

    def _extract_categories(self, validated_data: Dict[str, Any]) -> Set[str]:
        """Extract unique product category IDs from validated data as strings."""
        categories = set()
        for os_user_result in validated_data["os_users_results"]:
            for software in os_user_result["software"]:
                # Ensure each category is a string
                for cat in software.get("categories", [OPSWAT_CATEGORY_UNCLASSIFIED]):
                    categories.add(str(cat))
        return categories

    def _extract_vendors(self, validated_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract unique vendors from validated data."""
        vendors = {}
        for os_user_result in validated_data["os_users_results"]:
            for software in os_user_result["software"]:
                vendor_id = get_opswat_id_for(ProductVendor, software)
                if vendor_id not in vendors:
                    vendors[vendor_id] = {
                        "opswat_id": vendor_id,
                        "name": software["vendor"]["name"]
                    }
        return list(vendors.values())

    def _bulk_create_categories(self, categories: Set[str], cache: Dict[str, Any]) -> None:
        """Bulk create missing categories."""
        # Get existing categories
        existing_categories = ProductCategory.objects.filter(opswat_id__in=categories)
        cache["categories"] = {cat.opswat_id: cat for cat in existing_categories}

        # Create missing categories
        missing_categories = categories - set(cache["categories"].keys())
        if missing_categories:
            new_categories = [
                ProductCategory(opswat_id=cat_id, name="Unknown")
                for cat_id in missing_categories
            ]
            ProductCategory.objects.bulk_create(new_categories, ignore_conflicts=False)

            # Fetch ALL categories again to ensure we have everything in cache
            all_categories = ProductCategory.objects.filter(opswat_id__in=categories)
            cache["categories"] = {cat.opswat_id: cat for cat in all_categories}

    def _extract_products(self, validated_data: Dict[str, Any], cache: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract unique products from validated data."""
        products = {}
        for os_user_result in validated_data["os_users_results"]:
            for software in os_user_result["software"]:
                product_id = get_opswat_id_for(Product, software)
                if product_id not in products:
                    vendor_id = get_opswat_id_for(ProductVendor, software)
                    categories = software.get("categories", [OPSWAT_CATEGORY_UNCLASSIFIED])
                    # Convert category IDs to strings
                    categories_str = {str(cat) for cat in categories}

                    # Ensure all categories exist in cache
                    missing_categories = categories_str - set(cache["categories"].keys())
                    if missing_categories:
                        self._bulk_create_categories(missing_categories, cache)

                    products[product_id] = {
                        "opswat_id": product_id,
                        "name": software["product"]["name"],
                        "vendor": cache["vendors"][vendor_id],
                        "_meta_categories": [cache["categories"][str(cat)] for cat in categories]
                    }
        return list(products.values())

    def _extract_signatures(self, validated_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract unique product signatures from validated data."""
        signatures = {}
        for os_user_result in validated_data["os_users_results"]:
            for software in os_user_result["software"]:
                # Skip if signature is not provided or is None
                if not software.get("signature"):
                    continue

                signature_data = software["signature"]

                # Extract ID and name from the signature object
                # Both can be None/missing
                signature_id = signature_data.get('id')
                signature_name = signature_data.get('name', '')

                # Skip if no ID is provided
                if signature_id is None:
                    continue

                if signature_id not in signatures:
                    signatures[signature_id] = {
                        "opswat_id": signature_id,
                        "name": signature_name
                    }
        return list(signatures.values())

    def _get_signatures(self, signatures: List[Dict[str, Any]], cache: Dict[str, Any]) -> None:
        """
        Get existing signatures by their IDs.
        Creates signatures if they don't exist.
        """
        if not signatures:
            cache["signatures"] = {}
            return

        signature_ids = [s["opswat_id"] for s in signatures]
        existing_signatures = ProductSignature.objects.filter(opswat_id__in=signature_ids)
        cache["signatures"] = {sig.opswat_id: sig for sig in existing_signatures}
        cache["signature_product_mapping"] = {}
        # We'll create signatures after we've processed all the software data
        # and have the product mappings

    def _extract_versions(self, validated_data: Dict[str, Any], cache: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract unique product versions from validated data."""
        versions = {}
        for os_user_result in validated_data["os_users_results"]:
            for software in os_user_result["software"]:
                product_id = get_opswat_id_for(Product, software)
                version = software["version"]
                key = f"{product_id}:{version}"
                if key not in versions:
                    semantic_version = parse_semantic_version(version)
                    versions[key] = {
                        "product": cache["products"][product_id],
                        "raw_version": version,
                        "major": semantic_version.major if semantic_version else 0,
                        "minor": semantic_version.minor if semantic_version else 0,
                        "patch": semantic_version.patch if semantic_version else 0
                    }
        return list(versions.values())

    def _extract_cves(self, validated_data: Dict[str, Any], cache: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extracts all unique CVEs as `CVE` instances.
        """
        cves = {}
        for os_user_result in validated_data["os_users_results"]:
            for software in os_user_result["software"]:
                product_id = get_opswat_id_for(Product, software)
                version = software["version"]
                version_key = f"{product_id}:{version}"
                product_version = cache["versions"][version_key]

                for cve_data in software.get("cves", []):
                    cve_id = cve_data["cve_id"]
                    if cve_id not in cves:
                        cves[cve_id] = {
                            "opswat_id": cve_data["id"],
                            "cve_id": cve_id,
                            "severity": CVE.SEVERITY_MAP[cve_data["severity"].lower()],
                            "severity_index": cve_data["severity_index"],
                            "description": cve_data["description"],
                            "published_at": cve_data["published_at"],
                            "details": cve_data["details"],
                            "_meta_product_versions": { product_version.id }
                        }
                    else:
                        cves[cve_id]["_meta_product_versions"].add(product_version.id)
        return list(cves.values())

    def _bulk_create_vendors(self, vendors: List[Dict[str, Any]], cache: Dict[str, Any]) -> None:
        """Bulk create missing vendors."""
        vendor_ids = [v["opswat_id"] for v in vendors]
        existing_vendors = ProductVendor.objects.filter(opswat_id__in=vendor_ids)
        cache["vendors"] = {vendor.opswat_id: vendor for vendor in existing_vendors}

        # Create missing vendors
        missing_vendors = [
            ProductVendor(**vendor_data)
            for vendor_data in vendors
            if vendor_data["opswat_id"] not in cache["vendors"]
        ]
        if missing_vendors:
            with atomic():
                ProductVendor.objects.bulk_create(missing_vendors, ignore_conflicts=False)
                # Fetch the created vendors to get their IDs
                created_vendors = ProductVendor.objects.filter(
                    opswat_id__in=[v.opswat_id for v in missing_vendors]
                )
                cache["vendors"].update({vendor.opswat_id: vendor for vendor in created_vendors})

    def _bulk_create_products(self, products: List[Dict[str, Any]], cache: Dict[str, Any]) -> None:
        """Bulk create missing products."""
        product_ids = [p["opswat_id"] for p in products]
        existing_products = Product.objects.filter(opswat_id__in=product_ids)
        cache["products"] = {product.opswat_id: product for product in existing_products}
        # Create missing products
        missing_products = []
        product_categories = {}  # Store categories for each product
        for product_data in products:
            if product_data["opswat_id"] not in cache["products"]:
                categories = product_data.pop("_meta_categories")
                product_categories[product_data["opswat_id"]] = [cat.id for cat in categories]
                product_data["vendor_id"] = product_data["vendor"].id
                del product_data["vendor"]
                missing_products.append(Product(**product_data))

        if missing_products:
            with atomic():
                Product.objects.bulk_create(missing_products, ignore_conflicts=False)
                # Fetch the created products to get their IDs
                created_products = Product.objects.filter(
                    opswat_id__in=[p.opswat_id for p in missing_products]
                )
                # Set categories for newly created products using IDs
                for product in created_products:
                    if product.opswat_id in product_categories:
                        product.categories.set(product_categories[product.opswat_id])
                cache["products"].update({product.opswat_id: product for product in created_products})

    def _bulk_create_versions(self, versions: List[Dict[str, Any]], cache: Dict[str, Any]) -> None:
        """Bulk create missing product versions."""
        version_keys = set()
        for version_data in versions:
            product_id = version_data["product"].opswat_id
            version_keys.add(f"{product_id}:{version_data['raw_version']}")

        existing_versions = ProductVersion.objects.filter(
            product__opswat_id__in=[v["product"].opswat_id for v in versions]
        ).select_related("product")
        cache["versions"] = {
            f"{ver.product.opswat_id}:{ver.raw_version}": ver
            for ver in existing_versions
        }

        # Create missing versions
        missing_versions = []
        for version_data in versions:
            key = f"{version_data['product'].opswat_id}:{version_data['raw_version']}"
            if key not in cache["versions"]:
                # Use product_id instead of product object
                version_data["product_id"] = version_data["product"].id
                del version_data["product"]
                missing_versions.append(ProductVersion(**version_data))

        if missing_versions:
            with atomic():
                ProductVersion.objects.bulk_create(missing_versions, ignore_conflicts=False)
                created_versions = ProductVersion.objects.filter(
                    product_id__in=[v.product_id for v in missing_versions],
                    raw_version__in=[v.raw_version for v in missing_versions]
                )
                cache["versions"].update({
                    f"{ver.product.opswat_id}:{ver.raw_version}": ver
                    for ver in created_versions
                })

    def _bulk_create_cves(self, cves: List[Dict[str, Any]], cache: Dict[str, Any]) -> None:
        """
        Bulk create missing CVEs.
        """
        cve_ids = [cve["cve_id"] for cve in cves]
        existing_cves = CVE.objects.filter(cve_id__in=cve_ids).prefetch_related("product_version")
        cache["cves"] = {cve.cve_id: cve for cve in existing_cves}

        # Gather desired product version ids for each CVE from extracted data
        all_cve_versions = { cve["cve_id"]: cve.get("_meta_product_versions", set()) for cve in cves }

        # For existing CVEs, update their product_version relations
        for cve in existing_cves:
            current = set(product_version.id for product_version in cve.product_version.all())
            desired = all_cve_versions.get(cve.cve_id, set())
            to_add = desired - current
            if to_add:
                cve.product_version.add(*list(to_add))

        # Create missing CVEs
        missing_cves = []
        cve_product_versions = {}
        for cve_data in cves:
            if cve_data["cve_id"] not in cache["cves"]:
                pv_ids = cve_data.pop("_meta_product_versions", set())
                cve_product_versions[cve_data["cve_id"]] = pv_ids
                missing_cves.append(CVE(**cve_data))

        if missing_cves:
            with atomic():
                CVE.objects.bulk_create(missing_cves, ignore_conflicts=False)
                created_cves = CVE.objects.filter(cve_id__in=[cve.cve_id for cve in missing_cves])
                for cve in created_cves:
                    if cve.cve_id in cve_product_versions:
                        cve.product_version.add(*list(cve_product_versions[cve.cve_id]))
                cache["cves"].update({cve.cve_id: cve for cve in created_cves})

    def _create_installed_products(self, validated_data: Dict[str, Any], cache: Dict[str, Any]) -> None:
        """Create or update the most recent installed product record for the app install, aggregating data."""

        # Dictionary to store unique software by opswat_id
        software_dict = {}
        app_install = AppInstall.objects.filter(
            app_user__uuid=validated_data["app_user_uuid"],
            device_id=validated_data["device_id"],
            serial_number=validated_data["serial_number"]
        ).select_related("app_user").first()
        if not app_install:
            raise ValidationError({"non_field_errors": ["AppInstall not found."]})

        # Collect all OS users and their software information by opswat_id
        # All software is same on each OS user, but this is the format of the request
        # (with duplicated data).
        for os_user_result in validated_data["os_users_results"]:
            for software in os_user_result["software"]:
                opswat_id = get_opswat_id_for(Product, software)
                software_dict[opswat_id] = software

        # Convert dictionary values to list for further processing
        all_software_from_request = list(software_dict.values())

        # If no valid OS users/software found, exit early
        if not all_software_from_request:
            raise ValidationError({"non_field_errors": ["No valid OS user data or software found in the request."]})

        # Calculate hash based on the deduplicated software list
        new_installed_products_hash = InstalledProductsHasher(
            [
                {
                    "opswat_id": opswat_id,
                    "version": software["version"],
                    "cves": [cve["cve_id"] for cve in software.get("cves", [])]
                }
                for opswat_id, software in software_dict.items()
            ]
        ).generate_hash()

        # Use a transaction to prevent race conditions
        with atomic():
            # Get all InstalledProduct records for this app_install with a lock
            # This prevents other transactions from modifying these records until our transaction completes
            installed_products = InstalledProduct.objects.filter(
                app_install_id=app_install.id
            ).select_for_update().order_by('-modified')

            # Find the most recently updated InstalledProduct
            installed_product = installed_products.first()

            # If a record exists and the hash matches, no update needed
            if installed_product and installed_product.hash == new_installed_products_hash:
                # Clean up any other duplicate records if they exist
                if installed_products.count() > 1:
                    # Keep the most recent record and delete the rest
                    installed_products.exclude(id=installed_product.id).delete()
                return

            # If no record exists, create one.
            if not installed_product:
                installed_product = InstalledProduct.objects.create(
                    app_install_id=app_install.id,
                )
            # Clean up any other duplicate records if they exist
            elif installed_products.count() > 1:
                # Keep the most recent record and delete the rest
                installed_products.exclude(id=installed_product.id).delete()

            # Get unique product versions from the aggregated software list
            unique_product_version_keys = set()
            aggregated_product_versions = []

            version_signatures = {}
            # Iterate directly over the software dictionary items
            for product_id, software in software_dict.items():
                version_key = f"{product_id}:{software['version']}"
                # Ensure the version exists in the cache before adding
                if version_key in cache["versions"] and version_key not in unique_product_version_keys:
                    unique_product_version_keys.add(version_key)
                    product_version = cache["versions"][version_key]
                    aggregated_product_versions.append(product_version)
                    # Store signature information if available
                    if software.get("signature"):
                        signature_data = software["signature"]
                        signature_id = signature_data.get('id')
                        # Skip if no ID is provided
                        if signature_id is not None and signature_id in cache["signatures"]:
                            version_signatures[product_version.id] = cache["signatures"][signature_id]
                elif version_key not in cache["versions"]:
                    logger.warning(f"Version key {version_key} not found in cache for app_install {app_install.id}")

            # Update the most recent (or newly created) installed product
            current_ids = set(installed_product.product_versions.values_list("id", flat=True))
            new_ids = {pv.id for pv in aggregated_product_versions}

            to_add = new_ids - current_ids
            to_remove = current_ids - new_ids

            if to_remove:
                installed_product.product_versions.remove(*to_remove)

            # For product versions to add, we'll handle them manually to set signatures
            if to_add:
                for version_id in to_add:
                    # Check if we have a signature for this version
                    signature = version_signatures.get(version_id)
                    # Create the through model with the signature if available
                    InstalledProductVersion.objects.create(
                        installed_product=installed_product,
                        product_version_id=version_id,
                        signature=signature
                    )

            # Update signatures for existing product versions
            # (backfills existing product versions)
            existing_to_update = new_ids - to_add
            if existing_to_update and version_signatures:
                installed_product_versions = InstalledProductVersion.objects.filter(
                    installed_product=installed_product,
                    product_version_id__in=existing_to_update
                )
                for ipv in installed_product_versions:
                    if ipv.signature != version_signatures.get(ipv.product_version_id):
                        ipv.signature = version_signatures.get(ipv.product_version_id)
                        ipv.save(update_fields=["signature"])

            installed_product.hash = new_installed_products_hash
            installed_product.save()

            final_product_version_ids = list(installed_product.product_versions.values_list("id", flat=True))

        create_installed_products_archive.delay(
            app_install_id=installed_product.app_install_id,
            organisation_id=app_install.app_user.organisation_id,
            product_version_ids=final_product_version_ids,
            hash_value=new_installed_products_hash
        )

    def _map_signatures_to_products(self, validated_data: Dict[str, Any], cache: Dict[str, Any]) -> None:
        """
        Map signatures to their corresponding products based on the software data.
        This is needed to create missing signatures with the correct product association.
        """
        signature_product_mapping = {}

        # First, go through all software to map signatures to products
        for os_user_result in validated_data["os_users_results"]:
            for software in os_user_result["software"]:
                # Skip if signature is not provided or is None
                if not software.get("signature"):
                    continue

                signature_data = software["signature"]

                # Skip if no ID is provided
                signature_id = signature_data.get('id')
                if signature_id is None:
                    continue

                # Get the product ID for this software
                product_id = get_opswat_id_for(Product, software)

                # Map the signature to the product
                if signature_id not in signature_product_mapping and product_id in cache["products"]:
                    signature_product_mapping[signature_id] = cache["products"][product_id]

        # Store the mapping in the cache
        cache["signature_product_mapping"] = signature_product_mapping

        # Now create missing signatures with the correct product association
        signatures = self._extract_signatures(validated_data)
        missing_signatures = []

        for signature in signatures:
            if signature["opswat_id"] not in cache["signatures"]:
                product = signature_product_mapping.get(signature["opswat_id"])
                if product:
                    missing_signatures.append(
                        ProductSignature(
                            opswat_id=signature["opswat_id"],
                            name=signature["name"],
                            product=product,
                            support_3rd_party_patch=False
                        )
                    )

        # Create missing signatures if we have any
        if missing_signatures:
            with atomic():
                ProductSignature.objects.bulk_create(missing_signatures)
                # Fetch the created signatures to get their IDs
                created_signatures = ProductSignature.objects.filter(
                    opswat_id__in=[s.opswat_id for s in missing_signatures]
                )
                cache["signatures"].update({sig.opswat_id: sig for sig in created_signatures})

    def create(self, validated_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create all necessary objects in the correct order with proper relationships."""
        cache = {"categories": {}, "vendors": {}, "products": {}, "signatures": {}, "versions": {}, "cves": {}}

        categories = self._extract_categories(validated_data)
        self._bulk_create_categories(categories, cache)

        vendors = self._extract_vendors(validated_data)
        self._bulk_create_vendors(vendors, cache)

        products = self._extract_products(validated_data, cache)
        self._bulk_create_products(products, cache)

        signatures = self._extract_signatures(validated_data)
        self._get_signatures(signatures, cache)

        versions = self._extract_versions(validated_data, cache)
        self._bulk_create_versions(versions, cache)

        # Map signatures to products and create missing signatures
        self._map_signatures_to_products(validated_data, cache)

        cves = self._extract_cves(validated_data, cache)
        self._bulk_create_cves(cves, cache)

        # Create installed products last since they depend on all other objects
        self._create_installed_products(validated_data, cache)

        return validated_data


class OpswatOperatingSystemSerializer(serializers.ModelSerializer):
    """
    Serializer for OPSWAT Operating System information.
    Used to create/update OS information for an app install.
    """
    device_id = serializers.CharField(write_only=True, help_text="The device ID for authentication.")
    serial_number = serializers.CharField(write_only=True, required=False, allow_blank=True, help_text="The device serial number for authentication.")

    class Meta:
        model = OpswatOperatingSystem
        fields = ['os_id', 'os_type', 'architecture', 'device_id', 'serial_number']

    def validate_os_type(self, value):
        """
        Validate that os_type is one of the accepted values.
        Valid values are: 1 (Windows), 2 (Linux), 4 (Mac)
        """
        valid_os_types = [
            OpswatOperatingSystem.OS_TYPE_WINDOWS,
            OpswatOperatingSystem.OS_TYPE_LINUX,
            OpswatOperatingSystem.OS_TYPE_MAC
        ]

        if value not in valid_os_types:
            raise serializers.ValidationError(
                f"Invalid os_type. Must be one of: {valid_os_types} (1=Windows, 2=Linux, 4=Mac)"
            )

        return value

    def validate_architecture(self, value):
        """
        Validate that architecture is one of the accepted values.
        Valid values are: '32-bit', '64-bit'
        """
        valid_architectures = ['32-bit', '64-bit']

        if value not in valid_architectures:
            raise serializers.ValidationError(
                f"Invalid architecture. Must be one of: {', '.join(valid_architectures)}"
            )

        return value
