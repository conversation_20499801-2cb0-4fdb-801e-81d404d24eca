from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.generics import CreateAPIView
from rest_framework.response import Response

from appusers.tasks import update_installed_software_individual
from opswat.models import OpswatOperatingSystem
from opswat_patch.serializers import OpswatDeviceAuthenticationSerializer
from .serializers import SoftwareAPIViewSerializer, OpswatOperatingSystemSerializer
from software_inventory.tasks import update_installed_software_organisation_individual


class SoftwareAPIView(CreateAPIView):
    """ API endpoint that receives a list of software package from a OPSWAT scan done by CAP """
    authentication_classes = []
    serializer_class = SoftwareAPIViewSerializer

    @swagger_auto_schema(
        request_body=SoftwareAPIViewSerializer,
        responses={
            status.HTTP_201_CREATED: "Successfully saved the software data."
        }
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        update_installed_software_individual.delay(serializer.app_install.pk)
        update_installed_software_organisation_individual.delay(
            serializer.app_install.app_user.organisation_id
        )

        return Response(serializer.data, status=status.HTTP_201_CREATED)


class OperatingSystemAPIView(CreateAPIView):
    """
    API endpoint that receives operating system information from OPSWAT SDK GetOSInfo call.

    Authentication:
    - Expects app_user_uuid in HTTP_APPUSER_UUID header
    - Expects device_id and serial_number in request body

    Creates or updates OpswatOperatingSystem record for the identified AppInstall.
    """
    authentication_classes = []
    serializer_class = OpswatOperatingSystemSerializer

    @swagger_auto_schema(
        request_body=OpswatOperatingSystemSerializer,
        manual_parameters=[],
        responses={
            status.HTTP_201_CREATED: OpswatOperatingSystemSerializer,
            status.HTTP_200_OK: OpswatOperatingSystemSerializer,
            status.HTTP_400_BAD_REQUEST: "Bad Request - Invalid data or missing authentication",
            status.HTTP_404_NOT_FOUND: "Not Found - No matching AppInstall found"
        },
        operation_summary="Create or Update OPSWAT Operating System Information",
        operation_description=(
            "Receives OS information from OPSWAT SDK GetOSInfo call and stores it for the app install. "
            "Authentication requires device_id and serial_number in the request body, "
            "and app_user_uuid in the HTTP header (as HTTP_APPUSER_UUID). "
            "The app_user_uuid can be either a regular app_user UUID or a main_app_user UUID."
        )
    )
    def post(self, request, *args, **kwargs):
        # First validate the OS data
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Extract device authentication fields from validated data
        device_id = serializer.validated_data.pop('device_id')
        serial_number = serializer.validated_data.pop('serial_number', '')

        # Get the app_user_uuid from the header and authenticate
        app_user_uuid = request.META.get('HTTP_APPUSER_UUID')
        if not app_user_uuid:
            raise ValidationError({"detail": "Missing App User UUID in request header."})

        auth_data = {
            'device_id': device_id,
            'serial_number': serial_number,
            'uuid': app_user_uuid
        }
        auth_serializer = OpswatDeviceAuthenticationSerializer(data=auth_data, context={'request': request})
        auth_serializer.is_valid(raise_exception=True)

        if not auth_serializer.app_install:
            raise ValidationError({"detail": "No AppInstall found matching the provided authentication parameters."})

        # Create or update the OpswatOperatingSystem record
        opswat_os, created = OpswatOperatingSystem.objects.update_or_create(
            app_install=auth_serializer.app_install,
            defaults=serializer.validated_data
        )

        # Serialize the result
        response_serializer = self.get_serializer(opswat_os)

        # Return appropriate status code
        status_code = status.HTTP_201_CREATED if created else status.HTTP_200_OK
        return Response(response_serializer.data, status=status_code)
