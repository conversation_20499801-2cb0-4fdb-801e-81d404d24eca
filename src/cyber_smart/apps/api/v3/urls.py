from django.urls import include, re_path as url

app_name = 'api-v3'

urlpatterns = [
    url(r"^", include("api.v3.policies.urls")),
    url(r"^vulnerabilities/", include("api.v3.vulnerabilities.urls")),
    url(r"^partners/", include("api.v3.partners.urls", namespace="partners")),
    url(r"^organisations/", include("api.v3.organisations.urls")),
    url(r"^app-users/", include("api.v3.appusers.urls")),
    url(r"^opswat/", include("api.v3.opswat.urls", namespace="opswat")),
    url(r"^opswat-patch/", include("opswat_patch.urls", namespace="opswat-patch")),
]
