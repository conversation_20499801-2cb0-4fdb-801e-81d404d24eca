from django.conf import settings
from django.db import transaction
from django.http import Http404
from django.shortcuts import get_object_or_404
from django.urls import reverse
from rest_framework import exceptions, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.mixins import PermissionRoleRequired
from accounts.permissions import DEVICES_PERMISSION, PEOPLE_AND_ORGANISATION_PERMISSION
from analytics.tasks import calculate_organisation_analytics, calculate_app_user_analytics
from appusers.models import AppInstall, AppUser
from common.utils import get_full_name_or_email
from notifications.handlers import NotificationHandler
from organisations.models import Organisation, get_organisations
from .serializers import AppUserSerializer, MainBulkAppUserSerializer
from trustd.models import TrustdDevice
from trustd.tasks import update_trustd_device


class AppUserListAPI(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = AppUserSerializer

    def get(self, request, org_id, format=None):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise exceptions.NotAuthenticated
        if organisation.is_uba_enabled:
            app_users = organisation.user_attribution_appusers
        else:
            app_users = organisation.app_users.all()
        serializer = self.serializer_class(app_users, many=True)
        return Response(serializer.data)

    def post(self, request, org_id, format=None):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise exceptions.NotAuthenticated
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            serializer.create(organisation, serializer.validated_data)
            modified_data = {'uuid': serializer.validated_data.uuid}
            modified_data.update(serializer.data)
            return Response(modified_data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AppUserDetailEditAPI(APIView):
    """
    Retrieve, update or delete an app user instance.

    Requires the user to have next permissions:
    - `devices` or `people_and_organisation`
    """
    permission_classes = (IsAuthenticated, PermissionRoleRequired,)
    permission_required = (DEVICES_PERMISSION, PEOPLE_AND_ORGANISATION_PERMISSION)
    serializer_class = AppUserSerializer

    def get_object(self, pk, org_id):
        organisation = get_object_or_404(Organisation, secure_id=org_id)
        try:
            return AppUser.objects.get(pk=pk, organisation=organisation)
        except AppUser.DoesNotExist:
            raise Http404

    def get(self, request, pk, org_id, format=None):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise exceptions.NotAuthenticated
        appuser = self.get_object(pk, org_id)
        serializer = AppUserSerializer(appuser)
        return Response(serializer.data)

    def put(self, request, pk, org_id, format=None):
        appuser = self.get_object(pk, org_id)
        organisation_creator = appuser.pk == appuser.organisation.main_app_user.pk
        if organisation_creator and appuser.organisation.bulk_install:
            serializer = MainBulkAppUserSerializer(appuser, data=request.data)
        else:
            serializer = AppUserSerializer(appuser, data=request.data)
        appuser_old_data = appuser.__dict__.copy()
        allowed_users = appuser.organisation.get_management_users()
        if request.user not in allowed_users:
            return Response({'success': -1})

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        if AppUser.objects.filter(email=request.data['email'], organisation=appuser.organisation).exclude(
                pk=appuser.pk
        ).count():
            return Response({'success': -4})

        if not serializer.save():
            return Response({"success": 0}, status=status.HTTP_200_OK)

        # in case the user has a trustd device
        trustd_device = TrustdDevice.objects.filter(app_install__app_user=appuser).first()
        if trustd_device and not trustd_device.device_name:
            changed_data = {}
            if appuser_old_data.get('first_name') != appuser.first_name:
                from trustd.utils import build_hostname
                changed_data['name'] = build_hostname(appuser, trustd_device.app_install.machine_model)
                trustd_device.app_install.hostname = changed_data['name']
                trustd_device.app_install.save(update_fields=['hostname'])
            if appuser_old_data.get('email') != appuser.email:
                changed_data['email'] = appuser.email
            if changed_data:
                update_trustd_device.delay(trustd_device.id, data=changed_data)
        if not settings.IS_TEST:
            transaction.on_commit(lambda: calculate_app_user_analytics.delay([appuser.pk]))
        return Response({'data': serializer.data, 'success': 1}, status=status.HTTP_200_OK)

    def send_notification(self, appuser):
        """ Send notification """
        if self.request.user.profile.is_partner:
            partner_or_org_name = self.request.user.profile.partner.name
        else:
            partner_or_org_name = self.request.user.profile.organisation.name
        kwargs = {
            'user_name': get_full_name_or_email(self.request.user),
            'organisation_id': appuser.organisation.id,
            'partner_or_org_name': partner_or_org_name,
            'app_user_name': get_full_name_or_email(appuser),
            'app_user_organisation_name': appuser.organisation.name,
            'url': reverse('dashboard:add-users', kwargs={'org_id': appuser.organisation.secure_id})
        }
        NotificationHandler.send_notification(message_type='app_user_removal', **kwargs)

    def delete(self, request, pk, org_id, format=None):
        appuser = self.get_object(pk, org_id)
        if appuser.organisation.main_app_user.pk == int(pk) and appuser.organisation.bulk_install:
            return Response({'success': -3})
        allowed_users = appuser.organisation.get_management_users(separate_by_group=True)
        if request.user in allowed_users['administrators'] | allowed_users['partners']:
            appinstall = AppInstall.objects.filter(
                app_user__uuid=appuser.uuid, inactive=False).order_by('-id').first()
            if appinstall and appinstall.app_user.email == request.user.email:
                return Response({'success': -2})
            appuser.active = False
            appuser.groups.set([])
            appuser.save()
            appuser.organisation.admins.filter(user__email__iexact=appuser.email).delete()
            if not settings.IS_TEST:
                calculate_organisation_analytics.delay(appuser.organisation.pk)
            self.send_notification(appuser)
            return Response({'success': 1})
        else:
            return Response({'success': -1})
