from rest_framework import serializers

from appusers.models import AppUser
from common.models import Group
from api.common.serializers import NullablePrimaryKeyRelatedField, CleanedUUIDField
from common.utils import handle_user_input
from django.forms.models import model_to_dict

SAFE_NAME_CHARACTERS = r'[0-9a-zA-Z ]'


class RoundFloatField(serializers.FloatField):

    def to_representation(self, value):
        if value is None:
            return 0
        elif type(value) in (int, float):
            return int(round(value, 0))
        return super(RoundFloatField, self).to_representation(value)


class AppUserSerializer(serializers.Serializer):
    pk = serializers.IntegerField(read_only=True)
    uuid = CleanedUUIDField(read_only=True)
    email = serializers.EmailField()
    groups = NullablePrimaryKeyRelatedField(queryset=Group.objects.all(), many=True, allow_null=True)
    first_name = serializers.RegexField(
        max_length=125, allow_blank=True, regex=SAFE_NAME_CHARACTERS,
        error_messages={'invalid': 'Invalid characters. You can use only letters, digits and spaces'})
    last_name = serializers.RegexField(
        max_length=125, allow_blank=True, regex=SAFE_NAME_CHARACTERS,
        error_messages={'invalid': 'Invalid characters. You can use only letters, digits and spaces'})
    is_admin = serializers.BooleanField(default=False)
    active = serializers.BooleanField(default=True)
    full_name = serializers.CharField(source='get_full_name', read_only=True)

    def create(self, organisation, validated_data):
        data = validated_data.copy()
        if data['email']:
            data['email'] = handle_user_input(data['email'])
        if data['first_name']:
            data['first_name'] = handle_user_input(data['first_name'])
        if data['last_name']:
            data['last_name'] = handle_user_input(data['last_name'])

        instance = AppUser.objects.create(organisation=organisation, **data)
        organisation = instance.organisation
        groups = validated_data.get('groups',  [None])
        if len(groups) > 0 and groups[0] is not None:
            instance.groups.set(groups)
            instance.save()

    def update(self, instance, validated_data):
        instance.email = handle_user_input(validated_data.get('email', instance.email))
        instance.first_name = handle_user_input(validated_data.get(
            'first_name', instance.first_name
        ))
        instance.last_name = handle_user_input(validated_data.get(
            'last_name', instance.last_name
        ))
        instance.is_admin = validated_data.get('is_admin', instance.is_admin)
        if not instance.organisation.has_software_support:
            instance.is_admin = True
        groups = validated_data.get('groups', [])
        if len(groups) > 0 and groups[0] is not None:
            instance.groups.set(groups)
        else:
            instance.groups.set([])

        instance.save()
        return instance

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # Customize the representation dictionary as needed
        # note groups are prefetched and this does a single query
        ret_groups = []
        for group in instance.groups.all():
            _dict = model_to_dict(group)
            _dict['organisation_id'] = _dict.get('organisation')
            ret_groups.append(_dict)
        representation['groups'] = ret_groups
        return representation


class MainBulkAppUserSerializer(serializers.Serializer):
    pk = serializers.IntegerField(read_only=True)
    uuid = CleanedUUIDField(read_only=True)
    email = serializers.EmailField()
    groups = NullablePrimaryKeyRelatedField(queryset=Group.objects.all(), many=True, allow_null=True)
    first_name = serializers.RegexField(
        max_length=125, allow_blank=True, regex=SAFE_NAME_CHARACTERS,
        error_messages={'invalid': 'Invalid characters. You can use only letters, digits and spaces'})
    last_name = serializers.RegexField(
        max_length=125, allow_blank=True, regex=SAFE_NAME_CHARACTERS,
        error_messages={'invalid': 'Invalid characters. You can use only letters, digits and spaces'})
    is_admin = serializers.BooleanField(read_only=True)
    active = serializers.BooleanField(read_only=True)

    def create(self, organisation, validated_data):
        data = validated_data.copy()
        if data['email']:
            data['email'] = handle_user_input(data['email'])
        if data['first_name']:
            data['first_name'] = handle_user_input(data['first_name'])
        if data['last_name']:
            data['last_name'] = handle_user_input(data['last_name'])
        instance = AppUser.objects.create(organisation=organisation, **data)
        groups = validated_data.get('groups', [])
        if len(groups) > 0 and groups[0] is not None:
            instance.groups.set(groups)
        else:
            instance.groups.set([])

        instance.save()
        return instance

    def update(self, instance, validated_data):
        instance.email = handle_user_input(validated_data.get('email', instance.email))
        instance.first_name = handle_user_input(validated_data.get(
            'first_name', instance.first_name
        ))
        instance.last_name = handle_user_input(validated_data.get(
            'last_name', instance.last_name
        ))
        instance.is_admin = validated_data.get('is_admin', instance.is_admin)
        groups = validated_data.get('groups', [None])
        if len(groups) > 0 and groups[0] is not None:
            instance.groups.set(groups)
        instance.save()
        return instance
