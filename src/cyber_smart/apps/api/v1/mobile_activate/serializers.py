from django.conf import settings
from rest_framework import serializers

from appusers.models import AppUser
from organisations.models import OrganisationApprovedDomain


class MobileMagicLinkSerializer(serializers.Serializer):
    """
    Validates passed email and sends magic link for download mobile app.
    Currently only implemented for bulk enrollment.
    """
    email = serializers.EmailField(required=True)

    def __init__(self, *args, **kwargs):
        self._email = None
        self._organisation = None
        self._self_enrollment_app_users = []
        super(MobileMagicLinkSerializer, self).__init__(*args, **kwargs)

    def validate(self, data):
        self._email = data['email']

        if data['email']:
            # first check if user has self enrolment
            app_users = AppUser.objects.filter(email=self._email, organisation__bulk_install=False,
                                               active=True).select_related('organisation')
            if app_users.exists():
                self._self_enrollment_app_users = app_users
            else:
                # second check if user has bulk enrolment
                approved_domain = OrganisationApprovedDomain.objects.filter(
                    domain=self._email.split('@')[1]
                ).first()
                if approved_domain:
                    self._organisation = approved_domain.organisation
                else:
                    raise serializers.ValidationError('Email or authorised domain not found')
        else:
            raise serializers.ValidationError('You must specify an email address')

        return data

    def _send_email(self, organisation, full_name, deep_link, is_vodafone_customer):
        from emails.tasks import api_template_email
        substitutions = [
            {
                'id': 'app_user',
                'value': full_name if full_name else 'there'
            },
            {
                'id': 'organisation_name',
                'value': organisation.name
            },
            {
                'id': 'authorise_device',
                'value': deep_link
            },
        ]
        api_template_email.delay(
            to_email=self._email,
            subject='Add your mobile device',
            template_id='d-d0a6e0004e0d4e07871ef88182dce9d3',
            substitutions=substitutions,
            custom_args=[
                {
                    'Name': 'Magic Mobile Link',
                    'Category': 'Active Protect',
                    'Environment': settings.ENVIRONMENT_NAME,
                    'Audience': organisation.get_email_audience(),
                }
            ],
            vodafone_customer=is_vodafone_customer,
            organisation_id=organisation.id,
        )

    def create(self, validated_data):
        if self._email and not self._organisation:
            # send magic link to self enrolment users
            for app_user in self._self_enrollment_app_users:
                self._send_email(
                    app_user.organisation,
                    app_user.get_full_name,
                    app_user.get_temporary_absolute_deep_link(),
                    app_user.organisation.is_vodafone_customer
                )

        if self._email and self._organisation:
            # send magic link to bulk deploy users
            if self._organisation.bulk_install:
                # create new or get existing app user
                app_user, _ = AppUser.objects.get_or_create(
                    organisation=self._organisation,
                    email=self._email,
                    defaults={
                        "active": True,
                    }
                )
                self._send_email(
                    self._organisation,
                    '',
                    app_user.get_temporary_absolute_deep_link(),
                    self._organisation.is_vodafone_customer
                )
