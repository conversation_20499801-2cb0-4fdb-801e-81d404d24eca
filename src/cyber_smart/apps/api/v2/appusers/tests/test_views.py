import json
from unittest.mock import patch, call

from django.contrib.auth import get_user_model
from django.urls import reverse

from api.tests.test_api_base import ApiBaseTest
from appusers.models.factories import AppInstallFactory
from appusers.models.factories import AppUserFactory, DisableSendMailUserFactory
from common.utils import get_full_name_or_email
from trustd.factories import TrustdDeviceFactory

User = get_user_model()


class SendAppViewTest(ApiBaseTest):

    def setUp(self):
        super().setUp()
        self.new_app_user = AppUserFactory(organisation=self.organisation, active=True)
        self.new_app_user_2 = AppUserFactory(organisation=self.organisation, active=True)

    def post_response(self, data, organisation_secure_id=None):
        """ Helper method to do the POST requests """
        if not organisation_secure_id:
            organisation_secure_id = self.organisation.secure_id
        self.add_people_and_org_permission_to_test_user(self.organisation)
        self.c.force_authenticate(self.user)
        url = reverse('api-v2:send-app', kwargs={'org_id': organisation_secure_id})
        return self.c.post(url, format="json", data=data)

    @patch('emails.tasks.send_cap_v_five_pipeline_version_email.delay')
    def test_resend_email_to_user_without_uuid(self, mock_send_cap_v_five_pipeline_version_email):
        response = self.post_response({})
        self.assertEqual(response.status_code, 200)

        mock_send_cap_v_five_pipeline_version_email.assert_called_once()
        self.assertCountEqual(mock_send_cap_v_five_pipeline_version_email.call_args[0][0], [self.new_app_user.uuid, self.new_app_user_2.uuid])

    @patch('emails.tasks.send_cap_v_five_pipeline_version_email.delay')
    def test_resend_email_to_user_with_uuid(self, mock_send_cap_v_five_pipeline_version_email):
        response = self.post_response({
            'uuid': f"{self.new_app_user.uuid},{self.new_app_user_2.uuid}"
        })
        self.assertEqual(response.status_code, 200)

        mock_send_cap_v_five_pipeline_version_email.assert_called_once()
        self.assertCountEqual(mock_send_cap_v_five_pipeline_version_email.call_args[0][0], [self.new_app_user.uuid, self.new_app_user_2.uuid])

    @patch('emails.tasks.send_cap_v_five_pipeline_version_email.delay')
    def test_resend_email_to_user_with_uuid_but_user_disabled(self, mock_send_cap_v_five_pipeline_version_email):
        DisableSendMailUserFactory(app_user=self.new_app_user)
        response = self.post_response(
            {'uuid': f"{self.new_app_user.uuid},{self.new_app_user_2.uuid}"}
        )
        self.assertEqual(response.status_code, 200)

        mock_send_cap_v_five_pipeline_version_email.assert_called_once()
        mock_send_cap_v_five_pipeline_version_email.assert_called_once_with(
            [self.new_app_user_2.uuid])

    @patch('emails.tasks.send_cap_v_five_pipeline_version_email.delay')
    def test_resend_email_to_user_with_force(self, mock_send_cap_v_five_pipeline_version_email):
        DisableSendMailUserFactory(app_user=self.new_app_user)
        response = self.post_response(
            {'uuid': f"{self.new_app_user.uuid},{self.new_app_user_2.uuid}", 'force': 'true'}
        )
        self.assertEqual(response.status_code, 200)

        mock_send_cap_v_five_pipeline_version_email.assert_called_once()
        self.assertCountEqual(mock_send_cap_v_five_pipeline_version_email.call_args[0][0], [self.new_app_user.uuid, self.new_app_user_2.uuid])


class AppInstallUpdateAPIViewTest(ApiBaseTest):
    def setUp(self):
        super().setUp()
        self.app_install_1 = AppInstallFactory(
            app_user=self.app_user,
            os=self.operating_system,
            device_id='44dfdd14-4013-9c42-c4c364a2dcbc',
            registration_unique_token='some token'
        )
        self.app_install_2 = AppInstallFactory(
            app_user=self.app_user,
            os=self.operating_system,
            device_id='44dfdd14-4013-9c42',
            registration_unique_token='some token 2'
        )
        self.app_install_3 = AppInstallFactory(
            app_user=self.app_user,
            os=self.operating_system,
            device_id='44dfdd14-4013-9c42-aaaaa',
            registration_unique_token='some token 3'
        )

    def patch_response(self, data):
        """ Helper method to do the PATCH requests """
        self.c.force_authenticate(self.user)
        url = reverse('api-v2:app-install', kwargs={'org_id': self.organisation.secure_id})
        return self.c.patch(url, format="json", data=data)

    def test_deactivate(self):
        device = TrustdDeviceFactory(app_install=self.app_install_1)
        self.assertTrue(all([not self.app_install_1.inactive, not self.app_install_2.inactive]))
        with patch('trustd.tasks.delete_trustd_devices.delay') as mock_delete_trustd_devices:
            response = self.patch_response({"inactive": True,
                                            "app_install_ids": [self.app_install_1.id, self.app_install_2.id]})
        self.assertEqual(response.status_code, 200)
        self.app_install_1.refresh_from_db()
        self.app_install_2.refresh_from_db()
        self.assertTrue(all([self.app_install_1.inactive, self.app_install_2.inactive]))
        mock_delete_trustd_devices.assert_called_once_with([device.id])

    def test_activate(self):
        self.app_install_1.inactive = True
        self.app_install_1.save()
        self.app_install_2.inactive = True
        self.app_install_2.save()
        # Create a trustd device for the app_install_3, this should not get activated
        self.app_install_3.inactive = True
        self.app_install_3.save()
        TrustdDeviceFactory(app_install=self.app_install_3)

        self.assertTrue(all([self.app_install_1.inactive, self.app_install_2.inactive, self.app_install_3.inactive]))
        response = self.patch_response(
            {"inactive": False,
             "app_install_ids": [self.app_install_1.id, self.app_install_2.id, self.app_install_3.id]}
        )
        self.assertEqual(response.status_code, 200)
        self.app_install_1.refresh_from_db()
        self.app_install_2.refresh_from_db()
        self.app_install_3.refresh_from_db()
        self.assertTrue(all([not self.app_install_1.inactive, not self.app_install_2.inactive]))
        self.assertTrue(self.app_install_3.inactive)

    def test_activate_already_active(self):
        self.assertFalse(self.app_install_1.inactive)
        response = self.patch_response({"inactive": False,
                                        "app_install_ids": [self.app_install_1.id]})
        self.assertEqual(response.status_code, 200)
        self.app_install_1.refresh_from_db()
        self.assertFalse(self.app_install_1.inactive)

    def test_permission_denied(self):
        user = User.objects.create_user(
            username='test', email='<EMAIL>',
            password=self.USER_PASSWORD
        )
        self.c.force_authenticate(user)
        url = reverse('api-v2:app-install', kwargs={'org_id': self.organisation.secure_id})
        response = self.c.patch(url, format="json", data={})
        self.assertEqual(response.status_code, 403)

    def test_empty_ids(self):
        response = self.patch_response({"inactive": False, "app_install_ids": []})
        self.assertEqual(response.status_code, 400)
        self.assertEqual(json.loads(response.content), {'error': 'Inactive field and ids field are required.'})

    def test_inactive_not_bool(self):
        response = self.patch_response({"inactive": 'bad', "app_install_ids": [self.app_install_1.id]})
        self.assertEqual(response.status_code, 400)
        self.assertEqual(json.loads(response.content), {'error': 'Inactive field must be true or false.'})

    def get_notification_data_for_patch(self, inactive, app_install):
        response = self.patch_response({"inactive": inactive,
                                        "app_install_ids": [self.app_install_1.id, self.app_install_2.id]})
        self.assertEqual(response.status_code, 200)
        data = {
            'message_type': 'user_device_reactivated' if not inactive else 'user_device_inactivated',
            'user_name': get_full_name_or_email(self.user),
            'organisation_id': app_install.app_user.organisation_id,
            'partner_or_org_name': self.user.profile.organisation.name,
            'device_user': get_full_name_or_email(app_install.app_user),
            'device_name': app_install.display_os(),
            'url': app_install.url() if not inactive
            else reverse('dashboard:organisation', kwargs={'org_id': self.organisation.secure_id})
        }
        return data

    def test_deactivate_notification(self):
        with patch('api.v2.appusers.views.NotificationHandler.send_notification') as mock_send_notification:
            data_1 = self.get_notification_data_for_patch(inactive=True, app_install=self.app_install_1)
            data_2 = self.get_notification_data_for_patch(inactive=True, app_install=self.app_install_2)
            mock_send_notification.assert_has_calls([call(**data_1), call(**data_2)], any_order=True)

    def test_activate_notification(self):
        self.app_install_1.inactive = True
        self.app_install_1.save()
        self.app_install_2.inactive = True
        self.app_install_2.save()
        with patch('api.v2.appusers.views.NotificationHandler.send_notification') as mock_send_notification:
            data_1 = self.get_notification_data_for_patch(inactive=False, app_install=self.app_install_1)
            data_2 = self.get_notification_data_for_patch(inactive=False, app_install=self.app_install_2)
            mock_send_notification.assert_has_calls([call(**data_1), call(**data_2)], any_order=True)

    def test_activate_notification_only_when_actually_changes_inactive_status(self):
        self.app_install_1.inactive = True
        self.app_install_1.save()
        with patch('api.v2.appusers.views.NotificationHandler.send_notification') as mock_send_notification:
            data = self.get_notification_data_for_patch(inactive=False, app_install=self.app_install_1)
            mock_send_notification.assert_called_once_with(**data)
