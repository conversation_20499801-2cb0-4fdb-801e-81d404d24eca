from django.conf import settings
from django.http import JsonResponse
from django.urls import reverse
from django.db.models import Count, Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from simple_history.utils import bulk_update_with_history

from accounts.mixins import PermissionRoleRequired
from accounts.models import EventLog
from accounts.permissions import DEVICES_PERMISSION, PEOPLE_AND_ORGANISATION_PERMISSION
from analytics.tasks import calculate_organisation_analytics, calculate_app_user_analytics
from api.v1.dashboard.views import AppUserDetailEditAPI
from appusers.models import AppUser, AppInstall, DisableSendMailUser
from appusers.utils import send_emails_to_users_with_kind, send_apps
from beta_features.models import CAP_V_FIVE_BETA_SWITCH, TRUSTD_BETA_SWITCH, STABLE_VERSION_KIND, STABLE_MOBILE_VERSION_KIND
from common.models import Group
from common.utils import get_full_name_or_email
from notifications.handlers import NotificationHandler
from organisations.models import get_organisations


class AppUserApiView(AppUserDetailEditAPI):
    def __app_user_not_found_error(self, request, uuid):
        """
        Checks if the app user exists and returns a 404 if not.
        """
        self.app_user = AppUser.objects.filter(uuid=uuid).first()
        if not self.app_user:
            return Response(
                status=status.HTTP_404_NOT_FOUND, data={"error": _("User not found.")}
            )

    def __organisation_permission_error(self, request):
        """
        Checks if the user has permission to access the organisation and returns a 401 if not.
        """
        self.organisation = get_organisations(request.user, self.app_user.organisation.secure_id)
        if not self.organisation:
            return Response(
                status=status.HTTP_401_UNAUTHORIZED, data={"error": _("You don't have access to this organisation.")}
            )

    def __delete_main_app_user_error(self, request):
        """
        Checks if the user is the main app user and returns a 400 if so.
        """
        if self.app_user.pk == self.app_user.organisation.main_app_user.pk and self.app_user.organisation.bulk_install:
            return Response(
                status=status.HTTP_400_BAD_REQUEST, data={"error": _(
                    "The organisation creator cannot be removed, "
                    "however you may change the name and email address to re-assign this account to another user"
                )}
            )

    def __cannot_delete_yourself_error(self, request):
        """
        Checks if the user is trying to delete themselves and returns a 400 if so.
        """
        if self.app_user.email == request.user.email:
            return Response(status=status.HTTP_400_BAD_REQUEST, data={"error": _("You cannot delete yourself.")})

    def __not_authorized_to_perform_error(self, request):
        """
        Checks if the user is authorized to perform the action and returns a 401 if not.
        """
        allowed_users = self.app_user.organisation.get_management_users(separate_by_group=True)
        if (request.user not in allowed_users["administrators"] | allowed_users["partners"]):
            return Response(
                status=status.HTTP_401_UNAUTHORIZED, data={"error": _("You are not authorized to perform this action.")}
            )

    def delete(self, request, org_id, uuid, *args, **kwargs):
        """
        Delete an app user.
        """
        if error := self.__app_user_not_found_error(request, uuid):
            return error
        if error := self.__organisation_permission_error(request):
            return error
        if error := self.__delete_main_app_user_error(request):
            return error
        if error := self.__cannot_delete_yourself_error(request):
            return error
        if error := self.__not_authorized_to_perform_error(request):
            return error

        self.app_user.active = False
        self.app_user.save()
        # remove user from organisation admins
        self.app_user.organisation.admins.filter(user__email__iexact=self.app_user.email).delete()
        if not settings.IS_TEST:
            calculate_organisation_analytics.delay(self.app_user.organisation.pk)
        self.send_notification(self.app_user)
        return Response(status=status.HTTP_204_NO_CONTENT)


class AppInstallAPIView(viewsets.ViewSet):
    permission_classes = (IsAuthenticated,)
    http_method_names = ['patch']

    def perform_authentication(self, request):
        self.organisations = get_organisations(request.user)
        if not self.organisations:
            return self.permission_denied(request)

    def send_notification(self, app_install, inactive_status):
        """ Send notification """
        message_type = 'user_device_reactivated'
        url = app_install.url()
        if inactive_status:
            message_type = 'user_device_inactivated'
            url = reverse('dashboard:organisation', kwargs={'org_id': app_install.app_user.organisation.secure_id})
        if self.request.user.profile.is_partner:
            partner_or_org_name = self.request.user.profile.partner.name
        else:
            partner_or_org_name = self.request.user.profile.organisation.name
        kwargs = {
            'user_name': get_full_name_or_email(self.request.user),
            'organisation_id': app_install.app_user.organisation_id,
            'partner_or_org_name': partner_or_org_name,
            'device_user': get_full_name_or_email(app_install.app_user),
            'device_name': app_install.display_os(),
            'url': url,
        }
        NotificationHandler.send_notification(message_type=message_type, **kwargs)

    def create_event_log(self, app_install, inactive_status):
        event_type = EventLog.DEVICE_REMOVED if inactive_status else EventLog.DEVICE_REACTIVATED
        # create log
        EventLog.objects.create(
            user=self.request.user,
            organisation=app_install.app_user.organisation,
            type=event_type,
            app_install=app_install
        )

    @staticmethod
    def validate(request):
        """ Validate data """
        if request.data.get('inactive') is None or not request.data.get('app_install_ids', []):
            return {"error": _("Inactive field and ids field are required.")}
        inactive = str(request.data.get('inactive')).lower()
        if inactive not in ['true', 'false']:
            return {"error": _("Inactive field must be true or false.")}
        return None

    @swagger_auto_schema(
        request_body=openapi.Schema(type=openapi.TYPE_OBJECT, properties={
            "inactive": openapi.Schema(type=openapi.TYPE_BOOLEAN),
            "app_install_ids": openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Items(type=openapi.TYPE_INTEGER),
            ),
        }, ),
        responses={
            status.HTTP_200_OK: 'Returns `updated_count` with the number of updated app installs',
            status.HTTP_400_BAD_REQUEST: 'If `inactive` field and ids field are not found '
                                         'or `inactive` field is not true or false.',
        }
    )
    def update(self, request, org_id):
        """ Activate or inactivate a list of app installs """
        error = self.validate(request)
        if error:
            return JsonResponse(status=status.HTTP_400_BAD_REQUEST, data=error)
        # select app installs
        inactive_status = str(request.data.get('inactive')).lower() == 'true'
        app_installs = AppInstall.objects.filter(
            id__in=request.data.get('app_install_ids'),
            inactive=not inactive_status,
            app_user__organisation__id__in=self.organisations.values_list('id', flat=True)
        ).select_related('app_user', 'app_user__organisation', 'trustd_device')
        # we need to remove trustd devices during reactivation since it is not supported by Trustd
        if not inactive_status:
            app_installs = app_installs.filter(trustd_device__isnull=True)
        # send notification and create event log
        for app_install in app_installs:
            self.send_notification(app_install, inactive_status)
            self.create_event_log(app_install, inactive_status)

        app_users_to_update = list(app_installs.values_list("app_user", flat=True))
        trustd_device_ids = list(
            app_installs.filter(trustd_device__isnull=False).values_list('trustd_device__id', flat=True)
        )
        # update app installs
        updated_count = app_installs.count()
        for app_install in app_installs:
            app_install.inactive = inactive_status
            app_install.date_removed = timezone.now() if inactive_status else None
            app_install.modified = timezone.now()
        bulk_update_with_history(app_installs, fields=["inactive", "date_removed", "modified"], model=AppInstall)
        # if the app installs are trustd devices, then do the API call
        if inactive_status and trustd_device_ids:
            from trustd.tasks import delete_trustd_devices
            delete_trustd_devices.delay(trustd_device_ids)
        # run analytics task
        calculate_app_user_analytics.delay(app_users_pk=app_users_to_update)
        return JsonResponse(status=status.HTTP_200_OK, data={"updated_count": updated_count})


class AppUserGroupsBulkAPIView(viewsets.ViewSet):
    """
    API endpoint that allows groups of users to be updated.

    Requires the user to have next permissions:
    - `devices` or `people_and_organisation`
    """
    permission_classes = (IsAuthenticated, PermissionRoleRequired,)
    permission_required = (DEVICES_PERMISSION, PEOPLE_AND_ORGANISATION_PERMISSION)
    http_method_names = ['patch']

    def perform_authentication(self, request):
        self.organisations = get_organisations(request.user)
        if not self.organisations:
            return self.permission_denied(request)

    @staticmethod
    def validate(request):
        """ Validate data """
        if request.data.get('group_ids') is None or not request.data.get('app_user_ids', []):
            return {"error": _("group_ids field and app_user_ids field are required.")}
        return None

    @swagger_auto_schema(
        request_body=openapi.Schema(type=openapi.TYPE_OBJECT, properties={
            "group_ids": openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Items(type=openapi.TYPE_INTEGER),
            ),
            "app_user_ids": openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Items(type=openapi.TYPE_INTEGER),
            ),
        }, ),
        responses={
            status.HTTP_200_OK: 'Returns `updated_count` with the number of updated app users',
            status.HTTP_400_BAD_REQUEST: 'If `group_ids` field and ids of groups are not found '
                                         'or `app_user_ids` field and ids field are not found.',
        }
    )
    def update(self, request, org_id):
        """
        Edit groups of users list
        """
        error = self.validate(request)
        if error:
            return JsonResponse(status=status.HTTP_400_BAD_REQUEST, data=error)
        # select app installs
        group_ids = request.data.get('group_ids')
        groups = [] if group_ids[0] is None else Group.objects.filter(id__in=group_ids).all()

        app_users = AppUser.objects.filter(
            id__in=request.data.get('app_user_ids'),
            organisation__id__in=self.organisations.values_list('id', flat=True)
        ).select_related('organisation')

        updated_count = 0
        for app_user in app_users:
            app_user.groups.set(groups)
            app_user.save()
            updated_count += 1

        return JsonResponse(status=status.HTTP_200_OK, data={"updated_count": updated_count})


class SendAppView(viewsets.ViewSet):
    """
    API endpoint that sends CAP app to selected users.

    Requires the user to have next permissions:
    - `devices` or `people_and_organisation`
    """
    permission_classes = (IsAuthenticated, PermissionRoleRequired,)
    permission_required = (DEVICES_PERMISSION, PEOPLE_AND_ORGANISATION_PERMISSION)
    http_method_names = ['post']

    def __init__(self):
        self.organisation = None

    def perform_authentication(self, request):
        self.organisation = get_organisations(request.user, secure_id=self.kwargs['org_id'])
        if not self.organisation:
            return self.permission_denied(request)

    @swagger_auto_schema(
        request_body=openapi.Schema(type=openapi.TYPE_OBJECT),
        responses={
            status.HTTP_200_OK: 'Returns `sent_cap_apps_to` with the list of ids of notified app users',
        }
    )
    def _get_app_users_from_arr_uuid(self, uuid, organisation):
        arr_uuid = uuid.split(',')
        arr_app_user = AppUser.objects.filter(uuid__in=arr_uuid, organisation=organisation)
        return arr_app_user

    def send(self, request, org_id):
        """
        If force is true and uuids are explicitly passed,
        disregard DisableSendMailUser checks and send the email anyway.
        """
        uuid = request.data.get('uuid')
        force = request.data.get('force', 'false').lower() == 'true'
        kind = request.data.get('email_kind', STABLE_VERSION_KIND)

        possible_kinds = [STABLE_VERSION_KIND, CAP_V_FIVE_BETA_SWITCH, TRUSTD_BETA_SWITCH, STABLE_MOBILE_VERSION_KIND]
        if kind not in possible_kinds:
            return JsonResponse(status=status.HTTP_400_BAD_REQUEST,
                                data={'status': 'email_kind passed unsupported.'})

        data = {'status': 0}
        if uuid:
            arr_app_users = self._get_app_users_from_arr_uuid(uuid, self.organisation)
            if not force:
                arr_app_users = arr_app_users.filter(disablesendmail_appusers__isnull=True)
            if not arr_app_users:
                data = {'status': 0}
            else:
                data = {'status': 1}
                send_emails_to_users_with_kind(self.organisation, arr_app_users, kind)
                if not force:
                    disable_send_mail_users = [DisableSendMailUser(app_user=app_user) for app_user in arr_app_users]
                    DisableSendMailUser.objects.bulk_create(disable_send_mail_users)
        else:
            # send download links, only to those awaiting install
            awaiting_install = AppUser.objects.filter(
                organisation=self.organisation,
                active=True,
            ).annotate(
                active_installs_count=Count("installs", filter=Q(installs__inactive=False))
            ).filter(active_installs_count=0)
            if awaiting_install.count() > 0:
                send_apps(self.organisation, list(awaiting_install.values_list('uuid', flat=True)), reminder=True)
                data = {'status': 1}
            else:
                data = {'status': 0}

        return JsonResponse(status=status.HTTP_200_OK, data=data)
