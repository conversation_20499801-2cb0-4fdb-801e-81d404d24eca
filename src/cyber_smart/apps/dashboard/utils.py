from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.sites.models import Site
from django.db.models import OuterRef, Q, Subquery
from django.http import Http404

from appusers.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ult, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AppInstallCheckStatus
from common.utils import decode_base64_to_string
from organisations.models import OrganisationCheckPermanentFix, Organisation
from rulebook.models import CYBER_ESSENTIALS_PLUS


def get_applications_responses(
        order, distinct, pass_values=None, fail_values=None, return_passed=True, return_failed=True,
        app_install_pk=None, org_pk=None, app_install_report_pk=None, report_filters=None,
        check_result_filters=None,
):
    """
    Returns passed and failed responses.
    :param org_pk: organisation primary key (not required)
    :type org_pk: int
    :param order: order fields for responses
    :type order: tuple
    :param pass_values: values for passed responses
    :type pass_values: tuple
    :param fail_values: values for failed responses
    :type fail_values: tuple
    :param distinct: distinct for responses
    :type distinct: tuple
    :param return_passed: will return passed responses
    :type return_passed: bool
    :param return_failed: will return failed responses
    :type return_failed: bool
    :param app_install_pk: app install pk (not required)
    :type app_install_pk: int
    :param app_install_report_pk: app install report pk (not required)
    :type app_install_report_pk: int
    :param report_filters: filters that will be applied to report query set
    :type report_filters: dict
    :param check_result_filters: filters that will be applied to check result query set
    :type check_result_filters: dict
    :return: passed and failed responses
    :rtype: tuple(CheckResult queryset, CheckResult queryset) or CheckResult queryset

    """
    filters = {
        'app_install__inactive': False,
        'total_responses__isnull': False
    }
    # updated filters with passed
    if report_filters:
        filters.update(report_filters)

    if org_pk:
        filters['app_install__app_user__organisation__pk'] = org_pk

    if app_install_pk:
        filters['app_install__pk'] = app_install_pk

    if app_install_report_pk:
        reports_id_list = AppReport.objects.filter(pk=app_install_report_pk).values('id')
    else:
        reports_id_list = AppReport.objects.filter(**filters).order_by('app_install', '-modified').distinct(
            'app_install'
        ).values('id')

    if not check_result_filters:
        check_result_filters = {}
    # unique responses
    unique = CheckResult.objects.filter(
        report_id__in=Subquery(reports_id_list), app_check__isnull=False, app_check__active=True, **check_result_filters
    ).order_by(
        *order
    ).distinct(
        *distinct
    ).values_list(
        'pk', flat=True
    )

    # permanent fixed checks
    permanent_fixes_query_set = OrganisationCheckPermanentFix.objects.filter(
        organisation__pk=org_pk,
        app_check__active=True
    )
    permanent_fixes = list(permanent_fixes_query_set.values_list('app_check', flat=True))
    permanent_fixes_subquery = permanent_fixes_query_set.filter(app_check_id=OuterRef('app_check__pk'))

    org_filter = {}
    if org_pk:
        org_filter = {'report__app_install__app_user__organisation__pk': org_pk}

    if return_passed:
        # passed responses including manual and auto fixes
        passed_responses = CheckResult.objects.filter(
            Q(response=True) | Q(response=False, fixed=True) | Q(app_check__pk__in=permanent_fixes)
        ).filter(
            pk__in=Subquery(unique)
        ).filter(
            report_id__in=Subquery(reports_id_list), app_check__isnull=False, app_check__active=True, **check_result_filters, **org_filter
        ).select_related(
            'report', 'report__app_install', 'report__app_install__app_user', 'app_check', 'report__app_install__os'
        ).distinct(
            *distinct
        ).annotate(
            permanent_fix=Subquery(permanent_fixes_subquery.values_list('id')),
            last_os_user_username=Subquery(UserLoginHistory.objects.filter(
                app_install__pk=OuterRef("report__app_install_id")
            ).order_by("-created").values('user__username')[:1]),
            last_os_user_domain=Subquery(UserLoginHistory.objects.filter(
                app_install__pk=OuterRef("report__app_install_id")
            ).order_by("-created").values('user__domain')[:1]),
        )

        if pass_values:
            passed_responses = passed_responses.values(*pass_values)

    if return_failed:
        # failed responses excluding manual and auto fixed responses
        failed_responses = CheckResult.objects.filter(
            report_id__in=Subquery(reports_id_list), response=False, fixed=False,
            app_check__isnull=False, app_check__active=True, **check_result_filters, **org_filter
        ).exclude(app_check__pk__in=permanent_fixes).select_related(
            'report', 'report__app_install', 'report__app_install__app_user', 'app_check', 'report__app_install__os'
        ).distinct(
            *distinct
        ).annotate(
            permanent_fix=Subquery(permanent_fixes_subquery.values_list('id')),
            last_os_user_username=Subquery(UserLoginHistory.objects.filter(
                app_install__pk=OuterRef("report__app_install_id")
            ).order_by("-created").values('user__username')[:1]),
            last_os_user_domain=Subquery(UserLoginHistory.objects.filter(
                app_install__pk=OuterRef("report__app_install_id")
            ).order_by("-created").values('user__domain')[:1]),
            latest_status_changed=Subquery(AppInstallCheckStatus.objects.filter(
                app_check=OuterRef('app_check__pk'),
                app_install=OuterRef('report__app_install__pk')
            ).order_by('-status_changed').values('status_changed')[:1])
        )

        if fail_values:
            failed_responses = failed_responses.values(*fail_values)

    if return_passed and return_failed:
        return passed_responses, failed_responses
    elif return_passed and not return_failed:
        return passed_responses
    elif return_failed and not return_passed:
        return failed_responses


def get_applications_responses_2(organisation, checks):
    """
    An optimized version of get_applications relying on CPU rather than database queries.
    Useful when the number of devices is very high.
    """
    org_pk = organisation.pk
    order = ('app_check__pk', 'report__app_install__device_id', 'report__app_install__serial_number', '-report__created', '-modified')
    distinct = ('app_check__pk', 'report__app_install__device_id', 'report__app_install__serial_number')
    values = ('pk', 'app_check__pk', 'report__app_install__id', 'response', 'fixed')

    # Get distinct CheckResults
    distinct_results = CheckResult.objects.filter(
        report__app_install__inactive=False,
        report__total_responses__isnull=False,
        report__app_install__app_user__active=True,
        report__app_install__app_user__organisation__pk=org_pk,
        app_check__isnull=False,
        app_check__active=True,
        app_check__pk__in=checks.values_list('pk', flat=True)
    ).order_by(*order).distinct(*distinct).values(*values)

    # Fetch permanent fixes once
    permanent_fixes = set(OrganisationCheckPermanentFix.objects.filter(
        organisation__pk=org_pk,
        app_check__active=True
    ).values_list('app_check_id', flat=True))

    # Separate into passed and failed responses
    passed_responses = []
    failed_responses = []
    for result in distinct_results:
        if result['response'] or result['fixed'] or result['app_check__pk'] in permanent_fixes:
            passed_responses.append(result)
        else:
            failed_responses.append(result)

    return passed_responses, failed_responses

class CreateDashboardAdmin:
    def __call__(self, app_user: AppUser | None = None, request_user=None,
                 first_name: str | None = None,
                 last_name: str | None = None, email: str | None = None,
                 organisation: Organisation | None = None) -> None:
        """
        Creates a dashboard administrator.
        If app_user is passed it will use app_user data to create a dashboard administrator
        and set app_user as an administrator.
        Otherwise it will use other passed data to create a dashboard administrator.
        """
        if not request_user:
            raise ValueError("Request user is required.")

        if not app_user:
            if not email or not organisation:
                raise ValueError("Email and organisation are required when app_user is not passed.")

        if app_user:
            self.email = app_user.email
            self.first_name = app_user.first_name
            self.last_name = app_user.last_name
            self.organisation = app_user.organisation
            self.app_user = app_user
        else:
            self.email = email
            self.first_name = first_name
            self.last_name = last_name
            self.organisation = organisation
            self.app_user = None

        self.first_name = self.first_name[:30] if self.first_name else ""
        self.last_name = self.last_name[:30] if self.last_name else ""

        if not self.email:
            raise ValueError("Email is required.")

        self.email = self.email.lower()


        self.request_user = request_user
        self.django_users, self.created = self.__get_or_create_django_users()

        for django_user in self.django_users:
            self.__setup_django_user(django_user)
            self.__send_emails(django_user)

    def __setup_django_user(self, django_user):
        """
        Doing initial setup for django user.
        :param django_user: django user
        :type django_user: auth.User
        :return: nothing
        :rtype: None
        """
        # set as active user
        django_user.is_active = True

        # first name and last name
        django_user.first_name = self.first_name
        django_user.last_name = self.last_name
        django_user.save()

        # mark on-boarding as completed
        django_user.profile.onboarding_completed = True
        django_user.profile.skip_payment = True
        django_user.profile.save()

        # create organisation admin relationship
        self.organisation.admins.get_or_create(
            user=django_user,
            is_admin=True
        )

        if self.app_user:
            # set app user as an administrator
            self.app_user.is_admin = True

            self.app_user.save()

    def __get_or_create_django_users(self):
        """
        Doing a lookup in the database to find existing django users with such email or username and returns it.
        If nothing was found it creates a new django user and returns it.
        :return: django users
        :rtype: list
        """
        email = self.email
        django_users = get_user_model().objects.filter(Q(email__iexact=email) | Q(username__iexact=email))
        created = False

        if not django_users:
            # create a new django user
            django_user_new, created = get_user_model().objects.get_or_create(
                email=email, username=email
            )
            django_users = [django_user_new]

        return django_users, created

    def __send_emails(self, user) -> None:
        """
        Sends credentials to the user via email.
        """
        from emails.notifications import OrganisationAdminCredentialsEmail

        class Request:
            def __init__(self, request_user):
                self.user = request_user

        if not settings.IS_TEST:
            if self.created:
                OrganisationAdminCredentialsEmail(
                    user=user,
                    organisation=self.organisation,
                    request=Request(self.request_user)
                ).send()


set_dashboard_admin: CreateDashboardAdmin = CreateDashboardAdmin()


def remove_dashboard_admin(app_user):
    """
    Removes passed application user from dashboard administrators.
    :param app_user: application user
    :type app_user: organisations.models.AppUser instance
    :return: nothing
    :rtype: None
    """
    try:
        auth_user = get_user_model().objects.get(email__iexact=app_user.email)
    except get_user_model().DoesNotExist:
        pass
    else:
        app_user.organisation.admins.filter(user=auth_user, is_admin=True).delete()
    finally:
        app_user.is_admin = False
        app_user.save()


def convert_serial_number(serial_number):
    """
    Converts serial number from base64 to utf-8 string.
    :param serial_number: serial number
    :type serial_number: base64
    :return: converted to utf-8 serial number or None
    :rtype: unicode or str or None
    """
    return decode_base64_to_string(serial_number)


def is_ce_plus_and_not_iasme(user, cert_type):
    return cert_type == CYBER_ESSENTIALS_PLUS and not (user.profile.is_partner and user.profile.partner.iasme_cb)


def turn_cert_type_into_int(cert_type):
    try:
        cert_type = int(cert_type)
    except ValueError:
        raise Http404()
    return cert_type


def get_current_subdomain():
    try:
        # get site sub domain e.g. bau / thats / app (transforms performed on desktop-distribution-msi/pkg)
        return Site.objects.get_current().domain.split('.')[0].split(':')[0]
    except (Site.DoesNotExist, IndexError, ValueError, AttributeError):
        return 'development'


def get_bulk_live_server_link(app_type, uuid):
    """
    Returns live links from dynamic bulk server package generator
    app.cybersmart.co.uk > app
    localhost:8000 > localhost
    :param type: either msi or pkg
    """
    if settings.BULK_LIVE_LINK_SUFFIX:
        suffix = settings.BULK_LIVE_LINK_SUFFIX
    else:
        suffix = get_current_subdomain()
    return "{0}/{1}/{2}".format(settings.BULK_LIVE_LINK_SERVERS.get(app_type), uuid, suffix)


def get_bulk_live_server_link_cap_v5(app_type, uuid):
    """
    Returns live links from dynamic bulk server package generator
    for CAP V5 Continous Delivery flow.
    :param uuid: app_user uuid
    :param app_type: either msi or pkg
    """
    return f"{settings.CAP_V5_BULK_LIVE_LINK_SERVERS.get(app_type)}/build/{uuid}"
