import logging

from celery import shared_task as task
from django_redis import get_redis_connection

from analytics.tasks import calculate_app_user_analytics, calculate_app_install_analytics
from appusers.models import InstalledSoftwarePartnerSummary
from opswat.constants import APP_INSTALL_ANALYTICS_QUEUE_KEY
from organisations.models import Organisation

logger = logging.getLogger(__name__)


@task(queue="materialized")
def refresh_installed_software_partner_summary() -> None:
    """Refreshes the InstalledSoftwarePartnerSummary materialized view."""
    try:
        InstalledSoftwarePartnerSummary.refresh(concurrently=True)
    except Exception:
        InstalledSoftwarePartnerSummary.refresh(concurrently=False)


@task(queue="materialized")
def process_pending_app_install_analytics() -> None:
    """Processes pending AppInstall analytics updates stored in Redis."""
    redis_client = get_redis_connection("default")
    app_install_ids = redis_client.lrange(APP_INSTALL_ANALYTICS_QUEUE_KEY, 0, -1)

    if not app_install_ids:
        return

    redis_client.delete(APP_INSTALL_ANALYTICS_QUEUE_KEY)

    for app_install_id in app_install_ids:
        calculate_app_install_analytics.apply_async(
            args=[int(app_install_id)]
        )


@task(queue="materialized")
def refresh_installed_software_summary_partner() -> None:
    refresh_installed_software_partner_summary()


@task
def turn_off_uba(organisation_id) -> None:
    """
    Turns off UBA for given organisation.
    """
    try:
        organisation = Organisation.objects.get(id=organisation_id)
    except Organisation.DoesNotExist:
        logger.error(f"Organisation with id `{organisation_id}` does not exist.")
        return
    else:
        if not organisation.bulk_install:
            logger.error("UBA can be disabled only for bulk install organisations.")
            return
        if not organisation.is_uba_enabled:
            logger.error(f"UBA is already disabled for organisation `{organisation_id}`.")
            return
        if not organisation.main_app_user:
            logger.error(f"Main app user does not exist for organisation `{organisation_id}`.")
            return
        # assign all devices to the main app user
        organisation.all_devices.update(app_user=organisation.main_app_user)
        # disable UBA
        organisation.settings.uba_enabled = False
        organisation.settings.save()
        # recalculate organisation's analytics
        for app_user in organisation.app_users.all():
            calculate_app_user_analytics.delay(app_user.id)
        # after this API will start giving main app user uuid in the response to the client applications
        # and forcing them to update their config
        # if they are still trying to report not to the main app user
