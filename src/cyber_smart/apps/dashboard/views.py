import base64
import json
import logging
import re
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from http import HTTPStatus
from itertools import chain
from typing import Any
from urllib.parse import urlencode

import requests
import sentry_sdk
import waffle
from accounts.forms import ChangePlanForm
from accounts.mixins import PermissionRoleRequiredMixin
from accounts.models import EventLog, TrackingTimeUsers
from accounts.permissions import (
    CERTIFICATES_AND_INSURANCE_PERMISSION,
    DEVICES_PERMISSION,
    PEOPLE_AND_ORGANISATION_PERMISSION,
)
from analytics.models import (
    AppUserAnalytics,
    ManagementAnalytics,
    OrganisationAnalytics,
)
from analytics.tasks import (
    calculate_app_report,
    calculate_app_user_analytics,
    calculate_organisation_analytics,
    record_report_download_usage,
)
from api.v1.dashboard.serializers import AppUserSerializer
from appusers.forms import AppUserEnrollForm, FakeAppInstallForm
from appusers.models import (
    AppF<PERSON>,
    AppInstall,
    AppInstallActivationLog,
    AppInstallOSUser,
    AppInstallPolicyAgreement,
    AppOSInstalledSoftware,
    AppReport,
    AppUser,
    CheckAutoFix,
    CheckManualFix,
    CheckResult,
    InstalledSoftwareAppInstallIndividual,
    SoftwarePackageCVEs,
    UserLoginHistory,
    get_app_install,
)
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from appusers.utils import (
    generate_app_last_api_report,
    get_steps_to_secure_pdf,
    send_apps,
)
from beta_features.models import CAP_V_FIVE_BETA_SWITCH, TRUSTD_BETA_SWITCH, BetaFeature
from beta_features.templatetags.beta_feature_enrolment import (
    can_partner_enable_feature_enrolment,
    is_organisation_enrolled_in_feature,
)
from beta_features.utils import (
    get_app_install_cap_v_five_beta_q_object,
    get_app_install_trustd_beta_q_object,
    get_beta_app_install_queryset_filter,
    is_cap_v_five_ga_enabled,
    is_trustd_mobile_available_for_organisation,
)
from billing.mixins import PaymentMixin
from billing.models import PurchaseOrder
from billing.providers.chargebee import plans
from billing.providers.chargebee.exceptions import ChargebeeError
from billing.providers.chargebee.plans import (
    ADDONS_IDS,
    CE_GDPR_PLAN_ANNUAL_ID,
    CE_GDPR_PLAN_MONTHLY_ID,
    CERT_MONTHLY_PLANS_IDS,
    CERT_PLANS_IDS,
    DIRECT_CUSTOMER_V4_APP_CEP_ANNUAL,
    DIRECT_CUSTOMER_V4_APP_CEP_MONTHLY,
    DIRECT_CUSTOMER_V4_APP_GDPR_ANNUAL,
    DIRECT_CUSTOMER_V4_APP_GDPR_MONTHLY,
    DIRECT_CUSTOMER_V4_CE_ANNUAL_2024,
    DIRECT_CUSTOMER_V4_CE_MONTHLY_2024,
    DIRECT_CUSTOMER_V4_CEP_ANNUAL,
    DIRECT_CUSTOMER_V4_CEP_MONTHLY,
    DIRECT_CUSTOMER_V4_GDPR_ANNUAL,
    DIRECT_CUSTOMER_V4_GDPR_MONTHLY,
    DIRECT_CUSTOMER_V4_V5_PLANS,
    DIRECT_CUSTOMER_V5_APP_CE_ANNUAL_2024,
    DIRECT_CUSTOMER_V5_APP_CE_MONTHLY_2024,
)
from billing.providers.chargebee.wrapper import update_customer_meta_data
from billing.templatetags.plans import get_plan_price
from billing.utils import (
    get_currency_sign,
    get_plan_object_from_cache,
    get_plans,
    is_coupon_valid,
)
from common.mixins import FilteringViewMixin, OrderingViewMixin, SearchViewMixin
from common.models import ProjectSettings, StaticFiles
from common.utils import (
    SafePaginator,
    clean_serial_number,
    escape_csv,
    get_full_name_or_email,
    get_secret_pair_value,
    handle_user_input,
    uuid_is_valid,
)
from dashboard.forms import DeclarationForm, OrganizationSettingsForm
from dashboard.mixins import (
    AdminForSingleOrgMixin,
    CheckReportGetterMixin,
    CSVReportMixin,
)
from dashboard.utils import (
    convert_serial_number,
    get_applications_responses,
    get_applications_responses_2,
    get_bulk_live_server_link,
    get_bulk_live_server_link_cap_v5,
    is_ce_plus_and_not_iasme,
    turn_cert_type_into_int,
)
from dateutil.relativedelta import relativedelta
from distributors.models import DistributorMarketingPack
from django import forms
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.sites.models import Site
from django.core.exceptions import ObjectDoesNotExist, PermissionDenied, ValidationError
from django.core.files.base import ContentFile
from django.core.mail import EmailMessage
from django.core.paginator import Paginator
from django.db import OperationalError
from django.db.models import (
    BooleanField,
    Case,
    Count,
    DateTimeField,
    Exists,
    F,
    FloatField,
    OuterRef,
    Prefetch,
    Q,
    QuerySet,
    Subquery,
    TextField,
    Value,
    When,
)
from django.db.models.functions import Coalesce, Concat, NullIf
from django.forms import model_to_dict
from django.forms.models import formset_factory
from django.http import (
    Http404,
    HttpRequest,
    HttpResponse,
    HttpResponseRedirect,
    JsonResponse,
)
from django.shortcuts import get_object_or_404, redirect, render
from django.test.utils import override_settings
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.encoding import smart_str
from django.utils.safestring import SafeString, mark_safe
from django.utils.timesince import timesince
from django.utils.translation import gettext as _
from django.utils.translation import gettext_lazy
from django.views.generic import (
    DeleteView,
    ListView,
    RedirectView,
    TemplateView,
    UpdateView,
    View,
)
from django.views.generic.edit import FormView
from emails.notifications import send_declaration_sign_notifications
from insurance.insurers import IASME_INSURER, SUPERSCRIPT_INSURER
from insurance.utils import get_insurance_upsell_questions
from lms.handlers import LmsProvider
from lms.tasks import add_user_handlers
from notifications.handlers import NotificationHandler
from notifications.models import MarketingBanner
from oauth2_provider.models import AccessToken
from operating_systems.utils import get_organisation_unsupported_os_releases
from opswat.models import CVE, ProductVersion, InstalledProduct, InstalledProductVersion
from opswat.utils import parse_composite_ids
from opswat_patch.utils import enrich_installed_software_with_installers
from vulnerabilities.utils import OPSWAT_SOURCE
from organisations.forms import (
    ApprovedDomainForm,
    ManageFeaturesForm,
    ManageNotificationsForm,
    ManageOrganisationForm,
)
from organisations.models import (
    Organisation,
    OrganisationAdmin,
    OrganisationApprovedDomain,
    OrganisationCertification,
    OrganisationCheckPermanentFix,
    OrganisationPolicy,
    OrganisationPolicyVersion,
    OrganisationUserSync,
    get_organisations,
)
from organisations.tasks import (
    send_declaration_sent_out_for_signing,
    send_declaration_signing_reminder_email,
)
from organisations.utils import is_generic_domain
from partners.models import PartnerMarketingPack
from regions.utils import is_eu_geo
from rulebook.forms import ApprovalEmailForm
from rulebook.models import (
    CERTIFICATES,
    CYBER_ESSENTIALS,
    CYBER_ESSENTIALS_PLUS,
    CYBER_ESSENTIALS_PLUS_VERBOSE,
    CYBER_ESSENTIALS_VERBOSE,
    CYBERSMART_COMPLETE,
    GDPR,
    IASME_GOVERNANCE,
    IASME_GOVERNANCE_VERBOSE,
    AppCheck,
    AssessorNote,
    CertificateType,
    CertificationSurvey,
    OperatingSystem,
    SurveyDeclaration,
    SurveyQuestion,
    SurveyQuestionChoices,
    SurveyQuestionTopic,
    SurveyResponse,
    get_questionnaire_queryset,
)
from rulebook.pervade.tasks import generate_declaration_pdf_task
from rulebook.questions import (
    QUESTION_CODE_IASME_INSURANCE_EMAIL,
    QUESTION_CODE_IASME_INSURANCE_HEAD_OFFICE,
    QUESTION_CODE_IASME_INSURANCE_OPT_IN,
    QUESTION_CODE_IASME_INSURANCE_REVENUE,
    QUESTION_CODE_SUPERSCRIPT_INSURANCE_OPT_IN,
)
from rulebook.utils import (
    SurveyTopicStat,
    create_initial_responses,
    fill_survey_responses_for_debug_mode,
    forbid_declaration_submit_for_trial_accounts,
    generate_csv_organisation_survey,
    get_latest_version,
    get_questionnaire_html_report,
    get_questionnaire_status_structure,
    get_topics_status_structure,
    handle_checkbox_questions,
    handle_conscious_widget,
    handle_org_type_questions,
    handle_questions_with_parent_yes_no_radio_button,
    handle_radio_questions,
    handle_single_value_questions,
    handle_yes_no_text_widget,
)
from signup.mixins import OrganizationMixin, OrganizationTrainingMixin, SignupMixin
from smart_policies.utils import ALLOWED_EXTENSIONS, ALLOWED_TYPES
from trustd.utils import build_hostname
from upload_validator import FileTypeValidator
from vulnerabilities.models import CVERepository
from vulnerabilities.utils import aggregate_installed_software_counts

User = get_user_model()
logger = logging.getLogger(__name__)


class SyncConnectRedirectView(SignupMixin, RedirectView):

    def get_redirect_url(self, *args, **kwargs):
        provider = self.request.GET.get('provider')
        if not provider:
            raise Http404()

        self.request.session['con_type'] = 'sync'
        self.request.session['secure_id'] = kwargs.get('org_id')
        if provider == 'microsoft':
            url = reverse('microsoft_login')
        else:
            url = reverse('google_login')
        return url + '?' + urlencode({'process': 'connect'})


class SyncDisconnectView(SignupMixin, View):

    def get(self, request, org_id):
        response = {'status': 0}
        try:
            organisation = get_organisations(request.user, org_id)
            sync_obj = organisation.organisationusersync
            sync_obj.delete()
        except Exception:
            pass
        else:
            response = {'status': 1}

        return JsonResponse(response)


class CVEView(SignupMixin, TemplateView):
    template_name = "dashboard/cve_list.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["cves"] = []  # Initialize empty list by default

        software_id = self.request.GET.get("software_id", "")
        if not software_id:
            raise Http404("Software ID is required")

        opswat_ids, regular_ids = parse_composite_ids(software_id)

        context["cves"] = []
        if regular_ids:
            # Regular software CVEs
            vulnerable_packages = SoftwarePackageCVEs.objects.filter(software_id__in=regular_ids)
            context["cves"].extend(CVERepository.objects.filter(
                cve_id__in=list(chain.from_iterable(vulnerable_packages.values_list("cves", flat=True)))
            ).order_by('-base_score', '-published_at').distinct()[:10])
        if opswat_ids:
            # OPSWAT software CVEs
            product_versions = ProductVersion.objects.filter(id__in=opswat_ids)
            # Get all CVEs for this product versions
            cves = CVE.objects.filter(
                product_version__in=product_versions
            ).distinct().order_by('-severity_index', '-published_at')[:10]
            # Map OPSWAT CVE fields to match CVERepository fields for template
            context["cves"].extend([{
                'cve_id': cve.cve_id,
                'base_score': cve.severity_index / 10.0,  # Convert 0-100 to 0-10 scale
                'base_severity': cve.get_severity_display(),
                'published_at': cve.published_at,
                'description': cve.description,
                'references': cve.details.get('references', []) if cve.details else []
            } for cve in cves])

        if not context["cves"]:
            raise Http404("No records found")

        return context


class Dashboard(AdminForSingleOrgMixin, SignupMixin, ListView):
    """ Main Dashboard with list of Organisations """
    template_name = 'dashboard/dashboard-core.html'
    paginate_by = 20
    paginator_class = SafePaginator

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        if hasattr(request.user, "profile") and request.user.profile.is_partner and \
                not request.user.profile.is_distributor:
            return HttpResponseRedirect(reverse("partners:dashboard"))
        if hasattr(request.user, "profile") and request.user.profile.is_distributor:
            if waffle.switch_is_active("distributor-new-organisations"):
                return HttpResponseRedirect(reverse("distributors:organisations"))
        return super().dispatch(request, *args, **kwargs)

    def filter_queryset(self, organisations):
        """Filters organisations based on if they are passing/failing"""
        filter_arg = self.request.GET.get("filter")
        if filter_arg == 'failing':
            failing_analytics_orgs = self.organisations_analytics.filter(
                pass_percentage__lt=100.0
            ).values_list('organisation__id', flat=True)
            organisations = organisations.filter(id__in=failing_analytics_orgs)
        elif filter_arg == 'passing':
            organisations = organisations.filter(
                id__in=self.passing_analytics.values_list('organisation__id', flat=True)
            )
        return organisations

    def search_queryset(self, organisations):
        """Searches organisations"""
        self.search_arg = self.request.GET.get("search")
        if self.search_arg:
            queries = Q(name__icontains=self.search_arg)
            organisations = organisations.filter(queries)
        return organisations

    def get_order_by(self):
        """When ordering a specific column by asc/desc"""
        order_by = self.request.GET.get("order_by")
        order_arg = ['-created']
        if order_by:
            order_by_filter = F(order_by).asc(nulls_last=True)
            if '-' in order_by:
                order_by_filter = F(order_by.split('-')[1]).desc(nulls_last=True)
            order_arg.insert(0, order_by_filter)
        return order_arg

    def get_organisations(self):
        organisations = Organisation.objects.none()
        # if user is partner user (or org admin), then show only orgs for that user
        # note that we are annotating to user_has_access to always True as long as they have orgs
        # since user always has access to their orgs
        if (self.request.user.profile.is_partner and not self.request.user.profile.is_distributor) \
                or self.request.user.organisation_admins.exists():
            organisations = get_organisations(self.request.user, as_queryset=True).annotate(user_has_access=Count('id'))
        # in case the user is a distributor user, we show all orgs for that distributor
        # but only orgs that the distributor can have access to will have links, so we annotate the query with this
        elif self.request.user.profile.is_distributor:
            organisations = Organisation.objects.filter(
                partner__distributor=self.request.user.profile.get_distributor
            ).annotate(
                user_has_access=Case(
                    When((
                            Q(is_partner_org=True) & Q(partner__distributor__access_partners_organisations=True)
                            | Q(is_partner_org=False) & Q(partner__distributor__access_partners_clients=True)
                    ), then=True),
                    default=False,
                ),
            )
        return organisations.annotate(
            has_gdpr=Count(Case(When(certifications__version__type__type=GDPR, then=1))),
            has_cep=Count(Case(When(certifications__version__type__type=CYBER_ESSENTIALS_PLUS, then=1))),
        ).select_related(
            'analytics', 'partner', 'partner__distributor'
        ).prefetch_related(
            Prefetch('certifications', OrganisationCertification.objects.all().select_related(
                'version', 'version__type', 'survey', 'survey__declaration'
            )), 'admins', 'admins__user'
        ).order_by(
            *self.get_order_by()
        )

    def get_queryset(self):
        organisations = self.get_organisations()

        self.organisations_analytics = OrganisationAnalytics.objects.filter(organisation__in=organisations)
        self.passing_analytics = self.organisations_analytics.filter(pass_percentage__gte=100.0)
        organisations = self.filter_queryset(organisations)
        return self.search_queryset(organisations)

    def get_context_data(self, *, object_list=None, **kwargs):
        context = super().get_context_data()
        paginator = context.get('paginator')

        iasme_cb = self.request.user.profile.is_partner and self.request.user.profile.partner.iasme_cb
        if iasme_cb:
            # certifications for cb partner staff
            certifications_ready_to_assessment_count = OrganisationCertification.objects.filter(
                status__in=(OrganisationCertification.DECLARATION_SIGNED, OrganisationCertification.REQUIRES_ATTENTION),
                organisation_id__in=self.object_list.values_list('pk', flat=True),
                organisation__is_test=False
            ).distinct().count()
        else:
            # certifications for admin staff, all certifications except cb
            certifications_ready_to_assessment_count = OrganisationCertification.objects.filter(
                status__in=(OrganisationCertification.DECLARATION_SIGNED, OrganisationCertification.REQUIRES_ATTENTION),
                organisation__is_test=False
            ).exclude(organisation__partner__iasme_cb=True).distinct().count()

        passing_count = self.passing_analytics.count()
        failing_count = self.organisations_analytics.count() - passing_count

        query = self.request.GET.dict()
        if "page" in query:
            query.pop("page")
        context.update({
            'passing_organisations_count': passing_count,
            'failing_organisations_count': failing_count,
            'total_organisations_count': paginator.count,
            'managed_users_count': self.request.user.profile.get_all_appusers.count(),
            'devices_installed_count': self.request.user.profile.get_installed_devices.count(),
            'can_access_marketing_pack': self.request.user.profile.is_partner or paginator.count > 0,
            'iasme_cb': iasme_cb,
            'iasme_cb_assessment_organisations_count': certifications_ready_to_assessment_count,
            "params_query": urlencode(query),
            'param__search': self.search_arg,
        })
        return context


class PartnerDashboard(SignupMixin, View):  # Partner Dashboard
    template_name = 'dashboard/dashboard-partner.html'

    def get(self, request, *args, **kwargs):
        partner_user = request.user.profile.partner_user
        distributor = request.user.profile.distributor
        organisations = get_organisations(request.user, as_queryset=True)
        # allow access only for partners, distributors and for users that have access to organisations
        # created by a partner
        if not partner_user and not organisations.count() and not distributor:
            return HttpResponseRedirect(reverse('dashboard:home'))

        if distributor:
            # if current user is a distributor
            marketing_packs = DistributorMarketingPack.objects.filter(distributor=distributor.distributor)
        elif partner_user:
            # if current user is a partner
            marketing_packs = PartnerMarketingPack.objects.filter(partner=partner_user.partner)
            # default to distributor marketing pack
            if not marketing_packs:
                marketing_packs = DistributorMarketingPack.objects.filter(distributor=partner_user.partner.distributor)
        else:
            # if current user is a user that have access to any organisation created by a partner
            marketing_packs = PartnerMarketingPack.objects.filter(
                partner__id__in=organisations.order_by(
                    'partner'
                ).distinct('partner').values_list('partner__id', flat=True)
            )

        return render(request, self.template_name, {
            'marketing_packs': marketing_packs
        })



class OrganisationDashboardTemplate(SearchViewMixin, FilteringViewMixin, OrderingViewMixin, TemplateView):
    """
    Determines view template and context according to organisation type.
    """
    paginate_by = 20

    ordering_fields_map_used = False

    filtering_fields = ["secure_entities"]

    SEARCH_TYPE_SELF = 'self'
    SEARCH_TYPE_UBA = 'uba'

    @staticmethod
    def get_v6_plan_free_quantity(organisation):
        """ V6 CAP only plans have a free quantity amount, this method returns it """
        v6_free_quantity = None
        if organisation.eligible_for_freemium and not organisation.customer.has_payment_method:
            subscription = organisation.customer.subscriptions.live_subscriptions().filter(
                plan_id__in=plans.ALL_SOFTWARE_PLANS
            ).first()
            v6_free_quantity = subscription.plan_free_quantity if subscription else None
        return v6_free_quantity

    def get_base_app_installs_queryset(self, organisation, show_hidden_apps):
        """
        Returns base app install queryset.
        By default it returns all apps excluding inactive,
        but if show_hidden_apps is True it also returns inactive app installs.
        :param show_hidden_apps: if True returns all apps including inactive, otherwise returns all active apps
        :type show_hidden_apps: bool
        :return: base apps queryset
        :rtype: AppInstall queryset
        """
        installs_query = AppInstall.objects.filter(id__in=organisation.app_installs)
        if not show_hidden_apps:
            installs_query = installs_query.filter(inactive=False)
        installs_query = installs_query.order_by("inactive")
        return self.filtering_queryset(installs_query)

    def get_app_users(self, request, organisation, search_type=None):
        """
        Returns organisations app users with prefetched essential data.
        :param request: http request
        :type request: HttpRequest
        :param organisation: for which organisation return app users
        :type organisation: organisations.Organisation
        :return: app users queryset
        :rtype: AppUser queryset
        """
        include_inactive_apps = request.GET.get("show_hidden_apps", False) == "true"
        apps = self.get_base_app_installs_queryset(organisation, include_inactive_apps)

        secure_entities_filter = {}
        if request.GET.get("secure_entities") == "true":
            secure_entities_filter['id__in'] = organisation.passed_users
        elif request.GET.get("secure_entities") == "false":
            secure_entities_filter['id__in'] = organisation.failed_users

        app_installs_without_deprecated_duplicates = AppInstall.objects.installs_without_deprecated_duplicates_for_organisation(organisation)
        app_users = AppUser.objects.filter(
            organisation=organisation, active=True,
            **secure_entities_filter
        ).select_related("analytics").prefetch_related(
            Prefetch("installs", apps.select_related("os").prefetch_related(
                Prefetch("reports", AppReport.objects.all())
            ).select_related("analytics").annotate(
                is_beta_precomputed=Exists(
                    AppInstall.objects.filter(get_beta_app_install_queryset_filter(), pk=OuterRef('pk'))
                )
            ).annotate(
                latest_local_user_is_admin_account_precomputed=Subquery(
                    AppInstallOSUser.objects.filter(
                        app_install__pk=OuterRef('pk'),
                    ).order_by('-created').values('is_admin_account')[:1]
                )
            ).order_by('inactive', 'id')
        )
        ).prefetch_related(
            Prefetch("installs__os_users", AppInstallOSUser.objects.all())
        ).prefetch_related(
            Prefetch("installs__policies", AppInstallPolicyAgreement.objects.filter(
                agreed_date__isnull=False,
                version__policy__active=True,
                version__active=True,
                version__main=True
            ).distinct("version__policy"))
        ).prefetch_related(
            Prefetch("installs__reports", AppReport.objects.filter(total_responses__isnull=False).order_by(
                "app_install", "-modified"
            ).distinct("app_install"))
        ).annotate(
            most_recent_check_in_precomputed=Subquery(
                AppInstall.objects.filter(
                    id__in=AppInstall.objects.installs_without_deprecated_duplicates().filter(
                        app_user=OuterRef(OuterRef('pk'))
                    ),
                    reports__isnull=False,
                    inactive=False
                ).order_by('-reports__created').values('reports__created')[:1]
            )
        ).annotate(
            name_precomputed=Concat(F('first_name'), Value(' '), F('last_name'))
        ).annotate(
            # Gets the first install_text for each app_user.
            # This does not concatenate all AppInstall.install_text_values
            # as this is not possible in Django native ORM (or I can't figure it out).
            # So use this value only for ordering, and not for displaying in frontend.
            first_install_text_precomputed=Subquery(
                AppInstall.objects.filter(app_user=OuterRef('pk'), id__in=app_installs_without_deprecated_duplicates).annotate(
                    install_text=Concat(
                        F('hostname'), Value(' ['),
                        Case(
                            When(Q(caption='Unknown') | Q(caption__isnull=True) | Q(caption=''),
                                then=Coalesce(F('os__title'), Value(_('Unknown')))),
                            default=F('caption'),
                            output_field=TextField()
                        ),
                        Value(']'),
                        Case(
                            When(machine_model__isnull=False, then=Concat(Value(' - '), F('machine_model'))),
                            default=Value(''),
                            output_field=TextField()
                        ),
                        Value(' (app v'),
                        F('app_version'),
                        Value(')'),
                        output_field=TextField()
                    )
                ).order_by('inactive', 'id').values('install_text')[:1],
                output_field=TextField()
            )
        ).annotate(
            status_precomputed=Coalesce(
                Subquery(
                    AppUserAnalytics.objects.filter(appuser=OuterRef('pk')).values('pass_percentage')[:1]
                ),
                Value(0.0),
                output_field=FloatField()
            )
        ).annotate(
            is_not_main_app_user=Case(
                When(
                    email__startswith=Organisation.BULK_DEPLOY_PREFIX,
                    is_admin=True,
                    then=Value(False)
                ),
                default=Value(True),
                output_field=BooleanField()
            )
        )

        ordering_arg_for_distinct = request.GET.get(self.ordering_query_param, "").lstrip("-")
        order_by_default_is_not_main_app_user = False
        if not ordering_arg_for_distinct:
            # By default unassigned devices (attached to main_app_user) should appear at the start of the list.
            ordering_arg_for_distinct = 'is_not_main_app_user'
            order_by_default_is_not_main_app_user = True
        # In postgres, order by argument has to be included in distinct
        app_users = app_users.distinct(ordering_arg_for_distinct, 'pk')
        app_users = self.search_queryset(app_users, search_type=search_type)
        app_users = self.filtering_queryset(app_users)
        if order_by_default_is_not_main_app_user:
            app_users = app_users.order_by('is_not_main_app_user')
        else:
            app_users = self.ordering_queryset(app_users)
        return app_users

    def filtering_queryset(self, queryset: QuerySet) -> QuerySet:
        """
        Filters queryset based on the request GET parameters, applies only to app installs.
        """
        if queryset.model != AppInstall:
            return queryset

        filter_param = self.request.GET.get(self.filtering_query_param)

        if filter_param == CAP_V_FIVE_BETA_SWITCH:
            return queryset.filter(get_app_install_cap_v_five_beta_q_object())
        elif filter_param == TRUSTD_BETA_SWITCH:
            return queryset.filter(get_app_install_trustd_beta_q_object())
        else:
            return queryset.exclude(get_beta_app_install_queryset_filter())

    def self_enrollment_template(self, request, organisation):
        """
        Returns template name and the context for organisations that have chosen individual enrollment.
        :param request: http request
        :type request: HttpRequest
        :param organisation: for which organisation render template
        :type organisation: organisations.Organisation
        :return: template name and context
        :rtype: tuple
        """
        if not hasattr(organisation, "analytics"):
            calculate_organisation_analytics(organisation.pk)

        analytics = OrganisationAnalytics.objects.get(organisation=organisation)
        installed_users_count = analytics.installed_users_count
        enrolled_users_count = analytics.total_users_count
        secure_users_count = analytics.secure_users_count
        insecure_users_count = analytics.insecure_users_count
        awaiting_install_count = enrolled_users_count - installed_users_count

        app_users = self.get_app_users(request, organisation, search_type=self.SEARCH_TYPE_SELF)

        extra_app_users_id = organisation.get_extra_app_users_id(app_users)
        app_users = SafePaginator(app_users, self.paginate_by)


        context = {
            # STATS
            "total_app_users": enrolled_users_count,
            "installed_app_users": installed_users_count,
            "user_failing": insecure_users_count,
            "user_passing": secure_users_count,

            # APP USER DATA
            "awaiting_install_count": awaiting_install_count,
            "organisation": organisation,
            "policies_count": organisation.policies.filter(active=True).count(),
            "policies_support": organisation.policies_support,
            "certification": organisation.certifications.order_by("-created").first(),

            'app_users': app_users.get_page(int(request.GET.get("page", 1))),
            "show_hidden_apps": request.GET.get("show_hidden_apps", False) == "true",
            'extra_app_users_id': extra_app_users_id,
            'v6_free_quantity': self.get_v6_plan_free_quantity(organisation),
            'app_users_to_send_email_to': serialize_app_users(get_app_users_to_send_email_to(organisation)),
            **self.get_context_data(),
        }
        return "dashboard/dashboard-self.html", context

    def search_queryset(self, queryset, search_type=None):
        search = self.request.GET.get(self.search_query_param)
        if not search:
            return queryset

        if search_type == self.SEARCH_TYPE_SELF:
            queryset = queryset.filter(
                Q(name_precomputed__icontains=search) |
                Q(email__icontains=search) |
                Q(first_install_text_precomputed__icontains=search)
            )
        elif search_type == self.SEARCH_TYPE_UBA:
            queryset = queryset.filter(
                Q(email__icontains=search) |
                Q(first_install_text_precomputed__icontains=search)
            )
        else:
            queryset = queryset.filter(
                    Q(last_login_username_precomputed__icontains=search) |
                    Q(hostname__icontains=search) |
                    Q(machine_model__icontains=search) |
                    Q(serial_number_and_device_id__icontains=search) |
                    Q(os_title__icontains=search)
                )
        return queryset

    def bulk_enrollment_template(self, request, organisation):
        """
        Returns template name and the context for organisations that have chosen bulk enrollment.
        :param request: http request
        :type request: HttpRequest
        :param organisation: for which organisation render template
        :type organisation: organisations.Organisation
        :return: template name and context
        :rtype: tuple
        """
        main_app_user = organisation.main_app_user
        show_hidden_apps = request.GET.get("show_hidden_apps", False) == "true"
        inactive_filter = {}
        # if user does not want to see inactive apps
        if not show_hidden_apps:
            inactive_filter = {'inactive': False}

        secure_devices_filter = {}
        if request.GET.get("secure_entities") == "true":
            secure_devices_filter['id__in'] = organisation.secure_devices
        elif request.GET.get("secure_entities") == "false":
            secure_devices_filter['id__in'] = organisation.insecure_devices

        cap_mobile_app_file = AppFile.get_mobile_app_file()
        context = {
            'organisation': organisation,
            'policies_count': organisation.policies.filter(active=True).count(),
            'policies_support': organisation.policies_support,
            'total_devices': 0,
            'devices': None,
            'total_device_passing': 0,
            'total_device_failing': 0,
            'agent_link': None,
            'v6_free_quantity': self.get_v6_plan_free_quantity(organisation),
            'cap_mobile_app_file': cap_mobile_app_file,
            **self.get_context_data()
        }

        if main_app_user:
            agent_link = AppFile.get_agent_link_for_organisation(organisation)

            all_devices = AppInstall.objects.filter(id__in=organisation.app_installs).filter(
                app_user__active=True,
                **inactive_filter,
                **secure_devices_filter
            ).select_related(
                'os'
            ).select_related(
                'analytics'
            ).prefetch_related(
                Prefetch('os_users', AppInstallOSUser.objects.all().order_by("created"))
            ).select_related(
                'app_user'
            ).prefetch_related(
                'reports'
            ).prefetch_related(
                'user_login_records'
            ).annotate(
                os_title=Case(
                    When(Q(caption__in=['', 'Unknown']) | Q(caption=None), then=F('os__title')),
                    default=F('caption')
                )
            ).annotate(
                serial_number_and_device_id=Concat(
                    Coalesce(NullIf('serial_number', Value('')), Value(_('Unknown serial number'))),
                    Value(' - '),
                    'device_id'
                )
            ).annotate(
                last_login_username_precomputed=Subquery(
                    UserLoginHistory.objects.filter(
                        app_install__pk=OuterRef('pk'),
                    ).order_by('-created').annotate(
                        login_username=Coalesce('user__username', 'user__domain', Value(_('Unnamed')))
                    ).values('login_username')[:1]
                )
            ).annotate(
                most_recent_check_in_precomputed=Coalesce(
                    Subquery(
                        AppReport.objects.filter(
                            app_install=OuterRef('pk'),
                        ).order_by('-created').values('created')[:1],
                        output_field=DateTimeField()
                    ),
                    F('created'),
                    output_field=DateTimeField()
                )
            ).annotate(
                status_precomputed=Case(
                    When(analytics__latest_pass_percentage__isnull=False, then=F('analytics__latest_pass_percentage')),
                    default=Value(0.0),
                    output_field=FloatField()
                )
            )
            all_devices = self.search_queryset(all_devices)
            all_devices = self.ordering_queryset(all_devices)
            all_devices = self.filtering_queryset(all_devices)
            all_devices_paginated = SafePaginator(all_devices, self.paginate_by)

            extra_app_users_id = organisation.get_extra_app_users_id(organisation.get_app_users())

            if hasattr(organisation, 'analytics'):
                all_devices_count = organisation.analytics.installed_devices_count or 0
                devices_passing = organisation.analytics.secure_devices_count or 0
                devices_failing = organisation.analytics.insecure_devices_count or 0
            else:
                all_devices_count = organisation.installed_devices_count
                devices_passing = organisation.secure_devices_count
                devices_failing = organisation.insecure_devices_count

            v5_agent_link = None
            if (waffle.switch_is_active(CAP_V_FIVE_BETA_SWITCH) and
                organisation.is_enrolled_in_beta_feature(CAP_V_FIVE_BETA_SWITCH)) or is_cap_v_five_ga_enabled:
                v5_agent_link = AppFile.get_agent_link_for_organisation(organisation, get_v5=True)
                agent_link['version'] = AppFile.get_v5_desktop_version()

            extra_context = {
                'total_devices': all_devices_count,
                'devices': all_devices_paginated.get_page(int(request.GET.get("page", 1))),
                'total_device_passing': devices_passing,
                'total_device_failing': devices_failing,
                'agent_link': agent_link,
                'extra_app_users_id': extra_app_users_id,
                "show_hidden_apps": show_hidden_apps,
                "v5_agent_link": v5_agent_link
            }
            context = {**context, **extra_context}
        return "dashboard/dashboard-bulk.html", context

    def uba_type_template(self, request, organisation):
        """
        Returns template name and the context for organisations that have chosen bulk enrollment
        but also have user based attribution enabled.
        :param request: http request
        :type request: HttpRequest
        :param organisation: for which organisation render template
        :type organisation: organisations.Organisation
        :return: template name and context
        :rtype: tuple
        """
        main_app_user = organisation.main_app_user

        cap_mobile_app_file = AppFile.get_mobile_app_file()
        context = {
            "organisation": organisation,
            "policies_count": organisation.policies.filter(active=True).count(),
            "policies_support": organisation.policies_support,
            "total_devices": 0,
            "total_device_passing": 0,
            "total_device_failing": 0,
            "agent_link": None,
            'v6_free_quantity': self.get_v6_plan_free_quantity(organisation),
            'cap_mobile_app_file': cap_mobile_app_file,
            **self.get_context_data(),
        }

        if main_app_user:
            agent_link = AppFile.get_agent_link_for_organisation(organisation)

            if hasattr(organisation, "analytics"):
                all_devices_count = organisation.analytics.installed_devices_count or 0
                devices_passing = organisation.analytics.secure_devices_count or 0
                devices_failing = organisation.analytics.insecure_devices_count or 0
            else:
                all_devices_count = organisation.installed_devices_count
                devices_passing = organisation.secure_devices_count
                devices_failing = organisation.insecure_devices_count

            enrolled_users_count = organisation.enrolled_users_count
            installed_users_count = organisation.installed_users_count
            awaiting_install_count = enrolled_users_count - installed_users_count
            app_users = self.get_app_users(request, organisation, search_type=self.SEARCH_TYPE_UBA)
            extra_app_users_id = organisation.get_extra_app_users_id(app_users)
            app_users = SafePaginator(app_users, self.paginate_by)

            v5_agent_link = None
            if (waffle.switch_is_active(CAP_V_FIVE_BETA_SWITCH) and
                organisation.is_enrolled_in_beta_feature(CAP_V_FIVE_BETA_SWITCH)) or is_cap_v_five_ga_enabled:
                v5_agent_link = AppFile.get_agent_link_for_organisation(organisation, get_v5=True)
                agent_link['version'] = AppFile.get_v5_desktop_version()

            context = {
                "total_devices": all_devices_count,
                "total_device_passing": devices_passing,
                "total_device_failing": devices_failing,
                "agent_link": agent_link,

                "awaiting_install_count": awaiting_install_count,
                "organisation": organisation,
                "main_app_user": main_app_user,
                "policies_count": organisation.policies.filter(active=True).count(),
                "policies_support": organisation.policies_support,
                "certification": organisation.certifications.order_by("-created").first(),

                "show_hidden_apps": request.GET.get("show_hidden_apps", False) == "true",
                'extra_app_users_id': extra_app_users_id,
                'v5_agent_link': v5_agent_link,
                'app_users': app_users.get_page(int(request.GET.get("page", 1))),
                **self.get_context_data(),
            }
        return "dashboard/dashboard-uba.html", context


class OrganisationDashboard(PermissionRoleRequiredMixin, SignupMixin, OrganisationDashboardTemplate, View):
    permission_required = DEVICES_PERMISSION

    @staticmethod
    def show_success_modal(organisation):
        if organisation.showmodal is False and not organisation.is_trial:
            organisation.showmodal = True
            organisation.save()
            return True
        else:
            return False

    def get_common_context(self, organisation: Organisation) -> dict:
        """
        Returns common context for all organisation types.
        """
        enrolled_beta_features = organisation.get_enrolled_beta_features(
            feature_type=[CAP_V_FIVE_BETA_SWITCH, TRUSTD_BETA_SWITCH]
        )
        return {
            "fake_app_form": FakeAppInstallForm(),
            "showmodal": self.show_success_modal(organisation),
            "enrolled_beta_features": enrolled_beta_features,
            "param_filtering": self.request.GET.get(self.filtering_query_param)
        }

    def get(self, request, org_id, *args, **kwargs):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()

        # prevents customers accessing the dashboard (default home page) until onboarding is complete
        if request.session.get('LAST_ONBOARDED_PAGE') and not organisation.has_billing:
            return redirect(request.session.get('LAST_ONBOARDED_PAGE'))

        if organisation.is_uba_type:
            template, context = self.uba_type_template(request, organisation)
        elif organisation.is_bulk_enrollment_type:
            template, context = self.bulk_enrollment_template(request, organisation)
        else:
            template, context = self.self_enrollment_template(request, organisation)

        context.update(self.get_common_context(organisation))

        return render(request, template, context)


def device_detail_context(app_install: AppInstall, request: HttpRequest) -> dict:
    """
    Returns context for device detailed page
    :param app_install: app install
    :return: context
    """
    organisation = app_install.app_user.organisation
    latest_report = app_install.get_latest_report
    installed_software = InstalledSoftwareAppInstallIndividual.objects.filter(app_install_id=app_install.id)
    installed_software_count, safe_software_count, vulnerable_software_count = aggregate_installed_software_counts(
        installed_software
    )

    context = {
        'app_install': app_install,
        'user_login_records': app_install.user_login_records.all()[:6],
        'organisation': organisation,
        'app_users_to_send_email_to': serialize_app_users(AppUser.objects.filter(id=app_install.app_user.id).prefetch_related('groups')),
        'is_beta_device': app_install.is_beta,
        'allow_to_manually_resolve': not (app_install.is_beta or hasattr(app_install, 'trustd_device')),
        'app_install_duplicate': app_install.get_duplicate(),
    }

    # only shows in dev envs
    if not settings.IS_PROD:
        try:
            last_api_report_json = json.dumps(generate_app_last_api_report(app_install))
        except Exception:
            last_api_report_json = {}
        context['last_api_report_json'] = last_api_report_json
    else:
        context['last_api_report_json'] = {}

    if organisation.is_self_enrollment_type or app_install.is_assigned_to_user:
        """
        If an organisation is self enrollment, or bulk enrollment with user based attribution enabled and
        device is connected to its own app user instead of org admin
        """
        context['agreed_policies'] = app_install.app_user.agreed_policies()
        context['read_policies'] = app_install.app_user.read_policies(exclude_agreed=True)
        context['pending_policies'] = app_install.app_user.pending_policies()
    else:
        """
        If an organisation is bulk install
        """
        context['agreed_policies'] = app_install.agreed_policies()
        context['read_policies'] = app_install.read_policies(exclude_agreed=True)
        context['pending_policies'] = app_install.pending_policies()

    context['policies_support'] = organisation.policies_support

    if organisation.bulk_install is True:
        context['agent_link'] = True
    else:
        context['agent_link'] = False
    if not latest_report:
        return context # early return if no report

    context['latest_report'] = latest_report
    responses = latest_report.check_results.filter(app_check__active=True).distinct('app_check__id')
    passed_responses, failed_responses = get_applications_responses(
        org_pk=organisation.pk,
        app_install_pk=app_install.pk,
        order=('app_check__pk', 'report__app_install', '-report__modified', '-modified'),
        distinct=('app_check__pk', 'report__app_install'),
        return_passed=True,
        return_failed=True
    )
    passed_count = passed_responses.count()
    failed_count = failed_responses.count()
    responses_count = passed_count + failed_count

    has_steps_to_secure = failed_count
    has_successful_check = passed_count

    ssptr = ((float(100) / responses_count) * has_successful_check) if responses_count else 1
    faiptr = ((float(100) / responses_count) * has_steps_to_secure) if responses_count else 1
    context['responses'] = responses
    context['has_steps_to_secure'] = has_steps_to_secure
    context['has_successful_check'] = has_successful_check
    context['responses_count'] = responses_count
    context['ssptr'] = ssptr
    context['faiptr'] = faiptr

    # Installed Software
    apps = installed_software.order_by('product', 'version', 'vendor').distinct('product', 'version', 'vendor')


    if waffle.flag_is_active(request, 'opswat_patch'):
        apps = enrich_installed_software_with_installers(apps)
    context['apps'] = list(apps)

    context.update({
        "installed_software_count": installed_software_count,
        "safe_software_count": safe_software_count,
        "vulnerable_software_count": vulnerable_software_count,
        "app_install_supports_opswat": app_install.supports_opswat(),
        "app_install_supports_opswat_patch": app_install.supports_opswat_patch()
    })

    return context


class DeviceDetail(SignupMixin, View):
    template_name = 'dashboard/device-detail.html'

    def get(self, request, org_id, user_uuid, device_id, serial_number=None):
        organisation = get_organisations(request.user, org_id)
        app_install = get_app_install(org_id, user_uuid, device_id, convert_serial_number(serial_number), only_active=True)
        if not organisation or not app_install:
            raise PermissionDenied()

        return render(request, self.template_name, device_detail_context(app_install, request))

    @override_settings(STATIC_URL='file://' + settings.STATIC_ROOT + '/')
    def post(self, request, org_id, user_uuid, device_id, serial_number=None):
        try:
            organisation = get_organisations(request.user, org_id)
            app_install = get_app_install(
                org_id, user_uuid, device_id, convert_serial_number(serial_number), only_active=True
            )
            if not organisation or not app_install:
                raise PermissionDenied()
            # send mail
            message = "Hello {}, <br><br> Please perform the following to secure your machine.\
                <br> Files attached.".format(app_install.app_user.get_full_name)
            email = EmailMessage(
                "Steps to secure",
                message,
                settings.DEFAULT_FROM_EMAIL, [app_install.app_user.email]
            )
            email.content_subtype = "html"
            email.ip_pool_name = 'Transactional'
            email.categories = [Site.objects.get_current().domain, ]

            email.attach(
                '{0}_{1}.pdf'.format(app_install.display_os(), timezone.now()),
                get_steps_to_secure_pdf(app_install, request),
                'application/pdf'
            )
            res = email.send()

        except Exception:
            response = {'status': 0}
        else:
            response = {'status': 1 if res else 0}

        return JsonResponse(response)


@login_required
def device_detail_resolve(request, org_id, user_uuid, device_id, serial_number=None):
    check_id = request.POST.get('check_id')
    reason = request.POST.get('reason', '')
    automatic_fix = True if request.POST.get('automatic_fix') == 'true' else False or False
    manual_resolved = True if request.POST.get('manual_resolved') == 'true' else False or False

    response = {'status': 0}
    try:
        organisation = get_organisations(request.user, org_id)
        app_install = get_app_install(org_id, user_uuid, device_id, convert_serial_number(serial_number), only_active=True)
        if not organisation or not app_install or app_install.is_beta:
            raise PermissionDenied()
        check = AppCheck.objects.get(pk=check_id, active=True)
        try:
            if automatic_fix:
                CheckAutoFix.objects.create(
                    app_install=app_install,
                    app_check=check,
                    user=request.user
                )
            elif manual_resolved:
                CheckManualFix.objects.create(
                    app_install=app_install,
                    app_check=check,
                    user=request.user,
                    reason=reason
                )
            if not settings.IS_TEST:
                calculate_app_report.delay(app_install.get_latest_report.pk)
        except AppInstall.DoesNotExist:
            raise
        response = {'status': 1}
    except AppCheck.DoesNotExist:
        pass
    return JsonResponse(response)


@login_required
def device_bulk_resolve(request, org_id):
    """
    Resolves fail responses for passed checks or os title.
    :param request: http request
    :type request: django.http.HttpRequest
    :param org_id: organisation id
    :type org_id: int
    :return: http response
    :rtype: json
    """
    organisation = get_organisations(request.user, org_id)
    if not organisation:
        raise PermissionDenied()
    check_id = request.POST.get('question_id', '')
    os_id = request.POST.get('os_id')
    reason = request.POST.get('reason', '')
    if not any([check_id and check_id.isdigit(), os_id and os_id.isdigit()]):
        raise Http404()
    _, failed_responses = get_applications_responses(
        org_pk=organisation.pk,
        order=('app_check__pk', 'report__app_install', '-report__modified', '-modified'),
        distinct=('app_check__pk', 'report__app_install')
    )

    try:
        check = AppCheck.objects.get(pk=check_id, active=True)
        for f in failed_responses:

            # Do not allow manual resolution for Beta Application Checks
            if f.report.app_install.is_beta:
                continue

            if os_id:
                if f.app_check.id == int(check_id) and f.report.app_install.os.id == int(os_id):
                    CheckManualFix.objects.create(
                        app_install=f.report.app_install,
                        user=request.user,
                        app_check=check,
                        bulk_fix=True,
                        reason=reason
                    )
                    if not settings.IS_TEST:
                        calculate_app_report.delay(f.report.app_install.get_latest_report.pk)
            else:
                if f.app_check.id == int(check_id):
                    CheckManualFix.objects.create(
                        app_install=f.report.app_install,
                        user=request.user,
                        app_check=check,
                        bulk_fix=True,
                        reason=reason
                    )
                    if not settings.IS_TEST:
                        calculate_app_report.delay(f.report.app_install.get_latest_report.pk)
    except (AppCheck.DoesNotExist, AppInstall.DoesNotExist):
        response = {'status': 0}
    else:
        response = {'status': 1}
    return JsonResponse(response)


@login_required
def device_detail_revert(request, org_id, user_uuid, device_id, serial_number=None):
    check_id = request.POST.get('check_id')
    manual_resolved = True if request.POST.get('manual_resolved') == 'true' else False or False
    automatically_resolved = True if request.POST.get('automatically_resolved') == 'true' else False or False
    response = {'status': 0}
    try:
        organisation = get_organisations(request.user, org_id)
        app_install = get_app_install(org_id, user_uuid, device_id, convert_serial_number(serial_number), only_active=True)
        if not organisation or not app_install or app_install.is_beta:
            raise PermissionDenied()
        check = AppCheck.objects.get(pk=check_id, active=True)
        if manual_resolved:
            try:
                resolve_get = CheckManualFix.objects.filter(
                    app_install__app_user__organisation=organisation,
                    app_install=app_install,
                    app_check=check,
                    app_check__active=True
                )
            except CheckManualFix.DoesNotExist:
                pass
            else:
                for fix in resolve_get:
                    if not settings.IS_TEST:
                        calculate_app_report.delay(fix.app_install.get_latest_report.pk)
                resolve_get.delete()
            response = {'status': 1}
        elif automatically_resolved:
            try:
                resolve_get = CheckAutoFix.objects.filter(
                    app_install__app_user__organisation=organisation,
                    app_install=app_install,
                    app_check=check,
                    app_check__active=True
                )
            except CheckAutoFix.DoesNotExist:
                pass
            else:
                for fix in resolve_get:
                    if not settings.IS_TEST:
                        calculate_app_report.delay(fix.app_install.get_latest_report.pk)
                resolve_get.delete()
            response = {'status': 1}
    except AppCheck.DoesNotExist:
        pass
    return JsonResponse(response)


class DeviceAssignUserView(SignupMixin, View):
    http_method_names = ["post"]

    def post(self, request, org_id, user_uuid, device_id, serial_number=None):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            return JsonResponse(status=403, data={"error": "You don't have permission to access this organisation"})
        if not organisation.is_uba_enabled:
            return JsonResponse(status=403, data={"error": "UBA is not enabled for this organisation"})

        # by default, assign to main app user
        new_app_user = AppUser.objects.filter(
            uuid=request.POST.get("new_app_user_uuid")
        ).first() or organisation.main_app_user

        app_install = get_app_install(
            org_id, user_uuid, device_id, convert_serial_number(serial_number), only_active=True
        )
        if not app_install:
            return JsonResponse(status=403, data={"error": "Device not found"})

        if not app_install.app_user == new_app_user:
            old_app_user = app_install.app_user
            app_install.app_user = new_app_user
            # if it is a trustd device, build new hostname and update email
            if hasattr(app_install, 'trustd_device'):
                from trustd.tasks import update_trustd_device
                data = {'email': new_app_user.email}
                # update hostname only if not bulk device with device name already set
                if not app_install.trustd_device.device_name:
                    app_install.hostname = build_hostname(new_app_user, app_install.machine_model)
                    data['name'] = app_install.hostname
                update_trustd_device.delay(app_install.trustd_device.id, data)
            app_install.save(update_fields=["app_user", "hostname"])

            # trigger analytic update for old app user and new one
            calculate_app_user_analytics.delay(app_users_pk=[old_app_user.pk, new_app_user.pk])

        return JsonResponse(status=200, data={
            "success": True, "uuid": new_app_user.uuid, "url": app_install.url_device_assign_user()
        })


class AddUsers(SignupMixin, FormView):
    template_name = 'dashboard/add-users.html'
    organisation = None
    app_users = None
    nr_new_users_allowed = 0

    def get_initial(self):
        return {}

    def get_success_url(self):
        org_id = self.kwargs['org_id']
        organisation = get_object_or_404(Organisation, secure_id=org_id)
        return organisation.appusers_url

    def form_valid(self, forms):
        organisation = self.organisation

        existing_emails = []
        app_users_uuids = []
        for form in forms:
            app_user_uuid, is_new = form.update_data(self, organisation)
            if app_user_uuid and is_new:
                app_users_uuids.append(app_user_uuid)
            elif not is_new and form.cleaned_data['email']:
                existing_emails.append(form.cleaned_data['email'])

        # send download links if any new app users are added
        if organisation.has_software_support and not organisation.is_trial and app_users_uuids:
            send_apps(organisation, uuid_list=app_users_uuids)

        if organisation.learn_lite_tab_displayed and app_users_uuids:
            add_user_handlers.apply_async(args=[organisation.id, app_users_uuids])

        # Show existing users
        if existing_emails:
            existing_emails_str = "<br/>".join(set(existing_emails))
            message = gettext_lazy("Users with the following emails exist:") + f"<br/> {existing_emails_str}"
            messages.add_message(self.request, messages.INFO, message)
        return super(AddUsers, self).form_valid(forms)

    def set_organisation(self):
        """ Helper method to set organisation or raise permission denied """
        user = self.request.user
        org_id = self.kwargs['org_id']
        organisation = get_organisations(user, org_id)
        if not organisation:
            raise PermissionDenied()
        self.organisation = organisation

    def get_form_kwargs(self) -> dict:
        """
        Override form kwargs to pass organisation and request to the form
        """
        kwargs = super().get_form_kwargs()
        form_kwargs = kwargs.get("form_kwargs", {})
        form_kwargs.update({"organisation": self.organisation, "request": self.request})
        kwargs.update({"form_kwargs": form_kwargs})
        return kwargs

    def get_form_class(self):
        """Override form class to limit number of users the customer can add and to validate it"""
        self.set_organisation()
        self.app_users = self.organisation.get_app_users()
        #  if the org does not have a limit (>500) then just set the maximum for the form
        nr_new_users_allowed = 100
        # set this to None in order to not show the limitation info text in the front-end
        self.nr_new_users_allowed = None
        # if the org does not have a limit then we do not validate
        validate_max = False
        if self.organisation.has_max_cap_users:
            # number of enrolled app users that the org admin is allowed to add
            self.nr_new_users_allowed = self.organisation.get_nr_new_users_allowed()
            nr_new_users_allowed = self.nr_new_users_allowed
            validate_max = True
        return formset_factory(AppUserEnrollForm, can_delete=False, extra=1,
                               max_num=nr_new_users_allowed, validate_max=validate_max)

    def get_context_data(self, **kwargs):
        context = super(AddUsers, self).get_context_data(**kwargs)
        org_id = self.kwargs['org_id']
        organisation = self.organisation
        app_users = self.app_users

        enrolled_users_count = organisation.enrolled_users_count
        installed_users_count = organisation.installed_users_count
        secure_users_count = organisation.secure_users_count
        insecure_users_count = organisation.insecure_users_count

        if not hasattr(organisation, 'organisationusersync'):
            OrganisationUserSync.objects.create(organisation=organisation)

        if organisation.organisationusersync.sync_enabled:
            organisation.organisationusersync.sync_now()
            context['provider'] = organisation.organisationusersync.provider
            context['list_users'] = organisation.organisationusersync.users_data

        context['manage_users_page_name'] = gettext_lazy('Manage Users')
        context['app_users'] = app_users
        context['organisation'] = organisation
        context['nr_new_users_allowed'] = self.nr_new_users_allowed
        context['extra_app_users_id'] = organisation.get_extra_app_users_id(app_users)
        context['org_id'] = org_id
        context['is_bulk'] = organisation.bulk_install
        context['total_app_users'] = enrolled_users_count
        context['installed_app_users'] = installed_users_count
        context['user_passing'] = secure_users_count
        context['user_failing'] = insecure_users_count
        context['has_usable_password'] = self.request.user.has_usable_password()
        context['open_sync_users_modal'] = self.request.GET.get('open_sync_users_modal')
        return context


class PrivacyToolbox(PermissionRoleRequiredMixin, SignupMixin, View):
    template_name = 'dashboard/privacy-toolbox.html'
    permission_required = CERTIFICATES_AND_INSURANCE_PERMISSION

    def get(self, request, org_id):
        user = request.user
        organisation = get_organisations(user, org_id)

        if not organisation:
            raise PermissionDenied()
        context = {
            'organisation': organisation,
            'privacy_toolbox_url': f'{settings.PRIVACY_TOOLBOX_URL}?organisation={organisation.secure_id}&user={user.id}',
        }

        toolbox = StaticFiles.objects.last()

        if toolbox:
            context.update({
                'toolbox': toolbox,
            })
        return render(request, self.template_name, context)


class RansomwareToolbox(PermissionRoleRequiredMixin, SignupMixin, View):
    template_name = 'dashboard/r_n_r_toolbox/main.html'
    permission_required = CERTIFICATES_AND_INSURANCE_PERMISSION

    def get(self, request, org_id):
        user = request.user
        organisation = get_organisations(user, org_id)

        if not organisation:
            raise PermissionDenied()
        # get ransomware and recovery toolbox plan prices
        r_and_r_monthly_plan = get_plan_object_from_cache(plans.DIRECT_R_AND_R_TOOLBOX_MONTHLY_PLAN)
        r_and_r_monthly_price = int(
            get_plan_price(r_and_r_monthly_plan, organisation)) if r_and_r_monthly_plan else None
        r_and_r_annual_plan = get_plan_object_from_cache(plans.DIRECT_R_AND_R_TOOLBOX_ANNUAL_PLAN)
        r_and_r_annual_price = int(get_plan_price(r_and_r_annual_plan, organisation)) if r_and_r_annual_plan else None
        ce_cert = organisation.ce_certification

        skip_insurance_answers = True
        if not waffle.switch_is_active('disable_rnr_insurance_buying_together'):
            # if org is already certified/declaration signed but chose not to opt in, then we need to get insurance answers
            if ce_cert and (ce_cert.certified or ce_cert.declaration_signed):
                skip_insurance_answers = ce_cert.is_superscript_insurance and ce_cert.insurance_opt_in_survey_value

        toolbox = StaticFiles.objects.last()

        questions = get_insurance_upsell_questions(ce_cert)
        context = {
            'organisation': organisation,
            'r_and_r_toolbox_price_monthly': f'£{r_and_r_monthly_price}',
            'r_and_r_toolbox_price_annual': f'£{r_and_r_annual_price}',
            'skip_insurance_answers': skip_insurance_answers,
            'toolbox': toolbox,
            'questions': questions,
            'certification': ce_cert,
        }
        return render(request, self.template_name, context)

class Certification(PermissionRoleRequiredMixin, SignupMixin, View):
    template_name = 'dashboard/certification.html'
    permission_required = CERTIFICATES_AND_INSURANCE_PERMISSION

    def get(self, request, org_id, type, version_pk=None):
        user = request.user
        organisation = get_organisations(user, org_id)
        if not organisation:
            raise PermissionDenied()
        context = {}
        certification = organisation.get_latest_certificate(type=type, version_pk=version_pk)
        if not certification:
            return HttpResponseRedirect(reverse('dashboard:buy-certificate', kwargs={
                'org_id': organisation.secure_id,
                'certification_type': int(type)
            }))
        context.update({
            'is_gdpr': certification.is_gdpr,
        })
        # if organisation certificate subscription is paused and partner not cybersmart
        # redirect to view certificate paused. (not started / in survey)
        try:
            subscription = organisation.subscriptions.find_certificate_by_type(
                certificate_type=int(type))
        except ObjectDoesNotExist:
            pass
        else:
            if subscription:
                if not certification.certified and not certification.expired and subscription.paused_past_term_end:
                    return HttpResponseRedirect(reverse('dashboard:certificate-paused', kwargs={
                        'org_id': organisation.secure_id,
                        'certification_type': int(type)
                    }))
                elif subscription.paused:
                    context.update({'renewal_paused': True})

        try:
            declaration = SurveyDeclaration.objects.get(survey=certification.survey)
        except Exception:
            pass
        else:
            if declaration.ready_to_be_signed:
                if not declaration.signature and certification.version.declaration:
                    return HttpResponseRedirect(certification.declaration_url)

        # show modal for certification only users
        showmodal = False
        if organisation.showmodal is False:
            organisation.showmodal = True
            organisation.save()
            showmodal = True

        if not hasattr(certification, 'survey'):
            # if survey not started yet
            context.update({
                'certification': certification,
                'auto_answer_url': certification.auto_answer_url,
                'organisation': organisation,
                'showmodal': showmodal
            })
        else:
            # remove failed responses errors from session
            if 'failed_questions' in request.session:
                failed_questions = request.session['failed_questions']
                del request.session['failed_questions']
            else:
                failed_questions = []
            if 'failed_message' in request.session:
                failed_message = SafeString(request.session['failed_message'])
                del request.session['failed_message']
            else:
                failed_message = ''
            if 'failed_only_required' in request.session:
                failed_only_required = request.session['failed_only_required']
                del request.session['failed_only_required']
            else:
                failed_only_required = None
            if 'failed_questions_nums' in request.session:
                failed_questions_nums = request.session['failed_questions_nums']
            else:
                failed_questions_nums = []

            # if survey already started
            is_sent_back = hasattr(certification.survey, 'assessment') and certification.survey.assessment.is_sent_back
            survey_topic_stat = SurveyTopicStat(certification, main_key_as_title=True).get()
            insurance_topic_stat = survey_topic_stat.get('Insurance', {})
            context.update({
                'organisation': organisation,
                'showmodal': showmodal,
                'certification': certification,
                'auto_answer_url': certification.auto_answer_url,
                'failed_questions': failed_questions,
                'failed_questions_nums': failed_questions_nums,
                'failed_message': failed_message,
                'failed_only_required': failed_only_required,
                'topics': get_questionnaire_queryset(
                    certification,
                    is_sent_back
                ),
                'stripe_key': settings.STRIPE_KEY,
                'org_has_100k_insurance': organisation.has_ce_100k_r_and_r_toolbox_insurance,
                "unsupported_os_releases": json.dumps(get_organisation_unsupported_os_releases(organisation)),
                "answered_insurance_questions": insurance_topic_stat.get('answered', 0) > 0,
                'hide_submit_button': is_eu_geo(),
            })
            # add review topics for completed certifications or those that are sent back (with all questions)
            if certification.has_issued_certifications or certification.type == CYBERSMART_COMPLETE or is_sent_back:
                context.update({'review_topics': get_questionnaire_queryset(
                    certification,
                    include_not_applicable=True
                )})
            structure_for_topics = get_questionnaire_status_structure(certification, key_as_pk=True)
            context['topics_status_structure'] = get_topics_status_structure(
                certification, structure_for_topics, include_required=True
            )
            # for trial orgs pass chosen plans id
            if organisation.is_trial and organisation.is_test:
                context["plans_ids"] = organisation.plans_ids
            else:
                context["plans_ids"] = []
            # pre populate coupon code for payment
            pre_populated_coupon = request.session.get("pre_populated_coupon")
            if pre_populated_coupon:
                context["pre_populated_coupon"] = pre_populated_coupon
        return render(request, self.template_name, context)

    @staticmethod
    def update_answers_for_these_questions(request):
        """
        Returns the list of questions which we need to be updated with new answers.
        This list will only have those questions that were presented on the frontend.
        :param request: http request
        :type request: HttpResponse
        :return: list of questions primary keys
        :rtype: list
        """
        questions_list = []
        for response in request.POST:
            if response.startswith("response_"):
                _, question_pk, *_ = response.split("_")
                questions_list.append(question_pk)
            if response.startswith("failed_response_"):
                _, _, question_pk, *_ = response.split("_")
                questions_list.append(question_pk)
        return list(set(questions_list))

    def post(self, request, org_id, type, version_pk=None):
        # Tracking Time Users
        tracktimeuser = TrackingTimeUsers.objects.filter(
            user=request.user, pagename='In the questionnaire process').first()
        if tracktimeuser:
            time_tracking = tracktimeuser.time
        else:
            time_tracking = 0

        time = self.request.POST.get('time', '')
        try:
            time_tracking += int(time)
        except ValueError:
            pass

        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()
        certification = organisation.get_latest_certificate(type=type, version_pk=version_pk)
        if not certification:
            raise Http404()
        try:
            declaration = SurveyDeclaration.objects.get(survey=certification.survey)
        except Exception:
            pass
        else:
            if declaration.ready_to_be_signed:
                if not declaration.signature and certification.version.declaration:
                    return HttpResponseRedirect(certification.declaration_url)

        # if answers were re-answered redirect to organisation page
        if hasattr(certification.survey, 'assessment') and certification.survey.assessment.is_re_answered:
            return HttpResponseRedirect(organisation.url)

        # save & next button, finish later button, next topic
        current_topic = request.POST.get('current-topic')
        if current_topic and current_topic.isdigit():
            if 'finish-later' in request.POST or 'download_survey_as_csv' in request.POST:
                # if a user wants to finish later leave current topic as active
                next_topic = int(current_topic)
            else:
                # otherwise set next topic as active
                try:
                    next_topic = get_questionnaire_queryset(
                        certification,
                        hasattr(certification.survey, 'assessment') and certification.survey.assessment.is_sent_back
                    ).filter(order__gt=current_topic)[0].order
                except Exception:
                    next_topic = int(current_topic) + 1
            if not (organisation.is_direct_customer and organisation.is_trial):
                # do not save correct topic for trial direct customers
                request.session['{0}_current_topic'.format(certification.survey.pk)] = next_topic

        # handle questions and responses
        questions_to_update = self.update_answers_for_these_questions(request)
        if hasattr(certification.survey, 'assessment') and certification.survey.assessment.is_sent_back:

            notes = list(set(list(
                AssessorNote.objects.filter(
                    assessment__survey=certification.survey, re_answered=False, answer_accepted=False
                ).values_list('question__pk', flat=True)
            )))

            questions_with_notes = list(SurveyQuestion.objects.filter_insurer_options(certification).filter(
                version=certification.version
            ).filter(pk__in=notes).values_list('pk', flat=True))
            # and those questions children
            children = list(SurveyQuestion.objects.filter(parent__pk__in=questions_with_notes).values_list(
                'pk', flat=True
            ))
            questions_filter = {"pk__in": list(set(questions_with_notes + children + questions_to_update))}
        else:
            questions_filter = {"pk__in": questions_to_update}

        questions = SurveyQuestion.objects.filter_insurer_options(certification).filter(
                version=certification.version
        ).order_by(
            'order'
        ).select_related(
            'parent', 'parent'
        ).prefetch_related(
            Prefetch('responses', SurveyResponse.objects.filter(survey=certification.survey, not_applicable=False))
        ).prefetch_related(
            Prefetch('assessor_notes', AssessorNote.objects.filter(assessment__survey=certification.survey))
        ).prefetch_related('mandatory_for_parent_choices'
        ).filter(**questions_filter).annotate(
            choices_count=Count('choices'),
        )
        # track if there are any errors on the questions
        has_error = False
        for question in questions:
            if question.parent:
                # validate questions that depend on parent question choice answer
                if question.mandatory_for_parent_choices.exists():
                    # check if parent question has choice answer that required to show this child question
                    if not SurveyResponse.objects.filter(
                            question=question.parent, survey__certificate=certification,
                            choice__in=question.mandatory_for_parent_choices.all(), not_applicable=False
                    ).count():
                        # if it doesn't, skip this question
                        # create not applicable response
                        response, _ = SurveyResponse.objects.get_or_create(
                            survey=certification.survey,
                            question=question
                        )
                        if not response.not_applicable:
                            response.not_applicable = True
                            response.save()
                        continue
                else:
                    # validate questions that depend on parent Yes/No radio button
                    # get parent question response
                    if handle_questions_with_parent_yes_no_radio_button(question, certification, is_parent_2=False):
                        continue
            elif question.parent_2:
                # validate questions that depend on parent_2 Yes/No radio button
                if handle_questions_with_parent_yes_no_radio_button(question, certification, is_parent_2=True):
                    continue

            # handle questions
            try:
                if question.widget == SurveyQuestion.WIDGET_CONSCIOUS:
                    handle_conscious_widget(question, request, certification)
                elif question.widget == SurveyQuestion.WIDGET_ORGANISATION_TYPE_SELECT:
                    handle_org_type_questions(question, request, certification)
                elif question.widget in (
                        SurveyQuestion.WIDGET_YES_NO_TEXT,
                        SurveyQuestion.WIDGET_COMMON_REASONS,
                        SurveyQuestion.WIDGET_YES_NO_OPTIONAL_TEXT,
                ):
                    handle_yes_no_text_widget(question, request, certification)
                elif question.widget in (
                        SurveyQuestion.WIDGET_ORGANISATION_IT,
                        SurveyQuestion.WIDGET_FULL_NAME,
                        SurveyQuestion.WIDGET_YES_NO_TEXT,
                        SurveyQuestion.WIDGET_COMMON_REASONS
                ) or question.choices_count:
                    # handle questions with choices
                    if question.widget in (
                            SurveyQuestion.WIDGET_RADIO,
                            SurveyQuestion.WIDGET_RADIO_SIMPLE,
                            SurveyQuestion.WIDGET_RADIO_TEXTAREA
                    ):
                        # handle questions with choices that can have only one response
                        handle_radio_questions(question, request, certification)
                    elif question.widget in (
                            SurveyQuestion.WIDGET_CHECKBOX, SurveyQuestion.WIDGET_RADIO_CHECKBOX,
                            SurveyQuestion.WIDGET_ORGANISATION_ADDRESS, SurveyQuestion.WIDGET_ORGANISATION_IT,
                            SurveyQuestion.WIDGET_FULL_NAME
                    ):
                        # handle questions with choices that can have multiple responses
                        handle_checkbox_questions(question, request, certification)
                    else:
                        handle_single_value_questions(question, request, certification)
                else:
                    # handle simple questions with single response
                    handle_single_value_questions(question, request, certification)
            except forms.ValidationError as error:
                # support legacy - this can probably be removed
                if hasattr(error, 'message_dict'):
                    # show errors
                    for key, value in error.message_dict.items():
                        try:
                            parts = key.split('_')
                            if len(parts) >= 3:
                                _, _, question_order = parts[:3]
                                response_field = '_'.join(parts[3:]) if len(parts) > 3 else ''
                            else:
                                raise ValueError(f"Unexpected key format: {key}")

                            error_message = f'Question number {question.pervade_num}.{response_field} {value[0]}'
                            messages.add_message(request, messages.ERROR, error_message)
                        except ValueError as e:
                            logger.error(f"Error processing key '{key}': {e}")
                else:
                    for error_message in error:
                        messages.add_message(request, messages.ERROR, error_message)
                # don't progress until error is resolved
                if current_topic and current_topic.isdigit():
                    request.session[f'{certification.survey.pk}_current_topic'] = int(current_topic)
                has_error = True
        # If there are form validation errors, redirect to the same page
        if has_error:
            return HttpResponseRedirect(request.path_info)

        # save passed percentage
        if not (hasattr(certification.survey, 'assessment') and certification.survey.assessment.is_sent_back):
            # dont recalculate passed percent if some questions were sent back from assessor
            certification.survey.calculate_passed_percent()

        if request.POST.get('submit-survey-signal'):
            # if this is final submit redirect to validation view
            # for the submit organisation analytics will be calculated in another view
            return HttpResponseRedirect(certification.submit_survey_url)
        else:
            # update organisation analytics
            calculate_organisation_analytics.delay(organisation.pk)

            if 'finish-later' in request.POST:
                if organisation.is_direct_customer and organisation.is_trial:
                    # leave direct customer on the certification page
                    return HttpResponseRedirect(certification.url)
                else:
                    # if user wants to finish later redirect it to organisation page
                    return HttpResponseRedirect(organisation.url)
            elif 'download_survey_as_csv' in request.POST:
                # Download the survey as CSV
                filename = "{} assessment.csv".format(certification.version.type)
                topics = get_questionnaire_queryset(certification)
                content = generate_csv_organisation_survey(topics, certification.survey.id, escaped=False)
                response = HttpResponse(content, content_type='text/csv')
                response['Content-Disposition'] = 'attachment; filename={0}'.format(filename)
                return response
            # otherwise redirect to survey page
            return HttpResponseRedirect(certification.url)


class StartSurvey(SignupMixin, View):
    """
    Starts a survey for passed organisation and certification type.
    """

    class WrongOrder(Exception):
        """
        This exception will be raised in case of users will try to start survey in a wrong order.
        For example:
            To get IASME Governance certification a user first need to get next certifications:
            1) Cyber Essentials
            2) Cyber Essentials +
            But they can try to star survey directly for IASME Governance without passing survey for Cyber Essentials
            and Cyber Essentials +
            So, in this case the exception will be raised and error message will be shown.
        """
        pass

    class InvalidCE(Exception):
        """
        This exception will be raised in case of users will try to start CE + without a valid CE.
        CE must be completed 3 months prior to starting CE +
        """
        pass

    @staticmethod
    def post(request, org_id, version_pk=None):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()
        # validation
        cert_standard = request.POST.get('type', '')
        if not cert_standard or not cert_standard.isdigit():
            raise Http404()
        certification = organisation.get_latest_certificate(type=cert_standard, version_pk=version_pk)
        if not certification:
            raise Http404()
        if not certification.not_started:
            raise Http404()
        # check correct standards order
        is_debug_sales_cep_flag_active = waffle.flag_is_active(request, 'show_cep_without_ce_completion_debug')
        try:
            if certification.type == CYBER_ESSENTIALS_PLUS:
                ce = organisation.get_latest_certificate(
                    CYBER_ESSENTIALS, version_number=certification.version.version_number
                )
                if not is_debug_sales_cep_flag_active:
                    if not ce or ce and not ce.certified:
                        raise StartSurvey.WrongOrder(
                            f'You need to get this certification first: {CYBER_ESSENTIALS_VERBOSE}'
                        )
                    elif ce.issued_certification.date.date() < timezone.now().date() - relativedelta(months=3):
                        raise StartSurvey.InvalidCE(f'{CYBER_ESSENTIALS_VERBOSE} certificate needs to be completed '
                                                    f'less than 3 months from start of {CYBER_ESSENTIALS_PLUS_VERBOSE}')
            elif certification.type == IASME_GOVERNANCE:
                ce = organisation.get_latest_certificate(
                    CYBER_ESSENTIALS, version_number=certification.version.version_number
                )
                # cep = organisation.get_latest_certificate(CYBER_ESSENTIALS_PLUS)
                if ce and not (ce.certified or ce.declaration_signed):
                    raise StartSurvey.WrongOrder('You need to get this certification first: {0}'.format(
                        CYBER_ESSENTIALS_VERBOSE
                    ))
                # elif cep and not cep.certified:
                #     raise StartSurvey.WrongOrder('You need to get this certification first: {0}'.format(
                #         CYBER_ESSENTIALS_PLUS_VERBOSE
                #     ))
            elif certification.type == GDPR:
                # ce = organisation.get_latest_certificate(CYBER_ESSENTIALS)
                # cep = organisation.get_latest_certificate(CYBER_ESSENTIALS_PLUS)
                iasme = organisation.get_latest_certificate(
                    IASME_GOVERNANCE, version_number=certification.version.version_number
                )
                #
                # if ce and not (ce.certified or ce.declaration_signed):
                #     raise StartSurvey.WrongOrder('You need to get this certification first: {0}'.format(
                #         CYBER_ESSENTIALS_VERBOSE
                #     ))
                # elif cep and not cep.certified:
                #     raise StartSurvey.WrongOrder('You need to get this certification first: {0}'.format(
                #         CYBER_ESSENTIALS_PLUS_VERBOSE
                #     ))
                if iasme and not (iasme.certified or iasme.declaration_signed):
                    raise StartSurvey.WrongOrder('You need to get this certification first: {0}'.format(
                        IASME_GOVERNANCE_VERBOSE
                    ))
        except (StartSurvey.WrongOrder, StartSurvey.InvalidCE) as error:
            messages.add_message(request, messages.ERROR, error)
        else:
            # create a survey object
            certification_survey, created = CertificationSurvey.objects.get_or_create(
                certificate=certification,
                defaults={
                    'datetime_started': datetime.now()
                }
            )
            partner = organisation.partner
            # Charge for GDPR and CE when start the survey if the partner on any billing model including v2, v3 v4
            if partner.is_billable and not certification.free_of_charge:
                # get the PO if it was added during org creation by the partner
                purchase_order = PurchaseOrder.objects.filter(organisation=organisation).first()
                # get coupon code if it was added during org creation by the partner
                coupon_code = organisation.coupon_code
                # if coupon is not valid anymore, just remove it
                if not is_coupon_valid(coupon_code, 'random_plan'):
                    coupon_code = None
                # add the subscription
                partner.billing.add_certificate_plan(
                    organisation=organisation,
                    certificate_type=certification.type,
                    purchase_order=purchase_order,
                    # get coupon code if it was added during org creation by the partner
                    coupon_code=coupon_code
                )
            if created:
                # create empty answers for all questions
                SurveyResponse.objects.bulk_create([
                    SurveyResponse(
                        survey=certification.survey,
                        question_id=question_pk,
                        not_applicable=True
                    ) for question_pk in SurveyQuestion.objects.filter_insurer_options(certification).filter(
                        version=certification.version,
                    ).values_list(
                        'pk', flat=True
                    )
                ])

                # create test answers for all questions (debug mode)
                if request.POST.get('debug_answers') and not settings.IS_PROD:
                    fill_survey_responses_for_debug_mode(certification)
                    certification_survey.passed_percent = 100
                    certification_survey.save()
                    calculate_organisation_analytics.delay(organisation.pk)

                # initialize some questions with responses
                create_initial_responses(request, organisation, certification)
            else:
                certification.status = OrganisationCertification.IN_SURVEY
                certification.save()

            # create log
            EventLog.objects.create(
                user=request.user,
                organisation=organisation,
                type=EventLog.STARTED_QUESTIONNAIRE,
                survey=certification.survey
            )
            # send notification
            certification.send_notification('organisation_certification_started')
            # remove current topic from session
            if '{0}_current_topic'.format(certification.survey.pk) in request.session:
                del request.session['{0}_current_topic'.format(certification.survey.pk)]

        # redirect to survey page
        return HttpResponseRedirect(certification.url)


class SubmitSurvey(SignupMixin, View):
    """
    Validates a survey and saves result.
    """

    class Failed(Exception):
        """
        This exception will be raised in case of failed submitting.
        """
        pass

    @staticmethod
    def get(request, org_id, type, version_pk=None):
        user = request.user
        organisation = get_organisations(user, org_id)
        if not organisation:
            raise PermissionDenied()
        cert_standard = type
        if not cert_standard or not cert_standard.isdigit():
            raise Http404()
        certification = organisation.get_latest_certificate(type=cert_standard, version_pk=version_pk)
        if not certification:
            raise Http404()
        if certification.not_started:
            raise Http404()

        # do not allow submit GDPR survey until Cyber Essentials certification isn't finished yet
        if certification.is_gdpr:
            ce = organisation.get_latest_certificate(
                type=CYBER_ESSENTIALS, version_number=certification.version.version_number
            )
            if ce and not (ce.certified or ce.declaration_signed):
                raise PermissionDenied()

        # failed questions
        failed_questions = [int(num) for num in get_questionnaire_status_structure(
            certification,
            answered=False,
            key_as_pk=True,
            skip_not_required=True
        ).keys()]
        failed_questions_queryset = SurveyQuestion.objects.filter(
            pk__in=failed_questions
        ).exclude(
            insurer=SUPERSCRIPT_INSURER,
            code=QUESTION_CODE_SUPERSCRIPT_INSURANCE_OPT_IN
        ).order_by('pervade_title')

        minor_responses = failed_questions_queryset.filter(ranking=SurveyQuestion.RANKING_MINOR).count()
        major_responses = failed_questions_queryset.filter(ranking=SurveyQuestion.RANKING_MAJOR).count()
        fail_responses = failed_questions_queryset.filter(ranking=SurveyQuestion.RANKING_FAIL).count()
        required_responses = failed_questions_queryset.filter(required=True).exclude(ranking__in=[
            SurveyQuestion.RANKING_MINOR, SurveyQuestion.RANKING_MAJOR, SurveyQuestion.RANKING_FAIL
        ]).count()

        try:
            if minor_responses > certification.version.max_minors:
                # if among the failed questions the number of questions with the ranking minor is greater than
                # the certificate allows then the user is failed
                raise SubmitSurvey.Failed()
            if major_responses > certification.version.max_majors:
                # if among the failed questions the number of questions with the ranking major is greater than
                # the certificate allows then the user is failed
                raise SubmitSurvey.Failed()
            if fail_responses > certification.version.max_fails:
                # if among the failed questions the number of questions with the ranking fail is greater than
                # the certificate allows then the user is failed
                raise SubmitSurvey.Failed()
            if required_responses:
                # if there are failed required responses then the user is failed
                raise SubmitSurvey.Failed('only required')
        except SubmitSurvey.Failed as error:
            # redirect to survey page
            request.session['failed_questions'] = failed_questions
            request.session['failed_questions_nums'] = [q.pervade_num for q in failed_questions_queryset]
            request.session['failed_only_required'] = str(error) == 'only required'
            request.session['failed_message'] = '''
            You are allowed up to <b>{0}</b> minor, <b>{1}</b> major and <b>{2}</b> automatic fail responses.<br>
            You currently have <b>{3}</b> minor, <b>{4}</b> major and <b>{5}</b> automatic fail responses.
            '''.format(
                certification.version.max_minors,
                certification.version.max_majors,
                certification.version.max_fails,
                minor_responses,
                major_responses,
                fail_responses
            )
            request.session['failed_message'] = mark_safe(request.session['failed_message'])  # html formatting
            exclude = {}
            if organisation.insurance_tab_should_be_hidden:
                exclude['title'] = 'Insurance'
            request.session['{0}_current_topic'.format(certification.survey.pk)] = SurveyQuestionTopic.objects.annotate(
                q=Count('questions')).filter(
                q__gt=0,
                version=certification.version
            ).exclude(**exclude).order_by('questions__pervade_title').last().order
            return HttpResponseRedirect(certification.url)
        else:
            html_report = get_questionnaire_html_report(request, organisation, type, version_pk)
            # remove current topic from session
            if '{0}_current_topic'.format(certification.survey.pk) in request.session:
                del request.session['{0}_current_topic'.format(certification.survey.pk)]
            # create a declaration object
            try:
                declaration = SurveyDeclaration.objects.get(survey=certification.survey)
            except SurveyDeclaration.DoesNotExist:
                declaration = SurveyDeclaration.objects.create(
                    survey=certification.survey,
                    declaration_date=datetime.now(),
                    html_report=html_report,
                    ready_to_be_signed=True
                )
            else:
                declaration.declaration_date = datetime.now()
                if not declaration.declaration_name:
                    # don't change declaration name if already set
                    declaration.declaration_name = user.get_full_name()
                declaration.html_report = html_report
                declaration.ready_to_be_signed = True
                declaration.save()
            # create log
            EventLog.objects.create(
                user=user,
                organisation=organisation,
                type=EventLog.SUBMITTED_QUESTIONNAIRE,
                survey=certification.survey
            )
            # Mark all assessor notes as re-answered
            if hasattr(certification.survey, 'assessment') and certification.survey.assessment.is_sent_back:
                AssessorNote.objects.filter(
                    assessment__survey__certificate=certification, re_answered=False, answer_accepted=False
                ).update(re_answered=True)
            # mark assessment as re-answered
            if hasattr(certification.survey, 'assessment') and certification.survey.assessment.is_sent_back:
                certification.survey.assessment.set_re_answered()
                # if answers were re-answered redirect to organisation page
                if not declaration.signature:
                    return HttpResponseRedirect(certification.declaration_url)
                else:
                    return HttpResponseRedirect(organisation.url)
            # calculate organisation analytics
            calculate_organisation_analytics.delay(organisation.pk)
            # redirect to declaration page
            if certification.version.declaration:
                return HttpResponseRedirect(certification.declaration_url)
            else:
                return HttpResponseRedirect(certification.url)


class ShowCertificationHtmlReportView(View):
    http_method_names = ['get']

    @staticmethod
    def get(request, org_id, type, version_pk=None):
        # note that this view available only for staff, that's why we don't limit viewing only for organisation's
        # admins, owners and partners
        if not request.user.is_authenticated:
            organisation = None
        elif request.user.is_staff or request.user.is_superuser:
            # for staff and superusers
            organisation = get_object_or_404(Organisation, secure_id=org_id)
        else:
            # for the rest who can access this org with admin rights
            organisation = get_organisations(request.user, org_id)

        if not organisation:
            return HttpResponseRedirect(settings.LOGIN_REDIRECT_URL)

        certification = organisation.get_latest_certificate(type=type, version_pk=version_pk)
        if not certification:
            raise Http404()
        if not hasattr(certification, 'survey'):
            raise Http404()
        declaration = get_object_or_404(SurveyDeclaration, survey=certification.survey)
        if not declaration.html_report:
            raise Http404()
        return HttpResponse(declaration.html_report)

class DeclarationBase(View):

    def get_organisation(self, request, org_id):
        raise NotImplementedError()

    def certification_url(self, certification):
        raise NotImplementedError()

    @classmethod
    def get_survey_response_for_code(cls, code: str, certification: OrganisationCertification) -> SurveyResponse:
        return SurveyResponse.objects.get(
            question__code=code,
            survey=certification.survey,
            question__insurer=IASME_INSURER,
        )

    @classmethod
    def update_survey_response(cls, code: str, certification: OrganisationCertification, field_name: str, new_value: Any):
        response = cls.get_survey_response_for_code(code, certification)
        setattr(response, field_name, new_value)
        response.not_applicable = False
        response.save()

    @classmethod
    def handle_insurance(cls, show_insurance_questions, include_revenue, cleaned_data, certification):
        from django.db import transaction

        if not show_insurance_questions:
            return

        with transaction.atomic():
            if not cleaned_data.get('insurance_choice'):
                other_reason = cleaned_data.get('other_reason', '')
                other_reason_text = '' if not other_reason else f" {other_reason}"
                cls.update_survey_response(
                    QUESTION_CODE_IASME_INSURANCE_OPT_IN,
                    certification,
                    'value_text',
                    f"{cleaned_data.get('insurance_reason')}{other_reason_text}"
                )
                return

            cls.update_survey_response(
                QUESTION_CODE_IASME_INSURANCE_HEAD_OFFICE,
                certification,
                'value_boolean',
                True
            )
            cls.update_survey_response(
                QUESTION_CODE_IASME_INSURANCE_OPT_IN,
                certification,
                'value_boolean',
                True
            )
            cls.update_survey_response(
                QUESTION_CODE_IASME_INSURANCE_EMAIL,
                certification,
                'value_text',
                cleaned_data.get('email_address')
            )
            if include_revenue:
                cls.update_survey_response(
                    QUESTION_CODE_IASME_INSURANCE_REVENUE,
                    certification,
                    'value_money',
                    cleaned_data.get('revenue')
                )

    @staticmethod
    def show_insurance_questions(certification):
        return (waffle.switch_is_active('enable_insurance_opt_in_external_declaration')
                and not certification.insurance_opt_in_survey_value and certification.is_cyber_essentials)

    @classmethod
    def show_insurance_upgrade_questions(cls, certification: OrganisationCertification) -> bool:
        return (
            waffle.switch_is_active("enable_insurance_opt_in_external_declaration")
            and certification.is_cyber_essentials
            and certification.organisation.maximum_eligible_insurance_upgrade_pre_issued_certificate
            is not None
        )

    @classmethod
    def is_insurance_upgrade_question_required(cls, show_insurance_upgrade_questions: bool, certification: OrganisationCertification) -> bool:
        return show_insurance_upgrade_questions and certification.insurance_opt_in_survey_value

    @classmethod
    def handle_insurance_upgrade(cls, cleaned_data: dict, certification: OrganisationCertification, show_insurance_upgrade_questions: bool):
        if (not (show_insurance_upgrade_questions and cleaned_data.get('insurance_choice')) and
                not cls.is_insurance_upgrade_question_required(show_insurance_upgrade_questions, certification)):
            return

        if not cleaned_data.get('insurance_upgrade'):
            return

        certification.immediate_requested_coverage_amount = certification.organisation.maximum_eligible_insurance_upgrade_pre_issued_certificate
        certification.save()

    def post(self, request, org_id, type, version_pk=None):
        organisation = self.get_organisation(request, org_id)
        cert_standard = type
        if not cert_standard or not cert_standard.isdigit():
            raise Http404()
        certification = organisation.get_latest_certificate(type=cert_standard, version_pk=version_pk)
        if not certification or certification.not_started:
            raise Http404()

        survey_declaration = SurveyDeclaration.objects.filter(survey=certification.survey).first()
        if not certification.version.declaration or not survey_declaration:
            return render(request, self.template_name, {
                'access_error': 'Declaration page is not available'
            })
        if not survey_declaration.ready_to_be_signed:
            return render(request, self.template_name, {
                'access_error': 'Declaration is not ready to be signed'
            })
        if hasattr(organisation, 'partner') and organisation.partner:
            if forbid_declaration_submit_for_trial_accounts(organisation.partner):
                messages.add_message(
                    request,
                    messages.ERROR,
                    gettext_lazy("This is a trial account. You do not have permission to submit this declaration. "
                                 "Please contact your partner for more information.")
                )
                return HttpResponseRedirect(certification.declaration_url)

        # CE Willow does not have revenue question
        include_revenue = SurveyQuestion.objects.filter(
            code=QUESTION_CODE_IASME_INSURANCE_REVENUE,
            version=certification.version,
            insurer=IASME_INSURER,
        ).exists()
        show_insurance_questions = self.show_insurance_questions(certification)
        show_insurance_upgrade_questions = self.show_insurance_upgrade_questions(certification)
        insurance_upgrade_questions_required = self.is_insurance_upgrade_question_required(
            show_insurance_upgrade_questions,
            certification
        )
        form = DeclarationForm(
            request.POST,
            show_insurance_questions=show_insurance_questions,
            include_revenue=include_revenue,
            insurance_upgrade_questions_required=insurance_upgrade_questions_required,
            insurance_upgrade_questions_available=show_insurance_upgrade_questions,
        )
        if not form.is_valid():
            messages.add_message(
                request,
                messages.ERROR,
                gettext_lazy("Please make sure that all the fields have valid values.")
            )
            return HttpResponseRedirect(request.path)

        cleaned_data = form.cleaned_data
        signature_data = cleaned_data.get('signature_name')
        try:
            format_part, imgstr = signature_data.split(';base64,')
            ext = format_part.split('/')[-1]
        except Exception:
            messages.add_message(request, messages.ERROR, gettext_lazy("Invalid signature data."))
            return HttpResponseRedirect(request.path)

        self.handle_insurance_upgrade(cleaned_data, certification, show_insurance_upgrade_questions)

        file_name = f"{org_id}-{uuid.uuid4().hex}.{ext}"
        signature_file = ContentFile(base64.b64decode(imgstr), name=file_name)
        survey_declaration.signature = signature_file
        survey_declaration.date_signed = datetime.now()
        survey_declaration.declaration_name = cleaned_data.get("name")
        survey_declaration.declaration_job = cleaned_data.get("job")
        survey_declaration.declaration_date = timezone.now()
        survey_declaration.save()

        if hasattr(certification.survey, 'assessment') and certification.survey.assessment.is_sent_back:
            certification.survey.assessment.set_re_answered()
        # send sign declaration notification
        send_declaration_sign_notifications(organisation, survey_declaration)
        # create log
        EventLog.objects.create(
            user=request.user if request.user.is_authenticated else None,
            organisation=organisation,
            type=EventLog.SUBMITTED_DECLARATION,
            survey=certification.survey
        )
        generate_declaration_pdf_task.delay(survey_declaration.pk)
        certification.update_status()
        self.handle_insurance(show_insurance_questions, include_revenue, cleaned_data, certification)

        return self.certification_url(certification)


class Declaration(SignupMixin, DeclarationBase):
    """
    This view is available only for authorized users.
    """
    template_name = 'dashboard/declaration.html'

    def get_organisation(self, request, org_id):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()
        return organisation

    def certification_url(self, certification):
        return HttpResponseRedirect(certification.declaration_url)

    def get(self, request, org_id, type, version_pk=None):
        organisation = self.get_organisation(request, org_id)
        cert_standard = type
        if not cert_standard or not cert_standard.isdigit():
            raise Http404()
        certification = organisation.get_latest_certificate(type=cert_standard, version_pk=version_pk)
        if not certification:
            raise Http404()
        if certification.not_started:
            raise Http404()
        survey_declaration = SurveyDeclaration.objects.filter(survey=certification.survey).first()

        context = {
            "organisation": organisation,
            "certification": certification,
            "survey_declaration": survey_declaration,
        }

        # if organisation certificate subscription is paused and partner not cybersmart
        # redirect to view certificate paused. (not started / in survey)
        try:
            subscription = organisation.subscriptions.find_certificate_by_type(
                certificate_type=int(type))
        except ObjectDoesNotExist:
            pass
        else:
            if subscription and subscription.paused:
                context.update({'renewal_paused': True})

        if not certification.version.declaration or not survey_declaration:
            context.update({
                "access_error": "Declaration page is not available"
            })
            return render(request, self.template_name, context)
        if not survey_declaration.ready_to_be_signed:
            context.update({
                "access_error": "Declaration is not ready to be signed"
            })
            return render(request, self.template_name, context)

        declaration_text = certification.version.declaration_text
        if certification.insurance_opt_in_survey_value:
            declaration_text = certification.version.declaration_insurance_text
        elif certification.is_gdpr:
            # do not allow signing GDPR declaration without Cyber Essentials certifications issued
            if not organisation.ce_certification.has_issued_certifications:
                context.update({
                    "access_error": "You must be Cyber Essentials certified to proceed"
                })
                return render(request, self.template_name, context)

        topics = get_questionnaire_queryset(certification, include_not_applicable=True)
        downloaded_survey = generate_csv_organisation_survey(topics, certification.survey.id)

        context.update({
            "declaration_text": declaration_text,
            "topics": topics,
            "downloaded_survey": downloaded_survey
        })
        return render(request, self.template_name, context)


class DeclarationExternal(DeclarationBase):
    """
    This view will be available for everyone who knows organisation's secure id.
    """
    template_name = 'dashboard/declaration_external.html'

    def get_organisation(self, request, org_id):
        return get_object_or_404(Organisation, secure_id=org_id)

    def certification_url(self, certification):
        return HttpResponseRedirect(certification.declaration_signed_url)

    def get(self, request, org_id, type, version_pk=None):
        organisation = self.get_organisation(request, org_id)
        cert_standard = type
        if not cert_standard or not cert_standard.isdigit():
            raise Http404()
        certification = organisation.get_latest_certificate(type=cert_standard, version_pk=version_pk)
        if not certification:
            raise Http404()
        if certification.not_started:
            raise Http404()
        survey_declaration = SurveyDeclaration.objects.filter(survey=certification.survey).first()

        context = {
            "organisation": organisation,
            "certification": certification,
            "survey_declaration": survey_declaration
        }

        if not certification.version.declaration or not survey_declaration:
            context.update({
                "access_error": "Declaration page is not available"
            })
            return render(request, self.template_name, context)
        if not survey_declaration.ready_to_be_signed:
            context.update({
                "access_error": "Declaration is not ready to be signed"
            })
            return render(request, self.template_name, context)

        declaration_text = certification.version.declaration_text
        if certification.insurance_opt_in_survey_value:
            declaration_text = certification.version.declaration_insurance_text
        elif certification.is_gdpr:
            # do not allow signing GDPR declaration without Cyber Essentials certifications issued
            if not organisation.ce_certification.has_issued_certifications:
                context.update({
                    "access_error": "You must be Cyber Essentials certified to proceed"
                })
                return render(request, self.template_name, context)

        topics = get_questionnaire_queryset(certification, include_not_applicable=True)
        downloaded_survey = generate_csv_organisation_survey(topics, certification.survey.id)
        maximum_insurance_upgrade = organisation.maximum_eligible_insurance_upgrade_pre_issued_certificate_long
        show_insurance_upgrade_questions = self.show_insurance_upgrade_questions(certification)
        is_insurance_upgrade_question_required = self.is_insurance_upgrade_question_required(
            show_insurance_upgrade_questions,
            certification
        )

        context.update({
            "declaration_text": declaration_text,
            "topics": topics,
            "downloaded_survey": downloaded_survey,
            "show_insurance_questions": self.show_insurance_questions(certification),
            "include_revenue": SurveyQuestion.objects.filter(
                code=QUESTION_CODE_IASME_INSURANCE_REVENUE,
                version=certification.version,
                insurer=IASME_INSURER,
            ).exists(),
            "show_insurance_upgrade_questions": show_insurance_upgrade_questions,
            "maximum_insurance_upgrade": maximum_insurance_upgrade,
            "is_insurance_upgrade_question_required": is_insurance_upgrade_question_required,
        })
        return render(request, self.template_name, context)



@login_required
def get_companieshouse(request):
    q = request.GET.get('q', '')
    r = requests.get('https://api.companieshouse.gov.uk/search/companies?q=' + q,
                     headers={'Authorization': 'Basic WG9wTGV4YmlPZEJfcU5HSWZ5NFRHeHIxdFE4MFY5b3pyRy1nakVCajo='})
    try:
        response = r.json()
    except json.JSONDecodeError:
        response = {
            "page_number": 1,
            "kind": "search#companies",
            "total_results": 0,
            "items_per_page": 20,
            "start_index": 0,
            "items": []
        }
    return JsonResponse(response)


class CheckReport(PermissionRoleRequiredMixin, SignupMixin, View, CheckReportGetterMixin):
    template_name = 'dashboard/check-report.html'
    permission_required = DEVICES_PERMISSION

    def get(self, request, org_id):
        user = request.user
        try:
            organisation = get_organisations(user, org_id, as_queryset=True)
            if not organisation:
                raise IndexError
            organisation = organisation.first()
        except IndexError:
            raise PermissionDenied()

        # get the device type filter if any
        device_type = self.get_device_type()
        # get checks and filter by device type if applicable
        checks = self.filter_checks(AppCheck.objects.filter(active=True).order_by('order', 'id'), device_type)
        # get total number of installs and the nr of installs by device type if filtered
        all_installs = self.get_all_installs(org_id)
        installs_by_device_type = self.get_installs_by_device_type(org_id, device_type)

        if waffle.switch_is_active('use_new_applications_responses_2'):
            passed_responses, failed_responses = get_applications_responses_2(
                organisation=organisation,
                checks=checks
            )
        else:
            passed_responses, failed_responses = get_applications_responses(
                org_pk=organisation.pk,
                order=('app_check__pk', 'report__app_install__device_id', 'report__app_install__serial_number', '-report__created'),
                distinct=('app_check__pk', 'report__app_install__device_id', 'report__app_install__serial_number'),
                fail_values=(
                    'pk', 'app_check__pk', 'report__app_install__id'
                ),
                pass_values=(
                    'pk', 'app_check__pk', 'report__app_install__id'
                ),
                report_filters={'app_install__app_user__active': True},
                check_result_filters={'app_check__pk__in': checks.values_list('pk', flat=True)}
            )

        permanent_fixed_checks = {}
        for fix in OrganisationCheckPermanentFix.objects.filter(
                organisation=organisation, app_check__active=True
        ).values('app_check_id', 'reason'):
            permanent_fixed_checks[fix['app_check_id']] = fix['reason']

        installs_by_device_type_ids = set(installs_by_device_type.values_list('id', flat=True))
        failed_responses = [resp for resp in failed_responses if resp['report__app_install__id'] in installs_by_device_type_ids]

        passed_responses = [resp for resp in passed_responses if resp['report__app_install__id'] in installs_by_device_type_ids]

        failed_devices = set(CheckResult.objects.filter(
            id__in=[resp['pk'] for resp in failed_responses]
        ).distinct(
            'report__app_install__id'
        ).values_list('report__app_install__id', flat=True))

        check_pks = set(checks.values_list('pk', flat=True))
        checks_stat = {check_pk: {'failed': 0, 'passed': 0} for check_pk in check_pks}
        for resp in failed_responses:
            check_pk = resp['app_check__pk']
            if check_pk in check_pks:
                checks_stat[check_pk]['failed'] += 1

        for resp in passed_responses:
            check_pk = resp['app_check__pk']
            if check_pk in check_pks:
                checks_stat[check_pk]['passed'] += 1

        report = {
            'checks': list(checks),
            'is_ready': bool(passed_responses or failed_responses),
            'checks_stat': checks_stat,
        }

        context = {
            'organisation': organisation,
            'nr_installs': installs_by_device_type.count(),
            'total_installs': all_installs.count(),
            'count_failed_devices': len(failed_devices),
            'count_passed_devices': installs_by_device_type.exclude(id__in=failed_devices).count(),
            'report': report,
            'permanent_fixed_checks': permanent_fixed_checks,
            "enrolled_beta_features": organisation.get_enrolled_beta_features(
                feature_type=[CAP_V_FIVE_BETA_SWITCH, TRUSTD_BETA_SWITCH]
            ),
            'is_beta_device_type_filter': device_type in [CAP_V_FIVE_BETA_SWITCH, TRUSTD_BETA_SWITCH],
            'disable_beta_pill': waffle.flag_is_active(request, 'disable_beta_pill')
        }
        return render(request, self.template_name, context)


class BulkCheckReport(SignupMixin, View):
    template_name = 'dashboard/bulk-check-report.html'

    def get(self, request, org_id, *args, **kwargs):
        template = self.template_name
        user = request.user
        organisation = get_organisations(user, org_id)
        if not organisation:
            raise PermissionDenied()

        all_os = OperatingSystem.objects.all()
        app_users = AppUser.objects.filter(organisation=organisation)

        app_install_ids = organisation.app_installs.filter(inactive=False).values_list('id', flat=True)

        responses = []
        for x in app_install_ids:
            dict_response = {}
            installs = AppReport.objects.filter(app_install=x, total_responses__isnull=False)
            try:
                latest_report = installs.latest('modified')
                response = latest_report.check_results.all()
                for y in response:
                    try:
                        user_resolve = CheckManualFix.objects.get(
                            app_install=y.report.app_install,
                            app_check=y.check,
                            app_check__active=True
                        )

                    except CheckManualFix.DoesNotExist:
                        user_resolve = None
                    y.resolve = user_resolve
                    if (y.report.app_install.hostname not in dict_response):
                        dict_response[y.report.app_install.hostname] = {
                            'pass': 0,
                            'fail': 0,
                            'os': y.report.app_install.os
                        }
                    if y.response or y.resolve:
                        dict_response[y.report.app_install.hostname]['pass'] += 1
                    else:
                        dict_response[y.report.app_install.hostname]['fail'] += 1
            except Exception:
                latest_report = None
            responses.append({'response': dict_response})

        context = {
            'organisation': organisation,
            'all_os': all_os,
            'app_users': app_users,
            'responses': responses
        }
        return render(request, template, context)


def serialize_app_users(app_users):
    serializer = AppUserSerializer(app_users, many=True)
    return serializer.data


def get_app_users_to_send_email_to(organisation):
    """
    AppUsers should:
    - have at least one active application
    - be awaiting install
    Note: do not send to users who only have inactive applications.
    """
    res = AppUser.objects.filter(organisation=organisation, active=True).annotate(
        active_installs_count=Count("installs", filter=Q(installs__inactive=False)),
        inactive_installs_count=Count("installs", filter=Q(installs__inactive=True))
    ).filter(
        # Add users with at least one active install
        Q(active_installs_count__gt=0) |
        # Add users awaiting install
        Q(active_installs_count=0)
    ).exclude(
        # Exclude users with only inactive apps
        Q(active_installs_count=0) & Q(inactive_installs_count__gt=0)).prefetch_related('groups')
    return res


class ManageOrganisation(PermissionRoleRequiredMixin, SignupMixin, FormView):
    permission_required = PEOPLE_AND_ORGANISATION_PERMISSION
    template_name = 'dashboard/organisation-manage.html'
    form_class = ManageOrganisationForm

    def setup(self, request, *args, **kwargs):
        super().setup(request, *args, **kwargs)
        if request.user.is_authenticated:
            self.organisation = get_organisations(request.user, self.kwargs["org_id"])
            if not self.organisation:
                raise PermissionDenied()

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs.update({"organisation": self.organisation})
        return kwargs

    def get_success_url(self):
        return self.organisation.manageorganisation_url

    def paginate_queryset(self, queryset, page_size):
        """Paginate the queryset, if needed."""
        paginator = Paginator(queryset, page_size)
        page = self.kwargs.get('page') or self.request.GET.get('page') or 1
        try:
            page_number = int(page)
        except ValueError:
            if page == 'last':
                page_number = paginator.num_pages
            else:
                raise Exception(gettext_lazy('Page is not "last", nor can it be converted to an int.'))
        try:
            page = paginator.page(page_number)
            return (paginator, page, page.object_list, page.has_other_pages())
        except Exception as e:
            raise Exception(gettext_lazy('Invalid page (%(page_number)s): %(message)s') % {
                'page_number': page_number,
                'message': str(e)
            })

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["organisation"] = self.organisation
        context["params"] = self.request.GET
        context['params_query'] = 'notifications=1'
        context['pagination_url'] = reverse(
            'dashboard:manage-organisation',
            kwargs={'org_id': self.organisation.secure_id}
        )
        return context

    def get_initial(self):
        initial = {
            "name": self.organisation.name,
            "industry": self.organisation.industry,
            "industry_description": self.organisation.industry_description,
            "size": self.organisation.size,
            "bulk_install": self.organisation.bulk_install,
            "email_message": self.organisation.email_message,
            "security_emails_frequency": self.organisation.security_emails_frequency,
            "smart_policies": self.organisation.policies_support,
            "id__": self.kwargs["org_id"],
            'lms_send_email_notifications': self.organisation.settings.lms_send_email_notifications,
            'devices_cleaning_enabled': self.organisation.settings.devices_cleaning_enabled,
            'devices_cleaning_days': self.organisation.settings.devices_cleaning_days,
            'enforce_mfa': self.organisation.settings.enforce_multi_factor_authentication,
            'default_language': self.organisation.settings.default_language,
        }
        return initial

    def form_valid(self, form):
        try:
            form.update_data(self.request)
        except forms.ValidationError as error:
            messages.add_message(self.request, messages.ERROR, error.message)
            return HttpResponseRedirect(self.get_success_url())
        else:
            messages.add_message(self.request, messages.SUCCESS, gettext_lazy('Organisation details updated'))
            return super().form_valid(form)


class OrganisationSettingsBaseForm(SignupMixin, FormView):
    """ Base form to be overriden since it reduces code duplication """
    # the base url to use on the success_url
    base_url = None

    def setup(self, request, *args, **kwargs):
        super().setup(request, *args, **kwargs)
        if request.user.is_authenticated:
            self.organisation = get_organisations(request.user, self.kwargs["org_id"])
            if not self.organisation:
                raise PermissionDenied()
            self.set_success_url()

    def set_success_url(self):
        """ Helper method to set the success_url depending on the base_url """
        self.success_url = reverse(
            self.base_url,
            kwargs={'org_id': self.organisation.secure_id},
        )

    def _perform_update(self, form):
        """Does the update using form values"""
        organisation_settings = self.organisation.settings
        for field, value in form.cleaned_data.items():
            setattr(organisation_settings, field, value)
        organisation_settings.save()

    def form_valid(self, form):
        """ Save updated organisation settings """
        self._perform_update(form)
        messages.add_message(
            self.request,
            messages.SUCCESS,
            gettext_lazy('Changes have been saved')
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        """ We need to pass the organisation to the template,
        else it will get the parent/dist org """
        context = super().get_context_data(**kwargs)
        context["organisation"] = self.organisation
        return context


class ManageNotifications(OrganisationSettingsBaseForm):
    template_name = 'dashboard/manage-notifications.html'
    form_class = ManageNotificationsForm
    base_url = 'dashboard:manage-notifications'

    def set_initial_values(self, notifications, reminders):
        """ Set initial values for each in-app notification switch """
        org_settings = self.organisation.settings
        default_hours = 12
        reminders_check = f'{reminders}_check'
        self.initial[notifications] = getattr(org_settings, notifications)
        self.initial[reminders_check] = getattr(org_settings, reminders) is not None
        self.initial[reminders] = getattr(org_settings, reminders) \
            if getattr(org_settings, reminders) else default_hours

    def get_initial(self):
        """ Set initial values for the current organisation and return them """
        # In-app notifications
        switches = ['app_checks', 'policies', 'academy']
        for switch in switches:
            self.set_initial_values(f'{switch}_notifications', f'{switch}_reminders')
        return self.initial


class ManageFeaturesView(OrganisationSettingsBaseForm):
    """
    Manages the features for a specific organisation set by a partner
    """
    template_name = 'dashboard/manage-features.html'
    form_class = ManageFeaturesForm
    base_url = 'dashboard:manage-features'

    def get_initial(self):
        """ Get initial values for the current organisation """
        features = [
            "smart_score_enabled", "academy_enabled", "uba_enabled",
            "cap_steps_to_fix_enabled", "enforce_multi_factor_authentication", "cap_vulnerable_software_enabled",
            "cybersmart_learn_enabled"
        ]
        for feature in features:
            self.initial[feature] = getattr(self.organisation.settings, feature)

        for feature in BetaFeature.objects.all():
            self.initial[feature.kind] = self.organisation.is_enrolled_in_beta_feature(feature.kind)

        return self.initial

    def _perform_feature_enrolment_update(self, field, value):
        """ This performs the update for feature enrolment
        Note: skipping web notifications since this is a Beta launch
        """
        if can_partner_enable_feature_enrolment(self.organisation.partner, field):
            # to add or remove an organisation depending if the switch is ON or OFF
            action = {True: 'add', False: 'remove'}
            beta_enrolment = self.organisation.partner.beta_enrolment.get(feature__kind=field)
            # only change if value has changed
            if value != self.initial[field]:
                getattr(beta_enrolment.organisations, action[value])(self.organisation)
                calculate_organisation_analytics.delay(self.organisation.pk)
                # create the Trustd customer if the feature is enabled.
                if value and (
                        is_trustd_mobile_available_for_organisation(
                            self.organisation
                        ) or beta_enrolment.feature.kind == TRUSTD_BETA_SWITCH
                ):
                    from trustd.tasks import create_trustd_customer
                    create_trustd_customer.delay(self.organisation.id)

    def _perform_update(self, form):
        """ Save updated features """
        beta_features = BetaFeature.objects.values_list('kind', flat=True)
        for field, value in form.cleaned_data.items():
            # get the user friendly label
            label = form.fields.get(field).label
            if field in beta_features:
                self._perform_feature_enrolment_update(field, value)
            # only update if value has changed
            elif value != getattr(self.organisation.settings, field) and field != 'uba_enabled':
                setattr(self.organisation.settings, field, value)
                self.send_notification(label, value)
            # only update UBA if it is not enabled and switch was enabled
            elif not self.organisation.settings.uba_enabled and field == 'uba_enabled' and value:
                setattr(self.organisation.settings, field, value)
                self.send_notification(label, value)
        self.organisation.settings.save()

    def send_notification(self, label, value):
        """ Send notification """
        feature_switch_value = 'enabled' if value else 'disabled'
        kwargs = {
            'user_name': get_full_name_or_email(self.request.user),
            'organisation_id': self.organisation.id,
            'organisation_name': self.organisation.name,
            'partner_name': self.organisation.partner.name,
            'feature_switch_name': label,
            'feature_switch_value': feature_switch_value,
            'url': reverse('dashboard:organisation', kwargs={'org_id': self.organisation.secure_id})
        }
        NotificationHandler.send_notification(message_type='org_features_updated', **kwargs)


class UnsubscribeEmailView(TemplateView):
    template_name = 'dashboard/unsubscribe_email.html'

    def get_context_data(self, **kwargs):
        context = super(UnsubscribeEmailView, self).get_context_data(**kwargs)
        try:
            u = OrganisationAdmin.objects.get(organisation__secure_id=kwargs.get('org_id'), pk=kwargs.get('user'))
        except OrganisationAdmin.DoesNotExist:
            context['message'] = 'Something went wrong'
        else:
            if not u.subscribed:
                context['message'] = 'You are already unsubscribed from weekly email report'
            else:
                u.subscribed = False
                u.save()
                context['message'] = 'You have successfully unsubscribed from weekly email report'
        return context


class DownloadLinksView(TemplateView):
    template_name_self = 'dashboard/download-links-self.html'
    template_name_bulk = 'dashboard/download-links-bulk.html'

    def get(self, request, app_user_uuid, *args, **kwargs):
        if not uuid_is_valid(app_user_uuid):
            raise Http404()

        app_user = get_object_or_404(AppUser, uuid=app_user_uuid)

        cap_v_five_beta_requested = request.GET.get(CAP_V_FIVE_BETA_SWITCH, "").lower() == "true"
        cap_v_five_beta_page_enabled = False
        if cap_v_five_beta_requested and is_organisation_enrolled_in_feature(app_user.organisation, CAP_V_FIVE_BETA_SWITCH):
            cap_v_five_beta_page_enabled = True


        admin = app_user.organisation.get_admin_users.first()
        if not admin:
            admin_name = ''
        else:
            admin_name = admin.first_name or admin.email

        context = {
            'app_user': app_user,
            'org_name': app_user.organisation.name,
            'email': app_user.email,
            'full_name': '{0} {1}'.format(app_user.first_name, app_user.last_name),
            'admin_name': smart_str(admin_name),
            'admin_message': smart_str(app_user.organisation.email_message),
            'organisation': app_user.organisation,
            'hide_nav_bar': True,
            'cap_v_five_beta_page_enabled': cap_v_five_beta_page_enabled
        }

        if cap_v_five_beta_page_enabled or is_cap_v_five_ga_enabled():
            context["winmsi_link"] = get_bulk_live_server_link_cap_v5(settings.BUILD_TYPES.MSI, app_user_uuid)
            context["macos_link"] = get_bulk_live_server_link_cap_v5(settings.BUILD_TYPES.PKG, app_user_uuid)
            context["winmsi_version"] = AppFile.get_v5_windows_version()
            context["macos_version"] = AppFile.get_v5_macos_version()
        else:
            context["winmsi_version"] = AppFile.get_windows_version()
            context["macos_version"] = AppFile.get_macos_version()
            if app_user.organisation.bulk_install:
                context["winmsi_link"] = get_bulk_live_server_link(settings.BUILD_TYPES.MSI, app_user_uuid)
                context["macos_link"] = get_bulk_live_server_link(settings.BUILD_TYPES.PKG, app_user_uuid)
            else:
                winmsi_app_file = AppFile.get_windows_app_file()
                macos_app_file = AppFile.get_macos_app_file()
                context["winmsi_link"] = winmsi_app_file.download_url if winmsi_app_file else None
                context["macos_link"] = macos_app_file.download_url if macos_app_file else None

        return render(
            request, self.template_name_bulk if app_user.organisation.bulk_install else self.template_name_self, context
        )


class RegisterAppView(TemplateView):
    template_name = 'dashboard/register-app.html'

    def get(self, request):
        context = {}
        data = json.loads(base64.b64decode(request.GET["data"]))
        app = AppInstall.objects.filter(
            device_id=data.get("device_id"),
            serial_number=data.get("serial_number"),
            registration_unique_token=data.get("registration_unique_token")
        ).first()
        # full match
        if app:
            AppInstallActivationLog.objects.create(
                app_install=app,
                status=AppInstallActivationLog.STATUS.FULL_MATCH,
                endpoint=AppInstallActivationLog.ENDPOINT.REGISTER,
                device_id=data.get("device_id"),
                serial_number=data.get("serial_number"),
                registration_unique_token=data.get("registration_unique_token"),
                inactive_status=app.inactive,
                last_check_in_log=app.last_check_in
            )
            context.update({
                'matched_uuid': app.app_user.uuid
            })
        # attempt partial match
        else:
            if waffle.switch_is_active('app_user_activation_fallback_log'):
                try:
                    partial_match_app = AppInstall.objects.get(
                        device_id=data.get("device_id"),
                        serial_number=data.get("serial_number")
                    )
                    AppInstallActivationLog.objects.create(
                        app_install=partial_match_app,
                        status=AppInstallActivationLog.STATUS.PARTIAL_MATCH,
                        endpoint=AppInstallActivationLog.ENDPOINT.REGISTER,
                        device_id=data.get("device_id"),
                        serial_number=data.get("serial_number"),
                        registration_unique_token=data.get("registration_unique_token"),
                        inactive_status=partial_match_app.inactive,
                        last_check_in_log=partial_match_app.last_check_in
                    )
                    # UUID will be set in the CyberSmart application via a deep link
                    if waffle.switch_is_active('app_user_activation_fallback_fix'):
                        context.update({
                            'matched_uuid': partial_match_app.app_user.uuid
                        })
                except AppInstall.DoesNotExist:
                    AppInstallActivationLog.objects.create(
                        status=AppInstallActivationLog.STATUS.NO_MATCH,
                        endpoint=AppInstallActivationLog.ENDPOINT.REGISTER,
                        device_id=data.get("device_id"),
                        serial_number=data.get("serial_number"),
                        registration_unique_token=data.get("registration_unique_token")
                    )
                except AppInstall.MultipleObjectsReturned:
                    AppInstallActivationLog.objects.create(
                        status=AppInstallActivationLog.STATUS.MULTI_MATCH,
                        endpoint=AppInstallActivationLog.ENDPOINT.REGISTER,
                        device_id=data.get("device_id"),
                        serial_number=data.get("serial_number"),
                        registration_unique_token=data.get("registration_unique_token")
                    )
        return render(request, self.template_name, context)

    @staticmethod
    def post(request, *args, **kwargs):
        response = {"success": False, "info": False, "error": ""}
        try:
            # check if data parameter is passed
            if "data" not in request.POST:
                raise Exception("Incorrect request. Missing 'data' in POST request.")
            # check if app user uuid is passed
            if "uuid" not in request.POST:
                raise Exception("Incorrect request. Missing 'uuid' in POST request.")
            # parse base64 data
            data = json.loads(base64.b64decode(request.POST["data"]))
            # check if all needed data is passed
            if data.get("serial_number") is None or not data.get("device_id") or not data.get(
                    "registration_unique_token"):
                raise Exception(
                    "Incorrect request. Missing 'serial_number', 'device_id' or 'registration_unique_token'.")
            # get app user
            app_user = AppUser.objects.filter(uuid=request.POST.get("uuid")).first()
            if not app_user:
                raise Exception("UUID is not correct")

            # check if application is already registered
            if AppInstall.objects.filter(
                    app_user=app_user,
                    device_id=data["device_id"],
                    serial_number=data["serial_number"],
                    inactive=True,
                    registration_unique_token=data["registration_unique_token"]
            ).exists():
                response["info"] = str("The application is already registered")
                return JsonResponse(response)
            # check if application is already activated
            if AppInstall.objects.filter(
                    app_user=app_user,
                    device_id=data["device_id"],
                    serial_number=data["serial_number"],
                    inactive=False,
                    registration_unique_token=data["registration_unique_token"]
            ).exists():
                response["info"] = str("The application is already activated")
                return JsonResponse(response)
        except AppUser.DoesNotExist:
            response["error"] = "UUID is not found"
        except Exception as error:
            response["error"] = str(error)
        else:
            # if we already have an application with the same app user, device id and serial number
            # then it means that application was reinstalled and registration token was changed
            partial_match_app = AppInstall.objects.filter(
                app_user=app_user,
                device_id=data["device_id"],
                serial_number=data["serial_number"]
            ).exists()
            if not partial_match_app:
                # otherwise create a new app install
                app_install = AppInstall.objects.create(
                    app_user=app_user,
                    device_id=data["device_id"],
                    serial_number=clean_serial_number(data["serial_number"]),
                    registration_unique_token=data["registration_unique_token"],
                    inactive=True,
                    os=OperatingSystem.objects.all().first()  # random os
                )
                # send notification about installed active protect app
                app_install.send_notification('user_installed_app')
            response["success"] = True
        return JsonResponse(response)


class SendAppsAwaitingView(SignupMixin, View):
    """
    Sends new applications to all organisation's app users that dont't have any app installs yet.
    """
    http_method_names = ['get']

    @staticmethod
    def get(request, org_id):
        org = get_organisations(request.user, org_id)
        if not org:
            raise PermissionDenied()
        else:
            awaiting_install = AppUser.objects.filter(
                organisation=org,
                active=True
            ).annotate(
                active_installs_count=Count("installs", filter=Q(installs__inactive=False))
            ).filter(active_installs_count=0)
            # send download links
            if awaiting_install.count() > 0:
                send_apps(org, list(awaiting_install.values_list('uuid', flat=True)), reminder=True)
                messages.add_message(
                    request,
                    messages.SUCCESS,
                    gettext_lazy('Email with link to the install file is sent to all users yet to install')
                )
            else:
                messages.add_message(
                    request,
                    messages.ERROR,
                    gettext_lazy('No users awaiting install')
                )
            return HttpResponseRedirect(reverse('dashboard:organisation', kwargs={'org_id': org.secure_id}))


class SendNewAppView(SignupMixin, View):
    """
    Sends new application to individual app user.
    """
    http_method_names = ['get']

    @staticmethod
    def get(request, org_id):
        org = get_organisations(request.user, org_id)
        if not org:
            raise PermissionDenied()
        else:
            app_user = get_object_or_404(AppUser, uuid=request.GET.get('app_user_uuid'))

            # send download links
            send_apps(org, [app_user.uuid], reminder=True)

            messages.add_message(
                request,
                messages.SUCCESS,
                gettext_lazy('Email with link to the install file is sent')
            )
            return HttpResponseRedirect(request.headers.get('referer'))


class BulkResolvePermanentView(SignupMixin, View):
    http_method_names = ['post']

    @staticmethod
    def post(request, org_id):
        response = {'status': 1}
        try:
            organisation = get_organisations(request.user, org_id)
            if not organisation:
                raise PermissionDenied()
            check_pk = request.POST.get('question_pk')
            if not all([check_pk, check_pk.isdigit()]):
                raise Http404()
        except Http404:
            response['status'] = 2
        else:
            try:
                check = AppCheck.objects.get(pk=check_pk, active=True)
            except (AppCheck.DoesNotExist, AppCheck.MultipleObjectsReturned):
                response['status'] = 3
            else:
                OrganisationCheckPermanentFix.objects.get_or_create(
                    organisation=organisation,
                    app_check=check,
                    reason=request.POST.get('reason', ''),
                )
        return JsonResponse(response)


class RemovePermanentResolveView(SignupMixin, View):
    http_method_names = ['post']

    @staticmethod
    def post(request, org_id):
        response = {'status': 1}
        try:
            organisation = get_organisations(request.user, org_id)
            if not organisation:
                raise PermissionDenied()
            check_pk = request.POST.get('question_pk')
            if not all([check_pk, check_pk.isdigit()]):
                raise Http404()
        except Http404:
            response['status'] = 2
        else:
            try:
                check = AppCheck.objects.get(pk=check_pk, active=True)
            except (AppCheck.DoesNotExist, AppCheck.MultipleObjectsReturned):
                response['status'] = 3
            else:
                OrganisationCheckPermanentFix.objects.filter(
                    organisation=organisation,
                    app_check=check,
                    app_check__active=True
                ).delete()
        return JsonResponse(response)


class PoliciesView(PermissionRoleRequiredMixin, SignupMixin, View):
    """
    Legacy view for policies, no longer used.
    """
    template_name = 'dashboard/policies.html'
    permission_required = DEVICES_PERMISSION

    def get(self, request, org_id):
        organisation = get_organisations(request.user, org_id, as_queryset=True)
        if not organisation:
            raise PermissionDenied()

        if waffle.switch_is_active("smart-policies-new-ui"):
            return redirect('smart_policies:main', org_id=org_id)

        organisation = organisation.prefetch_related(
            Prefetch('policies', OrganisationPolicy.objects.filter(organisation__secure_id=org_id).order_by(
                '-created'
            ))
        )[0]
        ps = ProjectSettings.objects.get()
        return render(request, self.template_name, {
            'organisation': organisation,
            'policies_support': organisation.policies_support,
            'policy_guidance_html': ps.policy_guidance_html
        })

    def post(self, request, org_id):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()

        if not organisation.policies_support:
            messages.add_message(
                request,
                messages.ERROR,
                gettext_lazy('Smart Policies is included with CyberSmart Active Protect')
            )
            return HttpResponseRedirect(reverse('dashboard:policies', kwargs={'org_id': organisation.secure_id}))

        validator = FileTypeValidator(
            allowed_types=ALLOWED_TYPES,
            allowed_extensions=ALLOWED_EXTENSIONS
        )
        policy_id = request.GET.get('policy_id')
        for document in request.FILES.values():
            try:
                validator(document)
            except ValidationError:
                return JsonResponse({
                    'status': 'false',
                    'message': gettext_lazy(
                        "Invalid File type - File must be either pdf, document, spreadsheet, presentation or image."
                    )
                }, status=400)
            if policy_id:
                try:
                    policy = OrganisationPolicy.objects.get(pk=policy_id)
                except OrganisationPolicy.DoesNotExist:
                    continue
            else:
                policy = OrganisationPolicy.objects.create(
                    organisation=organisation,
                    name=document.name.split('.')[0]
                )
            # remove flag main from all versions, because just created version will be main
            policy.versions.update(main=False)
            OrganisationPolicyVersion.objects.create(
                policy=policy,
                document=document,
                version=1.0 if not policy.latest_version else '%.3f' % (policy.latest_version.version + 0.1),
                main=True
            )

        calculate_organisation_analytics.delay(organisation.pk)

        return render(request, self.template_name, {'organisation': organisation})


class PolicyUpdateView(LoginRequiredMixin, UpdateView):
    http_method_names = ['post']
    model = OrganisationPolicy
    fields = ['name', 'active']
    success_url = reverse_lazy('dashboard:policies')

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        self.org = get_organisations(request.user, kwargs.get('org_id'))
        if not self.org:
            raise PermissionDenied()
        if not self.org.policies_support:
            messages.add_message(
                request,
                messages.ERROR,
                gettext_lazy('Smart Policies is included with CyberSmart Active Protect')
            )
            return HttpResponseRedirect(reverse('dashboard:policies', kwargs={'org_id': self.org.secure_id}))
        return super(PolicyUpdateView, self).dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return super(PolicyUpdateView, self).get_queryset().filter(
            organisation__secure_id=self.kwargs.get('org_id')
        )

    def form_valid(self, form):
        if not re.match(r'^[a-zA-Z0-9-_. \[\]]*$', form.cleaned_data['name']):
            with sentry_sdk.push_scope() as scope:
                scope.set_extra('input value', form.cleaned_data['name'])
                sentry_sdk.capture_message(
                    message='Invalid characters. You can use only letters, digits and other safe characters',
                    level='error'
                )
            return JsonResponse(
                {'success': False,
                 'name': self.object.name,
                 'active': self.object.active,
                 'error': 'We do not allow special characters in the policy name, please change the policy name'
                 }
            )
        else:
            self.object = form.save()
            self.object.save()
            calculate_organisation_analytics.delay(self.org.pk)
            return JsonResponse(
                {'success': True, 'name': self.object.name, 'active': self.object.active}
            )

    def form_invalid(self, form):
        return JsonResponse(
            {'success': False, 'name': self.object.name, 'active': self.object.active}
        )


class PolicyDeleteView(LoginRequiredMixin, DeleteView):
    http_method_names = ['post']
    model = OrganisationPolicy
    fields = ['name', 'active']
    success_url = reverse_lazy('dashboard:policies')

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        self.org = get_organisations(request.user, kwargs.get('org_id'))
        if not self.org:
            raise PermissionDenied()
        if not self.org.policies_support:
            messages.add_message(
                request,
                messages.ERROR,
                gettext_lazy('Smart Policies is included with CyberSmart Active Protect')
            )
            return HttpResponseRedirect(reverse('dashboard:policies', kwargs={'org_id': self.org.secure_id}))
        return super(PolicyDeleteView, self).dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return super(PolicyDeleteView, self).get_queryset().filter(
            organisation__secure_id=self.kwargs.get('org_id')
        )

    def form_valid(self, form):
        """
        Calls the delete() method on the fetched object and then
        redirects to the success URL.
        """
        self.object.delete()
        calculate_organisation_analytics.delay(self.org.pk)
        return JsonResponse({'success': True})


class PolicyVersionUpdateView(LoginRequiredMixin, UpdateView):
    http_method_names = ['post']
    model = OrganisationPolicyVersion
    fields = ['active', 'version']
    success_url = reverse_lazy('dashboard:policies')

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        self.org = get_organisations(request.user, kwargs.get('org_id'))
        if not self.org:
            raise PermissionDenied()
        if not self.org.policies_support:
            messages.add_message(
                request,
                messages.ERROR,
                gettext_lazy('Smart Policies is included with CyberSmart Active Protect')
            )
            return HttpResponseRedirect(reverse('dashboard:policies', kwargs={'org_id': self.org.secure_id}))
        return super(PolicyVersionUpdateView, self).dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return super(PolicyVersionUpdateView, self).get_queryset().filter(
            policy_id=self.kwargs.get('policy_pk'),
            policy__organisation__secure_id=self.kwargs.get('org_id')
        )

    def form_valid(self, form):
        policy_version = OrganisationPolicyVersion.objects.get(pk=self.object.pk)
        version = handle_user_input(form.cleaned_data['version'])
        if self.get_queryset().filter(version=version).count() > 0:
            return JsonResponse(
                {
                    'success': False,
                    'active': self.object.active,
                    'version': self.object.version,
                    'message': gettext_lazy('This version already exists')
                }
            )
        elif type(version) not in (int, float):
            return JsonResponse(
                {
                    'success': False,
                    'active': self.object.active,
                    'version': self.object.version,
                    'message': gettext_lazy('Version number should be numbers')
                }
            )
        else:
            main = version > self.object.policy.latest_version.version
            if main:
                self.object.policy.versions.update(main=False)
            else:
                self.object.policy.versions.update(main=False)
                try:
                    highest_version = self.object.policy.versions.filter(active=True).order_by('version')[0]
                    highest_version.main = True
                    highest_version.save()
                except IndexError:
                    pass
            if version != policy_version.version:
                # if policy version was changed reset all agreements
                policy_version.policy.reset()
            self.object.main = main
            self.object.save()
            self.object = form.save()
            calculate_organisation_analytics.delay(self.org.pk)
            return JsonResponse(
                {'success': True, 'active': self.object.active, 'version': self.object.version}
            )

    def form_invalid(self, form):
        return JsonResponse(
            {'success': False, 'active': self.object.active, 'version': self.object.version}
        )


class SurveyAutoAnswerView(SignupMixin, View):
    http_method_names = ['post']

    @staticmethod
    def post(request, org_id, type, version_pk=None):
        response = {'success': True, 'questions': []}
        status = 200

        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()
        certification = organisation.get_latest_certificate(type=type, version_pk=version_pk)
        if not certification:
            raise Http404()

        if request.GET.get('for') == 'choices':
            # for questions with choices
            try:
                choice_pk = request.POST.get('choice_pk')
                if not choice_pk or not choice_pk.isdigit():
                    raise ValueError()
                choice_pk = json.loads(choice_pk)
                choice = SurveyQuestionChoices.objects.get(pk=choice_pk, question__version=certification.version)
            except (ValueError, SurveyQuestionChoices.DoesNotExist):
                response['success'] = False
                status = 400
            else:
                for auto_answer_question in choice.auto_answer_choices.all():
                    response['questions'].append({
                        'question_pk': auto_answer_question.pk,
                        'choice_pk': auto_answer_question.auto_answer_current_question_choice.pk
                    })
        else:
            # for boolean questions
            try:
                question_pk = request.POST.get('question_pk')
                question_value = request.POST.get('value')
                if not question_pk or not question_pk.isdigit():
                    raise ValueError()
                question_pk = json.loads(question_pk)
                question_value = json.loads(question_value)
                question = SurveyQuestion.objects.get(
                    pk=question_pk, version=certification.version,
                )
            except (ValueError, SurveyQuestion.DoesNotExist):
                response['success'] = False
                status = 400
            else:
                for auto_answer_question in question.auto_answer_questions.all():
                    if auto_answer_question.auto_answer_survey_question_boolean == question_value:
                        response['questions'].append({
                            'pk': auto_answer_question.pk,
                            'value': auto_answer_question.auto_answer_current_question_boolean
                        })
                    else:
                        response['questions'].append({
                            'pk': auto_answer_question.pk,
                            'value': not auto_answer_question.auto_answer_current_question_boolean
                        })
        return JsonResponse(response, status=status)


class ApproveDeclarationEmailView(SignupMixin, FormView):
    template_name = 'dashboard/declaration.html'
    form_class = ApprovalEmailForm

    def get_success_url(self):
        if self.kwargs.get('version_pk'):
            kwargs = {
                'org_id': self.kwargs['org_id'],
                'type': self.kwargs['type'],
                'version_pk': self.kwargs.get('version_pk')
            }
        else:
            kwargs = {
                'org_id': self.kwargs['org_id'],
                'type': self.kwargs['type']
            }
        return reverse('dashboard:declaration', kwargs=kwargs)

    def get_context_data(self, **kwargs):
        context = super(ApproveDeclarationEmailView, self).get_context_data(**kwargs)
        organisation = get_organisations(self.request.user, self.kwargs['org_id'])
        if not organisation:
            raise PermissionDenied()
        else:
            context['organisation'] = organisation
        return context

    def form_valid(self, form):
        organisation = get_organisations(self.request.user, self.kwargs['org_id'])
        if not organisation:
            raise PermissionDenied()
        cert_standard = self.kwargs['type']
        if not cert_standard or not cert_standard.isdigit():
            raise Http404()
        certification = organisation.get_latest_certificate(
            type=cert_standard, version_pk=self.kwargs.get('version_pk')
        )
        try:
            declaration = SurveyDeclaration.objects.get(survey=certification.survey)
        except SurveyDeclaration.DoesNotExist:
            raise Http404()
        else:
            declaration.declaration_name = handle_user_input(form.cleaned_data.get("name"))
            declaration.declaration_job = handle_user_input(form.cleaned_data.get("title"))
            declaration.declaration_email = handle_user_input(form.cleaned_data.get("email"))
            declaration.declaration_secondary_email = handle_user_input(form.cleaned_data.get("secondary_email"))
            declaration.save()
        # send email to inform org admins that the declaration has been sent out to be signed
        send_declaration_sent_out_for_signing.delay(declaration.id)
        # send email to inform selected user that they need to sign the declaration
        send_declaration_signing_reminder_email.delay(declaration.id)
        return super(ApproveDeclarationEmailView, self).form_valid(form)


class EndOfLifeReportView(SignupMixin, View):
    template_name = 'dashboard/end-of-life-report.html'

    def get_queryset(self) -> QuerySet[AppInstall]:
        """
        Returns a QuerySet of EndOfLife objects annotated with the number of AppInstalls that are using them.
        """
        return AppInstall.objects.filter(
            id__in=self.organisation.active_app_installs
        ).filter(
            end_of_life__isnull=False
        ).values('end_of_life__base_operating_system', 'end_of_life__cycle').annotate(
            counter=Count('end_of_life')
        ).values(
            'end_of_life__pk', 'end_of_life__base_operating_system', 'end_of_life__cycle',
            'end_of_life__end_of_life_date', 'end_of_life__latest', 'end_of_life__link',
            'end_of_life__codename', 'end_of_life__release_date', 'end_of_life__release_date',
            'end_of_life__manually_unsupported', 'counter'
        )

    def get(self, request, org_id):
        self.organisation = get_organisations(request.user, org_id)
        if not self.organisation:
            raise PermissionDenied()

        parsed = self.get_queryset()

        context = {
            'organisation': self.organisation,
            'parsed_builds': parsed,
            'today': datetime.today(),
        }
        return render(request, self.template_name, context)


class EndOfLifeCSVView(CSVReportMixin, SignupMixin, View):
    """
    View that generates a CSV file with the End of Life report.
    """

    def get_file_name(self) -> str:
        """
        Returns the name of the CSV file.
        """
        return f"end_of_file_{self.organisation.name.replace(' ', '_')}_{timezone.now().strftime('%b_%d_%Y')}"

    def get_field_names(self) -> list[str]:
        """
        Returns field names that will be used as table headers in csv file.
        """
        return ["Operating System", "Release", "Security End of life", "Devices", "Latest Build"]

    def get_queryset(self) -> QuerySet[AppInstall]:
        """
        Returns a QuerySet of EndOfLife objects annotated with the number of AppInstalls that are using them.
        """
        active_app_installs = self.organisation.active_app_installs.values_list('id', flat=True)
        return AppInstall.objects.filter(
            end_of_life__isnull=False, id__in=active_app_installs
        ).values('end_of_life__base_operating_system', 'end_of_life__cycle').annotate(
            counter=Count('end_of_life')
        ).values(
            'end_of_life__pk', 'end_of_life__base_operating_system', 'end_of_life__cycle',
            'end_of_life__end_of_life_date', 'end_of_life__latest', 'end_of_life__link',
            'end_of_life__codename', 'end_of_life__release_date', 'end_of_life__release_date',
            'counter'
        )

    @staticmethod
    def __get_end_of_life(build: dict) -> str:
        """
        Calculate the end-of-life status based on build information.
        """
        today = datetime.now().date()

        if not build["end_of_life__end_of_life_date"]:
            status = f"Supported (No end-of-life date, released {timesince(build['end_of_life__release_date'])} ago)"
        elif build["end_of_life__end_of_life_date"] < today:
            status = f"{build['end_of_life__end_of_life_date']} (Ended {timesince(build['end_of_life__end_of_life_date'])} ago)"
        elif build["end_of_life__end_of_life_date"] < today + timedelta(days=180):
            status = f"{build['end_of_life__end_of_life_date']} (Ends in {build['end_of_life__end_of_life_date'] - today})"
        else:
            status = f"{build['end_of_life__end_of_life_date']} (Ends in {build['end_of_life__end_of_life_date'] - today})"

        return status

    def __write_to_csv(self, writer) -> None:
        """
        Writes the report to the CSV file.
        """
        for app_install in self.get_queryset():
            row = {
                "Operating System": app_install["end_of_life__base_operating_system"],
                "Release": app_install["end_of_life__cycle"],
                "Security End of life": self.__get_end_of_life(app_install),
                "Devices": app_install["counter"],
                "Latest Build": app_install["end_of_life__latest"],
            }
            writer.writerow(row)

    def get(self, request, org_id):
        self.organisation = get_organisations(request.user, org_id)
        if not self.organisation:
            raise PermissionDenied()
        if not self.organisation.policies_support:
            raise Http404()

        response = self.get_csv_http_response()
        writer = self.get_csv_writer(response)
        self.__write_to_csv(writer=writer)

        # record download usage
        file_format = "xlsx" if request.GET.get("format") == "xlsx" else "csv"
        report_name = "end_of_life_report"
        record_report_download_usage.delay(
            entity_pk=self.organisation.pk,
            level="organisation",
            report_name=report_name,
            file_format=file_format
        )

        return self.csv_or_xlsx_response(response)


class SoftWareReportView(PermissionRoleRequiredMixin, SignupMixin, ListView):
    permission_required = DEVICES_PERMISSION
    http_method_names = ["get", "post"]
    template_name = "dashboard/software-report.html"
    model = InstalledSoftwareOrganisationIndividual
    context_object_name = "apps"
    paginate_by = 20
    paginator_class = SafePaginator

    def __init__(self):
        self.organisation = None
        self.allowed_ordering = [
            "product", "-product",
            "version", "-version",
            "vendor", "-vendor",
            "is_vulnerable", "-is_vulnerable",
            "freq", "-freq"
        ]
        super().__init__()

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        self.organisation = get_organisations(request.user, self.kwargs.get("org_id"))
        if not self.organisation:
            raise PermissionDenied()
        return super().dispatch(request, *args, **kwargs)

    def get_ordering(self):
        ordering = self.request.GET.get("ordering")
        if ordering:
            if ordering not in self.allowed_ordering:
                raise PermissionDenied
            return ordering

    def get_queryset(self):
        vulnerable = self.request.GET.get("vulnerable")
        search = self.request.GET.get("search")

        filters = {}
        if vulnerable is not None:
            filters["is_vulnerable"] = vulnerable == 'true'
        if search is not None:
            search_filter = Q(product__icontains=search) | Q(version__icontains=search) | Q(vendor__icontains=search)
        else:
            search_filter = None

        queryset = super().get_queryset().filter(
            organisation=self.organisation
        ).filter(
            **filters
        )
        if search_filter:
            queryset = queryset.filter(search_filter)

        return queryset

    def get_context_data(self, *, object_list=None, **kwargs):

        context = {"organisation": self.organisation}

        vulnerable = self.request.GET.get("vulnerable")

        if vulnerable in ["true", "false"]:
            filter_value = "vulnerable" if vulnerable == "true" else "safe"
            context["csv_xlxs_query_parameters"] = urlencode({"filter": filter_value})

        try:
            context.update(super().get_context_data())
            app_list = InstalledSoftwareOrganisationIndividual.objects.filter(organisation=self.organisation)
            total_count, safe_count, vulnerable_count = aggregate_installed_software_counts(app_list)
        except OperationalError:
            context['operational_error'] = True
        else:
            context["installed_software_count"] = total_count
            context["vulnerable_software_count"] = vulnerable_count
            context["safe_software_count"] = safe_count
            context["param__vulnerable"] = self.request.GET.get("vulnerable")
            context["param__ordering"] = self.get_ordering()
            context["param__search"] = self.request.GET.get("search")

            query = self.request.GET.dict()
            if "page" in query:
                query.pop("page")

            context.update({"params_query": urlencode(query)})
        return context
    def get(self, request, *args, **kwargs):
        if not request.headers.get("x-requested-with") == "XMLHttpRequest":
            return super().get(request, *args, **kwargs)

        software_id = request.GET.get("software")
        if not software_id:
            return JsonResponse({"error": "Software ID is required"}, status=400)

        # Parse the composite ID from InstalledSoftwareSummary
        opswat_ids, regular_ids = parse_composite_ids(software_id)

        data = []

        if regular_ids:
            # Regular software query
            user_installs = AppOSInstalledSoftware.objects.filter(
                software_id__in=regular_ids,
                report__app_install__app_user__organisation=self.organisation,
                report__app_install__app_user__active=True,
                report__app_install__inactive=False
            ).distinct("report__app_install").prefetch_related("report__app_install")

            data.extend([
                {
                    "hostname": obj.report.app_install.hostname,
                    "date_installed": obj.get_date_installed(),
                    "device_url": obj.report.app_install.url()
                } for obj in user_installs
            ])

        if opswat_ids:
            # OPSWAT software query
            installed_products = InstalledProduct.objects.filter(
                product_versions__id__in=opswat_ids,
                app_install__app_user__organisation=self.organisation,
                app_install__app_user__active=True,
                app_install__inactive=False
            ).prefetch_related("app_install")
            data.extend([
                {
                    "hostname": installed_product.app_install.hostname,
                    "date_installed": installed_product.get_date_installed(),
                    "device_url": installed_product.app_install.url()
                } for installed_product in installed_products
            ])

        return JsonResponse({"hosts": data})


class EnableCertificationView(SignupMixin, View):
    """
    Enables certification with given type for given organisation in case of it's not enabled yet.
    Only for IASME CB partners.
    """
    http_method_names = ["post"]

    @staticmethod
    def post(request, org_id, cert_type, version_pk=None):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()
        redirect = request.POST.get("redirect_to")
        cert_type = turn_cert_type_into_int(cert_type)
        if is_ce_plus_and_not_iasme(request.user, cert_type):
            raise PermissionDenied("Only IASME users may add CE+ certification")
        elif cert_type not in CERTIFICATES.keys():
            messages.add_message(request, messages.ERROR, gettext_lazy("Incorrect certification type"))
            return HttpResponseRedirect("/")
        elif organisation.get_latest_certificate(cert_type, version_pk=version_pk):
            messages.add_message(
                request,
                messages.ERROR,
                "{0} is already enabled for this organisation".format(CERTIFICATES.get(cert_type))
            )
            return HttpResponseRedirect("/")
        else:
            cert = organisation.certifications.create(
                version=get_latest_version(type=cert_type)
            )
            if not redirect:
                messages.add_message(
                    request,
                    messages.SUCCESS,
                    "{0} is successfully enabled".format(CERTIFICATES.get(cert_type))
                )

        return HttpResponseRedirect(cert.url) if redirect else HttpResponseRedirect("/")


class CertificationPausedView(SignupMixin, View):
    template_name = 'dashboard/certification/paused.html'

    def get(self, request, org_id, certification_type):
        self.certification_type = int(certification_type)
        user = request.user
        self.organisation = get_organisations(user, org_id)
        if not self.organisation:
            raise PermissionDenied()
        certification = self.organisation.get_latest_certificate(type=self.certification_type)
        try:
            subscription = self.organisation.subscriptions.find_certificate_by_type(
                certificate_type=self.certification_type)
        except ObjectDoesNotExist:
            raise PermissionDenied()
        else:
            if (certification.certified or certification.expired) or (subscription and not subscription.paused):
                return HttpResponseRedirect(certification.url)
            else:
                return render(request, self.template_name, {
                    "type": self.certification_type,
                    "name": CERTIFICATES.get(self.certification_type),
                    "organisation": self.organisation
                })


class BuyCertificationAddonView(SignupMixin, PaymentMixin, View):
    template_name = 'dashboard/certification/addon.html'

    def __init__(self, *args, **kwargs):
        self.organisation = None
        self.certification_type = None
        super().__init__(*args, **kwargs)

    def update_context_with_v4_plans(self, request, context):
        """
        Updates view context with Direct Customers V4 plans.
        :param context: view context
        :type context: dict
        :return: updated context
        :rtype: dict
        """
        if self.organisation.is_direct_customer_v4:
            # for direct customers v4 pass real plans objects to the template
            if self.certification_type == CYBER_ESSENTIALS:
                if self.organisation.has_software_subscription:
                    if self.organisation.pricing_band == self.organisation.DIRECT_CUSTOMER_V4_V5_PRICING_ANNUAL:
                        context["plans"] = [DIRECT_CUSTOMER_V5_APP_CE_ANNUAL_2024]
                    else:
                        context["plans"] = [DIRECT_CUSTOMER_V5_APP_CE_MONTHLY_2024]
                else:
                    if self.organisation.pricing_band == self.organisation.DIRECT_CUSTOMER_V4_V5_PRICING_ANNUAL:
                        context["plans"] = [DIRECT_CUSTOMER_V4_CE_ANNUAL_2024]
                    else:
                        context["plans"] = [DIRECT_CUSTOMER_V4_CE_MONTHLY_2024]
            elif self.certification_type == CYBER_ESSENTIALS_PLUS:
                if self.organisation.has_software_subscription:
                    if self.organisation.pricing_band == self.organisation.DIRECT_CUSTOMER_V4_V5_PRICING_ANNUAL:
                        context["plans"] = [DIRECT_CUSTOMER_V4_APP_CEP_ANNUAL]
                    else:
                        context["plans"] = [DIRECT_CUSTOMER_V4_APP_CEP_MONTHLY]
                else:
                    if self.organisation.pricing_band == self.organisation.DIRECT_CUSTOMER_V4_V5_PRICING_ANNUAL:
                        context["plans"] = [DIRECT_CUSTOMER_V4_CEP_ANNUAL]
                    else:
                        context["plans"] = [DIRECT_CUSTOMER_V4_CEP_MONTHLY]
            elif self.certification_type == GDPR:
                if self.organisation.has_software_subscription:
                    if self.organisation.pricing_band == self.organisation.DIRECT_CUSTOMER_V4_V5_PRICING_ANNUAL:
                        context["plans"] = [DIRECT_CUSTOMER_V4_APP_GDPR_ANNUAL]
                        if not self.organisation.has_ce_certification:
                            context["plans"].append(DIRECT_CUSTOMER_V5_APP_CE_ANNUAL_2024)
                    else:
                        context["plans"] = [DIRECT_CUSTOMER_V4_APP_GDPR_MONTHLY]
                        if not self.organisation.has_ce_certification:
                            context["plans"].append(DIRECT_CUSTOMER_V5_APP_CE_MONTHLY_2024)
                else:
                    if self.organisation.pricing_band == self.organisation.DIRECT_CUSTOMER_V4_V5_PRICING_ANNUAL:
                        context["plans"] = [DIRECT_CUSTOMER_V4_GDPR_ANNUAL]
                        if not self.organisation.has_ce_certification:
                            context["plans"].append(DIRECT_CUSTOMER_V4_CE_ANNUAL_2024)
                    else:
                        context["plans"] = [DIRECT_CUSTOMER_V4_GDPR_MONTHLY]
                        if not self.organisation.has_ce_certification:
                            context["plans"].append(DIRECT_CUSTOMER_V4_CE_MONTHLY_2024)

            # change IDs to plan objects
            context["plans"] = {plan_id: get_plan_object_from_cache(plan_id) for plan_id in context["plans"]}
        return context

    def get(self, request, org_id, certification_type):
        self.certification_type = int(certification_type)
        user = request.user
        self.organisation = get_organisations(user, org_id)
        if not self.organisation:
            raise PermissionDenied()
        certification = self.organisation.get_latest_certificate(type=self.certification_type)
        if certification:
            return HttpResponseRedirect(certification.url)
        else:
            certification_type = CertificateType.objects.get(type=self.certification_type)
            return render(request, self.template_name, self.update_context_with_v4_plans(request, {
                "type": self.certification_type,
                "certification_type": certification_type,
                "name": CERTIFICATES.get(self.certification_type),
                "organisation": self.organisation,
                'stripe_key': settings.STRIPE_KEY,
            }))

    @classmethod
    def post(cls, request, org_id, certification_type):
        certification_type = int(certification_type)
        user = request.user

        data = json.loads(request.body.decode("utf-8"))
        plans = [plan_id.strip() for plan_id in data.get('billing_plan').split(",") if plan_id]
        for plan in plans:
            if plan not in CERT_PLANS_IDS + DIRECT_CUSTOMER_V4_V5_PLANS:
                raise ChargebeeError("Invalid plan id")

        organisation = get_organisations(user, org_id)
        if not organisation:
            raise PermissionDenied()

        # if a certificate already exists, just redirect to the page
        certification = organisation.get_latest_certificate(type=certification_type)
        if certification:
            return HttpResponseRedirect(certification.url)

        cert_plan_id = plans[0]
        # special case when org does not have CE certification yet and chose GDPR
        if certification_type == GDPR and not organisation.is_direct_customer_v4:
            if not organisation.has_ce_certification:
                if cert_plan_id not in CERT_MONTHLY_PLANS_IDS:
                    cert_plan_id = CE_GDPR_PLAN_ANNUAL_ID
                else:
                    cert_plan_id = CE_GDPR_PLAN_MONTHLY_ID

        customer_id = organisation.customer.customer_id
        # create or confirm a payment intent
        result = cls._create_or_confirm_payment_intent(
            organisation, cert_plan_id, customer_id, payment_intent_id=data.get('payment_intent_id')
        )
        intent = result.get('intent')
        if result.get('error'):
            return JsonResponse({'error': result.get('error')})

        # return context data necessary for the user to perform the action in the front-end,
        # or skip if no action needs to be taken
        if cls._payment_requires_action(intent):
            context = {
                'requires_action': True,
                'payment_intent_client_secret': intent.client_secret
            }
            return JsonResponse(context)

        # if the payment does not require capture is because some error occurred
        if not cls._payment_requires_capture(intent):
            return JsonResponse({'error': intent.status})

        # if all is ok, then create new subscription and certification
        if certification_type == CYBER_ESSENTIALS:
            try:
                if organisation.is_billable:
                    organisation.customer.add_certificate_plan(cert_plan_id, intent_id=intent.id)
            except ChargebeeError as error:
                return JsonResponse({'error': str(error)})
            else:
                organisation.certifications.create(
                    version=get_latest_version(type=CYBER_ESSENTIALS)
                )
                return JsonResponse({'url': organisation.ce_certification.url})
        elif certification_type == CYBER_ESSENTIALS_PLUS:
            try:
                if organisation.is_billable:
                    organisation.customer.add_certificate_plan(cert_plan_id, intent_id=intent.id)
            except ChargebeeError as error:
                return JsonResponse({'error': str(error)})
            else:
                organisation.certifications.create(
                    version=get_latest_version(type=CYBER_ESSENTIALS_PLUS)
                )
                return JsonResponse({'url': organisation.cep_certification.url})
        elif certification_type == GDPR:
            try:
                if organisation.is_billable:
                    if not organisation.has_ce_certification and organisation.is_direct_customer_v4:
                        for plan in plans:
                            organisation.customer.add_certificate_plan(plan, intent_id=intent.id)
                    else:
                        organisation.customer.add_gdpr_plan(cert_plan_id, intent_id=intent.id)
            except ChargebeeError as error:
                return JsonResponse({'error': str(error)})
            else:
                organisation.certifications.create(
                    version=get_latest_version(type=GDPR)
                )
                # add also CE if needed
                if not organisation.has_ce_certification and organisation.is_direct_customer_v4:
                    organisation.certifications.create(
                        version=get_latest_version(type=CYBER_ESSENTIALS)
                    )
                return JsonResponse({'url': organisation.gdpr_certification.url})
        else:
            raise Http404()


class UpdateToSoftwarePlanView(SignupMixin, View):
    """
    For Direct Customers V4
    """
    @staticmethod
    def post(request, org_id):
        user = request.user

        organisation = get_organisations(user, org_id)
        if not organisation:
            raise PermissionDenied()

        if not organisation.is_billable:
            messages.add_message(request, messages.ERROR, "Something went wrong. Please contact customer support.")
            return HttpResponseRedirect("")

        if not organisation.is_direct_customer_v4:
            messages.add_message(request, messages.ERROR, "You are not allowed to update to these plans")
            return HttpResponseRedirect("")

        if organisation.has_software_subscription:
            messages.add_message(request, messages.ERROR, "You already have software subscription")
            return HttpResponseRedirect("")

        old_plans = organisation.get_plans_for_cap_upgrade(request)

        for subscription in organisation.customer.subscriptions.all():
            subscription.update_plan_and_quantity(old_plans[subscription.plan_id]["id"])

        organisation.software_support = True
        organisation.save()

        messages.add_message(request, messages.SUCCESS, "You successfully upgraded to CyberSmart Active Protect")

        return HttpResponseRedirect(request.headers.get('referer', '/'))


class BuySoftwareAddonView(SignupMixin, View):
    @staticmethod
    def post(request, org_id):
        user = request.user
        software_plan = request.POST.get('software_plan')
        if software_plan not in ADDONS_IDS:
            raise ChargebeeError("Invalid plan id")

        organisation = get_organisations(user, org_id)
        if not organisation:
            raise PermissionDenied()
        if not organisation.is_billable:
            messages.add_message(request, messages.ERROR, 'Something went wrong. Please contact customer support.')
            return HttpResponseRedirect('')
        # create subscription
        try:
            # update chargebee customer meta data with addon id
            update_customer_meta_data(organisation.customer.customer_id, {'addon_id': software_plan})
        except ChargebeeError as error:
            messages.add_message(request, messages.ERROR, str(error))
            return HttpResponseRedirect('')
        else:
            # set enrollment type to self enrollment
            organisation.bulk_install = False
            # change number of staff if needed
            if organisation.size == Organisation.ORGANISATION_SIZE_1:
                organisation.size = organisation.ORGANISATION_SIZE_5_9
            organisation.save()
            return HttpResponseRedirect(request.headers.get('referer', '/'))


class BillingView(SignupMixin, FormView):
    template_name = 'dashboard/billing.html'
    form_class = ChangePlanForm

    def get(self, request, *args, **kwargs):
        if request.headers.get("x-requested-with") == "XMLHttpRequest" and self.organisation.is_billable:
            portal_session = self.organisation.customer.create_portal_session()
            return JsonResponse(portal_session)

        return self.render_to_response(self.get_context_data())

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        self.organisation = get_organisations(request.user, self.kwargs.get('org_id'))
        if not self.organisation:
            raise PermissionDenied()

        if not self.organisation.is_billable:
            messages.add_message(
                request, messages.WARNING, 'You can\'t access this page since you are not using subscription'
            )
            return HttpResponseRedirect(
                reverse('dashboard:manage-organisation', kwargs={'org_id': self.organisation.secure_id})
            )
        return super(BillingView, self).dispatch(request, *args, **kwargs)

    def get_success_url(self):
        return reverse('dashboard:manage-organisation-billing', kwargs={'org_id': self.organisation.secure_id})

    def get_context_data(self):
        ps = ProjectSettings.objects.get()

        context = super(BillingView, self).get_context_data()
        context.update({
            'chargebee_site_name': settings.CHARGEBEE.get('SITE_NAME'),
            'organisation': self.organisation,
            'chargebee_plans': json.dumps(get_plans()),
            'TAX_ABBR': ps.tax_abbr,
            'live_subscriptions': self.organisation.customer.subscriptions.live_subscriptions
        })
        if self.organisation.has_v6_pricing_band and self.organisation.has_subscription:
            plan_id = self.organisation.customer.subscriptions.first().plan_id
            plan_dict = get_plan_object_from_cache(plan_id)
            v6_price = plan_dict['price'] / 100

            v6_price_formatted = '{}{:.2f}'.format(get_currency_sign(plan_id), float(v6_price))
            context.update({
                'v6_price': v6_price_formatted,
                'v6_free_quantity': plan_dict.get('free_quantity'),
            })
        return context

    def get_initial(self):
        initial = super(BillingView, self).get_initial()
        if self.organisation.has_certification_subscription:
            initial['certification_plan'] = self.organisation.customer.current_certification_plan.plan_id
        return initial

    def post(self, request, *args, **kwargs):
        """
        Handles POST requests, instantiating a form instance with the passed
        POST variables and then checked for validity.
        """
        form = self.get_form()
        if form.is_valid():
            return self.form_valid(form)
        else:
            return self.form_invalid(form)

    def get_form_kwargs(self):
        kwargs = super(BillingView, self).get_form_kwargs()
        kwargs.update({'organisation': self.organisation})
        return kwargs

    def form_valid(self, form):
        try:
            form.save()
        except ChargebeeError as error:
            messages.add_message(self.request, messages.ERROR, error)
        return super(BillingView, self).form_valid(form)


class BackToSurveyView(SignupMixin, View):

    @staticmethod
    def get(request, org_id, certification_type, version_pk=None):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()
        cert_standard = certification_type
        if not cert_standard or not cert_standard.isdigit():
            raise Http404()
        certification = organisation.get_latest_certificate(type=cert_standard, version_pk=version_pk)
        if not certification:
            raise Http404()
        if certification.not_started:
            raise Http404()
        if not certification.version.declaration:
            return HttpResponseRedirect(certification.url)
        try:
            survey_declaration = SurveyDeclaration.objects.get(survey=certification.survey)
        except SurveyDeclaration.DoesNotExist:
            raise Http404()

        survey_declaration.ready_to_be_signed = False
        survey_declaration.save()
        return HttpResponseRedirect(certification.url)


class DeviceCSVReportView(CSVReportMixin, SignupMixin, View):
    """
    Generates CSV report with failed and passed checks for all organisation devices.
    """

    def get_file_name(self):
        return "{0}_{1}".format(self.organisation.name.replace(" ", "_"), timezone.now().strftime("%b_%d_%Y"))

    def get_field_names(self) -> list[str]:
        """
        Returns field names that will be used as table headers in csv file.
        :return: table headers
        :rtype: list
        """
        if self.only_failing_checks:
            return [
                "Check", "Host", "Name", "Email", "Device ID", "Serial", "App Version", "OS Release", "OS Version",
                "OS Name", "Last User", "Domain", "Last Check In"
            ]
        elif self.organisation.bulk_install:
            # for bulk enrollment
            return [
                "Host", "Device ID", "Serial", "App Version", "OS Release", "OS Version", "OS Name", "Check",
                "Last User", "Domain", "Result", "Reason", "Last Check In"
            ]
        else:
            # for individual enrollment
            return [
                "Name", "Email", "Host", "Device ID", "Serial", "App Version", "OS Release", "OS Version", "OS Name",
                "Check", "Last User", "Domain", "Result", "Reason", "Last Check In"
            ]

    def get_value_for_field(self, field: str, check: CheckResult, passed_check: bool) -> str:
        """
        Returns value for given CheckResult field.
        :param field: CheckResult field
        :param check: CheckResult instance
        :param passed_check: represents if given check is passing or not
        """
        reason = 'Validated' if passed_check else 'Issues'
        # update reason in case it was resolved by user
        if check['permanent_fix']:
            reason = 'Permanently resolved'
        elif not check['response'] and check['fixed']:
            reason = 'Manually resolved'
        return {
            "Host": escape_csv(check["report__app_install__hostname"]),
            "Device ID": escape_csv(check["report__app_install__device_id"]),
            "Serial": escape_csv(check["report__app_install__serial_number"]),
            "App Version": escape_csv(check["report__app_install__app_version"]),
            "OS Release": escape_csv(check["report__app_install__release"]),
            "OS Version": escape_csv(check["report__app_install__os_release"]),
            "OS Name": escape_csv(check["report__app_install__os__title"]),
            "Check": escape_csv(check["app_check__title"]),
            "Last User": escape_csv(check["report__username"]),
            "Domain": escape_csv(check["report__domain"]),
            "Result": "Pass" if passed_check else "Fail",
            "Reason": reason,
            "Last Check In": escape_csv(check["report__created"]),
            "Name": escape_csv("{0} {1}".format(
                check["report__app_install__app_user__first_name"],
                check["report__app_install__app_user__last_name"]
            )),
            "Email": escape_csv(check["report__app_install__app_user__email"])
        }[field]

    def __write_checks_to_csv(self, checks, writer, passed):
        """
        Writes passed checks result to csv writer.
        :param checks: failed or passed checks
        :type checks: appusers.checks.CheckResult
        :param writer: csv writer
        :type writer: _csv.writer
        :param passed: True if checks are passed and False if checks are failed
        :type passed: bool
        :return: nothing
        :rtype: None
        """
        col_names = self.get_field_names()

        for check in checks:
            data = {}
            for col_name in col_names:
                data[col_name] = self.get_value_for_field(col_name, check, passed_check=passed)
            writer.writerow(data)

    def get(self, request, org_id):
        self.organisation = get_organisations(request.user, org_id)
        if not self.organisation:
            raise PermissionDenied()

        self.only_failing_checks = bool(request.GET.get("only_failing_checks"))

        response = self.get_csv_http_response()
        writer = self.get_csv_writer(response)

        # get passed and failed checks
        values = (
            "pk", "app_check__pk", "report__app_install__hostname", "report__app_install__device_id",
            "report__app_install__os__title", "report__app_install__app_user__first_name",
            "report__app_install__app_user__last_name", "report__app_install__app_user__uuid",
            "report__app_install__os__id", "report__app_install__serial_number", "report__app_install__caption",
            "report__app_install__app_user__email", "report__app_install__app_version",
            "report__app_install__release", "app_check__title", "report__app_install__pk", "report__username",
            "report__domain", "report__created", "response", "fixed", "permanent_fix",
            "report__app_install__os_release"
        )
        passed_checks, failed_checks = get_applications_responses(
            org_pk=self.organisation.pk,
            order=('app_check__pk', 'report__app_install__id', '-report__modified', '-modified'),
            distinct=('app_check__pk', 'report__app_install__id'),
            fail_values=values,
            pass_values=values,
            report_filters={'app_install__app_user__active': True}
        )

        if not self.only_failing_checks:
            # write passed checks
            self.__write_checks_to_csv(checks=passed_checks, writer=writer, passed=True)
        # write failed responses
        self.__write_checks_to_csv(checks=failed_checks, writer=writer, passed=False)

        return response


class SoftwareCSVReportView(CSVReportMixin, SignupMixin, View):
    """
    Generates CSV report with software detected for the organisation, and its vulnerability status.
    """

    def get_file_name(self):
        return "vulnerable_software_{0}_{1}".format(
            self.organisation.name.replace(" ", "_"),
            timezone.now().strftime("%b_%d_%Y")
        )

    def get_field_names(self):
        """
        Returns field names that will be used as table headers in csv file.
        :return: table headers
        :rtype: list
        """
        return ["Status", "Name", "Version", "Vendor", "Found on", "Devices", "CVEs"]

    @staticmethod
    def _get_device_display_name(device: AppInstall) -> str:
        """
        Returns a formatted string for the device display name.
        """
        return f"{device.hostname} - {device.device_id}"

    def __get_found_on_devices(self, source: str, source_id: str, is_vulnerable_filter: dict) -> list:
        """
        Returns list of devices where given software is found.
        For OPSWAT sources, uses InstalledProductVersion; for regular sources, uses InstalledSoftwareAppInstallIndividual.
        """
        if source == OPSWAT_SOURCE:
            # For OPSWAT sources, source_id is the ProductVersion.id
            found_on_installs = InstalledProductVersion.objects.filter(
                product_version_id=source_id,
                installed_product__app_install__app_user__organisation=self.organisation,
                installed_product__app_install__inactive=False
            ).select_related('installed_product__app_install')
            return [self._get_device_display_name(install.installed_product.app_install) for install in found_on_installs]
        else:
            # For regular sources, use the original approach
            found_on_installs = InstalledSoftwareAppInstallIndividual.objects.filter(
                source=source,
                source_id=source_id,
                app_install__app_user__organisation=self.organisation,
                app_install__inactive=False,
                **is_vulnerable_filter
            )
            return [self._get_device_display_name(software.app_install) for software in found_on_installs]

    @staticmethod
    def __get_cves_for_software(id: str) -> list:
        """
        Returns list of CVEs for given software.
        """
        opswat_ids, regular_ids = parse_composite_ids(id)

        # Get all OPSWAT CVEs in bulk
        opswat_cves = list(ProductVersion.objects.filter(
            id__in=opswat_ids,
            cves__isnull=False
        ).values_list("cves__cve_id", flat=True))

        # Get all regular scanner CVEs in bulk
        regular_cves = list(chain.from_iterable(
            SoftwarePackageCVEs.objects.filter(
                software_id__in=regular_ids
            ).values_list("cves", flat=True)
        ))

        return opswat_cves + regular_cves

    def _write_to_csv(self, software, writer, is_vulnerable_filter):
        for package in software:
            data = {
                "Status": "Vulnerable" if package.is_vulnerable else "No known vulnerabilities",
                "Name": escape_csv(package.product),
                "Version": escape_csv(package.version),
                "Vendor": escape_csv(package.vendor or "Unknown"),
                "Found on": escape_csv(f"{package.device_count} device{'s' if package.device_count != 1 else ''}"),
                "Devices": escape_csv(", ".join(self.__get_found_on_devices(package.source, package.source_id, is_vulnerable_filter))) if package.device_count else "",
                "CVEs": escape_csv(", ".join(self.__get_cves_for_software(package.id)))
            }
            writer.writerow(data)

    def get_is_vulnerable_filter(self):
        query_filter = self.request.GET.get("filter", "").lower()
        filters = {}
        if query_filter == "vulnerable":
            filters["is_vulnerable"] = True
        elif query_filter == "safe":
            filters["is_vulnerable"] = False
        return filters

    def get_queryset(self, is_vulerable_filter) -> QuerySet:
        filters = {"organisation": self.organisation}
        filters.update(is_vulerable_filter)
        return InstalledSoftwareOrganisationIndividual.objects.filter(**filters)

    def get(self, request, org_id):
        self.organisation = get_organisations(request.user, org_id)
        if not self.organisation:
            raise PermissionDenied()

        is_vulnerable_filter = self.get_is_vulnerable_filter()
        software = self.get_queryset(is_vulnerable_filter)
        response = self.get_csv_http_response()
        writer = self.get_csv_writer(response)
        self._write_to_csv(software, writer, is_vulnerable_filter)

        # record download usage
        file_format = "xlsx" if request.GET.get("format") == "xlsx" else "csv"
        report_name = "vulnerable_software"
        record_report_download_usage.delay(
            entity_pk=self.organisation.pk,
            level="organisation",
            report_name=report_name,
            file_format=file_format
        )

        return self.csv_or_xlsx_response(response)


class DashboardCSVDownloadView(CSVReportMixin, SignupMixin, View):
    """
    Generates CSV for an organisation with the exact data as the dashboard device/users table.
    """

    def get_file_name(self):
        return "{0}_{1}".format(self.organisation.name.replace(" ", "_"), timezone.now().strftime("%b_%d_%Y"))

    def get_devices(self):
        """
        Returns organisation app installs with prefetched essential data.
        :return: app install queryset
        :rtype: AppInstall queryset
        """
        return self.organisation.app_installs.filter(
            app_user__active=True
        ).select_related(
            'os'
        ).select_related(
            'analytics'
        ).prefetch_related(
            Prefetch('os_users', AppInstallOSUser.objects.all().order_by("created"))
        ).select_related(
            'app_user'
        )

    def get_app_users(self):
        """
        Returns organisation app users with prefetched essential data.
        :return: app users queryset
        :rtype: AppUser queryset
        """
        return AppUser.objects.filter(
            organisation=self.organisation, active=True
        ).select_related("analytics").prefetch_related(
            Prefetch("installs", AppInstall.objects.all().select_related("os").prefetch_related(
                Prefetch("reports", AppReport.objects.filter(total_responses__isnull=False))
            ).select_related("analytics"))
        ).prefetch_related(
            Prefetch("installs__os_users", AppInstallOSUser.objects.all())
        ).prefetch_related(
            Prefetch("installs__policies", AppInstallPolicyAgreement.objects.filter(
                agreed_date__isnull=False,
                version__policy__active=True,
                version__active=True,
                version__main=True
            ).distinct("version__policy"))
        ).prefetch_related(
            Prefetch("installs__reports", AppReport.objects.filter(total_responses__isnull=False).order_by(
                "app_install", "-modified"
            ).distinct("app_install"))
        ).distinct("pk")

    def get_field_names(self):
        """
        Returns field names that will be used as table headers in csv file.
        :return: table headers
        :rtype: list
        """
        self.policies_count = self.organisation.policies.filter(active=True).count()
        if self.organisation.is_uba_type:
            headers = ["Email", "Device", "Model", "Status", "Last Checked In", "Inactive"]
            policy_header_index = 4
        elif self.organisation.is_bulk_enrollment_type:
            headers = [
                "Host Name", "Model", "Last User", "Operating System", "Serial & Device ID",
                "App Version", "Status", "Last Checked In", "Inactive"
            ]
            policy_header_index = 7
        else:
            # for individual enrollment
            headers = ["Name", "Email", "Device", "Model", "Status", "Last Checked In", "Inactive"]
            policy_header_index = 5
        # if org has policies, then add the column
        if self.policies_count:
            headers.insert(policy_header_index, 'Policies')
        return headers

    @staticmethod
    def _get_device_cell_text(install):
        """ Helper method to get the Device cell data text """
        return f'{install.hostname} [{install.display_os()}] (app v{install.get_app_version()})'

    @staticmethod
    def _get_status_cell_text(install):
        """ Helper method to get the Status cell data text """
        return f'{install.analytics.latest_pass_percentage}%' \
            if hasattr(install, 'analytics') else 'Install pending'

    @staticmethod
    def _get_last_checkin_cell_text(install):
        """ Helper method to get the Last Checked In cell data text """
        return f'{install.last_check_in.strftime("%d %b %Y, %I:%M %p")}'

    @staticmethod
    def _get_inactive_text(install):
        """ Helper method to get the Inactive status of an app install in a friendly manner """
        return 'yes' if install.inactive else 'no'

    def _write_self_enrollment_row_data(self, writer, app_user, install=None):
        """ Write data into a row """
        data = {
            "Name": escape_csv(get_full_name_or_email(app_user)),
            "Email": escape_csv(app_user.email),
            "Device": 'Awaiting install',
            "Model": '',
            "Status": '',
            "Last Checked In": '',
            "Inactive": '',
        }
        if install:
            data['Device'] = escape_csv(self._get_device_cell_text(install))
            data['Model'] = escape_csv(install.machine_model)
            data['Status'] = escape_csv(self._get_status_cell_text(install))
            data['Last Checked In'] = escape_csv(self._get_last_checkin_cell_text(install))
            data['Inactive'] = escape_csv(self._get_inactive_text(install))

        # if org has policies, then add the data
        if self.policies_count:
            data['Policies'] = escape_csv(f'{app_user.agreed_policies_count} / {self.policies_count}')

        writer.writerow(data)

    def _write_uba_row_data(self, writer, app_user, install=None):
        """ Write data into a row """
        policies = []

        # If the main app user is not attached to an install, then do not show this row.
        # This is so that we don't render an empty "Unassigned device" row.
        is_main_app_user = app_user == self.organisation.main_app_user
        if is_main_app_user and not install:
            return

        data = {
            "Email": escape_csv(
                "Unassigned device" if is_main_app_user else app_user.email
            ),
            "Device": 'Awaiting install',
            "Model": '',
            "Status": '',
            "Last Checked In": '',
            "Inactive": '',
        }
        if install:
            data['Device'] = escape_csv(self._get_device_cell_text(install))
            data['Model'] = escape_csv(install.machine_model)
            data['Status'] = escape_csv(self._get_status_cell_text(install))
            data['Last Checked In'] = escape_csv(self._get_last_checkin_cell_text(install))
            data['Inactive'] = escape_csv(self._get_inactive_text(install))

        # if org has policies, then add the data
        if self.policies_count and install:
            if is_main_app_user:
                os_users = install.os_users.all()
                if os_users:
                    for os_user in os_users:
                        policies.append(
                            escape_csv(f'{os_user.agreed_policies_count} / {self.policies_count}')
                        )
                else:
                    policies.append(escape_csv(f'{install.policies.count()} / {self.policies_count}'))
            else:
                policies.append(escape_csv(f'{app_user.agreed_policies_count} / {self.policies_count}'))

        # if org has policies, then add the data
        if self.policies_count:
            data['Policies'] = escape_csv("\n".join(policies))

        writer.writerow(data)

    def __write_to_csv_self_enrollment_or_uba(self, writer):
        """
        Writes data to csv writer if self enrollment type or UBA type.
        :return: nothing
        :rtype: None
        """
        # row format depends if it is UBA or self enrollment
        if self.organisation.is_uba_type:
            row_writer = self._write_uba_row_data
        else:
            row_writer = self._write_self_enrollment_row_data

        app_users_group = self.get_app_users()
        for app_user in app_users_group:
            installs = app_user.raw_installed_devices.all()
            if not installs:
                row_writer(writer, app_user)
            else:
                for install in installs:
                    row_writer(writer, app_user, install)

    def __write_to_csv_bulk_enrollment(self, writer):
        """
        Writes data to csv writer.
        :return: nothing
        :rtype: None
        """
        devices = self.get_devices()
        for app_install in devices:
            user_type = 'user'
            if app_install.latest_local_user and app_install.latest_local_user.is_admin_account:
                user_type = 'admin'
            if app_install.caption == 'Unknown' or app_install.caption == '' or not app_install.caption:
                os = app_install.os.title
            else:
                os = app_install.caption
            data = {
                "Host Name": escape_csv(app_install.hostname),
                "Model": escape_csv(app_install.machine_model),
                "Last User": escape_csv(f'Local {user_type} {app_install.last_login_username}'),
                "Operating System": escape_csv(os),
                "Serial & Device ID": escape_csv(f'{app_install.serial_number} {app_install.device_id}'),
                "App Version": escape_csv(app_install.app_version),
                "Status": escape_csv(self._get_status_cell_text(app_install)),
                "Last Checked In": escape_csv(self._get_last_checkin_cell_text(app_install)),
                "Inactive": escape_csv(self._get_inactive_text(app_install))
            }
            # if org has policies, then add the data
            if self.policies_count:
                os_users = app_install.os_users.all()
                policies = []
                if os_users:
                    for os_user in os_users:
                        policies.append(
                            escape_csv(f'{os_user.agreed_policies_count} / {self.policies_count}')
                        )
                else:
                    policies.append(escape_csv(f'{app_install.policies.count()} / {self.policies_count}'))
                data['Policies'] = escape_csv("\n".join(policies))

            writer.writerow(data)

    def get(self, request, org_id):
        self.organisation = get_organisations(request.user, org_id)
        if not self.organisation:
            raise PermissionDenied()

        response = self.get_csv_http_response()
        writer = self.get_csv_writer(response)

        if self.organisation.is_uba_type or self.organisation.is_self_enrollment_type:
            self.__write_to_csv_self_enrollment_or_uba(writer)
        else:
            self.__write_to_csv_bulk_enrollment(writer)

        # record download usage
        file_format = "xlsx" if request.GET.get("format") == "xlsx" else "csv"
        report_name = "org-dashboard"
        record_report_download_usage.delay(
            entity_pk=self.organisation.pk,
            level="organisation",
            report_name=report_name,
            file_format=file_format
        )

        return self.csv_or_xlsx_response(response)


class ApprovedDomainsView(SignupMixin, FormView):
    template_name = 'dashboard/approved_domains.html'
    form_class = ApprovedDomainForm

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        self.org = get_organisations(request.user, kwargs.get('org_id'))
        if not self.org:
            raise PermissionDenied()
        if not self.org.bulk_install:
            return HttpResponseRedirect(self.org.url)
        return super(ApprovedDomainsView, self).dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super(ApprovedDomainsView, self).get_context_data(**kwargs)
        organisation = get_organisations(self.request.user, self.kwargs['org_id'])
        if not organisation:
            raise PermissionDenied()
        context['organisation'] = organisation
        context['domains'] = organisation.approved_domains.all()
        return context

    def get_success_url(self):
        return reverse_lazy('dashboard:approved-domains', kwargs={'org_id': self.kwargs['org_id']})

    def get_form_kwargs(self):
        """Return the keyword arguments for instantiating the form."""
        kwargs = super().get_form_kwargs()
        if self.request.method in ("POST", "PUT"):
            data = self.request.POST.copy()
            data.update({
                "organisation": self.org.id
            })
            kwargs['data'] = data
        return kwargs

    def form_valid(self, form):
        organisation = get_organisations(self.request.user, self.kwargs['org_id'])
        if not organisation:
            raise PermissionDenied()
        domain = form.cleaned_data['domain']
        if is_generic_domain(domain):
            messages.add_message(
                self.request, messages.ERROR,
                gettext_lazy("This generic domain is not allowed. Make sure to use your company's domain."
                             " If you do not have a company domain, contact us on live chat."))
        else:
            organisation.approved_domains.create(domain=handle_user_input(domain))
            messages.add_message(self.request, messages.SUCCESS, gettext_lazy('New approved domain successfully added'))
        return super(ApprovedDomainsView, self).form_valid(form)


class ApprovedDomainsDeleteView(SignupMixin, DeleteView):
    http_method_names = ['post']
    model = OrganisationApprovedDomain

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        org = get_organisations(request.user, kwargs.get('org_id'))
        if not org:
            raise PermissionDenied()
        if not org.bulk_install:
            return HttpResponseRedirect(org.url)
        return super(ApprovedDomainsDeleteView, self).dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return super(ApprovedDomainsDeleteView, self).get_queryset().filter(
            organisation__secure_id=self.kwargs.get('org_id')
        )

    def form_valid(self, form):
        """
        Calls the delete() method on the fetched object and then
        redirects to the success URL.
        """
        messages.add_message(
            self.request, messages.SUCCESS,
            gettext_lazy('{0} removed from your approved domains').format(self.object.domain)
        )
        self.object.delete()
        return HttpResponseRedirect(
            reverse_lazy('dashboard:approved-domains', kwargs={'org_id': self.kwargs['org_id']})
        )


class GenerateCheckPdfView(SignupMixin, View):

    @staticmethod
    def get(request, org_id, user_uuid, device_id, serial_number=None):
        organisation = get_organisations(request.user, org_id)
        app_install = get_app_install(org_id, user_uuid, device_id, convert_serial_number(serial_number), only_active=True)
        if not organisation or not app_install:
            raise PermissionDenied()

        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="{0}_{1}.pdf"'.format(
            app_install.display_os(),
            timezone.now()
        )
        response.write(get_steps_to_secure_pdf(app_install, request))
        return response


class IndustryBenchmarkReportView(SignupMixin, View):
    template_name = 'dashboard/industry-benchmark-report.html'

    def get(self, request, org_id):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()

        certifications_pass_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            certifications_pass_percentage__isnull=False,
            organisation__industry=organisation.industry
        ).values_list('certifications_pass_percentage', flat=True))
        try:
            certifications_pass_percentage = sum(
                certifications_pass_percentage_list
            ) / len(
                certifications_pass_percentage_list
            )
        except ZeroDivisionError:
            certifications_pass_percentage = 0

        cyber_essentials_pass_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            cyber_essentials_pass_percentage__isnull=False,
            organisation__industry=organisation.industry
        ).values_list('cyber_essentials_pass_percentage', flat=True))
        try:
            cyber_essentials_pass_percentage = sum(
                cyber_essentials_pass_percentage_list
            ) / len(
                cyber_essentials_pass_percentage_list
            )
        except ZeroDivisionError:
            cyber_essentials_pass_percentage = 0

        cyber_essentials_plus_pass_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            cyber_essentials_plus_pass_percentage__isnull=False,
            organisation__industry=organisation.industry
        ).values_list('cyber_essentials_plus_pass_percentage', flat=True))
        try:
            cyber_essentials_plus_pass_percentage = sum(
                cyber_essentials_plus_pass_percentage_list
            ) / len(
                cyber_essentials_plus_pass_percentage_list
            )
        except ZeroDivisionError:
            cyber_essentials_plus_pass_percentage = 0

        iasme_pass_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            iasme_pass_percentage__isnull=False,
            organisation__industry=organisation.industry
        ).values_list('iasme_pass_percentage', flat=True))
        try:
            iasme_pass_percentage = sum(
                iasme_pass_percentage_list
            ) / len(
                iasme_pass_percentage_list
            )
        except ZeroDivisionError:
            iasme_pass_percentage = 0

        gdpr_pass_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            gdpr_pass_percentage__isnull=False,
            organisation__industry=organisation.industry
        ).values_list('gdpr_pass_percentage', flat=True))
        try:
            gdpr_pass_percentage = sum(
                gdpr_pass_percentage_list
            ) / len(
                gdpr_pass_percentage_list
            )
        except ZeroDivisionError:
            gdpr_pass_percentage = 0

        secure_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            secure_percentage__isnull=False,
            organisation__industry=organisation.industry
        ).values_list('secure_percentage', flat=True))
        try:
            secure_percentage = sum(
                secure_percentage_list
            ) / len(
                secure_percentage_list
            )
        except ZeroDivisionError:
            secure_percentage = 0

        insecure_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            insecure_percentage__isnull=False,
            organisation__industry=organisation.industry
        ).values_list('insecure_percentage', flat=True))
        try:
            insecure_percentage = sum(
                insecure_percentage_list
            ) / len(
                insecure_percentage_list
            )
        except ZeroDivisionError:
            insecure_percentage = 0

        policies_agreed_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            policies_agreed_percentage__isnull=False,
            organisation__industry=organisation.industry
        ).values_list('policies_agreed_percentage', flat=True))
        try:
            policies_agreed_percentage = sum(
                policies_agreed_percentage_list
            ) / len(
                policies_agreed_percentage_list
            )
        except ZeroDivisionError:
            policies_agreed_percentage = 0

        policies_read_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            policies_read_percentage__isnull=False,
            organisation__industry=organisation.industry
        ).values_list('policies_read_percentage', flat=True))
        try:
            policies_read_percentage = sum(
                policies_read_percentage_list
            ) / len(
                policies_read_percentage_list
            )
        except ZeroDivisionError:
            policies_read_percentage = 0

        policies_not_agreed_percentage_list = list(OrganisationAnalytics.objects.filter(
            organisation__is_test=False,
            policies_not_agreed_percentage__isnull=False,
            organisation__industry=organisation.industry
        ).values_list('policies_not_agreed_percentage', flat=True))
        try:
            policies_not_agreed_percentage = sum(
                policies_not_agreed_percentage_list
            ) / len(
                policies_not_agreed_percentage_list
            )
        except ZeroDivisionError:
            policies_not_agreed_percentage = 0

        context = {
            'organisation': organisation,
            'operating_systems': OperatingSystem.objects.filter(
                pk__in=list(set(organisation.installed_devices.values_list('os__pk', flat=True)))
            ),
            'analytics': organisation.analytics,
            'management_analytics': ManagementAnalytics.objects.first(),
            # same industry
            'certifications_pass_percentage': certifications_pass_percentage,
            'cyber_essentials_pass_percentage': cyber_essentials_pass_percentage,
            'cyber_essentials_plus_pass_percentage': cyber_essentials_plus_pass_percentage,
            'iasme_pass_percentage': iasme_pass_percentage,
            'gdpr_pass_percentage': gdpr_pass_percentage,
            'secure_percentage': secure_percentage,
            'insecure_percentage': insecure_percentage,
            'policies_agreed_percentage': policies_agreed_percentage,
            'policies_read_percentage': policies_read_percentage,
            'policies_not_agreed_percentage': policies_not_agreed_percentage
        }
        return render(request, self.template_name, context)


class StarlingMarketplaceView(SignupMixin, TemplateView):
    template_name = 'dashboard/starling_marketplace.html'

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        if not request.user.profile.has_starling_connected:
            raise PermissionDenied()
        return super(StarlingMarketplaceView, self).dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super(StarlingMarketplaceView, self).get_context_data(**kwargs)
        organisation = get_organisations(self.request.user, self.kwargs['org_id'])
        if not organisation:
            raise PermissionDenied()
        context['organisation'] = organisation
        context['starling_account'] = self.request.user.profile.starling_account
        return context


class StarlingMarketplaceEnableView(SignupMixin, UpdateView):
    http_method_names = ['post']
    fields = ['marketplace_tile_enabled']
    model = Organisation

    def __init__(self, *args, **kwargs):
        self.organisation = None
        super(StarlingMarketplaceEnableView, self).__init__(*args, **kwargs)

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        if not request.user.profile.has_starling_connected:
            raise PermissionDenied()
        return super(StarlingMarketplaceEnableView, self).dispatch(request, *args, **kwargs)

    def get_object(self, queryset=None):
        self.organisation = get_organisations(self.request.user, self.kwargs['org_id'])
        return self.organisation

    def form_valid(self, form):
        # in case the user enabled the tile, send the auth code to starling oauth server
        if form.cleaned_data['marketplace_tile_enabled']:
            from starling.tasks import send_auth_code
            # send auth code to starling oauth server
            send_auth_code.delay(self.request.user.pk)
        # if user disables access, then revoke token and remove the tile
        else:
            self.organisation.marketplace_tile_enabled = form.cleaned_data['marketplace_tile_enabled']
            self.organisation.save()
            AccessToken.objects.filter(user=self.request.user, application__name='Starling').delete()
        return JsonResponse({'success': True, 'marketplace_tile_enabled': form.cleaned_data['marketplace_tile_enabled']})

    def form_invalid(self, form):
        return JsonResponse({'success': False, 'marketplace_tile_enabled': self.organisation.marketplace_tile_enabled})


class InsuranceDashboard(SignupMixin, View):
    template_name = 'dashboard/insurance.html'

    def get(self, request, org_id):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()

        context = {
            'organisation': organisation
        }
        return render(request, self.template_name, context)


class TrainingWelcomeView(PermissionRoleRequiredMixin, OrganizationTrainingMixin, TemplateView):
    """
    View that returns organization trainings welcome page.
    """
    permission_required = DEVICES_PERMISSION
    _welcome_page = True
    template_name = 'dashboard/training-welcome.html'

    http_method_names = ['get']

    def get(self, request, org_id):
        return render(request, self.template_name, {
            'organisation': self._organization,
            'summary': {
                'steps': ['1', '2', '3'],
                'accordion_items': LmsProvider().get_organization_courses(organization=self._organization)
            },
            **self.get_context_data()
        })


class SummaryConfirmView(OrganizationTrainingMixin, TemplateView):
    """
    View that returns organization license.
    """
    template_name = 'dashboard/training-summary-confirm.html'

    http_method_names = ['get', 'post']

    def get_context_data(self, **kwargs):
        provider = LmsProvider()
        organization = self._organization
        try:
            initial_data = model_to_dict(organization.settings)
        except ObjectDoesNotExist:
            initial_data = {}

        return {'organisation': organization,
                'users': organization.enrolled_users,
                'summary': {
                    'steps': ['1', '2', '3'],
                },
                'modules': provider.get_organization_courses(organization=self._organization),
                'form': OrganizationSettingsForm(self.request.POST or None, initial=initial_data)}

    def get(self, request, org_id, *args, **kwargs):
        return render(request, self.template_name, self.get_context_data())

    def post(self, request, org_id, *args, **kwargs):
        form = OrganizationSettingsForm(self.request.POST)
        if form.is_valid():
            form.save(self._organization)
            return HttpResponse()
        return JsonResponse(data={'errors': form.errors}, status=HTTPStatus.BAD_REQUEST)


class TrainingJourneyCompleteCompleteView(OrganizationTrainingMixin, View):
    http_method_names = ['get']

    def send_notification(self):
        """ Send notification """
        kwargs = {
            'user_name': '',
            'organisation_id': self._organization.id,
            'organisation_name': self._organization.name,
            'partner_name': self._organization.partner.name,
            'url': reverse('dashboard:training-progress-users', kwargs={'org_id': self._organization.secure_id})
        }
        NotificationHandler.send_notification(message_type='org_onboarding_completed', **kwargs)

    def get(self, request, org_id, *args, **kwargs):
        if not self._organization.learn_lite_enabled:
            # should not be happening as this step should be only available if training is enabled
            # to be double sure we're not enrolling users in organisations without training enabled
            return redirect('dashboard:home')
        self._organization.user_journey_passed = timezone.now()
        self._organization.save()
        uuid_list = list(self._organization.enrolled_users.values_list('uuid', flat=True))
        add_user_handlers.apply_async(args=(self._organization.id, uuid_list))
        self.send_notification()
        return redirect('dashboard:training-progress-users', self._organization.secure_id)


class TrainingProgressUsersView(PermissionRoleRequiredMixin, OrganizationMixin, TemplateView):
    """
    View that returns training progress of all users enrolled in training.
    """
    permission_required = DEVICES_PERMISSION
    template_name = 'dashboard/training-progress-users.html'

    http_method_names = ['get']

    def get_users(self, query):
        provider = LmsProvider()
        users = self._organization.academy_users
        if query:
            users = users.filter(
                Q(Q(first_name__icontains=query) | Q(last_name__icontains=query) | Q(email__icontains=query)))

        return provider.get_users_and_results(users, self._organization)

    def get(self, request, org_id, *args, **kwargs):
        users = self.get_users(request.GET.get('q'))

        if request.headers.get("x-requested-with") == "XMLHttpRequest":
            return render(request, 'partials/training/training-dashboard-user-table.html', {
                'users': users})
        return render(request, self.template_name, {
            'organisation': self._organization,
            'users': users,
            'statuses': [
                {
                    'text': gettext_lazy('Users Enrolled'),
                },
                {
                    'text': gettext_lazy('Average Completion Rate'),
                    'divider': 'true',
                },
                {
                    'text': gettext_lazy('Average Score'),
                    'percent': 'true',
                }
            ]
        })


class TrainingProgressModulesView(PermissionRoleRequiredMixin, OrganizationMixin, TemplateView):
    """
    View that returns modules training progress.
    """
    template_name = 'dashboard/training-progress-modules.html'
    permission_required = DEVICES_PERMISSION

    http_method_names = ['get']

    def get(self, request, org_id, *args, **kwargs):
        provider = LmsProvider()
        return render(request, self.template_name, {
            'organisation': self._organization,
            'statuses': [
                {
                    'text': gettext_lazy('Users Enrolled'),
                },
                {

                    'text': gettext_lazy('Average Completion Rate'),
                    'divider': 'true',
                },
                {
                    'text': gettext_lazy('Average Score'),
                    'percent': 'true',
                }
            ],
            'modules': provider.get_organization_courses_and_results(organization=self._organization),
        })


class TrainingProgressDataView(OrganizationMixin, View):
    http_method_names = ['get', 'post']

    def get(self, request, org_id, *args, **kwargs):
        query = request.GET.get('result')
        if not query:
            raise Http404

        organization = self._organization
        provider = LmsProvider()
        count = 0
        total_count = 0

        if query == 'enrolled_users_count':
            count = provider.get_total_enrolled_users(organization=organization)
            total_count = organization.enrolled_users.count()
        elif query == 'modules_completed':
            count = provider.get_total_completed(organization=organization)
            total_count = provider.get_total_courses()

        elif query == 'average_score':
            count = provider.get_average_score(organization=organization)
        return JsonResponse({'count': round(count or 0, 0), 'total_count': total_count})


class UserDeviceAttributionConfirmEmailView(TemplateView):
    template_name = "dashboard/user-based-attribution-email.html"

    def get(self, request, secret_key, *args, **kwargs):
        value = get_secret_pair_value(secret_key)
        if not value:
            raise Http404()
        else:
            try:
                app_install_pk, email = value.split("/")
            except ValueError:
                raise Http404()

        app_install = get_object_or_404(AppInstall, pk=app_install_pk)

        # create new or get existing app user
        app_user, created = AppUser.objects.get_or_create(
            organisation=app_install.app_user.organisation,
            email=email,
            defaults={"active": True}
        )

        # if device is already connected to correspond app user raise 404
        if app_install.app_user == app_user:
            messages.success(request, "Your email was already confirmed")
        else:
            # otherwise swap app users
            app_install.app_user = app_user
            app_install.save()

            # created confirmed email for app install
            app_install.confirmed_emails.create(
                email=email
            )
            # Enroll user to academy (LMS) if organisation has academy fully enabled
            if app_install.app_user.organisation.learn_lite_tab_displayed and (
                    created or app_user.lms_enrollment_status == AppUser.STATUS.LMS_NOT_ENROLLED):
                add_user_handlers.apply_async(args=(app_user.organisation_id, [app_user.uuid]))

            messages.success(request, "Your email has been confirmed")

        return render(request, self.template_name, {"email": email, "app_install": app_install, "app_user": app_user})


class NextCertificateView(SignupMixin, View):
    """
    This view redirects user to the next certification that is still in progress or not started.
    """

    @staticmethod
    def get(request, org_id):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()

        next_not_finished = organisation.certifications.filter().order_by("version__type__type").exclude(
            status__in=OrganisationCertification.CERTIFIED_STATUSES
        ).first()
        certification = next_not_finished or organisation.certifications.all().last()

        return HttpResponseRedirect(certification.url)


class VSSView(PermissionRoleRequiredMixin, SignupMixin, View):
    template_name = 'dashboard/vss.html'
    permission_required = CERTIFICATES_AND_INSURANCE_PERMISSION

    def get(self, request, org_id):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()

        ps = ProjectSettings.objects.get()
        context = {
            'organisation': organisation,
            'vss_prospect': ps.vss_prospect,
            'vss_customer': ps.vss_customer
        }
        return render(request, self.template_name, context)


class ToggleMarketingBanner(SignupMixin, View):

    def post(self, request):
        request.session['minimised_banner'] = not request.session.get('minimised_banner', False)
        return JsonResponse({'status': 'success'}, status=HTTPStatus.OK)


class PermanentlyCloseMarketingBanner(SignupMixin, View):

    def post(self, request, marketing_banner_id):
        marketing_banner = get_object_or_404(MarketingBanner, id=marketing_banner_id)
        marketing_banner.users_permanently_closed.add(request.user)
        return JsonResponse({}, status=HTTPStatus.OK)
