from __future__ import absolute_import

import copy
import json
from collections import namedtuple
from datetime import datetime
from unittest import mock
from unittest.mock import patch, MagicMock

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from django.http import Http404, QueryDict
from django.test import TestCase, Client, override_settings, RequestFactory
from django.urls import reverse
from django.utils import timezone
from django.utils.timezone import make_aware
from freezegun import freeze_time

from insurance.insurers.sutcliffe import SUTCLIFFE_SWITCH
from insurance.models import InsurerMixin, InsuranceOptIn

from rulebook.factories import (
    CertificationSurveyFactory,
    SurveyDeclarationFactory,
    SurveyQuestionFactory,
    SurveyResponseFactory,
)
from rulebook.questions import (
    QUESTION_CODE_IASME_INSURANCE_OPT_IN, QUESTION_CODE_IASME_INSURANCE_HEAD_OFFICE,
    QUESTION_CODE_IASME_INSURANCE_EMAIL, QUESTION_CODE_IASME_INSURANCE_REVENUE,
)
from vulnerabilities.factories import CVERepositoryFactory

try:
    from urllib import urlencode
except ImportError:
    from urllib.parse import urlencode
from waffle.testutils import override_switch

from accounts.models import UserStripe
from appusers.models import (
    AppInstall, AppUser, CheckResult, AppReport, CheckManualFix
)
from appusers.models.factories import (
    AppInstallFactory, AppUserFactory, AppReportFactory, AppInstallOSUserFactory,
    UserLoginHistoryFactory, AppOSInstalledSoftwareFactory, InstalledSoftwareAppInstallIndividualFactory,
)
from appusers.models.factories import SoftwarePackageFactory, SoftwarePackageCVEsFactory
from appusers.forms import AppUserForm
from beta_features.factories import BetaEnrolmentFactory, BetaFeatureFactory
from beta_features.models import CAP_V_FIVE_BETA_SWITCH
from beta_features.utils import CAP_V_FIVE_BETA_VERSION
from billing.factories import PartnerBillingFactory, DirectSubscriptionFactory, DirectCustomerFactory
from billing.providers.chargebee.plans import DIRECT_CUSTOMER_V5_APP_CE_ANNUAL_2024
from common.base_tests import BaseTestCase
from dashboard.utils import is_ce_plus_and_not_iasme, turn_cert_type_into_int
from dashboard.views import (
    AddUsers, get_app_users_to_send_email_to, OrganisationDashboard,
    OrganisationDashboardTemplate, SoftwareCSVReportView
)
from distributors.models import Distributor
from notifications.factories import MarketingBannerFactory
from organisations.forms import AddOrganisationForm
from organisations.factories import OrganisationFactory, OrganisationAdminFactory
from organisations.models import OrganisationAdmin, OrganisationUserSync, OrganisationCertification, Organisation
from partners.models import Partner, PartnerUser
from rulebook.models import (
    CertificationSurvey, SurveyDeclaration, CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS,
    CertificateType, SurveyResponse, SurveyQuestion
)
from trustd.factories import TrustdDeviceFactory
from vulnerabilities.models import CVERepository
from vulnerabilities.utils import OPSWAT_SOURCE, REGULAR_SOURCE
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from billing.models import PartnerSubscription
from billing.providers.chargebee import plans
from opswat.models import CVE
from opswat.factories import (
    ProductVendorFactory, ProductFactory, ProductVersionFactory, CVEFactory,
    InstalledProductFactory
)

User = get_user_model()


class DashboardViewsTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_login(self):
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD})

    def create_onboarding(self):
        self.payment = UserStripe.objects.create(
            user=self.user,
            customer='cus_xxx',
            subscription='sub_xxx',
            plan='LTT2'
        )
        self.user.profile.enroll_verified = True
        self.user.profile.save()
        OrganisationAdmin.objects.create(
            organisation=self.organisation,
            user=self.user,
            is_admin=True,
        )

    def test_get_failed_adduser(self):
        self.test_login()
        self.create_onboarding()
        url = reverse('dashboard:add-users',
                      kwargs={'org_id': self.organisation.secure_id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_success_adduser(self):
        self.test_login()
        self.create_onboarding()
        url = reverse('dashboard:add-users',
                      kwargs={'org_id': self.organisation.secure_id})
        response = self.client.post(url)
        self.assertEqual(response.status_code, 302)

    def test_get_failed_certificate(self):
        self.test_login()
        self.create_onboarding()
        url = self.organisation.certifications.all()[0].url
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_get_success_certificate(self):
        self.test_login()
        self.create_onboarding()
        for cert in self.organisation.certifications.all():
            url = reverse('dashboard:certificate', kwargs={'org_id': self.organisation.secure_id, "type": cert.type})
            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)

    def test_get_failed_declaration(self):
        self.user.profile.onboarding_completed = True
        self.test_login()
        self.create_onboarding()
        url = reverse('dashboard:declaration-external', kwargs={
            'org_id': self.organisation.secure_id,
            'type': self.cert_type.type
        })
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)


class DashboardManageUsersTestCase(BaseTestCase, TestCase):
    """
    Tests for Dashboard Manage Users page.
    """

    class View(object):
        def __init__(self, org_id, user, cleaned_data):
            self.cleaned_data = cleaned_data
            self.kwargs = {'org_id': org_id}
            self.user = user

        @property
        def request(self):
            return self

    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.user.profile.onboarding_completed = True
        self.user.profile.save()
        self.add_people_and_org_permission_to_test_user(self.organisation)
        self.client.force_login(self.user)

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def get_add_users_response(self):
        return self.client.get(reverse('dashboard:add-users', kwargs={'org_id': self.organisation.secure_id}))

    def create_org_sync_data(self):
        user_sync = OrganisationUserSync.objects.create(organisation=self.organisation, sync_enabled=True)
        user_1 = {
            'email': '<EMAIL>', 'full_name': 'Marco Polo', 'imported': False,
            'enrolled': False, 'active': False, 'installed': False
        }
        user_2 = {
            'email': '<EMAIL>', 'full_name': 'Marco Polo', 'imported': False,
            'enrolled': False, 'active': False, 'installed': False
        }
        user_3 = {
            'email': '<EMAIL>', 'full_name': 'Marco Polo', 'imported': False,
            'enrolled': False, 'active': False, 'installed': False
        }
        user_sync.users_data = {'users': [user_1, user_2, user_3], 'imported': 0, 'not_imported': 3, 'deactivated': 0}
        user_sync.save()

    @staticmethod
    def get_form_data(nr_users=1):
        """ Helper method to get the form data for AppUserEnrollForm formset """
        data = {
            # management_form data
            'form-INITIAL_FORMS': '0',
            'form-TOTAL_FORMS': str(nr_users),
            'form-MAX_NUM_FORMS': '2',

            # First user data
            'form-0-first_name': 'Marco',
            'form-0-last_name': 'Polo',
            'form-0-email': '<EMAIL>',
        }
        if nr_users not in [1, 2]:
            raise ValueError('nr_users must be 1 or 2')
        if nr_users == 2:
            data.update({
                # Second user data
                'form-1-first_name': 'Found',
                'form-1-last_name': 'You',
                'form-1-email': '<EMAIL>'
            })
        return data

    def check_if_formset_is_valid(self, response, data):
        form_view = AddUsers()
        form_view.setup(response.wsgi_request, org_id=self.organisation.secure_id)
        formset_class = form_view.get_form_class()
        # we should only have 1 app user
        self.assertEqual(form_view.app_users.count(), 1)
        formset = formset_class(data)
        self.assertTrue(formset.is_valid())

    def test_login(self):
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD})

    def test_default_user_count(self):
        self.assertEqual(self.organisation.admins.all().count(), 1)

    def test_default_user_is_admin(self):
        self.assertEqual(self.organisation.admins.all()[0].is_admin, True)

    def test_add_simple_appuser(self):
        view = self.View(org_id=self.organisation.secure_id, user=self.user, cleaned_data={
            'first_name': 'Marco', 'last_name': 'Larco', 'email': '<EMAIL>', 'is_admin': False, 'groups': ''})
        self.assertEqual(self.organisation.app_users.all().count(), 1)
        self.assertEqual(User.objects.filter(email=view.cleaned_data['email']).count(), 0)
        form = AppUserForm(data=view.cleaned_data)
        if form.is_valid():
            form.update_data(view, self.organisation)
        self.assertEqual(self.organisation.app_users.all().count(), 2)
        self.assertEqual(User.objects.filter(email=view.cleaned_data['email']).count(), 0)

    def test_add_admin_appuser(self):
        view = self.View(org_id=self.organisation.secure_id, user=self.user, cleaned_data={
            'first_name': 'Marco', 'last_name': 'Larco', 'email': '<EMAIL>', 'is_admin': True, 'groups': ''})
        self.assertEqual(self.organisation.app_users.all().count(), 1)
        self.assertEqual(User.objects.filter(email=view.cleaned_data['email']).count(), 0)
        form = AppUserForm(data=view.cleaned_data)
        if form.is_valid():
            form.update_data(view, self.organisation)
        new_user = User.objects.get(email=view.cleaned_data['email'])
        self.assertEqual(self.organisation.app_users.all().count(), 2)
        self.assertEqual(self.organisation.app_users.get(email=view.cleaned_data['email']).is_admin, True)
        self.assertEqual(User.objects.filter(email=view.cleaned_data['email']).count(), 1)
        org_user = OrganisationAdmin.objects.filter(
            user=User.objects.get(email=view.cleaned_data['email']),
            organisation=self.organisation
        )[0]
        self.assertEqual(org_user.is_admin, True)
        self.assertEqual(new_user.is_active, True)
        self.assertEqual(new_user.profile.onboarding_completed, True)
        self.assertEqual(new_user.profile.skip_payment, True)
        self.assertEqual(new_user.has_usable_password(), True)

    def test_add_many_different_users(self):
        data = [
            {'first_name': 'Marco', 'last_name': 'Larco', 'email': '<EMAIL>', 'is_admin': True,
             'groups': ''},
            {'first_name': 'Marco', 'last_name': 'Larco', 'email': '<EMAIL>', 'is_admin': True,
             'groups': ''},
            {'first_name': 'Marco', 'last_name': 'Larco', 'email': '<EMAIL>', 'is_admin': False,
             'groups': ''},
            {'first_name': 'Marco', 'last_name': 'Larco', 'email': '<EMAIL>', 'is_admin': True,
             'groups': ''},
            {'first_name': 'Marco', 'last_name': 'Larco', 'email': '<EMAIL>', 'is_admin': False,
             'groups': ''},
            {'first_name': 'Marco', 'last_name': 'Larco', 'email': 'wrongemail', 'is_admin': True,
             'groups': ''},
        ]
        self.assertEqual(self.organisation.app_users.all().count(), 1)
        self.assertEqual(User.objects.all().count(), 1)

        for cleaned_data in data:
            view = self.View(org_id=self.organisation.secure_id, user=self.user, cleaned_data=cleaned_data)
            form = AppUserForm(data=view.cleaned_data)
            if form.is_valid():
                form.update_data(view, self.organisation)

        self.assertEqual(self.organisation.app_users.all().count(), 6)
        self.assertEqual(User.objects.all().count(), 4)
        self.assertEqual(OrganisationAdmin.objects.filter(
            organisation=self.organisation,
            is_admin=True
        ).count(), 4)
        self.assertEqual(OrganisationAdmin.objects.filter(
            organisation=self.organisation,
            is_admin=False
        ).count(), 0)

    def add_sync_users(self):
        data = [
            {'first_name': 'Marco', 'last_name': 'Polo', 'email': '<EMAIL>', 'is_admin': True,
             'is_social': True, 'sync_user': True},
            {'first_name': 'Marco', 'last_name': 'Polo', 'email': '<EMAIL>', 'is_admin': True,
             'is_social': True, 'sync_user': False},
            {'first_name': 'Marco', 'last_name': 'Polo', 'email': '<EMAIL>', 'is_admin': True,
             'is_social': True, 'sync_user': True},
        ]
        self.assertEqual(self.organisation.app_users.all().count(), 1)
        self.assertEqual(User.objects.all().count(), 1)
        self.create_org_sync_data()

        for cleaned_data in data:
            view = self.View(org_id=self.organisation.secure_id, user=self.user, cleaned_data=cleaned_data)
            form = AppUserForm(data=view.cleaned_data)
            if form.is_valid():
                form.update_data(view, self.organisation)

        self.assertEqual(self.organisation.app_users.filter(active=True).count(), 3)
        self.assertEqual(User.objects.all().count(), 3)
        self.assertEqual(self.organisation.organisationusersync.users_data.get('imported'), 2)
        self.assertEqual(self.organisation.organisationusersync.users_data.get('not_imported'), 1)
        self.assertEqual(self.organisation.organisationusersync.users_data.get('deactivated'), 0)
        user_index = next(i for i, item in enumerate(self.organisation.organisationusersync.users_data['users'])
                          if item["email"] == '<EMAIL>')
        users_list = self.organisation.organisationusersync.users_data.get('users')
        self.assertEqual(users_list[user_index]['imported'], True)
        self.assertEqual(users_list[user_index]['active'], True)

    def test_remove_sync_users(self):
        """ If user was synced but the org admin decided to remove """
        self.add_sync_users()
        data = [
            {'first_name': 'Marco', 'last_name': 'Polo', 'email': '<EMAIL>', 'is_admin': True,
             'is_social': True, 'sync_user': True},
            {'first_name': 'Marco', 'last_name': 'Polo', 'email': '<EMAIL>', 'is_admin': True,
             'is_social': True, 'sync_user': False},
            # change this user to not sync
            {'first_name': 'Marco', 'last_name': 'Polo', 'email': '<EMAIL>', 'is_admin': True,
             'is_social': True, 'sync_user': False},
        ]

        for cleaned_data in data:
            view = self.View(org_id=self.organisation.secure_id, user=self.user, cleaned_data=cleaned_data)
            form = AppUserForm(data=view.cleaned_data)
            if form.is_valid():
                form.update_data(view, self.organisation)

        self.assertEqual(self.organisation.app_users.filter(active=True).count(), 2)
        self.assertEqual(self.organisation.app_users.filter(active=False).count(), 1)
        self.assertEqual(User.objects.all().count(), 3)
        self.assertEqual(self.organisation.organisationusersync.users_data.get('imported'), 2)
        self.assertEqual(self.organisation.organisationusersync.users_data.get('not_imported'), 1)
        self.assertEqual(self.organisation.organisationusersync.users_data.get('deactivated'), 1)
        user_index = next(i for i, item in enumerate(self.organisation.organisationusersync.users_data['users'])
                          if item["email"] == '<EMAIL>')
        users_list = self.organisation.organisationusersync.users_data.get('users')
        self.assertEqual(users_list[user_index]['imported'], True)
        self.assertEqual(users_list[user_index]['active'], False)

    def test_valid_adding_users(self):
        """ Test a valid user add  """
        self.organisation.max_cap_users = 2
        self.organisation.save()
        data = self.get_form_data()
        response = self.get_add_users_response()
        form_view = AddUsers()
        form_view.setup(response.wsgi_request, org_id=self.organisation.secure_id)
        formset_class = form_view.get_form_class()
        # we should only have 1 app user
        self.assertEqual(form_view.app_users.count(), 1)
        formset = formset_class(data)
        # form should be valid
        self.assertTrue(formset.is_valid())

    def test_organisation_has_max_cap(self):
        """ Test that if the organisation has max cap and user tries to add more than allowed, it will fail """
        # AppUser must be enrolled and not admin in order to count for the nr of users allowed to add
        app_user = self.organisation.app_users.first()
        app_user.is_admin = False
        app_user.save()

        self.organisation.max_cap_users = 2
        self.organisation.save()

        data = self.get_form_data(2)
        response = self.get_add_users_response()
        form_view = AddUsers()
        form_view.setup(response.wsgi_request, org_id=self.organisation.secure_id)
        formset_class = form_view.get_form_class()
        # we should only have 1 app user
        self.assertEqual(form_view.app_users.count(), 1)
        formset = formset_class(data)
        # the max cap is 2, but we already have 1 app user, so only 1 is allowed
        self.assertFalse(formset.is_valid())
        self.assertIn('Please submit at most 1 form.', formset.non_form_errors())

    def test_organisation_has_max_cap_user_not_enrolled(self):
        """ Test that if the organisation has max cap and user tries to add more users, it will succeed considering
         the app users already in the org are not enrolled """
        app_user = self.organisation.app_users.first()
        app_user.is_admin = False
        app_user.save()

        self.organisation.max_cap_users = 2
        self.organisation.save()

        data = self.get_form_data(1)
        response = self.get_add_users_response()
        self.check_if_formset_is_valid(response, data)

    def test_organisation_no_max_cap(self):
        """ Test that if the organisation does not have a max cap, is allowed to add as many as formset allows"""
        data = {
            # management_form data
            'form-INITIAL_FORMS': '0',
            'form-TOTAL_FORMS': '10',
            'form-MAX_NUM_FORMS': '10',
        }

        for count, value in enumerate('abcdefghij'):
            data[f'form-{count}-first_name'] = value
            data[f'form-{count}-last_name'] = value + 'surname'
            data[f'form-{count}-email'] = value + '@underwater.com'

        response = self.get_add_users_response()
        self.check_if_formset_is_valid(response, data)

    def test_organisation_no_max_cap_more_than_100(self):
        """ Test that if the organisation does not have a max cap and tries to add more than 100, is allowed """
        data = {
            # management_form data
            'form-INITIAL_FORMS': '0',
            'form-TOTAL_FORMS': '101',
            'form-MAX_NUM_FORMS': '101',
        }

        for count in range(101):
            data[f'form-{count}-first_name'] = str(count)
            data[f'form-{count}-last_name'] = str(count) + 'surname'
            data[f'form-{count}-email'] = str(count) + '@underwater.com'

        response = self.get_add_users_response()
        self.check_if_formset_is_valid(response, data)

    def get_user_data(self):
        return [
            {'first_name': 'Marco', 'last_name': 'Larco', 'email': '<EMAIL>', 'is_admin': True,
             'groups': []},
            {'first_name': 'Marco', 'last_name': 'Larco', 'email': '<EMAIL>', 'is_admin': True,
             'groups': []},
            {'first_name': 'Marco', 'last_name': 'Larco', 'email': '<EMAIL>', 'is_admin': False,
             'groups': []},
            {'first_name': 'Marco', 'last_name': 'Larco', 'email': '<EMAIL>', 'is_admin': True,
             'groups': []},
            {'first_name': 'Marco', 'last_name': 'Larco', 'email': '<EMAIL>', 'is_admin': False,
             'groups': []},
            {'first_name': 'Marco', 'last_name': 'Larco', 'email': 'wrongemail', 'is_admin': True,
             'groups': []},
        ]

    def test_add_many_different_users_then_mark_as_admin(self):
        """ The new view does not allow to add dashboard admin for app user, so is_admin is irrelevant """
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD})

        data = self.get_user_data()
        self.assertEqual(self.organisation.app_users.all().count(), 1)
        self.assertEqual(User.objects.all().count(), 1)
        self.assertEqual(OrganisationAdmin.objects.filter(organisation=self.organisation).count(), 1)

        for cleaned_data in data:
            view = self.View(org_id=self.organisation.secure_id, user=self.user, cleaned_data=cleaned_data)
            form = AppUserForm(data=view.cleaned_data)
            if form.is_valid():
                form.update_data(view, self.organisation)

        self.assertEqual(self.organisation.app_users.all().count(), 6)
        self.assertEqual(User.objects.all().count(), 4)
        previous_org_admin_count = 4
        self.assertEqual(
            OrganisationAdmin.objects.filter(organisation=self.organisation).count(),
            previous_org_admin_count
        )

        for nonadmin in self.organisation.app_users.filter(is_admin=True):
            response = self.client.put(
                reverse('api:dashboard:manage-users',
                        kwargs={'org_id': self.organisation.secure_id, 'pk': nonadmin.pk}),
                data=json.dumps({
                    'first_name': nonadmin.first_name,
                    'last_name': nonadmin.last_name,
                    'email': nonadmin.email,
                    'is_admin': True,
                    'groups': [],
                }),
                follow=True,
                content_type="application/json"
            )
            self.assertEqual(json.loads(response.content)['success'], 1)
            self.assertEqual(response.status_code, 200)
        self.assertEqual(
            OrganisationAdmin.objects.filter(organisation=self.organisation).count(),
            previous_org_admin_count
        )

    def test_was_admin(self):
        """ When user was already organisation admin, it does not remove it after updating app user """
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD})

        previous_name = 'Previous Name'
        app_user = AppUserFactory(first_name=previous_name, organisation=self.organisation, is_admin=True)
        OrganisationAdminFactory(organisation=self.organisation, user__email=app_user.email, is_admin=True)

        response = self.client.put(
            reverse('api:dashboard:manage-users',
                    kwargs={'org_id': self.organisation.secure_id, 'pk': app_user.pk}),
            data=json.dumps({
                'first_name': "New Name",
                'last_name': app_user.last_name,
                'email': app_user.email,
                'is_admin': False,
                'groups': [],
            }),
            follow=True,
            content_type="application/json"
        )
        self.assertEqual(json.loads(response.content)['success'], 1)
        self.assertEqual(response.status_code, 200)
        app_user.refresh_from_db()
        self.assertEqual(app_user.first_name, "New Name")
        self.assertEqual(app_user.is_admin, False)
        self.assertTrue(
            OrganisationAdmin.objects.filter(organisation=self.organisation,  user__email=app_user.email).exists()
        )

    def test_is_admin(self):
        """ When user has is_admin set but no OrganisationAdmin, it does not create the OrganisationAdmin"""
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD})

        previous_name = 'Previous Name'
        app_user = AppUserFactory(first_name=previous_name, organisation=self.organisation, is_admin=True)
        self.assertFalse(
            OrganisationAdmin.objects.filter(organisation=self.organisation, user__email=app_user.email).exists()
        )

        response = self.client.put(
            reverse('api:dashboard:manage-users',
                    kwargs={'org_id': self.organisation.secure_id, 'pk': app_user.pk}),
            data=json.dumps({
                'first_name': "New Name",
                'last_name': app_user.last_name,
                'email': app_user.email,
                'is_admin': True,
                'groups': [],
            }),
            follow=True,
            content_type="application/json"
        )
        self.assertEqual(json.loads(response.content)['success'], 1)
        self.assertEqual(response.status_code, 200)
        app_user.refresh_from_db()
        self.assertEqual(app_user.first_name, "New Name")
        self.assertEqual(app_user.is_admin, True)
        self.assertFalse(
            OrganisationAdmin.objects.filter(organisation=self.organisation, user__email=app_user.email).exists()
        )

    def test_no_change_doesnt_update_trustd_device(self):
        app_user = AppUserFactory(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='Firsty',
            last_name='Lastington',
        )
        with patch('trustd.tasks.update_trustd_device.delay') as mock_update_device:
            self.client.put(
                reverse('api:dashboard:manage-users',
                        kwargs={'org_id': self.organisation.secure_id, 'pk': app_user.pk}),
                data=json.dumps({
                    'first_name': app_user.first_name,
                    'last_name': app_user.last_name,
                    'email': app_user.email,
                    'is_admin': True,
                    'groups': [],
                }),
                follow=True,
                content_type="application/json"
            )
        mock_update_device.assert_not_called()

    def test_change_updates_trustd_device(self):
        app_user = AppUserFactory(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='Firsty',
            last_name='Lastington',
        )
        device = TrustdDeviceFactory(
            app_install__app_user=app_user,
            app_install__hostname="Firsty's Computificator",
            app_install__machine_model="Computificator"
        )
        with patch('trustd.tasks.update_trustd_device.delay') as mock_update_device:
            self.client.put(
                reverse('api:dashboard:manage-users',
                        kwargs={'org_id': self.organisation.secure_id, 'pk': app_user.pk}),
                data=json.dumps({
                    'first_name': 'Blirsty',
                    'last_name': 'Lastington',
                    'email': '<EMAIL>',
                    'is_admin': True,
                    'groups': [],
                }),
                follow=True,
                content_type="application/json"
            )
        mock_update_device.assert_called_once_with(
            device.id,
            data={
                'name': "Blirsty's Computificator",  # constructed from device hostname
                'email': '<EMAIL>',
            }
        )

    def test_trustd_device_with_name_unchanged(self):
        app_user = AppUserFactory(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='Firsty',
            last_name='Lastington',
        )
        device_name = "My Trustd Computificator"
        TrustdDeviceFactory(
            device_name=device_name,
            app_install__app_user=app_user,
            app_install__hostname=device_name,
            app_install__machine_model="Computificator"
        )
        with patch('trustd.tasks.update_trustd_device.delay') as mock_update_device:
            self.client.put(
                reverse('api:dashboard:manage-users',
                        kwargs={'org_id': self.organisation.secure_id, 'pk': app_user.pk}),
                data=json.dumps({
                    'first_name': 'Blirsty',
                    'last_name': 'Lastington',
                    'email': '<EMAIL>',
                    'is_admin': True,
                    'groups': [],
                }),
                follow=True,
                content_type="application/json"
            )
        mock_update_device.assert_not_called()


class PartnerDashboardViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.app_user = AppUser.objects.create(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_unauthenticated_access(self):
        response = self.client.get(reverse('dashboard:partner-dashboard'))
        self.assertEqual(302, response.status_code)
        self.assertTrue(response.url.startswith(settings.LOGIN_URL))

    def test_authenticated_access(self):
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD}, follow=True)
        response = self.client.get(reverse('dashboard:partner-dashboard'), follow=True)
        self.assertEqual(200, response.status_code)
        self.assertContains(response, 'Partner dashboard')


class CreateOrganisationViewTest(BaseTestCase, TestCase):
    class Request(object):
        def __init__(self, user):
            self.user = user
            self.POST = QueryDict(urlencode({'custom_admins': '<EMAIL>'}))

    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        PartnerUser.objects.create(
            partner=self.direct_partner,
            user=self.user
        )
        self.user.profile.onboarding_completed = True
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.app_user = AppUser.objects.create(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )
        self.request = self.Request(self.user)

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_unauthenticated_access(self):
        response = self.client.get(reverse('partners:create-organisation'))
        self.assertEqual(302, response.status_code)
        self.assertTrue(response.url.startswith(settings.LOGIN_URL))

    def test_authenticated_access(self):
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD}, follow=True)
        response = self.client.get(reverse('partners:create-organisation'), follow=True)
        self.assertEqual(200, response.status_code)
        self.assertContains(response, 'Add organisation')

    def test_create_organisation_fields_required(self):
        form = AddOrganisationForm(data={
            'name': '',
            'industry': '',
            'size': '',
            'bulk_install': '',
            'email_message': '',
        }, request=self.request)
        self.assertFalse(form.is_valid())
        # self.assertEqual(
        #     form.errors.as_data()['bulk_install'][0].messages[0],
        #     'This field is required.'
        # )
        # self.assertEqual(
        #     form.errors.as_data()['email_message'][0].messages[0],
        #     'This field is required.'
        # )
        self.assertEqual(
            form.errors.as_data()['industry'][0].messages[0],
            'This field is required.'
        )
        self.assertEqual(
            form.errors.as_data()['size'][0].messages[0],
            'This field is required.'
        )
        self.assertEqual(
            form.errors.as_data()['name'][0].messages[0],
            'This field is required.'
        )

    def test_create_organisation_fields_invalid(self):
        form = AddOrganisationForm(data={
            'name': 'nz' * 300,
            'industry': 'n' * 300,
            'size': 'n' * 300,
            'bulk_install': 'n' * 300,
            'email_message': 'n' * 600,
        }, request=self.request)
        self.assertFalse(form.is_valid())
        self.assertTrue(all([
            form.errors.as_data()['size'][0].messages[0].startswith('Select a valid choice.'),
            form.errors.as_data()['size'][0].messages[0].endswith('is not one of the available choices.')
        ]))
        self.assertEqual(
            form.errors.as_data()['name'][0].messages[0],
            'Ensure this value has at most 250 characters (it has 600).'
        )
        self.assertTrue(all([
            form.errors.as_data()['bulk_install'][0].messages[0].startswith('Select a valid choice.'),
            form.errors.as_data()['bulk_install'][0].messages[0].endswith('is not one of the available choices.')
        ]))
        self.assertTrue(all([
            form.errors.as_data()['industry'][0].messages[0].startswith('Select a valid choice.'),
            form.errors.as_data()['industry'][0].messages[0].endswith('is not one of the available choices.')
        ]))
        self.assertEqual(
            form.errors.as_data()['email_message'][0].messages[0],
            'Ensure this value has at most 500 characters (it has 600).'
        )

    def post_create_organisation_request(self, create_app_installs_debug='0', name='New Org', bundle=None, issue_ce_certification_debug=False):
        self.client.force_login(self.user)
        industry = Organisation.ORG_CHOICES[1][0]
        size = Organisation.ORGANISATION_SIZE_CHOICES_CHANNEL[1][0]
        bundle = bundle or AddOrganisationForm.CYBERSMART_BUNDLE
        data = {
            'name': name,
            'industry': industry,
            'size': size,
            'pricing_band': Organisation.PRICING_MONTHLY_V4,
            'bulk_install': False,
            'email_message': 'email message',
            'approved_domain': 'test.com',
            'certifications': CYBER_ESSENTIALS,
            'smart_policies': True,
            'security_emails_frequency': Organisation.SEF_CHOICES[0][0],
            'software-switch': 'on',
            'bundle': bundle,
            'create_app_installs_debug': create_app_installs_debug,
        }
        if issue_ce_certification_debug:
            data['issue_ce_certification_debug'] = 'on'
        response = self.client.post(reverse('partners:create-organisation'),
                                    data=data, follow=True)
        self.assertEqual(200, response.status_code)
        return response

    def test_create_organisation_triggers_trustd_customer_creation(self):
        with patch('trustd.tasks.create_trustd_customer.delay') as mock_create_trustd_customer:
            self.post_create_organisation_request()
            org_created = Organisation.objects.all().order_by('id').last()
            mock_create_trustd_customer.assert_called_with(org_created.id)

    @override_settings(IS_PROD=True)
    def test_create_organisation_with_ce_certification_on_prod(self):
        """
        Show that issuing certification for DEBUG purposes
        on production environment is not possible.
        """
        self.assertEqual(Organisation.objects.count(), 1)
        self.post_create_organisation_request()
        self.assertEqual(Organisation.objects.count(), 2)
        new_org = Organisation.objects.exclude(id=self.organisation.id).first()
        self.assertFalse(new_org.is_eligible_for_ce_plus_audit)

    @override_settings(IS_PROD=False)
    def test_create_organisation_with_ce_certification(self):
        """
        Test the ability for users to create an organisation and automatically
        issue certification for it in non-production environment.
        """
        self.assertEqual(Organisation.objects.count(), 1)
        self.post_create_organisation_request(issue_ce_certification_debug=True)
        self.assertEqual(Organisation.objects.count(), 2)
        new_org = Organisation.objects.exclude(id=self.organisation.id).first()
        self.assertTrue(new_org.is_eligible_for_ce_plus_audit)

    @mock.patch('billing.providers.chargebee.wrapper.chargebee.Subscription.create_for_customer')
    def test_create_organisation_with_cybersmart_complete_bundle(self, mock_create_subscription):
        PartnerBillingFactory(partner=self.direct_partner)
        mock_create_subscription.return_value = namedtuple(
            'Subscription', ['subscription'])(
            namedtuple('Subscription', ['id', 'plan_id', 'next_billing_at', 'started_at', 'plan_amount', 'plan_unit_price', 'plan_free_quantity', 'plan_quantity'])(
                'test-subscription-id', plans.DISTRIBUTOR_CYBER_SMART_COMPLETE_BUNDLE_ANNUAL, 1, 1, 100, 100, 0, 1
            )
        )

        self.post_create_organisation_request(bundle=AddOrganisationForm.CYBERSMART_COMPLETE_BUNDLE)
        self.assertEqual(Organisation.objects.count(), 2)

        new_org = Organisation.objects.exclude(id=self.organisation.id).first()
        ce_cert = new_org.certifications.filter(version__type__type=CYBER_ESSENTIALS).first()
        self.assertIsNotNone(ce_cert)

        ce_plus_cert = new_org.certifications.filter(version__type__type=CYBER_ESSENTIALS_PLUS).first()
        self.assertIsNotNone(ce_plus_cert)
        self.assertTrue(new_org.data_privacy_support)

        partner_subscription = PartnerSubscription.objects.filter(organisation=new_org).first()
        self.assertIsNotNone(partner_subscription)
        self.assertEqual(partner_subscription.plan_id, plans.DISTRIBUTOR_CYBER_SMART_COMPLETE_BUNDLE_ANNUAL)
        self.assertEqual(mock_create_subscription.call_count, 1)
        self.assertFalse(self.direct_partner.is_direct_partner)
        self.assertEqual(mock_create_subscription.call_args[0][1]['plan_id'],
                         plans.DISTRIBUTOR_CYBER_SMART_COMPLETE_BUNDLE_ANNUAL)

class OrganisationDashboardViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.app_user = AppUser.objects.create(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_unauthenticated_access(self):
        response = self.client.get(
            reverse(
                'dashboard:organisation',
                kwargs={'org_id': self.user.profile.get_organisations.all()[0].secure_id})
        )
        self.assertEqual(302, response.status_code)
        self.assertTrue(response.url.startswith(settings.LOGIN_URL))

    def test_authenticated_access(self):
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD}, follow=True)
        response = self.client.get(
            reverse(
                'dashboard:organisation',
                kwargs={'org_id': self.user.profile.get_organisations.all()[0].secure_id}), follow=True
        )
        self.assertEqual(200, response.status_code)
        self.assertContains(response, '- Dashboard')

    def test_get_app_users(self):
        app_user = AppUserFactory(organisation=self.organisation)
        app_install_1 = AppInstallFactory(app_user=app_user)
        app_install_2 = AppInstallFactory(app_user=app_user)
        with freeze_time("2022-01-01"):
            AppReportFactory(app_install=app_install_1)
        with freeze_time("2024-01-01"):
            AppReportFactory(app_install=app_install_2)
        with freeze_time("2023-01-01"):
            AppReportFactory(app_install=app_install_2)
        app_user_without_app_installs = AppUserFactory(organisation=self.organisation)
        view = OrganisationDashboard()
        view.request = RequestFactory().get('fake_request_for_testing')
        fetched_app_users = view.get_app_users(view.request, self.organisation)

        fetched_app_user = fetched_app_users.get(id=app_user.id)
        self.assertEqual(fetched_app_user.most_recent_check_in_precomputed, make_aware(datetime(2024, 1, 1, 0, 0)))
        self.assertEqual(fetched_app_user.most_recent_check_in_precomputed, fetched_app_user.most_recent_check_in)

        fetched_app_user_without_app_installs = fetched_app_users.get(id=app_user_without_app_installs.id)
        self.assertEqual(fetched_app_user_without_app_installs.most_recent_check_in_precomputed, None)
        self.assertEqual(fetched_app_user_without_app_installs.most_recent_check_in_precomputed, fetched_app_user_without_app_installs.most_recent_check_in)

    @override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
    def test_get_app_users_is_beta(self):
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        app_user = AppUserFactory(organisation=self.organisation)
        app_install_1 = AppInstallFactory(app_user=app_user, app_version='4.0.0')
        AppInstallFactory(app_user=app_user, app_version='5.1.0')
        view = OrganisationDashboard()
        view.request = RequestFactory().get('fake_request_for_testing', {CAP_V_FIVE_BETA_SWITCH: 'true'})
        fetched_app_users = view.get_app_users(view.request, self.organisation)

        fetched_app_user = fetched_app_users.get(id=app_user.id)
        fetched_app_install = fetched_app_user.installs.get(id=app_install_1.id)

        # count is 1 because Beta app installs are not included in listing all by default
        self.assertEqual(fetched_app_user.installs.count(), 1)

        self.assertFalse(fetched_app_install.is_beta)
        self.assertFalse(fetched_app_install.is_beta_precomputed)

    @override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
    def test_get_app_users_is_beta_for_beta_app_install(self):
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        app_user = AppUserFactory(organisation=self.organisation)
        app_install_beta = AppInstallFactory(app_user=app_user, app_version='5.1.0')
        view = OrganisationDashboard()
        view.request = RequestFactory().get('fake_request_for_testing', {'filter': [CAP_V_FIVE_BETA_SWITCH]})
        fetched_app_users = view.get_app_users(view.request, self.organisation)

        fetched_app_user = fetched_app_users.get(id=app_user.id)

        app_install_beta = fetched_app_user.installs.get(id=app_install_beta.id)
        self.assertTrue(app_install_beta.is_beta)
        self.assertTrue(app_install_beta.is_beta_precomputed)

    def test_get_latest_local_user_is_admin_account_precomputed(self):
        app_user = AppUserFactory(organisation=self.organisation)
        app_install = AppInstallFactory(app_user=app_user)
        AppInstallOSUserFactory(app_install=app_install)
        AppInstallOSUserFactory(app_install=app_install, is_admin_account=True)
        view = OrganisationDashboard()
        view.request = RequestFactory().get('fake_request_for_testing')
        fetched_app_users = view.get_app_users(view.request, self.organisation)

        fetched_app_user = fetched_app_users.get(id=app_user.id)
        fetched_app_install = fetched_app_user.installs.get(id=app_install.id)
        self.assertEqual(fetched_app_install.latest_local_user_is_admin_account_precomputed, True)
        self.assertEqual(app_install.latest_local_user.is_admin_account, True)

    def test_get_queryset_when_sorted(self):
        app_user = AppUserFactory(organisation=self.organisation)
        app_install = AppInstallFactory(app_user=app_user)
        AppInstallFactory(app_user=app_user)
        AppInstallOSUserFactory(app_install=app_install)
        AppInstallOSUserFactory(app_install=app_install, is_admin_account=True)
        view = OrganisationDashboard()
        view.request = RequestFactory().get('fake_request_for_testing')
        # Simulate request sorting
        view.request.GET = {'order_by': '-first_install_text_precomputed'}
        fetched_app_users = view.get_app_users(view.request, self.organisation)

        # assert that distinct works and duplicates are not getting returned
        self.assertEqual(fetched_app_users.count(), 3)
        fetched_app_user = fetched_app_users.get(id=app_user.id)
        self.assertIn(app_install.hostname, fetched_app_user.first_install_text_precomputed, 'first app_install not found')
        self.assertEqual(AppUser.objects.count(), 3)

    def test_get_last_login_username_precomputed_for_bulk_enrollment_template(self):
        app_user = AppUserFactory(organisation=self.organisation)
        app_install = AppInstallFactory(app_user=app_user)
        UserLoginHistoryFactory(app_install=app_install, user=AppInstallOSUserFactory(app_install=app_install, username='bar'))
        UserLoginHistoryFactory(app_install=app_install, user=AppInstallOSUserFactory(app_install=app_install, username='foo'))

        app_install_with_just_domain = AppInstallFactory(app_user=app_user)
        UserLoginHistoryFactory(
            app_install=app_install_with_just_domain,
            user=AppInstallOSUserFactory(app_install=app_install_with_just_domain, username=None, domain='just_domain'))

        app_install_unnamed = AppInstallFactory(app_user=app_user)
        UserLoginHistoryFactory(
            app_install=app_install_unnamed,
            user=AppInstallOSUserFactory(app_install=app_install_unnamed, username=None, domain=None))

        view = OrganisationDashboard()
        view.request = RequestFactory().get('fake_request_for_testing')
        _, context = view.bulk_enrollment_template(view.request, self.organisation)
        fetched_app_installs = context['devices'].object_list
        fetched_app_install = next(ai for ai in fetched_app_installs if ai.id == app_install.id)

        self.assertEqual(fetched_app_install.last_login_username_precomputed, 'foo')
        self.assertEqual(app_install.last_login_username, 'foo')

        fetched_app_install_with_just_domain = next(ai for ai in fetched_app_installs if ai.id == app_install_with_just_domain.id)
        self.assertEqual(fetched_app_install_with_just_domain.last_login_username_precomputed, 'just_domain')

        fetched_app_install_unnamed = next(ai for ai in fetched_app_installs if ai.id == app_install_unnamed.id)
        self.assertEqual(fetched_app_install_unnamed.last_login_username_precomputed, 'Unnamed')

    def test_get_os_title_for_bulk_enrollment_template(self):
        app_user = AppUserFactory(organisation=self.organisation)
        app_install = AppInstallFactory(app_user=app_user)

        app_install_without_caption = AppInstallFactory(app_user=app_user, caption=None)
        view = OrganisationDashboard()
        view.request = RequestFactory().get('fake_request_for_testing')
        _, context = view.bulk_enrollment_template(view.request, self.organisation)
        fetched_app_installs = context['devices'].object_list
        fetched_app_install = next(ai for ai in fetched_app_installs if ai.id == app_install.id)
        fetched_app_install_without_caption = next(ai for ai in fetched_app_installs if ai.id == app_install_without_caption.id)

        self.assertEqual(fetched_app_install.os_title, app_install.caption)
        self.assertIsNotNone(fetched_app_install_without_caption.os_title)
        self.assertEqual(fetched_app_install_without_caption.os_title, app_install_without_caption.os.title)

    def test_get_most_recent_check_in_precomputed(self):
        app_user = AppUserFactory(organisation=self.organisation)
        app_install = AppInstallFactory(app_user=app_user)
        app_report = AppReportFactory(app_install=app_install)
        UserLoginHistoryFactory(app_install=app_install, user=AppInstallOSUserFactory(app_install=app_install))
        view = OrganisationDashboard()
        view.request = RequestFactory().get('fake_request_for_testing')
        _, context = view.bulk_enrollment_template(view.request, self.organisation)
        fetched_app_installs = context['devices'].object_list
        fetched_app_install = next(ai for ai in fetched_app_installs if ai.id == app_install.id)

        self.assertEqual(fetched_app_install.most_recent_check_in_precomputed, app_install.last_check_in)
        self.assertEqual(fetched_app_install.most_recent_check_in_precomputed, app_report.created)

class DeviceDetailViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.app_user = AppUser.objects.create(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_unauthenticated_access(self):
        response = self.client.get(self.app_install.url())
        self.assertEqual(302, response.status_code)
        self.assertTrue(response.url.startswith(settings.LOGIN_URL))

    def test_authenticated_access(self):
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD}, follow=True)
        response = self.client.get(self.app_install.url())
        self.assertEqual(200, response.status_code)
        self.assertContains(response, "CyberSmart Active Protect hasn't generated a report yet. Or the report is corrupted.")
        # fix app report and try again
        self.app_report = AppReport.objects.create(
            app_install=self.app_install,
            total_responses=1,
            total_commands=3,
            total_passed=1,
            total_failed=2
        )
        response = self.client.get(self.app_install.url())
        self.assertEqual(200, response.status_code)
        self.assertContains(response, "Download Report")
        self.assertContains(response, "Send Report to User")


class DeviceDetailResolveViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.app_user = AppUser.objects.create(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )
        self.report = AppReport.objects.create(
            app_install=self.app_install,
            total_commands=10,
            total_responses=10,
            total_passed_responses=1,
            total_failed_responses=9,
            total_manual_fix=0,
            total_manual_fix_left=9,
            total_passed=1,
            total_failed=9,
            pass_percentage=10
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_check_resolved(self):
        self.login_resp = self.client.post(
            reverse('account_login'),
            {'login': self.USER_EMAIL,
             'password': self.USER_PASSWORD}, follow=True
        )
        res = CheckResult.objects.create(
            report=self.report,
            app_check=self.rule_check,
            report_only=True,
            response=False,
            response_text='aasdfsdf'
        )
        self.assertEqual(self.app_install.get_latest_report.get_total_passed_responses(), 0)
        self.assertEqual(self.app_install.get_latest_report.get_total_failed_responses(), 1)
        self.assertEqual(
            CheckManualFix.objects.filter(
                app_install=self.app_install,
                app_check=self.rule_check,
                app_check__active=True,
            ).count(), 0
        )
        response = self.client.post(
            self.app_install.url_resolve(),
            data={'check_id': self.rule_check.pk, 'id_response': res.pk, 'manual_resolved': 'true'}
        )
        self.assertEqual(response.content, '{"status": 1}'.encode())
        self.assertEqual(
            CheckManualFix.objects.filter(
                app_install=self.app_install,
                app_check=self.rule_check,
                app_check__active=True
            ).count(), 1
        )


@override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
class DeviceDetailBulkResolveViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.app_user = AppUserFactory(
            organisation=self.organisation,
            is_admin=True
        )
        self.report = AppReport.objects.create(
            app_install=self.app_install,
            total_commands=10,
            total_responses=10,
            total_passed_responses=1,
            total_failed_responses=9,
            total_manual_fix=0,
            total_manual_fix_left=9,
            total_passed=1,
            total_failed=9,
            pass_percentage=10
        )
        self.app_user_2 = AppUserFactory(
            organisation=self.organisation,
            is_admin=True
        )
        # AppInstall
        self.app_install_2 = AppInstall.objects.create(
            app_user=self.app_user_2,
            os=self.operating_system,
            hostname='Host Name',
            caption='Caption',
            platform='Platform',
            release='0.01',
            device_id='44dfdd14-e3de-4013-9c42-c4c364a2dcbc'
        )
        # AppReport
        self.report_2 = AppReport.objects.create(
            app_install=self.app_install_2,
            total_commands=10,
            total_responses=10,
            total_passed_responses=1,
            total_failed_responses=9,
            total_manual_fix=0,
            total_manual_fix_left=9,
            total_passed=1,
            total_failed=9,
            pass_percentage=10
        )

        # Beta AppInstall
        self.enable_cap_v5_and_trustd_beta(self.organisation)
        self.app_install_beta = AppInstallFactory(
            app_user=self.app_user_2,
            os=self.operating_system,
            app_version=f'{CAP_V_FIVE_BETA_VERSION}.0.0'
        )
        self.report_beta = AppReportFactory(
            app_install=self.app_install_beta
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_check_bulk_resolved(self):
        self.login_resp = self.client.post(
            reverse('account_login'),
            {'login': self.USER_EMAIL,
             'password': self.USER_PASSWORD}, follow=True
        )
        res = CheckResult.objects.create(
            report=self.report,
            app_check=self.rule_check,
            report_only=True,
            response=False,
            response_text='aasdfsdf'
        )
        res2 = CheckResult.objects.create(
            report=self.report_2,
            app_check=self.rule_check,
            report_only=True,
            response=False,
            response_text='aasdf1234sdf'
        )
        res_beta = CheckResult.objects.create(
            report=self.report_beta,
            app_check=self.rule_check,
            report_only=True,
            response=False,
            response_text='aasdf1234sdfa'
        )

        self.assertEqual(self.app_install.get_latest_report.get_total_passed_responses(), 0)
        self.assertEqual(self.app_install.get_latest_report.get_total_failed_responses(), 1)
        self.assertEqual(self.app_install_2.get_latest_report.get_total_passed_responses(), 0)
        self.assertEqual(self.app_install_2.get_latest_report.get_total_failed_responses(), 1)

        self.assertEqual(
            CheckManualFix.objects.filter(
                app_install=self.app_install,
                app_check=self.rule_check,
                app_check__active=True
            ).count(), 0
        )
        self.assertEqual(
            CheckManualFix.objects.filter(
                app_install=self.app_install_2,
                app_check=self.rule_check,
                app_check__active=True
            ).count(), 0
        )
        response = self.client.post(
            reverse(
                'dashboard:device-bulk-resolve',
                kwargs={
                    'org_id': self.organisation.secure_id,
                }
            ), data={'question_id': self.rule_check.pk, 'os_id': self.app_install.os.pk}
        )
        self.assertEqual(response.content, '{"status": 1}'.encode())
        self.assertEqual(
            CheckManualFix.objects.filter(
                app_install=self.app_install,
                app_check=self.rule_check,
                app_check__active=True
            ).count(), 1
        )
        self.assertEqual(
            CheckManualFix.objects.filter(
                app_install=self.app_install_2,
                app_check=self.rule_check,
                app_check__active=True
            ).count(), 1
        )
        self.assertEqual(
            CheckManualFix.objects.filter(
                app_install=self.app_install_beta,
                app_check=self.rule_check,
                app_check__active=True
            ).count(), 0
        )
        self.assertTrue(res.is_fixed())
        self.assertTrue(res2.is_fixed())
        self.assertFalse(res_beta.is_fixed())


class DeviceDetailResolveRevertViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.app_user = AppUser.objects.create(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )
        self.report = AppReport.objects.create(
            app_install=self.app_install,
            total_commands=10,
            total_responses=10,
            total_passed_responses=1,
            total_failed_responses=9,
            total_manual_fix=0,
            total_manual_fix_left=9,
            total_passed=1,
            total_failed=9,
            pass_percentage=10
        )
        self.res = CheckResult.objects.create(
            report=self.report,
            app_check=self.rule_check,
            report_only=True,
            response=False,
            response_text='aasdfsdf'
        )
        CheckManualFix.objects.create(
            app_install=self.app_install,
            app_check=self.rule_check,
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_check_resolved(self):
        self.login_resp = self.client.post(
            reverse('account_login'),
            {'login': self.USER_EMAIL,
             'password': self.USER_PASSWORD}, follow=True
        )
        self.assertEqual(
            CheckManualFix.objects.filter(
                app_install=self.app_install,
                app_check=self.rule_check,
                app_check__active=True
            ).count(), 1
        )
        self.assertTrue(self.res.is_fixed())
        response = self.client.post(
            self.app_install.url_revert(),
            data={'check_id': self.rule_check.pk, 'id_response': self.res.pk, 'manual_resolved': 'true'}
        )
        self.assertEqual(response.content, '{"status": 1}'.encode())
        self.assertEqual(
            CheckManualFix.objects.filter(
                app_install=self.app_install,
                app_check=self.rule_check,
                app_check__active=True
            ).count(), 0
        )
        self.assertFalse(self.res.is_fixed())


class EditUsersViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.add_people_and_org_permission_to_test_user(self.organisation)
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.new_app_user = AppUser.objects.create(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )
        self.new_user = User.objects.create_user(
            username='packa', email='<EMAIL>',
            password='********'
        )
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.new_user,
            is_admin=False
        ).save()

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_update_user_info(self):
        name = 'Jack'
        email = '<EMAIL>'
        first_name = 'Jack'
        last_name = 'Fresco'
        enroll = 'true'
        is_admin = 'false'
        groups = []
        data = {
            'name': name,
            'email': email,
            'first_name': first_name,
            'last_name': last_name,
            'enroll': enroll,
            'is_admin': is_admin,
            'groups': groups
        }
        self.login_resp = self.client.post(
            reverse('account_login'),
            {'login': self.USER_EMAIL,
             'password': self.USER_PASSWORD}, follow=True
        )
        # invalid user pk
        response = self.client.put(
            reverse(
                'api:dashboard:manage-users',
                kwargs={
                    'org_id': self.organisation.secure_id,
                    'pk': 1234567,
                }
            ), data=json.dumps(data), content_type='application/json'
        )
        self.assertEqual(response.content, '{"detail":"Not found."}'.encode())
        # if user is not admin and trying to edit users
        self.client.post(reverse('account_logout'))
        self.login_resp = self.client.post(
            reverse('account_login'),
            {'login': '<EMAIL>',
             'password': '********'}, follow=True
        )
        response = self.client.put(
            reverse(
                'api:dashboard:manage-users',
                kwargs={
                    'org_id': self.organisation.secure_id,
                    'pk': self.organisation.get_admin_users.first().pk,
                }
            ), data=json.dumps(data), content_type='application/json'
        )
        # commented this assert because it makes the ci failing
        # until figure out why it's only failing on ci
        # self.assertEqual(response.content, '{"detail":"Not found."}'.encode())
        # if new email already exists
        self.client.post(reverse('account_logout'))
        self.login_resp = self.client.post(
            reverse('account_login'),
            {'login': self.USER_EMAIL,
             'password': self.USER_PASSWORD}, follow=True
        )
        data2 = data.copy()
        data2['email'] = '<EMAIL>'
        response = self.client.put(
            reverse(
                'api:dashboard:manage-users',
                kwargs={
                    'org_id': self.organisation.secure_id,
                    'pk': self.app_user.pk,
                }
            ), data=json.dumps(data), content_type='application/json'
        )
        self.assertTrue('data' in json.loads(response.content))
        # change with old email
        response = self.client.put(
            reverse(
                'api:dashboard:manage-users',
                kwargs={
                    'org_id': self.organisation.secure_id,
                    'pk': self.app_user.pk,
                }
            ), data=json.dumps(data), content_type='application/json'
        )
        self.assertEqual(str(json.loads(response.content)['data']['pk']), str(self.app_user.pk))
        self.assertEqual(json.loads(response.content)['data']['first_name'], first_name)
        self.assertEqual(json.loads(response.content)['data']['last_name'], last_name)

        # change with new email
        data2 = data.copy()
        data2['email'] = '<EMAIL>'
        response = self.client.put(
            reverse(
                'api:dashboard:manage-users',
                kwargs={
                    'org_id': self.organisation.secure_id,
                    'pk': self.app_user.pk,
                }
            ), data=json.dumps(data2), content_type='application/json'
        )
        self.assertEqual(str(json.loads(response.content)['data']['pk']), str(self.app_user.pk))
        self.assertEqual(json.loads(response.content)['data']['first_name'], first_name)
        self.assertEqual(json.loads(response.content)['data']['last_name'], last_name)
        self.assertEqual(json.loads(response.content)['data']['email'], '<EMAIL>')


class DeleteUsersViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.new_app_user = AppUser.objects.create(
            organisation=self.organisation,
            email=self.user.email,
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()


class CertificationViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.new_app_user = AppUser.objects.create(
            organisation=self.organisation,
            email=self.user.email,
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()


class DeclarationViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.new_app_user = AppUser.objects.create(
            organisation=self.organisation,
            email=self.user.email,
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )
        cs = CertificationSurvey.objects.create(
            certificate=self.organisation.get_latest_certificate(),
            datetime_started=timezone.now()
        )
        SurveyDeclaration.objects.create(
            survey=cs,
            declaration_date=timezone.now(),
            declaration_name='asdf',
            declaration_job='asdf'
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_unauthenticated_access(self):
        response = self.client.post(
            reverse(
                'dashboard:declaration-external',
                kwargs={
                    'org_id': self.organisation.secure_id,
                    'type': self.cert_type.type
                }
            ),
            data={'name': 'name', 'job': 'job'}
        )
        self.assertEqual(302, response.status_code)

    def test_authenticated_access(self):
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD}, follow=True)
        response = self.client.post(
            reverse(
                'dashboard:declaration-external',
                kwargs={
                    'org_id': self.organisation.secure_id,
                    'type': self.cert_type.type
                }
            ),
            data={'name': 'name', 'job': 'job'}, follow=True
        )
        self.assertEqual(200, response.status_code)
        self.assertContains(response, 'Declaration')


class CompaniesHouseViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.new_app_user = AppUser.objects.create(
            organisation=self.organisation,
            email=self.user.email,
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_unauthenticated_access(self):
        response = self.client.get(reverse('dashboard:get-companieshouse'))
        self.assertEqual(302, response.status_code)
        self.assertTrue(response.url.startswith(settings.LOGIN_URL))

    def test_authenticated_access(self):
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD}, follow=True)
        response = self.client.get(reverse('dashboard:get-companieshouse'), data={'q': 'test'})
        self.assertEqual(200, response.status_code)


class CheckReportViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.new_app_user = AppUser.objects.create(
            organisation=self.organisation,
            email=self.user.email,
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_unauthenticated_access(self):
        response = self.client.get(
            reverse(
                'dashboard:check_report',
                kwargs={
                    'org_id': self.organisation.secure_id,
                }
            )
        )
        self.assertEqual(302, response.status_code)
        self.assertTrue(response.url.startswith(settings.LOGIN_URL))

    def test_authenticated_access(self):
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD}, follow=True)
        response = self.client.get(
            reverse(
                'dashboard:check_report',
                kwargs={
                    'org_id': self.organisation.secure_id,
                }
            )
        )
        self.assertEqual(200, response.status_code)
        self.assertContains(response, 'Device report')


class CheckBulkReportViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.new_app_user = AppUser.objects.create(
            organisation=self.organisation,
            email=self.user.email,
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_unauthenticated_access(self):
        response = self.client.get(
            reverse(
                'dashboard:bulk_check_report',
                kwargs={
                    'org_id': self.organisation.secure_id,
                }
            )
        )
        self.assertEqual(302, response.status_code)
        self.assertTrue(response.url.startswith(settings.LOGIN_URL))

    def test_authenticated_access(self):
        self.login_resp = self.client.post(reverse('account_login'),
                                           {'login': self.USER_EMAIL,
                                            'password': self.USER_PASSWORD}, follow=True)
        response = self.client.get(
            reverse(
                'dashboard:bulk_check_report',
                kwargs={
                    'org_id': self.organisation.secure_id,
                }
            )
        )
        self.assertEqual(200, response.status_code)
        self.assertContains(response, 'Bulk Check Report')


class ManageOrganisationViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.new_app_user = AppUser.objects.create(
            organisation=self.organisation,
            email=self.user.email,
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def post_request(self, data):
        """Performs post request"""
        response = self.client.post(
            reverse(
                'dashboard:manage-organisation',
                kwargs={
                    'org_id': self.organisation.secure_id,
                }
            ),
            data=data,
            follow=True
        )
        return response

    def perform_login(self):
        self.client.post(reverse('account_login'),
                         {'login': self.USER_EMAIL,
                          'password': self.USER_PASSWORD}, follow=True)

    def test_unauthenticated_access(self):
        response = self.client.get(
            reverse(
                'dashboard:manage-organisation',
                kwargs={
                    'org_id': self.organisation.secure_id,
                }
            )
        )
        self.assertEqual(302, response.status_code)
        self.assertTrue(response.url.startswith(settings.LOGIN_URL))

    def test_authenticated_access(self):
        self.perform_login()

        response = self.client.get(
            reverse(
                'dashboard:manage-organisation',
                kwargs={
                    'org_id': self.organisation.secure_id,
                }
            )
        )
        self.assertEqual(200, response.status_code)
        self.assertContains(response, 'Account Settings')

    def test_change_data_with_invalid_industry(self):
        new_org_name = 'New ORg name'
        industry = 'AFS'
        size = Organisation.ORGANISATION_SIZE_1
        bulk_install = 'False'
        email_message = 'ahaha'
        self.perform_login()
        data = {
            'email_message': email_message,
            'id__': self.organisation.secure_id,
            'name': new_org_name,
            'industry': industry,
            'size': size,
            'bulk_install': bulk_install,
            'devices_cleaning_enabled': False,
            'devices_cleaning_days': 90,
        }
        response = self.post_request(data)
        self.assertEqual(200, response.status_code)
        self.assertContains(response, 'Select a valid choice. AFS is not one of the available choices.')

    def test_change_with_different_language(self):
        new_org_name = 'New ORg name'
        industry = 'OTHE'
        industry_description = 'description'
        size = Organisation.ORGANISATION_SIZE_10_19
        bulk_install = 'False'
        email_message = 'ahaha'
        self.perform_login()

        data = {
            'email_message': email_message,
            'id__': self.organisation.secure_id,
            'name': new_org_name,
            'industry': industry,
            'industry_description': industry_description,
            'size': size,
            'bulk_install': bulk_install,
            'devices_cleaning_enabled': False,
            'devices_cleaning_days': 90,
            'default_language': 'sv'
        }
        response = self.post_request(data)
        self.assertEqual(200, response.status_code)
        self.assertContains(response, 'Organisation details updated')
        self.organisation.refresh_from_db()
        self.assertEqual(self.organisation.settings.default_language, 'sv')

    def test_change_data_with_invalid_language(self):
        new_org_name = 'New ORg name'
        industry = 'OTHE'
        industry_description = 'description'
        size = Organisation.ORGANISATION_SIZE_10_19
        bulk_install = 'False'
        email_message = 'ahaha'
        self.perform_login()

        data = {
            'email_message': email_message,
            'id__': self.organisation.secure_id,
            'name': new_org_name,
            'industry': industry,
            'industry_description': industry_description,
            'size': size,
            'bulk_install': bulk_install,
            'devices_cleaning_enabled': False,
            'devices_cleaning_days': 90,
            'default_language': 'invalid_language'
        }
        response = self.post_request(data)
        self.assertEqual(200, response.status_code)
        self.assertContains(response, 'Select a valid choice. invalid_language is not one of the available choices.')

    def test_change_lms_send_email_notifications(self):
        new_org_name = 'New ORg name'
        industry = 'OTHE'
        industry_description = 'description'
        size = Organisation.ORGANISATION_SIZE_10_19
        bulk_install = 'False'
        email_message = 'ahaha'
        self.perform_login()

        self.organisation.settings.lms_send_email_notifications = False
        self.organisation.settings.save()

        data = {
            'email_message': email_message,
            'id__': self.organisation.secure_id,
            'name': new_org_name,
            'industry': industry,
            'industry_description': industry_description,
            'size': size,
            'bulk_install': bulk_install,
            'lms_send_email_notifications': True,
            'devices_cleaning_enabled': False,
            'devices_cleaning_days': 90,
        }
        response = self.post_request(data)
        self.assertEqual(200, response.status_code)
        self.assertContains(response, 'Organisation details updated')
        self.organisation.settings.refresh_from_db()
        self.assertTrue(self.organisation.settings.lms_send_email_notifications)


class DeleteOrganisationViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.new_app_user = AppUser.objects.create(
            organisation=self.organisation,
            email=self.user.email,
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()


class DevicePDFReportTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.user.profile.onboarding_completed = True
        partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor
        )
        PartnerUser.objects.create(
            partner=partner,
            user=self.user
        )
        self.user.profile.skip_payment = True
        self.user.profile.save()
        self.app_user = AppUser.objects.create(
            organisation=self.organisation,
            email='<EMAIL>',
            first_name='First Name AppUser',
            last_name='Last Name AppUser',
            is_admin=True
        )
        self.report = AppReport.objects.create(
            app_install=self.app_install,
            total_commands=10,
            total_responses=10,
            total_passed_responses=1,
            total_failed_responses=9,
            total_manual_fix=0,
            total_manual_fix_left=9,
            total_passed=1,
            total_failed=9,
            pass_percentage=10
        )
        self.res = CheckResult.objects.create(
            report=self.report,
            app_check=self.rule_check,
            report_only=True,
            response=False,
            response_text='aasdfsdf'
        )
        CheckManualFix.objects.create(
            app_install=self.app_install,
            app_check=self.rule_check,
        )

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        UserStripe.objects.all().delete()

    def test_check_pdf_report(self):
        self.login_resp = self.client.post(
            reverse('account_login'),
            {'login': self.USER_EMAIL,
             'password': self.USER_PASSWORD}, follow=True
        )
        response = self.client.get(self.app_install.url_checks_pdf(), follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["content-type"], "application/pdf")


class EnableCertificationViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.distributor, _ = Distributor.objects.get_or_create(name=settings.DEFAULT_CS_DIRECT_DIST_NAME)
        self.partner = Partner.objects.create(
            name=self.user.get_full_name(),
            distributor=self.distributor,
            iasme_cb=True
        )
        PartnerUser.objects.create(
            partner=self.partner,
            user=self.user
        )

    def test_ce_plus_iasme_permission(self):
        cert_type = CYBER_ESSENTIALS_PLUS
        self.partner.iasme_cb = False
        self.partner.save()
        self.assertTrue(is_ce_plus_and_not_iasme(self.user, cert_type))

    def test_ce_iasme_allow(self):
        cert_type = CYBER_ESSENTIALS
        self.assertFalse(is_ce_plus_and_not_iasme(self.user, cert_type))

    def test_ce_plus_not_iasme_allow(self):
        cert_type = CYBER_ESSENTIALS_PLUS
        self.partner.iasme_cb = True
        self.partner.save()
        self.assertFalse(is_ce_plus_and_not_iasme(self.user, cert_type))

    def test_turn_cert_type_into_int(self):
        self.assertEqual(1, turn_cert_type_into_int('1'))

    def test_turn_cert_type_into_int_not_int(self):
        with self.assertRaises(Http404):
            turn_cert_type_into_int('string')

    def tearDown(self):
        self.core_tearing_down()
        User.objects.all().delete()


class ManageNotificationsTestCase(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        self.org_settings = self.organisation.settings
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.data = {
            'app_checks_notifications': self.org_settings.app_checks_notifications,
            'app_checks_reminders': '',
            'policies_notifications': self.org_settings.policies_notifications,
            'policies_reminders': '',
            'academy_notifications': self.org_settings.academy_notifications,
            'academy_reminders': '',
        }
        self.user.profile.onboarding_completed = True
        self.user.profile.save()
        self.client.force_login(self.user)

    def tearDown(self):
        self.core_tearing_down()
        Organisation.objects.all().delete()
        self.deleting_app_user()

    def make_post_request(self, data=None):
        """ helper method to do the POST request """
        if not data:
            data = self.data
        response = self.client.post(
            reverse('dashboard:manage-notifications', kwargs={'org_id': self.organisation.secure_id}),
            data=data,
            follow=True
        )
        self.assertEqual(response.status_code, 200)
        self.org_settings.refresh_from_db()
        return response

    def test_enable_app_checks_notifications_form(self):
        """ When enabling app checks notifications it updates organisation settings
        and does not set any reminders """
        previous_value = self.org_settings.app_checks_notifications
        self.data['app_checks_notifications'] = True

        self.make_post_request(self.data)
        self.assertNotEqual(previous_value, self.org_settings.app_checks_notifications)
        self.assertTrue(self.org_settings.app_checks_notifications)
        self.assertIsNone(self.org_settings.app_checks_reminders)

    def test_disable_policies_notifications_form(self):
        """ If we disable policies notifications
        but have reminders set for that, it will set to None """
        # set reminders and enable policy notifications
        self.org_settings.policies_notifications = True
        self.org_settings.policies_reminders = 12
        self.org_settings.save()
        previous_value = self.org_settings.policies_notifications
        self.data['policies_notifications'] = False

        self.make_post_request(self.data)
        self.assertNotEqual(previous_value, self.org_settings.policies_notifications)
        self.assertFalse(self.org_settings.policies_notifications)
        self.assertIsNone(self.org_settings.policies_reminders)

    def test_enable_academy_reminders_form(self):
        """ When enabling academy notifications and reminders
        it will update settings accordingly """
        reminders_hours = '16'
        previous_value = self.org_settings.academy_notifications
        self.data['academy_notifications'] = True
        self.data['academy_reminders'] = reminders_hours

        self.make_post_request(self.data)
        self.assertNotEqual(previous_value, self.org_settings.academy_notifications)
        self.assertTrue(self.org_settings.academy_notifications)
        self.assertIsNotNone(self.org_settings.academy_reminders)
        self.assertEqual(self.org_settings.academy_reminders, int(reminders_hours))

    def test_update_policies_reminders_form(self):
        """ When updating policies reminders it will update settings accordingly """
        reminders_hours = '12'
        new_hours = '24'
        # set reminders and enable policy notifications
        self.org_settings.policies_notifications = True
        self.org_settings.policies_reminders = int(reminders_hours)
        self.org_settings.save()
        self.org_settings.refresh_from_db()
        self.data['policies_notifications'] = self.org_settings.policies_notifications
        self.data['policies_reminders'] = new_hours

        self.make_post_request(self.data)
        self.assertTrue(self.org_settings.policies_notifications)
        self.assertEqual(self.org_settings.policies_reminders, int(new_hours))


@patch('notifications.handlers.NotificationHandler.send_notification')
class ManageFeaturesViewTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        self.org_settings = self.organisation.settings
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        PartnerUser.objects.create(
            partner=self.direct_partner,
            user=self.user
        )
        self.user.profile.onboarding_completed = True
        self.user.profile.save()
        self.client.force_login(self.user)
        self.data = {
            'smart_score_enabled': self.org_settings.smart_score_enabled,
            'academy_enabled': self.org_settings.academy_enabled,
            'uba_enabled': self.org_settings.uba_enabled,
            "cap_steps_to_fix_enabled": self.org_settings.cap_steps_to_fix_enabled,
            "cap_vulnerable_software_enabled": self.org_settings.cap_vulnerable_software_enabled
        }
        self.beta_feature = BetaFeatureFactory()
        self.beta_enrolment = BetaEnrolmentFactory(partner=self.direct_partner, feature=self.beta_feature)

    def tearDown(self):
        self.core_tearing_down()
        self.deleting_app_user()

    def make_post_request(self, data=None):
        """ helper method to do the POST request """
        if not data:
            data = self.data
        response = self.client.post(
            reverse('dashboard:manage-features', kwargs={'org_id': self.organisation.secure_id}),
            data=data,
            follow=True
        )
        self.assertEqual(response.status_code, 200)
        self.org_settings.refresh_from_db()

    def test_simple_change(self, _):
        """ Test a simple change from False to True on the feature, it updates the settings """
        self.assertFalse(self.org_settings.academy_enabled)
        self.data['academy_enabled'] = True
        self.make_post_request()
        self.assertTrue(self.org_settings.academy_enabled)

    def test_send_notification(self, send_notification):
        """ Test a change triggers the sending of notification """
        self.data['academy_enabled'] = True
        self.make_post_request()
        self.assertTrue(self.org_settings.academy_enabled)
        expected_kwargs = {
            'message_type': 'org_features_updated',
            'user_name': '<EMAIL>',
            'organisation_id': self.organisation.id,
            'organisation_name': self.organisation.name,
            'partner_name': self.direct_partner.name,
            'feature_switch_name': 'CyberSmart Learn Lite',
            'feature_switch_value': 'enabled',
            'url': reverse('dashboard:organisation', kwargs={'org_id': self.organisation.secure_id}),
        }
        send_notification.assert_called_with(**expected_kwargs)

    def test_beta_feature_enrolment_update(self, _):
        """ Test partner adds an organisation to beta enrolment """
        self.assertFalse(self.beta_enrolment.organisations.filter(id=self.organisation.id).exists())
        self.assertFalse(Organisation.objects.filter(id=self.organisation.id, beta_enrolment__isnull=False).exists())

        self.data[self.beta_feature.kind] = True
        self.make_post_request()

        self.assertTrue(self.beta_enrolment.organisations.filter(id=self.organisation.id).exists())
        self.assertTrue(Organisation.objects.filter(id=self.organisation.id, beta_enrolment__isnull=False).exists())

    def test_beta_feature_enrolment_partner_not_enabled(self, _):
        """ Test if partner does not have feature enrolment enabled then it does not add the organisation to the list """
        self.assertFalse(Organisation.objects.filter(id=self.organisation.id, beta_enrolment__isnull=False).exists())

        self.beta_enrolment.delete()
        self.data[self.beta_feature.kind] = True
        self.make_post_request()

        self.assertFalse(Organisation.objects.filter(id=self.organisation.id, beta_enrolment__isnull=False).exists())

    def test_beta_feature_enrolment_multiple_orgs(self, _):
        """ Test existing organisations will not get changed if partner enables beta enrolment for a specific org """
        self.beta_enrolment.organisations.add(OrganisationFactory())
        self.assertEqual(self.beta_enrolment.organisations.count(), 1)

        self.data[self.beta_feature.kind] = True
        self.make_post_request()

        self.assertTrue(self.beta_enrolment.organisations.filter(id=self.organisation.id).exists())
        self.assertEqual(self.beta_enrolment.organisations.count(), 2)

    def test_beta_feature_enrolment_disable(self, _):
        """ Test disabling beta enrolment for an organisation """
        self.beta_enrolment.organisations.add(OrganisationFactory())
        self.beta_enrolment.organisations.add(self.organisation)
        self.assertEqual(self.beta_enrolment.organisations.count(), 2)

        self.data[self.beta_feature.kind] = False
        self.make_post_request()

        self.assertFalse(self.beta_enrolment.organisations.filter(id=self.organisation.id).exists())
        self.assertEqual(self.beta_enrolment.organisations.count(), 1)


class TestMarketingBanner(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.user.profile.onboarding_completed = True
        self.user.profile.save()
        self.client.force_login(self.user)
        self.marketing_banner = MarketingBannerFactory(is_active=True)

    def tearDown(self):
        self.core_tearing_down()

    def test_dismiss_marketing_banner(self):
        # initially this variable is not set
        self.assertEqual(self.client.session.get('minimised_banner'), None)

        # toggle first time and see it's set to true
        response = self.client.post(
            reverse('dashboard:toggle-marketing-banner'),
            data=None,
            follow=True
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.client.session.get('minimised_banner'), True)

        # do it again and see that it's toggled
        self.client.post(
            reverse('dashboard:toggle-marketing-banner'),
            data=None,
            follow=True
        )
        self.assertEqual(self.client.session.get('minimised_banner'), False)

    def test_permanently_close_marketing_banner(self):
        self.assertNotIn(self.user, self.marketing_banner.users_permanently_closed.all())
        self.client.post(
            reverse('dashboard:permanently-close-marketing-banner', kwargs={'marketing_banner_id': self.marketing_banner.id}),
            data=None,
            follow=True
        )
        self.assertIn(self.user, self.marketing_banner.users_permanently_closed.all())


class TestBetaEmailInvitationSend(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.user.profile.onboarding_completed = True
        self.user.profile.save()
        self.client.force_login(self.user)

        self.new_app_user = AppUserFactory(organisation=self.organisation, active=True)
        self.new_app_user_2 = AppUserFactory(organisation=self.organisation, active=True)

    def tearDown(self):
        self.core_tearing_down()

    def test_get_app_users_to_send_email_to(self):
        self.app_user_awaiting_install = AppUserFactory(organisation=self.organisation)
        AppInstallFactory(app_user=self.app_user_awaiting_install)

        self.app_user_with_one_active_application = AppUserFactory(organisation=self.organisation)
        AppInstallFactory( app_user=self.app_user_with_one_active_application)

        self.app_user_with_inactive_application = AppUserFactory(organisation=self.organisation)
        AppInstallFactory( app_user=self.app_user_with_inactive_application, inactive=True)

        self.app_user_not_active = AppUserFactory(organisation=self.organisation, active=False)

        res = get_app_users_to_send_email_to(self.organisation)

        self.assertIn(self.app_user_with_one_active_application, res)
        self.assertIn(self.app_user_awaiting_install, res)
        self.assertIn(self.app_user, res)
        self.assertNotIn(self.app_user_with_inactive_application, res)
        self.assertNotIn(self.app_user_not_active, res)


class TestDeviceAssignUser(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user(with_app_install=True)
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.user.profile.onboarding_completed = True
        self.user.profile.save()
        self.client.force_login(self.user)
        # organisation must be bulk UBA
        self.ps.uba_enabled = True
        self.ps.save()
        self.organisation.bulk_install = True
        self.organisation.save()
        self.organisation.settings.uba_enabled = True
        self.organisation.settings.save()

        self.app_install_unassigned = AppInstallFactory(app_user=self.app_user)
        self.app_user_2 = AppUserFactory(organisation=self.organisation, active=True)
        self.app_install_assigned = AppInstallFactory(app_user=self.app_user_2)
        self.app_user_to_assign = AppUserFactory(
            first_name='Scotch',
            last_name='Bonnet',
            organisation=self.organisation,
            active=True
        )

    def tearDown(self):
        self.core_tearing_down()

    def test_assign_new_user_to_app_install(self):
        """ Test that we can assign a new user to an app install """
        self.assertEqual(self.app_install_unassigned.app_user, self.app_user)
        previous_hostname = self.app_install_unassigned.hostname
        response = self.client.post(
            self.app_install_unassigned.url_device_assign_user(),
            data={'new_app_user_uuid': self.app_user_to_assign.uuid},
        )
        self.assertEqual(response.status_code, 200)
        # new app user UUID should be in response
        self.assertEqual(response.json().get('uuid'), str(self.app_user_to_assign.uuid))
        self.app_install_unassigned.refresh_from_db()
        self.assertEqual(self.app_install_unassigned.app_user, self.app_user_to_assign)
        # hostname will not get updated since it is not a trustd device
        self.assertEqual(previous_hostname, self.app_install_unassigned.hostname)

    def test_assign_non_uba(self):
        """ Test that if organisation is not UBA then they cannot assign """
        self.organisation.settings.uba_enabled = False
        self.organisation.settings.save()
        response = self.client.post(
            self.app_install_unassigned.url_device_assign_user(),
            data={'new_app_user_uuid': self.app_user_to_assign.uuid},
        )
        self.assertEqual(response.status_code, 403)

    def test_assign_non_bulk(self):
        """ Test that if organisation is not bulk then they cannot assign """
        self.organisation.bulk_install = False
        self.organisation.save()
        response = self.client.post(
            self.app_install_unassigned.url_device_assign_user(),
            data={'new_app_user_uuid': self.app_user_to_assign.uuid},
        )
        self.assertEqual(response.status_code, 403)

    def test_assign_app_install_has_trustd_device(self):
        """ Test that if the app install is linked to a trustd device, then it updates hostname as well """
        # hostname will have to get updated
        previous_hostname = self.app_install_unassigned.hostname
        TrustdDeviceFactory(app_install=self.app_install_unassigned)
        with patch('trustd.tasks.update_trustd_device.delay') as mock_update_trustd_device:
            response = self.client.post(
                self.app_install_unassigned.url_device_assign_user(),
                data={'new_app_user_uuid': self.app_user_to_assign.uuid},
            )
        self.assertEqual(response.status_code, 200)
        self.app_install_unassigned.refresh_from_db()
        self.assertEqual(self.app_install_unassigned.app_user, self.app_user_to_assign)
        self.assertNotEqual(previous_hostname, self.app_install_unassigned.hostname)
        self.assertTrue(self.app_install_unassigned.hostname.startswith(self.app_user_to_assign.first_name))
        mock_update_trustd_device.assert_called_with(
            self.app_install_unassigned.trustd_device.id,
            {'email': self.app_user_to_assign.email, 'name': self.app_install_unassigned.hostname}
        )

    def test_assign_app_install_trustd_device_with_name(self):
        """ If the trustd device already has a name, that is not changed """
        device_name = "My Trustd Device"
        self.app_install_unassigned.hostname = device_name
        self.app_install_unassigned.save()
        TrustdDeviceFactory(app_install=self.app_install_unassigned, device_name=device_name)
        with patch('trustd.tasks.update_trustd_device.delay') as mock_update_trustd_device:
            response = self.client.post(
                self.app_install_unassigned.url_device_assign_user(),
                data={'new_app_user_uuid': self.app_user_to_assign.uuid},
            )
        self.assertEqual(response.status_code, 200)
        self.app_install_unassigned.refresh_from_db()
        self.assertEqual(self.app_install_unassigned.app_user, self.app_user_to_assign)
        self.assertEqual(device_name, self.app_install_unassigned.hostname)
        mock_update_trustd_device.assert_called_with(
            self.app_install_unassigned.trustd_device.id,
            {'email': self.app_user_to_assign.email}
        )


@override_switch(SUTCLIFFE_SWITCH, active=True)
@override_switch('enable_insurance_opt_in_external_declaration', active=True)
class TestDeclarationView(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        self.org_settings = self.organisation.settings
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()
        self.data = {
            'name': 'John Doe',
            'job': 'CEO',
            'signature_name': 'data:image/png;base64,aVZCT1J3MEtHZ29BQUFBTlNVaEVVZ0FBQUFnQUFBQUlBUU1BQUFEK3dTeklBQUFBQmxCTVZFWC8vLysvdjcralEzWTVBQUFBRGtsRVFWUUkxMlA0QUlYOEVBZ0FMZ0FEL2FOcGJ0RUFBQUFBU1VWT1JLNUNZSUk='
        }
        self.user.profile.onboarding_completed = True
        self.user.profile.save()

        self.certification_survey = CertificationSurveyFactory(certificate=self.organisation_certification)
        SurveyDeclarationFactory(survey=self.certification_survey)
        self.response_opt_in = SurveyResponseFactory(
            question= SurveyQuestionFactory(
                version=self.organisation_certification.version,
                insurer=InsurerMixin.IASME_INSURER,
                code=QUESTION_CODE_IASME_INSURANCE_OPT_IN,
            ),
            survey=self.certification_survey,
            value_boolean=True
        )
        self.certification_survey.certificate.status=OrganisationCertification.AWAITING_SIGNATURE
        self.certification_survey.certificate.save()
        self.create_insurance_responses()
        self.customer = DirectCustomerFactory(organisation=self.organisation)
        DirectSubscriptionFactory(
            customer=self.customer,
            plan_id=DIRECT_CUSTOMER_V5_APP_CE_ANNUAL_2024
        )

    def create_insurance_responses(self):
        self.codes = [QUESTION_CODE_IASME_INSURANCE_HEAD_OFFICE, QUESTION_CODE_IASME_INSURANCE_EMAIL, QUESTION_CODE_IASME_INSURANCE_REVENUE]
        for code in self.codes:
            SurveyResponseFactory(
                question=SurveyQuestionFactory(
                    version=self.organisation_certification.version,
                    insurer=InsurerMixin.IASME_INSURER,
                    code=code,
                ),
                survey=self.certification_survey,
                value_boolean=False,
                not_applicable=True
            )

    def make_post_request(self, user, url='dashboard:declaration', data=None):
        """ helper method to do the POST request """
        if not isinstance(user, AnonymousUser):
            self.client.force_login(user)
        response = self.client.post(
            reverse(url, kwargs={'org_id': self.organisation.secure_id, 'type': CYBER_ESSENTIALS}),
            data=data,
            follow=True
        )
        return response

    def _test_declaration_post(self, user, url, data):
        self.assertEqual(
            self.certification_survey.certificate.status,
            OrganisationCertification.AWAITING_SIGNATURE
        )
        with freeze_time('2021-01-01'):
            response = self.make_post_request(user, url, data)

        self.assertEqual(response.status_code, 200)
        self.certification_survey.refresh_from_db()
        self.assertEqual(
            self.certification_survey.certificate.status,
            OrganisationCertification.DECLARATION_SIGNED
        )
        self.assertEqual(
            self.certification_survey.declaration.date_signed,
            make_aware(datetime(2021, 1, 1))
        )
        self.assertEqual(
            self.certification_survey.declaration.declaration_date,
            make_aware(datetime(2021, 1, 1))
        )

    def test_declaration_post_updates_status(self):
        self._test_declaration_post(self.user, 'dashboard:declaration', self.data)

    def test_declaration_post_external_updates_status(self):
        anonymous_user = AnonymousUser()
        self._test_declaration_post(anonymous_user, 'dashboard:declaration-external', self.data)

    def test_declaration_post_insurance(self):
        anonymous_user = AnonymousUser()
        data = copy.deepcopy(self.data)
        data.update({
            'insurance_choice': 'true',
            'revenue': '100000',
            'email_address': '<EMAIL>'
        })
        self.response_opt_in.value_boolean = False
        self.response_opt_in.save()
        self._test_declaration_post(anonymous_user, 'dashboard:declaration-external', data)
        # Survey responses for insurance are updated
        self.response_opt_in.refresh_from_db()
        self.assertTrue(self.response_opt_in.value_boolean)
        for code in self.codes:
            response = SurveyResponse.objects.get(
                question__code=code,
                survey=self.certification_survey
            )
            self.assertFalse(response.not_applicable)
            if code == QUESTION_CODE_IASME_INSURANCE_HEAD_OFFICE:
                self.assertTrue(response.value_boolean)
            elif code == QUESTION_CODE_IASME_INSURANCE_REVENUE:
                self.assertEqual(response.value_money, int(data.get('revenue')))
            else:
                self.assertEqual(response.value_text, data.get('email_address'))

    def test_declaration_post_insurance_with_upgrade_not_filled(self):
        self.organisation.pricing_band = Organisation.PRICING_BUNDLE_ANNUAL
        self.organisation.save()
        anonymous_user = AnonymousUser()
        data = copy.deepcopy(self.data)
        data.update({
            'insurance_choice': 'true',
            'revenue': '100000',
            'email_address': '<EMAIL>'
        })
        self.response_opt_in.value_boolean = False
        self.response_opt_in.save()
        self.assertEqual(
            self.certification_survey.certificate.status,
            OrganisationCertification.AWAITING_SIGNATURE
        )
        with freeze_time('2021-01-01'):
            response = self.make_post_request(anonymous_user, 'dashboard:declaration-external', data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Please make sure that all the fields have valid values.')
        self.organisation_certification.refresh_from_db()
        self.certification_survey.refresh_from_db()
        self.assertEqual(
            self.certification_survey.certificate.status,
            OrganisationCertification.AWAITING_SIGNATURE
        )
        # Assert nothing has changed with regards to upgrade (default value)
        self.assertEqual(self.organisation_certification.immediate_requested_coverage_amount, InsuranceOptIn.COVERAGE.CE_25K)

    def test_declaration_post_insurance_with_no_upgrade(self):
        self.organisation.pricing_band = Organisation.PRICING_BUNDLE_ANNUAL
        self.organisation.save()
        anonymous_user = AnonymousUser()
        data = copy.deepcopy(self.data)
        data.update({
            'insurance_choice': 'true',
            'revenue': '100000',
            'email_address': '<EMAIL>',
            "insurance_upgrade": "false",
        })
        self.response_opt_in.value_boolean = False
        self.response_opt_in.save()
        self._test_declaration_post(anonymous_user, 'dashboard:declaration-external', data)
        # Survey responses for insurance are updated
        self.response_opt_in.refresh_from_db()
        self.assertTrue(self.response_opt_in.value_boolean)
        for code in self.codes:
            response = SurveyResponse.objects.get(
                question__code=code,
                survey=self.certification_survey
            )
            self.assertFalse(response.not_applicable)
            if code == QUESTION_CODE_IASME_INSURANCE_HEAD_OFFICE:
                self.assertTrue(response.value_boolean)
            elif code == QUESTION_CODE_IASME_INSURANCE_REVENUE:
                self.assertEqual(response.value_money, int(data.get('revenue')))
            else:
                self.assertEqual(response.value_text, data.get('email_address'))
        self.organisation_certification.refresh_from_db()
        self.assertEqual(self.organisation_certification.immediate_requested_coverage_amount, InsuranceOptIn.COVERAGE.CE_25K)

    def test_declaration_post_insurance_with_upgrade(self):
        self.organisation.pricing_band = Organisation.PRICING_BUNDLE_ANNUAL
        self.organisation.save()
        anonymous_user = AnonymousUser()
        data = copy.deepcopy(self.data)
        data.update({
            'insurance_choice': 'true',
            'revenue': '100000',
            'email_address': '<EMAIL>',
            "insurance_upgrade": "true",
        })
        self.response_opt_in.value_boolean = False
        self.response_opt_in.save()
        self._test_declaration_post(anonymous_user, 'dashboard:declaration-external', data)
        # Survey responses for insurance are updated
        self.response_opt_in.refresh_from_db()
        self.assertTrue(self.response_opt_in.value_boolean)
        for code in self.codes:
            response = SurveyResponse.objects.get(
                question__code=code,
                survey=self.certification_survey
            )
            self.assertFalse(response.not_applicable)
            if code == QUESTION_CODE_IASME_INSURANCE_HEAD_OFFICE:
                self.assertTrue(response.value_boolean)
            elif code == QUESTION_CODE_IASME_INSURANCE_REVENUE:
                self.assertEqual(response.value_money, int(data.get('revenue')))
            else:
                self.assertEqual(response.value_text, data.get('email_address'))
        self.organisation_certification.refresh_from_db()
        self.assertEqual(self.organisation_certification.immediate_requested_coverage_amount, InsuranceOptIn.COVERAGE.CE_100K)

    def test_declaration_post_insurance_remain_not_opt_in_with_upgrade_available(self):
        # setup to ensure also upgrade questions would be present
        self.organisation.pricing_band = Organisation.PRICING_BUNDLE_ANNUAL
        self.organisation.save()

        anonymous_user = AnonymousUser()
        data = copy.deepcopy(self.data)
        data.update({
            'insurance_choice': 'false',
            'insurance_reason': 'We have cyber coverage at a group level',
        })
        self.response_opt_in.value_boolean = False
        self.response_opt_in.save()

        self._test_declaration_post(anonymous_user, 'dashboard:declaration-external', data)

        # Survey responses for insurance should not get updated
        self.response_opt_in.refresh_from_db()
        self.assertFalse(self.response_opt_in.value_boolean)
        for code in self.codes:
            response = SurveyResponse.objects.get(
                question__code=code,
                survey=self.certification_survey
            )
            self.assertTrue(response.not_applicable)
            if code == QUESTION_CODE_IASME_INSURANCE_HEAD_OFFICE:
                self.assertFalse(response.value_boolean)
            self.assertIsNone(response.value_text)
        # although value text for insurance opt in choice will have the reason for opting out
        self.assertEqual(self.response_opt_in.value_text, data.get('insurance_reason'))

    def test_declaration_post_insurance_no_revenue(self):
        """ CE Willow does not have revenue question """
        SurveyQuestion.objects.filter(
            version=self.organisation_certification.version,
            insurer=InsurerMixin.IASME_INSURER,
            code=QUESTION_CODE_IASME_INSURANCE_REVENUE,
        ).delete()
        anonymous_user = AnonymousUser()
        data = copy.deepcopy(self.data)
        data.update({
            'insurance_choice': 'true',
            'email_address': '<EMAIL>'
        })
        self.response_opt_in.value_boolean = False
        self.response_opt_in.save()
        self._test_declaration_post(anonymous_user, 'dashboard:declaration-external', data)
        # Survey responses for insurance are updated
        self.response_opt_in.refresh_from_db()
        self.assertTrue(self.response_opt_in.value_boolean)
        for code in self.codes:
            if code == QUESTION_CODE_IASME_INSURANCE_REVENUE:
                self.assertFalse(SurveyResponse.objects.filter(
                    question__code=code,
                    survey=self.certification_survey
                ).exists())
                continue
            response = SurveyResponse.objects.get(
                question__code=code,
                survey=self.certification_survey
            )
            self.assertFalse(response.not_applicable)
            if code == QUESTION_CODE_IASME_INSURANCE_HEAD_OFFICE:
                self.assertTrue(response.value_boolean)
            elif code == QUESTION_CODE_IASME_INSURANCE_REVENUE:
                self.assertEqual(response.value_money, int(data.get('revenue')))
            else:
                self.assertEqual(response.value_text, data.get('email_address'))

    def test_declaration_post_insurance_no_opt_in(self):
        """ When customer decides not to opt in again we save their choices in the survey response """
        anonymous_user = AnonymousUser()
        data = copy.deepcopy(self.data)
        data.update({
            'insurance_choice': 'false',
            'insurance_reason': 'We have cyber coverage at a group level',
            'other_reason': ''
        })
        self.response_opt_in.value_boolean = False
        self.response_opt_in.save()
        self._test_declaration_post(anonymous_user, 'dashboard:declaration-external', data)
        # Survey responses for insurance should not get updated
        self.response_opt_in.refresh_from_db()
        self.assertFalse(self.response_opt_in.value_boolean)
        for code in self.codes:
            response = SurveyResponse.objects.get(
                question__code=code,
                survey=self.certification_survey
            )
            self.assertTrue(response.not_applicable)
            if code == QUESTION_CODE_IASME_INSURANCE_HEAD_OFFICE:
                self.assertFalse(response.value_boolean)
            self.assertIsNone(response.value_text)
        # although value text for insurance opt in choice will have the reason for opting out
        self.assertEqual(self.response_opt_in.value_text, data.get('insurance_reason'))

    def test_declaration_post_insurance_upgrade_upgrade_answer(self):
        """ When customer decides to upgrade their insurance """
        self.organisation.pricing_band = Organisation.PRICING_BUNDLE_ANNUAL
        self.organisation.save()
        anonymous_user = AnonymousUser()
        data = copy.deepcopy(self.data)
        data.update({
            "insurance_upgrade": "true",
        })

        self._test_declaration_post(anonymous_user, 'dashboard:declaration-external', data)
        # Survey responses for insurance should get updated
        self.organisation_certification.refresh_from_db()
        self.assertEqual(self.organisation_certification.immediate_requested_coverage_amount, InsuranceOptIn.COVERAGE.CE_100K)

    def test_declaration_post_insurance_upgrade_no_upgrade_answer(self):
        """ When customer decides to upgrade their insurance """
        self.organisation.pricing_band = Organisation.PRICING_BUNDLE_ANNUAL
        self.organisation.save()
        anonymous_user = AnonymousUser()
        data = copy.deepcopy(self.data)
        data.update({
            "insurance_upgrade": "false",
        })

        self._test_declaration_post(anonymous_user, 'dashboard:declaration-external', data)
        # Survey responses for insurance should respect user decision to stay at default
        self.organisation_certification.refresh_from_db()
        self.assertEqual(self.organisation_certification.immediate_requested_coverage_amount, InsuranceOptIn.COVERAGE.CE_25K)

    def test_declaration_post_lack_of_insurance_upgrade_when_expected(self):
        self.organisation.pricing_band = Organisation.PRICING_BUNDLE_ANNUAL
        self.organisation.save()
        anonymous_user = AnonymousUser()
        data = copy.deepcopy(self.data)

        self.assertEqual(
            self.certification_survey.certificate.status,
            OrganisationCertification.AWAITING_SIGNATURE
        )
        with freeze_time('2021-01-01'):
            response = self.make_post_request(anonymous_user, 'dashboard:declaration-external', data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Please make sure that all the fields have valid values.')
        self.organisation_certification.refresh_from_db()
        self.certification_survey.refresh_from_db()
        self.assertEqual(
            self.certification_survey.certificate.status,
            OrganisationCertification.AWAITING_SIGNATURE
        )
        # Assert nothing has changed with regards to upgrade (default value)
        self.assertEqual(self.organisation_certification.immediate_requested_coverage_amount, InsuranceOptIn.COVERAGE.CE_25K)


class TestBuyCertificationAddonView(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.client = Client()
        self.org_settings = self.organisation.settings
        self.user.profile.onboarding_completed = True
        self.user.profile.save()
        self.client.force_login(self.user)
        self.org_settings = self.organisation.settings
        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()

    def _make_get(self):
        url = reverse('dashboard:buy-certificate', kwargs={'org_id': self.organisation.secure_id, 'certification_type': CYBER_ESSENTIALS})
        response = self.client.get(url)
        return response

    def test_get_view_when_organisatoin_has_certification(self):

        response = self._make_get()
        self.assertEqual(response.status_code, 302)

    def test_get_view_when_organisation_does_not_have_certification(self):
        # To go to fallback branch in the get view
        OrganisationCertification.objects.all().delete()

        response = self._make_get()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['certification_type'], CertificateType.objects.get(type=CYBER_ESSENTIALS))


class TestOrganisationDashboardTemplate(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()

    def test_get_base_app_installs_queryset(self):
        view = OrganisationDashboardTemplate()
        request = RequestFactory().get('/')
        view.setup(request)
        queryset = view.get_base_app_installs_queryset(self.organisation, show_hidden_apps=True)
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset[0].id, self.app_install.id)


class CVEViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()

        self.app_install_1 = AppInstallFactory(app_user=self.app_user, hostname="Device1")

        # Create regular software with CVEs
        self.software = SoftwarePackageFactory()
        self.software_cves = SoftwarePackageCVEsFactory(
            software=self.software,
            cves=["CVE-2023-1234"]
        )

        self.cve_repo = CVERepository.objects.create(
            cve_id="CVE-2023-1234",
            base_score=7.5,
            base_severity="High",
            description="Test CVE",
            published_at=timezone.now(),
            references=[{"url": "http://test.com/regular-cve"}]
        )

        # Create OPSWAT software with CVEs
        self.vendor = ProductVendorFactory()
        self.product = ProductFactory(vendor=self.vendor)
        self.product_version = ProductVersionFactory(product=self.product)
        self.opswat_cve = CVEFactory(
            cve_id="CVE-2023-5678",
            severity=CVE.SEVERITY_CRITICAL,
            severity_index=90,
            description="Test OPSWAT CVE",
            published_at=timezone.now(),
            details={"references": ["http://test.com/cve"]}
        )
        self.opswat_cve.product_version.add(self.product_version)

        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()

        self.client = Client()
        self.user.profile.onboarding_completed = True
        self.user.profile.save()
        self.add_people_and_org_permission_to_test_user(self.organisation)
        self.client.force_login(self.user)

    def test_regular_software_cves(self):
        response = self.client.get(
            reverse('software-cves') + f'?software_id={self.app_install_1.id}:{REGULAR_SOURCE}:{self.software.id}',
            follow=True
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['cves']), 1)
        cve = response.context['cves'][0]
        self.assertEqual(cve.cve_id, "CVE-2023-1234")
        self.assertEqual(cve.base_score, 7.5)
        self.assertEqual(cve.references[0]['url'], "http://test.com/regular-cve")

    def test_opswat_software_cves(self):
        response = self.client.get(
            reverse('software-cves') + f'?software_id={self.app_install_1.id}:{OPSWAT_SOURCE}:{self.product_version.id}',
            follow=True
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['cves']), 1)
        cve = response.context['cves'][0]
        self.assertEqual(cve['cve_id'], "CVE-2023-5678")
        self.assertEqual(cve['base_score'], 9.0)  # 90/10
        self.assertEqual(cve['base_severity'], "Critical")
        self.assertEqual(len(cve['references']), 1)
        self.assertEqual(cve['references'][0], "http://test.com/cve")


    def test_nonexistent_software_id(self):
        response = self.client.get(
            reverse('software-cves') + '?software_id=1:regular:99999',
            follow=True
        )
        self.assertEqual(response.status_code, 404)

    def test_no_software_id(self):
        response = self.client.get(
            reverse('software-cves'),
            follow=True
        )
        self.assertEqual(response.status_code, 404)


class SoftwareCSVReportViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()

        self.app_install_1 = AppInstallFactory(app_user=self.app_user, hostname="Device1")
        self.app_install_2 = AppInstallFactory(app_user=self.app_user, hostname="Device2")

        # Create regular software with CVEs, which should be ignored since we have opswat software
        self.software_vulnerable = SoftwarePackageFactory(
            vendor="Microsoft Corporation",
            product="Microsoft Edge",
            version="135.0.3179.85"
        )
        self.cve_id_1 = ["CVE-2023-1234"]
        self.software_cves = SoftwarePackageCVEsFactory(
            software=self.software_vulnerable,
            cves=self.cve_id_1
        )
        self.cve_repo = CVERepositoryFactory(
            cve_id=self.cve_id_1[0],
            base_score=7.5,
            base_severity="High",
            description="Test CVE",
            published_at=timezone.now(),
            references=[{"url": "http://test.com/regular-cve"}]
        )
        self.software_cves.cves_fk.add(*CVERepository.objects.filter(cve_id__in=self.cve_id_1))
        # Create regular software without CVEs
        self.software = SoftwarePackageFactory()

        # Create OPSWAT software with CVEs
        self.vulnerable_product_version = ProductVersionFactory()
        self.opswat_cve = CVEFactory(
            cve_id="CVE-2023-5678",
            severity=CVE.SEVERITY_CRITICAL,
            severity_index=90,
            description="Test OPSWAT CVE",
            published_at=timezone.now(),
            details={"references": [{"url": "http://test.com/cve"}]}
        )
        self.opswat_cve.product_version.add(self.vulnerable_product_version)
        # Create OPSWAT software without CVEs
        self.product_version = ProductVersionFactory()

        # add regular software to app install
        AppOSInstalledSoftwareFactory(
            report__app_install=self.app_install_1,
            software=self.software_vulnerable,
        )
        InstalledSoftwareAppInstallIndividualFactory(
            source=REGULAR_SOURCE,
            source_id=self.software_vulnerable.id,
            app_install=self.app_install_1,
            vendor=self.software_vulnerable.vendor,
            product=self.software_vulnerable.product,
            version=self.software_vulnerable.version,
            is_vulnerable=True,
        )
        AppOSInstalledSoftwareFactory(
            report__app_install=self.app_install_1,
            software=self.software,
        )
        InstalledSoftwareAppInstallIndividualFactory(
            source=REGULAR_SOURCE,
            source_id=self.software.id,
            app_install=self.app_install_1,
            vendor=self.software.vendor,
            product=self.software.product,
            version=self.software.version,
        )
        # add opswat software to app install
        installed_product = InstalledProductFactory(
            app_install=self.app_install_1
        )
        installed_product.product_versions.add(self.vulnerable_product_version)
        installed_product.product_versions.add(self.product_version)
        InstalledSoftwareAppInstallIndividualFactory(
            source=OPSWAT_SOURCE,
            source_id=self.vulnerable_product_version.id,
            app_install=self.app_install_1,
            vendor=self.vulnerable_product_version.product.vendor.name,
            product=self.vulnerable_product_version.product.name,
            version=self.vulnerable_product_version.raw_version,
            is_vulnerable=True,
        )
        InstalledSoftwareAppInstallIndividualFactory(
            source=OPSWAT_SOURCE,
            source_id=self.product_version.id,
            app_install=self.app_install_1,
            vendor=self.product_version.product.vendor.name,
            product=self.product_version.product.name,
            version=self.product_version.raw_version,
        )

        # now app_install_2 but only 1 regular software
        AppOSInstalledSoftwareFactory(
            report__app_install=self.app_install_2,
            software=self.software_vulnerable,
        )
        InstalledSoftwareAppInstallIndividualFactory(
            source=REGULAR_SOURCE,
            source_id=self.software_vulnerable.id,
            app_install=self.app_install_2,
            vendor=self.software_vulnerable.vendor,
            product=self.software_vulnerable.product,
            version=self.software_vulnerable.version,
            is_vulnerable=True,
        )
        installed_product = InstalledProductFactory(
            app_install=self.app_install_2
        )
        installed_product.product_versions.add(self.vulnerable_product_version)
        installed_product.product_versions.add(self.product_version)
        InstalledSoftwareAppInstallIndividualFactory(
            source=OPSWAT_SOURCE,
            source_id=self.vulnerable_product_version.id,
            app_install=self.app_install_2,
            vendor=self.vulnerable_product_version.product.vendor.name,
            product=self.vulnerable_product_version.product.name,
            version=self.vulnerable_product_version.raw_version,
            is_vulnerable=True,
        )
        InstalledSoftwareAppInstallIndividualFactory(
            source=OPSWAT_SOURCE,
            source_id=self.product_version.id,
            app_install=self.app_install_2,
            vendor=self.product_version.product.vendor.name,
            product=self.product_version.product.name,
            version=self.product_version.raw_version,
        )

        # now create app installs for other organisation
        app_user_from_other_org = AppUserFactory()
        self.another_org = app_user_from_other_org.organisation
        app_install_3 = AppInstallFactory(app_user=app_user_from_other_org, hostname="Device3")

        # Create regular software with CVEs
        cve_id_2 = ["CVE-2020-5678"]
        vuln_software = SoftwarePackageCVEsFactory(
            software=SoftwarePackageFactory(
                vendor="Microsoft Corporation",
                product="Microsoft OneDrive",
                version="25.056.0324.0003"
            ),
            cves=cve_id_2
        )
        CVERepositoryFactory(
            cve_id=cve_id_2[0],
            base_score=8.5,
            base_severity="High",
            description="Test CVE 2",
            published_at=timezone.now(),
            references=[{"url": "http://test.com/regular-cve"}]
        )
        vuln_software.cves_fk.add(*CVERepository.objects.filter(cve_id__in=cve_id_2))
        # add to app install
        AppOSInstalledSoftwareFactory(
            report__app_install=app_install_3,
            software=vuln_software.software,
        )
        InstalledSoftwareAppInstallIndividualFactory(
            source=REGULAR_SOURCE,
            source_id=vuln_software.software.id,
            app_install=app_install_3,
            vendor=vuln_software.software.vendor,
            product=vuln_software.software.product,
            version=vuln_software.software.version,
            is_vulnerable=True,
        )
        installed_software = AppOSInstalledSoftwareFactory(
            report__app_install=app_install_3,
            software=SoftwarePackageFactory(
                vendor="Microsoft Corporation",
                product="DirectX",
                version="12"
            ),
        )
        InstalledSoftwareAppInstallIndividualFactory(
            source=REGULAR_SOURCE,
            source_id=installed_software.software.id,
            app_install=app_install_3,
            vendor=installed_software.software.vendor,
            product=installed_software.software.product,
            version=installed_software.software.version,
        )
        # add same opswat software as in other app installs
        InstalledSoftwareAppInstallIndividualFactory(
            source=OPSWAT_SOURCE,
            source_id=self.product_version.id,
            app_install=app_install_3,
            vendor=self.product_version.product.vendor.name,
            product=self.product_version.product.name,
            version=self.product_version.raw_version,
        )

        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()

        self.client = Client()
        self.user.profile.onboarding_completed = True
        self.user.profile.save()
        self.add_people_and_org_permission_to_test_user(self.organisation)
        self.client.force_login(self.user)
        InstalledSoftwareOrganisationIndividual.refresh()

    def test_get_queryset(self):
        view = SoftwareCSVReportView()
        view.organisation = self.organisation
        self.assertCountEqual(
            view.get_queryset({}).values_list("id", flat=True),
            InstalledSoftwareOrganisationIndividual.objects.filter(
                organisation=self.organisation
            ).values_list("id", flat=True)
        )
        # make sure that the other organisation data is not in the queryset
        self.assertNotIn(
            list(InstalledSoftwareOrganisationIndividual.objects.filter(
                organisation=self.another_org
            ).values_list("id", flat=True)),
            list(view.get_queryset({}).values_list("id", flat=True)),
        )

    def test_get_queryset_filtered(self):
        view = SoftwareCSVReportView()
        view.organisation = self.organisation
        self.assertCountEqual(
            view.get_queryset({"is_vulnerable": True}).values_list("id", flat=True),
            InstalledSoftwareOrganisationIndividual.objects.filter(
                organisation=self.organisation,
                is_vulnerable=True
            ).values_list("id", flat=True)
        )

    def test_write_to_csv(self):
        view = SoftwareCSVReportView()
        view.organisation = self.organisation
        software = view.get_queryset({})
        with patch.object(view, "get_csv_writer") as mock_get_csv_writer:
            mock_writer = MagicMock()
            mock_get_csv_writer.return_value = mock_writer
        view._write_to_csv(software, mock_writer, {})
        # the writer should have been called 4 times, since we have 4 software in the organisation
        # Why 4? because we have 2 app installs, first one has 4 software, second one has 3 same software as the first
        # app install, that's why we ignore them.
        self.assertEqual(mock_writer.writerow.call_count, 4)
        software = []
        # check data is as expected
        for call_done in mock_writer.writerow.call_args_list:
            data = call_done[0][0]
            software.append(data.get('Name'))
            if data.get('Name') == self.software_vulnerable.product:
                # it will be set as non-vulnerable since we have opswat software for these app installs as well
                # so regular source will be ignored for vulnerabilities
                self.assertEqual(data.get('Status'), 'No known vulnerabilities')
                self.assertEqual(data.get('CVEs'), self.software_vulnerable.related_cves.cves_fk.all().first().cve_id)
                self.assertEqual(data.get('Version'), self.software_vulnerable.version)
                self.assertEqual(data.get('Found on'), '2 devices')
                self.assertEqual(data.get('Devices'), ", ".join([view._get_device_display_name(self.app_install_1), view._get_device_display_name(self.app_install_2)]))
            elif data.get('Name') == self.software.product:
                self.assertEqual(data.get('Status'), 'No known vulnerabilities')
                self.assertEqual(data.get('CVEs'), '')
                self.assertEqual(data.get('Version'), self.software.version)
                self.assertEqual(data.get('Found on'), '1 device')
                self.assertEqual(data.get('Devices'), view._get_device_display_name(self.app_install_1))
            elif data.get('Name') == self.vulnerable_product_version.product.name:
                self.assertEqual(data.get('Status'), 'Vulnerable')
                self.assertEqual(data.get('CVEs'), self.opswat_cve.cve_id)
                self.assertEqual(data.get('Version'), self.vulnerable_product_version.raw_version)
                self.assertEqual(data.get('Found on'), '2 devices')
                self.assertEqual(data.get('Devices'), ", ".join([view._get_device_display_name(self.app_install_1), view._get_device_display_name(self.app_install_2)]))
            elif data.get('Name') == self.product_version.product.name:
                self.assertEqual(data.get('Status'), 'No known vulnerabilities')
                self.assertEqual(data.get('CVEs'), '')
                self.assertEqual(data.get('Version'), self.product_version.raw_version)
                self.assertEqual(data.get('Found on'), '2 devices')
                self.assertEqual(data.get('Devices'), ", ".join([view._get_device_display_name(self.app_install_1), view._get_device_display_name(self.app_install_2)]))
        self.assertCountEqual(
            software,
            [self.software_vulnerable.product, self.software.product, self.product_version.product.name, self.vulnerable_product_version.product.name]
        )


class SoftWareReportViewTest(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()

        self.app_install_1 = AppInstallFactory(app_user=self.app_user, hostname="Device1")

        # Create regular software
        self.software = SoftwarePackageFactory(product="Regular Software", version="1.0")

        # Create OPSWAT software
        self.product = ProductFactory(name="OPSWAT Software")
        self.product_version = ProductVersionFactory(product=self.product, raw_version="2.0")

        # Add regular software to app install
        AppOSInstalledSoftwareFactory(
            report__app_install=self.app_install_1,
            software=self.software,
        )

        # Add OPSWAT software to app install
        self.installed_product = InstalledProductFactory(app_install=self.app_install_1)
        self.installed_product.product_versions.add(self.product_version)

        OrganisationAdmin(
            organisation=self.organisation,
            user=self.user,
            is_admin=True
        ).save()

        self.client = Client()
        self.user.profile.onboarding_completed = True
        self.user.profile.save()
        self.add_devices_permission_to_test_user(self.organisation)
        self.client.force_login(self.user)
        InstalledSoftwareOrganisationIndividual.refresh()

    def test_ajax_request_with_regular_software(self):
        """Test AJAX request for regular software details"""
        # Create a composite ID for regular software
        software_id = f"{self.organisation.id}:{REGULAR_SOURCE}:{self.software.id}"

        url = reverse('dashboard:software-report', kwargs={'org_id': self.organisation.secure_id})
        response = self.client.get(
            url,
            {'software': software_id},
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )

        self.assertEqual(response.status_code, 200)

        data = json.loads(response.content)
        self.assertIn('hosts', data)
        self.assertEqual(len(data['hosts']), 1)
        self.assertEqual(data['hosts'][0]['hostname'], 'Device1')
        self.assertIn('device_url', data['hosts'][0])

    def test_ajax_request_with_opswat_software(self):
        """Test AJAX request for OPSWAT software details (previously causing 500 error)"""
        # Create a composite ID for OPSWAT software
        software_id = f"{self.organisation.id}:{OPSWAT_SOURCE}:{self.product_version.id}"

        url = reverse('dashboard:software-report', kwargs={'org_id': self.organisation.secure_id})
        response = self.client.get(
            url,
            {'software': software_id},
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )

        self.assertEqual(response.status_code, 200)

        data = json.loads(response.content)
        self.assertIn('hosts', data)
        self.assertEqual(len(data['hosts']), 1)
        self.assertEqual(data['hosts'][0]['hostname'], 'Device1')
        self.assertIn('device_url', data['hosts'][0])

    def test_ajax_request_with_both_software_types(self):
        """Test AJAX request with both regular and OPSWAT software IDs"""
        # Create a composite ID with both regular and OPSWAT software
        software_id = f"{self.organisation.id}:{REGULAR_SOURCE}:{self.software.id},{self.organisation.id}:{OPSWAT_SOURCE}:{self.product_version.id}"

        url = reverse('dashboard:software-report', kwargs={'org_id': self.organisation.secure_id})
        response = self.client.get(
            url,
            {'software': software_id},
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )

        self.assertEqual(response.status_code, 200)

        data = json.loads(response.content)
        self.assertIn('hosts', data)
        # We should have 2 entries (one for each software type)
        self.assertEqual(len(data['hosts']), 2)
        # Both entries should be for the same device since we only have one app install
        self.assertEqual(data['hosts'][0]['hostname'], 'Device1')
        self.assertEqual(data['hosts'][1]['hostname'], 'Device1')

    def test_ajax_request_without_software_id(self):
        """Test AJAX request without specifying a software ID (should return 400)"""
        url = reverse('dashboard:software-report', kwargs={'org_id': self.organisation.secure_id})
        response = self.client.get(
            url,
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )

        self.assertEqual(response.status_code, 400)

        data = json.loads(response.content)
        self.assertIn('error', data)
        self.assertEqual(data['error'], 'Software ID is required')

    def test_non_ajax_request(self):
        """Test normal (non-AJAX) request to the view"""
        url = reverse('dashboard:software-report', kwargs={'org_id': self.organisation.secure_id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/software-report.html')
