import logging
from datetime import datetime

from django.conf import settings
from django.core.exceptions import MultipleObjectsReturned, ObjectDoesNotExist
from django.db import models
from django.urls import reverse
from model_utils.models import TimeStampedModel

from common.models import ProjectSettings, CRUDSignalMixin
from common.upload_path_creator import upload_to_purchase_orders
from organisations.models import Organisation
from rulebook.models import CYBER_ESSENTIALS, GDPR, CYBER_ESSENTIALS_PLUS
from .providers.chargebee import (
    SUBSCRIPTION_ACTIVE, SUBSCRIPTION_IN_TRIAL, SUBSCRIPTION_STATUS_CHOICES, plans,
    wrapper as chargebee,
)
from .providers.chargebee.exceptions import CardError, ChargebeeError
from .providers.chargebee.models import Customer, Subscription
from .providers.chargebee.wrapper import update_current_term_end_date
from .utils import (get_trial_end_timestamp, is_coupon_valid, get_plan_object_from_cache)

logger = logging.getLogger(__name__)


def handle_webhook_events(event):
    try:
        customer = event['content']['customer']
        partner = customer['cf_partner_name']
    except KeyError:
        logger.error("WebHook Error --> Partner name isn't set for this customer id: %s" % customer['id'])
    else:
        distributor = customer.get('cf_distributor_name')
        if partner == settings.DEFAULT_CS_PARTNER_NAME or distributor == 'Aviva':
            DirectCustomer.event_handler(event)
        else:
            PartnerBilling.event_handler(event)


class PartnerBilling(Customer):
    partner = models.OneToOneField(
        'partners.Partner', related_name='billing',
        primary_key=True, on_delete=models.CASCADE
    )

    class Meta:
        verbose_name = "Partner Chargebee Customer"
        verbose_name_plural = "Partner Chargebee Customers"

    def __str__(self):
        return self.customer_id

    @classmethod
    def create_billing(cls, partner):
        customer_data = {
            'company': partner.name,
            'cf_partner_name': partner.name,
            'cf_distributor_name': partner.distributor.name,
            'cf_account_manager': partner.get_asm_name(),
            'cf_buying_group': partner.get_buying_groups(),
        }
        if partner.is_direct_partner:
            admin = partner.users.first()
            customer_data.update({'cf_direct_partner': True})
        else:
            admin = partner.distributor.users.first()

        customer_data.update({
            'first_name': admin.user.first_name,
            'last_name': admin.user.last_name,
            'email': admin.user.email})

        if partner.jem_id:
            customer_data.update({'cf_jem_id': partner.jem_id})

        try:
            result = chargebee.create_customer(
                customer_data=customer_data,
                billing_address={'country': 'GB'},
                auto_collection=False)
        except ChargebeeError:
            pass
        else:
            return cls.objects.create(
                partner=partner,
                customer_id=result.customer.id)

    def update_chargebee_customer(self):
        customer_data = {
            'company': self.partner.name,
            'cf_partner_name': self.partner.name,
            'cf_distributor_name': self.partner.distributor.name,
            'cf_jem_id': self.partner.jem_id,
            'cf_account_manager': self.partner.get_asm_name(),
            'cf_buying_group': self.partner.get_buying_groups(),
        }

        try:
            chargebee.update_customer(self.customer_id, customer_data=customer_data)
        except ChargebeeError:
            pass

    def create_portal_session(self):
        return chargebee.create_portal_session_for_customer(self.customer_id)

    def has_plans(self, organisation, plan_ids):
        return self.subscriptions.live_subscriptions().filter(
            organisation=organisation, plan_id__in=plan_ids).exists()

    def has_certificate_plan(self, organisation, certificate_type):
        if certificate_type == CYBER_ESSENTIALS:
            plan_ids = plans.PARTNER_CE_PLANS
        elif certificate_type == GDPR:
            plan_ids = plans.PARTNER_GDPR_PLANS
        elif certificate_type == CYBER_ESSENTIALS_PLUS:
            plan_ids = plans.PARTNER_CEP_PLANS
        else:
            return False

        return self.has_plans(organisation=organisation, plan_ids=plan_ids)

    def add_certificate_plan(self, organisation, certificate_type, purchase_order=None, coupon_code=None):
        plan_id = plans.get_certificate_plan_for_pricing_band(
            pricing_band=self.partner.default_pricing_band,
            is_direct_partner=self.partner.is_direct_partner,
            certificate_type=certificate_type)
        # if the plan is Cyber Essentials Plus, then will skip subscription creation.
        # and instead will send it as log to sentry.
        if not self.has_certificate_plan(organisation=organisation, certificate_type=certificate_type):
            if certificate_type == CYBER_ESSENTIALS_PLUS:
                logger.error(f"CE Plus Issued without a subscription ---> "
                             f"Partner: {self.partner.name} Organisation: {organisation.name}")
            else:
                unit_price = plans.get_certificate_plan_price(
                    organisation, certificate_type)

                return self._create_subscription(
                    organisation=organisation,
                    invoice_immediately=True,
                    plan_id=plan_id,
                    unit_price=unit_price,
                    purchase_order=purchase_order,
                    coupon_id=coupon_code
                )

    def _get_billing_cycles(self, commitment, monthly):
        billing_cycles = None
        if commitment == Organisation.ONE_YEAR_COMMITMENT:
            billing_cycles = 1
        elif commitment == Organisation.TWO_YEAR_COMMITMENT:
            billing_cycles = 2

        if monthly:
            billing_cycles = billing_cycles * 12
        else:
            # no contracts yet for annual plans
            billing_cycles = None

        return billing_cycles

    def add_bundle_plan(self, organisation, po_number, po_file, commitment, monthly, coupon=None, chosen_bundle=None):

        if self.has_plans(organisation=organisation, plan_ids=plans.PARTNER_BUNDLE_PLANS):
            return

        plan_id = plans.get_partner_bundle_plan(organisation, monthly, chosen_bundle)
        billing_cycles = self._get_billing_cycles(commitment, monthly)

        return self._create_subscription(
            organisation=organisation,
            plan_id=plan_id,
            po_number=po_number,
            po_file=po_file,
            quantity=organisation.get_company_quantity(),
            unit_price=None,
            invoice_immediately=True,
            billing_cycles=billing_cycles,
            coupon_id=coupon
        )

    def add_custom_bundle_plan(self, organisation, bundle_certs, po_number, po_file,
                               monthly, commitment, price, licenses, coupon=None):

        if not self.has_plans(organisation=organisation,
                              plan_ids=plans.PARTNER_BUNDLE_PLANS):

            plan_id = plans.get_partner_custom_bundle_plan(
                bundle_certs=bundle_certs,
                software=organisation.software_support,
                monthly=monthly
            )

            billing_cycles = self._get_billing_cycles(
                commitment, monthly)

            return self._create_subscription(
                organisation=organisation,
                plan_id=plan_id,
                unit_price=price,
                po_number=po_number,
                po_file=po_file,
                invoice_immediately=True,
                billing_cycles=billing_cycles,
                licenses=licenses,
                coupon_id=coupon
            )

    def add_application_plan(self, organisation, po_number=None, po_file=None, coupon=None):

        if not self.has_plans(organisation=organisation,
                              plan_ids=plans.PARTNER_SOFTWARE_PLANS):
            quantity = organisation.cap_licences_quantity

            plan_id = plans.get_application_plan_for_pricing_band(
                pricing_band=self.partner.default_pricing_band,
                quantity=quantity,
                is_direct_partner=self.partner.is_direct_partner)

            unit_price = plans.get_application_plan_price(
                plan_id=plan_id, organisation=organisation)

            # if the quantity is zero will set it to one
            # due to chargebee limitations.
            # Can't create subscription with zero quantity.
            if quantity == 0:
                quantity = 1

            return self._create_subscription(**{
                'organisation': organisation,
                'plan_id': plan_id,
                'unit_price': unit_price,
                'quantity': quantity,
                'trial_end': get_trial_end_timestamp(),
                'po_number': po_number,
                'po_file': po_file,
                'coupon_id': coupon
            })

    def _create_subscription(self, plan_id, organisation,
                             unit_price, quantity=None,
                             po_number=None, po_file=None,
                             invoice_immediately=False, trial_end=0,
                             billing_cycles=None, licenses=None, purchase_order=None, coupon_id=None):
        if organisation.partner.distributor.is_insurer and is_coupon_valid(
                plans.INSURER_COUPON_CODE, plan_id):
            coupon_id = plans.INSURER_COUPON_CODE

        try:
            result = chargebee.create_subscription_for_partner_customer(
                customer_id=self.customer_id,
                plan_id=plan_id,
                unit_price=unit_price,
                quantity=quantity,
                coupon_id=coupon_id,
                po_number=po_number or (purchase_order.number if purchase_order else None),
                trial_end=trial_end,
                invoice_immediately=invoice_immediately,
                billing_cycles=billing_cycles,
                cf_organisation=organisation.name,
                cf_partners_organisation=organisation.is_partner_org,
                cf_partner_name=organisation.partner.name)
        except ChargebeeError as e:
            logger.error(f"Failed to create subscription in Chargebee for org {organisation.name}: {e}")
            pass
        else:
            if trial_end > 0:
                status = SUBSCRIPTION_IN_TRIAL
            else:
                status = SUBSCRIPTION_ACTIVE

            kwargs = {
                'organisation': organisation,
                'status': status,
                'sub_id': result.subscription.id,
                'plan_id': result.subscription.plan_id,
                'next_billing_at': datetime.fromtimestamp(result.subscription.next_billing_at),
                'started_at': datetime.fromtimestamp(result.subscription.started_at),
                'plan_amount': result.subscription.plan_amount / 100,
                'plan_unit_price': result.subscription.plan_unit_price / 100,
                'plan_free_quantity': result.subscription.plan_free_quantity,
                'plan_quantity': result.subscription.plan_quantity,
            }

            if hasattr(result.subscription, 'current_term_end') and result.subscription.current_term_end:
                kwargs.update({'current_term_end_date': datetime.fromtimestamp(result.subscription.current_term_end)})
            if hasattr(result.subscription, 'contract_term') and result.subscription.contract_term:
                if hasattr(result.subscription.contract_term, 'contract_end')\
                        and result.subscription.contract_term.contract_end:
                    kwargs.update({
                        'contract_term_end_date':
                            datetime.fromtimestamp(result.subscription.contract_term.contract_end)
                    })
            subscription = self.subscriptions.create(**kwargs)
            if po_number and not purchase_order:
                PurchaseOrder.objects.update_or_create(
                    number=po_number,
                    subscription=subscription,
                    defaults={
                        'file': po_file,
                    }
                )
            # in case the purchase order already exists link the created subscription and remove linked org
            if purchase_order:
                purchase_order.subscription = subscription
                purchase_order.organisation = None
                purchase_order.save(update_fields=['subscription', 'organisation', 'modified'])
            return subscription


class PartnerSubscription(Subscription, CRUDSignalMixin):
    billing = models.ForeignKey(
        PartnerBilling, related_name='subscriptions',
        on_delete=models.CASCADE, null=True, blank=True)
    organisation = models.ForeignKey(
        'organisations.Organisation',
        related_name='subscriptions', on_delete=models.CASCADE)

    class Meta:
        verbose_name = "Partner Chargebee Subscription"
        verbose_name_plural = "Partner Chargebee Subscriptions"

    def __str__(self):
        return self.sub_id

    @property
    def po_number(self):
        """ Gets the latest PO number if it has one """
        return self.purchase_orders.order_by('id').last().number if hasattr(self, 'purchase_orders') else None

    def update_plan(self, plan_id, quantity=None, unit_price=None, end_term=False):
        result = chargebee.update_subscription_plan(
            sub_id=self.sub_id, plan_id=plan_id,
            quantity=quantity, unit_price=unit_price, end_term=end_term)
        update_fields = {
            'plan_id': result.subscription.plan_id,
            'plan_amount': result.subscription.plan_amount / 100,
            'plan_unit_price': result.subscription.plan_unit_price / 100,
            'plan_free_quantity': result.subscription.plan_free_quantity,
            'plan_quantity': result.subscription.plan_quantity,
            'next_billing_at': datetime.fromtimestamp(result.subscription.next_billing_at),
            'current_term_end_date': datetime.fromtimestamp(result.subscription.current_term_end),
        }
        for key, value in update_fields.items():
            setattr(self, key, value)
        self.save(update_fields=update_fields.keys())

    def update_quantity(self):
        quantity = self.organisation.cap_licences_quantity

        if quantity == 0:
            return chargebee.pause_subscription(self.sub_id)
        else:
            unit_price = plans.get_application_plan_price(plan_id=self.plan_id, organisation=self.organisation)

            self.update_subscription_quantity_and_unit_price(quantity, unit_price)

    def send_notification(self, message_type):
        """ Send notification """
        from notifications.handlers import NotificationHandler
        kwargs = {
            'user_name': '',
            'organisation_id': self.organisation.id,
            'organisation_name': self.organisation.name,
            'url': reverse('partners:subscriptions-overview'),
        }
        if message_type == "subscription_changes_partners":
            kwargs.update({
                "partner_name": self.organisation.partner.name,
                "url": reverse("distributors:subscriptions-overview")
            })
        NotificationHandler.send_notification(message_type=message_type, **kwargs)

    def add_grace_period(self, new_current_term_end_date: datetime):
        """ Update current term end date as part of adding grace period """
        if self.current_term_end_date == new_current_term_end_date.date():
            return
        current_term_end_date = update_current_term_end_date(self.sub_id, new_current_term_end_date.timestamp())
        self.added_grace_period = True
        self.current_term_end_date = current_term_end_date
        self.save(update_fields=['added_grace_period', 'current_term_end_date'])

    @property
    def evaluation_in_trial(self):
        """ When CAP subscription is not technically in evaluation (quantity > 3) but the subscription only becomes
        active at the end of the month.
        So subscription is still in trial therefore we can show as Evaluation until it becomes active and billed """
        if self.organisation.partner.distributor.enable_evaluation_licenses and self.organisation.cap_licences_quantity > 3 \
                and self.organisation.pricing_band == Organisation.PRICING_MONTHLY_V4 and self.in_trial:
            return True
        else:
            return False


class PartnerSubscriptionDetail(TimeStampedModel):
    BILLED = 'Billed'
    NFR = 'NFR'
    TRIAL = 'Trial'
    EVAL = 'Eval'
    LICENSE_TYPE_CHOICES = (
        (BILLED, 'Billed'),
        (NFR, 'Not for resale'),
        (TRIAL, 'Partner trial'),
        (EVAL, 'Evaluation'),
    )
    partner_subscription = models.OneToOneField(
        PartnerSubscription, related_name='subscription_detail',
        on_delete=models.CASCADE, primary_key=True)
    software_license_type = models.CharField(
        verbose_name='Software License Type', max_length=6, choices=LICENSE_TYPE_CHOICES, default='Billed'
    )
    software_license_count = models.IntegerField(
        verbose_name='Software License Count', null=True, blank=True
    )
    software_license_price = models.DecimalField(
        verbose_name='Software License Price', max_digits=6, decimal_places=2, blank=True, null=True
    )


class PartnerBillingHistory(models.Model):
    distributor = models.ForeignKey(
        'distributors.Distributor',
        related_name='billing_history',
        on_delete=models.CASCADE)
    partner = models.ForeignKey(
        'partners.Partner',
        related_name='billing_history',
        on_delete=models.CASCADE)
    organisation = models.ForeignKey(
        'organisations.Organisation',
        related_name='billing_history',
        on_delete=models.CASCADE)
    subscription = models.ForeignKey(
        PartnerSubscription, related_name='subscription_history',
        on_delete=models.CASCADE, null=True, blank=True)
    subscription_status = models.IntegerField(
        choices=SUBSCRIPTION_STATUS_CHOICES,
        null=True, blank=True)
    plan_id = models.CharField(max_length=100, null=True, blank=True)
    num_of_apps = models.PositiveIntegerField(
        'Number of Installed Apps', blank=True, null=True)
    price_per_app = models.DecimalField(
        max_digits=6, decimal_places=2, blank=True, null=True)
    total_price = models.DecimalField(
        max_digits=7, decimal_places=2, blank=True, null=True)
    date = models.DateField(auto_now_add=True)

    class Meta:
        verbose_name = "Partner's Billing History"
        verbose_name_plural = "Partners' Billing History"

    def __str__(self):
        return self.organisation.name

    @staticmethod
    def create_instance(organisation):
        subscription = None
        subscription_status = None
        price_per_app = None
        total_price = None
        plan_id = None
        quantity = organisation.cap_licences_quantity
        try:
            if organisation.has_any_bundle:
                subscription = organisation.subscriptions.partner_bundle_subscription()
                price_per_app = None
            else:
                subscription = organisation.subscriptions.partner_app_subscription()
                price_per_app = subscription.plan_unit_price
            total_price = subscription.plan_amount
            subscription_status = subscription.status
            # setting details based on subscription should be the most accurate
            plan_id = subscription.plan_id
        except ObjectDoesNotExist:
            if quantity > 0 and organisation.software_support:
                logger.error(
                    "PartnerBillingHistory creation: "
                    f"software subscription doesn't exist for Organisation: {organisation.name}",
                    extra={'Software Installs': quantity}
                )
        except MultipleObjectsReturned:
            logger.error(
                "PartnerBillingHistory creation: "
                f"multiple software subscriptions for Organisation: {organisation.name}"
            )

        return PartnerBillingHistory(
            distributor=organisation.partner.distributor,
            partner=organisation.partner,
            organisation=organisation,
            subscription=subscription,
            subscription_status=subscription_status,
            plan_id=plan_id,
            num_of_apps=quantity,
            price_per_app=price_per_app,
            total_price=total_price,
        )


class DirectCustomer(Customer):
    """
    This model represents Chargebee customer
    """
    organisation = models.OneToOneField(
        'organisations.Organisation', related_name='customer',
        primary_key=True, on_delete=models.CASCADE
    )

    class Meta:
        verbose_name = "Direct Chargebee Customer"
        verbose_name_plural = "Direct Chargebee Customers"

    def __unicode__(self):
        return self.customer_id

    def __str__(self):
        return self.__unicode__()

    @classmethod
    def create_customer(cls, organisation, meta_data):
        customer = organisation.get_organisation_creator

        if not organisation.has_billing:
            customer_data = {
                "first_name": customer.first_name,
                "last_name": customer.last_name,
                "email": customer.email,
                "company": organisation.name,
                "cf_partner_name": organisation.partner.name,
                "cf_distributor_name": organisation.partner.distributor.name,
                "cf_is_direct_customer": True,
                "allow_direct_debit": True
            }
            result = chargebee.create_customer(
                customer_data=customer_data,
                billing_address={'country': meta_data.get('country')},
                meta_data=meta_data)

            instance = cls.objects.create(
                organisation=organisation,
                customer_id=result.customer.id,
            )

            # Check if the organisation is Aviva customer
            cls.set_as_aviva_customer(meta_data.get("coupon_code"), organisation)
        else:
            instance = organisation.customer

        return instance

    @staticmethod
    def set_as_aviva_customer(coupon_code, organisation):
        """
        If coupon code belongs to aviva broker sets the organisation as aviva customer.
        :param coupon_code: payment coupon code
        :type coupon_code: str
        :param organisation: onboarded organisation
        :type organisation: organisations.Organisation
        :return: nothing
        """
        if coupon_code and isinstance(coupon_code, str):
            if coupon_code in plans.AVIVA_COUPONS or coupon_code.lower() in ProjectSettings.get_aviva_journey_coupons():
                organisation.set_organisation_as_aviva_customer()

    def update_chargebee_customer(self):
        customer_data = {
            "company": self.organisation.name,
            "cf_partner_name": self.organisation.partner.name,
            "cf_distributor_name": self.organisation.partner.distributor.name,
            "cf_is_direct_customer": True
        }
        try:
            chargebee.update_customer(
                self.customer_id, customer_data=customer_data)
        except ChargebeeError:
            pass

    @property
    def has_payment_method(self):
        return chargebee.is_customer_added_payment_method(self.customer_id)

    @property
    def num_of_installed_apps(self):
        from appusers.models import AppInstall
        return AppInstall.objects.filter(
            id__in=self.organisation.active_app_installs
        ).installs_without_trustd_mobile_duplicates().count()

    @property
    def num_of_enrolled_users(self):
        from appusers.models import AppUser
        return AppUser.objects.filter(organisation__id=self.organisation_id, active=True).count()

    def create_subscription_legacy(self, intent_id, addon_id=None, cert_plan=None, coupon_code=None):

        if not is_coupon_valid(coupon_code, addon_id):
            coupon_code = None

        if addon_id:
            self.add_software_plan(addon_id=addon_id, intent_id=intent_id, coupon_code=coupon_code)
            if cert_plan:
                try:
                    self.add_certificate_plan(cert_plan_id=cert_plan, coupon_code=coupon_code)
                except CardError:
                    # Note: If the customer subscribed for software and certification
                    # and the payment failed in the second charge will add the charges to the unbilled charges
                    self.add_certificate_plan(
                        cert_plan_id=cert_plan, coupon_code=coupon_code, invoice_immediately=False)

        if cert_plan and not addon_id:
            self.add_certificate_plan(cert_plan_id=cert_plan, intent_id=intent_id, coupon_code=coupon_code)

    def create_subscription(self, intent_id=None, plan_id=None, addon_id=None, cert_plan=None, coupon_code=None):
        """
        Creates a subscription in Chargebee for passed plan ID and applies to it a coupon code if passed.
        :param intent_id: payment intent (3DS)
        :type intent_id: object
        :param plan_id: Chargebee plan ID
        :type plan_id: str
        :param addon_id: addon id (legacy)
        :type addon_id: str
        :param cert_plan: cert plan (legacy)
        :type cert_plan: str
        :param coupon_code: coupon code
        :type coupon_code: str
        :return: nothing
        :rtype: None
        """
        if plan_id:
            coupon_code = None if not is_coupon_valid(coupon_code, plan_id) else coupon_code

            # create a new subscription if there are no subscriptions with passed plan ID yet
            if not chargebee.get_live_customer_subscription(self.customer_id, plan_id):
                subscription_data = {
                    "customer_id": self.customer_id,
                    "plan_id": plan_id,
                    "coupon_id": coupon_code
                }
                if intent_id:
                    subscription_data["intent_id"] = intent_id
                if (plan_id not in plans.DIRECT_CUSTOMER_V4_FLAT_FEE_PLANS
                        and plan_id not in plans.DIRECT_CUSTOMER_V6_SOFTWARE_PLANS):
                    # if plan is not Flat Fee add plan quantity
                    subscription_data["plan_quantity"] = self.organisation.get_company_quantity()
                try:
                    subscription_data["invoice_immediately"] = True

                    result = chargebee.create_subscription_for_customer(**subscription_data)
                except CardError:
                    subscription_data["invoice_immediately"] = False
                    result = chargebee.create_subscription_for_customer(**subscription_data)

                # create local subscription
                self.subscriptions.create(
                    sub_id=result.subscription.id,
                    plan_id=result.subscription.plan_id,
                    next_billing_at=datetime.fromtimestamp(
                        result.subscription.next_billing_at),
                    started_at=datetime.fromtimestamp(result.subscription.started_at),
                    plan_amount=result.subscription.plan_amount / 100,
                    plan_unit_price=result.subscription.plan_unit_price / 100,
                    plan_free_quantity=result.subscription.plan_free_quantity,
                    plan_quantity=result.subscription.plan_quantity,
                )
        else:
            # legacy
            self.create_subscription_legacy(intent_id, addon_id, cert_plan, coupon_code)

    def add_software_plan(self, addon_id, intent_id=None, coupon_code=None, invoice_immediately=True):
        plan_id = plans.get_plan_for_addon(addon_id)

        if not chargebee.get_live_customer_subscription(self.customer_id, plan_id):
            addon_quantity = self.organisation.get_initial_quantity()

            result = chargebee.create_subscription_for_customer(
                customer_id=self.customer_id,
                plan_id=plan_id,
                addon_id=addon_id,
                intent_id=intent_id,
                addon_quantity=addon_quantity,
                coupon_id=coupon_code,
                invoice_immediately=invoice_immediately)

            self.subscriptions.create(
                sub_id=result.subscription.id,
                plan_id=result.subscription.plan_id,
                addon_id=addon_id,
                next_billing_at=datetime.fromtimestamp(result.subscription.next_billing_at),
                started_at=datetime.fromtimestamp(result.subscription.started_at),
                plan_amount=result.subscription.plan_amount / 100,
                plan_unit_price=result.subscription.plan_unit_price / 100,
                plan_free_quantity=result.subscription.plan_free_quantity,
                plan_quantity=result.subscription.plan_quantity,
            )

    def add_certificate_plan(self, cert_plan_id, intent_id=None, coupon_code=None, invoice_immediately=True):

        if not chargebee.get_live_customer_subscription(self.customer_id, cert_plan_id):
            subscriptions_data = {
                "customer_id": self.customer_id,
                "plan_id": cert_plan_id,
                "intent_id": intent_id,
                "coupon_id": coupon_code,
                "invoice_immediately": invoice_immediately
            }
            if self.organisation.is_direct_customer_v4:
                if cert_plan_id in plans.DIRECT_CUSTOMER_V4_V5_STAIR_STEP_PLANS:
                    # if plan type is stair step add plan quantity
                    subscriptions_data["plan_quantity"] = self.organisation.get_company_quantity()

            result = chargebee.create_subscription_for_customer(**subscriptions_data)
            self.subscriptions.create(
                sub_id=result.subscription.id,
                plan_id=result.subscription.plan_id,
                next_billing_at=datetime.fromtimestamp(
                    result.subscription.next_billing_at),
                started_at=datetime.fromtimestamp(result.subscription.started_at),
                plan_amount=result.subscription.plan_amount / 100,
                plan_unit_price=result.subscription.plan_unit_price / 100,
                plan_free_quantity=result.subscription.plan_free_quantity,
                plan_quantity=result.subscription.plan_quantity,
            )

    def add_gdpr_plan(self, cert_plan_id, intent_id=None):
        if not self.organisation.is_direct_customer_v4:
            if not self.organisation.has_ce_certification:  # pylint: disable=no-member
                if cert_plan_id not in plans.CERT_MONTHLY_PLANS_IDS:
                    cert_plan_id = plans.CE_GDPR_PLAN_ANNUAL_ID
                else:
                    cert_plan_id = plans.CE_GDPR_PLAN_MONTHLY_ID

        self.add_certificate_plan(cert_plan_id, intent_id)

    @property
    def current_certification_plan(self):
        """
        Returns first certification subscription.
        :return: first certification subscription
        :rtype: chargebee.DirectSubscription
        """
        return self.subscriptions.live_subscriptions().filter(
            plan_id__in=plans.CERT_PLANS_IDS + plans.DIRECT_CUSTOMER_V4_V5_PLANS).first()

    def create_portal_session(self):
        return chargebee.create_portal_session_for_customer(self.customer_id)

    def delete_customer(self):
        """deletes the customer from chargebee and customer model"""
        chargebee.delete_customer(self.customer_id)  # pylint: disable=no-member
        self.delete()

    def has_recently_added_payment_method(self):
        return chargebee.is_customer_recently_added_payment_method(self.customer_id)

    def get_customer_details(self):
        return chargebee.get_customer(self.customer_id)


class DirectSubscription(Subscription):
    """
    This model represents Chargebee subscriptions
    """
    customer = models.ForeignKey(
        DirectCustomer, related_name='subscriptions', null=True, blank=True, on_delete=models.CASCADE
    )

    class Meta:
        verbose_name = "Direct Chargebee Subscription"
        verbose_name_plural = "Direct Chargebee Subscriptions"

    def __unicode__(self):
        return self.sub_id

    def __str__(self):
        return self.__unicode__()

    @property
    def addon_quantity(self):
        # NOTE: this line cannot be modified from the below
        # if self.addon_id not in plans.ADDONS_IDS and self.addon_id != 'cybersmart-monthly-channel':
        if self.addon_id not in plans.ADDONS_IDS and self.addon_id != 'cybersmart-monthly-channel':
            return 0
        else:
            if self.customer.organisation.bulk_install:
                return self.customer.num_of_installed_apps
            else:
                return self.customer.num_of_enrolled_users

    def update_addon(self, new_addon_id):
        plan_id = plans.get_plan_for_addon(new_addon_id)
        self.plan_id = plan_id
        self.addon_id = new_addon_id

        if self.addon_quantity == 0:
            chargebee.remove_subscription_addon(self.sub_id)
        else:
            chargebee.update_subscription(
                sub_id=self.sub_id,
                plan_id=plan_id,
                new_addon_id=new_addon_id,
                addon_quantity=self.addon_quantity)

        self.save()

    def update_plan_and_quantity(self, new_plan_id):
        """
        Changes subscription plan to a new one and updates quantity
        :param new_plan_id: new plan ID
        :type new_plan_id: str
        :return: nothing
        :rtype: None
        """
        result = chargebee.update_subscription_plan_and_quantity(
            sub_id=self.sub_id,
            plan_id=new_plan_id,
            plan_quantity=self.customer.organisation.get_company_quantity()
        )
        fields_to_update = {
            'plan_id': new_plan_id,
            'plan_amount': result.subscription.plan_amount / 100,
            'plan_unit_price': result.subscription.plan_unit_price / 100,
            'plan_free_quantity': result.subscription.plan_free_quantity,
            'plan_quantity': result.subscription.plan_quantity,
        }
        for key, value in fields_to_update.items():
            setattr(self, key, value)
        self.save(update_fields=fields_to_update.keys())

    def update_quantity(self):
        # v6 quantity updates
        v6_quantity = self.customer.organisation.v6_quantity
        if v6_quantity > 0 and self.plan_id in plans.DIRECT_CUSTOMER_V6_SOFTWARE_PLANS:
            # set to zero to avoid creating false MRR
            unit_price = 0
            # check if the customer has set up billing, skip Superscript customers
            if not self.customer.has_payment_method and not self.customer.organisation.is_superscript_customer:
                # get plan details from cache or fetch new
                plan_details = get_plan_object_from_cache(self.plan_id)
                # if customer has gone over the limit, they need to be forced to add a payment method
                if plan_details and v6_quantity > int(plan_details.get('free_quantity', 3)):
                    for user in self.customer.organisation.get_admin_users:
                        # set all the admin users to failed payment
                        if not user.profile.payment_failed:
                            user.profile.payment_failed = True
                            user.profile.save()
            elif self.customer.has_payment_method:
                # billing is set up, revert to original price
                unit_price = None

            self.update_subscription_quantity_and_unit_price(v6_quantity, unit_price)

    def update_addon_quantity(self, quantity):
        if self.addon_quantity == 0:
            chargebee.remove_subscription_addon(self.sub_id)
        else:
            chargebee.update_subscription_addon_quantity(
                sub_id=self.sub_id,
                addon_id=self.addon_id,
                quantity=quantity,
                invoice_immediately=False)

    def get_subscription_amount_estimate(self, plan_id, coupon=None, size=None):
        """ Returns the subscription amount estimate """
        customer_details = self.customer.get_customer_details()
        return chargebee.get_subscription_estimates(
            plan_quantity=self.customer.organisation.get_company_quantity(size),
            plan_id=plan_id,
            coupon_id=coupon,
            billing_address={"country": customer_details.billing_address.country}
        )

    def apply_subscription_coupon(self, coupon_code):
        chargebee.update_subscription_coupon_code(
            sub_id=self.sub_id,
            coupon_ids=[coupon_code]
        )


class PurchaseOrder(TimeStampedModel):
    """
    This model represents a Purchase order for a partner subscription.
    Note that a subscription will have many POs across time, due to renewals.
    """
    number = models.CharField(verbose_name='Purchase order number', blank=False, max_length=100)
    file = models.FileField(upload_to=upload_to_purchase_orders, null=True, blank=True, max_length=1000)
    subscription = models.ForeignKey(PartnerSubscription, related_name='purchase_orders', on_delete=models.CASCADE,
                                     null=True, blank=True)
    # currently this is only used when creating a certifications only organisation,
    # since the subscription is only created when the certification is started.
    organisation = models.ForeignKey(Organisation, related_name='purchase_orders', on_delete=models.CASCADE,
                                     null=True, blank=True, help_text='Only when no subscription is linked')

    class Meta:
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return f'{self.subscription}-{self.number}'
