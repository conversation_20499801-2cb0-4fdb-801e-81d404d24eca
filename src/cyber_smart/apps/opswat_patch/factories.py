import factory
from factory.django import DjangoModelFactory
from django.utils import timezone

from appusers.models.factories import AppInstallFactory
from opswat.factories import ProductFactory, ProductSignatureFactory
from opswat_patch.models.constants import ACTION_STATUS_CHOICES

class OpswatSchemaVersionFactory(DjangoModelFactory):
    class Meta:
        model = 'opswat_patch.OpswatSchemaVersion'

    schema_version = factory.Sequence(lambda n: f"1.0-{n}")


class OpswatArchitectureFactory(DjangoModelFactory):
    class Meta:
        model = 'opswat_patch.OpswatArchitecture'
        django_get_or_create = ('name',)

    name = factory.Iterator(['x86_32', 'x86_64', 'x86_all', 'arm', 'arm64', 'arm_all'])


class OpswatPatchInstallerFactory(DjangoModelFactory):
    class Meta:
        model = 'opswat_patch.OpswatPatchInstaller'

    product_name = factory.Sequence(lambda n: f"Product {n}")
    opswat_id = factory.Sequence(lambda n: f"patch_installer_id_{n}")
    vulnerabilities = factory.LazyFunction(list)
    release_note_link = factory.Faker('url')
    eula_link = factory.Faker('url')
    latest_version = factory.Sequence(lambda n: f"1.{n}.0")
    language_default = "en-US"
    fresh_installable = False
    release_date = factory.LazyFunction(timezone.now().date)
    requires_reboot = False
    requires_uninstall_first = False
    schema_version = factory.SubFactory(OpswatSchemaVersionFactory)


class OpswatPatchInstallerDownloadLinkFactory(DjangoModelFactory):
    class Meta:
        model = 'opswat_patch.OpswatPatchInstallerDownloadLink'

    patch_installer = factory.SubFactory(OpswatPatchInstallerFactory)
    os_architecture = factory.SubFactory(OpswatArchitectureFactory)
    architecture = factory.Iterator(['32-bit', '64-bit', 'arm64'])
    language = factory.Iterator(['en-US', 'fr', 'de'])
    link = factory.Faker('url')
    os_ids = factory.LazyFunction(list)


class OpswatProductPatchInstallerFactory(DjangoModelFactory):
    class Meta:
        model = 'opswat_patch.OpswatProductPatchInstaller'

    product = factory.SubFactory(ProductFactory)
    patch_installer = factory.SubFactory(OpswatPatchInstallerFactory)
    schema_version = factory.SubFactory(OpswatSchemaVersionFactory)
    title = factory.Sequence(lambda n: f"Product Patch Installer {n}")
    opswat_id = factory.Sequence(lambda n: f"opswat_id_{n}")
    v4_signatures = factory.LazyFunction(list)
    version_pattern = ""
    ranges = factory.LazyFunction(list)
    comment = ""
    is_latest = False
    os_deny = ""
    os_allow = ""
    architecture = factory.SubFactory(OpswatArchitectureFactory)
    channel_pattern = ""


class OpswatProductPatchInstallerSignatureFactory(DjangoModelFactory):
    class Meta:
        model = 'opswat_patch.OpswatProductPatchInstallerSignature'

    patch_installer = factory.SubFactory(OpswatProductPatchInstallerFactory)
    signature = factory.SubFactory(ProductSignatureFactory)


class OpswatScheduledProductInstallerFactory(DjangoModelFactory):
    class Meta:
        model = 'opswat_patch.OpswatScheduledProductInstaller'

    opswat_product_patch_installer = factory.SubFactory(OpswatProductPatchInstallerFactory)
    signature = factory.SubFactory(ProductSignatureFactory)
    app_install = factory.SubFactory(AppInstallFactory)
    creator = factory.SubFactory('accounts.factories.UserFactory')


class OpswatPatchJobFactory(DjangoModelFactory):
    class Meta:
        model = "opswat_patch.OpswatPatchJob"

    organisation = factory.SubFactory("organisations.factories.OrganisationFactory")
    initiated_by = factory.SubFactory("accounts.factories.UserFactory")


class OpswatPatchAttemptFactory(DjangoModelFactory):
    class Meta:
        model = 'opswat_patch.OpswatPatchAttempt'

    patch_job = factory.SubFactory(OpswatPatchJobFactory)
    scheduled_product_installer = factory.SubFactory(OpswatScheduledProductInstallerFactory)
    status = "pending"  # Use default status instead of Iterator to avoid random statuses in tests
    initiated_by = factory.SubFactory('accounts.factories.UserFactory')
    from_version = factory.Sequence(lambda n: f"1.{n}")
    to_version = factory.Sequence(lambda n: f"1.{n + 1}")
    created = factory.LazyFunction(timezone.now)
    modified = factory.LazyFunction(timezone.now)


class OpswatPatchEventLogFactory(DjangoModelFactory):
    class Meta:
        model = 'opswat_patch.OpswatPatchEventLog'

    opswat_scheduled_product_installer = factory.SubFactory(OpswatScheduledProductInstallerFactory)
    patch_attempt = factory.SubFactory(OpswatPatchAttemptFactory)
    status = factory.Iterator([choice[0] for choice in ACTION_STATUS_CHOICES])
    details = factory.Faker('sentence')

