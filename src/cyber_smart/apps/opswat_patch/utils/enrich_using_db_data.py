"""
Enrichment utilities for reading pre-stored OPSWAT patch installer data.

This module provides functions for enriching installed software records with
pre-calculated patch installer information that has been stored in the database.

Key features:
- This module reads pre-stored enrichment data rather than calculating it dynamically
- Works with records that already have opswat_product_patch_installer relationships
- Used when the 'opswat-enrichment-offline' feature flag is enabled
"""
from typing import Union, List, Set
from collections import defaultdict
from django.db.models import QuerySet

from opswat_patch.models import OpswatScheduledProductInstaller
from opswat.models import InstalledProductVersion
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from opswat_patch.utils.types import (
    AppInstallProductInstallerDict,
    AppInstallProductInstallerDynamicDict,
    AppInstallProductInstallersByAppInstallDynamic,
)


def _prefetch_scheduled_installers_for_app_install(app_install_ids: set[int], installer_ids: Set[int]) -> dict[int, Set[int]]:
    """
    Pre-fetch scheduled installer IDs for specific app_installs.
    Returns a dict mapping app_install_id to set of scheduled installer IDs for that app_install.
    """
    if not app_install_ids or not installer_ids:
        return {}

    scheduled_tuples = set(
        OpswatScheduledProductInstaller.non_terminal.filter(
            app_install__id__in=app_install_ids,
            opswat_product_patch_installer_id__in=installer_ids
        ).values_list('opswat_product_patch_installer_id', 'app_install_id')
    )

    scheduled_installers_for_app_install_id = defaultdict(set)
    for installer_id, app_install_id in scheduled_tuples:
        scheduled_installers_for_app_install_id[app_install_id].add(installer_id)

    return dict(scheduled_installers_for_app_install_id)


def get_enriched_installed_software_with_installers_from_db(
    installed_software: Union[QuerySet[Union[InstalledSoftwareAppInstallIndividual, InstalledSoftwareOrganisationIndividual]], List[Union[InstalledSoftwareAppInstallIndividual, InstalledSoftwareOrganisationIndividual]]]
) -> List[Union[InstalledSoftwareAppInstallIndividual, InstalledSoftwareOrganisationIndividual]]:
    """
    Enrich installed software with patch installer information using database-stored data only.
    Skips records without stored enrichment data.
    """
    if not installed_software:
        return []

    # Convert to list if QuerySet and prefetch related data
    if not isinstance(installed_software, list):
        installed_software = installed_software.select_related(
             'opswat_product_patch_installer__patch_installer',
             'opswat_product_patch_installer__product'
        ).prefetch_related(
            'opswat_product_patch_installer__signatures'
        )
        installed_software = list(installed_software)

    is_app_install_level = isinstance(installed_software[0], InstalledSoftwareAppInstallIndividual)
    if is_app_install_level:
        return _get_enriched_installed_software_with_installers_from_db_app_install_level(installed_software)
    return get_enriched_installed_software_with_installers_from_db_org_level(installed_software)


def _get_enriched_installed_software_with_installers_from_db_app_install_level(
    installed_software: List[InstalledSoftwareAppInstallIndividual],
) -> List[InstalledSoftwareAppInstallIndividual]:

    # Get scheduled installer IDs in bulk for determining is_scheduled status
    installer_ids = {r.opswat_product_patch_installer_id for r in installed_software if r.opswat_product_patch_installer}

    app_install_ids = set()
    for record in installed_software:
        app_install_ids.add(record.app_install_id)
    scheduled_installers_for_app_install_id = _prefetch_scheduled_installers_for_app_install(app_install_ids, installer_ids)

    # Build enrichment from stored database data
    enriched_apps = []
    for record in installed_software:
        installer = record.opswat_product_patch_installer
        if not installer:
            enriched_apps.append(record)
            continue
        is_scheduled = record.app_install_id in scheduled_installers_for_app_install_id and installer.id in scheduled_installers_for_app_install_id[record.app_install_id]

        # Build the main installer info using stored data
        installer_data: AppInstallProductInstallerDict = {
            'app_install_product_installer_id': installer.id,
            'title': installer.title,
            'is_scheduled': is_scheduled,
            'installer_version': installer.patch_installer.latest_version,
            'patch_detected_date': record.patch_detected_date,
        }
        record.app_install_product_installer = AppInstallProductInstallerDict(installer_data)
        enriched_apps.append(record)

    return enriched_apps


def get_enriched_installed_software_with_installers_from_db_org_level(
    installed_software: List[InstalledSoftwareOrganisationIndividual],
) -> List[InstalledSoftwareOrganisationIndividual]:
    # First, get all InstalledProductVersion objects using aggregated_installed_product_version_ids
    all_ipv_ids = []
    for record in installed_software:
        if hasattr(record, 'aggregated_installed_product_version_ids') and record.aggregated_installed_product_version_ids:
            all_ipv_ids.extend(record.aggregated_installed_product_version_ids)
    if not all_ipv_ids:
        return list(installed_software)
    # Fetch all IPVs in one query
    all_ipvs = list(InstalledProductVersion.objects.filter(id__in=all_ipv_ids).select_related(
        'product_version__product', 'installed_product__app_install')
    )

    # Extract product IDs from the records that have installers
    product_ids: Set[int] = set()
    for record in installed_software:
        if record.opswat_product_patch_installer:
            product_ids.add(record.opswat_product_patch_installer.product_id)

    from opswat_patch.utils.calculate_enrich_org_level import _prefetch_scheduled_installers_for_org_level
    scheduled_set = _prefetch_scheduled_installers_for_org_level(all_ipvs, product_ids)

    # Build enrichment from stored database data
    enriched_apps = []
    for record in installed_software:
        installer = record.opswat_product_patch_installer
        if not installer:
            enriched_apps.append(record)
            continue

        # Check if all individual app_installs are scheduled
        is_scheduled = True  # Start with True, will be set to False if any are not scheduled
        # Add individual install mappings for org level
        if (hasattr(record, 'app_install_product_installers_by_app_install') and
            record.app_install_product_installers_by_app_install):
            # Use stored individual mappings and update is_scheduled status
            app_install_product_installers_by_app_install_dynamic: AppInstallProductInstallersByAppInstallDynamic = {}
            for app_install_id, installer_id in record.app_install_product_installers_by_app_install.items():
                if installer_id and installer:
                    is_scheduled_individual = (int(app_install_id), installer.product_id) in scheduled_set
                else:
                    is_scheduled_individual = False

                if not is_scheduled_individual:
                    is_scheduled = False

                dynamic_data: AppInstallProductInstallerDynamicDict = {
                    'app_install_product_installer_id': installer_id,
                    'is_scheduled': is_scheduled_individual,
                    # Not stored in this path as it's not used.
                    'patch_detected_date': None,
                }
                app_install_product_installers_by_app_install_dynamic[str(app_install_id)] = AppInstallProductInstallerDynamicDict(dynamic_data)
            record.app_install_product_installers_by_app_install_dynamic = app_install_product_installers_by_app_install_dynamic
        else:
            # No mappings means nothing is scheduled
            is_scheduled = False

        # Build the main installer info using stored data
        # We use any installer that was matched in the loop above here.
        # If there are multiple possible installers
        # e.g. a mix of MacOS and Windows installs, we are showing the wrong
        # versions in the frontend.
        # This is known problem, and rework of the display table would fix it.
        installer_data: AppInstallProductInstallerDict = {
            'app_install_product_installer_id': installer.id,
            'title': installer.title,
            'is_scheduled': is_scheduled,
            'installer_version': installer.patch_installer.latest_version,
            'patch_detected_date': record.patch_detected_date,
        }
        record.app_install_product_installer = AppInstallProductInstallerDict(installer_data)

        enriched_apps.append(record)

    return enriched_apps
