# Re-export all functions to maintain backward compatibility
from opswat_patch.utils.calculate_enrich import (
    enrich_installed_software_with_installers,
    calculate_enrich_installed_software_with_installers,
    get_patch_detected_date,
    compare_semver_core,
)
from .enrich_using_db_data import (
    get_enriched_installed_software_with_installers_from_db,
)
from .types import (
    AppInstallProductInstallerDict,
    AppInstallProductInstallerDynamicDict,
    AppInstallProductInstallersByAppInstallDynamic,
)


__all__ = [
    'enrich_installed_software_with_installers',
    'calculate_enrich_installed_software_with_installers',
    'get_patch_detected_date',
    'compare_semver_core',
    'get_enriched_installed_software_with_installers_from_db',
    'AppInstallProductInstallerDict',
    'AppInstallProductInstallerDynamicDict',
    'AppInstallProductInstallersByAppInstallDynamic',
]
