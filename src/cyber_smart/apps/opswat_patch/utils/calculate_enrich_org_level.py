"""
Organization-level enrichment utilities for OPSWAT patch management.

This module contains functions for enriching InstalledSoftwareOrganisationIndividual
objects with patch installer information, optimized to avoid N+1 query issues.
"""
from collections import defaultdict
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple, Union

from django.db.models import QuerySet

from software_inventory.models import InstalledSoftwareOrganisationIndividual
from opswat.models import InstalledProductVersion
from opswat_patch.models import OpswatProductPatchInstaller, OpswatScheduledProductInstaller
from opswat_patch.utils import calculate_enrich
from opswat_patch.utils.types import (
    AppInstallProductInstallerDict,
    AppInstallProductInstallerDynamicDict,
    AppInstallProductInstallersByAppInstallDynamic,
)


def _prefetch_ipvs_for_org_level(
    installed_software: Union[QuerySet[InstalledSoftwareOrganisationIndividual], List[InstalledSoftwareOrganisationIndividual]]
) -> Tuple[Dict[str, List[InstalledProductVersion]], List[InstalledProductVersion]]:
    """
    Pre-fetch all InstalledProductVersion objects for organization-level enrichment.

    Args:
        installed_software: QuerySet or list of InstalledSoftwareOrganisationIndividual objects

    Returns:
        Tuple containing:
        - app_to_ipvs: dict mapping app.id to list of IPV objects (sorted by created)
        - all_ipvs: list of all IPV objects
    """
    # Collect all IPV ids and their app associations
    app_to_ipv_ids: Dict[str, List[int]] = {}
    all_ipv_ids: List[int] = []
    for app in installed_software:
        app_to_ipv_ids[app.id] = app.aggregated_installed_product_version_ids
        all_ipv_ids.extend(app.aggregated_installed_product_version_ids)

    if not all_ipv_ids:
        return {}, []

    # Pre-fetch all InstalledProductVersion objects in one query
    all_ipvs = InstalledProductVersion.objects.filter(
        id__in=all_ipv_ids
    ).select_related(
        'product_version__product',
        'installed_product__app_install__opswat_os',
        'installed_product__app_install__app_user',
        'signature'
    ).order_by('created')

    # Group IPVs by their ID for quick lookup
    ipvs_by_id: Dict[int, InstalledProductVersion] = {ipv.id: ipv for ipv in all_ipvs}

    # Group IPVs back to their apps, maintaining order
    app_to_ipvs: Dict[str, List[InstalledProductVersion]] = {}
    for app_id, ipv_ids in app_to_ipv_ids.items():
        app_ipvs = []
        for ipv_id in ipv_ids:
            if ipv_id in ipvs_by_id:
                app_ipvs.append(ipvs_by_id[ipv_id])
        # Sort by created to maintain the original order
        app_ipvs.sort(key=lambda x: x.created)
        app_to_ipvs[app_id] = app_ipvs

    return app_to_ipvs, list(all_ipvs)


def _prefetch_installers_by_product(all_product_ids: Set[int]) -> Dict[int, List[OpswatProductPatchInstaller]]:
    """
    Pre-fetch all OpswatProductPatchInstaller objects for given product IDs.

    Args:
        all_product_ids: Set of product IDs to fetch installers for

    Returns:
        Dict mapping product_id to list of installers
    """
    if not all_product_ids:
        return {}

    all_installers = OpswatProductPatchInstaller.objects.active().filter(
        product_id__in=all_product_ids
    ).select_related('patch_installer').prefetch_related('signatures')

    # Group installers by product_id
    installers_by_product: Dict[int, List[OpswatProductPatchInstaller]] = defaultdict(list)
    for installer in all_installers:
        installers_by_product[installer.product_id].append(installer)
    return installers_by_product


def _prefetch_scheduled_installers_for_org_level(
    all_ipvs: List[InstalledProductVersion],
    all_product_ids: Set[int]
) -> Set[Tuple[int, int]]:
    """
    Pre-fetch scheduled installer data for organization-level enrichment.

    Args:
        all_ipvs: List of all InstalledProductVersion objects
        all_product_ids: Set of all product IDs

    Returns:
        Set of (app_install_id, product_id) tuples for scheduled installers
    """
    all_app_install_ids: Set[int] = set(ipv.installed_product.app_install_id for ipv in all_ipvs)
    if not all_app_install_ids or not all_product_ids:
        return set()

    # Get scheduled installer data in one query
    scheduled_checks = OpswatScheduledProductInstaller.non_terminal.filter(
        app_install_id__in=all_app_install_ids,
        opswat_product_patch_installer__product_id__in=all_product_ids
    ).values_list('app_install_id', 'opswat_product_patch_installer__product_id')
    return set(scheduled_checks)

def calculate_enrich_installed_software_with_installers_organisation_level(
    installed_software: Union[QuerySet[InstalledSoftwareOrganisationIndividual], List[InstalledSoftwareOrganisationIndividual]],
) -> List[InstalledSoftwareOrganisationIndividual]:
    """
    InstalledSoftwareOrganisationIndividual Level Enrichment.

    Enriches installed software with patch installer information at the organization level.
    This function is optimized to avoid N+1 queries by pre-fetching all necessary data.

    Args:
        installed_software: QuerySet or list of InstalledSoftwareOrganisationIndividual objects

    Returns:
        List of enriched InstalledSoftwareOrganisationIndividual objects
    """
    enriched_apps: List[InstalledSoftwareOrganisationIndividual] = []
    if not installed_software:
        return enriched_apps

    # Pre-fetch all IPVs and group them by app
    app_to_ipvs, all_ipvs = _prefetch_ipvs_for_org_level(installed_software)
    if not all_ipvs:
        return list(installed_software)

    # Extract unique product IDs from all IPVs
    all_product_ids: Set[int] = set()
    for ipv in all_ipvs:
        if ipv.product_version and ipv.product_version.product_id:
            all_product_ids.add(ipv.product_version.product_id)

    # Pre-fetch all installers grouped by product
    installers_by_product = _prefetch_installers_by_product(all_product_ids)

    # Pre-fetch scheduled installer data if needed
    scheduled_set: Set[Tuple[int, int]] = _prefetch_scheduled_installers_for_org_level(all_ipvs, all_product_ids)

    # Process each app using pre-fetched data
    for app in installed_software:
        installed_product_versions = app_to_ipvs.get(app.id, [])
        app_install_product_installers_by_app_install_dynamic: AppInstallProductInstallersByAppInstallDynamic = {}

        any_compatible_installer: Optional[OpswatProductPatchInstaller] = None
        global_patch_detected_data: Optional[datetime] = None

        for ipv in installed_product_versions:
            compatible_installer: Optional[OpswatProductPatchInstaller] = None
            if not ipv.signature:
                continue

            # Get installers for this product from pre-fetched data
            product_id = ipv.product_version.product_id
            candidate_installers = installers_by_product.get(product_id, [])

            if not candidate_installers:
                continue

            os_id: Optional[int] = None
            if (hasattr(ipv.installed_product.app_install, 'opswat_os') and
                ipv.installed_product.app_install.opswat_os):
                os_id = ipv.installed_product.app_install.opswat_os.os_id

            compatible_installer = calculate_enrich._find_best_compatible_installer(
                candidate_installers,
                ipv.product_version.raw_version,
                [ipv.signature.id],
                os_id
            )

            if not compatible_installer:
                continue

            patch_detected_date = calculate_enrich.get_patch_detected_date(
                ipv.product_version.raw_version,
                ipv.created,
                compatible_installer
            )

            if patch_detected_date and (not global_patch_detected_data or patch_detected_date < global_patch_detected_data):
                global_patch_detected_data = patch_detected_date

            app_install_id = ipv.installed_product.app_install_id
            # Check if scheduled using pre-fetched data
            is_scheduled = (app_install_id, product_id) in scheduled_set

            dynamic_data: AppInstallProductInstallerDynamicDict = {
                'app_install_product_installer_id': compatible_installer.id,
                # TODO: stop calculating these properties here
                # after migration is fully done.
                'is_scheduled': is_scheduled,
                'patch_detected_date': patch_detected_date,
            }
            app_install_product_installers_by_app_install_dynamic[app_install_id] = AppInstallProductInstallerDynamicDict(dynamic_data)

            if not any_compatible_installer and compatible_installer:
                any_compatible_installer = compatible_installer

        if not any_compatible_installer:
            enriched_apps.append(app)
            continue

        app.app_install_product_installers_by_app_install_dynamic = app_install_product_installers_by_app_install_dynamic

        # Get patch detected date
        installer_data: AppInstallProductInstallerDict = {
            'app_install_product_installer_id': any_compatible_installer.id,
            'title': any_compatible_installer.title,
            'is_scheduled': False,
            'installer_version': any_compatible_installer.patch_installer.latest_version,
            'patch_detected_date': global_patch_detected_data,
        }
        app.app_install_product_installer = AppInstallProductInstallerDict(installer_data)
        enriched_apps.append(app)

    return enriched_apps
