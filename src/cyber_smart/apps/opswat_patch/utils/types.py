"""
Type definitions for OPSWAT patch enrichment data structures.
This module provides typed dictionary classes for patch installer
enrichment data.
"""
from typing import TypedDict, Optional, Dict, Union
from datetime import datetime

class AppInstallProductInstallerDict(TypedDict):
    """An object added to InstalledSoftwareOrganisationIndividual and
    InstalledSoftwareAppInstallIndividual to provide enrichment data for the
    frontend.
    Frontend uses this to determine if a patch is available for app_install level
    and to display informational data on organisation level.
    """
    app_install_product_installer_id: int
    title: str
    is_scheduled: bool
    installer_version: str
    patch_detected_date: Optional[datetime]


class AppInstallProductInstallerDynamicDict(TypedDict):
    """
    An object added to InstalledSoftwareOrganisationIndividual to provide enrichment data for the
    frontend.
    Frontend and form logic uses this to determine if a patch is available for each app install
    grouped in the InstalledSoftwareOrganisationIndividual record
    """
    app_install_product_installer_id: int
    is_scheduled: bool
    patch_detected_date: Optional[datetime]


# Type alias for the dynamic mapping
AppInstallProductInstallersByAppInstallDynamic = Dict[Union[int, str], Union[AppInstallProductInstallerDynamicDict, AppInstallProductInstallerDynamicDict]]
