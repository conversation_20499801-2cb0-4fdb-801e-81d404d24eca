from collections import defaultdict
import operator
from typing import Callable, Optional, List, Union
from datetime import datetime

from waffle import switch_is_active

from django.db.models import Prefetch, QuerySet

from appusers.utils import parse_version
from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from opswat.utils import parse_composite_ids
from opswat.models import ProductVersion, InstalledProductVersion
from opswat.opswat_operating_system import is_installer_os_compatible
from opswat_patch.models import OpswatProductPatchInstaller, OpswatScheduledProductInstaller
from opswat_patch.version_compatibility import is_patch_version_compatible
from opswat_patch.utils.calculate_enrich_org_level import (
    calculate_enrich_installed_software_with_installers_organisation_level,
)
from opswat_patch.utils.types import (
    AppInstallProductInstallerDict,
)

def enrich_installed_software_with_installers(
    installed_software: Union[QuerySet[InstalledSoftwareOrganisationIndividual], QuerySet[InstalledSoftwareAppInstallIndividual], List[Union[InstalledSoftwareOrganisationIndividual, InstalledSoftwareAppInstallIndividual]]],
) -> List[Union[InstalledSoftwareOrganisationIndividual, InstalledSoftwareAppInstallIndividual]]:
    """
    Backward compatible function which will calculate the compatible patch
    """
    opswat_enrichment_offline = switch_is_active("opswat-enrichment-offline")
    if not opswat_enrichment_offline:
        return calculate_enrich_installed_software_with_installers(installed_software)
    from opswat_patch.utils.enrich_using_db_data import get_enriched_installed_software_with_installers_from_db
    return get_enriched_installed_software_with_installers_from_db(installed_software)


def calculate_enrich_installed_software_with_installers(
    installed_software: Union[QuerySet[InstalledSoftwareAppInstallIndividual], List[InstalledSoftwareAppInstallIndividual]]
) -> Union[List[InstalledSoftwareAppInstallIndividual], List[InstalledSoftwareOrganisationIndividual]]:
    """
    Enrich installed software with patch installer information.
    Finds matching installers using signatures and adds installer info to each app.

    OS Filtering:
    - If app_install has opswat_os information, filters installers based on OS compatibility
      using os_allow and os_deny fields
    - For backward compatibility: if app_install has no opswat_os, all installers are considered
      compatible (no OS filtering applied)

    Args:
        installed_software: QuerySet or list of installed software objects
        app_install: AppInstall instance for app-level enrichment
    """
    if not installed_software:
        return []

    is_app_install_level = isinstance(installed_software[0], InstalledSoftwareAppInstallIndividual)
    if is_app_install_level:
        return _calculate_enrich_installed_software_with_installers_app_install_level(
            installed_software
        )
    else:
        return calculate_enrich_installed_software_with_installers_organisation_level(
            installed_software
        )

def _calculate_enrich_installed_software_with_installers_app_install_level(
    installed_software: Union[QuerySet[InstalledSoftwareAppInstallIndividual], List[InstalledSoftwareAppInstallIndividual]]
) -> List[InstalledSoftwareAppInstallIndividual]:
    # Extract product version IDs and signatures from installed software
    product_version_ids = []
    app_signatures_map = {}
    app_install_ids = set()

    for app in installed_software:
        app_install_ids.add(app.app_install_id)
        _product_version_ids, _ = parse_composite_ids(app.id)
        product_version_ids.extend(_product_version_ids)
        if hasattr(app, 'signatures') and app.signatures:
            app_signatures_map[app.id] = app.signatures

    if not product_version_ids:
        return list(installed_software)

    # patch_detected related data helpers
    product_versions = ProductVersion.objects.filter(id__in=product_version_ids).select_related('product')
    product_version_id_to_string = {str(v.id): v.get_version_string() for v in product_versions}

    installed_product_versions = InstalledProductVersion.objects.filter(
        product_version_id__in=product_version_ids,
        installed_product__app_install__in=app_install_ids
    )
    product_version_id_to_install_date = {
        str(ipv.product_version_id): ipv.created for ipv in installed_product_versions
    }

    # Get installers that match product versions and signatures
    # Prefetch only the specific versions we need to avoid loading unnecessary data
    versions_prefetch = Prefetch(
        'product__versions',
        queryset=ProductVersion.objects.filter(id__in=product_version_ids),
        to_attr='prefetched_versions'
    )

    installers = OpswatProductPatchInstaller.objects.active().filter(
        product__versions__in=product_version_ids
    ).select_related('product', 'patch_installer').prefetch_related('signatures', versions_prefetch).order_by('product_id', '-patch_installer__created')

    # Get scheduled installer IDs if app_install is provided
    scheduled_installer_ids = set(
        OpswatScheduledProductInstaller.non_terminal.filter(
            app_install__in=app_install_ids,
            opswat_product_patch_installer__in=installers
        ).values_list('opswat_product_patch_installer_id', 'app_install_id')
    )
    scheduled_installers_for_app_install_id = defaultdict(set)
    for installer_id, app_install_id in scheduled_installer_ids:
        scheduled_installers_for_app_install_id[app_install_id].add(installer_id)

    # Create a map of product version ID to best matching installer
    product_version_to_installer = defaultdict(list)
    # Group installers by product version
    for installer in installers:
        # Use the prefetched versions to avoid additional queries
        for version in installer.product.prefetched_versions:
            version_id = str(version.id)
            product_version_to_installer[version_id].append(installer)

    # Enrich apps with installer information
    enriched_apps = []
    for app in installed_software:
        _product_version_ids, _ = parse_composite_ids(app.id)
        if not _product_version_ids:
            enriched_apps.append(app)
            continue

        app_install = app.app_install
        # Check if we have OS information for filtering
        has_os_info = app_install.has_known_opswat_os_id if app_install else False
        os_id = app_install.opswat_os.os_id if hasattr(app_install, 'opswat_os') and has_os_info else None

        """
        This block ensures we check for installers in all provided product versions
        associated with the app. If no installers are found for any of the product versions,
        we skip enrichment for that app.
        Otherwise, we proceed with the first matching product version ID that has installers.
        """
        installer, product_version_id = None, None
        for product_version_id in _product_version_ids:
            all_installers = product_version_to_installer.get(str(product_version_id), [])
            installer = _find_best_compatible_installer(
                all_installers, app.version,
                app_signatures_map.get(app.id, []),
                os_id
            )
            if installer:
                product_version_id = str(product_version_id)
                break
        if not installer or not product_version_id:
            enriched_apps.append(app)
            continue

        # Get patch detected date
        patch_detected_date = get_patch_detected_date(
            product_version_id_to_string.get(product_version_id),
            product_version_id_to_install_date.get(product_version_id),
            installer
        )
        installer_data: AppInstallProductInstallerDict = {
            'app_install_product_installer_id': installer.id,
            'title': installer.title,
            'is_scheduled': app.app_install_id in scheduled_installers_for_app_install_id and installer.id in scheduled_installers_for_app_install_id[app.app_install_id],
            'installer_version': installer.patch_installer.latest_version,
            'patch_detected_date': patch_detected_date,
        }
        app.app_install_product_installer = AppInstallProductInstallerDict(installer_data)

        enriched_apps.append(app)
    return enriched_apps


def _find_best_compatible_installer(
    candidate_installers: list[OpswatProductPatchInstaller],
    installed_version: str,
    app_signatures: list[int],
    os_id: Optional[int]
) -> Optional[OpswatProductPatchInstaller]:
    """
    Finds the best compatible installer from a list of candidates.
    Args:
        candidate_installers: A list of OpswatProductPatchInstaller objects to filter.
        installed_version: The version string of the currently installed software.
        os_id: The OS ID to filter by. If None, OS filtering is skipped.
    Returns:
        The best matching OpswatProductPatchInstaller instance or None if no match is found.
    """
    # 0. If no signatures for this app are given, there is not compatible installer
    if not app_signatures:
        return None
    compatible_installers = []
    for candidate_installer in candidate_installers:
        candidate_installer_sigs = set(sig.id for sig in candidate_installer.signatures.all())
        if any(sig_id in candidate_installer_sigs for sig_id in app_signatures):
            compatible_installers.append(candidate_installer)

    if not compatible_installers:
        return None

    # 1. Filter by OS compatibility if OS information is available
    if os_id is not None:
        compatible_installers = [
            inst for inst in compatible_installers if is_installer_os_compatible(inst, os_id)
        ]
    else:
        # No OS information, so we don't filter by OS
        # (just for backward compatibility)
        # remove this after CAP v5.6.0 release
        pass

    if not compatible_installers:
        return None

    # 2. Filter by version compatibility (version_pattern or ranges)
    compatible_installers = [
        inst for inst in compatible_installers if is_patch_version_compatible(inst, installed_version)
    ]

    if not compatible_installers:
        return None

    # 3. Find the best installer (the first one that is a newer version)
    # Note: Assumes candidate_installers are pre-sorted by date descending.
    for installer in compatible_installers:
        # By this stage there should be one compatible installer,
        # but just in case there are multiple, we pick the first one that is a newer version.
        if compare_semver_core(installed_version, installer.patch_installer.latest_version, operator.lt):
            return installer
    return None

def get_patch_detected_date(device_version_string: Optional[str], installed_date: Optional[datetime], installer: OpswatProductPatchInstaller) -> Optional[datetime]:
    patch_detected_date: Optional[datetime] = None
    installer_version_string = installer.patch_installer.latest_version

    if not device_version_string:
        return None

    parsed_device = parse_version(device_version_string)
    parsed_installer = parse_version(installer_version_string)

    if not parsed_device or not parsed_installer:
        return None
    core_device = parsed_device[:-1]
    core_installer = parsed_installer[:-1]

    # Only proceed if patch version is higher than installed version
    if core_installer <= core_device:
        return None

    installer_modified_date = installer.modified

    if not installed_date:
        return None

    # Case 1: Patch available before install
    if installer_modified_date < installed_date:
        patch_detected_date = installed_date
    # Case 2: Install before patch available
    else:
        patch_detected_date = installer_modified_date
    return patch_detected_date

def compare_semver_core(version1: str, version2: str, op: Callable = operator.eq) -> bool:
    """
    Compare the core components (MAJOR.MINOR.PATCH) of two semantic version strings
    using a specified comparison operator.
    Semantic versions follow the format: MAJOR.MINOR.PATCH[-CHANNEL].
    This function compares the MAJOR, MINOR, and PATCH parts, ignoring channel info.

    Args:
        version1 (str): First semantic version string.
        version2 (str): Second semantic version string.
        op (Callable, optional): Comparison operator. Defaults to operator.eq.
    """
    parsed_v1 = parse_version(version1)
    parsed_v2 = parse_version(version2)

    if not parsed_v1 or not parsed_v2:
        return False

    core_v1 = parsed_v1[:-1]
    core_v2 = parsed_v2[:-1]

    return op(core_v1, core_v2)
