"""Version compatibility checking for patch installers."""
import logging
import re

from opswat_patch.models import OpswatProductPatchInstaller

logger = logging.getLogger(__name__)

def is_patch_version_compatible(installer: OpswatProductPatchInstaller, product_version: str) -> bool:
    """
    Check if a product version is compatible with installer's version_pattern or ranges.

    Priority:
    1. If version_pattern exists, use regex matching
    2. If ranges exist, check if version falls within any range
    3. If neither exist, return True (compatible by default)

    Args:
        installer: The patch installer to check
        product_version: The installed product version string

    Returns:
        bool: True if version is compatible, False otherwise
    """
    # If no version_pattern and no ranges, return True (compatible by default)
    if not installer.version_pattern and not installer.ranges:
        return True

    # First, check version_pattern if it exists
    if installer.version_pattern:
        try:
            # Compile and match the regex pattern
            pattern = re.compile(installer.version_pattern)
            # Use match from beginning of string
            if pattern.match(product_version):
                return True
            # If pattern exists but doesn't match, return False
            return False
        except re.error:
            logger.debug(f"Invalid regex pattern for installer {installer.id}: {installer.version_pattern}")
            # If regex is invalid, log and fall through to ranges check
            # In production, you might want to log this error
            pass

    # If version_pattern didn't match and there are no ranges, return False
    if not installer.ranges:
        return False

    if _does_match_version_range(installer, product_version):
        return True

    return False


def _does_match_version_range(installer: OpswatProductPatchInstaller, product_version: str) -> bool:
    # For range comparison, we need to handle multi-part versions (e.g., 19.11.9999.9999)
    # Parse the version parts manually for accurate comparison

    def parse_version_parts(version_str: str) -> tuple:
        """Parse version string into tuple of integers for comparison."""
        if not version_str or version_str == 'null':
            return None

        try:
            # Split by dots and convert to integers
            parts = []
            for part in version_str.split('.'):
                # Handle parts that might have non-numeric suffixes (e.g., "5.0.0-beta")
                # Take only the numeric part before any non-digit character
                numeric_part = ''
                for char in part:
                    if char.isdigit():
                        numeric_part += char
                    else:
                        break
                if numeric_part:
                    parts.append(int(numeric_part))
                else:
                    # If we hit a non-numeric part, stop processing
                    break

            # Ensure we have at least one part
            if not parts:
                return None

            # Pad with zeros to ensure consistent comparison
            # (e.g., "5" becomes (5, 0, 0, 0) and "5.1" becomes (5, 1, 0, 0))
            while len(parts) < 4:
                parts.append(0)

            return tuple(parts[:4])  # Take only first 4 parts for consistency
        except (ValueError, AttributeError):
            return None

    # Parse the product version
    parsed_product_version = parse_version_parts(product_version)
    if not parsed_product_version:
        # If we can't parse the version, we can't do range comparison
        return False

    for version_range in installer.ranges:
        # Handle start boundary
        start = version_range.get('start')
        if start is None or start == 'null':
            # null start means from version "0"
            start_version = (0, 0, 0, 0)
        else:
            start_version = parse_version_parts(start)
            if not start_version:
                continue

        # Handle limit boundary
        limit = version_range.get('limit')
        if limit is None or limit == 'null':
            # null limit means no upper bound
            limit_version = None
        else:
            limit_version = parse_version_parts(limit)
            if not limit_version:
                continue

        # Check if product version is within range (inclusive on both ends)
        if parsed_product_version >= start_version:
            if limit_version is None:
                return True
            else:
                if parsed_product_version <= limit_version:
                    return True

    # If we get here, version didn't match any range
    return False
