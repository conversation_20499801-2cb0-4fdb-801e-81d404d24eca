import json
from typing import Any, Dict, List, Optional, Tuple

from admin_auto_filters.filters import AutocompleteFilterFactory
from django.contrib import admin
from django.db.models import QuerySet
from django.http import HttpRequest, HttpResponse
from django.template.loader import render_to_string
from django.test import RequestFactory
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from common.admin import DisableEditMixin
from opswat.models import InstalledProduct, InstalledProductVersion
from opswat_patch.models import (
    OpswatScheduledProductInstaller,
    OpswatPatchSourceFile,
    OpswatArchitecture,
    OpswatPatchFileImport,
    OpswatPatchInstaller,
    OpswatPatchInstallerDownloadLink,
    OpswatSchemaVersion,
    OpswatProductPatchInstaller,
    OpswatProductPatchInstallerSignature,
    OpswatPatchEventLog,
    OpswatPatchAttempt,
    OpswatPatchJob
)
from opswat_patch.serializers import OpswatScheduledProductInstallerSerializer


class OpswatPatchEventLogInline(admin.TabularInline):
    model = OpswatPatchEventLog
    extra = 0
    fields = ('status', 'error_code', 'get_error_message_display', 'details_public_facing', 'created', 'modified')
    readonly_fields = ('status', 'error_code', 'get_error_message_display', 'details_public_facing', 'created', 'modified')
    can_delete = False
    ordering = ['-created']
    show_change_link = True
    verbose_name = "Patch Event Log"
    verbose_name_plural = "Patch Event Logs"

    def get_error_message_display(self, obj: OpswatPatchEventLog) -> str:
        """Display the human-readable error message"""
        return obj.get_error_message() or "-"
    get_error_message_display.short_description = "Error Message"  # type: ignore[attr-defined]

    def get_queryset(self, request: HttpRequest) -> QuerySet[OpswatPatchEventLog]:
        """Optimize queryset to avoid N+1 queries"""
        return super().get_queryset(request).select_related('opswat_scheduled_product_installer')

    def has_add_permission(self, request: HttpRequest, obj: Optional[OpswatPatchEventLog] = None) -> bool:
        """Disable adding new event logs through the inline"""
        return False

    def has_change_permission(self, request: HttpRequest, obj: Optional[OpswatPatchEventLog] = None) -> bool:
        """Disable editing event logs through the inline"""
        return False


# Note: InstalledProduct, InstalledProductVersion, and InstalledSoftware data
# are displayed via custom methods in the admin detail view instead of inlines
# due to the indirect relationships


class PatchAttemptInline(admin.TabularInline):
    model = OpswatPatchAttempt
    extra = 0
    fields = ("from_version", "to_version", "status", "initiated_by", "created", "modified")
    readonly_fields = ('status', 'created', 'modified')
    can_delete = False
    verbose_name = "Patch Attempt"
    verbose_name_plural = "Patch Attempts"


@admin.register(OpswatScheduledProductInstaller)
class OpswatScheduledProductInstallerAdmin(DisableEditMixin, admin.ModelAdmin):
    """Admin configuration for the OpswatScheduledProductInstaller model."""
    list_display = (
        'opswat_product_patch_installer', 'app_install', 'creator', 'status',
        'display_installed_products', 'display_signature_match', 'created', 'modified'
    )

    def changelist_view(self, request: HttpRequest, extra_context: Optional[Dict[str, Any]] = None) -> HttpResponse:
        """Override to add troubleshooting dashboard link"""
        extra_context = extra_context or {}
        extra_context['show_troubleshooting_link'] = True
        return super().changelist_view(request, extra_context)

    class Media:
        css = {
            'all': ('admin/opswat_patch/css/opswat_admin.css',)
        }
    search_fields = (
        'app_install__device_id', 'app_install__real_device_id',
        'opswat_product_patch_installer__product__name',
        'opswat_product_patch_installer__product__vendor__name',
        'creator__username', 'creator__email',
        'app_install__installed_products__hash',
        'signature__name', 'signature__opswat_id'
    )
    ordering = ('-created',)
    autocomplete_fields = ('app_install', 'opswat_product_patch_installer', 'creator', 'signature')

    # Phase 1: Enhanced Detail View - Comprehensive readonly fields
    readonly_fields = (
        'created', 'modified', 'status', 'retry_count',
        # External Links
        'display_opswat_links',
        # API Representation
        'display_json_serialization',
    )

    fieldsets = (
        ('Basic Information', {
            'fields': (
                'opswat_product_patch_installer',
                'app_install',
                'creator',
                'signature',
                'status',
                'retry_count',
                ('created', 'modified'),
            )
        }),
        ('External Data Sources', {
            'fields': (
                'display_opswat_links',
            ),
            'classes': ('collapse',),
        }),
        ('API Representation', {
            'fields': (
                'display_json_serialization',
            ),
            'classes': ('collapse',),
            'description': 'JSON representation as returned by the API'
        }),
    )

    list_filter = (
        'status',
        AutocompleteFilterFactory('Organisation', 'app_install__app_user__organisation'),
        AutocompleteFilterFactory('App Install', 'app_install'),
        AutocompleteFilterFactory('Opswat Product Patch Installer', 'opswat_product_patch_installer'),
        AutocompleteFilterFactory('Creator', 'creator'),
        AutocompleteFilterFactory('Signature', 'signature'),
    )

    # Phase 2: Related Data Inlines
    inlines = [
        OpswatPatchEventLogInline,
        PatchAttemptInline
        # Note: InstalledProduct, InstalledProductVersion, and InstalledSoftware data
        # are displayed via custom methods in the admin detail view instead of inlines
        # due to the indirect relationships (no direct ForeignKey to OpswatScheduledProductInstaller)
    ]

    def get_queryset(self, request: HttpRequest) -> QuerySet[OpswatScheduledProductInstaller]:
        """Optimize queryset to prefetch related data"""
        return super().get_queryset(request).select_related(
            'opswat_product_patch_installer__product__vendor',
            'app_install__app_user__organisation',
            'creator',
            'signature'
        ).prefetch_related(
            'event_logs',
            'app_install__installed_products__product_versions',
            'app_install__installed_products__installed_product_versions__signature',
            'opswat_product_patch_installer__signatures'
        )

    # List display methods
    def display_installed_products(self, obj: OpswatScheduledProductInstaller) -> str:
        """Display count of installed products for the app_install"""
        count = obj.app_install.installed_products.count()
        if count > 0:
            return format_html('<span style="color: green;">{} products</span>', count)
        return format_html('<span style="color: gray;">No products</span>')
    display_installed_products.short_description = "Installed Products"  # type: ignore[attr-defined]

    def display_signature_match(self, obj: OpswatScheduledProductInstaller) -> str:
        """Display if signatures match between installer and installed products"""
        if not obj.signature:
            return format_html('<span style="color: gray;">No signature</span>')

        # Check if signature matches any installed product versions
        matching_versions = InstalledProductVersion.objects.filter(
            installed_product__app_install=obj.app_install,
            signature=obj.signature
        ).exists()

        if matching_versions:
            return format_html('<span style="color: green;">✓ Match</span>')
        else:
            return format_html('<span style="color: orange;">⚠ No match</span>')
    display_signature_match.short_description = "Signature Match"  # type: ignore[attr-defined]


    # Phase 4: External Data Links
    def display_opswat_links(self, obj: OpswatScheduledProductInstaller) -> str:
        """Display related records and OPSWAT data sources"""
        if not obj.pk:
            return "-"

        # Prepare data for InstalledSoftwareAppInstallIndividual records
        product_name = obj.opswat_product_patch_installer.product.name
        detected_software = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=obj.app_install,
            product__icontains=product_name.split()[0]
        )
        detected_software_count = detected_software.count()

        # Prepare data for InstalledProduct records
        installed_products = InstalledProduct.objects.filter(
            app_install=obj.app_install
        ).prefetch_related('product_versions__product__vendor')

        # Build installed products data with additional info
        installed_products_data = []
        for ip in installed_products:
            target_product_versions = list(ip.product_versions.filter(
                product=obj.opswat_product_patch_installer.product
            ).select_related('product__vendor'))

            ip_data = {
                'id': ip.id,
                'hash': ip.hash,
                'created': ip.created,
                'product_count': ip.product_versions.count(),
                'has_target_product': bool(target_product_versions),
                'target_versions': target_product_versions,
                'sample_products': list(ip.product_versions.select_related('product__vendor')[:3]) if not target_product_versions else []
            }
            installed_products_data.append(ip_data)

        context = {
            'detected_software': list(detected_software[:10]),
            'detected_software_count': detected_software_count,
            'installed_products': installed_products,
            'installed_products_data': installed_products_data,
        }

        html = render_to_string(
            'admin/opswat_patch/opswatscheduledproductinstaller/external_data_sources.html',
            context
        )

        return mark_safe(html)
    display_opswat_links.short_description = "External Data Sources"  # type: ignore[attr-defined]




    def display_json_serialization(self, obj: OpswatScheduledProductInstaller) -> str:
        """Display the JSON serialization of this record as it would appear in the API"""
        if not obj.pk:
            return "-"

        context = {
            'obj_id': obj.pk,
            'json_data': None,
            'error_message': None
        }

        try:
            # Create a mock request with the creator user
            factory = RequestFactory()
            request = factory.get('/')
            request.user = obj.creator

            # Serialize the object
            serializer = OpswatScheduledProductInstallerSerializer(obj, context={'request': request})
            context['json_data'] = json.dumps(serializer.data, indent=2, sort_keys=True)

        except Exception as e:
            context['error_message'] = f'Error serializing object: {str(e)}'

        html = render_to_string(
            'admin/opswat_patch/opswatscheduledproductinstaller/json_serialization.html',
            context
        )

        return mark_safe(html)
    display_json_serialization.short_description = "JSON Serialization"  # type: ignore[attr-defined]



@admin.register(OpswatPatchSourceFile)
class OpswatPatchSourceFileAdmin(DisableEditMixin, admin.ModelAdmin):
    list_display = ('file_name', 'extracted_path', 'created')
    search_fields = ('file_name',)
    list_filter = ('created',)


@admin.register(OpswatArchitecture)
class OpswatArchitectureAdmin(DisableEditMixin, admin.ModelAdmin):
    list_display = ('name', 'created', 'modified')
    search_fields = ('name',)
    readonly_fields = ('created', 'modified')


@admin.register(OpswatPatchFileImport)
class OpswatPatchFileImportAdmin(DisableEditMixin, admin.ModelAdmin):
    list_display = ('file_name', 'sha256', 'import_status', 'total_processed', 'created_count', 'updated', 'created')
    search_fields = ('file_name', 'sha256')
    list_filter = ('import_status', 'created')


@admin.register(OpswatPatchInstaller)
class OpswatPatchInstallerAdmin(DisableEditMixin, admin.ModelAdmin):
    list_display = ('product_name', 'opswat_id', 'latest_version', 'fresh_installable', 'release_date', 'date_disabled', 'created', 'modified')
    search_fields = ('product_name', 'opswat_id')
    list_filter = ('fresh_installable', 'requires_reboot', 'requires_uninstall_first', 'release_date', 'date_disabled')
    filter_horizontal = ('architectures',)
    readonly_fields = ('created', 'modified')


@admin.register(OpswatPatchInstallerDownloadLink)
class OpswatPatchInstallerDownloadLinkAdmin(DisableEditMixin, admin.ModelAdmin):
    list_display = ('patch_installer', 'language', 'architecture', 'created')
    search_fields = ('patch_installer__product_name',)
    autocomplete_fields = ('patch_installer', 'os_architecture')
    readonly_fields = ('cached_link_sha',)


@admin.register(OpswatSchemaVersion)
class OpswatSchemaVersionAdmin(DisableEditMixin, admin.ModelAdmin):
    list_display = ('schema_version', 'created', 'modified')
    search_fields = ('schema_version',)
    readonly_fields = ('created', 'modified')


class OpswatProductPatchInstallerSignatureInline(admin.TabularInline):
    model = OpswatProductPatchInstallerSignature
    extra = 0
    fields = ('signature',)
    autocomplete_fields = ('signature',)
    can_delete = False
    verbose_name = "Associated Product Signature"
    verbose_name_plural = "Associated Product Signatures"


@admin.register(OpswatProductPatchInstaller)
class OpswatProductPatchInstallerAdmin(DisableEditMixin, admin.ModelAdmin):
    list_display = ('title', 'product', 'is_latest', 'opswat_id', 'date_disabled', 'created', 'modified', 'display_signatures')
    search_fields = ('title', 'product__name', 'opswat_id')
    list_filter = ('is_latest', 'date_disabled')
    autocomplete_fields = ('product', 'patch_installer', 'schema_version', 'architecture')
    inlines = [OpswatProductPatchInstallerSignatureInline]
    readonly_fields = ('created', 'modified')

    def display_signatures(self, obj: OpswatProductPatchInstaller) -> str:
        """Display signatures as a comma-separated list"""
        signatures = list(obj.signatures.values_list('id', flat=True))
        if signatures:
            return ", ".join(str(sig) for sig in signatures)
        return "-"
    display_signatures.short_description = "Signatures"  # type: ignore[attr-defined]


class HasErrorCodeFilter(admin.SimpleListFilter):
    """Custom filter to check if error_code exists or not"""
    title = _('Has Error Code')
    parameter_name = 'has_error_code'

    def lookups(self, request: HttpRequest, model_admin: admin.ModelAdmin) -> List[Tuple[str, str]]:
        return [
            ('yes', str(_('Has Error Code'))),
            ('no', str(_('No Error Code'))),
        ]

    def queryset(self, request: HttpRequest, queryset: QuerySet) -> QuerySet:
        if self.value() == 'yes':
            return queryset.exclude(error_code__exact='')
        if self.value() == 'no':
            return queryset.filter(error_code__exact='')
        return queryset


@admin.register(OpswatPatchEventLog)
class OpswatPatchEventLogAdmin(DisableEditMixin, admin.ModelAdmin):
    list_display = ('opswat_scheduled_product_installer', 'status', 'error_code', 'created', 'modified')
    search_fields = (
        'opswat_scheduled_product_installer__app_install__device_id',
        'opswat_scheduled_product_installer__app_install__real_device_id',
        'opswat_scheduled_product_installer__opswat_product_patch_installer__product__name',
        'status', 'error_code', 'details'
    )
    list_filter = (
        AutocompleteFilterFactory('Organisation', 'opswat_scheduled_product_installer__app_install__app_user__organisation'),
        AutocompleteFilterFactory('App Install', 'opswat_scheduled_product_installer__app_install'),
        AutocompleteFilterFactory('Product', 'opswat_scheduled_product_installer__opswat_product_patch_installer__product'),
        HasErrorCodeFilter,
        'error_code', 'status', 'created', 'modified',
    )
    autocomplete_fields = ('opswat_scheduled_product_installer',)
    readonly_fields = ('created', 'modified', 'cap_version_at_event_time')

    def cap_version_at_event_time(self, obj: OpswatPatchEventLog) -> str:
        """Display the CAP version that was active at the time this event was created"""
        version = obj.get_app_install_version_at_event_time()
        return version or "-"
    cap_version_at_event_time.short_description = "CAP Version at Event Time"  # type: ignore[attr-defined]

    def get_queryset(self, request: HttpRequest) -> QuerySet[OpswatPatchEventLog]:
        """Optimize queryset to prefetch related data"""
        return super().get_queryset(request).select_related(
            'opswat_scheduled_product_installer__opswat_product_patch_installer__product',
            'opswat_scheduled_product_installer__app_install__app_user',
            'opswat_scheduled_product_installer__creator'
        )


@admin.register(OpswatPatchAttempt)
class PatchAttemptAdmin(DisableEditMixin, admin.ModelAdmin):
    list_display = (
        "product", "vendor", "from_version", "to_version", "initiated_by", "status", "created",
        "finished_at"
    )
    search_fields = (
        "scheduled_product_installer__app_install__hostname",
        "scheduled_product_installer__app_install__device_id",
        "scheduled_product_installer__app_install__real_device_id",
        "scheduled_product_installer__opswat_product_patch_installer__product__name",
        "scheduled_product_installer__opswat_product_patch_installer__product__vendor__name",
        "status"
    )
    list_filter = (
        AutocompleteFilterFactory(
            "Organisation", "scheduled_product_installer__app_install__app_user__organisation"
        ),
        AutocompleteFilterFactory("App Install", "scheduled_product_installer__app_install"),
        AutocompleteFilterFactory(
            "Product", "scheduled_product_installer__opswat_product_patch_installer__product"
        ),
        AutocompleteFilterFactory("Initiated By", "initiated_by"),
        AutocompleteFilterFactory("Patch Job", "patch_job"),
        "status", "created", "modified"
    )
    inlines = [OpswatPatchEventLogInline]
    readonly_fields = ("created", "modified")
    raw_id_fields = ("patch_job", "scheduled_product_installer", "initiated_by")

    def get_queryset(self, request: HttpRequest) -> QuerySet[OpswatPatchAttempt]:
        """
        Optimize queryset to prefetch related data.
        """
        return super().get_queryset(request).select_related(
            "scheduled_product_installer__opswat_product_patch_installer__product__vendor",
            "scheduled_product_installer__app_install__app_user__organisation",
            "scheduled_product_installer__creator",
            "initiated_by",
            "patch_job"
        )

    def vendor(self, obj: OpswatPatchAttempt) -> str:
        """
        Display the vendor name for the product.
        """
        return obj.product.vendor.name
    vendor.short_description = "Vendor"  # type: ignore[attr-defined]


class PatchAttemptInlineAdmin(admin.TabularInline):
    model = OpswatPatchAttempt
    extra = 0
    fields = ("product", "from_version", "to_version", "status", "initiated_by", "created", "finished_at")
    readonly_fields = ('status', 'created', 'finished_at')
    can_delete = False
    verbose_name = "Patch Attempt"
    verbose_name_plural = "Patch Attempts"


@admin.register(OpswatPatchJob)
class OpswatPatchJobAdmin(DisableEditMixin, admin.ModelAdmin):
    list_display = ("organisation", "latest_attempt_product_names", "status", "initiated_by", "finished_at", "created")
    search_fields = ("organisation__name",)
    list_filter = (
        AutocompleteFilterFactory("Organisation", "organisation"),
        AutocompleteFilterFactory("Initiated By", "initiated_by"),
        "status"
    )
    readonly_fields = ("created", "modified", "finished_at")
    raw_id_fields = ("organisation", "initiated_by")
    inlines = [PatchAttemptInlineAdmin]

    def latest_attempt_product_names(self, obj: OpswatPatchJob) -> str:
        """
        Returns the latest patch attempt product names for display.
        """
        latest_attempts = obj.attempts.order_by("-created").distinct(
            "created", "scheduled_product_installer__opswat_product_patch_installer__product"
        )
        return ", ".join(
            attempt.scheduled_product_installer.opswat_product_patch_installer.product.name
            for attempt in latest_attempts if attempt.product
        )
    latest_attempt_product_names.short_description = "Products"  # type: ignore[attr-defined]

    def get_queryset(self, request: HttpRequest) -> QuerySet[OpswatPatchJob]:
        """
        Optimize queryset to prefetch related organisation and initiated_by fields.
        """
        return super().get_queryset(request).select_related(
            "organisation", "initiated_by"
        ).prefetch_related(
            "attempts__scheduled_product_installer__opswat_product_patch_installer__product__vendor",
            "attempts__app_install__app_user__organisation"
        )

