"""
Troubleshooting Dashboard Admin for OPSWAT Patch - Simplified version without changelist
"""
from datetime import datetime
from typing import Any, Dict, List, Optional

from django.contrib import admin
from django.db.models import Count, <PERSON>, Min, Avg, Case, When, IntegerField, Q
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse
from django.shortcuts import redirect
from django.urls import URLPattern

from common.admin import DisableEditMixin
from ..models import TroubleshootingDashboard, OpswatPatchEventLog
from ..models.constants import (
    COMPLETE, ERROR, SKIPPED_PATCH_VERSION_LOWER, TIMED_OUT,
    ROLLED_BACK, RETRYING, RETRYING_POPUP_TIMEOUT, RETRYING_USER_DISMISSED,
    PENDING, SCHEDULED, IN_PROGRESS, INSTALLED, WAITING_FOR_RESTART
)


@admin.register(TroubleshootingDashboard)
class TroubleshootingDashboardAdmin(DisableEditMixin, admin.ModelAdmin):
    """
    Admin interface for patch troubleshooting insights.
    Provides aggregated statistics grouped by product and signature.
    """

    class Media:
        css = {
            'all': ('admin/opswat_patch/css/opswat_admin.css',)
        }

    @staticmethod
    def get_platform_display(platform: Optional[str]) -> str:
        """Convert platform string to display name"""
        if not platform:
            return 'Unknown'
        platform_lower = platform.lower()
        if 'darwin' in platform_lower or 'mac' in platform_lower:
            return 'macOS'
        elif 'win' in platform_lower:
            return 'Windows'
        else:
            return platform

    def has_add_permission(self, request: HttpRequest) -> bool:
        """No adding allowed - this is a read-only dashboard"""
        return False

    def has_delete_permission(self, request: HttpRequest, obj: Optional[TroubleshootingDashboard] = None) -> bool:
        """No deletion allowed - this is a read-only dashboard"""
        return False

    def has_view_permission(self, request: HttpRequest, obj: Optional[TroubleshootingDashboard] = None) -> bool:
        """Always allow viewing the dashboard"""
        return request.user.is_authenticated and request.user.is_staff

    def has_change_permission(self, request: HttpRequest, obj: Optional[TroubleshootingDashboard] = None) -> bool:  # type: ignore[override]
        """
        Override DisableEditMixin to return True.
        This is needed to make the link clickable in Django admin.
        We don't actually allow changes - this is just for access.
        """
        return True

    def has_module_permission(self, request: HttpRequest) -> bool:
        """Always show in admin index for staff users"""
        return request.user.is_authenticated and request.user.is_staff

    def get_model_perms(self, request: HttpRequest) -> Dict[str, bool]:
        """
        Return empty permissions to hide from model list.
        """
        return {}

    def changelist_view(self, request: HttpRequest, extra_context: Optional[Dict[str, Any]] = None) -> HttpResponse:
        """
        Redirect to custom dashboard view
        """
        return redirect('admin:opswat_patch_troubleshooting_dashboard')

    def get_urls(self) -> List[URLPattern]:
        """Add custom URLs"""
        from django.urls import path

        urls = [
            path(
                '',
                self.admin_site.admin_view(self.dashboard_view),
                name='opswat_patch_troubleshooting_dashboard'
            ),
            path(
                'drill-down/',
                self.admin_site.admin_view(self.drill_down_view),
                name='opswat_patch_troubleshooting_drill_down'
            ),
        ]
        return urls

    def dashboard_view(self, request: HttpRequest) -> TemplateResponse:
        """
        Custom dashboard view without changelist
        """
        # Import these inside the function to avoid potential circular import issues
        from django.db.models import Exists as DjangoExists, OuterRef as DjangoOuterRef

        # Build queryset with filters - exclude AUTOMATEDTEST organizations
        queryset = self.model.objects.exclude(
            app_install__app_user__organisation__name__startswith='AUTOMATEDTEST_'
        )

        # Apply date filter using date range
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')

        if date_from:
            try:
                date_from_parsed = datetime.strptime(date_from, '%Y-%m-%d').date()
                queryset = queryset.filter(created__date__gte=date_from_parsed)
            except ValueError:
                pass

        if date_to:
            try:
                date_to_parsed = datetime.strptime(date_to, '%Y-%m-%d').date()
                queryset = queryset.filter(created__date__lte=date_to_parsed)
            except ValueError:
                pass

        # Apply other filters
        if request.GET.get('status'):
            queryset = queryset.filter(status=request.GET.get('status'))

        if request.GET.get('opswat_product_patch_installer__product__id__exact'):
            queryset = queryset.filter(
                opswat_product_patch_installer__product__id=request.GET.get('opswat_product_patch_installer__product__id__exact')
            )

        if request.GET.get('app_install__app_user__organisation__id__exact'):
            queryset = queryset.filter(
                app_install__app_user__organisation__id=request.GET.get('app_install__app_user__organisation__id__exact')
            )

        # First, create a subquery to check if a patch has error codes
        has_error_code_subquery = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer=DjangoOuterRef('pk'),
            error_code__gt=''
        )

        # Annotate each patch with whether it has error codes
        queryset_with_error_flag = queryset.annotate(
            has_error_code=DjangoExists(has_error_code_subquery)
        )

        # Aggregate statistics by product, signature, and platform
        stats = queryset_with_error_flag.values(
            'opswat_product_patch_installer__product__id',
            'opswat_product_patch_installer__product__name',
            'opswat_product_patch_installer__product__vendor__name',
            'signature__id',
            'signature__name',
            'signature__opswat_id',
            'app_install__platform'
        ).annotate(
            total_patches=Count('id'),
            successful_patches=Count(
                Case(
                    When(status__in=[COMPLETE, SKIPPED_PATCH_VERSION_LOWER], then=1),
                    output_field=IntegerField()
                )
            ),
            # Failed (Error) - actual errors with error codes
            failed_error_patches=Count(
                Case(
                    When(
                        status__in=[ERROR, TIMED_OUT, ROLLED_BACK],
                        has_error_code=True,
                        then=1
                    ),
                    output_field=IntegerField()
                )
            ),
            # Failed (User) - errors without error codes
            failed_user_patches=Count(
                Case(
                    When(
                        status__in=[ERROR, TIMED_OUT, ROLLED_BACK],
                        has_error_code=False,
                        then=1
                    ),
                    output_field=IntegerField()
                )
            ),
            # In Progress - all non-terminal statuses
            in_progress_patches=Count(
                Case(
                    When(status__in=[PENDING, SCHEDULED, IN_PROGRESS, INSTALLED, WAITING_FOR_RESTART, RETRYING, RETRYING_POPUP_TIMEOUT, RETRYING_USER_DISMISSED], then=1),
                    output_field=IntegerField()
                )
            ),
            unique_devices=Count('app_install__device_id', distinct=True),
            unique_orgs=Count('app_install__app_user__organisation', distinct=True),
            avg_retry_count=Avg('retry_count'),
            max_retry_count=Max('retry_count'),
            first_attempt=Min('created'),
            last_attempt=Max('created'),
        ).order_by('-failed_error_patches', '-total_patches')

        # Get top 5 error codes per product/signature/platform efficiently

        # Get error codes with row numbers per group
        all_error_codes = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer__in=queryset,
            error_code__gt=''
        ).values(
            'opswat_scheduled_product_installer__opswat_product_patch_installer__product__id',
            'opswat_scheduled_product_installer__signature__id',
            'opswat_scheduled_product_installer__app_install__platform',
            'error_code'
        ).annotate(
            count=Count('id')
        ).order_by(
            'opswat_scheduled_product_installer__opswat_product_patch_installer__product__id',
            'opswat_scheduled_product_installer__signature__id',
            'opswat_scheduled_product_installer__app_install__platform',
            '-count'
        )

        # Group error codes by product/signature/platform
        from collections import defaultdict
        error_codes_by_product = defaultdict(list)
        for error in all_error_codes:
            key = (
                error['opswat_scheduled_product_installer__opswat_product_patch_installer__product__id'],
                error['opswat_scheduled_product_installer__signature__id'],
                error['opswat_scheduled_product_installer__app_install__platform']
            )
            if len(error_codes_by_product[key]) < 5:  # Only keep top 5
                error_codes_by_product[key].append({
                    'error_code': error['error_code'],
                    'count': error['count']
                })

        # Calculate success rate for each group and add error codes
        stats_list = []
        for stat in stats:
            total = stat['total_patches']
            successful = stat['successful_patches']
            stat['success_rate'] = (successful / total * 100) if total > 0 else 0
            # Failure rate is only for actual errors, not user dismissals
            stat['failure_rate'] = ((stat['failed_error_patches'] / total) * 100) if total > 0 else 0

            # Clean up platform display
            stat['platform_display'] = self.get_platform_display(stat.get('app_install__platform'))

            # Get error codes from pre-fetched data
            key = (
                stat['opswat_product_patch_installer__product__id'],
                stat['signature__id'],
                stat['app_install__platform']
            )
            stat['top_error_codes'] = error_codes_by_product.get(key, [])
            stats_list.append(stat)

        # Handle sorting
        sort_by = request.GET.get('sort', '-failed_error_patches')  # Default sort
        reverse_sort = sort_by.startswith('-')
        sort_key = sort_by.lstrip('-')

        # Define valid sort fields
        valid_sort_fields = {
            'product': 'opswat_product_patch_installer__product__name',
            'vendor': 'opswat_product_patch_installer__product__vendor__name',
            'signature': 'signature__name',
            'platform': 'platform_display',
            'total_patches': 'total_patches',
            'successful_patches': 'successful_patches',
            'failed_error_patches': 'failed_error_patches',
            'success_rate': 'success_rate',
            'failure_rate': 'failure_rate',
        }

        if sort_key in valid_sort_fields:
            actual_key = valid_sort_fields[sort_key]

            # Special handling for text fields to ensure proper sorting
            if sort_key in ['product', 'vendor', 'signature', 'platform']:
                stats_list.sort(
                    key=lambda x: (x.get(actual_key) or '').lower(),
                    reverse=reverse_sort
                )
            else:
                # Numeric fields
                stats_list.sort(
                    key=lambda x: x.get(actual_key, 0) if x.get(actual_key) is not None else 0,
                    reverse=reverse_sort
                )

        # Overall statistics - only count failures with error codes
        # Subquery to check if a scheduled installer has error codes
        has_error_code = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer=DjangoOuterRef('pk'),
            error_code__gt=''
        )

        # Use a single aggregate query for better performance

        overall_stats = queryset.aggregate(
            total_patches=Count('id'),
            successful_patches=Count(
                Case(
                    When(status__in=[COMPLETE, SKIPPED_PATCH_VERSION_LOWER], then=1),
                    output_field=IntegerField()
                )
            ),
            failed_error_patches=Count(
                Case(
                    When(
                        Q(status__in=[ERROR, TIMED_OUT, ROLLED_BACK]) & DjangoExists(has_error_code),
                        then=1
                    ),
                    output_field=IntegerField()
                )
            ),
            failed_user_patches=Count(
                Case(
                    When(
                        Q(status__in=[ERROR, TIMED_OUT, ROLLED_BACK]) & ~DjangoExists(has_error_code),
                        then=1
                    ),
                    output_field=IntegerField()
                )
            ),
            in_progress_patches=Count(
                Case(
                    When(status__in=[PENDING, SCHEDULED, IN_PROGRESS, INSTALLED, WAITING_FOR_RESTART, RETRYING, RETRYING_POPUP_TIMEOUT, RETRYING_USER_DISMISSED], then=1),
                    output_field=IntegerField()
                )
            ),
            unique_products=Count('opswat_product_patch_installer__product', distinct=True),
            unique_signatures=Count('signature', distinct=True, filter=Q(signature__isnull=False)),
            unique_devices=Count('app_install__device_id', distinct=True),
            unique_orgs=Count('app_install__app_user__organisation', distinct=True)
        )

        if overall_stats['total_patches'] > 0:
            overall_stats['success_rate'] = (overall_stats['successful_patches'] / overall_stats['total_patches']) * 100
            overall_stats['failure_rate'] = (overall_stats['failed_error_patches'] / overall_stats['total_patches']) * 100
            overall_stats['user_failure_rate'] = (overall_stats['failed_user_patches'] / overall_stats['total_patches']) * 100
        else:
            overall_stats['success_rate'] = 0
            overall_stats['failure_rate'] = 0
            overall_stats['user_failure_rate'] = 0

        # Additional insights
        insights = {
            'high_failure_products': [],
            'high_retry_products': [],
            'recommendations': []
        }

        # Products with high failure rates (only considering true failures with error codes)
        for stat in stats:
            if stat['total_patches'] >= 10 and stat.get('failure_rate', 0) > 30:
                insights['high_failure_products'].append({
                    'product': stat['opswat_product_patch_installer__product__name'],
                    'vendor': stat['opswat_product_patch_installer__product__vendor__name'],
                    'signature': stat['signature__name'],
                    'signature_opswat_id': stat['signature__opswat_id'],
                    'failure_rate': stat['failure_rate'],
                    'total_patches': stat['total_patches']
                })

            # Products with high retry counts
            if stat['avg_retry_count'] and stat['avg_retry_count'] > 2:
                insights['high_retry_products'].append({
                    'product': stat['opswat_product_patch_installer__product__name'],
                    'vendor': stat['opswat_product_patch_installer__product__vendor__name'],
                    'signature': stat['signature__name'],
                    'signature_opswat_id': stat['signature__opswat_id'],
                    'avg_retry': stat['avg_retry_count'],
                    'max_retry': stat['max_retry_count']
                })

        # Generate recommendations
        if overall_stats['failure_rate'] > 20:
            insights['recommendations'].append({
                'severity': 'high',
                'message': f"High overall failure rate ({overall_stats['failure_rate']:.1f}%). Review error logs and consider adjusting patch deployment strategy."
            })

        if len(insights['high_failure_products']) > 0:
            insights['recommendations'].append({
                'severity': 'medium',
                'message': f"{len(insights['high_failure_products'])} products have failure rates above 30%. Consider investigating these specific products."
            })

        # Build filter data
        from opswat.models import Product
        from appusers.models import Organisation

        # Use exists() subquery for better performance
        from django.db.models import Exists, OuterRef
        from opswat_patch.models import OpswatProductPatchInstaller

        # Get products that have scheduled installers
        available_products = Product.objects.filter(
            Exists(
                OpswatProductPatchInstaller.objects.filter(
                    product=OuterRef('pk'),
                    scheduled_product_installers__isnull=False
                )
            )
        ).select_related('vendor').order_by('name')

        # Get organizations more efficiently - exclude AUTOMATEDTEST organizations
        available_orgs = Organisation.objects.filter(
            id__in=self.model.objects.exclude(
                app_install__app_user__organisation__name__startswith='AUTOMATEDTEST_'
            ).values_list('app_install__app_user__organisation__id', flat=True).distinct()[:500]
        ).exclude(
            name__startswith='AUTOMATEDTEST_'
        ).order_by('name')

        available_statuses = [
            {'value': status[0], 'display': status[1]}
            for status in self.model._meta.get_field('status').choices
        ]

        context = {
            'title': 'Patch Troubleshooting Dashboard',
            'stats': stats_list,
            'overall_stats': overall_stats,
            'insights': insights,
            'has_filters': bool(request.GET),
            'opts': self.model._meta,
            'has_view_permission': True,
            'site_title': self.admin_site.site_title,
            'site_header': self.admin_site.site_header,
            'available_products': available_products,
            'available_orgs': available_orgs,
            'available_statuses': available_statuses,
            'current_filters': request.GET,
            'current_sort': sort_by,
            'sort_key': sort_key,
            'reverse_sort': reverse_sort,
        }

        return TemplateResponse(
            request,
            'admin/opswat_patch/troubleshooting_dashboard_simple.html',
            context
        )

    def drill_down_view(self, request: HttpRequest) -> TemplateResponse:
        """
        Detailed drill-down view for specific product/signature combination
        """
        product_id = request.GET.get('product_id')
        signature_id = request.GET.get('signature_id')
        platform = request.GET.get('platform')

        if not product_id:
            from django.http import Http404
            raise Http404("Product ID is required")

        # Get detailed records - exclude AUTOMATEDTEST organizations
        queryset = self.model.objects.filter(
            opswat_product_patch_installer__product__id=product_id
        ).exclude(
            app_install__app_user__organisation__name__startswith='AUTOMATEDTEST_'
        )

        if signature_id and signature_id != 'None':
            try:
                queryset = queryset.filter(signature__id=int(signature_id))
            except (ValueError, TypeError):
                queryset = queryset.filter(signature__isnull=True)
        else:
            queryset = queryset.filter(signature__isnull=True)

        # Filter by platform if provided
        if platform:
            queryset = queryset.filter(app_install__platform=platform)

        # Get detailed statistics
        records = queryset.select_related(
            'opswat_product_patch_installer__product__vendor',
            'app_install__app_user__organisation',
            'app_install__app_user',
            'app_install',
            'signature',
            'creator'
        ).prefetch_related(
            'event_logs'
        ).order_by('-created')[:100]

        # Error code breakdown
        error_breakdown = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer__in=queryset,
            error_code__gt=''
        ).values('error_code').annotate(
            count=Count('id'),
            first_seen=Min('created'),
            last_seen=Max('created')
        ).order_by('-count')

        # Get platform display name
        platform_display = self.get_platform_display(platform) if platform else None

        context = {
            'title': 'Patch Troubleshooting Drill-Down',
            'records': records,
            'error_breakdown': error_breakdown,
            'product_id': product_id,
            'signature_id': signature_id,
            'platform': platform,
            'platform_display': platform_display,
            'opts': self.model._meta,
            'has_view_permission': True,
            'site_title': self.admin_site.site_title,
            'site_header': self.admin_site.site_header,
        }

        return TemplateResponse(
            request,
            'admin/opswat_patch/troubleshooting_drill_down.html',
            context
        )
