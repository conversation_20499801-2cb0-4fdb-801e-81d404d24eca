from django.conf import settings
from django.db import models
from django.utils import timezone

from appusers.models import AppInstall
from model_utils.models import TimeStampedModel

from opswat.models import Product
from opswat_patch.models import ACTION_STATUS_CHOICES, PENDING
from opswat_patch.models.constants import PATCH_JOB_STATUS_CHOICES, IN_PROGRESS, COMPLETE, ERROR, SHORT_STATUS, \
    STATUS_AGGREGATION_GROUPS, TERMINAL_STATUSES
from opswat_patch.mixins import StatusMixin


class OpswatPatchJob(TimeStampedModel):
    """
    Represents a high-level patch job, which can include one or more patch attempts
    on one or more devices for one or more products.
    Patch job is an action initiated by a user through the web dashboard with an intention
    to patch specific products on specific devices.
    Schema:
    ###################################
    # OpswatPatchJob (action) - patch Chrome, Firefox, and VLC on devices Device 1 and Device 2
    ###################################
    # - OpswatPatchAttempt - patch Chrome on Device 1
    # - OpswatPatchAttempt - patch Firefox on Device 1
    # - OpswatPatchAttempt - patch VLC on Device 1
    # - OpswatPatchAttempt - patch Chrome on Device 2
    # - OpswatPatchAttempt - patch Firefox on Device 2
    # - OpswatPatchAttempt - patch VLC on Device 2
    ###################################
    """
    organisation = models.ForeignKey(
        to="organisations.Organisation",
        verbose_name="Organisation",
        on_delete=models.CASCADE,
        related_name="opswat_patch_jobs",
        help_text="The organisation this patch job belongs to."
    )
    initiated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="opswat_patch_jobs_initiated",
    )
    finished_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when the patch job was completed. Null if still in progress."
                  "Completed means all attempts are in terminal statuses."
    )
    status = models.CharField(max_length=50, choices=PATCH_JOB_STATUS_CHOICES, default=IN_PROGRESS)

    def __str__(self):
        """
        Returns a string representation of the patch job.
        """
        return f"PatchJob {self.pk} for {self.organisation.name}"

    class Meta:
        verbose_name = "OPSWAT Patch Job"
        verbose_name_plural = "OPSWAT Patch Jobs"
        indexes = [
            models.Index(fields=["status"]),
            models.Index(fields=["organisation"])
        ]


    def update_status(self, force_save=False) -> bool:
        """
        Update the status and finished_at fields of the patch job based on the statuses of its related attempts.

        Args:
            force_save: If True, always save even if nothing changed

        Returns:
            True if the status was updated, False otherwise

        Logic:
        - If there are no attempts, set status to IN_PROGRESS and clear finished_at.
        - If all attempts are in TERMINAL_STATUSES:
            - If any are ERROR, set status to ERROR and set finished_at to now.
            - Otherwise, all must be in COMPLETE group, set status to COMPLETE and set finished_at to now.
        - If any attempt is IN_PROGRESS, set status to IN_PROGRESS and clear finished_at.
        - Otherwise, set the status to IN_PROGRESS and clear finished_at.

        After updating, the model is saved only if status or finished_at changed.
        """
        # Use only() to fetch just the status field for performance
        attempt_statuses = set(self.attempts.only("status").values_list("status", flat=True))

        now = timezone.now()
        new_status = IN_PROGRESS
        new_finished_at = None

        if not attempt_statuses:
            # no attempts yet, job is considered in progress
            pass
        elif all(status in TERMINAL_STATUSES for status in attempt_statuses):
            if any(status in STATUS_AGGREGATION_GROUPS[ERROR] for status in attempt_statuses):
                new_status = ERROR
                new_finished_at = now
            else:
                # All are terminal and none are errors, so all must be in COMPLETE group
                new_status = COMPLETE
                new_finished_at = now
        elif any(status in STATUS_AGGREGATION_GROUPS[IN_PROGRESS] for status in attempt_statuses):
            pass

        # Only save if something changed
        if force_save or self.status != new_status or self.finished_at != new_finished_at:
            self.status = new_status
            self.finished_at = new_finished_at
            self.save(update_fields=["status", "finished_at", "modified"])
            return True
        return False

    @property
    def is_finished(self) -> bool:
        """
        Returns True if the patch job is finished, otherwise False.
        """
        return self.status in [COMPLETE, ERROR]

    @property
    def short_status(self) -> str:
        """
        Returns a short, human-readable version of the object's status. This
        property uses a predefined mapping to turn the internal status into a
        simpler and more digestible form for users. If no mapping is found for
        the given status, it returns the original status value.
        """
        return SHORT_STATUS.get(self.status, self.status)

    @property
    def latest_attempt(self):
        """
        Returns the latest patch attempt for this job.
        """
        return self.attempts.order_by("-created").first()

    @property
    def affected_devices_count(self) -> int:
        """
        Returns the count of distinct devices affected by this patch job.
        """
        return self.attempts.values_list(
        "scheduled_product_installer__app_install__device_id", flat=True
        ).distinct().count()

    @property
    def product_names(self) -> list[str]:
        """
        Returns a list of product names involved in this patch job.
        """
        return list(self.attempts.values_list(
            "scheduled_product_installer__opswat_product_patch_installer__product__name", flat=True
        ).distinct().order_by("scheduled_product_installer__opswat_product_patch_installer__product__name"))


class OpswatPatchAttempt(TimeStampedModel, StatusMixin):
    """
    Represents a historical action of scheduling a patch install
    that a user (or automation) performed at a specific point in time.
    This is equivalent to a user coming to the dashboard and clicking to patch a single software for a single device.

    If a user performs such an action by scheduling multiple patches at once for multiple software or devices,
    individual records are recorded for each of the unique (software, device) pairs
    and connected to a single OpswatPatchJob.
    The related model OpswatScheduledProductInstaller is materialization of the currently active process
    of patching with CAP.
    While OpswatPatchAttempt is a user-centric historical view of this process.
    """
    patch_job = models.ForeignKey(
        to=OpswatPatchJob,
        on_delete=models.CASCADE,
        related_name="attempts",
        help_text="The patch job this attempt belongs to."
    )
    scheduled_product_installer = models.ForeignKey(
        to="opswat_patch.OpswatScheduledProductInstaller",
        on_delete=models.CASCADE,
        related_name="patch_attempts",
    )
    status = models.CharField(max_length=50, choices=ACTION_STATUS_CHOICES, default=PENDING)
    initiated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="initiated_patch_attempts",
    )
    from_version = models.CharField(
        verbose_name="From Version",
        max_length=50,
        help_text="The product version being patched from (original installed version)."
    )
    to_version = models.CharField(
        verbose_name="To Version",
        max_length=50,
        help_text="The patch installer version being applied."
    )
    finished_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when the patch attempt was completed."
    )

    class Meta:
        verbose_name = "OPSWAT Patch Attempt"
        verbose_name_plural = "OPSWAT Patch Attempts"
        indexes = [
            models.Index(fields=["status"]),
            models.Index(fields=["scheduled_product_installer"]),
        ]
        ordering = ("-created",)

    def save(self, *args, **kwargs):
        """
        Override save method to update the patch job status after saving the attempt.

        Note: When updating from event logs, use direct update() queries to avoid this hook.
        """
        is_new = self.pk is None
        old_status = None

        # Track the old status if this is an existing attempt
        if not is_new:
            old_status = OpswatPatchAttempt.objects.filter(pk=self.pk).values_list('status', flat=True).first()

        super().save(*args, **kwargs)

        # Update job status if it's a new attempt or if the status has changed
        # Terminal status updates from event logs use direct update() queries and bypass this hook
        if is_new or (old_status is not None and old_status != self.status):
            self.patch_job.update_status()

    def __str__(self) -> str:
        """
        Returns a string representation of the patch attempt.
        """
        return (f"Patch Attempt for {self.scheduled_product_installer.opswat_product_patch_installer.product.name}"
                f" from {self.from_version} to {self.to_version} (Status: {self.status})")

    @property
    def latest_event(self):
        """
        Returns the latest event log for this scheduled installer.
        """
        return self.event_logs.order_by("-created").first()

    @property
    def product(self) -> Product:
        """
        Returns the product associated with this patch attempt.
        """
        return self.scheduled_product_installer.opswat_product_patch_installer.product

    @property
    def app_install(self) -> AppInstall:
        """
        Returns the app install associated with this patch attempt.
        """
        return self.scheduled_product_installer.app_install
