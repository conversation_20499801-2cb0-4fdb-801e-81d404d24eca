from django.utils.translation import gettext_lazy as _

from opswat_patch.models.base import OpswatScheduledProductInstaller


class TroubleshootingDashboard(OpswatScheduledProductInstaller):
    """
    Proxy model for OpswatScheduledProductInstaller to provide a troubleshooting dashboard view.
    This model provides aggregated statistics for troubleshooting patch failures.
    """

    class Meta:
        proxy = True
        verbose_name = _("Patch Troubleshooting Dashboard")
        verbose_name_plural = _("Patch Troubleshooting Dashboard")
