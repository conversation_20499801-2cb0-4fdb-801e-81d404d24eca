from typing import Optional, Any

from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _, gettext

from model_utils.models import TimeStampedModel

from appusers.models import AppVersion
from opswat_patch.models.base import OpswatScheduledProductInstaller
from opswat_patch.models.constants import (
    ACTION_STATUS_CHOICES, ERROR, MAX_RETRY_ATTEMPTS, TERMINAL_STATUSES,
    RETRYING_USER_DISMISSED, RETRYING_POPUP_TIMEOUT
)
from opswat_patch.mixins import StatusMixin
from opswat_patch.models.error_codes import get_error_message
from opswat_patch.models.patch_attempt import OpswatPatchAttempt


class OpswatPatchEventLog(TimeStampedModel, StatusMixin):
    """
    Model to track status updates and events for an OpswatScheduledProductInstaller action.
    """
    opswat_scheduled_product_installer = models.ForeignKey(
        OpswatScheduledProductInstaller,
        on_delete=models.CASCADE,
        related_name='event_logs',
        verbose_name=_('Scheduled Product Installer'),
    )
    # make this field mandatory after the initial migration
    patch_attempt = models.ForeignKey(
        to="opswat_patch.OpswatPatchAttempt",
        on_delete=models.CASCADE,
        related_name="event_logs",
        null=True,
        blank=True,
        verbose_name=_("Patch Attempt"),
        help_text=_("The patch attempt associated with this event log.")
    )
    status = models.CharField(
        _('Action Status'),
        max_length=50,
        choices=ACTION_STATUS_CHOICES,
        help_text=_('The current status of the patch action event.')
    )
    error_code = models.CharField(
        _('Error Code'),
        max_length=50,
        blank=True,
        default="",
        help_text=_('Optional error code if the status is Error.')
    )
    details_public_facing = models.TextField(
        _('Details Public Facing'),
        blank=True,
        default="",
        help_text=_('Additional details about the event for public facing purposes.')
    )
    details = models.TextField(
        _('Details'),
        blank=True,
        default="",
        help_text=_('Additional details about the event. (internal use only, contains sensitive information)')
    )


    def save(self, *args: Any, **kwargs: Any) -> None:
        """
        Save the event log and optionally update the associated installer's and patch attempt's statuses.

        If `update_installer` is True (default), the status of the associated
        `OpswatScheduledProductInstaller` and `OpswatPatchAttempt` will be updated
        based on the current event log's status. Retry logic and error escalation
        are also handled here.
        """
        update_installer = kwargs.pop('update_installer', True)
        super().save(*args, **kwargs)

        if not update_installer:
            return

        # Update the parent installer's status to match this event log
        installer = self.opswat_scheduled_product_installer

        # Prepare update fields to minimize database writes
        update_fields = ['status', 'modified']
        original_status = installer.status
        original_retry_count = installer.retry_count

        # Handle retry logic for the new retry statuses
        if self.status in [RETRYING_USER_DISMISSED, RETRYING_POPUP_TIMEOUT]:
            # Only count retries towards the limit if:
            # Status is RETRYING_USER_DISMISSED (user actively dismissed the popup)
            #
            # We DO NOT count retries with status RETRYING_POPUP_TIMEOUT because
            # the user may have been away from the machine and never saw the popup
            if self.status == RETRYING_USER_DISMISSED:
                installer.retry_count += 1
                update_fields.append('retry_count')

                # If retry count exceeds MAX_RETRY_ATTEMPTS, transition to ERROR instead
                if installer.retry_count > MAX_RETRY_ATTEMPTS:
                    installer.status = ERROR
                    installer.save(update_fields=update_fields)

                    # Update patch attempt if exists
                    if self.patch_attempt_id:
                        self._update_patch_attempt_status(ERROR)

                    # Create an additional event log to record the auto-transition to ERROR
                    error_log = OpswatPatchEventLog(
                        opswat_scheduled_product_installer=installer,
                        patch_attempt_id=self.patch_attempt_id,
                        status=ERROR,
                        details=gettext("Automatically transitioned to error after %(max_attempts)s user-dismissed retry attempts") % {'max_attempts': MAX_RETRY_ATTEMPTS},
                        details_public_facing=gettext("User dismissed patching pop-up.")
                    )
                    error_log.save(update_installer=False)  # Prevent recursion
                    return

            # Always update the status to RETRYING
            installer.status = self.status
        else:
            # Reset retry count only when transitioning to a terminal status
            # This prevents the retry counter from resetting when going to IN_PROGRESS
            # and allows us to track retries across the entire patch journey
            if self.status in TERMINAL_STATUSES and installer.retry_count > 0:
                installer.retry_count = 0
                update_fields.append('retry_count')
            installer.status = self.status

        # Only save if something actually changed
        if installer.status != original_status or installer.retry_count != original_retry_count:
            installer.save(update_fields=update_fields)

        # Update patch attempt if exists
        if self.patch_attempt_id:
            self._update_patch_attempt_status(self.status)

    def _update_patch_attempt_status(self, status: str) -> None:
        """
        Update the status of the associated patch attempt and patch job based on this event log's status.
        """
        if not self.patch_attempt_id:
            return

        update_data = {
            "status": status,
            "modified": timezone.now()
        }

        if self.is_finished:
            update_data["finished_at"] = self.modified

        # Use update() to avoid fetching the object first and triggering save() hooks
        OpswatPatchAttempt.objects.filter(id=self.patch_attempt_id).update(**update_data)

        # If this is a terminal status, update the job
        if status in TERMINAL_STATUSES:
            # Get the patch job and update it
            try:
                attempt = OpswatPatchAttempt.objects.select_related('patch_job').only('patch_job_id').get(id=self.patch_attempt_id)
                attempt.patch_job.update_status()
            except OpswatPatchAttempt.DoesNotExist:
                pass


    def get_app_install_version_at_event_time(self) -> Optional[str]:
        """
        Get the app install version that was active at the time this event was created.
        Uses the HistoricalAppInstall model to find the version at the event's creation time.

        WARNING: This method relies on the historical/archive database tables and should ONLY
        be used in Django admin interfaces or for reporting purposes. DO NOT use this in
        application code as it may have performance implications and depends on the
        existence of historical records.

        Returns:
            str: The app version string at the time of event creation, or None if no historical record exists
        """

        app_install = self.opswat_scheduled_product_installer.app_install

        # Get the historical record that was active at the time of this event
        historical_record = app_install.history.filter(
            history_date__lte=self.created
        ).order_by('-history_date').first()

        if historical_record:
            # Try to get version from the AppVersion relationship first
            if hasattr(historical_record, 'version_id') and historical_record.version_id:
                try:
                    version_obj = AppVersion.objects.get(id=historical_record.version_id)
                    return version_obj.get_version_string()
                except AppVersion.DoesNotExist:
                    pass

            # Fall back to the app_version string field
            if historical_record.app_version:
                return historical_record.app_version.split("-")[0]

        # If no historical record found, return None (will display as "-" in admin)
        return None

    def __str__(self) -> str:
        return f"Event Log for {self.opswat_scheduled_product_installer_id}: {self.get_status_display()}"

    def get_error_message(self) -> str:
        """
        Get the human-readable error message for this event log's error code.

        Returns:
            The human-readable error message or empty string if no error code.
        """
        if not self.error_code:
            return ""
        return get_error_message(self.error_code)

    class Meta:
        verbose_name = _('OPSWAT Patch Event Log')
        verbose_name_plural = _('OPSWAT Patch Event Logs')
        ordering = ['-created']
