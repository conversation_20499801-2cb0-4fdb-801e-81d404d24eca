

from typing import TYPE_CHECKING, Optional

from django.db import models
from django.conf import settings

from model_utils.models import TimeStampedModel

from appusers.models import AppInstall
from opswat_patch.models.constants import (
    PENDING, ACTION_STATUS_CHOICES, TERMINAL_STATUSES
)
from opswat_patch.mixins import StatusMixin

from opswat.models import ProductSignature

if TYPE_CHECKING:
    from opswat_patch.models import OpswatPatchAttempt

class NonTerminalManager(models.Manager):
    """
    Custom manager to exclude terminal statuses from the queryset.
    """
    def get_queryset(self) -> models.QuerySet:
        return super().get_queryset().exclude(status__in=TERMINAL_STATUSES)


class OpswatScheduledProductInstaller(TimeStampedModel, StatusMixin):
    """
    Model representing a scheduled product installer action.
    Which means a creator has scheduled an installation of a product installer on an app install.
    """
    opswat_product_patch_installer = models.ForeignKey('opswat_patch.OpswatProductPatchInstaller', on_delete=models.CASCADE, related_name='scheduled_product_installers')
    signature = models.ForeignKey(
        ProductSignature,
        verbose_name="Product Signature",
        on_delete=models.PROTECT,
        related_name="scheduled_product_installers",
        help_text="The product signature associated with this scheduled product installer."
    )
    app_install = models.ForeignKey(AppInstall, on_delete=models.CASCADE, related_name='scheduled_product_installers')
    creator = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='scheduled_product_installers')
    status = models.CharField(max_length=50, choices=ACTION_STATUS_CHOICES, default=PENDING)
    retry_count = models.PositiveIntegerField(default=0, help_text="Number of times this installer has been retried")

    objects = models.Manager()
    non_terminal = NonTerminalManager()

    def __str__(self) -> str:
        app_user_email = self.app_install.app_user.email if self.app_install and self.app_install.app_user else 'Unknown'
        return f"{self.opswat_product_patch_installer.product} on {self.app_install.device_id} (User: {app_user_email})"

    class Meta:
        verbose_name = "OPSWAT Scheduled Product Installer"
        verbose_name_plural = "OPSWAT Scheduled Product Installers"
        unique_together = [
            ('opswat_product_patch_installer', 'app_install')
        ]
        indexes = [
            models.Index(fields=["status"]),
        ]

    @property
    def latest_attempt(self) -> Optional['OpswatPatchAttempt']:
        """
        Returns the latest OpswatPatchAttempt for this scheduled product installer.
        """
        return self.patch_attempts.order_by("-created").first()
