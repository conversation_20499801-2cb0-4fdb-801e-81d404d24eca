from django.utils.translation import gettext_lazy as _

PENDING = 'pending'
SCHEDULED = 'scheduled'
IN_PROGRESS = 'in_progress'
INSTALLED = 'installed'
WAITING_FOR_RESTART = 'waiting_for_restart'
COMPLETE = 'complete'
TIMED_OUT = 'timed_out'
ROLLED_BACK = 'rolled_back'
ERROR = 'error'
# When the patching is skipped on CAP because the version found on CAP is higher than the scheduled version
SKIPPED_PATCH_VERSION_LOWER = 'skipped_patch_version_lower'

# Legacy error code
RETRYING = 'retrying'

# User has dismissed the popup on CAP indicating to close the running application to patch it
RETRYING_USER_DISMISSED = 'retrying_user_dismissed'
# User has not reacted to the popup on CAP indicating to close the running application to patch it
RETRYING_POPUP_TIMEOUT = 'retrying_popup_timeout'

# Maximum number of retry attempts before auto-transitioning to ERROR
MAX_RETRY_ATTEMPTS = 3

ACTION_STATUS_CHOICES = [
    (PENDING, _('Waiting for device to run patch.')),
    (SCHEDULED, _('Scheduled on device.')),
    (IN_PROGRESS, _('Patching software is in progress.')),
    (INSTALLED, _('Files for patch installed.')),
    (WAITING_FOR_RESTART, _('Restart required to complete the installation.')),
    (RETRYING, _('Retrying patch installation.')),
    (RETRYING_POPUP_TIMEOUT, _('Retrying - User has not reacted to the patching popup.')),
    (RETRYING_USER_DISMISSED, _('Retrying - User dismissed the patching popup.')),
    (COMPLETE, _('Patch successfully complete.')),
    (TIMED_OUT, _('Failed due to timeout.')),
    (ROLLED_BACK, _('Patch rolled back due to an issue.')),
    (ERROR, _('An error occurred during the patch process.')),
    (SKIPPED_PATCH_VERSION_LOWER, _('Skipped patching as the version on the devices is higher or equal to the scheduled version.')),
]


SHORT_STATUS = {
    PENDING: _('Pending'),
    SCHEDULED: _('Scheduled'),
    IN_PROGRESS: _('In Progress'),
    INSTALLED: _('Installed'),
    WAITING_FOR_RESTART: _('Waiting for Restart'),
    RETRYING: _('Retrying'),
    COMPLETE: _('Complete'),
    TIMED_OUT: _('Timed Out'),
    ROLLED_BACK: _('Rolled Back'),
    ERROR: _('Error'),
    SKIPPED_PATCH_VERSION_LOWER: _('Skipped'),
    RETRYING_POPUP_TIMEOUT: _('Retrying - Popup Timeout'),
    RETRYING_USER_DISMISSED: _('Retrying - User Dismissed'),
}


STATUS_AGGREGATION_GROUPS = {
    IN_PROGRESS: [PENDING, SCHEDULED, IN_PROGRESS, INSTALLED, WAITING_FOR_RESTART, RETRYING, RETRYING_POPUP_TIMEOUT, RETRYING_USER_DISMISSED],
    COMPLETE: [COMPLETE, SKIPPED_PATCH_VERSION_LOWER],
    ERROR: [TIMED_OUT, ROLLED_BACK, ERROR],
}

# Statuses after which CAP will not retry
TERMINAL_STATUSES = STATUS_AGGREGATION_GROUPS[COMPLETE] + STATUS_AGGREGATION_GROUPS[ERROR]

PATCH_JOB_STATUS_CHOICES = [
    (IN_PROGRESS, _("Patching software is in progress.")),
    (COMPLETE, _("Patch successfully complete.")),
    (ERROR, _("An error occurred during the patch process."))
]

PATCH_JOB_SHORT_STATUS = {
    IN_PROGRESS: _("In Progress"),
    COMPLETE: _("Complete"),
    ERROR: _("Error"),
}
