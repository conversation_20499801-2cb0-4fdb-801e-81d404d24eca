"""
Error code mappings for MetaDefender Endpoint Security SDK.
https://software.opswat.com/OESIS_V4/html/c_return_codes.html#error-codes
"""
from django.utils.translation import gettext_lazy as _


# Success codes
WAAPI_OK = "0"
WAAPI_OK_TRUE = "1"
WAAPI_OK_FALSE = "2"
WAAPI_PARTIAL_SUCCESS = "3"
WAAPI_OPERATION_INPROGRESS = "4"
WAAPI_CACHE_DATA_STALE = "5"
WAAPI_OK_MORE_RESULTS = "6"
WAAPI_OK_IN_USE = "7"
WAAPI_OK_OUTDATED_DATABASE = "8"
WAAPI_FAILED_TO_GET_PASSWORD_PROTECTION = "9"
WAAPI_OK_RESTART_TO_ENABLE = "10"
WAAPI_OK_NO_KEYBOARD_DETECTED = "11"
WAAPI_OK_CANCELED = "12"
WAAPI_OK_RESULT_BEFORE_LOST_CONTROL = "13"
WAAPI_OK_VALIDATION_FAILED = "14"
WAAPI_OK_CANNOT_VALIDATE = "15"
WAAPI_OK_VALIDATION_NOT_SUPPORTED = "16"
WAAPI_OK_PENDING_REMOVE_APPLE_DEVICES = "17"

# HTTP success codes
HTTP_OK = "200"
HTTP_CREATED = "201"
HTTP_NON_AUTHORITATIVE = "203"
HTTP_NO_CONTENT = "204"
HTTP_RESET_CONTENT = "205"
HTTP_PARTIAL_CONTENT = "206"

# VMOD data status codes
VMOD_DATA_LIVE = "1000"
VMOD_DATA_CACHED = "1001"
VMOD_DATA_EXPIRED = "1002"
WA_VMOD_INSTALLATION_NEED_RESTART = "1003"
WA_VMOD_PARTIAL_INSTALLATION_NEED_RESTART = "1004"
WAAPI_OK_NO_BACKUP_REPORTED = "2000"
WAAPI_OK_NOT_SHOWN_IN_GUI = "5000"
WAAPI_OK_NO_SCAN_REPORTED = "5001"
WAAPI_OK_PRODUCT_LICENSE_EXPIRED = "5002"
WAAPI_OK_USER_INTERACTION = "5003"
WAAPI_OK_GUI_DISPLAYED = "5004"
WAAPI_OK_ALTERNATE_STREAMS_NOT_COPIED = "5005"
WAAPI_OK_USER_DOES_NOT_SET = "5006"

# General error codes
WAAPI_ERROR_GENERAL = "-1"
WAAPI_ERROR_LICENSE_MISSING = "-2"
WAAPI_ERROR_LICENSE_MISMATCH = "-3"
WAAPI_ERROR_LICENSE_EXPIRED = "-4"
WAAPI_ERROR_NOT_INITIALIZED = "-5"
WAAPI_ERROR_ALREADY_INITIALIZED = "-6"
WAAPI_ERROR_COMPONENT_NOT_LICENSED = "-7"
WAAPI_ERROR_COMPONENT_LICENSE_EXPIRED = "-8"
WAAPI_ERROR_COMPONENT_NOT_DEPLOYED = "-9"
WAAPI_ERROR_COMPONENT_TAMPERED = "-10"
WAAPI_ERROR_COMPONENT_METHOD_NOT_SUPPORTED = "-11"
WAAPI_ERROR_COMPONENT_METHOD_NOT_IMPLEMENTED = "-12"
WAAPI_ERROR_DATABASE_MISSING = "-13"
WAAPI_ERROR_DATABASE_CORRUPTED = "-14"
WAAPI_ERROR_DATABASE_ENTRY_MISSING = "-15"
WAAPI_ERROR_DATABASE_NOT_INITIALIZED = "-16"
WAAPI_ERROR_NO_CONNECTION = "-17"
WAAPI_ERROR_IP_NOT_FOUND = "-18"
WAAPI_ERROR_INVALID_CREDENTIALS = "-19"
WAAPI_ERROR_INVALID_INPUT_ARGS = "-20"
WAAPI_ERROR_INVALID_JSON = "-21"
WAAPI_ERROR_ACCESS_DENIED = "-22"
WAAPI_ERROR_INVALID_STATE = "-23"
WAAPI_ERROR_NOT_RUNNING = "-24"
WAAPI_ERROR_COM = "-25"
WAAPI_ERROR_DISPATCH_EXCEPTION = "-26"
WAAPI_ERROR_NATIVE_API = "-27"
WAAPI_ERROR_NOT_FOUND = "-28"
WAAPI_ERROR_REGISTRY_NOT_FOUND = "-29"
WAAPI_ERROR_REGISTRY_ACCESS_DENIED = "-30"
WAAPI_ERROR_REGISTRY_READ_TYPE_NOT_SUPPORTED = "-31"
WAAPI_ERROR_REGISTRY_GENERAL = "-32"
WAAPI_ERROR_CRYPTO = "-33"
WAAPI_ERROR_NO_CACHE_ENTRY = "-34"
WAAPI_ERROR_CACHE_OUT_OF_DATE = "-35"
WAAPI_ERROR_INVALID_CONFIG_VALUE = "-36"
WAAPI_ERROR_INVALID_CONFIG_KEY = "-37"
WAAPI_ERROR_INVALID_SYNTAX = "-38"
WAAPI_ERROR_NOT_A_FUNCTION = "-39"
WAAPI_ERROR_UNINSTALL_TIMEOUT = "-40"
WAAPI_ERROR_SCRIPTING_ENGINE = "-41"
WAAPI_ERROR_SCRIPTING_GENERAL = "-42"
WAAPI_ERROR_WMI = "-43"
WAAPI_ERROR_SCRIPT_COMPILE_FAILURE = "-44"
WAAPI_ERROR_MALFORMED_EXPRESSION = "-45"
WAAPI_ERROR_LOCAL_CACHE = "-46"
WAAPI_ERROR_INVALID_SIGNATURE = "-47"
WAAPI_ERROR_HASH_MISMATCH = "-48"
WAAPI_ERROR_THREAD_TIMEOUT = "-49"
WAAPI_ERROR_NO_ACTIVE_USER_SESSION = "-50"
WAAPI_ERROR_INVALID_MOCKUP = "-51"
WAAPI_ERROR_TOO_MANY_QUERIES = "-52"
WAAPI_ERROR_BUNDLE_NOT_FOUND = "-53"
WAAPI_ERROR_BUNDLE_ACCESS_DENIED = "-54"
WAAPI_ERROR_BUNDLE_GENERAL = "-55"
WAAPI_ERROR_OPERATION_IN_PROGRESS = "-56"
WAAPI_ERROR_IN_USE = "-57"
WAAPI_ERROR_INVALID_OUTPUT_ARGS = "-58"
WAAPI_ERROR_LICENSE_INVALID = "-59"
WAAPI_ERROR_3RD_PARTY_HOST_CONNECTION_NOT_ESTABLISHED = "-60"
WAAPI_ERROR_INFORMATION = "-61"
WAAPI_ERROR_TAMPER_PROTECTION_ON = "-62"
WAAPI_ERROR_INVALID_INSTANCE_ID = "-63"
WAAPI_ERROR_PASSWORD_REQUIRED = "-64"
WAAPI_ERROR_CAPTCHA_REQUIRED = "-65"
WAAPI_ERROR_TEARDOWN_BUSY = "-66"
WAAPI_ERROR_INVALID_CONFIG_COMPONENT_LOCATION = "-67"
WAAPI_ERROR_REAL_TIME_MONITORING_METHOD_NOT_IMPLEMENTED = "-68"
WAAPI_ERROR_MEMORY_CORUPTED = "-69"

# HTTP error codes
HTTP_MULTIPLE_CHOICES = "-300"
HTTP_MOVED_PERMANENTLY = "-301"
HTTP_FOUND = "-302"
HTTP_SEE_OTHER = "-303"
HTTP_NOT_MODIFIED = "-304"
HTTP_USE_PROXY = "-305"
HTTP_TEMPORARY_REDIRECT = "-307"
HTTP_BAD_REQUEST = "-400"
HTTP_UNAUTHORIZED = "-401"
HTTP_FORBIDDEN = "-403"
HTTP_NOT_FOUND = "-404"
HTTP_METHOD_NOT_ALLOWED = "-405"
HTTP_NOT_ACCEPTABLE = "-406"
HTTP_PROXY_AUTHENTICATION_REQUIRED = "-407"
HTTP_REQUEST_TIMEOUT = "-408"
HTTP_CONFLICT = "-409"
HTTP_GONE = "-410"
HTTP_LENGTH_REQUIRED = "-411"
HTTP_PRECONDITION_FAILED = "-412"
HTTP_REQUEST_ENTITY_TOO_LARGE = "-413"
HTTP_REQUEST_URI_TOO_LONG = "-414"
HTTP_UNSUPPORTED_MEDIA_TYPE = "-415"
HTTP_REQUESTED_RANGE_NOT_SATISFIABLE = "-416"
HTTP_EXPECTATION_FAILED = "-417"
HTTP_INTERNAL_SERVER_ERROR = "-500"
HTTP_NOT_IMPLEMENTED = "-501"
HTTP_BAD_GATEWAY = "-502"
HTTP_SERVICE_UNAVAILABLE = "-503"
HTTP_GATEWAY_TIMEOUT = "-504"
HTTP_VERSION_NOT_SUPPORTED = "-505"
HTTP_INVALID_VERSION_STAMP = "-506"

# VMOD error codes
VMOD_ERROR_OESIS_COM_NOT_AVAILABLE = "-1000"
VMOD_ERROR_OOD_NOT_AVAILABLE = "-1001"
VMOD_ERROR_INITIALIZE_COM = "-1002"
VMOD_ERROR_ACTIVATE_OOD = "-1003"
VMOD_ERROR_OOD_NOT_INITIALIZED = "-1004"
VMOD_ERROR_PRODUCT_NOT_FOUND = "-1005"
VMOD_ERROR_INTERFACE_NOT_SUPPORTED = "-1006"
VMOD_ERROR_CANNOT_UPDATE_PRODUCT = "-1007"
VMOD_ERROR_CANNOT_FIND_CACHE_FILE = "-1008"
VMOD_ERROR_QUERY_ISNT_CACHED = "-1009"
VMOD_ERROR_CACHING_IS_DISABLED = "-1010"
VMOD_ERROR_CANNOT_GET_DEF_STATE = "-1011"
VMOD_ERROR_CANNOT_DETECT_PRODUCTS = "-1012"
VMOD_ERROR_GEARS_CANNOT_INITIALIZE = "-1013"
VMOD_ERROR_LEGACY_SERVER = "-1014"
VMOD_ERROR_NOROUTETOSERVER = "-1015"
WA_VMOD_ERROR_INTERNAL_FORMAT_MISMATCH = "-1016"
WA_VMOD_ERROR_UPDATEVERIFY_NOT_INITIALIZED = "-1017"
WA_VMOD_ERROR_4V_NOT_INITIALIZED = "-1018"
WA_VMOD_ERROR_OFFLINEVMOD_NOT_INITIALIZED = "-1019"
WA_VMOD_ERROR_CANNOT_GET_VERSION = "-1020"
WA_VMOD_ERROR_GET_SYSTEM_PATCH_TIMEOUT = "-1021"
WA_VMOD_ERROR_GEARS_CANNOT_GET_OS_INFO = "-1022"
WA_VMOD_ERROR_INVALID_VULN_SOURCE = "-1023"
WA_VMOD_ERROR_PRODUCT_NOT_SUPPORTED = "-1026"
WA_VMOD_ERROR_LANGUAGE_NOT_SUPPORTED = "-1027"
WA_VMOD_ERROR_ARCHITECTURE_NOT_SUPPORTED = "-1028"
WA_VMOD_ERROR_NOT_RECOGNIZED_FILE = "-1029"
WA_VMOD_ERROR_CANNOT_TERMINATE_PRODUCT = "-1030"
WA_VMOD_ERROR_CANNOT_GET_PROCESSES = "-1031"
WA_VMOD_ERROR_NEED_UNINSTALL_PRODUCT_FIRST = "-1033"
WA_VMOD_ERROR_INSTALLATION_FAILED = "-1034"
WA_VMOD_ERROR_EXTRACT_FAILED = "-1036"
WA_VMOD_ERROR_OUT_OF_MEMORY = "-1037"
WA_VMOD_ERROR_DATA_TYPE_NOT_SUPPORTED = "-1038"
WA_VMOD_ERROR_OUT_OF_RANGE = "-1039"
WA_VMOD_ERROR_INVALID_ONLINE_DB_URI = "-1040"
WA_VMOD_ERROR_ANOTHER_MSI_INSTALLATION_INPROGRESS = "-1041"
WA_VMOD_ERROR_VERSION_MISMATCH = "-1042"
WA_VMOD_ERROR_CANNOT_VERIFY = "-1043"
WA_VMOD_ERROR_CIRCULAR_PACKAGE = "-1044"
WA_VMOD_ERROR_VALIDATE_FAILED = "-1045"
WA_VMOD_ERROR_OS_ARCHITECTURE_NOT_SUPPORTED = "-1046"
WA_VMOD_DELAY_TIMEFRAME_NOT_REACHED = "-1047"
WAAPI_ERROR_HOST_REQUEST_TIMEOUT = "-1048"
WAAPI_ERROR_NO_FREE_SPACE = "-1049"
WAAPI_ERROR_FAILED_TO_UNCOMPRESS = "-1050"

# These error codes are not in the OPSWAT SDK, but are used internally by CAP.
# When adding new ones, please start from - 10 million (-10,000,000)
UNKNOWN_ERROR = "-9999"
INTERNAL_TIMEOUT_ERROR_CODE = "-10000001"

# Error code to human-readable message mapping
ERROR_CODE_MESSAGES = {
    # Success codes
    WAAPI_OK: _("Success."),
    WAAPI_OK_TRUE: _("Success and value of TRUE."),
    WAAPI_OK_FALSE: _("Success and value of FALSE."),
    WAAPI_PARTIAL_SUCCESS: _("Partial success (for multiple operations, some succeeded and some failed)."),
    WAAPI_OPERATION_INPROGRESS: _("Successful call, but requested resource wasn't found in local cache and must be pulled from network."),
    WAAPI_CACHE_DATA_STALE: _("Successful call, but the data being used in the call is considered old by the API."),
    WAAPI_OK_MORE_RESULTS: _("Successful call where not all results have been retrieved."),
    WAAPI_OK_IN_USE: _("Successful call where not all desired effects have been achieved."),
    WAAPI_OK_OUTDATED_DATABASE: _("Successful call where the database version is older than core version."),
    WAAPI_FAILED_TO_GET_PASSWORD_PROTECTION: _("Successful call where a failure occurred when trying to get the password protection state."),
    WAAPI_OK_RESTART_TO_ENABLE: _("Successful call where the device is required to reboot for the functionality to be operational."),
    WAAPI_OK_NO_KEYBOARD_DETECTED: _("Successful call, but no connected keyboards were detected for the Anti Keylogger feature."),
    WAAPI_OK_CANCELED: _("Successful call, but the request is not completed because user request to cancel."),
    WAAPI_OK_RESULT_BEFORE_LOST_CONTROL: _("Successful call, but the request is not completed because SDK lost control of the helper process."),
    WAAPI_OK_VALIDATION_FAILED: _("Success but validation failed."),
    WAAPI_OK_CANNOT_VALIDATE: _("Success but cannot perform validation."),
    WAAPI_OK_VALIDATION_NOT_SUPPORTED: _("Success, but validation is not supported for the product."),
    WAAPI_OK_PENDING_REMOVE_APPLE_DEVICES: _("Success, but requires removing all Apple devices to complete the process."),

    # HTTP success codes
    HTTP_OK: _("HTTP 200: OK success code."),
    HTTP_CREATED: _("HTTP 201: Created success code."),
    HTTP_NON_AUTHORITATIVE: _("HTTP 203: Non authoritative success code."),
    HTTP_NO_CONTENT: _("HTTP 204: No content success code."),
    HTTP_RESET_CONTENT: _("HTTP 205: Reset content success code."),
    HTTP_PARTIAL_CONTENT: _("HTTP 206: Partial content success code."),

    # VMOD data status codes
    VMOD_DATA_LIVE: _("Data is live."),
    VMOD_DATA_CACHED: _("Data is cached."),
    VMOD_DATA_EXPIRED: _("Data is expired."),
    WA_VMOD_INSTALLATION_NEED_RESTART: _("Product installation requires restart."),
    WA_VMOD_PARTIAL_INSTALLATION_NEED_RESTART: _("Product installation requires restart after successfully installing partially."),
    WAAPI_OK_NO_BACKUP_REPORTED: _("Successful call, but a product has never, or does not report that it has ever, run a Backup."),
    WAAPI_OK_NOT_SHOWN_IN_GUI: _("Successful call, but a product's user interface may not reflect any changes."),
    WAAPI_OK_NO_SCAN_REPORTED: _("Successful call, but a product has never, or does not report that it has ever, run a scan."),
    WAAPI_OK_PRODUCT_LICENSE_EXPIRED: _("Successful call, but the product cannot perform the operation because of an expired license."),
    WAAPI_OK_USER_INTERACTION: _("Successful call, but the product may not be able to perform the operation without some interaction from the user in the product UI."),
    WAAPI_OK_GUI_DISPLAYED: _("Successful call, but the product may open a user interface in order to perform the operation."),
    WAAPI_OK_ALTERNATE_STREAMS_NOT_COPIED: _("Successful call, file contents was copied but NTFS name streams couldn't be copied."),
    WAAPI_OK_USER_DOES_NOT_SET: _("Successful call for circumstance the user does not set yet."),

    # General error codes
    WAAPI_ERROR_GENERAL: _("General failure."),
    WAAPI_ERROR_LICENSE_MISSING: _("License file is missing."),
    WAAPI_ERROR_LICENSE_MISMATCH: _("License provided does not match the license file."),
    WAAPI_ERROR_LICENSE_EXPIRED: _("License key has expired."),
    WAAPI_ERROR_NOT_INITIALIZED: _("API has not been initialized."),
    WAAPI_ERROR_ALREADY_INITIALIZED: _("API is already initialized."),
    WAAPI_ERROR_COMPONENT_NOT_LICENSED: _("Component is not licensed."),
    WAAPI_ERROR_COMPONENT_LICENSE_EXPIRED: _("Component license has expired."),
    WAAPI_ERROR_COMPONENT_NOT_DEPLOYED: _("Component is licensed but not deployed."),
    WAAPI_ERROR_COMPONENT_TAMPERED: _("Component module has been tampered with."),
    WAAPI_ERROR_COMPONENT_METHOD_NOT_SUPPORTED: _("Method is not supported by the component."),
    WAAPI_ERROR_COMPONENT_METHOD_NOT_IMPLEMENTED: _("Method is not implemented by the component."),
    WAAPI_ERROR_DATABASE_MISSING: _("Database is missing."),
    WAAPI_ERROR_DATABASE_CORRUPTED: _("Database exists but is corrupted."),
    WAAPI_ERROR_DATABASE_ENTRY_MISSING: _("Database entry is missing."),
    WAAPI_ERROR_DATABASE_NOT_INITIALIZED: _("Database has not been initialized."),
    WAAPI_ERROR_NO_CONNECTION: _("No connection when one is expected."),
    WAAPI_ERROR_IP_NOT_FOUND: _("Computer name or IP not found in the network."),
    WAAPI_ERROR_INVALID_CREDENTIALS: _("Invalid user, password, or domain information."),
    WAAPI_ERROR_INVALID_INPUT_ARGS: _("Invalid input arguments."),
    WAAPI_ERROR_INVALID_JSON: _("Invalid JSON format."),
    WAAPI_ERROR_ACCESS_DENIED: _("Full Disk Access required for patching."),
    WAAPI_ERROR_INVALID_STATE: _("System or product is in an invalid state."),
    WAAPI_ERROR_NOT_RUNNING: _("Product is not running."),
    WAAPI_ERROR_COM: _("Error with Component Object Model (COM)."),
    WAAPI_ERROR_DISPATCH_EXCEPTION: _("Exception thrown during request dispatch."),
    WAAPI_ERROR_NATIVE_API: _("Error in native system API."),
    WAAPI_ERROR_NOT_FOUND: _("Object not found."),
    WAAPI_ERROR_REGISTRY_NOT_FOUND: _("Object not found in Windows Registry."),
    WAAPI_ERROR_REGISTRY_ACCESS_DENIED: _("Registry operation denied access."),
    WAAPI_ERROR_REGISTRY_READ_TYPE_NOT_SUPPORTED: _("Unsupported registry data type."),
    WAAPI_ERROR_REGISTRY_GENERAL: _("General registry error."),
    WAAPI_ERROR_CRYPTO: _("Error with encryption/decryption security module."),
    WAAPI_ERROR_NO_CACHE_ENTRY: _("Cache entry is missing."),
    WAAPI_ERROR_CACHE_OUT_OF_DATE: _("Cache entry exists but is out of date."),
    WAAPI_ERROR_INVALID_CONFIG_VALUE: _("Invalid configuration value type."),
    WAAPI_ERROR_INVALID_CONFIG_KEY: _("Invalid configuration key."),
    WAAPI_ERROR_INVALID_SYNTAX: _("Invalid expression format."),
    WAAPI_ERROR_NOT_A_FUNCTION: _("Invalid dynamic function call."),
    WAAPI_ERROR_UNINSTALL_TIMEOUT: _("Uninstallation timeout."),
    WAAPI_ERROR_SCRIPTING_ENGINE: _("Internal scripting engine error."),
    WAAPI_ERROR_SCRIPTING_GENERAL: _("General script error."),
    WAAPI_ERROR_WMI: _("General WMI error."),
    WAAPI_ERROR_SCRIPT_COMPILE_FAILURE: _("Script compilation failure."),
    WAAPI_ERROR_MALFORMED_EXPRESSION: _("Invalid or corrupt expression."),
    WAAPI_ERROR_LOCAL_CACHE: _("Local caching mechanism error."),
    WAAPI_ERROR_INVALID_SIGNATURE: _("Digital signature failed checksum verification."),
    WAAPI_ERROR_HASH_MISMATCH: _("Hash value does not match expected value."),
    WAAPI_ERROR_THREAD_TIMEOUT: _("Internal thread failed to respond in time."),
    WAAPI_ERROR_NO_ACTIVE_USER_SESSION: _("No active user session found."),
    WAAPI_ERROR_INVALID_MOCKUP: _("Mockup JSON is not properly formatted."),
    WAAPI_ERROR_TOO_MANY_QUERIES: _("Too many sub-queries in a bulk query."),
    WAAPI_ERROR_BUNDLE_NOT_FOUND: _("Bundle resource cannot be found."),
    WAAPI_ERROR_BUNDLE_ACCESS_DENIED: _("Insufficient permissions to access bundle resource."),
    WAAPI_ERROR_BUNDLE_GENERAL: _("Operation on bundle resource failed."),
    WAAPI_ERROR_OPERATION_IN_PROGRESS: _("Operation is already running."),
    WAAPI_ERROR_IN_USE: _("Resource is being used."),
    WAAPI_ERROR_INVALID_OUTPUT_ARGS: _("Invalid output arguments."),
    WAAPI_ERROR_LICENSE_INVALID: _("License is not valid for requested mode of operation."),
    WAAPI_ERROR_3RD_PARTY_HOST_CONNECTION_NOT_ESTABLISHED: _("Connection to 3rd party host cannot be established."),
    WAAPI_ERROR_INFORMATION: _("Additional information for the main error."),
    WAAPI_ERROR_TAMPER_PROTECTION_ON: _("Access denied due to active tamper protection."),
    WAAPI_ERROR_INVALID_INSTANCE_ID: _("Invalid instance ID value."),
    WAAPI_ERROR_PASSWORD_REQUIRED: _("Operation failed due to password protection."),
    WAAPI_ERROR_CAPTCHA_REQUIRED: _("Operation failed due to captcha protection."),
    WAAPI_ERROR_TEARDOWN_BUSY: _("Teardown request already in progress."),
    WAAPI_ERROR_INVALID_CONFIG_COMPONENT_LOCATION: _("Invalid component location configuration."),
    WAAPI_ERROR_REAL_TIME_MONITORING_METHOD_NOT_IMPLEMENTED: _("Real-time monitoring not implemented for this method."),
    WAAPI_ERROR_MEMORY_CORUPTED: _("Memory has been corrupted."),

    # HTTP error codes
    HTTP_MULTIPLE_CHOICES: _("HTTP 300: Multiple choices notification."),
    HTTP_MOVED_PERMANENTLY: _("HTTP 301: Moved permanently notification."),
    HTTP_FOUND: _("HTTP 302: Found notification."),
    HTTP_SEE_OTHER: _("HTTP 303: See other notification."),
    HTTP_NOT_MODIFIED: _("HTTP 304: Not modified notification."),
    HTTP_USE_PROXY: _("HTTP 305: Use proxy notification."),
    HTTP_TEMPORARY_REDIRECT: _("HTTP 307: Temporary redirect notification."),
    HTTP_BAD_REQUEST: _("HTTP 400: Bad request error."),
    HTTP_UNAUTHORIZED: _("HTTP 401: Unauthorized error."),
    HTTP_FORBIDDEN: _("HTTP 403: Forbidden error."),
    HTTP_NOT_FOUND: _("HTTP 404: Not found error."),
    HTTP_METHOD_NOT_ALLOWED: _("HTTP 405: Method not allowed error."),
    HTTP_NOT_ACCEPTABLE: _("HTTP 406: Not acceptable error."),
    HTTP_PROXY_AUTHENTICATION_REQUIRED: _("HTTP 407: Proxy authentication required error."),
    HTTP_REQUEST_TIMEOUT: _("HTTP 408: Request timeout error."),
    HTTP_CONFLICT: _("HTTP 409: Conflict error."),
    HTTP_GONE: _("HTTP 410: Gone error."),
    HTTP_LENGTH_REQUIRED: _("HTTP 411: Length required error."),
    HTTP_PRECONDITION_FAILED: _("HTTP 412: Precondition failed error."),
    HTTP_REQUEST_ENTITY_TOO_LARGE: _("HTTP 413: Request entity too large error."),
    HTTP_REQUEST_URI_TOO_LONG: _("HTTP 414: Request URI too long error."),
    HTTP_UNSUPPORTED_MEDIA_TYPE: _("HTTP 415: Unsupported media type error."),
    HTTP_REQUESTED_RANGE_NOT_SATISFIABLE: _("HTTP 416: Range not satisfiable error."),
    HTTP_EXPECTATION_FAILED: _("HTTP 417: Expectation failed error."),
    HTTP_INTERNAL_SERVER_ERROR: _("HTTP 500: Internal server error."),
    HTTP_NOT_IMPLEMENTED: _("HTTP 501: Not implemented error."),
    HTTP_BAD_GATEWAY: _("HTTP 502: Bad gateway error."),
    HTTP_SERVICE_UNAVAILABLE: _("HTTP 503: Service unavailable error."),
    HTTP_GATEWAY_TIMEOUT: _("HTTP 504: Gateway timeout error."),
    HTTP_VERSION_NOT_SUPPORTED: _("HTTP 505: Version not supported error."),
    HTTP_INVALID_VERSION_STAMP: _("HTTP 506: Invalid version stamp error."),

    # VMOD error codes
    VMOD_ERROR_OESIS_COM_NOT_AVAILABLE: _("MetaDefender Endpoint Security SDK COM object is not available."),
    VMOD_ERROR_OOD_NOT_AVAILABLE: _("MetaDefender Endpoint Security SDK On Demand object is not available."),
    VMOD_ERROR_INITIALIZE_COM: _("Problem initializing COM lib."),
    VMOD_ERROR_ACTIVATE_OOD: _("Problem activating MetaDefender Endpoint Security SDK On Demand."),
    VMOD_ERROR_OOD_NOT_INITIALIZED: _("OOD object is not initialized."),
    VMOD_ERROR_PRODUCT_NOT_FOUND: _("Queried product cannot be found."),
    VMOD_ERROR_INTERFACE_NOT_SUPPORTED: _("Queried product interface is not supported."),
    VMOD_ERROR_CANNOT_UPDATE_PRODUCT: _("Queried product cannot update."),
    VMOD_ERROR_CANNOT_FIND_CACHE_FILE: _("Queried product cannot locate cache."),
    VMOD_ERROR_QUERY_ISNT_CACHED: _("Queried call is not cached."),
    VMOD_ERROR_CACHING_IS_DISABLED: _("Caching is not enabled."),
    VMOD_ERROR_CANNOT_GET_DEF_STATE: _("Definition state cannot be retrieved."),
    VMOD_ERROR_CANNOT_DETECT_PRODUCTS: _("GEARS cannot detect products."),
    VMOD_ERROR_GEARS_CANNOT_INITIALIZE: _("GEARS cannot be initialized."),
    VMOD_ERROR_LEGACY_SERVER: _("Operation requires a newer version of server."),
    VMOD_ERROR_NOROUTETOSERVER: _("Server could not be reached."),
    WA_VMOD_ERROR_INTERNAL_FORMAT_MISMATCH: _("Mismatch in output format between internal components."),
    WA_VMOD_ERROR_UPDATEVERIFY_NOT_INITIALIZED: _("UpdateVerify is not initialized."),
    WA_VMOD_ERROR_4V_NOT_INITIALIZED: _("MetaDefender Endpoint Security SDK 4V is not initialized."),
    WA_VMOD_ERROR_OFFLINEVMOD_NOT_INITIALIZED: _("VMod Source is not initialized."),
    WA_VMOD_ERROR_CANNOT_GET_VERSION: _("Cannot get product's version."),
    WA_VMOD_ERROR_GET_SYSTEM_PATCH_TIMEOUT: _("System updates retrieval timed out."),
    WA_VMOD_ERROR_GEARS_CANNOT_GET_OS_INFO: _("Cannot retrieve OS info."),
    WA_VMOD_ERROR_INVALID_VULN_SOURCE: _("Invalid vulnerability source in input."),
    WA_VMOD_ERROR_PRODUCT_NOT_SUPPORTED: _("Product is not supported."),
    WA_VMOD_ERROR_LANGUAGE_NOT_SUPPORTED: _("Language is not supported."),
    WA_VMOD_ERROR_ARCHITECTURE_NOT_SUPPORTED: _("Architecture is not supported."),
    WA_VMOD_ERROR_NOT_RECOGNIZED_FILE: _("File type is not supported."),
    WA_VMOD_ERROR_CANNOT_TERMINATE_PRODUCT: _("App was open during patch. Retry after closure."),
    WA_VMOD_ERROR_CANNOT_GET_PROCESSES: _("Cannot retrieve list of blocking processes."),
    WA_VMOD_ERROR_NEED_UNINSTALL_PRODUCT_FIRST: _("Existing product needs to be uninstalled before installation."),
    WA_VMOD_ERROR_INSTALLATION_FAILED: _("An error occurred during the patch process. Please retry."),
    WA_VMOD_ERROR_EXTRACT_FAILED: _("File/installer extraction failed."),
    WA_VMOD_ERROR_OUT_OF_MEMORY: _("Not enough memory."),
    WA_VMOD_ERROR_DATA_TYPE_NOT_SUPPORTED: _("Type of data/content is not supported."),
    WA_VMOD_ERROR_OUT_OF_RANGE: _("Queried index is out of array range."),
    WA_VMOD_ERROR_INVALID_ONLINE_DB_URI: _("Online database prefix is not set or invalid."),
    WA_VMOD_ERROR_ANOTHER_MSI_INSTALLATION_INPROGRESS: _("Another MSI installation is in progress. Please retry."),
    WA_VMOD_ERROR_VERSION_MISMATCH: _("Product version mismatches with expected version."),
    WA_VMOD_ERROR_CANNOT_VERIFY: _("SDK cannot perform the requested verification."),
    WA_VMOD_ERROR_CIRCULAR_PACKAGE: _("Detected circular package."),
    WA_VMOD_ERROR_VALIDATE_FAILED: _("Validation failed."),
    WA_VMOD_ERROR_OS_ARCHITECTURE_NOT_SUPPORTED: _("OS architecture is not supported."),
    WA_VMOD_DELAY_TIMEFRAME_NOT_REACHED: _("Delay timeframe is not passed."),
    WAAPI_ERROR_HOST_REQUEST_TIMEOUT: _("WaHost request timeout elapsed."),
    WAAPI_ERROR_NO_FREE_SPACE: _("Device is out of memory."),
    WAAPI_ERROR_FAILED_TO_UNCOMPRESS: _("Uncompression failed."),

    INTERNAL_TIMEOUT_ERROR_CODE: _("Internal patching installation failed to respond in time."),
    UNKNOWN_ERROR: _("Please retry in a couple of hours. If error persists, please contact support."),
}


def get_error_message(error_code: str) -> str:
    """
    Get the human-readable error message for a given error code.

    Args:
        error_code: The error code as a string.

    Returns:
        The human-readable error message.
    """
    return ERROR_CODE_MESSAGES.get(error_code, _("An error occurred during the operation."))
