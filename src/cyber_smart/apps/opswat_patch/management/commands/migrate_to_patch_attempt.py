from django.core.management.base import BaseCommand
from django.conf import settings
from django.db import transaction
from django.db.models import Prefetch

from opswat_patch.models import (
    OpswatScheduledProductInstaller,
    TERMINAL_STATUSES,
    OpswatPatchAttempt,
    OpswatPatchJob,
    OpswatPatchEventLog
)

class Command(BaseCommand):
    """
    Command class for handling the migration of patch history.

    As we added the OpswatPatchJob and OpswatPatchAttempt models, we need to
    process existing OpswatScheduledProductInstaller and their event logs to
    create structured patch jobs and attempts.
    This command will link event logs to their respective patch attempts, ensuring
    a clear record of actions taken for patch management.
    """
    def handle(self, *args, **options):
        installers = OpswatScheduledProductInstaller.objects.prefetch_related(
            Prefetch(
                "event_logs",
                queryset=OpswatPatchEventLog.objects.filter(patch_attempt__isnull=True).order_by("created"),
                to_attr="unlinked_event_logs"
            ),
            "app_install__app_user__organisation",
            "opswat_product_patch_installer__patch_installer",
            "opswat_product_patch_installer__product",
            "creator",
        )

        for installer in installers:
            event_logs = installer.unlinked_event_logs
            if not event_logs:
                continue

            # Split logs into attempts based on TERMINAL_STATUSES
            attempts = []
            current_attempt = []
            for log in event_logs:
                current_attempt.append(log)
                if log.status in TERMINAL_STATUSES:
                    attempts.append(current_attempt)
                    current_attempt = []
            if current_attempt:
                attempts.append(current_attempt)

            patch_jobs = []
            patch_attempts = []
            logs_to_update = []

            for logs in attempts:
                first_log = logs[0]
                last_log = logs[-1]
                finished_at = last_log.created if last_log.status in TERMINAL_STATUSES else None

                patch_jobs.append(OpswatPatchJob(
                    organisation=installer.app_install.app_user.organisation,
                    initiated_by=installer.creator,
                    status=last_log.aggregated_status,
                    finished_at=finished_at,
                    created=first_log.created,
                ))

                patch_attempts.append({
                    "logs": logs,
                    "first_log": first_log,
                    "last_log": last_log,
                    "finished_at": finished_at
                })

            with transaction.atomic():
                created_jobs = OpswatPatchJob.objects.bulk_create(patch_jobs)

                for job, attempt_data in zip(created_jobs, patch_attempts):
                    patch_attempt = OpswatPatchAttempt.objects.create(
                        patch_job=job,
                        scheduled_product_installer=installer,
                        status=attempt_data["last_log"].status,
                        initiated_by=installer.creator,
                        from_version="",
                        to_version=installer.opswat_product_patch_installer.patch_installer.latest_version,
                        finished_at=attempt_data["finished_at"],
                        created=attempt_data["first_log"].created,
                    )
                    for log in attempt_data["logs"]:
                        log.patch_attempt = patch_attempt
                        logs_to_update.append(log)

                # Bulk update logs to link patch_attempt
                if logs_to_update:
                    OpswatPatchEventLog.objects.bulk_update(logs_to_update, ["patch_attempt"])

        if not settings.IS_TEST:
            print("Patch history migration completed.")
