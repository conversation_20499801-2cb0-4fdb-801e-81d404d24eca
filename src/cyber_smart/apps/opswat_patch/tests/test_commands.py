from datetime import timed<PERSON><PERSON>

from django.core.management import call_command
from django.test import TestCase
from django.utils import timezone

from appusers.models.factories import AppInstallFactory
from opswat.factories import ProductFactory
from opswat_patch.factories import OpswatProductPatchInstallerFactory, OpswatScheduledProductInstallerFactory, \
    OpswatPatchEventLogFactory
from opswat_patch.models import OpswatPatchJob, OpswatPatchAttempt
from opswat_patch.models.constants import COMPLETE, ERROR, IN_PROGRESS, PENDING, SCHEDULED, WAITING_FOR_RESTART, \
    INSTALLED, TIMED_OUT
from organisations.factories import OrganisationFactory


class MigrateToPatchAttemptCommandTestCase(TestCase):
    """
    Test case for the migrate_to_patch_attempt management command.
    This command processes existing OpswatScheduledProductInstaller and their event logs
    to create structured patch jobs and attempts.
    """
    def setUp(self):
        self.organisation = OrganisationFactory()
        self.app_install = AppInstallFactory(app_user__organisation=self.organisation)
        self.product = ProductFactory(name="Patch Attempt Test Product")
        self.patch_installer = OpswatProductPatchInstallerFactory(
            product=self.product,
            patch_installer__latest_version="2.0.0"
        )
        self.base_time = timezone.now()

    def _create_installer(self, status):
        return OpswatScheduledProductInstallerFactory(
            app_install=self.app_install,
            opswat_product_patch_installer=self.patch_installer,
            status=status,
        )

    def _create_event_logs(self, installer, status_minutes_list):
        for status, minutes_ago in status_minutes_list:
            OpswatPatchEventLogFactory(
                opswat_scheduled_product_installer=installer,
                patch_attempt=None,
                status=status,
                created=self.base_time - timedelta(minutes=minutes_ago)
            )

    def _assert_attempt(self, installer, expected_status, expected_log_count, expected_job_status, finished=True):
        attempt = OpswatPatchAttempt.objects.get(scheduled_product_installer=installer)
        self.assertEqual(attempt.status, expected_status)
        self.assertEqual(attempt.event_logs.count(), expected_log_count)
        self.assertEqual(attempt.patch_job.status, expected_job_status)
        if finished:
            self.assertIsNotNone(attempt.finished_at)
        else:
            self.assertIsNone(attempt.finished_at)

    def test_completed_successfully(self):
        installer = self._create_installer(COMPLETE)
        self._create_event_logs(installer, [
            (PENDING, 40), (SCHEDULED, 30), (IN_PROGRESS, 20), (COMPLETE, 10)
        ])
        call_command("migrate_to_patch_attempt")
        self._assert_attempt(installer, COMPLETE, 4, COMPLETE)

    def test_completed_with_error(self):
        installer = self._create_installer(ERROR)
        self._create_event_logs(installer, [
            (PENDING, 30), (IN_PROGRESS, 20), (ERROR, 10)
        ])
        call_command("migrate_to_patch_attempt")
        self._assert_attempt(installer, ERROR, 3, ERROR)

    def test_in_progress_patching(self):
        installer = self._create_installer(WAITING_FOR_RESTART)
        self._create_event_logs(installer, [
            (PENDING, 50), (SCHEDULED, 40), (IN_PROGRESS, 30), (INSTALLED, 20), (WAITING_FOR_RESTART, 10)
        ])
        call_command("migrate_to_patch_attempt")
        self._assert_attempt(installer, WAITING_FOR_RESTART, 5, IN_PROGRESS, finished=False)

    def test_timed_out_patching(self):
        installer = self._create_installer(TIMED_OUT)
        self._create_event_logs(installer, [
            (PENDING, 50), (SCHEDULED, 40), (IN_PROGRESS, 30), (INSTALLED, 20), (TIMED_OUT, 10)
        ])
        call_command("migrate_to_patch_attempt")
        self._assert_attempt(installer, TIMED_OUT, 5, ERROR)

    def test_missing_pending_event(self):
        installer = self._create_installer(COMPLETE)
        self._create_event_logs(installer, [
            (SCHEDULED, 30), (IN_PROGRESS, 20), (COMPLETE, 10)
        ])
        call_command("migrate_to_patch_attempt")
        self._assert_attempt(installer, COMPLETE, 3, COMPLETE)

    def test_multiple_patch_attempts(self):
        installer = self._create_installer(COMPLETE)

        self._create_event_logs(installer, [
            # First attempt - ERROR
            (PENDING, 90), (IN_PROGRESS, 80), (ERROR, 70),
            # Second attempt - TIMED_OUT
            (PENDING, 60), (SCHEDULED, 55), (IN_PROGRESS, 50), (INSTALLED, 45), (TIMED_OUT, 40),
            # Third attempt - COMPLETE
            (PENDING, 30), (SCHEDULED, 25), (IN_PROGRESS, 20), (COMPLETE, 10)
        ])

        call_command("migrate_to_patch_attempt")

        attempts = OpswatPatchAttempt.objects.filter(
            scheduled_product_installer=installer
        ).order_by("created")

        self.assertEqual(attempts.count(), 3)
        self.assertEqual(OpswatPatchJob.objects.count(), 3)

        self.assertEqual([(a.status, a.event_logs.count(), a.patch_job.status) for a in attempts], [
            (ERROR, 3, ERROR),
            (TIMED_OUT, 5, ERROR),
            (COMPLETE, 4, COMPLETE),
        ])

    def test_idempotent_behavior(self):
        """
        Test that re-running the migration doesn't create duplicate attempts.
        """
        installer = self._create_installer(COMPLETE)
        self._create_event_logs(installer, [
            (PENDING, 30), (SCHEDULED, 25), (IN_PROGRESS, 20), (COMPLETE, 10)
        ])
        call_command("migrate_to_patch_attempt")
        call_command("migrate_to_patch_attempt")

        attempts = OpswatPatchAttempt.objects.filter(scheduled_product_installer=installer)
        self.assertEqual(attempts.count(), 1)
