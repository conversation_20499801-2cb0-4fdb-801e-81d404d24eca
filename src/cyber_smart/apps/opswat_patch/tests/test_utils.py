import datetime
from unittest.mock import Mock

from software_inventory.models import InstalledSoftwareOrganisationIndividual
from appusers.models.factories import AppInstallFactory, AppUserFactory
from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from appusers.tests.test_constants import (
    AI2_EDGE,
    AI2_SAFARI,
)
from appusers.tests.helpers import SoftwarePackageHelperTestMixin
from django.test import TestCase
from django.utils import timezone
from freezegun import freeze_time
from opswat.factories import (
    InstalledProductFactory,
    InstalledProductVersionFactory,
    OpswatOperatingSystemFactory,
    ProductFactory,
    ProductSignatureFactory,
    ProductVendorFactory,
    ProductVersionFactory,
)
from opswat.models import InstalledProductVersion
from opswat.models import Product
from opswat.opswat_operating_system import OpswatOperatingSystem
from opswat_patch.factories import (
    OpswatPatchInstallerFactory,
    OpswatProductPatchInstallerFactory,
    OpswatScheduledProductInstallerFactory,
)
from opswat.opswat_operating_system import (
    is_installer_os_compatible,
    is_os_id_in_ranges,
    parse_os_range,
)
from opswat_patch.models import OpswatProductPatchInstaller, OpswatScheduledProductInstaller
from opswat_patch.models.constants import COMPLETE, PENDING
from opswat_patch.utils import (
    enrich_installed_software_with_installers,
)
from opswat_patch.utils.enrich_using_db_data import (
    get_enriched_installed_software_with_installers_from_db,
)
from organisations.factories import OrganisationFactory


class ParseOsRangeTests(TestCase):
    """Test cases for OS range parsing functionality."""

    def test_parse_single_range(self):
        """Test parsing a single OS range."""
        result = parse_os_range("[1,10]")
        self.assertEqual(result, [(1, 10)])

    def test_parse_multiple_ranges(self):
        """Test parsing multiple OS ranges."""
        result = parse_os_range("[1,10],[20,30]")
        self.assertEqual(result, [(1, 10), (20, 30)])

    def test_parse_negative_range(self):
        """Test parsing range with negative values."""
        result = parse_os_range("[-10,58]")
        self.assertEqual(result, [(-10, 58)])

    def test_parse_empty_string(self):
        """Test parsing empty string returns empty list."""
        self.assertEqual(parse_os_range(""), [])
        self.assertEqual(parse_os_range("   "), [])

    def test_parse_invalid_format(self):
        """Test parsing invalid format returns empty list."""
        result = parse_os_range("invalid")
        self.assertEqual(result, [])

    def test_parse_mixed_ranges(self):
        """Test parsing mixed positive and negative ranges."""
        result = parse_os_range("[-10,-5],[0,5],[10,20]")
        self.assertEqual(result, [(-10, -5), (0, 5), (10, 20)])

    def test_parse_no_limit_ranges(self):
        """Test parsing ranges with -2 (no limit) values."""
        # -2 at start means no lower limit
        result = parse_os_range("[-2,58]")
        self.assertEqual(result, [(None, 58)])

        # -2 at end means no upper limit
        result = parse_os_range("[59,-2]")
        self.assertEqual(result, [(59, None)])

        # Multiple ranges with -2
        result = parse_os_range("[-2,58], [67, 70]")
        self.assertEqual(result, [(None, 58), (67, 70)])

        # Complex case with single values
        result = parse_os_range("[-2, 42], [66, 66], [75,75]")
        self.assertEqual(result, [(None, 42), (66, 66), (75, 75)])

    def test_parse_decimal_ranges_are_skipped(self):
        """Test that ranges with decimal numbers are skipped."""
        # Single decimal range should be skipped
        result = parse_os_range("[-2, 10.14]")
        self.assertEqual(result, [])

        # Mixed decimal and integer ranges - only integer ranges kept
        result = parse_os_range("[10.0.19045,61],[63,65],[71,74]")
        self.assertEqual(result, [(63, 65), (71, 74)])

        # Complex case from real data
        result = parse_os_range("[10.0.19045,61],[63,65],[71,74],[10.0.22631,78],[80,-2]")
        self.assertEqual(result, [(63, 65), (71, 74), (80, None)])

        # Decimal in first value
        result = parse_os_range("[10.5, 20]")
        self.assertEqual(result, [])

        # Decimal in second value
        result = parse_os_range("[10, 20.5]")
        self.assertEqual(result, [])


class IsOsIdInRangesTests(TestCase):
    """Test cases for OS ID range checking."""

    def test_os_id_in_single_range(self):
        """Test OS ID within a single range."""
        ranges = [(1, 10)]
        self.assertTrue(is_os_id_in_ranges(5, ranges))
        self.assertTrue(is_os_id_in_ranges(1, ranges))
        self.assertTrue(is_os_id_in_ranges(10, ranges))
        self.assertFalse(is_os_id_in_ranges(0, ranges))
        self.assertFalse(is_os_id_in_ranges(11, ranges))

    def test_os_id_in_multiple_ranges(self):
        """Test OS ID within multiple ranges."""
        ranges = [(1, 10), (20, 30)]
        self.assertTrue(is_os_id_in_ranges(5, ranges))
        self.assertTrue(is_os_id_in_ranges(25, ranges))
        self.assertFalse(is_os_id_in_ranges(15, ranges))

    def test_os_id_with_negative_range(self):
        """Test OS ID with negative range."""
        ranges = [(-2, 58)]
        self.assertTrue(is_os_id_in_ranges(-1, ranges))
        self.assertTrue(is_os_id_in_ranges(0, ranges))
        self.assertTrue(is_os_id_in_ranges(58, ranges))
        self.assertFalse(is_os_id_in_ranges(-3, ranges))
        self.assertFalse(is_os_id_in_ranges(59, ranges))

    def test_empty_os_id(self):
        """Test empty or None OS ID."""
        ranges = [(1, 10)]
        self.assertFalse(is_os_id_in_ranges(None, ranges))

    def test_empty_ranges(self):
        """Test empty ranges list."""
        self.assertFalse(is_os_id_in_ranges(5, []))

    def test_os_id_with_no_limit_ranges(self):
        """Test OS ID with ranges containing None (no limit) values."""
        # No lower limit
        ranges = [(None, 58)]
        self.assertTrue(is_os_id_in_ranges(0, ranges))
        self.assertTrue(is_os_id_in_ranges(-100, ranges))
        self.assertTrue(is_os_id_in_ranges(58, ranges))
        self.assertFalse(is_os_id_in_ranges(59, ranges))

        # No upper limit
        ranges = [(59, None)]
        self.assertTrue(is_os_id_in_ranges(59, ranges))
        self.assertTrue(is_os_id_in_ranges(1000, ranges))
        self.assertFalse(is_os_id_in_ranges(58, ranges))

        # Mixed ranges
        ranges = [(None, 42), (66, 66), (75, None)]
        self.assertTrue(is_os_id_in_ranges(0, ranges))  # In first range
        self.assertTrue(is_os_id_in_ranges(42, ranges))  # In first range
        self.assertFalse(is_os_id_in_ranges(43, ranges))  # Not in any range
        self.assertTrue(is_os_id_in_ranges(66, ranges))  # In second range
        self.assertFalse(is_os_id_in_ranges(67, ranges))  # Not in any range
        self.assertTrue(is_os_id_in_ranges(75, ranges))  # In third range
        self.assertTrue(is_os_id_in_ranges(1000, ranges))  # In third range



class IsInstallerOsCompatibleTests(TestCase):
    """Test cases for is_installer_os_compatible function."""

    def test_os_deny_blocks_installer(self):
        """Test that OS ID in deny range blocks installer."""
        installer = Mock()
        installer.os_deny = "[-2,58]"
        installer.os_allow = ""

        self.assertFalse(is_installer_os_compatible(installer, 10))  # In deny range
        self.assertFalse(is_installer_os_compatible(installer, -1))  # In deny range
        self.assertTrue(is_installer_os_compatible(installer, 100))  # Outside deny range

    def test_os_allow_restricts_installer(self):
        """Test that OS ID must be in allow range when set."""
        installer = Mock()
        installer.os_allow = "[1,20]"
        installer.os_deny = ""

        self.assertTrue(is_installer_os_compatible(installer, 10))   # In allow range
        self.assertFalse(is_installer_os_compatible(installer, 30))  # Outside allow range
        self.assertFalse(is_installer_os_compatible(installer, 0))   # Outside allow range

    def test_os_deny_takes_precedence(self):
        """Test that os_deny takes precedence over os_allow."""
        installer = Mock()
        installer.os_allow = "[1,100]"
        installer.os_deny = "[10,20]"

        self.assertTrue(is_installer_os_compatible(installer, 5))    # In allow, not in deny
        self.assertFalse(is_installer_os_compatible(installer, 15))  # In both allow and deny
        self.assertTrue(is_installer_os_compatible(installer, 50))   # In allow, not in deny
        self.assertFalse(is_installer_os_compatible(installer, 150)) # Outside allow

    def test_no_restrictions_allows_all(self):
        """Test that installer without OS restrictions allows all OS."""
        installer = Mock()
        installer.os_allow = ""
        installer.os_deny = ""

        self.assertTrue(is_installer_os_compatible(installer, 10))
        self.assertTrue(is_installer_os_compatible(installer, -5))
        self.assertTrue(is_installer_os_compatible(installer, 1000))

    def test_installer_with_no_limit_ranges(self):
        """Test installer compatibility with -2 (no limit) ranges."""
        installer = Mock()

        # Test os_deny with no lower limit
        installer.os_deny = "[-2,58], [67, 70]"
        installer.os_allow = ""
        self.assertFalse(is_installer_os_compatible(installer, 0))    # In deny range
        self.assertFalse(is_installer_os_compatible(installer, 58))   # In deny range
        self.assertTrue(is_installer_os_compatible(installer, 59))    # Not in deny range
        self.assertFalse(is_installer_os_compatible(installer, 67))   # In deny range
        self.assertTrue(is_installer_os_compatible(installer, 71))    # Not in deny range

        # Test os_allow with no upper limit
        installer.os_allow = "[59,-2]"
        installer.os_deny = ""
        self.assertFalse(is_installer_os_compatible(installer, 58))   # Outside allow range
        self.assertTrue(is_installer_os_compatible(installer, 59))    # In allow range
        self.assertTrue(is_installer_os_compatible(installer, 1000))  # In allow range

        # Test complex case
        installer.os_deny = "[-2, 42], [66, 66], [75,75]"
        installer.os_allow = ""
        self.assertFalse(is_installer_os_compatible(installer, -100)) # In deny range
        self.assertFalse(is_installer_os_compatible(installer, 42))   # In deny range
        self.assertTrue(is_installer_os_compatible(installer, 43))    # Not in deny range
        self.assertFalse(is_installer_os_compatible(installer, 66))   # In deny range
        self.assertTrue(is_installer_os_compatible(installer, 67))    # Not in deny range
        self.assertFalse(is_installer_os_compatible(installer, 75))   # In deny range
        self.assertTrue(is_installer_os_compatible(installer, 76))    # Not in deny range

    def test_installer_with_decimal_ranges(self):
        """Test that installer handles decimal ranges by skipping them."""
        installer = Mock()

        # Test os_deny with decimal ranges - they should be skipped
        installer.os_deny = "[-2, 10.14]"
        installer.os_allow = ""
        # Since the decimal range is skipped, all OS IDs should be allowed
        self.assertTrue(is_installer_os_compatible(installer, 10))
        self.assertTrue(is_installer_os_compatible(installer, 11))

        # Test mixed decimal and integer ranges
        installer.os_allow = "[10.0.19045,61],[63,65],[71,74],[10.0.22631,78],[80,-2]"
        installer.os_deny = ""
        # Only the valid integer ranges should be used: [63,65], [71,74], [80,-2]
        self.assertFalse(is_installer_os_compatible(installer, 62))   # Not in any allow range
        self.assertTrue(is_installer_os_compatible(installer, 63))    # In allow range
        self.assertTrue(is_installer_os_compatible(installer, 65))    # In allow range
        self.assertFalse(is_installer_os_compatible(installer, 66))   # Not in any allow range
        self.assertTrue(is_installer_os_compatible(installer, 71))    # In allow range
        self.assertTrue(is_installer_os_compatible(installer, 74))    # In allow range
        self.assertFalse(is_installer_os_compatible(installer, 75))   # Not in any allow range
        self.assertTrue(is_installer_os_compatible(installer, 80))    # In allow range
        self.assertTrue(is_installer_os_compatible(installer, 1000))  # In allow range (no upper limit)


class EnrichPatchDetectedDateTests(TestCase):
    """
    This is the logic of the patch_detected:

    Patch version has to be higher than the currently installed version of the product on the CAP device.
    If this is not the case, the patch_detected_date is None.

    And then we have 2 cases:
    1. If the patch was available in CyberSmart dashbaord  before CAP was installed, then the patch_detected_date is the date_created of the ProductVersion on that CAP device.
    2. If the CAP product_version was installed before, and the patch was released after that, then the patch_detected_date is the date_modified of the PatchInstaller.
    """

    @freeze_time("2020-01-01")
    def setUp(self):
        self.app_install = AppInstallFactory()
        self.product_version = ProductVersionFactory(
            raw_version="1.0.0",
            major=1,
            minor=0,
            patch=0
        )
        self.installed_product = InstalledProductFactory(app_install=self.app_install)
        self.signature = ProductSignatureFactory(product=self.product_version.product)

    @freeze_time("2020-02-01")
    def test_case1_patch_available_before_product_installed(self):
        """Test case 1: Patch available before install -> date should be install date."""
        with freeze_time("2020-01-01"):
            # Create patch installer with modification date before product installation
            patch_installer = OpswatPatchInstallerFactory(latest_version="1.1.0")
            installer = OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=patch_installer
            )
            installer.signatures.add(self.signature)

        with freeze_time("2020-02-01"):
            installed_version = InstalledProductVersionFactory(
                installed_product=self.installed_product,
                product_version=self.product_version,
                signature=self.signature
            )

        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software_list = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install)

        enriched_results = enrich_installed_software_with_installers(installed_software_list)
        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        self.assertEqual(enriched_item.app_install_product_installer['patch_detected_date'], installed_version.created)

    @freeze_time("2020-02-01")
    def test_case2_product_installed_before_patch_available(self):
        """Test case 2: Install before patch available -> date should be patch modified date."""
        InstalledProductVersionFactory(
            installed_product=self.installed_product,
            product_version=self.product_version,
            signature=self.signature
        )

        # Create patch installer with modification date after product installation (July 1st)
        with freeze_time("2020-07-01"):
            patch_installer = OpswatPatchInstallerFactory(latest_version="1.1.0")
            # Retrieve the installer instance to get its modified date
            opswat_installer = OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=patch_installer
            )
            opswat_installer.signatures.add(self.signature)

        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software_list = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install)
        self.assertEqual(installed_software_list.count(), 1)

        # Get the detection date via enrichment
        enriched_results = enrich_installed_software_with_installers(installed_software_list)
        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        # Should be the installer modification date
        self.assertEqual(enriched_item.app_install_product_installer['patch_detected_date'], opswat_installer.modified)

    @freeze_time("2020-06-15")
    def test_returns_none_if_patch_version_not_greater(self):
        """Test patch_detected_date is None if patch version is not greater."""
        # Create the installed product version
        InstalledProductVersionFactory(
            installed_product=self.installed_product,
            product_version=self.product_version,
            signature=self.signature
        )

        patch_installer = OpswatPatchInstallerFactory(latest_version="1.0.0")  # Same version
        installer = OpswatProductPatchInstallerFactory(product=self.product_version.product, patch_installer=patch_installer)
        installer.signatures.add(self.signature)

        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software_list = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install)
        enriched_results = enrich_installed_software_with_installers(installed_software_list)
        enriched_item = enriched_results[0]
        self.assertFalse(hasattr(enriched_item, "app_install_product_installer"))

    @freeze_time("2020-06-15")
    def test_returns_none_if_parse_version_fails(self):
        """Test patch_detected_date is None if version parsing fails."""
        # Create the installed product version
        InstalledProductVersionFactory(
            installed_product=self.installed_product,
            product_version=self.product_version,
            signature=self.signature
        )

        patch_installer = OpswatPatchInstallerFactory(latest_version="invalid")
        installer = OpswatProductPatchInstallerFactory(product=self.product_version.product, patch_installer=patch_installer)
        installer.signatures.add(self.signature)

        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software_list = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install)
        enriched_results = enrich_installed_software_with_installers(installed_software_list)
        enriched_item = enriched_results[0]
        self.assertFalse(hasattr(enriched_item, "app_install_product_installer"))


class EnrichInstalledSoftwareWithInstallersTests(SoftwarePackageHelperTestMixin, TestCase):
    """
    Tests for enrich_installed_software_with_installers utility function.
    """

    def setUp(self):
        self.setUpTwoOrganisationsFourAppInstalls()

        # Create signature for Edge product
        edge_product = Product.objects.get(name=AI2_EDGE)
        self.edge_signature = ProductSignatureFactory(product=edge_product)

        # Add signature to existing Edge installed product versions
        edge_installed_products = InstalledProductVersion.objects.filter(
            installed_product__app_install=self.app_install_2,
            product_version__product=edge_product
        )
        for ipv in edge_installed_products:
            ipv.signature = self.edge_signature
            ipv.save()

        # Refresh the view after adding signatures
        InstalledSoftwareAppInstallIndividual.refresh()
        self.installed_software = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install_2)
        self.assertEqual(self.installed_software.count(), 3)

        # Ensure the patch installer has a higher version than installed Edge (100.0.0.0)
        patch_installer = OpswatPatchInstallerFactory(latest_version="*********")
        self.product_patch_installer = OpswatProductPatchInstallerFactory(
            product=edge_product,
            title="Edge Installer",
            patch_installer=patch_installer
        )
        self.product_patch_installer.signatures.add(self.edge_signature)
        OpswatScheduledProductInstallerFactory(
            opswat_product_patch_installer=self.product_patch_installer,
            app_install=self.app_install_2
        )

    def test_enrich_installed_software_with_installers(self):
        """Test that installers are correctly added to installed software."""

        result = enrich_installed_software_with_installers(self.installed_software)

        self.assertEqual(len(result), self.installed_software.count())

        edge_sw = next((sw for sw in result if AI2_EDGE.lower() in sw.product.lower()), None)
        safari_sw = next((sw for sw in result if AI2_SAFARI.lower() in sw.product.lower()), None)

        # Verify both items are found
        self.assertIsNotNone(edge_sw, "Edge software not found in result")
        self.assertIsNotNone(safari_sw, "Safari software not found in result")

        # Verify Edge is enriched with installer and scheduled
        self.assertTrue(hasattr(edge_sw, 'app_install_product_installer'))
        self.assertEqual(edge_sw.app_install_product_installer['app_install_product_installer_id'], self.product_patch_installer.id)
        self.assertEqual(edge_sw.app_install_product_installer['title'], "Edge Installer")
        self.assertEqual(edge_sw.app_install_product_installer['is_scheduled'], True)
        self.assertEqual(edge_sw.app_install_product_installer['installer_version'], self.product_patch_installer.patch_installer.latest_version)
        self.assertEqual(edge_sw.app_install_product_installer['patch_detected_date'], self.product_patch_installer.modified)

    def test_no_matching_installers(self):
        """Test behavior when no matching installers are found."""
        OpswatProductPatchInstaller.objects.all().delete()

        result = enrich_installed_software_with_installers(self.installed_software)

        # All items should be returned but none should be enriched
        self.assertEqual(len(result), self.installed_software.count())

        # Verify no items have the app_install_product_installer attribute
        for item in result:
            self.assertFalse(hasattr(item, 'app_install_product_installer'))

    def test_no_enrichment_when_no_signature_match(self):
        """Test that when no installer matches the signature, NO enrichment happens (stricter behavior)."""
        teamviewer_vendor = ProductVendorFactory(
            name="TeamViewer GmbH",
            opswat_id="152"
        )
        teamviewer_product = ProductFactory(
            name="TeamViewer",
            vendor=teamviewer_vendor,
            opswat_id="173"
        )

        # Create TeamViewer version
        teamviewer_version = ProductVersionFactory(
            product=teamviewer_product,
            raw_version="11.0.0",
            major=11,
            minor=0,
            patch=0
        )

        # Create TeamViewer signatures
        signature1 = ProductSignatureFactory(
            product=teamviewer_product,
            name="TeamViewer 11",
            opswat_id="3342",
            support_3rd_party_patch=True,
            fresh_installable=1
        )

        signature2 = ProductSignatureFactory(
            product=teamviewer_product,
            name="TeamViewer 12",
            opswat_id="3343",
            support_3rd_party_patch=True,
            fresh_installable=1
        )

        signature3 = ProductSignatureFactory(
            product=teamviewer_product,
            name="TeamViewer 13",
            opswat_id="3344",
            support_3rd_party_patch=True,
            fresh_installable=1
        )

        # Create app install and installed product with signature3
        app_install = AppInstallFactory()
        installed_product = InstalledProductFactory(app_install=app_install)
        InstalledProductVersionFactory(
            installed_product=installed_product,
            product_version=teamviewer_version,
            signature=signature3
        )

        # Create patch installers for different TeamViewer versions
        patch_installer1 = OpswatPatchInstallerFactory(
            product_name="TeamViewer 11",
            latest_version="11.0.59"
        )
        patch_installer2 = OpswatPatchInstallerFactory(
            product_name="TeamViewer 12",
            latest_version="12.0.0"
        )

        # Create product patch installers with signatures 1 and 2 (but not 3)
        product_patch_installer1 = OpswatProductPatchInstallerFactory(
            product=teamviewer_product,
            title="TeamViewer 11 Installer",
            patch_installer=patch_installer1
        )
        product_patch_installer1.signatures.add(signature1)

        product_patch_installer2 = OpswatProductPatchInstallerFactory(
            product=teamviewer_product,
            title="TeamViewer 12 Installer",
            patch_installer=patch_installer2
        )
        product_patch_installer2.signatures.add(signature2)

        # Refresh the materialized view
        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=app_install)

        # Get the enriched software
        result = enrich_installed_software_with_installers(installed_software)

        # Find the TeamViewer software in the result
        teamviewer_sw = next((sw for sw in result if "teamviewer" in sw.product.lower()), None)
        self.assertIsNotNone(teamviewer_sw, "TeamViewer software not found in result")

        # With stricter signature matching, NO enrichment should happen
        # because the app has signature3 but installers only have signature1 and signature2
        self.assertFalse(hasattr(teamviewer_sw, 'app_install_product_installer'),
                         "TeamViewer should NOT be enriched when no installer matches its signature")

    def test_teamviewer_signature_based_installer_selection(self):
        """Test that TeamViewer installers are selected based on signature when available."""
        # Create TeamViewer vendor and product
        teamviewer_vendor = ProductVendorFactory(
            name="TeamViewer GmbH",
            opswat_id="152"
        )
        teamviewer_product = ProductFactory(
            name="TeamViewer",
            vendor=teamviewer_vendor,
            opswat_id="173"
        )

        # Create TeamViewer version
        teamviewer_version = ProductVersionFactory(
            product=teamviewer_product,
            raw_version="11.0.0",
            major=11,
            minor=0,
            patch=0
        )

        # Create TeamViewer signatures based on the provided data
        teamviewer_signature = ProductSignatureFactory(
            product=teamviewer_product,
            name="TeamViewer",
            opswat_id="177",
            support_3rd_party_patch=False
        )

        teamviewer_11_signature = ProductSignatureFactory(
            product=teamviewer_product,
            name="TeamViewer 11",
            opswat_id="3342",
            support_3rd_party_patch=True,
            fresh_installable=1
        )

        ProductSignatureFactory(
            product=teamviewer_product,
            name="TeamViewer 12",
            opswat_id="3343",
            support_3rd_party_patch=True,
            fresh_installable=1
        )

        # Create app install and installed product
        app_install = AppInstallFactory()
        installed_product = InstalledProductFactory(app_install=app_install)
        InstalledProductVersionFactory(
            installed_product=installed_product,
            product_version=teamviewer_version,
            signature=teamviewer_11_signature  # Associate with TeamViewer 11 signature
        )

        # Create patch installers for different TeamViewer versions
        patch_installer_v11 = OpswatPatchInstallerFactory(
            product_name="TeamViewer 11",
            latest_version="11.0.59"
        )
        patch_installer_v15 = OpswatPatchInstallerFactory(
            product_name="TeamViewer",
            latest_version="15.0.0"
        )

        # Create product patch installers with different signatures
        product_patch_installer_v11 = OpswatProductPatchInstallerFactory(
            product=teamviewer_product,
            title="TeamViewer 11 Installer",
            patch_installer=patch_installer_v11
        )
        product_patch_installer_v11.signatures.add(teamviewer_11_signature)

        product_patch_installer_v15 = OpswatProductPatchInstallerFactory(
            product=teamviewer_product,
            title="TeamViewer 15 Installer",
            patch_installer=patch_installer_v15
        )
        product_patch_installer_v15.signatures.add(teamviewer_signature)

        # Refresh the materialized view
        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=app_install)

        # Get the enriched software
        result = enrich_installed_software_with_installers(installed_software)

        # Find the TeamViewer software in the result
        teamviewer_sw = next((sw for sw in result if "teamviewer" in sw.product.lower()), None)
        self.assertIsNotNone(teamviewer_sw, "TeamViewer software not found in result")

        # Verify TeamViewer is enriched with the correct installer based on signature (v11)
        self.assertTrue(hasattr(teamviewer_sw, 'app_install_product_installer'))
        self.assertEqual(teamviewer_sw.app_install_product_installer['app_install_product_installer_id'], product_patch_installer_v11.id)
        self.assertEqual(teamviewer_sw.app_install_product_installer['title'], "TeamViewer 11 Installer")
        self.assertEqual(teamviewer_sw.app_install_product_installer['installer_version'], "11.0.59")


class EnrichInstalledSoftwareOrganisationLevelTests(TestCase):
    """
    Tests for enrich_installed_software_with_installers at organisation level.
    """

    @freeze_time("2020-01-01")
    def setUp(self):
        """Set up test data for organisation-level testing."""
        self.organisation = OrganisationFactory()
        self.user1 = AppUserFactory(organisation=self.organisation)
        self.user2 = AppUserFactory(organisation=self.organisation)
        self.app_install1 = AppInstallFactory(app_user=self.user1)
        self.app_install2 = AppInstallFactory(app_user=self.user2)

        # Create a product version
        self.product_version = ProductVersionFactory(
            raw_version="1.0.0",
            major=1,
            minor=0,
            patch=0
        )
        self.signature = ProductSignatureFactory(product=self.product_version.product)

        # Create installed products for both app installs
        self.installed_product1 = InstalledProductFactory(app_install=self.app_install1)
        self.installed_product2 = InstalledProductFactory(app_install=self.app_install2)


    @freeze_time("2020-02-01")
    def test_organisation_level_patch_detected_when_patch_released_after_install(self):
        """Test organisation-level patch_detected when patch is released after installation."""
        # Install the product on both devices
        with freeze_time("2020-01-15"):
            InstalledProductVersionFactory(
                installed_product=self.installed_product1,
                product_version=self.product_version,
                signature=self.signature
            )

        with freeze_time("2020-01-20"):
            InstalledProductVersionFactory(
                installed_product=self.installed_product2,
                product_version=self.product_version,
                signature=self.signature
            )

        # Create patch installer after both installs
        with freeze_time("2020-01-25"):
            patch_installer = OpswatPatchInstallerFactory(latest_version="1.1.0")
            opswat_installer = OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=patch_installer
            )
            opswat_installer.signatures.add(self.signature)

        # Refresh materialized view
        InstalledSoftwareOrganisationIndividual.refresh()

        # Get organisation-level installed software
        installed_software = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation_id=self.organisation.id
        )

        # Enrich without app_install
        enriched_results = enrich_installed_software_with_installers(installed_software)

        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        # Should use the patch installer modified date (Jan 25)
        self.assertEqual(
            enriched_item.app_install_product_installer['patch_detected_date'],
            opswat_installer.modified
        )

    def test_organisation_level_takes_earliest_install_date_across_multiple_devices(self):
        """Test organisation-level patch_detected uses earliest install date across multiple devices."""
        # Create multiple devices with different install dates
        devices_data = [
            ("2020-01-10", self.installed_product1),
            ("2020-01-15", self.installed_product2),
        ]

        # Create a third device
        user3 = AppUserFactory(organisation=self.organisation)
        app_install3 = AppInstallFactory(app_user=user3)
        installed_product3 = InstalledProductFactory(app_install=app_install3)
        devices_data.append(("2020-01-05", installed_product3))

        earliest_date = None
        for install_date, installed_product in devices_data:
            with freeze_time(install_date):
                ipv = InstalledProductVersionFactory(
                    installed_product=installed_product,
                    product_version=self.product_version,
                    signature=self.signature
                )
                if not earliest_date or ipv.created < earliest_date:
                    earliest_date = ipv.created

        # Create patch installer before the earliest install
        with freeze_time("2020-01-01"):
            patch_installer = OpswatPatchInstallerFactory(latest_version="1.1.0")
            installer = OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=patch_installer
            )
            installer.signatures.add(self.signature)

        # Refresh materialized view
        InstalledSoftwareOrganisationIndividual.refresh()

        # Get organisation-level installed software
        installed_software = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation_id=self.organisation.id
        )

        # Enrich without app_install to trigger organisation-level logic
        enriched_results = enrich_installed_software_with_installers(installed_software)

        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        # Should use the earliest install date (Jan 5) since patch was available before
        self.assertEqual(
            enriched_item.app_install_product_installer['patch_detected_date'],
            earliest_date
        )

    def test_organisation_level_enrich_individual_installs(self):
        """Test enriching individual app_install_ids"""
        # Create different OS configurations for each app_install
        OpswatOperatingSystemFactory(app_install=self.app_install1, os_id=10)  # Windows
        OpswatOperatingSystemFactory(app_install=self.app_install2, os_id=50)  # Different OS

        # Install products on both devices
        with freeze_time("2020-01-15"):
            InstalledProductVersionFactory(
                installed_product=self.installed_product1,
                product_version=self.product_version,
                signature=self.signature
            )

        with freeze_time("2020-01-20"):
            InstalledProductVersionFactory(
                installed_product=self.installed_product2,
                product_version=self.product_version,
                signature=self.signature
            )

        # Create patch installer with OS restrictions
        patch_installer = OpswatPatchInstallerFactory(latest_version="1.1.0")
        installer = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            os_allow="[1,20]",  # Only allows OS IDs 1-20
            title="Windows Only Installer"
        )
        installer.signatures.add(self.signature)

        # Schedule installer for app_install1 only
        OpswatScheduledProductInstallerFactory(
            opswat_product_patch_installer=installer,
            app_install=self.app_install1
        )

        # Refresh materialized view
        InstalledSoftwareOrganisationIndividual.refresh()

        # Get organisation-level installed software
        installed_software = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation_id=self.organisation.id
        )

        # Enrich with individual installs enabled
        enriched_results = enrich_installed_software_with_installers(installed_software)

        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        # Check that we have the new attribute
        self.assertTrue(hasattr(enriched_item, 'app_install_product_installers_by_app_install_dynamic'))
        installers_by_app = enriched_item.app_install_product_installers_by_app_install_dynamic

        # app_install1 should have the installer (OS compatible and scheduled)
        self.assertIn(self.app_install1.id, installers_by_app)
        app1_installer = installers_by_app[self.app_install1.id]
        self.assertEqual(app1_installer['app_install_product_installer_id'], installer.id)
        self.assertTrue(app1_installer['is_scheduled'])

        # app_install2 should not have the installer (OS not compatible)
        self.assertNotIn(self.app_install2.id, installers_by_app)

    def test_organisation_level_different_installers_for_aggregate_vs_individual(self) -> None:
        """Test that org-level aggregate installer can differ from individual app_install installers."""
        # Create OS configurations: Windows and macOS
        OpswatOperatingSystemFactory(app_install=self.app_install1, os_id=10)  # Windows
        OpswatOperatingSystemFactory(app_install=self.app_install2, os_id=60)  # macOS

        # Install products on both devices
        with freeze_time("2020-01-15"):
            InstalledProductVersionFactory(
                installed_product=self.installed_product1,
                product_version=self.product_version,
                signature=self.signature
            )
            InstalledProductVersionFactory(
                installed_product=self.installed_product2,
                product_version=self.product_version,
                signature=self.signature
            )

        # Create three installers with different OS restrictions
        # 1. Windows-only installer (oldest - will be selected for aggregate)
        with freeze_time("2020-01-20"):
            windows_patch = OpswatPatchInstallerFactory(latest_version="1.1.0")
            windows_installer = OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=windows_patch,
                os_allow="[1,20]",  # Windows range
                title="Windows Only Installer"
            )
            windows_installer.signatures.add(self.signature)

        # 2. macOS-only installer (older)
        with freeze_time("2020-01-21"):
            macos_patch = OpswatPatchInstallerFactory(latest_version="1.1.0")
            macos_installer = OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=macos_patch,
                os_allow="[55,70]",  # macOS range
                title="macOS Only Installer"
            )
            macos_installer.signatures.add(self.signature)

        # 3. Universal installer (newest)
        with freeze_time("2020-01-25"):
            universal_patch = OpswatPatchInstallerFactory(latest_version="1.2.0")
            universal_installer = OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=universal_patch,
                # No OS restrictions - works on all platforms
                title="Universal Installer"
            )
            universal_installer.signatures.add(self.signature)

        # Refresh materialized view
        InstalledSoftwareOrganisationIndividual.refresh()

        # Get organisation-level installed software
        installed_software = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation_id=self.organisation.id
        )

        # Enrich with individual installs enabled
        enriched_results = enrich_installed_software_with_installers(installed_software)

        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        # Aggregate should have the oldest Windows installer
        # because the installed_product_version was created before the other installers,
        # and matched first.
        self.assertEqual(
            enriched_item.app_install_product_installer['title'],
            "Windows Only Installer"
        )
        self.assertEqual(
            enriched_item.app_install_product_installer['installer_version'],
            "1.1.0"
        )

        # Individual app_installs should have OS-specific installers
        installers_by_app = enriched_item.app_install_product_installers_by_app_install_dynamic

        # Windows app_install should have ALL compatible installers, but we pick the newest
        # We pick windows as the newest because it was created first
        self.assertIn(self.app_install1.id, installers_by_app)
        self.assertEqual(
            installers_by_app[self.app_install1.id]['app_install_product_installer_id'],
            windows_installer.id
        )

        # macOS app_install should also get macOS installer because it was created first
        self.assertIn(self.app_install2.id, installers_by_app)
        self.assertEqual(
            installers_by_app[self.app_install2.id]['app_install_product_installer_id'],
            macos_installer.id
        )

class EnrichInstalledSoftwareDisabledInstallerTests(SoftwarePackageHelperTestMixin, TestCase):
    """
    Tests for ensuring disabled installers are filtered out from enrichment.
    """

    def setUp(self):
        self.setUpTwoOrganisationsFourAppInstalls()

        # Create signature for Edge product
        edge_product = Product.objects.get(name=AI2_EDGE)
        self.edge_signature = ProductSignatureFactory(product=edge_product)

        # Add signature to existing Edge installed product versions
        edge_installed_products = InstalledProductVersion.objects.filter(
            installed_product__app_install=self.app_install_2,
            product_version__product=edge_product
        )
        for ipv in edge_installed_products:
            ipv.signature = self.edge_signature
            ipv.save()

        # Refresh the view after adding signatures
        InstalledSoftwareAppInstallIndividual.refresh()
        self.installed_software = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install_2)
        self.assertEqual(self.installed_software.count(), 3)

        # Create active patch installer for Edge
        active_patch_installer = OpswatPatchInstallerFactory(
            latest_version="*********",
            date_disabled=None
        )
        self.active_product_patch_installer = OpswatProductPatchInstallerFactory(
            product=edge_product,
            title="Active Edge Installer",
            patch_installer=active_patch_installer
        )
        self.active_product_patch_installer.signatures.add(self.edge_signature)

        # Create disabled patch installer for Edge
        disabled_patch_installer = OpswatPatchInstallerFactory(
            latest_version="*********",
            date_disabled=timezone.now()
        )
        self.disabled_product_patch_installer = OpswatProductPatchInstallerFactory(
            product=edge_product,
            title="Disabled Edge Installer",
            patch_installer=disabled_patch_installer
        )
        self.disabled_product_patch_installer.signatures.add(self.edge_signature)

    def test_disabled_installers_not_included_in_enrichment(self):
        """Test that disabled patch installers are not included in enrichment results."""
        # Create scheduled installer only for the active installer
        OpswatScheduledProductInstallerFactory(
            opswat_product_patch_installer=self.active_product_patch_installer,
            app_install=self.app_install_2
        )

        result = enrich_installed_software_with_installers(self.installed_software)

        self.assertEqual(len(result), self.installed_software.count())

        edge_sw = next((sw for sw in result if AI2_EDGE.lower() in sw.product.lower()), None)
        self.assertIsNotNone(edge_sw, "Edge software not found in result")

        # Verify Edge is enriched with the active installer, not the disabled one
        self.assertTrue(hasattr(edge_sw, 'app_install_product_installer'))
        self.assertEqual(
            edge_sw.app_install_product_installer['app_install_product_installer_id'],
            self.active_product_patch_installer.id
        )
        self.assertEqual(edge_sw.app_install_product_installer['title'], "Active Edge Installer")
        self.assertEqual(edge_sw.app_install_product_installer['installer_version'], "*********")

        # Verify the disabled installer is not used
        self.assertNotEqual(
            edge_sw.app_install_product_installer['app_install_product_installer_id'],
            self.disabled_product_patch_installer.id
        )

    def test_active_installer_preferred_over_disabled_fallback(self):
        """Test that when both active and disabled installers exist, active is preferred."""

        # Create a third installer that would be the most recent by creation date, but is disabled
        newer_disabled_patch_installer = OpswatPatchInstallerFactory(
            latest_version="103.0.0.0",
            date_disabled=timezone.now()
        )

        # Force creation time to be newer
        newer_disabled_product_patch_installer = OpswatProductPatchInstallerFactory(
            product=Product.objects.get(name=AI2_EDGE),
            title="Newer Disabled Edge Installer",
            patch_installer=newer_disabled_patch_installer
        )
        newer_disabled_product_patch_installer.signatures.add(self.edge_signature)
        newer_disabled_product_patch_installer.created = timezone.now() + datetime.timedelta(minutes=1)
        newer_disabled_product_patch_installer.save()

        result = enrich_installed_software_with_installers(self.installed_software)

        edge_sw = next((sw for sw in result if AI2_EDGE.lower() in sw.product.lower()), None)
        self.assertIsNotNone(edge_sw, "Edge software not found in result")

        # Verify Edge is enriched with the active installer, not the newer disabled one
        self.assertTrue(hasattr(edge_sw, 'app_install_product_installer'))
        self.assertEqual(
            edge_sw.app_install_product_installer['app_install_product_installer_id'],
            self.active_product_patch_installer.id
        )
        self.assertEqual(edge_sw.app_install_product_installer['title'], "Active Edge Installer")
        self.assertEqual(edge_sw.app_install_product_installer['installer_version'], "*********")

        self.assertNotEqual(
            edge_sw.app_install_product_installer['app_install_product_installer_id'],
            newer_disabled_product_patch_installer.id
        )


class OsFilteringEnrichmentTests(TestCase):
    """Test cases for OS filtering in the enrich_installed_software_with_installers function."""

    def setUp(self):
        """Set up test data for OS filtering tests."""

        self.organisation = OrganisationFactory()
        self.user = AppUserFactory(organisation=self.organisation)

        self.app_install_windows_10 = AppInstallFactory(app_user=self.user)
        self.app_install_windows_11 = AppInstallFactory(app_user=self.user)
        self.app_install_macos = AppInstallFactory(app_user=self.user)
        self.app_install_linux = AppInstallFactory(app_user=self.user)

        # Create OpswatOperatingSystem instances with different os_id values
        self.os_windows_10 = OpswatOperatingSystemFactory(
            app_install=self.app_install_windows_10,
            os_id=10,  # Within [-2,58] range
            os_type=OpswatOperatingSystem.OS_TYPE_WINDOWS
        )
        self.os_windows_11 = OpswatOperatingSystemFactory(
            app_install=self.app_install_windows_11,
            os_id=11,  # Within [-2,58] range
            os_type=OpswatOperatingSystem.OS_TYPE_WINDOWS
        )
        self.os_macos = OpswatOperatingSystemFactory(
            app_install=self.app_install_macos,
            os_id=100,  # Outside [-2,58] range
            os_type=OpswatOperatingSystem.OS_TYPE_MAC
        )
        self.os_linux = OpswatOperatingSystemFactory(
            app_install=self.app_install_linux,
            os_id=200,  # Outside [-2,58] range
            os_type=OpswatOperatingSystem.OS_TYPE_LINUX
        )

        self.product_version = ProductVersionFactory(
            raw_version="1.0.0", major=1, minor=0, patch=0
        )

        # signatures
        self.signature = ProductSignatureFactory(product=self.product_version.product)

        # Create installed products
        self.installed_product_win10 = InstalledProductFactory(app_install=self.app_install_windows_10)
        self.installed_product_win11 = InstalledProductFactory(app_install=self.app_install_windows_11)
        self.installed_product_macos = InstalledProductFactory(app_install=self.app_install_macos)
        self.installed_product_linux = InstalledProductFactory(app_install=self.app_install_linux)

        # Create installed product versions
        InstalledProductVersionFactory(
            installed_product=self.installed_product_win10,
            product_version=self.product_version,
            signature=self.signature
        )
        InstalledProductVersionFactory(
            installed_product=self.installed_product_win11,
            product_version=self.product_version,
            signature=self.signature
        )
        InstalledProductVersionFactory(
            installed_product=self.installed_product_macos,
            product_version=self.product_version,
            signature=self.signature
        )
        InstalledProductVersionFactory(
            installed_product=self.installed_product_linux,
            product_version=self.product_version,
            signature=self.signature
        )

    def test_installer_with_os_deny_filters_correctly(self):
        """Test that installers with os_deny field filter out incompatible OS."""
        # Create patch installer
        patch_installer = OpswatPatchInstallerFactory(latest_version="2.0.0")

        # Create installer with os_deny="[-2,58]" (denies Windows but allows macOS/Linux)
        installer_with_deny = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            os_deny="[-2,58]",  # Denies OS IDs up to 58 (no lower limit)
            title="Installer with OS Deny",
        )
        installer_with_deny.signatures.add(self.signature)

        # Refresh materialized view and get installed software for each OS
        InstalledSoftwareAppInstallIndividual.refresh()

        # Test Windows 10 (os_id=10) - should NOT get installer
        software_win10 = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_windows_10
        )
        enriched_win10 = enrich_installed_software_with_installers(software_win10)
        self.assertEqual(len(enriched_win10), 1)
        self.assertFalse(hasattr(enriched_win10[0], 'app_install_product_installer'))

        # Test macOS (os_id=100) - should get installer
        software_macos = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_macos
        )
        enriched_macos = enrich_installed_software_with_installers(software_macos)
        self.assertEqual(len(enriched_macos), 1)
        self.assertTrue(hasattr(enriched_macos[0], 'app_install_product_installer'))
        self.assertEqual(
            enriched_macos[0].app_install_product_installer['app_install_product_installer_id'],
            installer_with_deny.id
        )

    def test_installer_with_os_allow_filters_correctly(self):
        """Test that installers with os_allow field only allow specific OS."""
        # Create patch installer
        patch_installer = OpswatPatchInstallerFactory(latest_version="2.0.0")

        # Create installer with os_allow="[1,20]" (allows only Windows 10 and 11)
        installer_with_allow = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            os_allow="[1,20]",  # Allows OS IDs from 1 to 20
            title="Installer with OS Allow"
        )
        installer_with_allow.signatures.add(self.signature)

        # Refresh materialized view
        InstalledSoftwareAppInstallIndividual.refresh()

        # Test Windows 10 (os_id=10) - should get installer
        software_win10 = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_windows_10
        )
        enriched_win10 = enrich_installed_software_with_installers(software_win10)
        self.assertEqual(len(enriched_win10), 1)
        self.assertTrue(hasattr(enriched_win10[0], 'app_install_product_installer'))
        self.assertEqual(
            enriched_win10[0].app_install_product_installer['app_install_product_installer_id'],
            installer_with_allow.id
        )

        # Test macOS (os_id=100) - should NOT get installer
        software_macos = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_macos
        )
        enriched_macos = enrich_installed_software_with_installers(software_macos)
        self.assertEqual(len(enriched_macos), 1)
        self.assertFalse(hasattr(enriched_macos[0], 'app_install_product_installer'))

    def test_installer_with_both_os_allow_and_deny(self):
        """Test installer with both os_allow and os_deny fields."""
        # Create patch installer
        patch_installer = OpswatPatchInstallerFactory(latest_version="2.0.0")

        # Create installer with both allow and deny
        # os_allow="[1,100]" - allows 1 to 100
        # os_deny="[10,20]" - denies 10 to 20
        # Result: allows 1-9 and 21-100
        _installer_complex = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            os_allow="[1,100]",
            os_deny="[10,20]",
            title="Installer with Complex OS Rules"
        )
        _installer_complex.signatures.add(self.signature)

        # Refresh materialized view
        InstalledSoftwareAppInstallIndividual.refresh()

        # Test Windows 10 (os_id=10) - should NOT get installer (in deny range)
        software_win10 = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_windows_10
        )
        enriched_win10 = enrich_installed_software_with_installers(software_win10)
        self.assertEqual(len(enriched_win10), 1)
        self.assertFalse(hasattr(enriched_win10[0], 'app_install_product_installer'))

        # Test macOS (os_id=100) - should get installer (in allow range but not in deny range)
        software_macos = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_macos
        )
        enriched_macos = enrich_installed_software_with_installers(software_macos)
        self.assertEqual(len(enriched_macos), 1)
        self.assertTrue(hasattr(enriched_macos[0], 'app_install_product_installer'))

        # Test Linux (os_id=200) - should NOT get installer (outside allow range)
        software_linux = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_linux
        )
        enriched_linux = enrich_installed_software_with_installers(software_linux)
        self.assertEqual(len(enriched_linux), 1)
        self.assertFalse(hasattr(enriched_linux[0], 'app_install_product_installer'))

    def test_no_os_restrictions_allows_all(self):
        """Test that installers without OS restrictions work for all OS."""
        # Create patch installer without any OS restrictions
        patch_installer = OpswatPatchInstallerFactory(latest_version="2.0.0")
        installer_universal = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            title="Universal Installer"
        )
        installer_universal.signatures.add(self.signature)

        # Refresh materialized view
        InstalledSoftwareAppInstallIndividual.refresh()

        # Test all OS - should all get the installer
        for app_install in [self.app_install_windows_10, self.app_install_macos, self.app_install_linux]:
            software = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=app_install)
            enriched = enrich_installed_software_with_installers(software)
            self.assertTrue(hasattr(enriched[0], 'app_install_product_installer'))
            self.assertEqual(enriched[0].app_install_product_installer['title'], "Universal Installer")

    def test_app_install_without_opswat_os_gets_all_installers(self):
        """Test that app_installs without opswat_os don't get OS filtering applied."""
        # Create app install without OS info
        app_install_no_os = AppInstallFactory(app_user=self.user)
        # Explicitly ensure there's no opswat_os
        self.assertFalse(hasattr(app_install_no_os, 'opswat_os'))

        # Create installed product
        installed_product = InstalledProductFactory(app_install=app_install_no_os)
        InstalledProductVersionFactory(
            installed_product=installed_product,
            product_version=self.product_version,
            signature=self.signature
        )

        # Create installer with strict OS restrictions
        patch_installer = OpswatPatchInstallerFactory(latest_version="2.0.0")
        installer_restricted = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            os_allow="[1,10]",  # Very restrictive - only OS IDs 1-10
            title="Restricted Installer"
        )
        installer_restricted.signatures.add(self.signature)

        # Refresh materialized view
        InstalledSoftwareAppInstallIndividual.refresh()

        # Get software for app_install without OS
        software = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=app_install_no_os
        )

        # Enrich - should get installer despite restrictive os_allow
        enriched = enrich_installed_software_with_installers(software)
        self.assertEqual(len(enriched), 1)
        self.assertTrue(hasattr(enriched[0], 'app_install_product_installer'))
        self.assertEqual(enriched[0].app_install_product_installer['title'], "Restricted Installer")

    def test_app_install_with_unknown_os_gets_all_installers(self):
        """Test that app_installs with unknown OS (os_id=-1) don't get OS filtering applied."""
        # Create app install with unknown OS
        app_install_unknown_os = AppInstallFactory(app_user=self.user)
        OpswatOperatingSystemFactory(
            app_install=app_install_unknown_os,
            os_id=-1,  # Unknown OS
            os_type=OpswatOperatingSystem.OS_TYPE_WINDOWS  # Type doesn't matter for unknown OS
        )

        # Create installed product
        installed_product = InstalledProductFactory(app_install=app_install_unknown_os)
        InstalledProductVersionFactory(
            installed_product=installed_product,
            product_version=self.product_version,
            signature=self.signature
        )

        # Create installer with strict OS restrictions
        patch_installer = OpswatPatchInstallerFactory(latest_version="2.0.0")
        installer = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            os_allow="[1,10]",  # Very restrictive - only OS IDs 1-10
            title="Restricted Installer for Known OS"
        )
        installer.signatures.add(self.signature)

        # Refresh materialized view
        InstalledSoftwareAppInstallIndividual.refresh()

        # Get software for app_install with unknown OS
        software = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=app_install_unknown_os
        )

        # Enrich - should get installer despite restrictive os_allow because os_id=-1 is treated as no OS info
        enriched = enrich_installed_software_with_installers(software)
        self.assertEqual(len(enriched), 1)
        self.assertTrue(hasattr(enriched[0], 'app_install_product_installer'))
        self.assertEqual(enriched[0].app_install_product_installer['title'], "Restricted Installer for Known OS")

class OSFilteringEnrichmentWithOSAndRanges(TestCase):
    """Test cases for OS filtering combined with version ranges in enrichment."""

    def setUp(self):
        """Set up test data for OS and version range filtering tests."""
        # Create organisation and users
        self.organisation = OrganisationFactory()
        self.user1 = AppUserFactory(organisation=self.organisation)
        self.user2 = AppUserFactory(organisation=self.organisation)

        self.app_install_windows = AppInstallFactory(app_user=self.user1)
        self.app_install_macos = AppInstallFactory(app_user=self.user2)
        self.app_install_macos_good_range = AppInstallFactory(app_user=self.user1)

        # Create OpswatOperatingSystem instances
        OpswatOperatingSystemFactory(app_install=self.app_install_windows, os_id=10)  # Windows 10
        OpswatOperatingSystemFactory(app_install=self.app_install_macos, os_id=100)  # macOS
        OpswatOperatingSystemFactory(app_install=self.app_install_macos_good_range, os_id=100)  # macOS

        self.product = ProductFactory()

        # Create product version 20.5.0 (within the version range)
        self.product_version_20 = ProductVersionFactory(
            product=self.product, raw_version="20.5.0",
            major=20, minor=5, patch=0
        )

        # Create product version 19.9.0 (outside the version range)
        self.product_version_19 = ProductVersionFactory(
            product=self.product, raw_version="19.9.0",
            major=19, minor=9, patch=0
        )

        # Create installed products
        self.installed_product_windows = InstalledProductFactory(app_install=self.app_install_windows)
        self.installed_product_macos = InstalledProductFactory(app_install=self.app_install_macos)
        self.installed_product_macos_good_range = InstalledProductFactory(app_install=self.app_install_macos_good_range)

        # Product signature
        product_signature = ProductSignatureFactory(product=self.product)

        # Create installed product versions
        InstalledProductVersionFactory(
            installed_product=self.installed_product_windows,
            product_version=self.product_version_20,
            signature=product_signature
        )
        InstalledProductVersionFactory(
            installed_product=self.installed_product_macos,
            product_version=self.product_version_19,
            signature=product_signature
        )
        InstalledProductVersionFactory(
            installed_product=self.installed_product_macos_good_range,
            product_version=self.product_version_20,
            signature=product_signature
        )

        # Create installer with OS deny and version ranges (like Nodejs Current 20)
        patch_installer = OpswatPatchInstallerFactory(latest_version="20.10.0")
        self.installer_nodejs_20 = OpswatProductPatchInstallerFactory(
            product=self.product,
            patch_installer=patch_installer,
            os_deny="[-2,50]",  # Deny OS IDs from -2 to 50 (blocks Windows 10 with os_id=10)
            version_pattern="^20\\.",  # Match versions starting with "20."
            ranges=[{"start": "20.0.0", "limit": "20.999.999"}],  # Version range for 20.x.x
            title="Nodejs Current 20"
        )
        self.installer_nodejs_20.signatures.add(product_signature)

    def test_app_install_level_with_os_deny_and_version_ranges(self):
        """Test app_install level enrichment with both OS deny and version ranges."""
        # Refresh materialized view
        InstalledSoftwareAppInstallIndividual.refresh()

        # Test Windows 10 (os_id=10, version=20.5.0) - should NOT get installer (OS denied)
        software_win = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_windows
        )
        enriched_win = enrich_installed_software_with_installers(software_win)
        self.assertEqual(len(enriched_win), 1)
        self.assertFalse(hasattr(enriched_win[0], 'app_install_product_installer'),
                         "Windows 10 should not get installer due to OS deny despite matching version")

        # Test Macos with good version (os_id=100, version=20.5.0) - should get installer (OS allowed, version matches)
        software_good = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_macos_good_range
        )
        enriched_good = enrich_installed_software_with_installers(software_good)
        self.assertEqual(len(enriched_good), 1)
        self.assertTrue(hasattr(enriched_good[0], 'app_install_product_installer'),
                        "Linux should get installer - OS not denied and version matches")
        self.assertEqual(enriched_good[0].app_install_product_installer['title'], "Nodejs Current 20")

        # Test macOS (os_id=100, version=19.9.0) - should NOT get installer (version doesn't match)
        software_macos = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_macos
        )
        enriched_macos = enrich_installed_software_with_installers(software_macos)
        self.assertEqual(len(enriched_macos), 1)
        self.assertFalse(hasattr(enriched_macos[0], 'app_install_product_installer'),
                         "macOS should not get installer due to version mismatch despite OS being allowed")

    def test_organisation_level_with_os_deny_and_version_ranges(self):
        """Test organization level enrichment with both OS deny and version ranges."""
        # Refresh materialized view
        InstalledSoftwareOrganisationIndividual.refresh()

        # Get organisation-level installed software
        installed_software = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation_id=self.organisation.id
        )

        # Enrich with individual installs enabled
        enriched_results = enrich_installed_software_with_installers(installed_software)

        # We should have 2 enriched items (versions 20.5.0 and 19.9.0)
        self.assertEqual(len(enriched_results), 2)

        # Find the version 20.5.0 item
        version_20_item = next((item for item in enriched_results if item.version == "20.5.0"), None)
        self.assertIsNotNone(version_20_item)

        # The aggregate for version 20.5.0 should have the installer
        # (it's compatible with at least one app_install - Linux)
        self.assertTrue(hasattr(version_20_item, 'app_install_product_installer'))
        self.assertEqual(version_20_item.app_install_product_installer['title'], "Nodejs Current 20")

        # Check individual app_installs
        installers_by_app = version_20_item.app_install_product_installers_by_app_install_dynamic

        # Windows should NOT have the installer (OS denied)
        self.assertNotIn(self.app_install_windows.id, installers_by_app,
                         "Windows should not get installer due to OS deny")

        # macos_good_range should have the installer (OS allowed, version matches)
        self.assertIn(self.app_install_macos_good_range.id, installers_by_app)
        self.assertEqual(
            installers_by_app[self.app_install_macos_good_range.id]['app_install_product_installer_id'],
            self.installer_nodejs_20.id
        )

        # Find the version 19.9.0 item
        version_19_item = next((item for item in enriched_results if item.version == "19.9.0"), None)
        self.assertIsNotNone(version_19_item)

        # The aggregate for version 19.9.0 should NOT have the installer
        # (version doesn't match the ranges)
        self.assertFalse(hasattr(version_19_item, 'app_install_product_installer'),
                         "Version 19.9.0 should not get installer due to version mismatch")


class DatabaseEnrichmentTestCase(SoftwarePackageHelperTestMixin, TestCase):
    """Test case for get_enriched_installed_software_with_installers_from_db function
    when the enriching process is happening leveraging the data pre-computed in the
    database for speed purposes.
    """

    def setUp(self):
        """Set up test data using the mixin."""
        self.setUpTwoOrganisationsFourAppInstalls()

    def test_get_enriched_installed_software_with_installers_from_db_org_level(self):
        """Test database enrichment function at organization level extracts pre-stored data."""

        # Get any software at org level to test with
        org_software_record = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation=self.org1
        ).first()

        # Create a patch installer for the found product
        product_name = org_software_record.product
        patch_installer = OpswatPatchInstallerFactory(
            product_name=product_name,
            latest_version="*********"
        )
        # Try to find or create the OPSWAT product
        product, _ = Product.objects.get_or_create(name=product_name)
        opswat_product_patch = OpswatProductPatchInstallerFactory(
            product=product,
            patch_installer=patch_installer,
            title=f"{product_name} Security Update"
        )

        # Manually set the pre-computed enrichment data on the model
        # This simulates what the offline enrichment process would do
        org_software_record.opswat_product_patch_installer = opswat_product_patch
        org_software_record.patch_detected_date = timezone.now()
        # Also set individual app_install mappings
        org_software_record.app_install_product_installers_by_app_install = {
            str(app_id): opswat_product_patch.id
            for app_id in org_software_record.app_install_ids[:2]  # Just test first 2 app installs
        }
        org_software_record.save()

        # Now test that get_enriched_installed_software_with_installers_from_db
        # extracts this pre-stored data
        org_software_qs = InstalledSoftwareOrganisationIndividual.objects.filter(
            id=org_software_record.id
        )

        enriched = get_enriched_installed_software_with_installers_from_db(installed_software=org_software_qs)

        # Should return the software WITH enrichment from pre-stored data
        self.assertEqual(len(enriched), 1)
        self.assertTrue(hasattr(enriched[0], 'app_install_product_installer'))

        # Verify the enrichment data was extracted from the model
        installer_data = enriched[0].app_install_product_installer
        self.assertEqual(installer_data['app_install_product_installer_id'], opswat_product_patch.id)
        self.assertEqual(installer_data['title'], f"{product_name} Security Update")
        self.assertEqual(installer_data['installer_version'], "*********")
        self.assertIsNotNone(installer_data['patch_detected_date'])

        # Verify individual app_install mappings were preserved
        self.assertTrue(hasattr(enriched[0], 'app_install_product_installers_by_app_install'))
        # The function should preserve the mappings we set
        mappings = enriched[0].app_install_product_installers_by_app_install_dynamic
        self.assertGreaterEqual(len(mappings), 1)
        # Verify the mapping structure
        for app_install_id, mapping in mappings.items():
            self.assertIn('app_install_product_installer_id', mapping)
            self.assertIn('is_scheduled', mapping)
            self.assertEqual(mapping['app_install_product_installer_id'], opswat_product_patch.id)

    def test_get_enriched_installed_software_with_installers_from_db_app_install_level(self):
        """Test database enrichment extracts pre-stored data from the model at app install level."""

        # Create a patch installer
        edge_patch = OpswatPatchInstallerFactory(
            product_name="Microsoft Edge",
            latest_version="*********"
        )
        opswat_product_patch = OpswatProductPatchInstallerFactory(
            product=Product.objects.get(name=AI2_EDGE),
            patch_installer=edge_patch,
            title="Edge Security Update"
        )

        # First, refresh to get the software records
        InstalledSoftwareAppInstallIndividual.refresh()

        # Get Edge software record
        edge_software = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_2,
            product=AI2_EDGE
        ).first()

        # Manually set the pre-computed enrichment data on the model
        # This simulates what the offline enrichment process would do
        edge_software.opswat_product_patch_installer = opswat_product_patch
        patch_detected_date = datetime.datetime(2025, 1, 1, tzinfo=datetime.timezone.utc)
        edge_software.patch_detected_date = datetime.datetime(2025, 1, 1)
        edge_software.save()

        # Now test that get_enriched_installed_software_with_installers_from_db
        # extracts this pre-stored data
        software_qs = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_2,
            product=AI2_EDGE
        )

        enriched = get_enriched_installed_software_with_installers_from_db(
            installed_software=software_qs
        )

        # Should return the software WITH enrichment from pre-stored data
        self.assertEqual(len(enriched), 1)
        self.assertTrue(hasattr(enriched[0], 'app_install_product_installer'))

        # Verify the enrichment data was extracted from the model
        installer_data = enriched[0].app_install_product_installer
        self.assertEqual(installer_data['app_install_product_installer_id'], opswat_product_patch.id)
        self.assertEqual(installer_data['title'], "Edge Security Update")
        self.assertEqual(installer_data['installer_version'], "*********")
        self.assertFalse(installer_data['is_scheduled'])
        self.assertEqual(installer_data['patch_detected_date'], patch_detected_date)


class ScheduledInstallerAppInstallLevelLooksAtNonTerminalstates(SoftwarePackageHelperTestMixin, TestCase):
    """
    Regression test showing that scheduled installers in terminal
    states are not considered is_scheduled.
    """

    def setUp(self):
        self.setUpTwoOrganisationsFourAppInstalls()

        # Create signature for Edge product
        edge_product = Product.objects.get(name=AI2_EDGE)
        self.edge_signature = ProductSignatureFactory(product=edge_product)

        # Add signature to existing Edge installed product versions
        edge_installed_products = InstalledProductVersion.objects.filter(
            installed_product__app_install=self.app_install_2,
            product_version__product=edge_product
        )
        for ipv in edge_installed_products:
            ipv.signature = self.edge_signature
            ipv.save()

        # Refresh the view to get signatures aggregated
        InstalledSoftwareAppInstallIndividual.refresh()

        # Create a patch installer for Edge
        edge_patch = OpswatPatchInstallerFactory(
            product_name="Microsoft Edge",
            latest_version="*********"
        )
        self.product_patch_installer = OpswatProductPatchInstallerFactory(
            product=edge_product,
            patch_installer=edge_patch,
            title="Edge Security Update"
        )
        self.product_patch_installer.signatures.add(self.edge_signature)

    def test_app_install_level_ignores_terminal_state_scheduled_installers(self):
        # Clean up any existing scheduled installers for this test case
        OpswatScheduledProductInstaller.objects.all().delete()

        # Create a scheduled installer in a terminal state (completed)
        completed_scheduled = OpswatScheduledProductInstallerFactory(
            opswat_product_patch_installer=self.product_patch_installer,
            app_install=self.app_install_2,
            status=COMPLETE
        )
        edge_software = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_2,
            product=AI2_EDGE
        ).first()
        edge_software.opswat_product_patch_installer = self.product_patch_installer
        edge_software.patch_detected_date = timezone.now()
        edge_software.save()

        installed_software = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_2,
            product=AI2_EDGE
        )

        enriched_results = get_enriched_installed_software_with_installers_from_db(
            installed_software
        )

        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        # Should have installer info but NOT be scheduled (because it's in terminal state)
        self.assertTrue(hasattr(enriched_item, 'app_install_product_installer'))
        self.assertFalse(enriched_item.app_install_product_installer['is_scheduled'])
        self.assertFalse(enriched_item.app_install_product_installer['is_scheduled'])

        # Now update to non-terminal state
        completed_scheduled.status = PENDING
        completed_scheduled.save()

        # Re-fetch and enrich
        enriched_results = get_enriched_installed_software_with_installers_from_db(
            installed_software,
        )

        enriched_item = enriched_results[0]
        # Now it SHOULD be scheduled (pending is non-terminal)
        self.assertTrue(enriched_item.app_install_product_installer['is_scheduled'])
