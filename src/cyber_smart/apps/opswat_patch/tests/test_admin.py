from django.test import TestCase
from django.contrib.admin.sites import AdminSite

from appusers.models.factories import AppInstallFactory, AppUserFactory, AppVersionFactory
from opswat_patch.admin.base import OpswatPatchEventLogAdmin
from opswat_patch.factories import OpswatScheduledProductInstallerFactory
from opswat_patch.models.event_log import OpswatPatchEventLog
from opswat_patch.models.constants import SCHEDULED


class OpswatPatchEventLogAdminTests(TestCase):
    """Tests for the OpswatPatchEventLogAdmin."""

    def setUp(self):
        self.site = AdminSite()
        self.admin = OpswatPatchEventLogAdmin(OpswatPatchEventLog, self.site)

        # Create test data
        self.app_user = AppUserFactory()
        self.app_version = AppVersionFactory(major=5, minor=5, patch=0)
        self.app_install = AppInstallFactory(
            app_user=self.app_user,
            version=self.app_version,
            app_version="5.5.0"
        )
        self.scheduled_installer = OpswatScheduledProductInstallerFactory(
            app_install=self.app_install
        )

        # Create an event log
        self.event_log = OpswatPatchEventLog.objects.create(
            opswat_scheduled_product_installer=self.scheduled_installer,
            status=SCHEDULED,
            details="Test event log"
        )

    def test_cap_version_at_event_time_display(self):
        """Test that the admin method displays the CAP version at event time."""
        version = self.admin.cap_version_at_event_time(self.event_log)
        self.assertEqual(version, "5.5.0")
