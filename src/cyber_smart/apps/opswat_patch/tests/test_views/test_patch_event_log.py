import json

from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from appusers.models.factories import AppInstallFactory, AppUserFactory
from common.base_tests import BaseTestCase
from opswat_patch.factories import OpswatScheduledProductInstallerFactory, OpswatPatchAttemptFactory
from opswat_patch.models.constants import (
    SCHEDULED, ERROR, MAX_RETRY_ATTEMPTS, RETRYING_USER_DISMISSED, RETRYING_POPUP_TIMEOUT, IN_PROGRESS, COMPLETE
)
from opswat_patch.models.event_log import OpswatPatchEventLog


class OpswatPatchEventLogCreateAPIViewTests(BaseTestCase, APITestCase):
    """Tests for the OpswatPatchEventLogCreateAPIView."""

    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()

        self.app_install = AppInstallFactory(
            app_user=self.app_user,
            serial_number="TestSN12345",
        )
        self.scheduled_installer = OpswatScheduledProductInstallerFactory(app_install=self.app_install)
        self.patch_attempt = OpswatPatchAttemptFactory(
            scheduled_product_installer=self.scheduled_installer
        )
        self.url = reverse("api-v3:opswat-patch:event-log")

        self.headers = {"HTTP_APPUSER_UUID": str(self.app_user.uuid)}
        self.valid_payload = {
            "opswat_scheduled_product_installer": self.scheduled_installer.pk,
            "status": SCHEDULED,
            "details": "(internal) Installation process initiated.",
            "details_public_facing": "Public facing details of the installation process.",
            "device_id": self.app_install.device_id,
            "serial_number": self.app_install.serial_number,
        }

    def test_create_event_log_success(self):
        """Test successful creation of an event log."""
        response = self.client.post(
            self.url,
            data=self.valid_payload,
            format='json',
            **self.headers
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content)
        self.assertTrue(
            OpswatPatchEventLog.objects.filter(
                opswat_scheduled_product_installer=self.scheduled_installer,
                status=SCHEDULED,
            ).exists()
        )
        log_entry = OpswatPatchEventLog.objects.get(opswat_scheduled_product_installer=self.scheduled_installer)
        self.assertEqual(log_entry.details, "(internal) Installation process initiated.")
        self.assertEqual(log_entry.details_public_facing, "Public facing details of the installation process.")
        self.assertEqual(response.data["status"], SCHEDULED)

        # Verify the scheduled installer status was updated
        self.scheduled_installer.refresh_from_db()
        self.assertEqual(self.scheduled_installer.status, SCHEDULED)

    def test_create_event_log_missing_device_id(self):
        """Test failure when device_id is missing from request body."""
        payload = self.valid_payload.copy()
        del payload["device_id"]
        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("device_id", response.data)

    def test_create_event_log_missing_serial_number(self):
        """Test success when serial_number is missing from request body (defaults to empty string)."""
        payload = self.valid_payload.copy()
        del payload["serial_number"]

        # Create an AppInstall with empty serial_number for this test
        app_install_no_serial = AppInstallFactory(
            app_user=self.app_user,
            serial_number="",
            device_id=self.app_install.device_id
        )
        scheduled_installer_no_serial = OpswatScheduledProductInstallerFactory(app_install=app_install_no_serial)
        OpswatPatchAttemptFactory(
            scheduled_product_installer=scheduled_installer_no_serial
        )
        payload["opswat_scheduled_product_installer"] = scheduled_installer_no_serial.pk

        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content)
        self.assertTrue(
            OpswatPatchEventLog.objects.filter(
                opswat_scheduled_product_installer=scheduled_installer_no_serial,
                status=payload["status"],
            ).exists()
        )

        # Verify the scheduled installer status was updated
        scheduled_installer_no_serial.refresh_from_db()
        self.assertEqual(scheduled_installer_no_serial.status, payload["status"])

    def test_create_event_log_missing_appuser_uuid_header(self):
        """Test failure when appuser_uuid header is missing."""
        response = self.client.post(
            self.url,
            data=self.valid_payload,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("detail", response.data)
        self.assertEqual(response.data["detail"], "Missing App User UUID in request header.")

    def test_create_event_log_invalid_payload_scheduled_installer(self):
        """Test failure when opswat_scheduled_product_installer PK is invalid."""
        payload = self.valid_payload.copy()
        invalid_pk = (self.scheduled_installer.pk or 0) + 9999
        payload["opswat_scheduled_product_installer"] = invalid_pk
        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("opswat_scheduled_product_installer", response.data)

    def test_create_event_log_with_main_app_user_uuid(self):
        """Test that the endpoint works with main_app_user UUID."""
        # Create a bulk deploy user for the organisation
        bulk_deploy_user = self.app_user.organisation.get_or_create_bulk_deploy_user()

        # Create another app_user in the same organisation
        other_app_user = AppUserFactory(organisation=self.app_user.organisation)

        # Create an app_install for the other app_user
        other_app_install = AppInstallFactory(
            app_user=other_app_user,
            serial_number="TestSN67890",
        )

        # Create a scheduled installer for this app_install
        other_scheduled_installer = OpswatScheduledProductInstallerFactory(app_install=other_app_install)

        OpswatPatchAttemptFactory(
            scheduled_product_installer=other_scheduled_installer
        )

        # Create a payload for the other app_install but using the main_app_user UUID
        payload = {
            "opswat_scheduled_product_installer": other_scheduled_installer.pk,
            "status": SCHEDULED,
            "details": "Installation process initiated via main_app_user.",
            "device_id": other_app_install.device_id,
            "serial_number": other_app_install.serial_number,
        }

        # Make the request using the main_app_user UUID
        headers = {"HTTP_APPUSER_UUID": str(bulk_deploy_user.uuid)}  # Using main_app_user UUID

        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **headers
        )

        # Verify the response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content)
        self.assertTrue(
            OpswatPatchEventLog.objects.filter(
                opswat_scheduled_product_installer=other_scheduled_installer,
                status=SCHEDULED,
            ).exists()
        )

        # Verify the scheduled installer status was updated
        other_scheduled_installer.refresh_from_db()
        self.assertEqual(other_scheduled_installer.status, SCHEDULED)

    def test_create_retrying_event_log_success(self):
        """Test successful creation of a RETRYING_USER_DISMISSED event log."""
        payload = self.valid_payload.copy()
        payload["status"] = RETRYING_USER_DISMISSED
        payload["details"] = "User dismissed the retry dialog."

        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **self.headers
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content)
        self.assertTrue(
            OpswatPatchEventLog.objects.filter(
                opswat_scheduled_product_installer=self.scheduled_installer,
                status=RETRYING_USER_DISMISSED,
            ).exists()
        )

        # Verify the scheduled installer status and retry count were updated
        self.scheduled_installer.refresh_from_db()
        self.assertEqual(self.scheduled_installer.status, RETRYING_USER_DISMISSED)
        self.assertEqual(self.scheduled_installer.retry_count, 1)

    def test_three_retrying_events_trigger_auto_error(self):
        """Test that 3 RETRYING_USER_DISMISSED events are allowed, and 4th triggers auto-error."""
        payload = self.valid_payload.copy()
        payload["status"] = RETRYING_USER_DISMISSED
        payload["details"] = "User dismissed the retry dialog."

        # Create first RETRYING event
        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.scheduled_installer.refresh_from_db()
        self.assertEqual(self.scheduled_installer.retry_count, 1)
        self.assertEqual(self.scheduled_installer.status, RETRYING_USER_DISMISSED)

        # Create second RETRYING event
        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.scheduled_installer.refresh_from_db()
        self.assertEqual(self.scheduled_installer.retry_count, 2)
        self.assertEqual(self.scheduled_installer.status, RETRYING_USER_DISMISSED)

        # Create third RETRYING event - should still be allowed
        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.scheduled_installer.refresh_from_db()
        self.assertEqual(self.scheduled_installer.retry_count, 3)
        self.assertEqual(self.scheduled_installer.status, RETRYING_USER_DISMISSED)

        # Create fourth RETRYING event - should trigger auto-error
        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.scheduled_installer.refresh_from_db()
        self.assertEqual(self.scheduled_installer.retry_count, 4)
        self.assertEqual(self.scheduled_installer.status, ERROR)

        # Verify that an additional ERROR event log was automatically created
        error_logs = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer=self.scheduled_installer,
            status=ERROR
        )
        self.assertEqual(error_logs.count(), 1)
        self.assertIn(f"Automatically transitioned to error after {MAX_RETRY_ATTEMPTS} user-dismissed retry attempts",
                     error_logs.first().details)

    def test_retry_with_user_dismissed_status_counts_towards_limit(self):
        """Test that retries with RETRYING_USER_DISMISSED status count towards the limit."""
        payload = self.valid_payload.copy()
        payload["status"] = RETRYING_USER_DISMISSED
        payload["details"] = "User dismissed the retry popup."

        # Create 3 retries with user_dismissed error code
        for i in range(3):
            response = self.client.post(
                self.url,
                data=payload,
                format='json',
                **self.headers
            )
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)

            self.scheduled_installer.refresh_from_db()
            self.assertEqual(self.scheduled_installer.retry_count, i + 1)
            self.assertEqual(self.scheduled_installer.status, RETRYING_USER_DISMISSED)

        # 4th retry should trigger auto-error
        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.scheduled_installer.refresh_from_db()
        self.assertEqual(self.scheduled_installer.retry_count, 4)
        self.assertEqual(self.scheduled_installer.status, ERROR)

        # Verify error log was created
        error_logs = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer=self.scheduled_installer,
            status=ERROR
        )
        self.assertEqual(error_logs.count(), 1)
        self.assertIn("user-dismissed retry attempts", error_logs.first().details)

    def test_retry_with_popup_timeout_does_not_count_towards_limit(self):
        """Test that retries with RETRYING_POPUP_TIMEOUT status do NOT count towards the limit."""
        payload = self.valid_payload.copy()
        payload["status"] = RETRYING_POPUP_TIMEOUT
        payload["details"] = "Retry popup timed out."

        # Create 5 retries with popup_timeout error code (more than MAX_RETRY_ATTEMPTS)
        for i in range(5):
            response = self.client.post(
                self.url,
                data=payload,
                format='json',
                **self.headers
            )
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)

            self.scheduled_installer.refresh_from_db()
            # Retry count should remain 0 since timeout retries don't count
            self.assertEqual(self.scheduled_installer.retry_count, 0)
            self.assertEqual(self.scheduled_installer.status, RETRYING_POPUP_TIMEOUT)

        # Verify no error transition occurred
        error_logs = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer=self.scheduled_installer,
            status=ERROR
        )
        self.assertEqual(error_logs.count(), 0)

    def test_retry_count_persists_through_in_progress_state(self):
        """Test that retry count does not reset when status changes to IN_PROGRESS."""
        # First, create a RETRYING event
        payload = self.valid_payload.copy()
        payload["status"] = RETRYING_USER_DISMISSED
        payload["details"] = "Retrying installation process."

        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.scheduled_installer.refresh_from_db()
        self.assertEqual(self.scheduled_installer.retry_count, 1)
        self.assertEqual(self.scheduled_installer.status, RETRYING_USER_DISMISSED)

        # Change status to IN_PROGRESS
        payload["status"] = IN_PROGRESS
        payload["details"] = "Installation in progress."

        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify retry count persists (does not reset to 0)
        self.scheduled_installer.refresh_from_db()
        self.assertEqual(self.scheduled_installer.retry_count, 1)
        self.assertEqual(self.scheduled_installer.status, IN_PROGRESS)

        # Create another RETRYING event
        payload["status"] = RETRYING_USER_DISMISSED
        payload["details"] = "Retrying installation process again."

        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            **self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify retry count incremented correctly
        self.scheduled_installer.refresh_from_db()
        self.assertEqual(self.scheduled_installer.retry_count, 2)
        self.assertEqual(self.scheduled_installer.status, RETRYING_USER_DISMISSED)

    def test_extract_error_code_from_details_when_error_code_is_9999(self):
        """E2E test: error code extraction from details field when error_code is -9999."""
        # Using actual structure from real OPSWAT error response
        details_data = {
            "errors": [ { "returned_at": "8", "message": "d", "code": -1026 }, { "returned_at": "d2", "code": -1026 }, { "returned_at": "b", "code": -1026 } ],
            "method": 50300, "code": -1026,
            "description": "Defines an error when a product is not supported.", "timing": 391, "timestamp": "1753105193",
            "signature": 477,
            "define": "WA_VMOD_ERROR_PRODUCT_NOT_SUPPORTED"
        }

        payload = self.valid_payload.copy()
        payload["status"] = ERROR
        payload["error_code"] = "-9999"
        payload["details"] = json.dumps(details_data)

        response = self.client.post(self.url, data=payload, format='json', **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content)

        # Verify the event log was created with the extracted error code
        event_log = OpswatPatchEventLog.objects.get(
            opswat_scheduled_product_installer=self.scheduled_installer
        )
        self.assertEqual(event_log.error_code, "-1026")  # Extracted from details
        self.assertEqual(event_log.status, ERROR)

    def test_event_log_updates_patch_attempt_status(self) -> None:
        """Test that creating an event log updates the associated patch attempt status."""
        # Verify initial patch attempt status
        self.patch_attempt.refresh_from_db()
        initial_status = self.patch_attempt.status

        # Create an event log with IN_PROGRESS status
        payload = self.valid_payload.copy()
        payload["status"] = IN_PROGRESS
        payload["details"] = "Installation in progress."

        response = self.client.post(self.url, data=payload, format='json', **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify patch attempt status was updated
        self.patch_attempt.refresh_from_db()
        self.assertEqual(self.patch_attempt.status, IN_PROGRESS)
        self.assertNotEqual(self.patch_attempt.status, initial_status)

        # Verify the event log is associated with the patch attempt
        event_log = OpswatPatchEventLog.objects.get(
            opswat_scheduled_product_installer=self.scheduled_installer,
            status=IN_PROGRESS
        )
        self.assertEqual(event_log.patch_attempt_id, self.patch_attempt.id)

    def test_terminal_status_updates_patch_attempt_finished_at(self) -> None:
        """Test that terminal status event logs update patch attempt finished_at timestamp."""
        # Verify initial state
        self.patch_attempt.refresh_from_db()
        self.assertIsNone(self.patch_attempt.finished_at)

        # Create an event log with terminal status (ERROR)
        payload = self.valid_payload.copy()
        payload["status"] = ERROR
        payload["error_code"] = "-1001"
        payload["details"] = "Installation failed."

        response = self.client.post(self.url, data=payload, format='json', **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify patch attempt was updated
        self.patch_attempt.refresh_from_db()
        self.assertEqual(self.patch_attempt.status, ERROR)
        self.assertIsNotNone(self.patch_attempt.finished_at)

    def test_terminal_status_triggers_patch_job_update(self) -> None:
        """Test that terminal status event logs trigger patch job status update."""
        # Get the patch job
        patch_job = self.patch_attempt.patch_job
        patch_job.refresh_from_db()
        self.assertEqual(patch_job.status, IN_PROGRESS)
        self.assertIsNone(patch_job.finished_at)

        # Create an event log with terminal status (COMPLETE)
        payload = self.valid_payload.copy()
        payload["status"] = COMPLETE
        payload["details"] = "Installation completed successfully."

        response = self.client.post( self.url, data=payload, format='json', **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify patch job was updated
        patch_job.refresh_from_db()
        self.assertEqual(patch_job.status, COMPLETE)
        self.assertIsNotNone(patch_job.finished_at)

    def test_multiple_attempts_job_status_aggregation(self) -> None:
        """Test patch job status aggregation with multiple patch attempts."""
        # Create second app install and patch attempt
        app_install2 = AppInstallFactory(
            app_user=self.app_user,
            serial_number="TestSN22222",
        )
        scheduled_installer2 = OpswatScheduledProductInstallerFactory(app_install=app_install2)
        OpswatPatchAttemptFactory(
            scheduled_product_installer=scheduled_installer2,
            patch_job=self.patch_attempt.patch_job
        )

        # Create third app install and patch attempt
        app_install3 = AppInstallFactory(
            app_user=self.app_user,
            serial_number="TestSN33333",
        )
        scheduled_installer3 = OpswatScheduledProductInstallerFactory(app_install=app_install3)
        OpswatPatchAttemptFactory(
            scheduled_product_installer=scheduled_installer3,
            patch_job=self.patch_attempt.patch_job
        )

        # Mark first attempt as COMPLETE
        payload1 = {
            "opswat_scheduled_product_installer": self.scheduled_installer.pk,
            "status": COMPLETE,
            "details": "First installation completed.",
            "device_id": self.app_install.device_id,
            "serial_number": self.app_install.serial_number,
        }
        response1 = self.client.post(self.url, data=payload1, format='json', **self.headers)
        self.assertEqual(response1.status_code, status.HTTP_201_CREATED, response1.content)

        # Verify job is still IN_PROGRESS (not all attempts are terminal)
        patch_job = self.patch_attempt.patch_job
        patch_job.refresh_from_db()
        self.assertEqual(patch_job.status, IN_PROGRESS)

        # Mark second attempt as COMPLETE
        payload2 = {
            "opswat_scheduled_product_installer": scheduled_installer2.pk,
            "status": COMPLETE,
            "details": "Second installation completed.",
            "device_id": app_install2.device_id,
            "serial_number": app_install2.serial_number,
        }
        response2 = self.client.post(self.url, data=payload2, format='json', **self.headers)
        self.assertEqual(response2.status_code, status.HTTP_201_CREATED)

        # Job should still be IN_PROGRESS
        patch_job.refresh_from_db()
        self.assertEqual(patch_job.status, IN_PROGRESS)

        # Mark third attempt as ERROR
        payload3 = {
            "opswat_scheduled_product_installer": scheduled_installer3.pk,
            "status": ERROR,
            "error_code": "-1002",
            "details": "Third installation failed.",
            "device_id": app_install3.device_id,
            "serial_number": app_install3.serial_number,
        }
        response3 = self.client.post(self.url, data=payload3, format='json', **self.headers)
        self.assertEqual(response3.status_code, status.HTTP_201_CREATED)

        # Now all attempts are terminal, job should be ERROR (since one failed)
        patch_job.refresh_from_db()
        self.assertEqual(patch_job.status, ERROR)
        self.assertIsNotNone(patch_job.finished_at)

    def test_non_terminal_status_does_not_update_job(self) -> None:
        """Test that non-terminal status event logs don't trigger job updates."""
        # Get initial job state
        patch_job = self.patch_attempt.patch_job
        patch_job.refresh_from_db()
        initial_job_modified = patch_job.modified

        # Create event log with non-terminal status
        payload = self.valid_payload.copy()
        payload["status"] = IN_PROGRESS
        payload["details"] = "Installation in progress."

        response = self.client.post(self.url, data=payload, format='json', **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify job was not updated (modified timestamp should be the same)
        patch_job.refresh_from_db()
        self.assertEqual(patch_job.status, IN_PROGRESS)
        self.assertEqual(patch_job.modified, initial_job_modified)
