from django.urls import reverse

from rest_framework.test import APITestCase

from appusers.models.factories import AppInstallFactory, AppUserFactory
from common.base_tests import BaseTestCase
from organisations.factories import OrganisationFactory
from opswat_patch.factories import (
    OpswatScheduledProductInstallerFactory,
    OpswatProductPatchInstallerFactory,
    OpswatPatchInstallerFactory,
    OpswatPatchInstallerDownloadLinkFactory,
    OpswatArchitectureFactory,
    OpswatPatchEventLogFactory, OpswatPatchAttemptFactory,
)
from opswat_patch.models import OpswatScheduledProductInstaller, OpswatArchitecture
from opswat_patch.models.constants import PENDING, IN_PROGRESS, COMPLETE, ERROR
from opswat.factories import ProductFactory, ProductVendorFactory, ProductSignatureFactory


class OpswatScheduledProductInstallerAPITest(BaseTestCase, APITestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.create_onboarding()
        self.add_devices_permission_to_test_user(self.organisation)
        self.client.force_authenticate(user=self.user)

    def test_get_scheduled_product_installers_success(self):
        app_install = AppInstallFactory(app_user=self.app_user, serial_number='serial_number_cant_be_empty')
        vendor = ProductVendorFactory()
        product = ProductFactory(vendor=vendor)
        patch_installer = OpswatPatchInstallerFactory()

        architecture, _ = OpswatArchitecture.objects.get_or_create(name="x64")
        download_link1 = OpswatPatchInstallerDownloadLinkFactory(
            patch_installer=patch_installer,
            os_architecture=architecture
        )
        download_link2 = OpswatPatchInstallerDownloadLinkFactory(patch_installer=patch_installer)

        signature1 = ProductSignatureFactory()
        signature2 = ProductSignatureFactory()

        opswat_product_patch_installer = OpswatProductPatchInstallerFactory(
            product=product,
            patch_installer=patch_installer,
            title="Test Patch Title",
            os_allow="windows_10,windows_11",
            os_deny="mac_os"
        )
        opswat_product_patch_installer.signatures.add(signature1, signature2)

        opswat_scheduled_product_patch_installer = OpswatScheduledProductInstallerFactory(
            creator=self.user,
            opswat_product_patch_installer=opswat_product_patch_installer,
            app_install=app_install,
            signature=signature1
        )
        OpswatPatchAttemptFactory(
            scheduled_product_installer=opswat_scheduled_product_patch_installer
        )

        other_app_install = AppInstallFactory(app_user=self.app_user)
        other_opswat_product_patch_installer = OpswatProductPatchInstallerFactory()
        OpswatScheduledProductInstallerFactory(
            creator=self.user,
            opswat_product_patch_installer=other_opswat_product_patch_installer,
            app_install=other_app_install
        )

        url = reverse('api-v3:opswat-patch:scheduled-product-installers')
        response = self.client.get(
            url,
            data={
                'device_id': app_install.device_id,
                'serial_number': app_install.serial_number,
            },
            HTTP_APPUSER_UUID=str(app_install.app_user.uuid)
        )

        self.assertEqual(response.status_code, 200, response.content)
        data = response.json()
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 1)

        item = data[0]
        self.assertIn('opswat_product_patch_installer', item)
        nested_data = item['opswat_product_patch_installer']

        self.assertEqual(nested_data['id'], opswat_product_patch_installer.id)
        self.assertEqual(nested_data['opswat_id'], opswat_product_patch_installer.opswat_id)

        self.assertIn('product', nested_data)
        product_data = nested_data['product']
        self.assertEqual(product_data['opswat_id'], product.opswat_id)
        self.assertEqual(product_data['name'], product.name)
        self.assertEqual(product_data['vendor'], vendor.id)

        self.assertIn('patch_installer', nested_data)
        patch_installer_data = nested_data['patch_installer']
        self.assertEqual(patch_installer_data['id'], patch_installer.id)
        self.assertIn('download_links', patch_installer_data)
        self.assertEqual(len(patch_installer_data['download_links']), 2)
        download_link_ids = {link['id'] for link in patch_installer_data['download_links']}
        self.assertEqual(download_link_ids, {download_link1.id, download_link2.id})

        link1_data = next((link for link in patch_installer_data['download_links'] if link['id'] == download_link1.id), None)
        self.assertIsNotNone(link1_data)
        self.assertIn('os_architecture', link1_data)
        self.assertIsNotNone(link1_data['os_architecture'])
        self.assertEqual(link1_data['os_architecture']['id'], architecture.id)
        self.assertEqual(link1_data['os_architecture']['name'], architecture.name)

        self.assertIn('os_allow', nested_data)
        self.assertEqual(nested_data['os_allow'], "windows_10,windows_11")
        self.assertIn('os_deny', nested_data)
        self.assertEqual(nested_data['os_deny'], "mac_os")

        self.assertIn('signature', item)
        self.assertEqual(item['signature'], int(signature1.opswat_id))

    def test_get_scheduled_product_installers_filtering_TERMINAL_STATUSES(self):
        """Test that scheduled installers in terminal states are filtered out by default."""
        app_install = AppInstallFactory(app_user=self.app_user, serial_number='serial_number_cant_be_empty')

        # Non-terminal state
        pending_installer = OpswatScheduledProductInstallerFactory(app_install=app_install)
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=pending_installer,
            status=PENDING
        )
        in_progress_installer = OpswatScheduledProductInstallerFactory(app_install=app_install)
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=in_progress_installer,
            status=IN_PROGRESS
        )

        # Terminal states
        completed_installer = OpswatScheduledProductInstallerFactory(app_install=app_install)
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=completed_installer,
            status=COMPLETE
        )
        error_installer = OpswatScheduledProductInstallerFactory(app_install=app_install)
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=error_installer,
            status=ERROR,
            error_code="TEST_ERROR"
        )

        # Should only get non-terminal states by default
        url = reverse('api-v3:opswat-patch:scheduled-product-installers')
        response = self.client.get(
            url,
            data={
                'device_id': app_install.device_id,
                'serial_number': app_install.serial_number,
            },
            HTTP_APPUSER_UUID=str(app_install.app_user.uuid)
        )

        self.assertEqual(response.status_code, 200, response.content)
        data = response.json()
        self.assertEqual(len(data), 2)

        # Get the statuses from the response data
        statuses = [item.get('status') for item in data]
        self.assertIn(PENDING, statuses)
        self.assertIn(IN_PROGRESS, statuses)
        self.assertNotIn(COMPLETE, statuses)
        self.assertNotIn(ERROR, statuses)

        # Get all states when all=true is specified
        response = self.client.get(
            url,
            data={
                'device_id': app_install.device_id,
                'serial_number': app_install.serial_number,
                'all': 'true'
            },
            HTTP_APPUSER_UUID=str(app_install.app_user.uuid)
        )

        self.assertEqual(response.status_code, 200, response.content)
        data = response.json()
        self.assertEqual(len(data), 4)

        # Get all statuses from the response data
        all_statuses = [item.get('status') for item in data]
        self.assertIn(PENDING, all_statuses)
        self.assertIn(IN_PROGRESS, all_statuses)
        self.assertIn(COMPLETE, all_statuses)
        self.assertIn(ERROR, all_statuses)

    def test_get_scheduled_product_installers_missing_parameters(self):
        url = reverse('api-v3:opswat-patch:scheduled-product-installers')
        response = self.client.get(url, HTTP_APPUSER_UUID='dummy-uuid')
        self.assertEqual(response.status_code, 400)

    def test_get_scheduled_product_installers_with_main_app_user_uuid(self):
        """Test that the endpoint works with main_app_user UUID."""
        # Create a bulk deploy user for the organisation
        bulk_deploy_user = self.organisation.get_or_create_bulk_deploy_user()

        # Create an app_user in the same organisation
        other_app_user = AppUserFactory(organisation=self.organisation)

        # Create an app_install for the other app_user
        app_install = AppInstallFactory(
            app_user=other_app_user,
            serial_number='serial_number_for_main_app_user_test'
        )

        # Create a scheduled installer for this app_install
        OpswatScheduledProductInstallerFactory(app_install=app_install)

        # Make the request using the main_app_user UUID
        url = reverse('api-v3:opswat-patch:scheduled-product-installers')
        response = self.client.get(
            url,
            data={
                'device_id': app_install.device_id,
                'serial_number': app_install.serial_number,
            },
            HTTP_APPUSER_UUID=str(bulk_deploy_user.uuid)  # Using main_app_user UUID
        )

        # Verify the response
        self.assertEqual(response.status_code, 200, response.content)
        data = response.json()
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 1)

class OpswatScheduledProductInstallerCreateAPITest(BaseTestCase, APITestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.creating_app_user()
        self.create_onboarding()
        self.add_devices_permission_to_test_user(self.organisation)
        self.client.force_authenticate(user=self.user)

    def test_create_scheduled_product_installer_success(self):
        app_install = AppInstallFactory(app_user=self.app_user, app_user__organisation=self.organisation)
        vendor = ProductVendorFactory()
        product = ProductFactory(vendor=vendor)
        patch_installer = OpswatPatchInstallerFactory()

        architecture = OpswatArchitectureFactory(name="arm64")
        download_link = OpswatPatchInstallerDownloadLinkFactory(
            patch_installer=patch_installer,
            os_architecture=architecture
        )
        signature1 = ProductSignatureFactory()

        opswat_product_patch_installer = OpswatProductPatchInstallerFactory(
            product=product,
            patch_installer=patch_installer,
            title="Test Patch Title for Create",
            os_allow="linux",
            os_deny="windows_xp",
            architecture=architecture
        )
        opswat_product_patch_installer.signatures.add(signature1)

        url = reverse('api-v3:opswat-patch:scheduled-product-installer-create')
        data = {
            "opswat_product_patch_installer_id": opswat_product_patch_installer.id,
            "app_install": app_install.id,
            "signature": signature1.opswat_id
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, 201, response.content)
        json_data = response.json()

        self.assertTrue(OpswatScheduledProductInstaller.objects.filter(
            opswat_product_patch_installer=opswat_product_patch_installer,
            app_install=app_install,
            signature=signature1
        ).exists())

        scheduled_installer = OpswatScheduledProductInstaller.objects.get(
            opswat_product_patch_installer=opswat_product_patch_installer,
            app_install=app_install,
            signature=signature1
        )
        self.assertEqual(scheduled_installer.creator, self.user)

        self.assertIn('opswat_product_patch_installer', json_data)
        nested_data = json_data['opswat_product_patch_installer']

        self.assertEqual(nested_data['id'], opswat_product_patch_installer.id)
        self.assertEqual(nested_data['opswat_id'], opswat_product_patch_installer.opswat_id)

        self.assertIn('product', nested_data)
        product_data = nested_data['product']
        self.assertEqual(product_data['opswat_id'], product.opswat_id)
        self.assertEqual(product_data['name'], product.name)
        self.assertEqual(product_data['vendor'], vendor.id)

        self.assertIn('patch_installer', nested_data)
        patch_installer_data = nested_data['patch_installer']
        self.assertEqual(patch_installer_data['id'], patch_installer.id)

        self.assertIn('download_links', patch_installer_data)
        self.assertEqual(len(patch_installer_data['download_links']), 1)
        self.assertEqual(patch_installer_data['download_links'][0]['id'], download_link.id)

        link_data = patch_installer_data['download_links'][0]
        self.assertIn('os_architecture', link_data)
        self.assertIsNotNone(link_data['os_architecture'])
        self.assertEqual(link_data['os_architecture']['id'], architecture.id)
        self.assertEqual(link_data['os_architecture']['name'], architecture.name)

        self.assertIn('os_allow', nested_data)
        self.assertEqual(nested_data['os_allow'], "linux")
        self.assertIn('os_deny', nested_data)
        self.assertEqual(nested_data['os_deny'], "windows_xp")

        self.assertIn('signature', json_data)
        self.assertEqual(json_data['signature'], int(signature1.opswat_id))

    def test_create_scheduled_product_installer_missing_fields(self):
        url = reverse('api-v3:opswat-patch:scheduled-product-installer-create')
        data = {}
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, 400, response.content)
        self.assertIn("This field is required.", response.json().get("opswat_product_patch_installer_id", ""))
        self.assertIn("This field is required.", response.json().get("app_install", ""))

    def test_create_scheduled_product_installer_unauthorized(self):
        self.client.force_authenticate(user=None)
        url = reverse('api-v3:opswat-patch:scheduled-product-installer-create')
        data = {
            "opswat_product_patch_installer_id": 1,
            "app_install": 1
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, 403, response.content)

    def test_create_scheduled_product_installer_not_found(self):
        url = reverse('api-v3:opswat-patch:scheduled-product-installer-create')
        app_install = AppInstallFactory(app_user=self.app_user)
        architecture = OpswatArchitectureFactory(name="arm64")
        existing_installer = OpswatProductPatchInstallerFactory(architecture=architecture)
        non_existent_pk = (existing_installer.id or 0) + 1000
        data = {
            "opswat_product_patch_installer_id": non_existent_pk,
            "app_install": app_install.id
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, 400)
        self.assertIn("does not exist", str(response.json().get("opswat_product_patch_installer_id", "")))
        self.assertFalse(OpswatScheduledProductInstaller.objects.exists())

    def test_create_scheduled_product_installer_org_mismatch(self):
        other_organisation = OrganisationFactory()
        different_app_user = AppUserFactory(organisation=other_organisation)
        app_install = AppInstallFactory(app_user=different_app_user, serial_number='serial_number_cant_be_empty')
        opswat_product_patch_installer = OpswatProductPatchInstallerFactory()
        url = reverse('api-v3:opswat-patch:scheduled-product-installer-create')
        data = {
            "opswat_product_patch_installer_id": opswat_product_patch_installer.id,
            "app_install": app_install.id
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, 403, response.content)
