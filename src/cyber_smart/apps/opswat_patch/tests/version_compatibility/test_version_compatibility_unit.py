"""Unit tests for is_patch_version_compatible function - no database required."""
import unittest
from unittest.mock import Mock

from opswat_patch.models import OpswatProductPatchInstaller
from opswat_patch.version_compatibility import is_patch_version_compatible


class TestIsVersionCompatible(unittest.TestCase):

    def create_mock_installer(self, version_pattern=None, ranges=None):
        """Create a mock installer with given version_pattern and ranges."""
        installer = Mock(spec=OpswatProductPatchInstaller)
        installer.version_pattern = version_pattern
        installer.ranges = ranges
        return installer

    def test_basic_pattern_functionality(self):
        """Test basic version pattern matching."""
        installer = self.create_mock_installer(version_pattern=r'^5\.')

        self.assertTrue(is_patch_version_compatible(installer, "5.0"))
        self.assertTrue(is_patch_version_compatible(installer, "5.1"))
        self.assertFalse(is_patch_version_compatible(installer, "4.0"))
        self.assertFalse(is_patch_version_compatible(installer, "6.0"))

    def test_basic_range_functionality(self):
        """Test basic version range matching."""
        installer = self.create_mock_installer(
            ranges=[{"start": "2.0", "limit": "3.0"}]
        )

        self.assertTrue(is_patch_version_compatible(installer, "2.0"))
        self.assertTrue(is_patch_version_compatible(installer, "2.5"))
        self.assertTrue(is_patch_version_compatible(installer, "3.0"))
        self.assertFalse(is_patch_version_compatible(installer, "1.9"))
        self.assertFalse(is_patch_version_compatible(installer, "3.1"))
