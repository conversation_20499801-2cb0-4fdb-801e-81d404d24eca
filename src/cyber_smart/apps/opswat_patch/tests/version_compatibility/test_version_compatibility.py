import unittest
from unittest.mock import Mock

from opswat_patch.models import OpswatProductPatchInstaller
from opswat_patch.version_compatibility import is_patch_version_compatible


class TestVersionCompatibility(unittest.TestCase):

    def create_installer(self, version_pattern=None, ranges=None):
        """Helper to create mock installer."""
        installer = Mock(spec=OpswatProductPatchInstaller)
        installer.version_pattern = version_pattern
        installer.ranges = ranges
        return installer

    # ========== VERSION PATTERN TESTS ==========

    def test_pattern_simple_major_version(self):
        r"""Test simple major version patterns like ^5\."""
        test_cases = [
            # (pattern, version, should_match)
            (r'^5\.', '5.0', True),
            (r'^5\.', '5.1', True),
            (r'^5\.', '5.99.99', True),
            (r'^5\.', '4.99', False),
            (r'^5\.', '6.0', False),
            (r'^5\.', '50.0', False),
        ]

        for pattern, version, should_match in test_cases:
            installer = self.create_installer(version_pattern=pattern)
            result = is_patch_version_compatible(installer, version)
            self.assertEqual(result, should_match,
                           f"Pattern {pattern} with version {version} should be {should_match}")

    def test_pattern_major_minor_version(self):
        r"""Test major.minor version patterns like ^5\.0\."""
        test_cases = [
            # (pattern, version, should_match)
            (r'^5\.0\.', '5.0.0', True),
            (r'^5\.0\.', '5.0.1', True),
            (r'^5\.0\.', '5.0.99', True),
            (r'^5\.0\.', '5.1.0', False),
            (r'^5\.0\.', '4.0.0', False),
            (r'^10\.2\.', '10.2.0', True),
            (r'^10\.2\.', '10.2.99', True),
            (r'^10\.2\.', '10.3.0', False),
        ]

        for pattern, version, should_match in test_cases:
            installer = self.create_installer(version_pattern=pattern)
            result = is_patch_version_compatible(installer, version)
            self.assertEqual(result, should_match)

    def test_pattern_character_classes(self):
        """Test patterns with character classes like [0-9], [1-5]."""
        test_cases = [
            # (pattern, version, should_match)
            (r'^[1-5]\.', '1.0', True),
            (r'^[1-5]\.', '3.5', True),
            (r'^[1-5]\.', '5.9', True),
            (r'^[1-5]\.', '0.9', False),
            (r'^[1-5]\.', '6.0', False),
            (r'^[0-9]{2}\.', '10.0', True),
            (r'^[0-9]{2}\.', '99.9', True),
            (r'^[0-9]{2}\.', '9.0', False),
            (r'^[0-9]{2}\.', '100.0', False),
        ]

        for pattern, version, should_match in test_cases:
            installer = self.create_installer(version_pattern=pattern)
            result = is_patch_version_compatible(installer, version)
            self.assertEqual(result, should_match)

    def test_pattern_with_alternation(self):
        """Test patterns with alternation (|)."""
        test_cases = [
            # (pattern, version, should_match)
            (r'^5\.|^10\.', '5.0', True),
            (r'^5\.|^10\.', '10.0', True),
            (r'^5\.|^10\.', '7.0', False),
            (r'^\d\.\d\.\d\.\d$|^\d\.\d$', '1.2.3.4', True),
            (r'^\d\.\d\.\d\.\d$|^\d\.\d$', '1.2', True),
            (r'^\d\.\d\.\d\.\d$|^\d\.\d$', '1.2.3', False),
        ]

        for pattern, version, should_match in test_cases:
            installer = self.create_installer(version_pattern=pattern)
            result = is_patch_version_compatible(installer, version)
            self.assertEqual(result, should_match)

    def test_pattern_invalid_regex(self):
        """Test handling of invalid regex patterns."""
        # Invalid regex should fall through to ranges or default behavior
        installer = self.create_installer(
            version_pattern='[invalid(regex',  # Unclosed bracket
            ranges=[{'start': '1.0', 'limit': '2.0'}]
        )

        # Should use ranges since pattern is invalid
        self.assertTrue(is_patch_version_compatible(installer, '1.5'))
        self.assertFalse(is_patch_version_compatible(installer, '2.5'))

    # ========== VERSION RANGE TESTS ==========

    def test_range_single_with_both_boundaries(self):
        """Test single range with start and limit."""
        test_cases = [
            # (ranges, version, should_match)
            ([{'start': '1.0.0', 'limit': '2.0.0'}], '0.9.9', False),
            ([{'start': '1.0.0', 'limit': '2.0.0'}], '1.0.0', True),   # Start boundary
            ([{'start': '1.0.0', 'limit': '2.0.0'}], '1.5.0', True),   # Middle
            ([{'start': '1.0.0', 'limit': '2.0.0'}], '2.0.0', True),   # Limit boundary
            ([{'start': '1.0.0', 'limit': '2.0.0'}], '2.0.1', False),
        ]

        for ranges, version, should_match in test_cases:
            installer = self.create_installer(ranges=ranges)
            result = is_patch_version_compatible(installer, version)
            self.assertEqual(result, should_match)

    def test_range_open_ended(self):
        """Test ranges with no upper limit."""
        installer = self.create_installer(ranges=[{'start': '5.0.0'}])

        # Should match anything >= 5.0.0
        self.assertTrue(is_patch_version_compatible(installer, '5.0.0'))
        self.assertTrue(is_patch_version_compatible(installer, '10.0.0'))
        self.assertTrue(is_patch_version_compatible(installer, '999.999.999'))

        # Should not match below start
        self.assertFalse(is_patch_version_compatible(installer, '4.9.9'))

    def test_range_with_null_start(self):
        """Test ranges with null/None start."""
        test_cases = [
            # Test with None
            {'start': None, 'limit': '5.0.0'},
            # Test with string 'null'
            {'start': 'null', 'limit': '5.0.0'},
        ]

        for range_config in test_cases:
            installer = self.create_installer(ranges=[range_config])

            # Should match anything <= 5.0.0
            self.assertTrue(is_patch_version_compatible(installer, '0.0.0'))
            self.assertTrue(is_patch_version_compatible(installer, '1.0.0'))
            self.assertTrue(is_patch_version_compatible(installer, '5.0.0'))

            # Should not match above limit
            self.assertFalse(is_patch_version_compatible(installer, '5.0.1'))

    def test_range_multiple_non_overlapping(self):
        """Test multiple non-overlapping ranges."""
        installer = self.create_installer(ranges=[
            {'start': '1.0', 'limit': '1.9'},
            {'start': '3.0', 'limit': '3.9'},
            {'start': '5.0', 'limit': '5.9'}
        ])

        # Should match within any range
        self.assertTrue(is_patch_version_compatible(installer, '1.5'))
        self.assertTrue(is_patch_version_compatible(installer, '3.5'))
        self.assertTrue(is_patch_version_compatible(installer, '5.5'))

        # Should not match in gaps
        self.assertFalse(is_patch_version_compatible(installer, '2.0'))
        self.assertFalse(is_patch_version_compatible(installer, '4.0'))
        self.assertFalse(is_patch_version_compatible(installer, '6.0'))

    # ========== PRIORITY TESTS ==========

    def test_priority_pattern_over_ranges(self):
        """Test that pattern takes priority when both exist."""
        installer = self.create_installer(
            version_pattern=r'^5\.',
            ranges=[{'start': '1.0', 'limit': '3.0'}]
        )

        # Version matches pattern but not ranges - should match
        self.assertTrue(is_patch_version_compatible(installer, '5.0'))

        # Version matches ranges but not pattern - should not match
        self.assertFalse(is_patch_version_compatible(installer, '2.0'))

    def test_priority_no_pattern_use_ranges(self):
        """Test that ranges are used when no pattern exists."""
        installer = self.create_installer(
            version_pattern=None,
            ranges=[{'start': '1.0', 'limit': '3.0'}]
        )

        self.assertTrue(is_patch_version_compatible(installer, '2.0'))
        self.assertFalse(is_patch_version_compatible(installer, '4.0'))

    def test_priority_no_filters_match_all(self):
        """Test that everything matches when no pattern or ranges exist."""
        installer = self.create_installer()  # No pattern, no ranges

        # Should match any version
        test_versions = ['0.0.0', '1.2.3', '999.999.999', 'any.version', '']
        for version in test_versions:
            self.assertTrue(is_patch_version_compatible(installer, version))

    # ========== EDGE CASES ==========

    def test_edge_case_empty_strings(self):
        """Test handling of empty strings."""
        installer_with_pattern = self.create_installer(version_pattern=r'^5\.')
        installer_with_range = self.create_installer(ranges=[{'start': '1.0', 'limit': '2.0'}])

        # Empty string should not match pattern
        self.assertFalse(is_patch_version_compatible(installer_with_pattern, ''))

        # Empty string should not match range (can't parse)
        self.assertFalse(is_patch_version_compatible(installer_with_range, ''))

    def test_edge_case_invalid_versions(self):
        """Test handling of unparseable version strings."""
        installer = self.create_installer(ranges=[{'start': '1.0', 'limit': '2.0'}])

        # Invalid versions should return False for range checks
        invalid_versions = ['invalid', 'abc.def', '...', 'version-1']
        for version in invalid_versions:
            self.assertFalse(is_patch_version_compatible(installer, version))

    def test_edge_case_prerelease_versions(self):
        """Test versions with prerelease tags."""
        installer = self.create_installer(ranges=[{'start': '5.0.0', 'limit': '5.0.99'}])

        # Should handle prerelease versions
        self.assertTrue(is_patch_version_compatible(installer, '5.0.0-beta'))
        self.assertTrue(is_patch_version_compatible(installer, '5.0.50-alpha.1'))
        self.assertFalse(is_patch_version_compatible(installer, '5.1.0-beta'))

    def test_edge_case_very_large_versions(self):
        """Test handling of very large version numbers."""
        installer = self.create_installer(ranges=[{'start': '999.0.0'}])

        self.assertTrue(is_patch_version_compatible(installer, '999.0.0'))
        self.assertTrue(is_patch_version_compatible(installer, '9999.9999.9999'))
        self.assertFalse(is_patch_version_compatible(installer, '998.999.999'))

    def test_range_with_four_part_versions(self):
        """Test ranges with 4-part version numbers like 19.11.9999.9999."""
        installer = self.create_installer(ranges=[
            {'start': '0', 'limit': '19.11.9999.9999'},
            {'start': '20.02.0.000', 'limit': '22.02.9999.9999'},
            {'start': '22.04.0.000', 'limit': '23.11.9999.9999'},
            {'start': '24.03.0.000'}
        ])

        # Test versions in first range
        self.assertTrue(is_patch_version_compatible(installer, '0.0.0.0'))
        self.assertTrue(is_patch_version_compatible(installer, '19.11.9999.9999'))
        self.assertTrue(is_patch_version_compatible(installer, '19.11.5000.0'))
        self.assertTrue(is_patch_version_compatible(installer, '10.0.0.0'))

        # Test gap between first and second range
        self.assertFalse(is_patch_version_compatible(installer, '19.12.0.0'))
        self.assertFalse(is_patch_version_compatible(installer, '20.01.9999.9999'))

        # Test versions in second range
        self.assertTrue(is_patch_version_compatible(installer, '20.02.0.000'))
        self.assertTrue(is_patch_version_compatible(installer, '21.0.0.0'))
        self.assertTrue(is_patch_version_compatible(installer, '22.02.9999.9999'))

        # Test gap between second and third range
        self.assertFalse(is_patch_version_compatible(installer, '22.03.0.0'))

        # Test versions in third range
        self.assertTrue(is_patch_version_compatible(installer, '22.04.0.000'))
        self.assertTrue(is_patch_version_compatible(installer, '23.11.9999.9999'))

        # Test gap before fourth range
        self.assertFalse(is_patch_version_compatible(installer, '23.12.0.0'))
        self.assertFalse(is_patch_version_compatible(installer, '24.02.9999.9999'))

        # Test versions in fourth range (open-ended)
        self.assertTrue(is_patch_version_compatible(installer, '24.03.0.000'))
        self.assertTrue(is_patch_version_compatible(installer, '99.99.9999.9999'))

    def test_version_comparison_with_different_part_counts(self):
        """Test version comparison when versions have different number of parts."""
        installer = self.create_installer(ranges=[
            {'start': '5.0', 'limit': '5.0.99.9999'}
        ])

        # These should all be treated consistently
        self.assertTrue(is_patch_version_compatible(installer, '5.0'))
        self.assertTrue(is_patch_version_compatible(installer, '5.0.0'))
        self.assertTrue(is_patch_version_compatible(installer, '5.0.0.0'))
        self.assertTrue(is_patch_version_compatible(installer, '5.0.50'))
        self.assertTrue(is_patch_version_compatible(installer, '5.0.50.1234'))
        self.assertTrue(is_patch_version_compatible(installer, '5.0.99.9999'))

        # These should be outside the range
        self.assertFalse(is_patch_version_compatible(installer, '4.9.99.9999'))
        self.assertFalse(is_patch_version_compatible(installer, '5.0.100.0'))
        self.assertFalse(is_patch_version_compatible(installer, '5.1.0.0'))
