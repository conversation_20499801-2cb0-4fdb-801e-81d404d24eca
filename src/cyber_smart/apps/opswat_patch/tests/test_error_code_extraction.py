import json
from unittest import TestCase
from opswat_patch.serializers import extract_error_code_from_json_details


class TestErrorCodeExtraction(TestCase):
    def test_extract_code_from_simple_structure(self):
        """Test extraction from a simple JSON structure."""
        details = json.dumps({"code": -1026, "description": "Product not supported"})
        result = extract_error_code_from_json_details(details)
        self.assertEqual(result, "-1026")

    def test_extract_code_from_nested_structure(self):
        """Test extraction from nested error structure."""
        details = json.dumps({
            "errors": [
                {"code": -1026, "message": "error"},
                {"code": -1026}
            ],
            "code": -1026,
            "description": "Product not supported"
        })
        # Should get the first occurrence
        result = extract_error_code_from_json_details(details)
        self.assertEqual(result, "-1026")

    def test_extract_code_from_deeply_nested_structure(self):
        """Test extraction from deeply nested structure."""
        details = json.dumps({
            "errors": [{
                "code": -33,
                "errors": [{
                    "code": -33,
                    "errors": [{"code": -33}]
                }]
            }],
            "code": -33,
            "description": "Crypto error"
        })
        # Should get the first occurrence
        result = extract_error_code_from_json_details(details)
        self.assertEqual(result, "-33")

    def test_extract_code_from_wrapped_error_structure(self):
        """Test extraction from structure wrapped in 'error' object."""
        details = json.dumps({
            "error": {
                "errors": [{"code": -1026}],
                "code": -1026,
                "description": "Product not supported"
            }
        })
        result = extract_error_code_from_json_details(details)
        self.assertEqual(result, "-1026")

    def test_return_none_for_invalid_json(self):
        """Test that None is returned for invalid JSON."""
        result = extract_error_code_from_json_details("Invalid JSON string")
        self.assertIsNone(result)

    def test_return_none_for_missing_code(self):
        """Test that None is returned when no code field exists."""
        details = json.dumps({"errors": [], "description": "No code here"})
        result = extract_error_code_from_json_details(details)
        self.assertIsNone(result)

    def test_return_none_for_non_numeric_code(self):
        """Test that None is returned for non-numeric code."""
        details = json.dumps({"code": "not_a_number"})
        result = extract_error_code_from_json_details(details)
        self.assertIsNone(result)

    def test_return_none_for_too_long_code(self):
        """Test that None is returned for codes longer than 20 characters."""
        details = json.dumps({"code": -123456789012345678901})
        result = extract_error_code_from_json_details(details)
        self.assertIsNone(result)

    def test_return_none_for_empty_string(self):
        """Test that None is returned for empty string."""
        result = extract_error_code_from_json_details("")
        self.assertIsNone(result)

    def test_return_none_for_none_input(self):
        """Test that None is returned for None input."""
        result = extract_error_code_from_json_details(None)
        self.assertIsNone(result)

    def test_extract_positive_error_code(self):
        """Test extraction of positive error codes."""
        details = json.dumps({"code": 1234})
        result = extract_error_code_from_json_details(details)
        self.assertEqual(result, "1234")

    def test_real_world_example_with_hashed_data(self):
        """Test with real-world example containing hashed/encrypted data."""
        details = json.dumps({
            "errors": [{
                "returned_at": "8",
                "message": "d",
                "code": -1026
            }],
            "method": 50300,
            "code": -1026,
            "description": "Defines an error when a product is not supported.",
            "timing": 391,
            "timestamp": "1753105193",
            "signature": 477,
            "define": "WA_VMOD_ERROR_PRODUCT_NOT_SUPPORTED"
        })
        result = extract_error_code_from_json_details(details)
        self.assertEqual(result, "-1026")
