

from unittest.mock import patch

from django.test import TestCase
from django.utils import timezone

from appusers.models.factories import AppInstallFactory, AppUserFactory, AppVersionFactory
from opswat_patch.factories import (
    OpswatScheduledProductInstallerFactory, OpswatPatchEventLogFactory, OpswatPatchInstallerFactory,
    OpswatProductPatchInstallerFactory, OpswatPatchAttemptFactory
)
from opswat_patch.models.constants import PENDING, IN_PROGRESS, COMPLETE, RETRYING_USER_DISMISSED, ERROR, MAX_RETRY_ATTEMPTS, SCHEDULED
from opswat_patch.models import OpswatPatchInstaller, OpswatProductPatchInstaller


class OpswatPatchEventLogTest(TestCase):
    """Tests for the OpswatPatchEventLog model."""

    def test_installer_status_updates_with_event_log(self):
        """Test that OpswatScheduledProductInstaller status is updated when creating an OpswatPatchEventLog."""
        # Create a scheduled installer
        installer = OpswatScheduledProductInstallerFactory()
        OpswatPatchAttemptFactory(
            scheduled_product_installer=installer
        )
        installer_modified_time = installer.modified
        self.assertEqual(installer.status, PENDING)  # Default status

        # Create an event log for the installer
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=installer,
            status=IN_PROGRESS
        )

        # Refresh from database to get updated status
        installer.refresh_from_db()
        self.assertEqual(installer.status, IN_PROGRESS)
        # Check that modified timestamp was updated
        self.assertGreater(installer.modified, installer_modified_time)

        # Store the current modified time for next comparison
        installer_modified_time = installer.modified

        # Create another event log with a different status
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=installer,
            status=COMPLETE
        )

        # Refresh from database again
        installer.refresh_from_db()
        self.assertEqual(installer.status, COMPLETE)
        # Check that modified timestamp was updated again
        self.assertGreater(installer.modified, installer_modified_time)

    def test_retry_count_increment_on_retrying_status(self):
        """Test that retry_count increments when RETRYING_USER_DISMISSED status is set."""
        installer = OpswatScheduledProductInstallerFactory()
        self.assertEqual(installer.retry_count, 0)

        # First retry
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=installer,
            status=RETRYING_USER_DISMISSED
        )

        installer.refresh_from_db()
        self.assertEqual(installer.retry_count, 1)
        self.assertEqual(installer.status, RETRYING_USER_DISMISSED)

        # Second retry
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=installer,
            status=RETRYING_USER_DISMISSED
        )

        installer.refresh_from_db()
        self.assertEqual(installer.retry_count, 2)
        self.assertEqual(installer.status, RETRYING_USER_DISMISSED)

    def test_retry_count_reset_on_non_retrying_status(self):
        """Test that retry_count resets only when transitioning to terminal status."""
        installer = OpswatScheduledProductInstallerFactory()

        # Set some retry attempts
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=installer,
            status=RETRYING_USER_DISMISSED
        )
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=installer,
            status=RETRYING_USER_DISMISSED
        )

        installer.refresh_from_db()
        self.assertEqual(installer.retry_count, 2)

        # Transition to a non-terminal status - count should NOT reset
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=installer,
            status=IN_PROGRESS
        )

        installer.refresh_from_db()
        self.assertEqual(installer.retry_count, 2)  # Should not reset
        self.assertEqual(installer.status, IN_PROGRESS)

        # Transition to terminal status - count should reset
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=installer,
            status=COMPLETE
        )

        installer.refresh_from_db()
        self.assertEqual(installer.retry_count, 0)  # Should reset now
        self.assertEqual(installer.status, COMPLETE)

    def test_auto_error_after_three_retries(self):
        """Test that installer automatically transitions to ERROR after 3 user-dismissed retries."""
        installer = OpswatScheduledProductInstallerFactory()

        # First three retries should all succeed
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=installer,
            status=RETRYING_USER_DISMISSED
        )
        installer.refresh_from_db()
        self.assertEqual(installer.retry_count, 1)
        self.assertEqual(installer.status, RETRYING_USER_DISMISSED)

        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=installer,
            status=RETRYING_USER_DISMISSED
        )
        installer.refresh_from_db()
        self.assertEqual(installer.retry_count, 2)
        self.assertEqual(installer.status, RETRYING_USER_DISMISSED)

        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=installer,
            status=RETRYING_USER_DISMISSED
        )
        installer.refresh_from_db()
        self.assertEqual(installer.retry_count, 3)
        self.assertEqual(installer.status, RETRYING_USER_DISMISSED)

        # Fourth retry should trigger auto-transition to ERROR
        OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=installer,
            status=RETRYING_USER_DISMISSED
        )

        installer.refresh_from_db()
        self.assertEqual(installer.retry_count, 4)
        self.assertEqual(installer.status, ERROR)

        # Check that an additional ERROR event log was created
        error_logs = installer.event_logs.filter(status=ERROR)
        self.assertEqual(error_logs.count(), 1)
        self.assertIn(f"Automatically transitioned to error after {MAX_RETRY_ATTEMPTS} user-dismissed retry attempts",
                     error_logs.first().details)

    def test_get_app_install_version_at_event_time(self):
        """Test that we can retrieve the app install version at the time of event creation."""
        # Create test data
        app_user = AppUserFactory()
        app_version = AppVersionFactory(major=8, minor=5, patch=0)
        app_install = AppInstallFactory(
            app_user=app_user,
            version=app_version,
            app_version="8.5.0"
        )
        scheduled_installer = OpswatScheduledProductInstallerFactory(app_install=app_install)

        # Create an event log
        event_log = OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=scheduled_installer,
            status=SCHEDULED
        )

        # Test the method returns the correct version
        version_at_event_time = event_log.get_app_install_version_at_event_time()
        self.assertEqual(version_at_event_time, "8.5.0")

        # Now update the app install version
        new_app_version = AppVersionFactory(major=8, minor=6, patch=0)
        app_install.version = new_app_version
        app_install.app_version = "8.6.0"
        app_install.save()

        # Create another event log
        new_event_log = OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=scheduled_installer,
            status=IN_PROGRESS
        )

        # The first event should still return the old version
        self.assertEqual(event_log.get_app_install_version_at_event_time(), "8.5.0")

        # The new event should return the new version
        self.assertEqual(new_event_log.get_app_install_version_at_event_time(), "8.6.0")

    def test_get_app_install_version_at_event_time_no_history(self):
        """Test that the method returns None when no historical record found."""
        # Create test data
        app_user = AppUserFactory()
        app_install = AppInstallFactory(app_user=app_user)
        scheduled_installer = OpswatScheduledProductInstallerFactory(app_install=app_install)

        # Create an event log
        event_log = OpswatPatchEventLogFactory(
            opswat_scheduled_product_installer=scheduled_installer,
            status=SCHEDULED
        )

        # Mock the history queryset to return empty
        mock_history = patch.object(app_install, 'history')
        with mock_history as mock_hist:
            mock_hist.filter.return_value.order_by.return_value.first.return_value = None

            # Should return None when no historical record found
            version_at_event_time = event_log.get_app_install_version_at_event_time()
            self.assertIsNone(version_at_event_time)


class OpswatPatchInstallerManagerTest(TestCase):
    """Tests for the OpswatPatchInstaller manager and disabled functionality."""

    def test_default_date_disabled_value(self):
        """Test that new OpswatPatchInstaller instances are active by default (date_disabled=None)."""
        installer = OpswatPatchInstallerFactory()
        self.assertIsNone(installer.date_disabled)  # NULL = active

    def test_active_manager_filters_disabled_installers(self):
        """Test that the active() manager method filters out disabled installers."""
        # Create active installer (date_disabled=None)
        active_installer = OpswatPatchInstallerFactory(date_disabled=None)

        # Create disabled installer (date_disabled=NOT NULL)
        disabled_installer = OpswatPatchInstallerFactory(
            date_disabled=timezone.now()
        )

        # Test all() includes both
        all_installers = OpswatPatchInstaller.objects.all()
        self.assertEqual(all_installers.count(), 2)
        self.assertIn(active_installer, all_installers)
        self.assertIn(disabled_installer, all_installers)

        # Test active() only includes active installer
        active_installers = OpswatPatchInstaller.objects.active()
        self.assertEqual(active_installers.count(), 1)
        self.assertIn(active_installer, active_installers)
        self.assertNotIn(disabled_installer, active_installers)


class OpswatProductPatchInstallerManagerTest(TestCase):
    """Tests for the OpswatProductPatchInstaller manager and active filtering."""

    def test_active_manager_filters_by_disabled_status(self):
        """Test that the active() manager method filters by both self.date_disabled and patch_installer.date_disabled."""
        # Create active patch installer and active product patch installer
        active_patch_installer = OpswatPatchInstallerFactory(date_disabled=None)
        active_product_patch_installer = OpswatProductPatchInstallerFactory(
            patch_installer=active_patch_installer,
            date_disabled=None
        )

        # Create disabled patch installer (should make product patch installer inactive)
        disabled_patch_installer = OpswatPatchInstallerFactory(
            date_disabled=timezone.now()
        )
        disabled_via_patch_installer = OpswatProductPatchInstallerFactory(
            patch_installer=disabled_patch_installer,
            date_disabled=None  # This one is active but its patch installer is disabled
        )

        # Create disabled product patch installer (with active patch installer)
        active_patch_installer_2 = OpswatPatchInstallerFactory(date_disabled=None)
        disabled_product_patch_installer = OpswatProductPatchInstallerFactory(
            patch_installer=active_patch_installer_2,
            date_disabled=timezone.now()  # This one is directly disabled
        )

        # Test all() includes all three
        all_product_installers = OpswatProductPatchInstaller.objects.all()
        self.assertEqual(all_product_installers.count(), 3)
        self.assertIn(active_product_patch_installer, all_product_installers)
        self.assertIn(disabled_via_patch_installer, all_product_installers)
        self.assertIn(disabled_product_patch_installer, all_product_installers)

        # Test active() only includes the fully active one
        active_product_installers = OpswatProductPatchInstaller.objects.active()
        self.assertEqual(active_product_installers.count(), 1)
        self.assertIn(active_product_patch_installer, active_product_installers)
        self.assertNotIn(disabled_via_patch_installer, active_product_installers)  # Disabled via patch installer
        self.assertNotIn(disabled_product_patch_installer, active_product_installers)  # Directly disabled
