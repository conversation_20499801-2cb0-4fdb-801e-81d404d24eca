from django.test import TestCase
from django.utils.translation import gettext_lazy as _

from opswat_patch.models.error_codes import get_error_message, WAAPI_ERROR_ACCESS_DENIED, UNKNOWN_ERROR, ERROR_CODE_MESSAGES, INTERNAL_TIMEOUT_ERROR_CODE
from opswat_patch.factories import OpswatPatchEventLogFactory


class ErrorCodeTests(TestCase):
    """Tests for the error code mapping functionality."""
    def test_get_error_message(self):
        """Test that get_error_message returns the correct message for a given error code."""
        # Test with a known error code
        error_code = str(WAAPI_ERROR_ACCESS_DENIED)  # -22
        expected_message = _("Full Disk Access required for patching.")
        self.assertEqual(get_error_message(error_code), expected_message)

        # Test with an unknown error code that is not -9999
        unknown_error_code = "-8888"
        expected_unknown_message = _("An error occurred during the operation.")
        self.assertEqual(get_error_message(unknown_error_code), expected_unknown_message)

        # Test with -9999 error code (should return a default message)
        self.assertEqual(get_error_message("-9999"), ERROR_CODE_MESSAGES[UNKNOWN_ERROR])

        # Test with an empty error code
        self.assertEqual(get_error_message(""), _("An error occurred during the operation."))

        # Test internal timeout error
        self.assertEqual(
            get_error_message(INTERNAL_TIMEOUT_ERROR_CODE), ERROR_CODE_MESSAGES[INTERNAL_TIMEOUT_ERROR_CODE]
        )

    def test_event_log_get_error_message(self):
        """Test that OpswatPatchEventLog.get_error_message returns the correct message."""
        # Create an event log with a known error code
        event_log = OpswatPatchEventLogFactory(error_code=str(WAAPI_ERROR_ACCESS_DENIED))
        expected_message = _("Full Disk Access required for patching.")
        self.assertEqual(event_log.get_error_message(), expected_message)

        # Create an event log with an unknown error code that is not -9999
        event_log = OpswatPatchEventLogFactory(error_code="-8888")
        expected_unknown_message = _("An error occurred during the operation.")
        self.assertEqual(event_log.get_error_message(), expected_unknown_message)

        # Create an event log with -9999 error code (should return a default message)
        event_log = OpswatPatchEventLogFactory(error_code="-9999")
        self.assertEqual(event_log.get_error_message(), ERROR_CODE_MESSAGES[UNKNOWN_ERROR])

        # Create an event log with no error code
        event_log = OpswatPatchEventLogFactory(error_code="")
        self.assertEqual(event_log.get_error_message(), "")
