from datetime import timedelta

from django.test import TestCase
from django.utils import timezone

from opswat_patch.factories import OpswatScheduledProductInstallerFactory, OpswatPatchAttemptFactory
from opswat_patch.models import OpswatScheduledProductInstaller
from opswat_patch.models.event_log import OpswatPatchEventLog
from opswat_patch.models.constants import (
    PENDING, IN_PROGRESS, COMPLETE, TIMED_OUT, ERROR, RETRYING_USER_DISMISSED
)
from opswat_patch.tasks.timeout_scheduled_installers import timeout_scheduled_installers
from common.models import ProjectSettings

class TimeoutScheduledInstallersTaskTest(TestCase):
    def setUp(self):
        self.old_time = timezone.now() - timedelta(days=8)
        self.recent_time = timezone.now() - timedelta(days=2)
        # Set expiry to 7 days (default) in minutes
        ProjectSettings.objects.update(scheduled_installer_expiry_minutes=None)

    def create_installer(self, status, created_time):
        installer = OpswatScheduledProductInstallerFactory(status=status)
        OpswatPatchAttemptFactory(scheduled_product_installer=installer)
        OpswatScheduledProductInstaller.objects.filter(pk=installer.pk).update(created=created_time)
        return installer.pk

    def test_timeout_scheduled_installers_default(self):
        # Old, non-terminal
        old_pending = self.create_installer(PENDING, self.old_time)
        old_in_progress = self.create_installer(IN_PROGRESS, self.old_time)
        # Old, terminal
        old_complete = self.create_installer(COMPLETE, self.old_time)
        old_error = self.create_installer(ERROR, self.old_time)
        # Recent, non-terminal
        recent_pending = self.create_installer(PENDING, self.recent_time)
        # Recent, terminal
        recent_complete = self.create_installer(COMPLETE, self.recent_time)

        timeout_scheduled_installers()

        # Refresh from DB
        objs = OpswatScheduledProductInstaller.objects.in_bulk([
            old_pending, old_in_progress, old_complete, old_error, recent_pending, recent_complete
        ])
        self.assertEqual(objs[old_pending].status, TIMED_OUT)
        self.assertEqual(objs[old_in_progress].status, TIMED_OUT)
        self.assertEqual(objs[old_complete].status, COMPLETE)
        self.assertEqual(objs[old_error].status, ERROR)
        self.assertEqual(objs[recent_pending].status, PENDING)
        self.assertEqual(objs[recent_complete].status, COMPLETE)

    def test_timeout_scheduled_installers_custom_minutes(self):
        # Set expiry to 3 days in minutes
        ProjectSettings.objects.update(scheduled_installer_expiry_minutes=3 * 24 * 60)
        old_time = timezone.now() - timedelta(days=4)
        recent_time = timezone.now() - timedelta(days=2)
        old_pending = self.create_installer(PENDING, old_time)
        recent_pending = self.create_installer(PENDING, recent_time)

        timeout_scheduled_installers()

        objs = OpswatScheduledProductInstaller.objects.in_bulk([
            old_pending, recent_pending
        ])
        self.assertEqual(objs[old_pending].status, TIMED_OUT)
        self.assertEqual(objs[recent_pending].status, PENDING)

    def test_timeout_considers_last_pending_event_as_start_time(self):
        """
        Test that timeout considers the last PENDING event date as the start time,
        not the last terminal event date.
        This is the main bug fix - when a scheduled installer fails and is re-scheduled,
        the timeout should be based on when it was re-scheduled (PENDING status), not when it errored.
        """
        # Create an installer that was created 10 days ago (old)
        very_old_time = timezone.now() - timedelta(days=10)
        installer = OpswatScheduledProductInstallerFactory(status=PENDING)
        patch_attempt = OpswatPatchAttemptFactory(scheduled_product_installer=installer, created=very_old_time)
        OpswatScheduledProductInstaller.objects.filter(pk=installer.pk).update(created=very_old_time)

        # Create terminal event logs that are more recent (within the 7-day window)
        recent_terminal_time = timezone.now() - timedelta(days=2)

        # Simulate the scenario: installer fails recently
        OpswatPatchEventLog.objects.create(
            opswat_scheduled_product_installer=installer,
            patch_attempt=patch_attempt,
            status=ERROR,
            created=recent_terminal_time,
            modified=recent_terminal_time,
            details="Test error"
        )

        # Then it gets re-scheduled (this creates a PENDING event)
        OpswatPatchEventLog.objects.create(
            opswat_scheduled_product_installer=installer,
            patch_attempt=patch_attempt,
            status=PENDING,
            details="Re-scheduled after error"
        )

        # Run the timeout task
        timeout_scheduled_installers()

        # Refresh from DB
        installer.refresh_from_db()

        # The installer should NOT be timed out because the last PENDING event
        # was recent (just created), even though the installer was created 10 days ago
        # and had an ERROR event 2 days ago
        self.assertEqual(installer.status, PENDING)

    def test_timeout_with_old_pending_event_times_out(self):
        """
        Test that timeout works when the last PENDING event is old.
        Also verifies that timing out creates an event log entry with detailed debugging info.
        """
        # Create an installer that was created a long time ago
        very_old_time = timezone.now() - timedelta(days=15)
        installer = OpswatScheduledProductInstallerFactory(status=PENDING)
        patch_attempt = OpswatPatchAttemptFactory(scheduled_product_installer=installer, created=very_old_time)
        OpswatScheduledProductInstaller.objects.filter(pk=installer.pk).update(created=very_old_time)

        # Create a PENDING event that is old (beyond the 7-day window)
        old_pending_time = timezone.now() - timedelta(days=10)
        OpswatPatchEventLog.objects.create(
            opswat_scheduled_product_installer=installer,
            patch_attempt=patch_attempt,
            status=PENDING,
            created=old_pending_time,
            modified=old_pending_time,
            details="Old pending event - scheduled 10 days ago"
        )

        # Count event logs before timeout
        initial_event_count = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer=installer
        ).count()

        # Run the timeout task
        timeout_scheduled_installers()

        # Refresh from DB
        installer.refresh_from_db()

        # The installer SHOULD be timed out because the last PENDING event
        # was 10 days ago (beyond the 7-day threshold)
        self.assertEqual(installer.status, TIMED_OUT)

        # Verify that an event log was created for the timeout
        final_event_count = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer=installer
        ).count()
        self.assertEqual(final_event_count, initial_event_count + 1)

        # Check the timeout event log details
        timeout_event = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer=installer,
            status=TIMED_OUT
        ).first()

        self.assertIsNotNone(timeout_event)
        self.assertEqual(timeout_event.status, TIMED_OUT)
        self.assertIn("Timed out by scheduled task", timeout_event.details)
        self.assertIn("Reference date:", timeout_event.details)
        self.assertIn("Threshold:", timeout_event.details)
        self.assertIn("Expiry minutes:", timeout_event.details)
        # Could be any of the reference sources depending on the test
        self.assertTrue(
            "last PENDING event (scheduled)" in timeout_event.details or
            "last terminal event (fallback)" in timeout_event.details or
            "creation date (fallback)" in timeout_event.details
        )
        self.assertIn("Last scheduled status: pending", timeout_event.details)

    def test_bug_retrying_user_dismissed_should_not_timeout_prematurely(self):
        """
        Test the specific bug scenario from the issue:
        An installer created long ago but recently scheduled should NOT timeout
        even after getting 'Retrying - User Dismissed' events.
        """
        # Create an installer that was created more than 7 days ago
        old_time = timezone.now() - timedelta(days=10)
        installer = OpswatScheduledProductInstallerFactory(status=RETRYING_USER_DISMISSED)
        patch_attempt = OpswatPatchAttemptFactory(scheduled_product_installer=installer)
        OpswatScheduledProductInstaller.objects.filter(pk=installer.pk).update(created=old_time)

        # Simulate that it was scheduled recently (PENDING event)
        recent_time = timezone.now() - timedelta(hours=2)
        OpswatPatchEventLog.objects.create(
            opswat_scheduled_product_installer=installer,
            patch_attempt=patch_attempt,
            status=PENDING,
            created=recent_time,
            modified=recent_time,
            details="Recently scheduled"
        )

        # Then got a RETRYING_USER_DISMISSED event
        OpswatPatchEventLog.objects.create(
            opswat_scheduled_product_installer=installer,
            patch_attempt=patch_attempt,
            status=RETRYING_USER_DISMISSED,
            details="Retrying - User Dismissed"
        )

        # Run the timeout task
        timeout_scheduled_installers()

        # Refresh from DB
        installer.refresh_from_db()

        # The installer should NOT be timed out because even though it was created 10 days ago,
        # it was scheduled (PENDING) only 2 hours ago, which is within the timeout threshold
        self.assertEqual(installer.status, RETRYING_USER_DISMISSED)


    def test_fallback_to_terminal_event_when_no_pending_exists(self):
        """
        Test that when no PENDING event exists, the system falls back to using
        the last terminal event date for timeout calculation.
        This tests the backwards compatibility scenario where an installer
        transitioned from terminal to non-terminal state without a PENDING event.
        """
        # Create an installer that's currently IN_PROGRESS but has no PENDING events
        old_time = timezone.now() - timedelta(days=10)
        recent_time = timezone.now() - timedelta(days=2)

        installer = OpswatScheduledProductInstallerFactory(status=IN_PROGRESS)
        patch_attempt = OpswatPatchAttemptFactory(scheduled_product_installer=installer)
        OpswatScheduledProductInstaller.objects.filter(pk=installer.pk).update(created=old_time)

        # Simulate a scenario where:
        # 1. Installer had an ERROR state recently
        OpswatPatchEventLog.objects.create(
            opswat_scheduled_product_installer=installer,
            patch_attempt=patch_attempt,
            status=ERROR,
            created=recent_time,
            modified=recent_time,
            details="Recent error"
        )

        # 2. Then transitioned to IN_PROGRESS without a PENDING event
        # (this could happen in legacy systems or manual status changes)
        OpswatPatchEventLog.objects.create(
            opswat_scheduled_product_installer=installer,
            patch_attempt=patch_attempt,
            status=IN_PROGRESS,
            created=recent_time + timedelta(minutes=1),
            modified=recent_time + timedelta(minutes=1),
            details="Moved to in progress without PENDING"
        )

        # Run the timeout task
        timeout_scheduled_installers()

        # Refresh from DB
        installer.refresh_from_db()

        # The installer should NOT be timed out because the terminal event (ERROR)
        # was recent (2 days ago), providing backwards compatibility
        self.assertEqual(installer.status, IN_PROGRESS)

