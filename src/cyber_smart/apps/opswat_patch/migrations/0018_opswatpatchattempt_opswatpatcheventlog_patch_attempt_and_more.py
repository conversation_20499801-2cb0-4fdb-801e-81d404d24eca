# Generated by Django 5.1.11 on 2025-07-16 05:59

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import opswat_patch.mixins
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('opswat_patch', '0017_opswatpatchinstaller_date_disabled_and_more'),
        ('organisations', '0194_alter_organisation_created_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OpswatPatchAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False)),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False)),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False)),
                ('status', models.CharField(default='pending', max_length=50)),
                ('from_version', models.CharField(max_length=50)),
                ('to_version', models.CharField(max_length=50)),
                ('finished_at', models.DateTimeField(blank=True, null=True)),
                ('initiated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('scheduled_product_installer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='opswat_patch.opswatscheduledproductinstaller')),
            ],
            options={
                'verbose_name': 'OPSWAT Patch Attempt',
                'verbose_name_plural': 'OPSWAT Patch Attempts',
                'ordering': ('-created',),
            },
            bases=(models.Model, opswat_patch.mixins.StatusMixin),
        ),
        migrations.AddField(
            model_name='opswatpatcheventlog',
            name='patch_attempt',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='opswat_patch.opswatpatchattempt'),
        ),
        migrations.CreateModel(
            name='OpswatPatchJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False)),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False)),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False)),
                ('finished_at', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(default='in_progress', max_length=50)),
                ('initiated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('organisation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='organisations.organisation')),
            ],
            options={
                'verbose_name': 'OPSWAT Patch Job',
                'verbose_name_plural': 'OPSWAT Patch Jobs',
            },
        ),
        migrations.AddField(
            model_name='opswatpatchattempt',
            name='patch_job',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='opswat_patch.opswatpatchjob'),
        ),
        migrations.AddIndex(
            model_name='opswatpatchjob',
            index=models.Index(fields=['status'], name='opswat_patc_status_a8a598_idx'),
        ),
        migrations.AddIndex(
            model_name='opswatpatchjob',
            index=models.Index(fields=['organisation'], name='opswat_patc_organis_6b33b9_idx'),
        ),
        migrations.AddIndex(
            model_name='opswatpatchattempt',
            index=models.Index(fields=['status'], name='opswat_patc_status_024de0_idx'),
        ),
        migrations.AddIndex(
            model_name='opswatpatchattempt',
            index=models.Index(fields=['scheduled_product_installer'], name='opswat_patc_schedul_ee91a7_idx'),
        ),
    ]
