# Generated by Django 5.1.11 on 2025-07-31 16:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("opswat", "0015_alter_cve_created_alter_cve_modified_and_more"),
        (
            "opswat_patch",
            "0018_opswatpatchattempt_opswatpatcheventlog_patch_attempt_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="TroubleshootingDashboard",
            fields=[],
            options={
                "verbose_name": "Patch Troubleshooting Dashboard",
                "verbose_name_plural": "Patch Troubleshooting Dashboard",
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("opswat_patch.opswatscheduledproductinstaller",),
        ),
        migrations.AlterField(
            model_name="opswatscheduledproductinstaller",
            name="signature",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="opswat.productsignature",
            ),
        ),
    ]
