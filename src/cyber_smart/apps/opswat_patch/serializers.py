import logging
import re

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from rest_framework.exceptions import ValidationError

from api.common.serializers import DeviceAuthenticationSerializer
from opswat.models import Product, ProductSignature
from opswat_patch.models import OpswatScheduledProductInstaller, OpswatProductPatchInstaller, OpswatArchitecture, \
    OpswatPatchAttempt
from opswat_patch.models.patch_installer import OpswatPatchInstaller, OpswatPatchInstallerDownloadLink
from opswat_patch.models.event_log import OpswatPatchEventLog
from appusers.models import AppInstall, AppUser
from opswat_patch.models.constants import ACTION_STATUS_CHOICES, ERROR


logger = logging.getLogger(__name__)


def extract_error_code_from_json_details(details_str):
    """
    Extract error code from OPSWAT JSON details string.

    When CAP returns error code -9999 (UNKNOWN_ERROR), the actual error code
    from OPSWAT can sometimes be embedded in the JSON details field.
    This function tries to extracts it.
    """
    if not details_str:
        return None

    # Look for the first occurrence of "code": followed by a number
    # Since all error codes in the structure are the same, we can stop at the first one
    match = re.search(r'"code"\s*:\s*(-?\d+)', details_str)
    if not match:
        return None

    extracted_code = match.group(1)
    # Validate that the extracted code is reasonable length
    if len(extracted_code) <= 20:
        return extracted_code

    return None


class OpswatArchitectureSerializer(serializers.ModelSerializer):
    """Serializer for the OpswatArchitecture model."""
    class Meta:
        model = OpswatArchitecture
        fields = ('id', 'name')


class OpswatProductSerializer(serializers.ModelSerializer):
    """Basic serializer for the Product model."""
    class Meta:
        model = Product
        fields = ('opswat_id', 'name', 'vendor')
        ref_name = "OpswatPatchProduct"


class OpswatPatchInstallerDownloadLinkSerializer(serializers.ModelSerializer):
    """Serializer for the OpswatPatchInstallerDownloadLink model."""
    os_architecture = OpswatArchitectureSerializer(read_only=True)

    class Meta:
        model = OpswatPatchInstallerDownloadLink
        fields = ('id', 'architecture', 'language', 'link', 'os_ids', 'os_architecture')


class OpswatPatchInstallerDetailSerializer(serializers.ModelSerializer):
    download_links = OpswatPatchInstallerDownloadLinkSerializer(many=True, read_only=True)

    class Meta:
        model = OpswatPatchInstaller
        fields = [
            'id', 'product_name', 'vulnerabilities', 'release_note_link',
            'eula_link', 'latest_version', 'language_default', 'fresh_installable',
            'release_date', 'requires_reboot', 'requires_uninstall_first', 'schema_version',
            'download_links'
        ]


class OpswatProductPatchInstallerSerializer(serializers.ModelSerializer):
    product = OpswatProductSerializer(read_only=True)
    product_id = serializers.PrimaryKeyRelatedField(
        queryset=Product.objects.all(),
        write_only=True,
        source='product',
        help_text="ID of the Product (v4_pid)."
    )
    patch_installer = OpswatPatchInstallerDetailSerializer(read_only=True)
    # IMPORTANT: The active() filter below only affects write operations (creating new associations).
    # For read operations (when CAP fetches data), ALL patch installers including disabled ones
    # are serialized and sent. This is intentional - we prevent creating new schedules for
    # disabled installers via the dashboard, but existing schedules with disabled installers
    # MUST be sent to CAP so it can return the appropriate WA_VMOD_ERROR_PRODUCT_NOT_SUPPORTED error.
    patch_installer_id = serializers.PrimaryKeyRelatedField(
        queryset=OpswatPatchInstaller.objects.active(),
        write_only=True,
        source='patch_installer',
        help_text="ID of the OpswatPatchInstaller. Only active installers can be selected when creating new schedules."
    )
    os_allow = serializers.CharField(read_only=True)
    os_deny = serializers.CharField(read_only=True)

    class Meta:
        model = OpswatProductPatchInstaller
        fields = (
            'id', 'opswat_id',
            'product', 'product_id',
            'patch_installer', 'patch_installer_id',
            'os_allow', 'os_deny'
        )


class OpswatScheduledProductInstallerSerializer(serializers.ModelSerializer):
    opswat_product_patch_installer = OpswatProductPatchInstallerSerializer(read_only=True)
    opswat_product_patch_installer_id = serializers.PrimaryKeyRelatedField(
        queryset=OpswatProductPatchInstaller.objects.all(),
        write_only=True,
        source='opswat_product_patch_installer'
    )
    app_install = serializers.PrimaryKeyRelatedField(queryset=AppInstall.objects.all(), write_only=True)
    creator = serializers.HiddenField(default=serializers.CurrentUserDefault())
    status = serializers.CharField(read_only=True)
    signature = serializers.SlugRelatedField(
        queryset=ProductSignature.objects.all(),
        slug_field='opswat_id',
        required=False,
        allow_null=True,
        help_text="Signature ID (opswat_id). "
    )

    class Meta:
        model = OpswatScheduledProductInstaller
        fields = (
            "id",
            "opswat_product_patch_installer",
            "opswat_product_patch_installer_id",
            "app_install",
            "creator",
            "status",
            "signature"
        )
        read_only_fields = ("id", "creator", "status")

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        signature_obj = instance.signature
        if signature_obj and hasattr(signature_obj, 'opswat_id') and signature_obj.opswat_id is not None:
            try:
                ret['signature'] = int(signature_obj.opswat_id)
            except (ValueError, TypeError):
                ret['signature'] = signature_obj.opswat_id
        elif 'signature' in ret:
            ret['signature'] = None
        return ret


class OpswatPatchEventLogSerializer(serializers.ModelSerializer):
    """
    Serializer for the OpswatPatchEventLog model.
    Used primarily for creating new event logs via the API.
    """
    opswat_scheduled_product_installer = serializers.PrimaryKeyRelatedField(
        queryset=OpswatScheduledProductInstaller.objects.select_related(
            'opswat_product_patch_installer__product__vendor',
            'opswat_product_patch_installer__patch_installer',
            'app_install__app_user__organisation'
        ).all(),
        help_text=_("The ID of the scheduled product installer this event belongs to.")
    )
    device_id = serializers.CharField(write_only=True, help_text=_("The device ID for authentication."))
    serial_number = serializers.CharField(write_only=True, required=False, allow_blank=True, help_text=_("The device serial number for authentication."))
    error_message = serializers.SerializerMethodField(help_text=_("Human-readable error message for the error code."))

    class Meta:
        model = OpswatPatchEventLog
        fields = [
            'id',
            'opswat_scheduled_product_installer',
            'status',
            'error_code',
            'error_message',
            'details',
            'details_public_facing',
            'created',
            'modified',
            'device_id',
            'serial_number',
        ]
        read_only_fields = ['id', 'created', 'modified', 'error_message']

    def save(self, **kwargs):
        """
        Automatically injects the latest OpswatPatchAttempt for the given installer
        if not explicitly provided. This ensures the event log is associated with the
        correct patch attempt without requiring it from the CAP.
        """
        if "patch_attempt" not in kwargs or kwargs["patch_attempt"] is None:
            installer = self.validated_data.get("opswat_scheduled_product_installer")
            patch_attempt = OpswatPatchAttempt.objects.filter(
                scheduled_product_installer=installer
            ).order_by("-created").first()

            if patch_attempt:
                kwargs["patch_attempt"] = patch_attempt
            else:
                logger.error(
                    f"No patch_attempt found for installer ID {installer.id} — saving without patch_attempt"
                )
        return super().save(**kwargs)

    def get_error_message(self, obj):
        """Get the human-readable error message for the error code."""
        return obj.get_error_message()

    def validate_status(self, value):
        """Ensure the provided status is a valid choice."""
        valid_statuses = [choice[0] for choice in ACTION_STATUS_CHOICES]
        if value not in valid_statuses:
            raise serializers.ValidationError(_("Invalid status provided."))
        return value

    def validate(self, data):
        """Validate that error_code is provided if status is 'failed'."""
        if data.get('status') == ERROR and not data.get('error_code'):
            raise serializers.ValidationError("Error code is required when status is 'Failed'.")

        if data.get('error_code') == '-9999' and data.get('details'):
            extracted_code = extract_error_code_from_json_details(data['details'])

            if extracted_code:
                data['error_code'] = extracted_code

        return data


class OpswatDeviceAuthenticationSerializer(DeviceAuthenticationSerializer):
    """
    Extended DeviceAuthenticationSerializer that also accepts main_app_user UUIDs.
    If a main_app_user UUID is provided, it will find the app install within that organisation
    using the device_id and serial_number.
    This serializer is used in cases when the operation is happening on the app_install level,
    e.g. software patching on the app_install level.
    """

    def validate(self, attrs):
        """
        Validates the serializer fields and checks if the application exists.
        If the UUID belongs to a main_app_user, it will find the app install within that organisation.
        """
        uuid_field_name = self.get_uuid_field_name()

        if uuid_field_name not in attrs:
            raise ValidationError("App User UUID is required")

        uuid_value = attrs[uuid_field_name]
        self.app_user = AppUser.objects.select_related('organisation').filter(uuid=uuid_value).first()

        if not self.app_user:
            raise ValidationError("No App User found with the provided UUID")

        # Try to find the app install directly with the provided app_user
        self.app_install = AppInstall.objects.select_related('app_user', 'app_user__organisation').filter(
            app_user=self.app_user,
            device_id=attrs["device_id"],
            serial_number=attrs["serial_number"]
        ).first()

        # If no app install found and this is a main_app_user, try to find an app install
        # within the organisation with the same device_id and serial_number
        if not self.app_install and self.app_user.organisation.main_app_user == self.app_user:
            self.app_install = AppInstall.objects.select_related('app_user', 'app_user__organisation').filter(
                app_user__organisation=self.app_user.organisation,
                device_id=attrs["device_id"],
                serial_number=attrs["serial_number"]
            ).first()

        if not self.app_install:
            raise ValidationError("No AppInstall found matching the provided parameters")

        self.organisation = self.app_user.organisation
        return attrs
