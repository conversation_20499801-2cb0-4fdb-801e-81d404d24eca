{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block extrahead %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static 'admin/opswat_patch/css/troubleshooting.css' %}">
{% endblock %}

{% block title %}{{ title }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
  <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
  &rsaquo; <a href="{% url 'admin:app_list' app_label='opswat_patch' %}">{% trans 'OPSWAT Patch' %}</a>
  &rsaquo; <a href="{% url 'admin:opswat_patch_troubleshooting_dashboard' %}">{% trans 'Patch Troubleshooting Dashboard' %}</a>
  &rsaquo; {% trans 'Drill-Down View' %}
</div>
{% endblock %}

{% block content %}
<!-- Platform Filter Info -->
{% if platform_display %}
<div class="module platform-filter-info">
  <h3>Filtered by Platform: {{ platform_display }}</h3>
</div>
{% endif %}

<!-- Error Code Breakdown -->
{% if error_breakdown %}
<div class="module" style="margin-bottom: 20px;">
  <h2>{% trans "Error Code Breakdown" %}</h2>
  <table style="width: 100%;">
    <thead>
      <tr>
        <th>{% trans "Error Code" %}</th>
        <th>{% trans "Count" %}</th>
        <th>{% trans "First Seen" %}</th>
        <th>{% trans "Last Seen" %}</th>
      </tr>
    </thead>
    <tbody>
      {% for error in error_breakdown %}
      <tr>
        <td><strong>{{ error.error_code }}</strong></td>
        <td>{{ error.count }}</td>
        <td>{{ error.first_seen|date:"Y-m-d H:i" }}</td>
        <td>{{ error.last_seen|date:"Y-m-d H:i" }}</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endif %}

<!-- Detailed Records -->
<div class="module">
  <h2>{% trans "Recent Patch Attempts" %} ({% trans "Showing up to 100 records" %})</h2>
  <table id="result_list" style="width: 100%;">
    <thead>
      <tr>
        <th>{% trans "Created" %}</th>
        <th>{% trans "Device ID" %}</th>
        <th>{% trans "Platform" %}</th>
        <th>{% trans "Organization" %}</th>
        <th>{% trans "Status" %}</th>
        <th>{% trans "Retry Count" %}</th>
        <th>{% trans "Creator" %}</th>
        <th>{% trans "Latest Event Status" %}</th>
        <th>{% trans "Latest Event Details" %}</th>
        <th>{% trans "Actions" %}</th>
      </tr>
    </thead>
    <tbody>
      {% for record in records %}
      <tr class="{% cycle 'row1' 'row2' %}">
        <td>{{ record.created|date:"Y-m-d H:i" }}</td>
        <td>{{ record.app_install.device_id }}</td>
        <td>
          {% with platform=record.app_install.platform %}
            {% if platform %}
              {% if 'darwin' in platform|lower or 'mac' in platform|lower %}
                macOS
              {% elif 'win' in platform|lower %}
                Windows
              {% else %}
                {{ platform }}
              {% endif %}
            {% else %}
              -
            {% endif %}
          {% endwith %}
        </td>
        <td>{{ record.app_install.app_user.organisation.name|default:"-" }}</td>
        <td>
          <span style="color: {% if record.status == 'complete' %}green{% elif record.status == 'error' or record.status == 'timed_out' %}red{% else %}orange{% endif %};">
            {{ record.get_status_display }}
          </span>
        </td>
        <td>{{ record.retry_count }}</td>
        <td>{{ record.creator.email|default:record.creator.username }}</td>
        <td>
          {% with latest_event=record.event_logs.first %}
            {% if latest_event %}
              <span style="color: {% if latest_event.status == 'complete' %}green{% elif latest_event.status == 'error' or latest_event.status == 'timed_out' %}red{% else %}orange{% endif %};">
                {{ latest_event.get_status_display }}
              </span>
              {% if latest_event.error_code %}
                <br><small style="color: red;">Code: {{ latest_event.error_code }}</small>
              {% endif %}
            {% else %}
              -
            {% endif %}
          {% endwith %}
        </td>
        <td style="max-width: 300px; word-wrap: break-word;">
          {% with latest_event=record.event_logs.first %}
            {% if latest_event and latest_event.details %}
              <small>{{ latest_event.details|truncatewords:20 }}</small>
              {% if latest_event.created %}
                <br><small style="color: #666;">{{ latest_event.created|date:"Y-m-d H:i:s" }}</small>
              {% endif %}
            {% else %}
              -
            {% endif %}
          {% endwith %}
        </td>
        <td>
          <a href="{% url 'admin:opswat_patch_opswatscheduledproductinstaller_change' record.id %}" class="viewlink">
            {% trans "View" %}
          </a>
        </td>
      </tr>
      {% empty %}
      <tr>
        <td colspan="10" style="text-align: center; padding: 20px;">
          {% trans "No records found." %}
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>

<!-- Event Log Timeline -->
{% if records %}
<div class="module" style="margin-top: 20px;">
  <h2>{% trans "Event Timeline (Sample from Recent Records)" %}</h2>
  <div style="max-height: 400px; overflow-y: auto;">
    {% for record in records|slice:":10" %}
      {% for event in record.event_logs.all|slice:":5" %}
      <div style="border-left: 3px solid {% if event.status == 'error' %}red{% elif event.status == 'complete' %}green{% else %}orange{% endif %}; padding: 10px; margin: 10px 0;">
        <strong>{{ event.created|date:"Y-m-d H:i:s" }}</strong> - 
        Device: {{ record.app_install.device_id }} - 
        Status: <span style="color: {% if event.status == 'error' %}red{% elif event.status == 'complete' %}green{% else %}orange{% endif %};">{{ event.get_status_display }}</span>
        {% if event.error_code %}
          - Error: <code>{{ event.error_code }}</code>
        {% endif %}
        {% if event.details %}
          <br><small>{{ event.details }}</small>
        {% endif %}
      </div>
      {% endfor %}
    {% endfor %}
  </div>
</div>
{% endif %}

<div style="margin-top: 20px;">
  <a href="{% url 'admin:opswat_patch_troubleshooting_dashboard' %}" class="button">
    {% trans "Back to Dashboard" %}
  </a>
</div>

{% endblock %}