{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block extrahead %}
{{ block.super }}
<script type="text/javascript" src="{% static 'admin/js/calendar.js' %}"></script>
<link rel="stylesheet" type="text/css" href="{% static 'admin/css/widgets.css' %}">
<link rel="stylesheet" type="text/css" href="{% static 'admin/opswat_patch/css/troubleshooting.css' %}">
{% endblock %}

{% block title %}{{ title }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
  <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
  &rsaquo; <a href="{% url 'admin:app_list' app_label='opswat_patch' %}">{% trans 'OPSWAT Patch' %}</a>
  &rsaquo; {% trans 'Patch Troubleshooting Dashboard' %}
</div>
{% endblock %}

{% block content %}
<!-- Filters -->
<div class="module" style="margin-bottom: 20px;">
  <h2>{% trans "Filters" %}</h2>
  <form method="get" id="filter-form" style="padding: 10px;">
    <div style="margin-bottom: 10px;">
      <label for="date_from">{% trans "Date From:" %}</label>
      <input type="date" name="date_from" id="date_from" value="{{ current_filters.date_from }}" style="margin-left: 10px;" class="vDateField" onchange="document.getElementById('filter-form').submit()">
      
      <label for="date_to" style="margin-left: 20px;">{% trans "Date To:" %}</label>
      <input type="date" name="date_to" id="date_to" value="{{ current_filters.date_to }}" style="margin-left: 10px;" class="vDateField" onchange="document.getElementById('filter-form').submit()">
      
      <span style="margin-left: 20px;">
        <a href="#" onclick="setDateRange('today'); return false;" style="margin: 0 5px;">{% trans "Today" %}</a> |
        <a href="#" onclick="setDateRange('yesterday'); return false;" style="margin: 0 5px;">{% trans "Yesterday" %}</a> |
        <a href="#" onclick="setDateRange('3days'); return false;" style="margin: 0 5px;">{% trans "Last 3 days" %}</a> |
        <a href="#" onclick="setDateRange('week'); return false;" style="margin: 0 5px;">{% trans "Last 7 days" %}</a> |
        <a href="#" onclick="setDateRange('month'); return false;" style="margin: 0 5px;">{% trans "Last 30 days" %}</a> |
        <a href="#" onclick="setDateRange('60days'); return false;" style="margin: 0 5px;">{% trans "Last 60 days" %}</a> |
        <a href="#" onclick="setDateRange('90days'); return false;" style="margin: 0 5px;">{% trans "Last 90 days" %}</a>
      </span>
    </div>
    
    <div style="margin-bottom: 10px;">
      <label for="status">{% trans "Status:" %}</label>
      <select name="status" id="status" style="margin-left: 10px;" onchange="document.getElementById('filter-form').submit()">
        <option value="">{% trans "All" %}</option>
        {% for status in available_statuses %}
          <option value="{{ status.value }}" {% if current_filters.status == status.value %}selected{% endif %}>{{ status.display }}</option>
        {% endfor %}
      </select>
    </div>
    
    <div style="margin-bottom: 10px;">
      <label for="product">{% trans "Product:" %}</label>
      <select name="opswat_product_patch_installer__product__id__exact" id="product" style="margin-left: 10px;" onchange="document.getElementById('filter-form').submit()">
        <option value="">{% trans "All products" %}</option>
        {% for product in available_products %}
          <option value="{{ product.id }}" {% if current_filters.opswat_product_patch_installer__product__id__exact == product.id|stringformat:"s" %}selected{% endif %}>{{ product.name }}</option>
        {% endfor %}
      </select>
    </div>
    
    <div style="margin-bottom: 10px;">
      <label for="org">{% trans "Organisation:" %}</label>
      <select name="app_install__app_user__organisation__id__exact" id="org" style="margin-left: 10px;" onchange="document.getElementById('filter-form').submit()">
        <option value="">{% trans "All organisations" %}</option>
        {% for org in available_orgs %}
          <option value="{{ org.id }}" {% if current_filters.app_install__app_user__organisation__id__exact == org.id|stringformat:"s" %}selected{% endif %}>{{ org.name }}</option>
        {% endfor %}
      </select>
    </div>
    
    <a href="{% url 'admin:opswat_patch_troubleshooting_dashboard' %}" class="button" style="margin-left: 10px;">{% trans "Clear filters" %}</a>
  </form>
</div>

<!-- Overall Statistics -->
<div class="module" style="margin-bottom: 20px;">
  <h2>{% trans "Overall Statistics" %}</h2>
  <table style="width: 100%; border-collapse: collapse;">
    <tr>
      <td style="padding: 10px; border: 1px solid #ddd;">
        <strong>{% trans "Total Patches" %}:</strong> {{ overall_stats.total_patches|default:"0" }}
      </td>
      <td style="padding: 10px; border: 1px solid #ddd;">
        <strong>{% trans "Successful" %}:</strong> 
        <span class="status-success">{{ overall_stats.successful_patches|default:"0" }} ({{ overall_stats.success_rate|floatformat:1 }}%)</span>
      </td>
      <td style="padding: 10px; border: 1px solid #ddd;">
        <strong>{% trans "Failed (Error)" %}:</strong> 
        <span class="status-error">{{ overall_stats.failed_error_patches|default:"0" }} ({{ overall_stats.failure_rate|floatformat:1 }}%)</span>
      </td>
      <td style="padding: 10px; border: 1px solid #ddd;">
        <strong>{% trans "Failed (User)" %}:</strong> 
        <span class="status-warning">{{ overall_stats.failed_user_patches|default:"0" }} ({{ overall_stats.user_failure_rate|floatformat:1 }}%)</span>
      </td>
    </tr>
    <tr>
      <td style="padding: 10px; border: 1px solid #ddd;">
        <strong>{% trans "In Progress" %}:</strong> 
        <span class="status-info">{{ overall_stats.in_progress_patches|default:"0" }}</span>
      </td>
      <td style="padding: 10px; border: 1px solid #ddd;">
        <strong>{% trans "Unique Products" %}:</strong> {{ overall_stats.unique_products|default:"0" }}
      </td>
      <td style="padding: 10px; border: 1px solid #ddd;">
        <strong>{% trans "Unique Signatures" %}:</strong> {{ overall_stats.unique_signatures|default:"0" }}
      </td>
      <td style="padding: 10px; border: 1px solid #ddd;">
        <strong>{% trans "Unique Devices" %}:</strong> {{ overall_stats.unique_devices|default:"0" }}
      </td>
      <td style="padding: 10px; border: 1px solid #ddd;">
        <strong>{% trans "Unique Organizations" %}:</strong> {{ overall_stats.unique_orgs|default:"0" }}
      </td>
    </tr>
  </table>
</div>


<!-- Grouped Statistics Table -->
<div class="module">
  <h2>{% trans "Patch Statistics by Product and Signature" %}</h2>
  <table id="result_list" style="width: 100%;">
    <thead>
      <tr>
        <th>
          <a href="?{% for k, v in current_filters.items %}{% if k != 'sort' %}{{ k }}={{ v }}&{% endif %}{% endfor %}sort={% if current_sort == 'product' %}-{% endif %}product" style="color: #333; text-decoration: none;">
            {% trans "Product" %}
            {% if sort_key == 'product' %}
              {% if reverse_sort %}▼{% else %}▲{% endif %}
            {% endif %}
          </a>
        </th>
        <th>
          <a href="?{% for k, v in current_filters.items %}{% if k != 'sort' %}{{ k }}={{ v }}&{% endif %}{% endfor %}sort={% if current_sort == 'vendor' %}-{% endif %}vendor" style="color: #333; text-decoration: none;">
            {% trans "Vendor" %}
            {% if sort_key == 'vendor' %}
              {% if reverse_sort %}▼{% else %}▲{% endif %}
            {% endif %}
          </a>
        </th>
        <th>
          <a href="?{% for k, v in current_filters.items %}{% if k != 'sort' %}{{ k }}={{ v }}&{% endif %}{% endfor %}sort={% if current_sort == 'signature' %}-{% endif %}signature" style="color: #333; text-decoration: none;">
            {% trans "Signature" %}
            {% if sort_key == 'signature' %}
              {% if reverse_sort %}▼{% else %}▲{% endif %}
            {% endif %}
          </a>
        </th>
        <th>
          <a href="?{% for k, v in current_filters.items %}{% if k != 'sort' %}{{ k }}={{ v }}&{% endif %}{% endfor %}sort={% if current_sort == 'platform' %}-{% endif %}platform" style="color: #333; text-decoration: none;">
            {% trans "Platform" %}
            {% if sort_key == 'platform' %}
              {% if reverse_sort %}▼{% else %}▲{% endif %}
            {% endif %}
          </a>
        </th>
        <th>
          <a href="?{% for k, v in current_filters.items %}{% if k != 'sort' %}{{ k }}={{ v }}&{% endif %}{% endfor %}sort={% if current_sort == 'total_patches' %}-{% endif %}total_patches" style="color: #333; text-decoration: none;">
            {% trans "Total" %}
            {% if sort_key == 'total_patches' %}
              {% if reverse_sort %}▼{% else %}▲{% endif %}
            {% endif %}
          </a>
        </th>
        <th>
          <a href="?{% for k, v in current_filters.items %}{% if k != 'sort' %}{{ k }}={{ v }}&{% endif %}{% endfor %}sort={% if current_sort == 'successful_patches' %}-{% endif %}successful_patches" style="color: #333; text-decoration: none;">
            {% trans "Success" %}
            {% if sort_key == 'successful_patches' %}
              {% if reverse_sort %}▼{% else %}▲{% endif %}
            {% endif %}
          </a>
        </th>
        <th>
          <a href="?{% for k, v in current_filters.items %}{% if k != 'sort' %}{{ k }}={{ v }}&{% endif %}{% endfor %}sort={% if current_sort == 'failed_error_patches' %}-{% endif %}failed_error_patches" style="color: #333; text-decoration: none;">
            {% trans "Failed (Error)" %}
            {% if sort_key == 'failed_error_patches' %}
              {% if reverse_sort %}▼{% else %}▲{% endif %}
            {% endif %}
          </a>
        </th>
        <th>{% trans "Failed (User)" %}</th>
        <th>{% trans "In Progress" %}</th>
        <th>
          <a href="?{% for k, v in current_filters.items %}{% if k != 'sort' %}{{ k }}={{ v }}&{% endif %}{% endfor %}sort={% if current_sort == 'success_rate' %}-{% endif %}success_rate" style="color: #333; text-decoration: none;">
            {% trans "Success Rate" %}
            {% if sort_key == 'success_rate' %}
              {% if reverse_sort %}▼{% else %}▲{% endif %}
            {% endif %}
          </a>
        </th>
        <th>{% trans "Avg Retries" %}</th>
        <th>{% trans "Top Error Codes" %}</th>
        <th>{% trans "Actions" %}</th>
      </tr>
    </thead>
    <tbody>
      {% for stat in stats %}
      <tr class="{% cycle 'row1' 'row2' %}">
        <td>{{ stat.opswat_product_patch_installer__product__name }}</td>
        <td>{{ stat.opswat_product_patch_installer__product__vendor__name }}</td>
        <td>
          {% if stat.signature__name %}
            {{ stat.signature__name }}
            {% if stat.signature__opswat_id %}(ID: {{ stat.signature__opswat_id }}){% endif %}
          {% else %}
            -
          {% endif %}
        </td>
        <td>{{ stat.platform_display }}</td>
        <td>{{ stat.total_patches }}</td>
        <td class="status-success">{{ stat.successful_patches }}</td>
        <td class="status-error">{{ stat.failed_error_patches }}</td>
        <td class="status-warning">{{ stat.failed_user_patches }}</td>
        <td style="color: blue;">{{ stat.in_progress_patches }}</td>
        <td>
          <span style="color: {% if stat.success_rate < 70 %}red{% elif stat.success_rate < 90 %}orange{% else %}green{% endif %};">
            {{ stat.success_rate|floatformat:1 }}%
          </span>
        </td>
        <td>{{ stat.avg_retry_count|floatformat:1|default:"-" }}</td>
        <td>
          {% if stat.top_error_codes %}
            <ul style="margin: 0; padding-left: 20px;">
              {% for error in stat.top_error_codes|slice:":3" %}
                <li style="font-size: 0.9em;">{{ error.error_code }} ({{ error.count }})</li>
              {% endfor %}
            </ul>
          {% else %}
            -
          {% endif %}
        </td>
        <td>
          <a href="{% url 'admin:opswat_patch_troubleshooting_drill_down' %}?product_id={{ stat.opswat_product_patch_installer__product__id }}&signature_id={{ stat.signature__id|default:'None' }}&platform={{ stat.app_install__platform|urlencode }}&{{ request.GET.urlencode }}" 
             class="viewlink">{% trans "View Details" %}</a>
        </td>
      </tr>
      {% empty %}
      <tr>
        <td colspan="13" style="text-align: center; padding: 20px;">
          {% trans "No patch data available for the selected filters." %}
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>

<!-- High Failure Products -->
{% if insights.high_failure_products %}
<div class="module" style="margin-top: 20px;">
  <h2>{% trans "Products with High Failure Rates" %}</h2>
  <table style="width: 100%;">
    <thead>
      <tr>
        <th>{% trans "Product" %}</th>
        <th>{% trans "Vendor" %}</th>
        <th>{% trans "Signature" %}</th>
        <th>{% trans "Failure Rate" %}</th>
        <th>{% trans "Total Patches" %}</th>
      </tr>
    </thead>
    <tbody>
      {% for product in insights.high_failure_products %}
      <tr>
        <td>{{ product.product }}</td>
        <td>{{ product.vendor }}</td>
        <td>
          {% if product.signature %}
            {{ product.signature }}
            {% if product.signature_opswat_id %}(ID: {{ product.signature_opswat_id }}){% endif %}
          {% else %}
            -
          {% endif %}
        </td>
        <td style="color: red;">{{ product.failure_rate|floatformat:1 }}%</td>
        <td>{{ product.total_patches }}</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endif %}


<script>
function setDateRange(range) {
    var today = new Date();
    var dateFrom = document.getElementById('date_from');
    var dateTo = document.getElementById('date_to');
    
    // Format date as YYYY-MM-DD
    function formatDate(date) {
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        return year + '-' + month + '-' + day;
    }
    
    // Define day offsets for each range
    var dayOffsets = {
        'today': 0,
        'yesterday': 1,
        '3days': 3,
        'week': 7,
        'month': 30,
        '60days': 60,
        '90days': 90
    };
    
    // Special case for yesterday - both dates are the same
    if (range === 'yesterday') {
        var yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        dateFrom.value = dateTo.value = formatDate(yesterday);
    } else {
        // For all other ranges
        dateTo.value = formatDate(today);
        if (dayOffsets[range] !== undefined) {
            var startDate = new Date(today);
            startDate.setDate(startDate.getDate() - dayOffsets[range]);
            dateFrom.value = formatDate(startDate);
        }
    }
    
    // Automatically submit the form
    document.getElementById('filter-form').submit();
}
</script>
{% endblock %}