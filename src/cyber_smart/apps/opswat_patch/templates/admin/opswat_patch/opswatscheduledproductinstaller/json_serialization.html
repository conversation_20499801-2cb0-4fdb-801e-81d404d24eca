<h4>JSON API Representation</h4>

{% if json_data %}
<pre class="opswat-json-pre">{{ json_data }}</pre>

<script>
function copyToClipboard{{ obj_id }}() {
    const jsonText = document.querySelector('#json-content-{{ obj_id }}').textContent;
    navigator.clipboard.writeText(jsonText).then(() => {
        const btn = document.querySelector('#copy-btn-{{ obj_id }}');
        const originalText = btn.textContent;
        btn.textContent = 'Copied!';
        btn.style.backgroundColor = '#28a745';
        setTimeout(() => {
            btn.textContent = originalText;
            btn.style.backgroundColor = '#0078d4';
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy: ', err);
        alert('Failed to copy to clipboard');
    });
}
</script>

<pre id="json-content-{{ obj_id }}" style="display: none;">{{ json_data }}</pre>
<button id="copy-btn-{{ obj_id }}" type="button" onclick="copyToClipboard{{ obj_id }}()" class="opswat-copy-btn">Copy JSON to Clipboard</button>
{% else %}
<p style="color: red;">{{ error_message }}</p>
{% endif %}