{% load admin_urls %}

<div class="opswat-external-data">
    <h4>InstalledSoftwareAppInstallIndividual Records</h4>
    {% if detected_software %}
        <p>Found {{ detected_software_count }} record(s) for this product:</p>
        <ul class="opswat-record-list">
            {% for sw in detected_software %}
            <li class="opswat-record-item">
                <strong><a href="{% url 'admin:appusers_installedsoftwareappinstallindividual_change' sw.id %}" target="_blank">{{ sw.vendor }} - {{ sw.product }} v{{ sw.version }}</a></strong><br>
                <span class="opswat-meta-info">Source: {{ sw.get_source_display }}</span>
                {% if sw.is_vulnerable %}
                    | <span class="opswat-vulnerable">⚠ Vulnerable (CVE Count: {{ sw.cve_count }}, Severity: {{ sw.highest_severity }})</span>
                {% else %}
                    | <span class="opswat-not-vulnerable">✓ Not Vulnerable</span>
                {% endif %}
                {% if sw.signatures %}
                    <br><span class="opswat-meta-info">Signatures: 
                    {% for sig in sw.signatures|slice:":3" %}{{ sig }}{% if not forloop.last %}, {% endif %}{% endfor %}
                    {% if sw.signatures|length > 3 %}... (+{{ sw.signatures|length|add:"-3" }} more){% endif %}
                    </span>
                {% endif %}
                <br><span class="opswat-meta-info">Source ID: {{ sw.source_id }}</span>
            </li>
            {% endfor %}
            {% if detected_software_count > 10 %}
                <li><em>... and {{ detected_software_count|add:"-10" }} more records</em></li>
            {% endif %}
        </ul>
    {% else %}
        <p><em>No InstalledSoftwareAppInstallIndividual records found for this product</em></p>
    {% endif %}

    <h4>InstalledProduct Records</h4>
    {% if installed_products %}
        <p>Found {{ installed_products|length }} InstalledProduct record(s) for this app_install:</p>
        <ul class="opswat-record-list">
            {% for ip_data in installed_products_data %}
            <li class="opswat-record-item">
                <strong><a href="{% url 'admin:opswat_installedproduct_change' ip_data.id %}" target="_blank">InstalledProduct Record</a></strong>
                {% if ip_data.has_target_product %}
                    <span class="opswat-contains-target">✓ Contains target product</span>
                {% endif %}
                <br><span class="opswat-meta-info">Hash: {{ ip_data.hash }}</span>
                <br><span class="opswat-meta-info">Total Products: {{ ip_data.product_count }}</span>
                <br><span class="opswat-meta-info">Created: {{ ip_data.created|date:"Y-m-d H:i:s" }}</span>
                
                {% if ip_data.target_versions %}
                    <br><strong>Target Product Versions:</strong>
                    <ul class="opswat-product-versions">
                        {% for pv in ip_data.target_versions|slice:":3" %}
                        <li><a href="{% url 'admin:opswat_productversion_change' pv.id %}" target="_blank">{{ pv.product.vendor.name }} - {{ pv.product.name }} v{{ pv.raw_version }}</a> (Channel: {{ pv.version_channel }})</li>
                        {% endfor %}
                        {% if ip_data.target_versions|length > 3 %}
                        <li><em>... and {{ ip_data.target_versions|length|add:"-3" }} more versions</em></li>
                        {% endif %}
                    </ul>
                {% elif ip_data.sample_products %}
                    <br><span class="opswat-meta-info">Sample products in this record:</span>
                    <ul class="opswat-product-versions opswat-meta-info">
                        {% for pv in ip_data.sample_products|slice:":3" %}
                        <li>{{ pv.product.vendor.name }} - {{ pv.product.name }} v{{ pv.raw_version }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </li>
            {% endfor %}
        </ul>
    {% else %}
        <p><em>No InstalledProduct records found for this app_install</em></p>
    {% endif %}

    <h4>CyberSmart OPSWAT Database Sources</h4>
    <p><em>JSON data sources where OPSWAT information is extracted:</em></p>
    <ul class="opswat-external-links">
        <li><a href="https://cs-opswat-db-prod.cybersmart.co.uk/analog/server/patch_associations.json" target="_blank">Patch Associations JSON</a></li>
        <li><a href="https://cs-opswat-db-prod.cybersmart.co.uk/analog/server/products.json" target="_blank">Products JSON</a></li>
        <li><a href="https://cs-opswat-db-prod.cybersmart.co.uk/analog/server/patch_aggregation.json" target="_blank">Patch Aggregation JSON</a></li>
    </ul>
</div>