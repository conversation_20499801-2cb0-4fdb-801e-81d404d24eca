/* Common styles for OPSWAT Patch Troubleshooting */

.module {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
}

.module h2 {
    margin-top: 0;
    color: #333 !important;
    font-size: 18px;
    font-weight: bold;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    background: transparent !important;
}

#result_list {
    border: 1px solid #ddd;
    width: 100%;
}

#result_list th {
    background: #f0f0f0;
    padding: 10px;
    text-align: left;
    font-weight: bold;
}

#result_list td {
    padding: 8px 10px;
    border-top: 1px solid #eee;
}

.row1 {
    background: #fff;
}

.row2 {
    background: #f9f9f9;
}

.button {
    display: inline-block;
    padding: 10px 20px;
    background: #417690;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
}

.button:hover {
    background: #205067;
}

.button.default {
    background: #79aec8;
}

.button.default:hover {
    background: #609ab6;
}

/* Status color classes */
.status-success {
    color: green;
}

.status-error {
    color: red;
}

.status-warning {
    color: orange;
}

.status-info {
    color: blue;
}

/* Date range filter styles */
.date-filter-container {
    margin-bottom: 10px;
}

.date-filter-container label {
    margin-right: 10px;
}

.date-filter-container input[type="date"] {
    margin-right: 20px;
}

.date-quick-filters {
    margin-left: 20px;
}

.date-quick-filters a {
    margin: 0 5px;
    color: #417690;
    text-decoration: none;
}

.date-quick-filters a:hover {
    text-decoration: underline;
}

/* Platform filter info box */
.platform-filter-info {
    background: #f0f8ff !important;
    margin-bottom: 20px;
}

.platform-filter-info h3 {
    margin: 0;
    color: #333;
    font-size: 16px;
    font-weight: normal;
}

/* Sortable header styles */
#result_list th a {
    color: #333 !important;
    text-decoration: none !important;
    display: block;
    padding-right: 20px;
    position: relative;
}

#result_list th a:hover {
    text-decoration: underline !important;
    color: #205067 !important;
}