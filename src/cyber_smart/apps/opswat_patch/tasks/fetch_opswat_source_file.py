import json
import os
import tempfile
import zipfile
import shutil
from datetime import datetime
from typing import Dict, Any, Tu<PERSON>, Optional

import requests
from celery import shared_task as task, chain
from celery.utils.log import get_task_logger
from django.conf import settings
from django.core.files.storage import default_storage, storages
from storages.backends.s3 import S3Storage

from opswat_patch.models import OpswatPatchSourceFile
from opswat_patch.tasks.import_products import import_products
from opswat_patch.tasks.import_patch_installers import import_patch_installers
from opswat_patch.tasks.import_patch_associations import import_patch_associations
from opswat_patch.tasks.refresh_enrichment import refresh_enrichment_data

logger = get_task_logger('opswat_patch')


def get_opswat_token() -> str:
    """
    Get OPSWAT API token from settings.
    """
    return settings.OPSWAT_TOKEN


def download_opswat_zip_file(token: str) -> Tuple[str, str, bool]:
    """
    Download the latest OPSWAT patch data zip file to a temporary location.

    Args:
        token: OPSWAT API token.

    Returns:
        Tuple of (original_file_name, temp_file_path, is_temp_file).
        The caller is responsible for deleting the temporary file.
    """
    url = f"{settings.OPSWAT_URL_BASE}{token}"
    headers = {
        "Accept": "application/octet-stream"
    }

    logger.info(f"Downloading OPSWAT patch data from {url}")
    original_file_name = "analog.zip"  # Default name if not provided

    # Create a temporary file to store the download
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".zip")
    temp_file_path = temp_file.name
    is_temp_file = True # Flag to indicate if the file needs cleanup

    try:
        with requests.get(url, headers=headers, stream=True) as response:
            response.raise_for_status()  # Raise an exception for bad status codes

            content_disposition = response.headers.get('content-disposition')
            if content_disposition:
                parts = content_disposition.split('filename=')
                if len(parts) > 1:
                    original_file_name = parts[1].strip('"')

            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            last_percent = 0

            logger.info(f"Downloading to temporary file: {temp_file_path}")
            for chunk in response.iter_content(chunk_size=8192 * 4):
                if chunk:
                    temp_file.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = int((downloaded / total_size) * 100)
                        if percent >= last_percent + 10 or downloaded == total_size:
                            logger.info(f"Download progress: {percent}% ({downloaded}/{total_size} bytes)")
                            last_percent = percent

        temp_file.close() # Close the file handle before returning path
        logger.info(f"Downloaded OPSWAT patch data to {temp_file_path}")
        # Explicitly return 3 values
        return original_file_name, temp_file_path, is_temp_file

    except requests.exceptions.RequestException as e:
        temp_file.close()
        if is_temp_file and os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        raise Exception(f"Failed to download OPSWAT patch data: {e}")
    except Exception as e:
        temp_file.close()
        if is_temp_file and os.path.exists(temp_file_path):
             os.remove(temp_file_path)
        raise e


def read_local_zip_file(local_file_path: str) -> Tuple[str, str, bool]:
    """
    Validate and return the path to a local OPSWAT patch data zip file.

    Args:
        local_file_path: Path to the local zip file.

    Returns:
        Tuple of (file_name, file_path, is_temp_file=False).
    """
    logger.info(f"Using local OPSWAT patch data from {local_file_path}")

    if not os.path.exists(local_file_path):
        raise FileNotFoundError(f"Local file not found: {local_file_path}")
    if not os.path.isfile(local_file_path):
         raise IsADirectoryError(f"Local path is not a file: {local_file_path}")

    try:
        if not zipfile.is_zipfile(local_file_path):
            logger.warning(f"Local file {local_file_path} may not be a valid zip file.")

        file_name = os.path.basename(local_file_path)
        logger.info(f"Confirmed local OPSWAT patch data: {file_name}")
        # Explicitly return 3 values
        return file_name, local_file_path, False # Not a temp file
    except Exception as e:
        raise Exception(f"Failed to process local file {local_file_path}: {str(e)}")


def should_upload_file(file_path_in_zip: str) -> bool:
    """
    Determine if a file from within the zip should be uploaded.
    We use forward slashes here as zipfile uses them regardless of OS.

    Args:
        file_path_in_zip: The full path of the file within the zip archive.

    Returns:
        True if the file should be uploaded, False otherwise.
    """
    # Normalize path separators to forward slashes for consistency
    normalized_path = file_path_in_zip.replace(os.sep, '/')

    # Check if the file is directly inside the 'analog/' directory (excluding subdirs)
    is_in_analog_root = normalized_path.startswith('analog/') and '/' not in normalized_path[len('analog/'):]

    # Check other conditions
    return any([
        is_in_analog_root,
        normalized_path.startswith('analog/server/'),
        normalized_path.startswith('analog/client/') and '/docs/' not in normalized_path
    ])

def extract_and_upload_zip_file(file_name: str, zip_file_path: str, extract_prefix: str) -> str:
    """
    Extract selected files from a zip file path and upload them to both default storage and Cloudflare R2.

    Args:
        file_name: Original name of the zip file (used for logging/naming).
        zip_file_path: Path to the local zip file.
        extract_prefix: Prefix for the extracted files in default storage.

    Returns:
        Path prefix where files were extracted in default storage.
    """
    logger.info(f"Extracting selected files from zip at {zip_file_path}") # Clarified log

    try:
        r2_storage = get_cloudflare_r2_storage()
    except Exception as e:
        logger.error(f"Failed to get Cloudflare R2 storage: {str(e)}")
        r2_storage = None

    # Generate the base path prefix for extracted files in storage
    storage_extract_path_prefix = f"{extract_prefix}/{os.path.splitext(file_name)[0]}"
    uploaded_count = 0
    processed_count = 0

    try:
        # Open the zip file directly from the provided path
        with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
            members = zip_ref.infolist()
            total_members = len(members)
            logger.info(f"Found {total_members} members in {file_name}")

            for member_info in members:
                processed_count += 1
                # Skip directories based on ZipInfo attributes
                if member_info.is_dir():
                    continue

                relative_path = member_info.filename # Path inside the zip

                # Check if the file should be uploaded based on its path within the zip
                if not should_upload_file(relative_path):
                    continue

                try:
                    # Determine storage paths
                    storage_path = f"{storage_extract_path_prefix}/{relative_path}"
                    r2_path = relative_path

                    # Upload to default storage (streaming)
                    try:
                        with zip_ref.open(member_info) as member_file_default:
                            default_storage.save(storage_path, member_file_default)
                    except Exception as e:
                         logger.error(f"Failed to upload file {relative_path} to default storage: {str(e)}")
                         # Continue to attempt R2 upload even if default fails

                    # Upload to Cloudflare R2 if available (streaming)
                    if r2_storage:
                        try:
                            # Re-open the member file for the second upload to get a fresh stream
                            with zip_ref.open(member_info) as member_file_r2:
                                r2_storage.save(r2_path, member_file_r2)
                        except Exception as e:
                            logger.error(f"Failed to upload file {relative_path} to Cloudflare R2: {str(e)}")

                    uploaded_count += 1
                    # Log progress periodically
                    if uploaded_count % 50 == 0 or uploaded_count == 1:
                         logger.info(f"Processed {processed_count}/{total_members}, Attempted upload for {uploaded_count} filtered files...")

                except Exception as e:
                    logger.error(f"Error processing member {relative_path}: {str(e)}")

        logger.info(f"Finished processing zip. Processed {processed_count}/{total_members} members. Uploaded {uploaded_count} filtered files to storage prefix {storage_extract_path_prefix}")
        if r2_storage:
            logger.info("Cloudflare R2 upload attempted for filtered files.")

        return storage_extract_path_prefix

    except zipfile.BadZipFile as e:
        logger.error(f"Error opening or reading zip file {zip_file_path}: {e}")
        raise # Re-raise specific zip error
    except FileNotFoundError as e:
         logger.error(f"Zip file not found at path {zip_file_path}: {e}")
         raise # Re-raise file not found error
    except Exception as e:
        logger.error(f"An unexpected error occurred during extraction/upload from {zip_file_path}: {e}", exc_info=True)
        raise # Re-raise other exceptions


def get_cloudflare_r2_storage() -> S3Storage:
    """
    Get the Cloudflare R2 storage backend.

    Returns:
        An instance of the Cloudflare R2 storage backend.
    """
    return storages['cloudflare_r2']


def parse_header_file(storage_path: str) -> Dict[str, Dict[str, Any]]:
    """
    Parse the header file to get information about the patch files.
    The header.json file is included in the zip by default.

    Args:
        storage_path: Path to the extracted files in storage.

    Returns:
        Dictionary mapping file names to file information.
    """
    header_path = f"{storage_path}/analog/header.json"
    logger.info(f"Parsing header file {header_path}")

    if default_storage.exists(header_path):
        with default_storage.open(header_path, 'r') as f:
            header_data = json.load(f)
            files_info = {}

            if "files" in header_data['oesis'][1]:
                for file_name, file_contents in header_data['oesis'][1]["files"].items():
                    files_info[file_name] = file_contents

            logger.info(f"Found {len(files_info)} files in header")
            return files_info
    else:
        logger.error(f"Header file not found: {header_path}")
        return {}


def create_source_file(file_name: str) -> OpswatPatchSourceFile:
    """
    Create a new source file record.

    Args:
        file_name: Name of the source file.

    Returns:
        A new source file record
    """
    # Always create a new source file record
    source_file = OpswatPatchSourceFile.objects.create(
        file_name=file_name,
        extracted_path=""
    )

    logger.info(f"Created new source file record: {source_file.id}")
    return source_file


def process_patch_source_file() -> Dict[str, Any]:
    """
    Process a patch file from the latest source file.

    Creates a Celery chain that:
    1. Imports products
    2. Imports patch installers
    3. Imports patch associations

    Returns:
        Dictionary with processing statistics.
    """
    # For non-UK production environments, use direct R2 paths
    # as we download the zip files from OPSWAT and unpack them to R2, S3
    # only on the UK production environment there are OpswatPatchSourceFile records.
    if settings.IS_PROD and not settings.IS_UK_GEO:
        products_file_path = "analog/server/products.json"
        patch_file_path = "analog/server/patch_aggregation.json"
        patch_associations_file_path = "analog/server/patch_associations.json"
        source_file_id = None
    else:
        # Get the latest source file with an extracted path
        latest_source_file = OpswatPatchSourceFile.objects.filter(
            extracted_path__isnull=False,
            extracted_path__gt=""
        ).order_by('-created').first()

        if not latest_source_file:
            error_msg = "No valid source file found for processing"
            logger.error(error_msg)
            return {
                "processed": False,
                "error": error_msg
            }

        # Construct paths to the OPSWAT files in storage
        extract_path = latest_source_file.extracted_path
        # Path to products file
        products_file_path = f"{extract_path}/analog/server/products.json"
        # Path to patch aggregation file
        patch_file_path = f"{extract_path}/analog/server/patch_aggregation.json"
        # Path to patch associations file
        patch_associations_file_path = f"{extract_path}/analog/server/patch_associations.json"
        source_file_id = latest_source_file.id

    # Check if all required files exist
    for file_path, file_name in [
        (products_file_path, "products.json"),
        (patch_file_path, "patch_aggregation.json"),
        (patch_associations_file_path, "patch_associations.json")
    ]:
        if not default_storage.exists(file_path):
            error_msg = f"{file_name} not found at {file_path}"
            logger.error(error_msg)
            return {
                "processed": False,
                "error": error_msg
            }

    logger.info(f"Processing OPSWAT patch files from source file {source_file_id}" if source_file_id else "Processing OPSWAT patch files from R2")

    # Create a Celery chain to process the files in sequence
    # We use signatures with immutable=True to prevent passing results between tasks
    # Each task will run with only its own arguments

    chain_tasks = chain(
        import_products.si(products_file_path),
        import_patch_installers.si(patch_file_path),
        import_patch_associations.si(patch_associations_file_path),
        refresh_enrichment_data.si()
    )

    # Execute the chain
    logger.info("Starting Celery chain to import OPSWAT patch data")
    chain_tasks.apply_async()

    return {
        "processed": True,
        "source_file_id": source_file_id,
        "products_file_path": products_file_path,
        "patch_file_path": patch_file_path,
        "patch_associations_file_path": patch_associations_file_path
    }

@task
def fetch_opswat_source_file_task(local_zip_file: Optional[str] = None)-> None:
    """
    Fetch OPSWAT patch zip, extract it, save it, then call tasks that will process those files.

    Args:
        local_zip_file: Path to a local zip file to use instead of downloading from the server.

    """
    logger.info("Starting OPSWAT patch data fetch")

    if settings.IS_PROD and not settings.IS_UK_GEO:
        logger.info(
            "Skipping fetching data in non-UK production environment."
            "All non-UK production environments will be using the files uploaded by the UK production environment task."
        )
        process_patch_source_file()
        return

    file_path = None
    is_temp_file = False
    try:
        if local_zip_file:
            logger.info(f"Using local zip file: {local_zip_file}")
            file_name, file_path, is_temp_file = read_local_zip_file(local_file_path=local_zip_file)
        else:
            token = get_opswat_token()
            if not token:
                error_msg = "OPSWAT API token not found in settings"
                logger.error(error_msg)
                return

            file_name, file_path, is_temp_file = download_opswat_zip_file(token)

        # Archive the downloaded/provided zip file (stream copy)
        archive_timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        zip_storage_path = f"opswat_patch/zip_files/{archive_timestamp}_{file_name}"
        logger.info(f"Archiving zip file to {zip_storage_path}")
        try:
            # Use default_storage.open for writing to storage
            with open(file_path, 'rb') as source_f, default_storage.open(zip_storage_path, 'wb') as target_f:
                shutil.copyfileobj(source_f, target_f)
            logger.info(f"Successfully archived zip file to {zip_storage_path}")
        except Exception as e:
            logger.error(f"Failed to archive zip file {file_path} to {zip_storage_path}: {str(e)}")
            # Decide if we should continue or raise error

        # Always create a new source file record
        source_file = create_source_file(file_name)

        # Extract directly from the zip file path
        extract_prefix = f"opswat_patch/extracted_zip_files/{source_file.id}"
        # Pass the file_path, not the file content
        extracted_storage_path = extract_and_upload_zip_file(file_name, file_path, extract_prefix)

        # Update the source file with the extracted path prefix
        source_file.extracted_path = extracted_storage_path
        source_file.save(update_fields=["extracted_path"])

        logger.info(f"Updated source file {source_file.id} with extracted path prefix {extracted_storage_path}")

        # Trigger the processing chain for the newly extracted files
        process_patch_source_file()

    except Exception as e:
        logger.error(f"Failed to fetch and process OPSWAT source file: {str(e)}", exc_info=True)

    finally:
        # Clean up the temporary file if one was created
        if is_temp_file and file_path and os.path.exists(file_path):
            logger.info(f"Cleaning up temporary zip file: {file_path}")
            try:
                os.remove(file_path)
            except OSError as e:
                logger.error(f"Error removing temporary file {file_path}: {e}")
