from datetime import timedelta

from django.utils import timezone
from celery import shared_task as task
from celery.utils.log import get_task_logger

from opswat_patch.models import OpswatScheduledProductInstaller
from opswat_patch.models.event_log import OpswatPatchEventLog
from opswat_patch.models.constants import TERMINAL_STATUSES, TIMED_OUT, PENDING
from common.models import ProjectSettings

logger = get_task_logger(__name__)

@task
def timeout_scheduled_installers() -> None:
    """
    Set status to TIMED_OUT for all scheduled product installers not in terminal states and
    whose reference date is older than X days ago.

    Reference date priority:
    1. Last PENDING event (marks when installer was scheduled/re-scheduled)
    2. Last terminal event (for backwards compatibility)
    3. Installer creation date
    """
    minutes = ProjectSettings.get_scheduled_installer_expiry_minutes()
    threshold = timezone.now() - timedelta(minutes=minutes)

    # Get installers that are not in terminal states
    installers = OpswatScheduledProductInstaller.objects.exclude(status__in=TERMINAL_STATUSES)

    installers_to_timeout = []

    for installer in installers:
        # Get the latest PENDING event - this marks when the installer was scheduled/re-scheduled
        # PENDING is always set when scheduling or re-scheduling an installation, making it
        # a reliable marker for the start of an installation attempt
        latest_pending_event = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer=installer,
            status=PENDING
        ).order_by('-created').first()

        if latest_pending_event:
            # Use the PENDING event date as it marks when the installer was scheduled
            reference_date = latest_pending_event.created
        else:
            # Fall back to last terminal event if no PENDING event exists
            latest_terminal_event = OpswatPatchEventLog.objects.filter(
                opswat_scheduled_product_installer=installer,
                status__in=TERMINAL_STATUSES
            ).order_by('-created').first()

            if latest_terminal_event:
                # Use last terminal event date
                reference_date = latest_terminal_event.created
            else:
                # Final fallback to installer creation date
                reference_date = installer.created

        # Check if the reference date is older than the threshold
        if reference_date < threshold:
            installers_to_timeout.append(installer.id)

    if not installers_to_timeout:
        logger.info("No scheduled product installers to timeout.")
        return

    # Timeout the installers and create event logs
    count = 0
    for installer_id in installers_to_timeout:
        installer = OpswatScheduledProductInstaller.objects.get(id=installer_id)

        # Get reference date info for debugging
        latest_pending_event = OpswatPatchEventLog.objects.filter(
            opswat_scheduled_product_installer=installer,
            status=PENDING
        ).order_by('-created').first()

        if latest_pending_event:
            reference_date = latest_pending_event.created
            reference_source = "last PENDING event (scheduled)"
            last_status = "pending"
        else:
            # Fall back to last terminal event
            latest_terminal_event = OpswatPatchEventLog.objects.filter(
                opswat_scheduled_product_installer=installer,
                status__in=TERMINAL_STATUSES
            ).order_by('-created').first()

            if latest_terminal_event:
                reference_date = latest_terminal_event.created
                reference_source = "last terminal event (fallback)"
                last_status = latest_terminal_event.status
            else:
                reference_date = installer.created
                reference_source = "creation date (fallback)"
                last_status = "none"

        # Create event log for the timeout with detailed debugging info
        details = (
            f"Timed out by scheduled task at {timezone.now().strftime('%Y-%m-%d %H:%M:%S UTC')} "
            f"due to expiry. Reference date: {reference_date.strftime('%Y-%m-%d %H:%M:%S UTC')} "
            f"({reference_source}). Threshold: {threshold.strftime('%Y-%m-%d %H:%M:%S UTC')}. "
            f"Last scheduled status: {last_status}. Expiry minutes: {minutes}"
        )

        OpswatPatchEventLog.objects.create(
            opswat_scheduled_product_installer=installer,
            patch_attempt=installer.latest_attempt,
            status=TIMED_OUT,
            details=details
        )
        count += 1

    logger.info(f"Timed out {count} scheduled product installers.")
