from django.db import transaction
from celery import shared_task as task
from celery.utils.log import get_task_logger
from django.conf import settings

logger = get_task_logger('opswat_patch')


def refresh_all_enrichment_data():
    """
    Re-enrich all OPSWAT software records across all organizations.
    This should be called nightly after OPSWAT data updates.

    This function uses the existing enrichment methods that were added to the
    update_individual_record processes, making it simple and consistent.
    """
    from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
    from software_inventory.models import InstalledSoftwareOrganisationIndividual
    from organisations.models import Organisation
    from appusers.models import AppInstall

    # Get total count for progress tracking
    total_orgs = Organisation.objects.count()
    logger.info(f"Starting enrichment refresh for {total_orgs} organisations")

    processed_orgs = 0

    # Process each organisation
    for organisation in Organisation.objects.exclude(partner__name__in=[settings.CS_PARTNER_CANCELLED_DIRECT_CUSTOMER_NAME, settings.CS_PARTNER_CANCELLED_PARTNER_CUSTOMER_NAME]):
        try:
            with transaction.atomic():
                # Update organisation-level records
                org_records = list(InstalledSoftwareOrganisationIndividual.objects.filter(
                    organisation=organisation
                ))

                if org_records:
                    InstalledSoftwareOrganisationIndividual._populate_with_opswat_product_patch_installer(org_records)

                # Get all app_install_ids for this organisation
                app_install_ids = list(AppInstall.objects.filter(
                    app_user__organisation=organisation,
                ).values_list('id', flat=True))

                # Update app-install level records for all app_installs in this org
                for app_install_id in app_install_ids:
                    # Get existing records for this app_install
                    records = list(InstalledSoftwareAppInstallIndividual.objects.filter(
                        app_install_id=app_install_id
                    ))

                    if records:
                        InstalledSoftwareAppInstallIndividual._populate_with_opswat_product_patch_installer(records, app_install_id)

            processed_orgs += 1

            # Log progress periodically
            if processed_orgs % 10 == 0:
                logger.info(f"Progress: {processed_orgs}/{total_orgs} organisations processed")

        except Exception as e:
            logger.error(f"Failed to process organisation {organisation.id}: {str(e)}", exc_info=True)
            # Continue with next organisation even if one fails
            processed_orgs += 1
            continue

    logger.info(f"Completed enrichment refresh for {processed_orgs}/{total_orgs} organisations")


@task
def refresh_enrichment_data():
    """
    Refresh enrichment data for all OPSWAT software records after OPSWAT data updates.

    This task runs as part of the nightly OPSWAT data import chain to ensure
    all installed software records have up-to-date installer information.
    """
    logger.info("Starting enrichment data refresh for all OPSWAT software records")

    try:
        # Call the refresh function
        refresh_all_enrichment_data()

        logger.info("Successfully completed enrichment data refresh")
        return {"success": True, "message": "Enrichment data refreshed successfully"}

    except Exception as e:
        error_msg = f"Failed to refresh enrichment data: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return {"success": False, "error": error_msg}
