import logging

from dateutil.relativedelta import relativedelta
from django.urls import reverse
from django.utils.timezone import now
from mock import patch
from rest_framework import status

from organisations.models import Organisation, OrganisationCertification
from vodafone import models
from vodafone import products
from vodafone.tests import utils as test_utils
from vodafone.utils import get_or_create_user

logging.disable(logging.CRITICAL)


class TestAPI(test_utils.TestAPIBase):

    def test_vodafone_unauthorized_request(self):
        response = self.client.get(reverse('vodafone:handle_cert_webhook'))
        self.assertEqual(status.HTTP_401_UNAUTHORIZED, response.status_code)
        self.assertEqual({"success": False}, response.json())

    def test_happy_path_org_create(self):
        # Authorization header is added to simulate real requests
        with patch("vodafone.services.VodafoneServices.enrol_appusers_to_training") as enrol_appusers_to_training:
            response = self.client.get(f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_order_CE-Monitoring",
                                       **{'HTTP_AUTHORIZATION': self.vf_token})
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_content = response.json()
        self.assertTrue(response_content["success"])
        self.assertIsNotNone(response_content["accountIdentifier"])
        self.assertEqual(1, Organisation.objects.count())
        org = Organisation.objects.first()
        self.assertTrue(org.software_support)
        self.assertTrue(org.has_ce_certification)
        self.assertEqual(org.size, Organisation.ORGANISATION_SIZE_5_9)
        self.assertEqual(org.vodafone_organisation.vf_monitors_count, 5)
        self.assertTrue(org.settings.smart_score_enabled)
        self.assertTrue(org.learn_lite_tab_displayed)
        self.assertTrue(enrol_appusers_to_training.called)

    def test_vodafone_request_limited_to_GET(self):
        response = self.client.post(reverse('vodafone:handle_cert_webhook'))
        self.assertEqual(status.HTTP_405_METHOD_NOT_ALLOWED, response.status_code)

    def test_user_unsubscription(self):
        # Authorization header is added to simulate real requests
        org = Organisation.objects.create(partner=self.partner)
        admin_email = "<EMAIL>"
        user, created = get_or_create_user(email=admin_email, first_name="", last_name="")
        self.VF_services.associate_user_to_organisation(user, org)
        self.VF_services.create_application_users(organisation=org)
        self.assertEqual(1, org.admins.count())
        app_user = org.app_users.first()
        self.assertTrue(app_user.active)

        response = self.client.get(f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=user_unassignment",
                                   **{'HTTP_AUTHORIZATION': self.vf_token})
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual({"success": True, "accountIdentifier": org.secure_id}, response.json())
        self.assertEqual(0, org.admins.count())
        app_user.refresh_from_db()
        self.assertFalse(app_user.active)

    def test_purchase_CEP_successfully(self):
        cep_response = self.client.get(f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_order_CEP",
                                       **{'HTTP_AUTHORIZATION': self.vf_token})
        self.assertEqual(status.HTTP_200_OK, cep_response.status_code)
        self.assertEqual(Organisation.objects.count(), 1)
        org = Organisation.objects.first()
        self.assertTrue(org.has_cep_certification)
        self.assertTrue(org.settings.smart_score_enabled)

    def test_purchase_CEP_then_CEP_onsite_audit_successfully(self):
        self.client.get(f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_order_CEP",
                        **{'HTTP_AUTHORIZATION': self.vf_token})
        cep_onsite_audit_response = self.client.get(
            f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_order_CEP-OnSite-Audit",
            **{'HTTP_AUTHORIZATION': self.vf_token})
        subscription = models.VFSubscription.objects.get(edition_code=products.CEPlusOnSiteAudit.edition_code)
        self.assertEqual({"success": True, "accountIdentifier": str(subscription.uuid)},
                         cep_onsite_audit_response.json())
        self.assertEqual(models.CEPOnSiteAudit.objects.count(), 1)
        cep_onsite_audit = models.CEPOnSiteAudit.objects.first()
        self.assertEqual(cep_onsite_audit.total_audits_count, 5)

    def test_purchase_CEP_then_Monitoring_successfully(self):
        self.client.get(f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_order_CEP",
                        **{'HTTP_AUTHORIZATION': self.vf_token})
        org = Organisation.objects.first()
        self.assertFalse(org.learn_lite_tab_displayed)
        self.assertTrue(org.has_cep_certification)
        with patch("vodafone.services.VodafoneServices.enrol_appusers_to_training") as enrol_appusers_to_training:
            monitoring_response = self.client.get(
                f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_order_Monitoring",
                **{'HTTP_AUTHORIZATION': self.vf_token})
        subscription = models.VFSubscription.objects.get(edition_code=products.Monitoring.edition_code)
        self.assertEqual({"success": True, "accountIdentifier": str(subscription.uuid)},
                         monitoring_response.json())
        org.refresh_from_db()
        self.assertTrue(org.software_support)
        self.assertEqual(org.size, Organisation.ORGANISATION_SIZE_5_9)
        self.assertEqual(org.vodafone_organisation.vf_monitors_count, 5)
        self.assertTrue(org.learn_lite_tab_displayed)
        self.assertTrue(enrol_appusers_to_training.called)

    def test_purchase_CEP_after_being_CE_certified_successfully(self):
        """
        Given an org CE certified for less than 3 months
        When purchase CEP
        Then the CEP will be added successfully
        """
        self.client.get(f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_order_CE-Monitoring",
                        **{'HTTP_AUTHORIZATION': self.vf_token})
        org = Organisation.objects.first()
        certification_date = now() - relativedelta(months=3) + relativedelta(days=1)
        test_utils.update_or_create_organisation_certified_ce_certification(
            organisation=org, certification_date=certification_date)
        response = self.client.get(f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_order_CEP",
                                   **{'HTTP_AUTHORIZATION': self.vf_token})
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertTrue(response.json()["success"])
        self.assertTrue(org.has_cep_certification)

    def test_subscription_update_default_handling(self):
        # Authorization header is added to simulate real requests
        response = self.client.get(f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_change_CEP",
                                   **{'HTTP_AUTHORIZATION': self.vf_token})
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_content = response.json()
        self.assertFalse(response_content["success"])

    def test_update_CEP_onsite_audit_successfully(self):
        """
        Given an org CEP on-site audit
        When increase on-site audit quantity
        Then the CEP on-site audit total_audits_count will be updated successfully
        """
        org, vf_org = self.get_or_create_vf_organisation()
        cep_on_site_audit = models.CEPOnSiteAudit.objects.create(vf_organisation=vf_org, total_audits_count=0)
        self.create_vf_subscription(vf_org, edition_code=products.CEPlusOnSiteAudit.edition_code)
        response = self.client.get(f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_change_CEP-OnSite-Audit",
                                   **{'HTTP_AUTHORIZATION': self.vf_token})
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertTrue(response.json()["success"])
        cep_on_site_audit.refresh_from_db()
        self.assertTrue(cep_on_site_audit.total_audits_count > 0)

    def test_update_CE_Monitoring_successfully(self):
        """
        Given an org CE-Monitoring
        When increase CE-Monitoring quantity
        Then the CE-Monitoring vf_monitors_count will be updated successfully
        """
        self.url = f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_change_CE-Monitoring"
        self.monitoring_count_update(edition_code=products.CEMonitoring.edition_code)

    def test_update_Monitoring_successfully(self):
        """
        Given an org Monitoring
        When increase Monitoring quantity
        Then the Monitoring vf_monitors_count will be updated successfully
        """
        self.url = f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_change_Monitoring"
        self.monitoring_count_update(edition_code=products.Monitoring.edition_code)

    def test_reset_CE_Monitoring_successfully(self):
        """
        Given an org CE-Monitoring
        When reset CE-Monitoring quantity
        Then the CE-Monitoring vf_monitors_count will be 0 successfully
        """
        self.url = f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_change_CE-Monitoring_0_monitoring"
        self.monitoring_count_reset(edition_code=products.CEMonitoring.edition_code)

    def test_reset_Monitoring_successfully(self):
        """
        Given an org Monitoring
        When reset Monitoring quantity
        Then the Monitoring vf_monitors_count will be reset successfully
        """
        self.url = f"{reverse('vodafone:handle_cert_webhook')}?eventUrl=subscription_change_Monitoring_0_monitoring"
        self.monitoring_count_reset(edition_code=products.Monitoring.edition_code)

    def test_cancel_Monitoring_successfully(self):
        # Authorization header is added to simulate real requests
        org, vf_org = self.get_or_create_vf_organisation(size=Organisation.ORGANISATION_SIZE_5_9, vf_monitors_count=5)
        app_install = self.create_app_install(org)
        subscription = self.create_vf_subscription(vf_org, products.Monitoring.edition_code)
        self.assertTrue(org.learn_lite_tab_displayed)

        self.send_cancel_requset()
        org.refresh_from_db()
        vf_org.refresh_from_db()
        app_install.refresh_from_db()
        subscription.refresh_from_db()

        self.assertFalse(org.software_support)
        self.assertFalse(org.policies_support)
        self.assertEqual(vf_org.vf_monitors_count, 0)
        self.assertEqual(org.size, Organisation.ORGANISATION_SIZE_0)
        self.assertTrue(app_install.inactive)
        self.assertFalse(subscription.is_active)
        self.assertFalse(org.learn_lite_tab_displayed)

    def test_cancel_unused_CEP_onsite_audit_successfully(self):
        # Authorization header is added to simulate real requests
        org, vf_org = self.get_or_create_vf_organisation()
        cep_on_site_audit = models.CEPOnSiteAudit.objects.create(vf_organisation=vf_org, done_audits_count=0)
        subscription = self.create_vf_subscription(vf_org, products.CEPlusOnSiteAudit.edition_code)

        self.send_cancel_requset()
        subscription.refresh_from_db()

        self.assertFalse(subscription.is_active)
        self.assertFalse(models.CEPOnSiteAudit.objects.filter(id=cep_on_site_audit.id).exists())

    def cancel_uncertified_CE_Monitoring(self, app_install_activity, org_size, vf_monitors_count, software_support):
        # Authorization header is added to simulate real requests
        org, vf_org = self.get_or_create_vf_organisation()
        subscription = self.create_vf_subscription(vf_org, products.CEMonitoring.edition_code)
        test_utils.get_or_create_certification(org, products.Certificates.CERT_CYBER_ESSENTIALS)
        app_install = self.create_app_install(org)
        self.assertTrue(org.has_ce_certification)
        self.assertTrue(org.learn_lite_tab_displayed)

        self.send_cancel_requset()
        subscription.refresh_from_db()
        app_install.refresh_from_db()
        org.refresh_from_db()
        vf_org.refresh_from_db()

        self.assertFalse(subscription.is_active)
        self.assertFalse(org.has_ce_certification)
        self.assertEqual(app_install.inactive, app_install_activity)
        self.assertEqual(vf_org.vf_monitors_count, vf_monitors_count)
        self.assertEqual(org.size, org_size)
        self.assertEqual(org.software_support, software_support)
        self.assertEqual(org.policies_support, software_support)
        self.assertEqual(org.learn_lite_tab_displayed, software_support)

    def test_cancel_uncertified_CE_Monitoring_without_another_Monitoring_successfully(self):
        """
        Given an org CE uncertified while not owning a Monitoring edition
        When cancel CE-Monitoring
        Then the CE will be cancelled successfully
        And CE will be deleted
        And deactivate the appinstalls
        """
        self.cancel_uncertified_CE_Monitoring(True, Organisation.ORGANISATION_SIZE_0, 0, False)

    def test_cancel_uncertified_CE_Monitoring_with_another_active_Monitoring_successfully(self):
        """
        Given an org CE uncertified while owning a Monitoring edition
        When cancel CE-Monitoring
        Then the CE will be cancelled successfully
        And CE will be deleted
        And keep the appinstalls active
        """
        org, vf_org = self.get_or_create_vf_organisation(size=Organisation.ORGANISATION_SIZE_1, vf_monitors_count=1)
        models.VFSubscription.objects.create(vf_organisation=vf_org, edition_code="vf-monitoring")
        self.cancel_uncertified_CE_Monitoring(False, org.size, vf_org.vf_monitors_count, True)

    def cancel_certified_CE_Monitoring(self, app_install_activity, software_support):
        # Authorization header is added to simulate real requests
        org, vf_org = self.get_or_create_vf_organisation()
        subscription = self.create_vf_subscription(vf_org, products.CEMonitoring.edition_code)
        test_utils.get_or_create_certification(
            org, products.Certificates.CERT_CYBER_ESSENTIALS, status=OrganisationCertification.CERTIFIED)
        app_install = self.create_app_install(org)
        self.assertTrue(org.has_ce_certification)
        self.assertTrue(org.learn_lite_tab_displayed)

        self.send_cancel_requset()
        subscription.refresh_from_db()
        app_install.refresh_from_db()
        org.refresh_from_db()
        vf_org.refresh_from_db()

        self.assertFalse(subscription.is_active)
        self.assertTrue(org.has_ce_certification)
        self.assertEqual(app_install.inactive, app_install_activity)
        self.assertEqual(org.software_support, software_support)
        self.assertEqual(org.policies_support, software_support)
        self.assertEqual(org.learn_lite_tab_displayed, software_support)
        return vf_org, org

    def test_cancel_yearly_certified_CE_Monitoring_without_another_Monitoring_successfully(self):
        """
        Given an org CE annually certified
        When cancel CE-Monitoring
        Then the CE will be cancelled successfully
        And CE will be kept being fully paid
        And deactivate the appinstalls
        """
        vf_org, org = self.cancel_certified_CE_Monitoring(True, False)
        self.assertEqual(vf_org.vf_monitors_count, 0)
        self.assertEqual(org.size, Organisation.ORGANISATION_SIZE_0)

    def test_cancel_yearly_certified_CE_Monitoring_with_another_Monitoring_successfully(self):
        """
        Given an org CE annually certified while owning a Monitoring edition
        When cancel CE-Monitoring
        Then the CE will be cancelled successfully
        And CE will be kept being fully paid
        And keep the appinstalls active
        """
        org, vf_org = self.get_or_create_vf_organisation(size=Organisation.ORGANISATION_SIZE_5_9, vf_monitors_count=5)
        models.VFSubscription.objects.create(vf_organisation=vf_org, edition_code="vf-monitoring")
        vf_org, org = self.cancel_certified_CE_Monitoring(False, True)
        self.assertEqual(vf_org.vf_monitors_count, 5)
        self.assertEqual(org.size, Organisation.ORGANISATION_SIZE_5_9)

    def cancel_uncertified_CEP(self, has_cep_certification, certification_status):
        # Authorization header is added to simulate real requests
        org, vf_org = self.get_or_create_vf_organisation()
        subscription = self.create_vf_subscription(vf_org, products.CEPlus.edition_code)
        test_utils.get_or_create_certification(org, products.Certificates.CERT_CYBER_ESSENTIALS_PLUS, certification_status)
        self.assertTrue(org.has_cep_certification)
        self.send_cancel_requset()
        subscription.refresh_from_db()
        self.assertFalse(subscription.is_active)
        self.assertEqual(org.has_cep_certification, has_cep_certification)

    def test_cancel_uncertified_CEP_successfully(self):
        """
        Given an org CEP uncertified while not owning a Monitoring edition
        When cancel CEP
        Then the CEP will be cancelled successfully
        And CEP will be deleted
        """
        self.cancel_uncertified_CEP(False, OrganisationCertification.NOT_STARTED)

    def test_cancel_yearly_certified_CEP_successfully(self):
        """
        Given an org yearly CEP certified while not owning a Monitoring edition
        When cancel CEP
        Then the CEP will be cancelled successfully
        And CEP will be kept being fully paid
        """
        self.cancel_uncertified_CEP(True, OrganisationCertification.CERTIFIED)
