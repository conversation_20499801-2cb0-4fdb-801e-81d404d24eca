import logging

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import DatabaseError, transaction
from django.utils import timezone

import rulebook.models as rulebook_models
from analytics.tasks import calculate_organisation_analytics, update_partner_analytics
from appusers.models.app_installs import AppI<PERSON><PERSON>, AppUser
from distributors.models import Distributor
from lms.tasks import add_user_handlers
from organisations.models import Organisation
from partners.models import Partner
from rulebook.utils import get_latest_version
from vodafone import models, exceptions, products
from vodafone.utils import get_or_create_user

VODAFONE = "Vodafone"
SUCCESS = True
FAIL = False

logger = logging.getLogger(__name__)
User = get_user_model()


def get_create_distributor_partner(country="gb"):
    distributor, distributor_created = Distributor.objects.get_or_create(name=VODAFONE)
    if distributor_created:
        logger.info("VF distributor account created")
    partner, partner_created = Partner.objects.get_or_create(
        name=f'{VODAFONE} {country.upper()}',
        distributor=distributor,
        defaults={
            'header_background_color': Partner.VODAFONE_COLOR,
            'default_academy_enabled': True
        }
    )
    if partner_created:
        logger.info("VF partner account created")
    return distributor, partner


def update_analytics_for_organisation(org: Organisation):
    """wrapper for the async task """
    calculate_organisation_analytics.delay(org.pk)


def remove_user_from_their_organisation(user_email):
    appuser, org = get_appuser_data_from_email(user_email)
    deactivate_appuser(appuser=appuser)
    remove_appuser_from_organisation_admin(appuser=appuser, org=org)
    update_analytics_for_organisation(org)
    return org.secure_id


def get_appuser_data_from_email(user_email):
    appuser = AppUser.objects.get(email=user_email)
    org = appuser.organisation
    return appuser, org


def remove_appuser_from_organisation_admin(appuser, org):
    org.admins.filter(user__email=appuser.email).delete()


def deactivate_appuser(appuser):
    appuser.active = False
    appuser.save()


class VodafoneServices:
    webhook_data: models.AppDirectWebhook = None

    def __init__(self, config):
        """
        object oriented means we can set dependencies at run-time.
        this means we can swap them out in testing, & when we go multi-region
        """
        self.config = config

    def ingest_data(self, webhook_raw_data: str):
        """
        given json data (from a json webhook) convert it to a AppDirectWebhook object
        """
        self.webhook_data: models.AppDirectWebhook = models.AppDirectWebhook.from_json(webhook_raw_data)

    @property
    def country(self):
        return self.webhook_data.company_country.upper()

    @property
    def partner_name(self):
        return f"Vodafone {self.country}"

    @property
    def distributor_name(self):
        return self.distributor.name

    @property
    def distributor(self):
        return Distributor.objects.get(name=VODAFONE)

    @property
    def partner(self):
        return Partner.objects.get(name=self.partner_name, distributor=self.distributor)

    @property
    def pricing_band(self):
        return self.partner.default_pricing_band

    def execute(self):
        webhook_type = self.webhook_data.webhook_type.lower()
        webhook_method = f'execute_{webhook_type}_webhook'
        org_secure_id = getattr(self, webhook_method)()
        return {'success': True, "accountIdentifier": org_secure_id}

    def execute_subscription_order_webhook(self):
        """
        Orchestrate VF organisation creation and setup
        """
        logger.debug("VF:44 subscription_order started")
        product = products.get_product(self.webhook_data.edition_code, self)
        vf_organisation = product.purchase()
        subscription_uuid = self.save_subscription_order(vf_organisation=vf_organisation)
        logger.debug("VF:65 subscription_order completed")
        return subscription_uuid

    def execute_subscription_change_webhook(self):
        """
        Orchestrate VF organisation updates
        """
        logger.debug("VF:88 subscription_change started")
        product = products.get_product(self.webhook_data.edition_code, self)
        product.update()
        logger.debug("VF:91 subscription_change completed")

    def execute_user_unassignment_webhook(self):
        logger.debug("VF:69 user_unassignment started")
        user_email = self.webhook_data.payload_user.email
        org_secure_id = remove_user_from_their_organisation(user_email)
        logger.debug("VF:77 user_unassignment completed")
        return org_secure_id

    def execute_subscription_cancel_webhook(self):
        logger.debug("VF:102 subscription_cancel started")
        product = products.Product.get_cancelled_product(self)
        product.cancel()
        logger.debug("VF:105 subscription_cancel completed")

    def setup_organisation(self):
        try:
            with transaction.atomic():
                # todo check : there's performance implications of using a transaction block
                org = self.create_organisation()
                self.create_vf_organisation(org)
                user, created = self.initialize_user()
                self.associate_user_to_organisation(admin_user=user, organisation=org)
        except DatabaseError:
            logger.error(f"VF:55 subscription_order FAILED: {self.webhook_data}")
            raise exceptions.InvalidData()
        else:
            self.send_admin_credentials(user=user)
            self.send_organisation_created_notifications_to_staff(organisation=org, user=user)
            self.update_analytics(org)
            return org

    def handle_create_certifications(self, org, certificate):
        self.create_certification(organisation=org, certificate=certificate)

    def update_analytics(self, org):
        update_partner_analytics.delay(org.partner.pk)

    def create_organisation(self):
        org = Organisation.objects.create(
            name=self.webhook_data.company_name,
            bulk_install=False,
            partner=self.partner,
            pricing_band=self.pricing_band,
        )
        org.settings.smart_score_enabled = True
        org.settings.save()
        return org

    def create_vf_organisation(self, org):
        models.VFOrganisation.objects.create(
            name=self.webhook_data.company_name,
            vf_uuid=self.webhook_data.company_uuid,
            organisation=org,
            country=self.country
        )

    def enable_org_software_support(self, org):
        org.software_support = True
        org.settings.academy_enabled = True
        org.settings.save()
        org.user_journey_passed = timezone.now()
        org.save()

    def handle_software_support(self, org):
        if self.has_software_support():
            self.set_monitors_count(org.vodafone_organisation)
            if not org.software_support:
                self.enable_org_software_support(org)
                self.create_application_users(organisation=org)
                app_users = org.app_users.all()
                self.send_app_download_links(app_users=app_users)
                if not org.learn_lite_tab_displayed:
                    return
                self.enrol_appusers_to_training(org, list(app_users.values_list('uuid', flat=True)))

    def has_software_support(self):
        return self.webhook_data.quantity > 0

    def initialize_user(self):
        user, created = get_or_create_user(self.webhook_data.admin_email)
        user.profile.set_onboarding_as_completed()
        return user, created

    def create_application_users(self, organisation):
        # create app user for admin
        admin_users = organisation.admins.all()
        for admin_user in admin_users:
            user = admin_user.user
            organisation.app_users.update_or_create(
                email=user.email,
                defaults={
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "is_admin": True
                }
            )

    def associate_user_to_organisation(self, admin_user, organisation):
        # add new admin user to an organisation
        organisation.admins.create(
            user=admin_user,
            is_admin=True
        )

    def create_certification(self, organisation, certificate):
        organisation.certifications.create(
            version=get_latest_version(type=certificate.value)
        )

    def send_app_download_links(self, app_users):
        """
        Sends an email with download app link to all passed app_users
        :param app_users: organisations app users
        :type app_users: appusers.AppUser
        :return: nothing
        :rtype: None
        """
        for app_user in app_users:
            context_vars = {
                "first-name": app_user.first_name,
                "download-url": app_user.get_download_url()
            }
            self.config.send_email(to_email=app_user.email,
                                   template_name=models.VODAFONE_DOWNLOAD_APPS,
                                   context_vars=context_vars,
                                   )

    def send_admin_credentials(self, user):
        # access-url email password first-name
        context_vars = {
            "first-name": user.first_name,
            "access-url": self.config.get_access_url(),
            "email": user.email,
            "password": user.profile.allauth_password_reset_url()
        }
        self.config.send_email(to_email=user.email,
                               template_name=models.VODAFONE_SIGN_UP,
                               context_vars=context_vars,
                               )

    def send_cybersmart_complete_achieved(self, user, certificate):
        """
        Sends congratulation email about achieving CyberSmart Complete.
        :param user: to what user email will be sent
        :type user: auth.User
        :param certificate: CyberSmart Complete certificate object
        :type certificate: organisations.OrganisationCertification
        :return:
        """
        self.config.send_email(
            to_email=user.email,
            template_name=models.ACHIEVED_CYBERSMART_COMPLETE,
            context_vars={"access-url": certificate.absolute_url, "admin-name": user.get_full_name() or user.email}
        )

    def send_organisation_created_notifications_to_staff(self, organisation, user):
        for staff_email in settings.ONBOARDING_NOTIFICATIONS:
            context_vars = {
                "date": organisation.created,
                "org-name": organisation.name,
                "user-email": user.email,
                "distributor": self.distributor_name,
                "partner": self.partner_name,
            }
            self.config.send_email(to_email=staff_email,
                                   template_name=models.STAFF_NOTIFICATION,
                                   context_vars=context_vars,
                                   )

    def get_organisation_size(self, quantity):
        for size_key, size_range in Organisation.ORGANISATION_SIZE_RANGE.items():
            if quantity in size_range:
                return size_key

    def handle_adding_cep_onsite_audit(self, vf_organisation):
        models.CEPOnSiteAudit.objects.create(vf_organisation=vf_organisation, total_audits_count=self.webhook_data.quantity)

    def has_invalid_CE_certification(self, organisation):
        ce_certification = organisation.get_latest_certificate(rulebook_models.CYBER_ESSENTIALS)
        return bool(ce_certification.certified and ce_certification.certified_date < timezone.now() - relativedelta(months=3))

    def is_valid_cep_onsite_audit_update(self, vf_organisation):
        return (hasattr(vf_organisation, "cep_onsite_audit") and
                vf_organisation.cep_onsite_audit.done_audits_count <= self.webhook_data.quantity)

    def update_cep_onsite_audit(self, vf_organisation):
        vf_organisation.cep_onsite_audit.total_audits_count = self.webhook_data.quantity
        vf_organisation.cep_onsite_audit.save()

    def is_valid_monitors_count_increase(self, vf_organisation):
        return vf_organisation.vf_monitors_count < self.webhook_data.quantity

    def set_monitors_count(self, vf_organisation):
        self.set_organisation_size(vf_organisation, self.webhook_data.quantity)

    def set_organisation_size(self, vf_organisation, quantity=0):
        vf_organisation.vf_monitors_count = quantity
        vf_organisation.save()
        organisation = vf_organisation.organisation
        organisation.size = self.get_organisation_size(quantity)
        organisation.save()
        return organisation

    def disable_software_support(self, organisation):
        organisation.software_support = False
        organisation.settings.academy_enabled = True
        organisation.settings.save()
        organisation.user_journey_passed = None
        organisation.save()
        AppInstall.objects.filter(app_user__organisation_id=organisation.id).update(
            inactive=True, modified=timezone.now(), date_removed=timezone.now())

    def save_subscription_order(self, vf_organisation):
        return models.VFSubscription.objects.create(
            vf_organisation=vf_organisation,
            edition_code=self.webhook_data.edition_code,
            pricing_duration=self.webhook_data.pricing_duration).uuid

    def can_be_cancelled(self, cep_onsite_audit):
        return cep_onsite_audit.done_audits_count == 0

    def cancel_cep_onsite_audit(self, cep_onsite_audit):
        models.CEPOnSiteAudit.objects.filter(pk=cep_onsite_audit.pk).delete()

    def deactivate_subscription(self, subscription_uuid):
        models.VFSubscription.objects.filter(uuid=subscription_uuid).update(is_active=False, modified=timezone.now())

    def has_active_monitoring_subscription(self, organisation):
        return models.VFSubscription.objects.filter(
            vf_organisation__organisation=organisation, is_active=True, edition_code=products.Monitoring.edition_code).exists()

    def has_active_cep_onsite_audit_subscription(self, organisation):
        return models.VFSubscription.objects.filter(
            vf_organisation__organisation=organisation, is_active=True,
            edition_code=products.CEPlusOnSiteAudit.edition_code).exists()

    def can_delete_certification(self, certification):
        return certification.status not in certification.CERTIFIED_STATUSES

    def deactivate_org_users(self, subscription_uuid):
        User.objects.filter(organisation_admins__organisation__vodafone_organisation__subscriptions__uuid=subscription_uuid
                            ).update(is_active=False)

    def has_vf_org_active_subscription(self, subscription_uuid):
        return models.VFSubscription.objects.filter(
            vf_organisation__subscriptions__uuid=subscription_uuid, is_active=True).exists()

    def has_org_active_subscription(self, vf_organisation):
        return models.VFSubscription.objects.filter(vf_organisation=vf_organisation, is_active=True).exists()

    def deactivate_vf_org(self, subscription_uuid):
        models.VFOrganisation.objects.filter(subscriptions__uuid=subscription_uuid).update(is_active=False)

    def reactivate_vf_org(self, vf_org):
        vf_org.is_active = True
        vf_org.save()

    def reactivate_org_users(self, org):
        User.objects.filter(organisation_admins__organisation=org).update(is_active=True)

    def is_same_subscription(self, subscription):
        return (subscription.edition_code == self.webhook_data.edition_code
                and subscription.pricing_duration == self.webhook_data.pricing_duration)

    def enrol_appusers_to_training(self, org, app_users_uuids):
        add_user_handlers.apply_async(args=[org.id, app_users_uuids])
