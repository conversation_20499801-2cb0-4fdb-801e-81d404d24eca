import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from urllib.parse import urlencode

import waffle
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.db import OperationalError
from django.db.models import Case, Count, F, Q, When, Sum
from django.db.models import QuerySet
from django.forms.models import formset_factory
from django.http import HttpResponseRedirect, JsonResponse
from django.shortcuts import render
from django.template.loader import render_to_string
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from django.views.generic import C<PERSON><PERSON>ie<PERSON>, <PERSON>ete<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>w, <PERSON><PERSON>iew, <PERSON><PERSON>late<PERSON>ie<PERSON>, UpdateView, View
from oauth2_provider.models import get_application_model

from accounts.permissions import PEOPLE_AND_ORGANISATION_PERMISSION
from analytics.models import PartnerAnalytics
from analytics.tasks import update_partner_analytics
from appusers.models import AppInstall, AppOSInstalledSoftware, InstalledSoftwarePartnerSummary, AppInstallOSUser
from beta_features.mixins import BetaViewMixin
from billing.models import PurchaseOrder
from billing.providers.chargebee.plans import PARTNER_CE_PLAN, PARTNER_GDPR_PLAN
from billing.utils import is_coupon_valid
from common.mixins import OrderingViewMixin, SearchViewMixin
from common.models import CSVFile, ProjectSettings
from common.tasks import generate_devices_csv
from common.utils import get_full_name_or_email, handle_user_input, SafePaginator
from common.views import BaseCSVReportView, BaseDevicesView, BaseOrganisationsView
from dashboard.tasks import refresh_installed_software_summary_partner
from emails.tasks import send_request_cep_audit_email
from notifications.handlers import NotificationHandler
from notifications.mixins import SendNotificationDeleteMixin
from opswat.utils import parse_composite_ids
from organisations.forms import AddOrganisationForm, BulkCreateOrganisationForm
from organisations.models import Organisation, OrganisationCertification, get_organisations
from partners.forms import (
    BrandingForm, PartnerUserSimpleForm, RequestCEPlusAuditForm, SecurityControlEmailForm,
    SecurityControlForm
)
from partners.mixins import PartnerViewAccessMixin
from partners.models import Partner, PartnerUser, SecurityControl, SecurityControlEmail
from partners.tasks import generate_partner_org_csv
from partners.utils import create_or_update_oauth_application_for_partner, get_partner_oauth_application_name
from regions.utils import is_eu_geo
from rulebook.models import CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS, GDPR
from rulebook.utils import get_latest_version
from sales.salesforce.api import SalesForceApi
from signup.mixins import SignupMixin
from trustd.tasks import update_partner_logo_trustd
from vulnerabilities.utils import aggregate_installed_software_counts

logger = logging.getLogger(__name__)

User = get_user_model()
Oauth_Application = get_application_model()



class DirectPartnerSubscriptionsOverviewView(SignupMixin, OrderingViewMixin, SearchViewMixin, ListView):
    context_object_name = 'organisations'
    paginate_by = 20
    template_name = 'partners/subscriptions-overview/main.html'
    search_fields = [
        "name"
    ]
    ordering_fields_map = {'name': ['name']}

    def get(self, request, *args, **kwargs):
        ps = ProjectSettings.objects.get()
        if not request.user.profile.is_direct_partner or ps.disable_payments:
            return HttpResponseRedirect(reverse('dashboard:home'))
        user_profile = request.user.profile
        if request.headers.get("x-requested-with") == "XMLHttpRequest" and user_profile.is_direct_partner:
            partner = user_profile.partner
            if partner.is_billable:
                portal_session = partner.billing.create_portal_session()
                return JsonResponse(portal_session)

        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        return self.ordering_queryset(
            self.search_queryset(
                Organisation.objects.filter(partner=self.request.user.profile.partner)
            )
        ).prefetch_related('subscriptions')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        query = self.request.GET.dict()
        if 'page' in query:
            query.pop('page')
        context.update({
            'params_query': urlencode(query),
            'partner_view': True,
            'ce_plan_id_v4_monthly': PARTNER_CE_PLAN,
            'privacy_toolbox_plan_id_v4_monthly': PARTNER_GDPR_PLAN,
            'GDPR_feature_name': _("Privacy Toolbox")
        })
        return context


class ChannelPartnerSubscriptionsOverviewView(SignupMixin, OrderingViewMixin, SearchViewMixin, ListView):
    context_object_name = 'organisations'
    paginate_by = 20
    template_name = 'partners/subscriptions/main.html'
    search_fields = [
        "name"
    ]
    ordering_fields_map = {'name': ['name']}

    def get(self, request, *args, **kwargs):
        ps = ProjectSettings.objects.get()
        if not request.user.profile.is_channel_partner or ps.disable_payments \
                or not waffle.switch_is_active("channel-subscriptions-overview"):
            return HttpResponseRedirect(reverse('dashboard:home'))
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        return self.ordering_queryset(
            self.search_queryset(
                Organisation.objects.filter(partner=self.request.user.profile.partner)
            )
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        query = self.request.GET.dict()
        if 'page' in query:
            query.pop('page')
        context.update({
            'params_query': urlencode(query),
            'GDPR_feature_name': _("Privacy Toolbox")
        })
        return context


class PartnerManageUsersView(SignupMixin, TemplateView):
    http_method_names = ("get", "post")
    template_name = "partners/manage-users.html"

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, "partner") or not request.user.partner:
            return HttpResponseRedirect(reverse("dashboard:home"))
        return super().dispatch(request, *args, **kwargs)


class PartnerUserDeleteView(LoginRequiredMixin, SendNotificationDeleteMixin, DeleteView):
    http_method_names = ['post']
    model = PartnerUser
    success_url = reverse_lazy('partners:manage-users')
    slug_field = 'secure_id'
    slug_url_kwarg = 'secure_id'
    message_type = 'partner_user_deletion'

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, 'partner') or not request.user.partner:
            return HttpResponseRedirect(reverse('dashboard:home'))
        return super(PartnerUserDeleteView, self).dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return self.request.user.profile.partner.users.all()

    def get_notification_handler_kwargs(self):
        """ Builds kwargs to send notification. Sending is done by SendNotificationDeleteMixin """
        return {
            'message_type': self.message_type,
            'user_name': get_full_name_or_email(self.request.user),
            'organisation_id': self.organisation_id,
            'partner_name': self.request.user.profile.partner.name,
            'partner_user_name_deleted': self.deleted_user_name,
            'url': reverse('partners:manage-users')
        }


class PartnerUserCreateView(LoginRequiredMixin, FormView):
    http_method_names = ['post']
    form_class = PartnerUserSimpleForm
    success_url = reverse_lazy('partners:manage-users')

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, 'partner') or not request.user.partner:
            return HttpResponseRedirect(reverse('dashboard:home'))
        return super(PartnerUserCreateView, self).dispatch(request, *args, **kwargs)

    def send_notification(self, new_user, partner):
        """ Send notification """
        main_organisation = self.request.user.profile.organisation
        kwargs = {
            'user_name': get_full_name_or_email(self.request.user),
            'organisation_id': main_organisation.id,
            'partner_name': partner.name,
            'new_partner_user_name': get_full_name_or_email(new_user),
            'url': reverse('partners:manage-users')
        }
        NotificationHandler.send_notification(message_type='partner_user_creation', **kwargs)

    def form_valid(self, form):
        # create regular user
        user = get_user_model().objects.create(
            email=handle_user_input(form.cleaned_data['email']),
            username=handle_user_input(form.cleaned_data['email'])
        )

        # set as active user
        user.is_active = True
        user.save()
        # mark on boarding as completed
        user.profile.onboarding_completed = True
        user.profile.skip_payment = True
        user.profile.save()
        # send an email
        from emails.notifications import send_welcome_email
        send_welcome_email(user, self.request.user)
        # create partner user
        partner = self.request.user.profile.partner
        PartnerUser.objects.create(
            partner=self.request.user.profile.partner,
            user=user
        )
        self.send_notification(user, partner)
        messages.add_message(self.request, messages.SUCCESS, _("New partner user successfully created"))
        return HttpResponseRedirect(reverse('partners:manage-users'))

    def form_invalid(self, form):
        messages.add_message(self.request, messages.ERROR, form.errors.as_text())
        return HttpResponseRedirect(reverse('partners:manage-users'))


class SecurityControlView(SignupMixin, FormView):
    template_name = 'partners/security_control/index.html'
    form_class = SecurityControlForm
    success_url = reverse_lazy("partners:security-controls")

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, 'partner') or not request.user.partner or request.user.partner.partner.frozen:
            return HttpResponseRedirect(reverse('dashboard:home'))
        return super(SecurityControlView, self).dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super(SecurityControlView, self).get_context_data(**kwargs)
        security_control = self.request.user.partner.partner.security_control
        context["languages"] = settings.LANGUAGES
        if security_control:
            context["failed_report_emails"] = security_control.emails.filter(type=SecurityControl.FAILED_REPORT)
            context["failed_check_emails"] = security_control.emails.filter(type=SecurityControl.FAILED_CHECK)
        return context

    def get_initial(self):
        security_control, _ = SecurityControl.objects.get_or_create(
            partner=self.request.user.partner.partner
        )
        return {key: getattr(security_control, key) for key in self.form_class.base_fields.keys()}

    def form_valid(self, form):
        security_control = self.request.user.partner.partner.security_control
        for field, value in form.cleaned_data.items():
            setattr(security_control, field, value)
        security_control.save()
        return super(SecurityControlView, self).form_valid(form)


class SecurityControlEmailCreateView(SignupMixin, CreateView):
    http_method_names = ["post"]
    form_class = SecurityControlEmailForm

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, 'partner') or not request.user.partner or \
                not request.user.partner.partner.security_control:
            return HttpResponseRedirect(reverse('dashboard:home'))
        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs.update({'request': self.request})
        return kwargs

    def form_valid(self, form):
        email = self.request.user.partner.partner.security_control.emails.create(
            email=form.cleaned_data.get("email"),
            type=form.cleaned_data.get("type")
        )
        return JsonResponse({"success": True, "pk": email.pk, "delete_url": email.delete_url})

    def form_invalid(self, form):
        return JsonResponse({"success": False, "error": form.errors})


class SecurityControlEmailDeleteView(SignupMixin, DeleteView):
    http_method_names = ["delete"]
    model = SecurityControlEmail

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, 'partner') or not request.user.partner or \
                not request.user.partner.partner.security_control:
            return HttpResponseRedirect(reverse('dashboard:home'))
        return super(SecurityControlEmailDeleteView, self).dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return super(SecurityControlEmailDeleteView, self).get_queryset().filter(
            security_control__partner=self.request.user.partner.partner
        )

    def delete(self, request, *args, **kwargs):
        """
        Calls the delete() method on the fetched object and then
        redirects to the success URL.
        """
        self.object = self.get_object()
        self.object.delete()
        return JsonResponse({"success": True})


class PartnerEnableSoftware(SignupMixin, View):
    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, 'partner') or not request.user.partner:
            return HttpResponseRedirect(reverse('dashboard:home'))
        return super().dispatch(request, *args, **kwargs)

    @staticmethod
    def post(request, org_id):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()
        # create software subscription
        partner = organisation.partner
        if not partner.is_billable:
            messages.add_message(request, messages.ERROR, _("Something went wrong, Please contact customer support."))
        else:
            partner.billing.add_application_plan(organisation)

            organisation.software_support = True
            organisation.save(update_fields=['software_support', 'modified'])
            messages.add_message(request, messages.SUCCESS, _("Software subscription was enabled for this organisation"))

        return HttpResponseRedirect(organisation.url)


class PartnerEnableCyberEssentials(SignupMixin, View):
    """
    Enable Cyber Essentials for an organisation (adds cert and creates a subscription)
    """
    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, "partner") or not request.user.partner:
            return HttpResponseRedirect(reverse("dashboard:home"))
        return super().dispatch(request, *args, **kwargs)

    @staticmethod
    def post(request, org_id):
        organisation = get_organisations(request.user, org_id)
        if not organisation:
            raise PermissionDenied()

        partner = organisation.partner

        if not partner.is_billable:
            messages.add_message(request, messages.ERROR, _("Something went wrong, Please contact customer support."))
        elif organisation.ce_certification:
            messages.add_message(request, messages.ERROR, _("This organisation already has Cyber Essentials."))
        else:
            # add cert
            organisation.certifications.create(
                version=get_latest_version(type=CYBER_ESSENTIALS)
            )
            # get the PO if it was added during org creation by the partner
            purchase_order = PurchaseOrder.objects.filter(organisation=organisation).first()
            # get coupon code if it was added during org creation by the partner
            coupon_code = organisation.coupon_code
            # if coupon is not valid anymore, just remove it
            if not is_coupon_valid(coupon_code, "random_plan"):
                coupon_code = None
            # add the subscription
            partner.billing.add_certificate_plan(
                organisation=organisation,
                certificate_type=CYBER_ESSENTIALS,
                purchase_order=purchase_order,
                # get coupon code if it was added during org creation by the partner
                coupon_code=coupon_code
            )
            messages.add_message(
                request, messages.SUCCESS, _("Cyber Essentials subscription was enabled for this organisation")
            )
        return HttpResponseRedirect(request.GET.get("next", organisation.url))


class DevicesView(BaseDevicesView, PartnerViewAccessMixin):
    """
    Displays all devices under a partner.
    """
    template_name = "partners/devices/main.html"

    def get_view_url(self):
        view_url = reverse('partners:devices')
        if self.distributor_viewing_partner_page:
            view_url = reverse('partners:devices', kwargs={'partner_id': self.partner.secure_id})
        return view_url

    def get_main_entity(self) -> Optional[Partner]:
        """
        Returns the main entity for the view.
        """
        return self.partner

class PartnerReportViewBase():
    def get_end_of_life_report_view_url(self):
        end_of_life_report_view_url = reverse('partners:end-of-life-report')
        if self.distributor_viewing_partner_page:
            end_of_life_report_view_url = reverse('partners:end-of-life-report', kwargs={'partner_id': self.partner.secure_id})
        return end_of_life_report_view_url

    def get_software_report_view_url(self):
        software_report_view_url = reverse('partners:software-report')
        if self.distributor_viewing_partner_page:
            software_report_view_url = reverse('partners:software-report', kwargs={'partner_id': self.partner.secure_id})
        return software_report_view_url


class PartnerEndOfLifeReportView(PartnerViewAccessMixin, TemplateView, PartnerReportViewBase):
    template_name = 'partners/reports/partner-end-of-life-report.html'

    def get_queryset(self) -> QuerySet[AppInstall]:
        """
        Returns a QuerySet of EndOfLife objects annotated with the number of AppInstalls that are using them.
        """
        return AppInstall.objects.filter(
            id__in=self.partner.active_app_installs,
            end_of_life__isnull=False
        ).values('end_of_life__base_operating_system', 'end_of_life__cycle').annotate(
            counter=Count('end_of_life')
        ).values(
            'end_of_life__pk', 'end_of_life__base_operating_system', 'end_of_life__cycle',
            'end_of_life__end_of_life_date', 'end_of_life__latest', 'end_of_life__link',
            'end_of_life__codename', 'end_of_life__release_date', 'end_of_life__release_date',
            'end_of_life__manually_unsupported', 'counter'
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["end_of_life_report_view_url"] = self.get_end_of_life_report_view_url()
        context["software_report_view_url"] = self.get_software_report_view_url()
        parsed = self.get_queryset()
        context['parsed_builds'] = parsed
        context['today'] = datetime.today()
        return context

    def get(self, request, **kwargs):
        context = self.get_context_data(**kwargs)
        return render(request, self.template_name, context)


class PartnerSoftWareReportView(PartnerViewAccessMixin, ListView, PartnerReportViewBase):
    http_method_names = ["get", "post"]
    template_name = "partners/reports/partner-software-report.html"
    model = InstalledSoftwarePartnerSummary
    context_object_name = "apps"
    paginate_by = 20
    paginator_class = SafePaginator

    def __init__(self):
        self.organisations = None
        self.allowed_ordering = [
            "product", "-product",
            "version", "-version",
            "vendor", "-vendor",
            "is_vulnerable", "-is_vulnerable",
            "freq", "-freq"
        ]
        super().__init__()

    @method_decorator(login_required)
    def dispatch(self, request, **kwargs):
        self.organisations = get_organisations(request.user)
        return super().dispatch(request, **kwargs)

    def get_ordering(self):
        ordering = self.request.GET.get("ordering")
        if ordering:
            if ordering not in self.allowed_ordering:
                raise PermissionDenied
            return ordering

    def get_queryset(self):
        vulnerable = self.request.GET.get("vulnerable")
        search = self.request.GET.get("search")

        filters = {}
        if vulnerable is not None:
            filters["is_vulnerable"] = vulnerable == 'true'
        if search is not None:
            search_filter = Q(product__icontains=search) | Q(version__icontains=search) | Q(vendor__icontains=search)
        else:
            search_filter = None

        queryset = super().get_queryset().filter(
            partner=self.partner
        ).filter(
            **filters
        )
        if search_filter:
            queryset = queryset.filter(search_filter)

        queryset = queryset.values('id', 'product', 'version', 'vendor', 'is_vulnerable').annotate(freq=Sum('freq'))

        return queryset

    def get_context_data(self, *, object_list=None, **kwargs):
        context = {"organisations": self.organisations}
        context["end_of_life_report_view_url"] = self.get_end_of_life_report_view_url()
        context["software_report_view_url"] = self.get_software_report_view_url()
        try:
            context.update(super().get_context_data())
            app_list = InstalledSoftwarePartnerSummary.objects.filter(
                partner=self.partner
            )
            total_count, safe_count, vulnerable_count = aggregate_installed_software_counts(app_list)
        except OperationalError as error:
            context['operational_error'] = True
            logger.debug(f"Error constructing report: {error}")
            refresh_installed_software_summary_partner.delay()
        else:
            context["installed_software_count"] = total_count
            context["vulnerable_software_count"] = vulnerable_count
            context["safe_software_count"] = safe_count
            context["param__vulnerable"] = self.request.GET.get("vulnerable")
            context["param__ordering"] = self.get_ordering()
            context["param__search"] = self.request.GET.get("search")

            query = self.request.GET.dict()
            if "page" in query:
                query.pop("page")

            context.update({"params_query": urlencode(query)})
        return context

    def get(self, request, *args, **kwargs):
        if request.headers.get("x-requested-with") == "XMLHttpRequest":
            software_id = request.GET.get("software")
            if software_id:
                opswat_ids, regular_ids = parse_composite_ids(software_id)

                data = []

                if regular_ids:
                    # Regular software query
                    user_installs = AppOSInstalledSoftware.objects.filter(
                        software_id__in=regular_ids,
                        report__app_install__app_user__organisation__in=self.organisations,
                        report__app_install__app_user__active=True,
                        report__app_install__inactive=False
                    ).distinct("report__app_install").prefetch_related("report__app_install")

                    data.extend([
                        {
                            "organisation_name": obj.report.app_install.app_user.organisation.name,
                            "hostname": obj.report.app_install.hostname,
                            "date_installed": obj.get_date_installed(),
                            "device_url": obj.report.app_install.url()
                        } for obj in user_installs
                    ])

                if opswat_ids:
                    # OPSWAT software query
                    user_installs = AppInstallOSUser.objects.filter(
                        app_install__installed_products__product_versions__id__in=opswat_ids,
                        app_install__app_user__organisation__in=self.organisations,
                        app_install__app_user__active=True,
                        app_install__inactive=False
                    ).order_by('app_install').distinct('app_install').prefetch_related("app_install")

                    data.extend([
                        {
                            "organisation_name": obj.app_install.app_user.organisation.name,
                            "hostname": obj.app_install.hostname,
                            "date_installed": obj.installed_products.get_date_installed(),
                            "device_url": obj.app_install.url()
                        } for obj in user_installs
                    ])

                return JsonResponse({"hosts": data})

        return super().get(request, *args, **kwargs)


class DevicesCSVReportView(BaseCSVReportView, PartnerViewAccessMixin):
    """ Generates CSV report with all Devices."""

    type_of_csv = CSVFile.TYPES_OF_CSV.DEVICES
    csv_generation_task = generate_devices_csv
    objects_name = 'device_ids'
    main_entity_name = 'partner_id'


class CertificatesView(SignupMixin, PartnerViewAccessMixin, ListView):
    """
    Displays all certificates under a partner.
    """
    template_name = "partners/certificates/main.html"
    context_object_name = "certificates"
    paginate_by = 15

    def get(self, request, *args, **kwargs):
        self.partner_orgs_ids = Organisation.objects.filter(
            partner_id=self.partner.id,
            is_test=False,
        ).values_list("id", flat=True)
        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        filter_by_expiring = self.request.GET.get('expiring')
        is_expiring_only = filter_by_expiring and filter_by_expiring.lower() == 'true'
        q = Q(organisation__id__in=self.partner_orgs_ids)

        if is_expiring_only:
            q &= Q(
                    status__in=[
                        OrganisationCertification.EXPIRED,
                        OrganisationCertification.EXPIRING,
                    ]
                )

        certs = OrganisationCertification.objects.filter(q).distinct().select_related(
            "organisation", "version", "version__type", "survey", "survey__declaration",
            "survey__assessment", "issued_certification"
        ).annotate(multiversion=Count(Case(When(
            organisation__certifications__version__type__type=F("version__type__type"), then=1
        )))).order_by("-created")

        # not started certs
        not_started_certs = certs.filter(
            status=OrganisationCertification.NOT_STARTED,
            # hide certs that wasn't started during 12 months after creation
            created__gte=timezone.now() - timedelta(days=365)
        ).order_by("-created")
        # not started certs count
        not_started_count = not_started_certs.count()
        # not started CE certs count
        not_started_ce_count = not_started_certs.filter(
            version__type__type=CYBER_ESSENTIALS
        ).count()
        # not started CE+ certs count
        not_started_ce_plus_count = not_started_certs.filter(
            version__type__type=CYBER_ESSENTIALS_PLUS
        ).count()
        # not started GDPR certs count
        not_started_gdpr_count = not_started_certs.filter(
            version__type__type=GDPR
        ).count()

        # in progress certs
        in_progress_certs = certs.filter(
            status__in=OrganisationCertification.IN_PROGRESS_STATUSES
        ).order_by("-created")
        # in progress certs count
        in_progress_count = in_progress_certs.count()
        # in progress CE certs count
        in_progress_ce_count = in_progress_certs.filter(
            version__type__type=CYBER_ESSENTIALS
        ).count()
        # in progress CE+ certs count
        in_progress_ce_plus_count = in_progress_certs.filter(
            version__type__type=CYBER_ESSENTIALS_PLUS
        ).count()
        # in progress GDPR certs count
        in_progress_gdpr_count = in_progress_certs.filter(
            version__type__type=GDPR
        ).count()

        # completed certs
        completed_certs = certs.filter(
            Q(
                status__in=OrganisationCertification.CERTIFIED_STATUSES
            ) | Q(
                status=OrganisationCertification.EXPIRED,
                renewal_end_date__gte=timezone.now() - timedelta(days=365)
                # expired certs will be shown for 1 year after expiry date
            )
        ).order_by("-issued_certification__date")
        # completed certs count
        completed_count = completed_certs.count()
        # completed CE certs count
        completed_ce_count = completed_certs.filter(
            version__type__type=CYBER_ESSENTIALS
        ).count()
        # completed CE+ certs count
        completed_ce_plus_count = completed_certs.filter(
            version__type__type=CYBER_ESSENTIALS_PLUS
        ).count()
        # completed GDPR certs count
        completed_gdpr_count = completed_certs.filter(
            version__type__type=GDPR
        ).count()

        context["not_started_certs"] = not_started_certs.annotate(**self.get_annotate_params())
        context["not_started_count"] = not_started_count
        context["not_started_ce_count"] = not_started_ce_count
        context["not_started_ce_plus_count"] = not_started_ce_plus_count
        context["not_started_gdpr_count"] = not_started_gdpr_count

        context["in_progress_certs"] = in_progress_certs.annotate(**self.get_annotate_params())
        context["in_progress_count"] = in_progress_count
        context["in_progress_ce_count"] = in_progress_ce_count
        context["in_progress_ce_plus_count"] = in_progress_ce_plus_count
        context["in_progress_gdpr_count"] = in_progress_gdpr_count

        context["completed_certs"] = completed_certs.annotate(**self.get_annotate_params())
        context["completed_count"] = completed_count
        context["completed_ce_count"] = completed_ce_count
        context["completed_ce_plus_count"] = completed_ce_plus_count
        context["completed_gdpr_count"] = completed_gdpr_count

        context["is_expiring_only"] = is_expiring_only
        context["show_gdpr_counter"] = (
                completed_gdpr_count > 0 or in_progress_gdpr_count > 0 or not_started_gdpr_count > 0
        )

        context["view_url"] = reverse('partners:certificates')
        if self.distributor_viewing_partner_page:
            context["view_url"] = reverse('partners:certificates', kwargs={'partner_id': self.partner.secure_id})

        context['show_detailed_counts'] = False if settings.IS_AUS_GEO or is_eu_geo() else True
        return context

    def get_queryset(self):
        return OrganisationCertification.objects.none()


class PartnerDashboardView(SignupMixin, PartnerViewAccessMixin, TemplateView):
    http_method_names = ("get",)
    template_name = "partners/dashboard/main.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["analytic"] = None
        context['show_widget_buttons'] = False if settings.IS_AUS_GEO else True
        context["show_certificate_breakdown"] = False if settings.IS_AUS_GEO else True
        context["expiring_certifications_count"] = self.partner.expiring_soon_organisations_count
        context["expired_certifications_count"] = self.partner.organisations_with_expired_certs_count()
        context["any_smart_score_data_present"] = self.partner.has_any_organisation_smart_score
        if hasattr(self.partner, 'analytics'):
            if self.partner.frozen:
                # Create empty analytics to set everything to 0
                analytic = PartnerAnalytics()
            else:
                analytic = self.partner.analytics
            context["analytic"] = analytic
            context['certifications_in_progress_count'] = \
                analytic.certifications_in_progress_count - analytic.certifications_in_progress_gdpr_count
            context['certifications_ready_for_assessment_count'] = \
                analytic.certifications_ready_for_assessment_count - \
                analytic.certifications_ready_for_assessment_gdpr_count
            context["show_gdpr"] = (analytic.certifications_certified_gdpr_count > 0)
            context["insecure_devices_present"] = analytic.insecure_devices_count > 0
            context["vulnerable_devices_present"] = analytic.vulnerable_devices_count > 0
        if self.partner.partners_own_org:
            context["partner_org_devices_count"] = self.partner.partners_own_org.installed_devices_count

        context["display_certifications_widget_non_empty_state"] = (
            self.display_certification_widget_non_empty_state(analytic.certifications_count if hasattr(self.partner, 'analytics') else 0)
        )
        return context

    def display_certification_widget_non_empty_state(self, all_certifications_count: int) -> bool:
        """
        Returns whether certification widget should show empty state or not
        """
        if all_certifications_count > 1:
            return True

        if all_certifications_count == 0:
            return False

        # all below deals with single certification present in analytics
        if not self.partner.partners_own_org:
            # covers the edge case when the only cert is not from partner own org (e.g. another org created first before partner org onboarded it's own)
            return True

        # check for analytics desync
        default_certifications = self.partner.get_certifications_of(cert_type=self.partner.default_certifications)
        analytics_desync = all_certifications_count != default_certifications.count()

        if analytics_desync: # in case of analytics desync, make sure we're checking against partner's own org
            default_certifications = default_certifications.filter(organisation=self.partner.partners_own_org)

        cert = default_certifications.first()
        # make sure the certification exists, belongs to partner's own org and is not ready for assessment
        # if all these conditions are met, we can show the widget, otherwise we show the empty state
        return (
            cert
            and cert.organisation == self.partner.partners_own_org
            and cert.status not in OrganisationCertification.READY_FOR_ASSESSMENT_STATUSES
        )


class PartnerOrganisationsView(BaseOrganisationsView, PartnerViewAccessMixin, BetaViewMixin):
    template_name = "partners/organisations/main.html"

    def get_object_id(self):
        """ see BaseOrganisationsView """
        return self.partner.id

    def get_model_filter_name(self):
        """ see BaseOrganisationsView """
        return "partner"

    def get_context_data(self, *, object_list=None, **kwargs):
        context = super().get_context_data(object_list=object_list, **kwargs)
        context["analytic"] = self.partner.analytics if hasattr(self.partner, "analytics") else None
        context["organisations_url"] = reverse('partners:organisations')
        if self.distributor_viewing_partner_page:
            context["organisations_url"] = reverse(
                'partners:organisations', kwargs={'partner_id': self.partner.secure_id}
            )
        context["metrics_view_url"] = context["organisations_url"]
        context["organisation_ids"] = list(self.object_list.values_list('id', flat=True))
        context['filtered'] = True if context.get('param_filtering') or context.get('param_search') else False
        context.update(self.get_beta_context(self.partner))
        return context


class BrandingView(LoginRequiredMixin, UpdateView):
    template_name = 'partners/branding.html'
    model = Partner
    form_class = BrandingForm
    success_url = reverse_lazy('partners:branding')
    context_object_name = 'partner_instance'

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        if not request.user.profile.is_partner:
            return HttpResponseRedirect(reverse("dashboard:home"))
        return super(BrandingView, self).dispatch(request, *args, **kwargs)

    def get_object(self, queryset=None):
        return self.request.user.profile.partner

    def get_context_data(self, **kwargs):
        context = super(BrandingView, self).get_context_data(**kwargs)
        # here we need to pass the Partner User to the template in order to get breadcrumbs
        context.update({'partner_user': self.request.user.profile.partner_user})
        return context

    def form_valid(self, form):
        # save object first so we don't update Trustd if save fails
        response = super().form_valid(form)
        changed_logos = {}
        if 'light_logo' in form.changed_data:
            changed_logos['light_logo'] = self.object.absolute_logo_url
        if 'dark_logo' in form.changed_data:
            changed_logos['dark_logo'] = self.object.absolute_dark_logo_url
        if changed_logos:
            update_partner_logo_trustd.delay(partner_id=self.object.id, changed_logos=changed_logos)
        return response


class OrganisationsCSVReportView(BaseCSVReportView, PartnerViewAccessMixin):
    """ Generates CSV report with all Organisations."""

    type_of_csv = CSVFile.TYPES_OF_CSV.ORGANISATIONS
    csv_generation_task = generate_partner_org_csv
    objects_name = 'organisation_ids'
    main_entity_name = 'partner_id'


class RequestCEPlusAuditView(SignupMixin, FormView):
    # todo: legacy view, remove after migration to the new CE+ audit request flow
    http_method_names = ("post",)
    form_class = RequestCEPlusAuditForm

    def form_valid(self, form):
        org = get_organisations(self.request.user, secure_id=self.kwargs.get("org_id"), as_queryset=True).first()
        data = form.data
        if org:
            try:
                api = SalesForceApi()
                response = api.send_ce_plus_audit_request(
                    cert_provider=data.get("valid_ce_certificate"),
                    organisation=data.get("organisation_name"),
                    first_name=data.get("first_name"),
                    last_name=data.get("last_name"),
                    email=data.get("email"),
                    cert_date=data.get("ce_date_achieved"),
                    desktop_devices=data.get("desktop_devices_in_scope"),
                    mobile_devices=data.get("mobile_devices_in_scope"),
                )
                if not response.get("success"):
                    raise Exception(response)
            except Exception as error:
                logger.error(f"Error sending CE+ audit request to Salesforce: {error}")
                messages.error(self.request, _(
                    "There was an error sending your request. Please try again later or contact support."
                ))
            else:
                messages.success(self.request, _(
                    "You have successfully requested a Cyber Essentials Plus audit."
                    " Please wait for someone to contact you"
                ))
                org.cep_audit_request_date = timezone.now()
                org.save()

                send_request_cep_audit_email.delay(
                    org_id=org.id, requester_user_id=self.request.user.id)

        return HttpResponseRedirect(self.request.headers.get("referer"))

    def form_invalid(self, form):
        # add form errors to messages with proper field labels
        for field, errors in form.errors.items():
            for error in errors:
                messages.error(self.request, f"{form.fields[field].label}: {error}")
        return HttpResponseRedirect(self.request.headers.get("referer"))


class BulkCreateOrganisationsView(SignupMixin, FormView):
    """ This handles the bulk creation of organisations by a partner. """
    template_name = 'dashboard/organisation-create.html'

    def dispatch(self, request, *args, **kwargs):
        self.partner = request.user.profile.partner
        if not self.partner and not self.partner.bulk_create_organisations:
            return HttpResponseRedirect(reverse('dashboard:home'))
        return super().dispatch(request, *args, **kwargs)

    def get_success_url(self):
        return reverse('partners:create-organisation')

    def form_invalid(self, form):
        return HttpResponseRedirect(self.get_success_url())

    def get_form_class(self):
        return formset_factory(BulkCreateOrganisationForm, can_delete=False, extra=1)

    def post(self, request, *args, **kwargs):
        """ Create organisations from the formset. """
        formset = self.get_form()
        orgs_failed_to_create = []
        orgs_created = 0
        for form in formset.forms:
            form.partner = self.partner
            if form.is_valid():
                form.create_organisation(request)
                orgs_created += 1
            else:
                orgs_failed_to_create.append(form['name'].value())

        update_partner_analytics.delay(self.partner.id)
        if formset.is_valid():
            messages.add_message(request, messages.SUCCESS, _(f"Successfully created {orgs_created} organisations."))
            return self.form_valid(formset)

        messages.add_message(request, messages.ERROR,
                             _(f"Failed to create the following organisations: {', '.join(orgs_failed_to_create)}."))
        return self.form_invalid(formset)


class CyberSmartAPIView(PartnerViewAccessMixin, TemplateView):
    """
    View to generate or regenerate OAuth2 credentials for the partner's CyberSmart API integration.
    """
    template_name = "partners/cybersmart_api.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            "oauth_app": Oauth_Application.objects.filter(name=get_partner_oauth_application_name(self.partner)).first(),
            "partner_user": self.request.user.profile.partner_user
        })
        return context

    def post(self, request, *args, **kwargs):
        try:
            _, client_id, client_secret = create_or_update_oauth_application_for_partner(
                self.partner, regenerate_secret=True
            )
        except ValueError as e:
            return JsonResponse({"error": str(e)}, status=400)
        else:
            return JsonResponse({
                "client_id": client_id,
                "client_secret": client_secret,
            })


class CreateOrganisation(SignupMixin, FormView):
    template_name = 'dashboard/organisation-create.html'
    form_class = AddOrganisationForm

    def get_success_url(self):
        return reverse('partners:organisations')

    def get(self, request, *args, **kwargs):
        # allow access only for partners, distributors
        if not request.user.profile.is_partner and not request.user.profile.is_distributor:
            return HttpResponseRedirect(reverse('dashboard:home'))
        else:
            return super().get(self, request, *args, **kwargs)

    def send_notification(self, organisation):
        """ Send notification about organisation creation """
        kwargs = {
            'user_name': get_full_name_or_email(self.request.user),
            'organisation_name': organisation.name,
            'organisation_id': organisation.id,
            'partner_name': organisation.partner.name,
            'url': reverse('dashboard:organisation', kwargs={'org_id': organisation.secure_id})
        }
        NotificationHandler.send_notification(message_type='org_creation', **kwargs)
        messages.add_message(self.request, messages.SUCCESS, mark_safe(
            render_to_string('partials/org_created_message.html', {'organisation': organisation, 'perms': PEOPLE_AND_ORGANISATION_PERMISSION})
        ))

    def form_valid(self, form):
        self.organisation = form.create_organisation(self.request)
        self.send_notification(self.organisation)
        return super(CreateOrganisation, self).form_valid(form)

    def form_invalid(self, form):
        context = self.get_context_data(form=form)
        admins = self.request.POST.getlist("custom_admins")
        if admins:
            context["custom_admins"] = admins
        return self.render_to_response(context)

    def get_form_kwargs(self):
        kwargs = super(CreateOrganisation, self).get_form_kwargs()
        kwargs.update({'request': self.request})
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['bulk_add_orgs_form'] = formset_factory(
            BulkCreateOrganisationForm, can_delete=False, extra=1
        )
        return context
