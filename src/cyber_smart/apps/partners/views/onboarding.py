import stripe
from allauth.socialaccount.models import SocialToken, SocialAccount
from django import forms
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import HttpResponseRedirect, JsonResponse
from django.urls import reverse_lazy
from django.utils.translation import gettext, gettext_lazy as _
from django.views.generic.edit import FormView

from accounts.models import EventLog
from appusers.forms import AppUserEnrollFormset
from appusers.utils import send_apps
from common.utils import handle_user_input
from common.validators import domain_name_validator
from organisations.forms import OrganisationForm, AddOrganisationForm
from organisations.models import (
    Organisation, OrganisationUserSync
)
from organisations.utils import is_generic_domain
from rulebook.models import CYBER_ESSENTIALS, GDPR, NIS2
from rulebook.utils import get_latest_version
from signup.mixins import SignupMixin

stripe.api_key = settings.STRIPE_SECRET


class OrganisationView(SignupMixin, FormView):
    template_name = 'partners/onboarding/organisation.html'
    form_class = OrganisationForm
    success_url = reverse_lazy('partners:enroll')

    def form_valid(self, form):
        form.create_organisation(self.request)
        return super(OrganisationView, self).form_valid(form)


class SetupOrganisationMixin(object):

    def _set_onboarding_as_completed(self):
        """
        Sets onboarding as completed
        :return: nothing
        :rtype: None
        """
        self.request.user.profile.onboarding_completed = True
        self.request.user.profile.save()

    def _create_finished_onboarding_event_log(self, organisation):
        """
        Creates an event log which indicates finished onboarding for organisation.
        :param organisation: user organisation
        :type organisation: organisations.Organisation
        :return: nothing
        :rtype: None
        """
        EventLog.objects.create(
            user=self.request.user,
            organisation=organisation,
            type=EventLog.COMPLETED_ONBOARDING
        )

    @staticmethod
    def _create_certifications(organisation):
        """
        Creates needed certifications.
        :param organisation: user organisation
        :type organisation: organisations.Organisation
        :return: nothing
        :rtype: None
        """
        if organisation.has_certification_subscription or organisation.partner:
            cert = organisation.partner.default_certifications
            if cert == CYBER_ESSENTIALS:
                organisation.certifications.create(
                    version=get_latest_version(type=CYBER_ESSENTIALS)
                )
            if cert == GDPR:
                organisation.certifications.create(
                    version=get_latest_version(type=CYBER_ESSENTIALS)
                )
                organisation.data_privacy_support = True
                organisation.save()
            if cert == NIS2:
                organisation.certifications.create(
                    version=get_latest_version(type=NIS2)
                )

    def setup_organisation(self, organisation):
        """
        Setup an organisation.
        :param organisation: user organisation
        :type organisation: organisations.Organisation
        :return: nothing
        :rtype: None
        """
        # save default admin message
        if organisation.size == Organisation.ORGANISATION_SIZE_1:
            organisation.email_message = "You are the only admin for your CyberSmart account. Let's get certified!"

        # create needed certifications
        self._create_certifications(organisation)

        if not organisation.bulk_install:
            # send apps to users
            send_apps(organisation, first_email=True)

        # finish onboarding
        self._set_onboarding_as_completed()
        self._create_finished_onboarding_event_log(organisation)


class EnrollView(SignupMixin, SetupOrganisationMixin, FormView):
    template_name = 'partners/onboarding/enrol.html'
    form_class = AppUserEnrollFormset
    success_url = reverse_lazy('dashboard:home')

    def _skip_enroll_for_organisation(self, organisation):
        """
        We skip enroll page for organisations with just one user.
        If passed organisation's size is equal to 1 skip enroll page and setup organisation.
        :param organisation: user organisation
        :type organisation: organisations.Organisation
        :return: True or False
        :rtype: bool
        """
        if not self.request.user.profile.onboarding_completed:
            if organisation.size == Organisation.ORGANISATION_SIZE_1:
                self.setup_organisation(organisation)
                return True
        return False

    def _save_organisation_enrollment_type(self, organisation):
        """
        Saves organisation enrollment type and default email message.
        :param organisation: user organisation
        :type organisation: organisations.Organisation
        :return: nothing
        :rtype: None
        """
        organisation.email_message = handle_user_input(self.request.POST.get('email_message', ''))
        bulk_install = self.request.POST.get('bulk_install', 'False') == 'True'
        if bulk_install:
            organisation.get_or_create_bulk_deploy_user()
        organisation.bulk_install = bulk_install

        organisation.save()

    def _email_message_is_incorrect(self, organisation):
        """
        Returns True if email message is incorrect otherwise returns False.
        :param organisation: user organisation
        :type organisation: organisations.Organisation
        :return: True or False
        :rtype: bool
        """
        if not organisation.bulk_install:
            if organisation.email_message:
                if gettext("[SECRET]") in organisation.email_message:
                    messages.add_message(
                        self.request,
                        messages.ERROR,
                        _('Please replace the word [SECRET] for your own word that you tell staff to look out for!')
                    )
                    return True
        return False

    def _domain_name_is_incorrect(self, organisation, form):
        """
        Returns context with error message if domain name is incorrect otherwise create an organisation domain name.
        :param organisation: user organisation
        :type organisation: organisations.Organisation
        :return: error context
        :rtype: dict
        """
        error_context = {}
        context = self.get_context_data(form=form)
        if organisation.bulk_install:
            approved_domain = self.request.POST.get('approved_domain')
            try:
                domain_name_validator(approved_domain)
            except forms.ValidationError:
                messages.add_message(
                    self.request,
                    messages.ERROR,
                    _('Approved domain is not a valid domain name')
                )
                error_context['approved_domain'] = approved_domain
                return dict(context, **error_context)
            else:
                error_message = ''
                if is_generic_domain(approved_domain):
                    error_message = _("This generic domain is not allowed. Make sure to use your company’s domain. "
                                      "If you do not have a company domain, contact us on live chat.")

                if error_message:
                    messages.add_message(
                        self.request,
                        messages.ERROR,
                        error_message
                    )
                    context['approved_domain'] = approved_domain
                    return dict(context, **error_context)

                organisation.approved_domains.get_or_create(
                    domain=approved_domain
                )
        return error_context

    def get_context_data(self, **kwargs):
        context = super(EnrollView, self).get_context_data(**kwargs)
        context['approved_domain'] = self.request.user.profile.email_domain
        context['email_message_field'] = AddOrganisationForm(request=self.request).fields['email_message']
        return context

    def get(self, request, *args, **kwargs):
        if self._skip_enroll_for_organisation(request.user.profile.organisation):
            return HttpResponseRedirect(self.success_url)
        return super(EnrollView, self).get(request, *args, **kwargs)

    def form_valid(self, form):
        organisation = self.request.user.profile.get_organisations.first()

        # save initial organisation parameters
        self._save_organisation_enrollment_type(organisation)

        # check if email message is correct
        if self._email_message_is_incorrect(organisation):
            return self.render_to_response(self.get_context_data(form=form))

        # check if domain name is correct and save it
        error_context = self._domain_name_is_incorrect(organisation, form)
        if error_context:
            return self.render_to_response(error_context)

        # validate users form
        try:
            for each_form in form:
                if each_form is not None:
                    each_form.update_data(self, organisation)
        except forms.ValidationError as error:
            messages.add_message(self.request, messages.ERROR, error.message)
        else:
            # if all is good setup organisation
            self.setup_organisation(organisation)

        return super(EnrollView, self).form_valid(form)


@login_required
def has_cloud_contacts(request):
    user = request.user
    response = {'status': 0, 'data': False}
    try:
        social_account = user.socialaccount_set.get()
        response = {'status': 1, 'data': True,
                    'provider': social_account.provider}
    except SocialAccount.DoesNotExist:
        social_account = None
    return JsonResponse(response)


# first we need to make sure there is a org with secure_id
# then we need to check if the org has sync obj if not will create one
# lastly will enable auto sync
# we need to pass the social account also figure a way if the org has two social accounts.
@login_required
def enable_auto_sync(request):
    response = {'status': 0}
    secure_id = request.GET.get('secure_id')
    social_id = request.GET.get('social_id')
    if not (secure_id and social_id):
        return JsonResponse(response)

    try:
        organisation = Organisation.objects.get(secure_id=secure_id)
        social_token = SocialToken.objects.get(account__pk=social_id)
        if not hasattr(organisation, 'OrganisationUserSync'):
            sync_obj = OrganisationUserSync.objects.create(organisation=organisation)
        else:
            sync_obj = organisation.organisationusersync
        response = {'status': 1}
    except Exception:
        pass
    else:
        if not sync_obj.sync_enabled:
            sync_obj.enable_auto_sync(social_token)
    return JsonResponse(response)
