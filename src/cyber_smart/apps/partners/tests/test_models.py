from django.test import TestCase
from waffle.testutils import override_switch

from analytics.factories import OrganisationAnalyticsFactory
from appusers.models import AppInstall
from beta_features.factories import BetaFeatureFactory, BetaEnrolmentFactory
from beta_features.models import CAP_V_FIVE_BETA_SWITCH, TRUSTD_BETA_SWITCH
from common.base_tests import BaseTestCase
from organisations.factories import OrganisationFactory
from organisations.models import Organisation, OrganisationSettings
from partners.factories import PartnerFactory
from trustd.factories import TrustdDeviceFactory


@override_switch(CAP_V_FIVE_BETA_SWITCH, active=True)
@override_switch(TRUSTD_BETA_SWITCH, active=True)
class PartnerTestCase(BaseTestCase, TestCase):
    def setUp(self):
        self.core_setting_up()
        self.creating_user()
        self.create_partner()

    def teardown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.partner.delete()

    def create_partner(self):
        self.partner = PartnerFactory.create(
            name="Beta Partner",
            distributor=self.direct_distributor
        )

    def create_org(self):
        organisation = OrganisationFactory(
            industry="CONS",
            size=Organisation.ORGANISATION_SIZE_5_9,
            bulk_install=False,
            partner=self.partner,
            software_support=True
        )
        OrganisationSettings.objects.get_or_create(organization=organisation)
        return organisation

    def test_get_enrolled_beta_features(self):
        beta_feature = BetaFeatureFactory(kind=CAP_V_FIVE_BETA_SWITCH)
        BetaEnrolmentFactory(feature=beta_feature, partner = self.direct_partner)
        self.assertEqual(self.direct_partner.get_enrolled_beta_features().first(), beta_feature)

    def test_beta_organisations_count(self):
        org1 = self.create_org()
        user1 = self.create_cap_user(organisation=org1)
        self.create_cap(
            app_user=user1,
            app_version="4.0.0",
            device_type=AppInstall.DESKTOP,
            platform="win32"
        )
        self.create_cap(
            app_user=user1,
            app_version="4.2.0",
            device_type=AppInstall.MOBILE,
            platform="android"
        )
        org2 = self.create_org()
        user2 = self.create_cap_user(organisation=org2)
        self.create_cap(
            app_user=user2,
            app_version="5.0.0",
            device_type=AppInstall.DESKTOP,
            platform="win32"
        )
        self.create_cap(
            app_user=user2,
            app_version="4.2.0",
            device_type=AppInstall.MOBILE,
            platform="ios"
        )
        org3 = self.create_org()
        user3 = self.create_cap_user(organisation=org3)
        self.create_cap(
            app_user=user3,
            app_version="5.0.0",
            device_type=AppInstall.DESKTOP,
            platform="win32"
        )
        TrustdDeviceFactory.create(app_install=self.create_cap(
            app_user=user3,
            app_version="4.2.0",
            device_type=AppInstall.MOBILE,
            platform="ios"
        ))
        org3.analytics = OrganisationAnalyticsFactory.create(
            organisation=org3,
            desktop_devices_count=1,
            mobile_devices_count=1,
            total_users_count=1,
            total_beta_users_count=0
        )
        self.assertEqual(self.partner.organisations_count, 3)
        self.assertEqual(self.partner.beta_organisations_count, 0)
        org3.analytics.total_beta_users_count = 1
        org3.analytics.save()
        self.enable_cap_v5_and_trustd_beta(org1, org2, org3)
        self.assertEqual(self.partner.organisations_count, 3)
        self.assertEqual(self.partner.beta_organisations_count, 1)
