import uuid
from datetime import <PERSON><PERSON><PERSON>
from typing import Optional
from urllib.parse import urljoin

import waffle
from ckeditor_uploader.fields import RichTextUploading<PERSON>ield
from colorful.fields import RGBColorField
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.sites.models import Site
from django.core.validators import RegexValidator
from django.db import models
from django.db.models import Avg, Sum, F
from django.dispatch import receiver
from django.shortcuts import reverse
from django.utils import timezone
from django.utils.text import slugify
from django.utils.translation import get_language_info
from model_utils.models import TimeStampedModel

from analytics.models import AppInstallAnalytics, OrganisationAnalytics
from appusers.models import AppInstall, AppReport
from beta_features.mixins import BetaDevicesMetricsMethodsMixin
from beta_features.models import BetaFeature
from billing.mixins import BillingEnabledModelMixin
from common.mixins import DevicesMetricsMethodsMixin
from common.models import CRUDSignalMixin, LogoMixin, SecureIDMixin, ProjectSettings
from common.signals import model_crud
from common.utils import get_http_protocol_url_name
from organisations.mixins import OrganisationDefaultFeatures
from organisations.models import Organisation, OrganisationCertification, OrganisationSmartScore
from organisations.utils import exclude_finished_renewed_certs
from regions.utils import is_eu_geo
from rulebook.models import (CERTIFICATES, SurveyDeclaration)

# Generate dynamic language choices from settings.LANGUAGES
LANGUAGE_CHOICES = [
    (lang_code, get_language_info(lang_code)["name"])
    for lang_code, _ in settings.LANGUAGES
]



def get_default_certifications():
    """
    Return default certifications depending on the geo environment.
    Note: do not use before models are loaded,
    e.g. in model choices as it depends on waffle flag model being loaded.
    """
    DEFAULT_CERTIFICATIONS = (
        (Organisation.CYBER_ESSENTIALS, 'Cyber Essentials'),
        (Organisation.CYBER_ESSENTIALS__CYBER_ESSENTIALS_PLUS__GDPR, 'Cyber Essentials & Privacy Toolbox')
    )
    if settings.IS_AUS_GEO:
        DEFAULT_CERTIFICATIONS = (
            (Organisation.ESSENTIAL_EIGHT, CERTIFICATES[Organisation.ESSENTIAL_EIGHT]),
        )
    if is_eu_geo():
        DEFAULT_CERTIFICATIONS = (
            (Organisation.NIS2, CERTIFICATES[Organisation.NIS2]),
        )
    return DEFAULT_CERTIFICATIONS

DEFAULT_SOFTWARE = (
    (0, 'CyberSmart'),
    (1, 'CyberSmart Pro')
)

AVAILABLE_BILLING_MODELS = (
    (0, 'v4 Unbundled only'),
    (1, 'v4 Unbundled and v5 Bundles'),
    (2, 'v5 Bundles only'),
)

PRICING_BAND_PARTNER = Organisation.PRICING_BAND_DISTRIBUTOR


class Partner(
    TimeStampedModel, SecureIDMixin, CRUDSignalMixin, BillingEnabledModelMixin,
    LogoMixin, OrganisationDefaultFeatures, DevicesMetricsMethodsMixin, BetaDevicesMetricsMethodsMixin
):
    """
    This model represents a partner entity.
    Partner can have multiple users and linked to one distributor.
    """
    api_rate_limit_validator = RegexValidator(
        regex=r'^\d+/(second|minute|hour|day)$',
        message='API rate limit must be in the format "<number>/second|minute|hour|day", e.g., "100/hour".'
    )

    CYBERSMART_COLOR = '#0F222D'
    CYBERSMART_FONT_COLOR = '#ffffff'
    VODAFONE_COLOR = '#e60000'
    name = models.CharField(verbose_name='Partner name', max_length=255, null=True, blank=True)
    distributor = models.ForeignKey('distributors.Distributor', related_name='partners', on_delete=models.CASCADE)
    iasme_cb = models.BooleanField(verbose_name='IASME CB', default=False)
    trial_account = models.BooleanField(verbose_name='Trial account', default=False)
    enforce_mfa = models.BooleanField(
        verbose_name='Force MFA Authentication', default=False)
    default_pricing_band = models.IntegerField(
        verbose_name='Default band for new organisations',
        choices=PRICING_BAND_PARTNER,
        default=Organisation.PRICING_MONTHLY_V4
    )
    default_certifications = models.IntegerField(
        verbose_name='Default certifications for new organisations',
        choices=(
            (Organisation.CYBER_ESSENTIALS, 'Cyber Essentials'),
            (Organisation.CYBER_ESSENTIALS__CYBER_ESSENTIALS_PLUS__GDPR, 'Cyber Essentials & Privacy Toolbox'),
            (Organisation.ESSENTIAL_EIGHT, CERTIFICATES[Organisation.ESSENTIAL_EIGHT]),
            (Organisation.NIS2, CERTIFICATES[Organisation.NIS2])
        ),
        default=None,
        null=True,
        blank=True
    )
    default_software = models.IntegerField(
        verbose_name='Default software plan for new organisations',
        choices=DEFAULT_SOFTWARE,
        default=None,
        null=True,
        blank=True
    )
    available_billing_models = models.IntegerField(
        verbose_name='Available billing models for new organisations',
        choices=AVAILABLE_BILLING_MODELS,
        default=1,
        null=True,
        blank=True
    )
    license_number = models.IntegerField(verbose_name='Number Of License', blank=True, null=True)
    license_price = models.DecimalField(verbose_name='Per License Price', max_digits=6, decimal_places=2, blank=True, null=True)
    ce_price = models.DecimalField(verbose_name='Cyber Essentials Price', max_digits=6, decimal_places=2, blank=True, null=True)
    gdpr_price = models.DecimalField(verbose_name='GDPR Price', max_digits=6, decimal_places=2, blank=True, null=True)
    not_for_resale = models.BooleanField(verbose_name='Partner own organisation has free software (NFR)', default=False)
    free_mobile_apps = models.BooleanField(verbose_name='Free Mobile Apps', default=False)
    include_100k_insurance = models.BooleanField(
        verbose_name='Upgrade partner to include £100K insurance upgrade', default=False
    )
    bulk_create_organisations = models.BooleanField(verbose_name='Can create organisations in bulk', default=False)
    frozen = models.BooleanField(
        help_text=(
            'Freeze partner account (Stops organisations under this partner from submitting any questionnaire)'
        ),
        default=False
    )
    note = models.TextField(verbose_name='Note', null=True, blank=True)
    asm = models.ForeignKey(
        'sales.AccountSalesManager', related_name='partners', verbose_name="Account Sales Manager",
        null=True, blank=True, on_delete=models.SET_NULL)
    buying_groups = models.ManyToManyField(
        'sales.BuyingGroup', related_name='partners', blank=True)
    header_background_color = RGBColorField(colors=[CYBERSMART_COLOR, VODAFONE_COLOR], default=CYBERSMART_COLOR)
    header_font_color = RGBColorField(colors=[CYBERSMART_FONT_COLOR], default=CYBERSMART_FONT_COLOR)
    external_uuid = models.UUIDField(unique=True, default=uuid.uuid4, editable=False)
    jem_id = models.CharField(verbose_name='JEM ID', max_length=99, null=True, blank=True)
    custom_api_rate_limit = models.CharField(
        max_length=20,
        validators=[api_rate_limit_validator],
        blank=True,
        help_text='Custom API rate limit in the format "<number>/second|minute|hour|day", e.g., "100/hour".'
    )

    class Meta:
        verbose_name = 'Partner'
        verbose_name_plural = 'Partners'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


    def __unicode__(self):
        return '{0} [Partner]'.format(self.name)

    def __str__(self):
        return self.__unicode__()

    @property
    def main_entity_filter_name(self) -> str:
        """
        Returns the name of the main entity filter for DevicesMetricsMethodsMixin.
        """
        return DevicesMetricsMethodsMixin.PARTNER

    def save(self, *args, **kwargs):
        if self.pk is not None:
            orig = Partner.objects.get(pk=self.pk)
            if orig.trial_account != self.trial_account and not self.trial_account:
                # on trial end, move all org trial certs to pre billing status
                # so they can be billed as non-trial certs
                reset_certifications_on_trial_end(self)
        super(Partner, self).save(*args, **kwargs)

    @property
    def edit_url(self):
        """
        Returns edit url.
        :return: edit url
        :rtype: str
        """
        return reverse('distributors:update-partner', kwargs={'secure_id': self.secure_id})

    @property
    def absolute_logo_url(self):
        """
        Returns absolute logo url with domain name and http schema if exists otherwise returns None.
        :return: marketing pack logo
        :rtype: str or None
        """
        if self.light_logo:
            return urljoin(
                '{0}://{1}'.format(get_http_protocol_url_name(), Site.objects.get_current().domain),
                self.light_logo.url  # pylint: disable=no-member
            )
        else:
            return None

    @property
    def absolute_dark_logo_url(self):
        """
        Returns absolute dark logo url with domain name and http schema if exists otherwise returns None.
        :return: marketing pack logo
        :rtype: str or None
        """
        if self.dark_logo:
            return urljoin(
                '{0}://{1}'.format(get_http_protocol_url_name(), Site.objects.get_current().domain),
                self.dark_logo.url  # pylint: disable=no-member
            )
        else:
            return None

    def get_asm_name(self):
        asm = self.asm
        return asm.full_name if asm else ""

    def get_buying_groups(self):
        buying_groups = self.buying_groups.all()
        if buying_groups:
            return ", ".join(group.name for group in buying_groups)
        return ""

    @property
    def organisations_count(self) -> int:
        """
        Returns the total number of organisations under partner.
        """
        return self.organisations.count()

    @property
    def beta_organisations(self) -> models.QuerySet:
        """
        Returns all beta organisations under partner.
        It means organisations that have only beta CAP installed.
        """
        return self.organisations.filter(
            analytics__total_beta_users_count__gt=0,
            analytics__total_users_count=F("analytics__total_beta_users_count")
        )

    @property
    def beta_organisations_count(self) -> int:
        """
        Returns the total number of beta organisations under partner.
        """
        return self.beta_organisations.count()

    @property
    def secure_organisations(self):
        """
        Returns all secure organisations under partner.
        """
        secure_analytics = OrganisationAnalytics.get_secure_analytics_queryset(self.pk)
        return Organisation.objects.filter(pk__in=secure_analytics.values_list("organisation__pk", flat=True))

    @property
    def secure_organisations_count(self) -> int:
        """
        Returns the total number of secure organisations under partner.
        """
        return self.secure_organisations.count()

    @property
    def secure_beta_organisations(self):
        """
        Returns all secure beta organisations under partner.
        """
        return self.secure_organisations.filter(pk__in=self.beta_organisations.values_list("pk", flat=True))

    @property
    def secure_beta_organisations_count(self) -> int:
        """
        Returns the total number of secure beta organisations under partner.
        """
        return self.secure_beta_organisations.count()

    @property
    def insecure_organisations(self):
        """
        Returns all insecure organisations under partner.
        """
        return self.organisations.exclude(pk__in=self.secure_organisations.values_list("pk", flat=True))

    @property
    def insecure_organisations_count(self) -> int:
        """
        Returns the total number of insecure organisations under partner.
        """
        return self.insecure_organisations.count()

    @property
    def average_smart_score_percentage(self) -> float:
        """
        Returns the average smart score percentage of all organisations under partner.
        """
        return round(
            self.organisations.exclude(
                pk__in=self.beta_organisations.values_list("pk", flat=True)
            ).aggregate(Avg("smart_score__overall"))["smart_score__overall__avg"] or 0.0, 2
        )

    @property
    def has_any_organisation_smart_score(self) -> bool:
        """
        Returns True if any organisation under partner has smart score otherwise returns False.
        """
        return OrganisationSmartScore.objects.filter(organisation__partner=self).exists()

    def get_certifications_of(self, cert_type: int = None, status: list = None) -> models.QuerySet[OrganisationCertification]:
        """
        Returns all certifications under partner given cert type and status criteria.
        """
        filters = {"organisation__partner": self}
        if cert_type:
            filters["version__type__type"] = cert_type
        if status:
            filters["status__in"] = status
        return exclude_finished_renewed_certs(OrganisationCertification.objects.filter(**filters))

    def get_certifications_count(self, cert_type: int = None, status: list = None) -> int:
        """
        Returns the total number of certifications under partner given cert type and status criteria.
        """
        return self.get_certifications_of(cert_type=cert_type, status=status).count()

    @property
    def certified_organisations_count(self) -> int:
        """
        Returns the total number of certified organisations under partner.
        """
        return OrganisationCertification.objects.filter(
            organisation__partner_id=self.pk,
            status__in=OrganisationCertification.CERTIFIED_STATUSES,
        ).order_by('organisation').distinct("organisation").count()

    @property
    def expiring_soon_organisations_count(self) -> int:
        """
        Returns the total number of organisations that have certifications that will expire soon.
        """
        return OrganisationCertification.objects.filter(
            organisation__partner=self,
            status=OrganisationCertification.EXPIRING,
        ).order_by('organisation').distinct("organisation").count()

    def organisations_with_expired_certs_count(self, exclude_renewed_certificates: bool = True) -> int:
        """
        Returns the total number of organisations that have certifications that have expired.
        Optionally, exclude renewed certificates.
        """
        qs = OrganisationCertification.objects.filter(
            organisation__partner=self,
            status=OrganisationCertification.EXPIRED,
        )

        if exclude_renewed_certificates:
            qs = exclude_finished_renewed_certs(qs)

        return qs.order_by('organisation').distinct("organisation").count()

    @property
    def issued_certificates(self):
        """
        Returns all issued certificates under partner.
        """
        from rulebook.models import IssuedCertification
        return IssuedCertification.objects.filter(
            certificate__organisation__partner=self
        )

    @property
    def issued_certificates_count(self) -> int:
        """
        Returns the total number of issued certificates under partner.
        """
        return self.issued_certificates.count()

    def update_analytics(self):
        """
        Updates partner's analytics.
        :return: nothing
        :rtype: None
        """
        from analytics.tasks import update_partner_analytics
        update_partner_analytics.delay(self.pk)

    @property
    def has_billing(self):
        """
        Returns True if this partner has chargebee billing info otherwise returns False.
        :return: True of False
        :rtype: bool
        """
        return hasattr(self, 'billing')

    @property
    def is_direct_partner(self):
        return self.distributor.name == settings.DEFAULT_CS_DIST_NAME or self.distributor.is_synaxon

    @property
    def distributor_is_brigantia(self):
        """
        Returns if partner has distributor named Brigantia or UC-Reseller
        :return: True or False
        :rtype: bool
        """
        return self.distributor and (self.distributor.name == 'Brigantia' or self.distributor.name == 'UC-Reseller')

    @property
    def is_v3_pricing_band(self):
        return self.default_pricing_band in [Organisation.PRICING_ANNUAL, Organisation.PRICING_MONTHLY]

    def is_bundle_enabled(self):
        """
        flag for disable v5 bundles creation if the partner BSI or AJG or v4 only available
        :return: True or False
        """
        if settings.IS_AUS_GEO or is_eu_geo():
            return False
        return self.name not in ('BSI', 'Arthur J Gallagher') and not self.is_v4_only_available()

    def is_v4_only_available(self):
        """
        Returns True if partner has only v4 bundles available otherwise returns False
        """
        return self.available_billing_models == 0

    def is_v5_only_available(self):
        """
        Returns True if partner has only v5 bundles available otherwise returns False
        """
        return self.available_billing_models == 2

    @property
    def app_installs(self):
        return AppInstall.objects.installs_without_deprecated_duplicates_for_partner(self)

    @property
    def active_app_installs(self):
        return self.app_installs.active()

    def get_total_licenses_for_pricing_band(self, pricing_band):
        """
        Returns total number of devices installed for all organisations on given model
        :rtype: int
        """
        total_installs = None
        # Note: this statment for testing enviroment only.
        # we're using it to simulate large number of app installs for testing billing
        if settings.IS_STAGE or settings.IS_DEVELOP or settings.DEBUG:
            queryset = self.organisations.filter(pricing_band=pricing_band)
            if self.not_for_resale:
                queryset = queryset.exclude(is_partner_org=True)

            total_installs = queryset.aggregate(
                total_installs=Sum('simulate_installed_devices_count'))['total_installs']

        if not total_installs:
            queryset = self.active_app_installs.filter(
                app_user__organisation__pricing_band=pricing_band,
            )
            if self.not_for_resale:
                queryset = queryset.exclude(app_user__organisation__is_partner_org=True)
            total_installs = queryset.count()

        # it should be the greater of license_number or
        # all licenses for partner's organisations (excluding partners own org if NFR)
        if self.license_number and total_installs < self.license_number:
            return self.license_number

        return total_installs

    def calculate_monthly_license_cost_v4(self):
        licenses_installed = self.get_total_licenses_for_pricing_band(
            pricing_band=Organisation.PRICING_MONTHLY_V4)
        if self.is_direct_partner:
            if licenses_installed < 100:
                the_price = 2.85
            elif licenses_installed < 1000:
                the_price = 1.60
            else:
                the_price = 1.30
        elif self.distributor_is_brigantia:
            if licenses_installed < 100:
                the_price = 2.00
            elif licenses_installed < 1000:
                the_price = 1.00
            else:
                the_price = 0.80
        else:
            if licenses_installed < 100:
                the_price = 2.15
            elif licenses_installed < 1000:
                the_price = 1.20
            else:
                the_price = 1.00

        return the_price

    def is_annual_pricing_band(self):
        return self.default_pricing_band in [
            Organisation.PRICING_ANNUAL, Organisation.PRICING_ANNUAL_V2]

    def get_org_signup_url(self):
        return (f'https://cybersmart.co.uk/aviva/?coupon=AVIVABROKER3&p={self.external_uuid}'
                f'&utm_source=avivabrokers&utm_medium=email&utm_campaign={slugify(self.name)}')

    def get_enrolled_beta_features(self, feature_type: Optional[list] = None) -> models.QuerySet:
        """
        Returns a list of enabled beta features for this partner.
        If feature_type is provided, it will return only the features of that type.
        """
        queryset = BetaFeature.objects.filter(enrolments__partner=self)
        if feature_type and isinstance(feature_type, list):
            queryset = queryset.filter(kind__in=feature_type)

        # Can be enrolled only if the Beta Waffle switch is enabled globally
        queryset = queryset.filter(
            kind__in=[
                feature.kind for feature in queryset
                if waffle.switch_is_active(feature.kind)
            ]
        )
        return queryset

    @property
    def partners_own_org(self):
        return self.organisations.filter(is_partner_org=True).order_by('created').first()

    def can_input_coupon_during_org_creation(self):
        """
        Determines if a partner can input a coupon during organisation creation to get a discount.
        """
        if waffle.switch_is_active('always_allow_partner_org_creation_coupon'):
            return True
        return self.distributor.can_use_coupon and not settings.IS_AUS_GEO

    def is_academy_enabled(self) -> bool:
        """
        Returns True if the academy is available for this partner.
        """
        ps = ProjectSettings.objects.first()
        return ps.academy_enabled and self.default_academy_enabled


class PartnerUser(TimeStampedModel, SecureIDMixin, CRUDSignalMixin):
    """
    This model represents a partner user.
    Each partner can have multiple users associated.
    """
    partner = models.ForeignKey(to=Partner, related_name='users', on_delete=models.CASCADE)
    user = models.OneToOneField(to=get_user_model(), related_name='partner', on_delete=models.CASCADE)
    pervade_assessor = models.CharField(verbose_name='Pervade API assessor name', max_length=255, null=True, blank=True)
    pervade_token = models.CharField(verbose_name='Pervade API token', max_length=255, null=True, blank=True)
    pervade_valid = models.BooleanField(
        verbose_name='Pervade organisation name and token are valid', default=None, null=True, blank=True
    )

    class Meta:
        verbose_name = 'Partner User'
        verbose_name_plural = 'Partners Users'

    def __unicode__(self):
        return '{0} {1}'.format(self.partner, self.user)

    def __str__(self):
        return self.__unicode__()

    @property
    def has_wrong_pervade_credentials(self):
        """
        Returns True if partner has wrong pervade credentials otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.pervade_valid is False


    @property
    def has_app_user_on_own_org(self):
        """
        Check if AppUser with same email as partner user exists on partner's own org.
        """
        partner = self.partner
        partner_org = partner.partners_own_org
        if not partner_org:
            return False
        app_users = partner_org.app_users.filter(email=self.user.email)
        return app_users.exists()


class PartnerMarketingPack(TimeStampedModel):
    """
    This model represents partner marketing pack that we use on frontend.
    """
    partner = models.ForeignKey(to=Partner, related_name='marketing_packs', on_delete=models.CASCADE)
    marketing_pack_html = RichTextUploadingField(blank=True, null=True)

    class Meta:
        verbose_name = 'Partner Marketing Pack'
        verbose_name_plural = 'Partner Marketing Packs'

    def __unicode__(self):
        return '{0}'.format(self.partner.name)

    def __str__(self):
        return self.__unicode__()


class PartnerDay(TimeStampedModel):
    """
    Each partner can attend multiple partner days
    Is not currently used. Should we remove it?
    """
    # todo: remove this model
    partner = models.ForeignKey(to=Partner, related_name='partner_day', on_delete=models.CASCADE)
    date = models.DateField("Date held")

    class Meta:
        verbose_name = 'Partner Day'
        verbose_name_plural = 'Partner Days'

    def __unicode__(self):
        return '{0}'.format(self.partner.name)

    def __str__(self):
        return self.__unicode__()


class SecurityControl(TimeStampedModel):
    """
    Contains settings for partner security control.
    """
    FAILED_REPORT = 1
    FAILED_CHECK = 2

    TYPES = (
        (FAILED_REPORT, "Failed report"),
        (FAILED_CHECK, "Failed check")
    )

    partner = models.OneToOneField(to=Partner, related_name="security_control", on_delete=models.CASCADE)
    enabled = models.BooleanField(verbose_name="Enabled", default=True)
    failed_report_enabled = models.BooleanField(verbose_name="Failed report enabled", default=True)
    failed_report_trigger = models.SmallIntegerField(verbose_name="Failed report trigger (days)", default=7)
    failed_report_frequency = models.SmallIntegerField(verbose_name="Email frequency for failed reports", default=1)
    failed_check_enabled = models.BooleanField(verbose_name="Failed check enabled", default=True)
    failed_check_frequency = models.SmallIntegerField(verbose_name="Email frequency for failed reports", default=0)

    class Meta:
        verbose_name = "Partner Security Control"
        verbose_name_plural = "Partner Security Control"

    def __unicode__(self):
        return "{0}".format(self.partner.name)

    def __str__(self):
        return self.__unicode__()

    def devices_failed_reports(self):
        """
        Returns queryset with Partner devices that are not reporting back to the server
        for some period of time.
        This does not include devices that never reported.
        :return: devices that are not reporting
        :rtype: AppInstall queryset
        """
        if not self.enabled or not self.failed_report_enabled or self.failed_report_trigger == 0:
            return AppInstall.objects.none()
        else:
            trigger_data = timezone.now() - timedelta(days=self.failed_report_trigger)
            # first find apps that are reporting
            reporting = AppReport.objects.filter(
                app_install__app_user__organisation__partner=self.partner,
                app_install__app_user__active=True,
                app_install__inactive=False,
                created__gte=trigger_data
            ).distinct("app_install").values_list("app_install__pk", flat=True)
            # then exclude them from partner apps that reported at least once
            return self.partner.active_app_installs.filter(
                reports__isnull=False
            ).exclude(pk__in=reporting).distinct()

    def devices_failed_checks(self):
        """
        Returns queryset with Partner devices that failed checks.
        This does not include devices without reports.
        :return: devices that have failed checks
        :rtype: AppInstall queryset
        """
        if not self.enabled or not self.failed_check_enabled:
            return AppInstall.objects.none()
        else:
            return self.partner.app_installs.filter(pk__in=AppInstallAnalytics.objects.filter(
                app_install__app_user__organisation__partner=self.partner,
                app_install__app_user__active=True,
                app_install__inactive=False,
                latest_pass_percentage__lt=100
            ).distinct().values_list("app_install__pk", flat=True)).distinct()


class SecurityControlEmail(TimeStampedModel):
    security_control = models.ForeignKey(
        to=SecurityControl, verbose_name="Security Control", related_name="emails", on_delete=models.CASCADE
    )
    type = models.SmallIntegerField(verbose_name="Email type", choices=SecurityControl.TYPES)
    email = models.EmailField(verbose_name="Email")
    enabled = models.BooleanField(verbose_name="Enabled", default=True)

    class Meta:
        verbose_name = "Security Control Email"
        verbose_name_plural = "Partner Security Control Emails"

    def __unicode__(self):
        return "{0} | {1} - {2}".format(self.email, self.get_type_display(), self.security_control.partner.name)

    def __str__(self):
        return self.__unicode__()

    @property
    def delete_url(self):
        """
        Returns delete url.
        """
        return reverse("partners:security-controls-email-delete", kwargs={"pk": self.pk})


@receiver(model_crud, sender=Partner, dispatch_uid=uuid.uuid4().hex)
def partner_crud(sender, instance, **kwargs):
    """
    Detects creating, updating, deleting for Partner model.
    :param sender: model sender
    :type sender: Partner
    :param instance: model instance
    :type instance: Partner instance
    :param kwargs: keyword arguments
    :type kwargs: dict
    :return: nothing
    :rtype: None
    """
    if kwargs["post_create"]:
        # create security control settings
        SecurityControl.objects.get_or_create(partner=instance)


def reset_certifications_on_trial_end(instance):
    for organisation in instance.organisations.all():
        statuses = [
                OrganisationCertification.DECLARATION_SIGNED,
                OrganisationCertification.REQUIRES_ATTENTION,
            ]
        statuses += OrganisationCertification.CERTIFIED_STATUSES
        certs = organisation.certifications.exclude(
            status__in=statuses
        )
        certs.update(
            status=OrganisationCertification.NOT_STARTED
        )
        declarations = SurveyDeclaration.objects.filter(
            survey__certificate__in=certs
        )
        declarations.delete()
        # add any software subscriptions at this point
        partner = organisation.partner
        if organisation.software_support and partner.is_billable:
            partner.billing.add_application_plan(organisation)


@receiver(model_crud, sender=PartnerUser, dispatch_uid=uuid.uuid4().hex)
def partner_user_crud(sender, instance, **kwargs):
    """
    Detects creating, updating, deleting for PartnerUser model.
    :param sender: model sender
    :type sender: PartnerUser
    :param instance: model instance
    :type instance: PartnerUser instance
    :param kwargs: keyword arguments
    :type kwargs: dict
    :return: nothing
    :rtype: None
    """
    if kwargs["post_update"] or kwargs["post_create"]:
        # update contact relationship
        if hasattr(instance.user, "profile"):
            p = instance.user.profile
            p.contact_relationship = p.get_contact_relationship()
            p.save()
