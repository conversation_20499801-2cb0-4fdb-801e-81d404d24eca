import re
from unittest.mock import patch

from django.test import TestCase

from appusers.models.factories import AppInstallFactory
from appusers.tests.helpers import SoftwarePackageHelperTestMixin
from software_inventory.models import InstalledSoftwareOrganisationIndividual
from vulnerabilities.utils import (
    microsoft_security_release_notes, extract_channel_builds,
    extract_name_and_year, get_actual_cves_data_from_microsoft_security_release_notes,
    aggregate_installed_software_counts
)

OFFICE_2024_RETAIL = 'Office 2024 Retail: Version 2410 (Build 18129.20158)'
OFFICE_2016_RETAIL = 'Office 2016 Retail: Version 2405 (Build 17628.20144)'
OFFICE_LTSC_2024 = 'Office LTSC 2024 Volume Licensed: Version 2408 (Build 17932.20162)'
TEXT = f"""display-none-print">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button aria-label="View all contributors" class="contributors-button link-button" data-bi-name="contributors" title="View all contributors">11 contributors</button>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</li></ul>\n\t\t\t\t\t\t\t\t\t\t</div>\n<div id="user-feedback" class="margin-block-xxs display-none-print" data-hide-on-archived>\n\t<button\n\t\tid="user-feedback-button"\n\t\tdata-test-id="conceptual-feedback-button"\n\t\tclass="button button-sm button-clear button-primary"\n\t\ttype="button"\n\t\tdata-bi-name="user-feedback-button"\n\t\tdata-user-feedback-button\n\t>\n\t\t<span class="icon" aria-hidden="true">\n\t\t\t<span class="docon docon-like"></span>\n\t\t</span>\n\t\t<span>Feedback</span>\n\t</button>\n</div></div><nav id="center-doc-outline" class="doc-outline is-hidden-desktop display-none-print margin-bottom-sm" data-bi-name="intopic toc" aria-label="In this article">\n\t\t\t\t\t\t\t\t\t\t\t<h2 id="ms--in-this-article" class="title is-6 margin-block-xs">In this article</h2>\n\t\t\t\t\t\t\t\t\t\t</nav><!-- <content> --><p>These release notes provide information about security fixes that are included in updates to Microsoft Office.</p>\n<p>This information applies to Microsoft 365 Apps for enterprise, Microsoft 365 Apps for business, Office 2016 Retail (C2R), Office 2019, Office LTSC 2021, Office 2021, Office LTSC 2024, and Office 2024.</p>\n<h2 id="november-12-2024">November 12, 2024</h2>\n<p>Current Channel: Version 2410 (Build 18129.20158)<br>\nMonthly Enterprise Channel: Version 2409 (Build 18025.20214)<br>\nMonthly Enterprise Channel: Version 2408 (Build 17928.20286)<br>\nSemi-Annual Enterprise Channel (Preview): Version 2408 (Build 17928.20286)<br>\nSemi-Annual Enterprise Channel: Version 2402 (Build 17328.20648)<br>\nSemi-Annual Enterprise Channel: Version 2308 (Build 16731.20948)<br>\n{OFFICE_2024_RETAIL}<br>\nOffice 2021 Retail: Version 2410 (Build 18129.20158)<br>\nOffice 2019 Retail: Version 2410 (Build 18129.20158)<br>\nOffice 2016 Retail: Version 2410 (Build 18129.20158)<br>\n{OFFICE_LTSC_2024}<br>\nOffice LTSC 2021 Volume Licensed: Version 2108 (Build 14332.20812)<br>\nOffice 2019 Volume Licensed: Version 1808 (Build 10416.20007)</p>\n<h3 id="excel">Excel</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-49026" data-linktype="external">CVE-2024-49026</a></li>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-49027" data-linktype="external">CVE-2024-49027</a></li>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-49028" data-linktype="external">CVE-2024-49028</a></li>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-49029" data-linktype="external">CVE-2024-49029</a></li>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-49030" data-linktype="external">CVE-2024-49030</a></li>\n</ul>\n<h3 id="word">Word</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-49033" data-linktype="external">CVE-2024-49033</a></li>\n</ul>\n<h3 id="office-suite">Office Suite</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-49031" data-linktype="external">CVE-2024-49031</a></li>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-49032" data-linktype="external">CVE-2024-49032</a></li>\n</ul>\n<h2 id="october-08-2024">October 08, 2024</h2>\n<p>Current Channel: Version 2409 (Build 18025.20140)<br>\nMonthly Enterprise Channel: Version 2408 (Build 17928.20216)<br>\nMonthly Enterprise Channel: Version 2407 (Build 17830.20232)<br>\nSemi-Annual Enterprise Channel (Preview): Version 2408 (Build 17928.20216)<br>\nSemi-Annual Enterprise Channel: Version 2402 (Build 17328.20612)<br>\nSemi-Annual Enterprise Channel: Version 2308 (Build 16731.20822)<br>\nOffice 2024 Retail: Version 2409 (Build 18025.20140)<br>\nOffice 2021 Retail: Version 2409 (Build 18025.20140)<br>\nOffice 2019 Retail: Version 2409 (Build 18025.20140)<br>\nOffice 2016 Retail: Version 2409 (Build 18025.20140)<br>\nOffice LTSC 2024 Volume Licensed: Version 2408 (Build 17932.20130)<br>\nOffice LTSC 2021 Volume Licensed: Version 2108 (Build 14332.20791)<br>\nOffice 2019 Volume Licensed: Version 1808 (Build 10415.20025)</p>\n<h3 id="excel-1">Excel</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-43504" data-linktype="external">CVE-2024-43504</a></li>\n</ul>\n<h3 id="visio">Visio</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-43505" data-linktype="external">CVE-2024-43505</a></li>\n</ul>\n<h3 id="office-suite-1">Office Suite</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-43616" data-linktype="external">CVE-2024-43616</a></li>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-43576" data-linktype="external">CVE-2024-43576</a></li>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-43609" data-linktype="external">CVE-2024-43609</a></li>\n</ul>\n<h2 id="september-10-2024">September 10, 2024</h2>\n<p>Current Channel: Version 2408 (Build 17928.20156)<br>\nMonthly Enterprise Channel: Version 2407 (Build 17830.20210)<br>\nMonthly Enterprise Channel: Version 2406 (Build 17726.20222)<br>\nSemi-Annual Enterprise Channel (Preview): Version 2408 (Build 17928.20156)<br>\nSemi-Annual Enterprise Channel: Version 2402 (Build 17328.20588)<br>\nSemi-Annual Enterprise Channel: Version 2308 (Build 16731.20810)<br>\nOffice 2021 Retail: Version 2408 (Build 17928.20156)<br>\nOffice 2019 Retail: Version 2408 (Build 17928.20156)<br>\nOffice 2016 Retail: Version 2408 (Build 17928.20156)<br>\nMicrosoft 365 Apps on Windows 7: Version 2002 (Build 12527.22253)<br>\nOffice LTSC 2021 Volume Licensed: Version 2108 (Build 14332.20771)<br>\nOffice 2019 Volume Licensed: Version 1808 (Build 10414.20002)</p>\n<h3 id="excel-2">Excel</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-43465" data-linktype="external">CVE-2024-43465</a></li>\n</ul>\n<h3 id="visio-1">Visio</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-38016" data-linktype="external">CVE-2024-38016</a></li>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-43463" data-linktype="external">CVE-2024-43463</a></li>\n</ul>\n<h3 id="publisher">Publisher</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-38226" data-linktype="external">CVE-2024-38226</a></li>\n</ul>\n<h2 id="august-13-2024">August 13, 2024</h2>\n<p>Current Channel: Version 2407 (Build 17830.20166)<br>\nMonthly Enterprise Channel: Version 2406 (Build 17726.20206)<br>\nMonthly Enterprise Channel: Version 2405 (Build 17628.20206)<br>\nSemi-Annual Enterprise Channel (Preview): Version 2402 (Build 17328.20550)<br>\nSemi-Annual Enterprise Channel: Version 2402 (Build 17328.20550)<br>\nSemi-Annual Enterprise Channel: Version 2308 (Build 16731.20792)<br>\nSemi-Annual Enterprise Channel: Version 2302 (Build 16130.21094)<br>\nOffice 2021 Retail: Version 2407 (Build 17830.20166)<br>\nOffice 2019 Retail: Version 2407 (Build 17830.20166)<br>\nOffice 2016 Retail: Version 2407 (Build 17830.20166)<br>\nOffice LTSC 2021 Volume Licensed: Version 2108 (Build 14332.20763)<br>\nOffice 2019 Volume Licensed: Version 1808 (Build 10413.20020)</p>\n<h3 id="excel-3">Excel</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-38172" data-linktype="external">CVE-2024-38172</a></li>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-38170" data-linktype="external">CVE-2024-38170</a></li>\n</ul>\n<h3 id="outlook">Outlook</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-38173" data-linktype="external">CVE-2024-38173</a></li>\n</ul>\n<h3 id="powerpoint">PowerPoint</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-38171" data-linktype="external">CVE-2024-38171</a></li>\n</ul>\n<h3 id="project">Project</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-38189" data-linktype="external">CVE-2024-38189</a></li>\n</ul>\n<h3 id="visio-2">Visio</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-38169" data-linktype="external">CVE-2024-38169</a></li>\n</ul>\n<h3 id="office-suite-2">Office Suite</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-38200" data-linktype="external">CVE-2024-38200</a></li>\n</ul>\n<h2 id="july-09-2024">July 09, 2024</h2>\n<p>Current Channel: Version 2406 (Build 17726.20160)<br>\nMonthly Enterprise Channel: Version 2405 (Build 17628.20188)<br>\nMonthly Enterprise Channel: Version 2404 (Build 17531.20210)<br>\nSemi-Annual Enterprise Channel (Preview): Version 2402 (Build 17328.20452)<br>\nSemi-Annual Enterprise Channel: Version 2402 (Build 17328.20452)<br>\nSemi-Annual Enterprise Channel: Version 2308 (Build 16731.20738)<br>\nSemi-Annual Enterprise Channel: Version 2302 (Build 16130.21042)<br>\nOffice 2021 Retail: Version 2406 (Build 17726.20160)<br>\nOffice 2019 Retail: Version 2406 (Build 17726.20160)<br>\nOffice 2016 Retail: Version 2406 (Build 17726.20160)<br>\nOffice LTSC 2021 Volume Licensed: Version 2108 (Build 14332.20736)<br>\nOffice 2019 Volume Licensed: Version 1808 (Build 10412.20006)</p>\n<h3 id="access">Access</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2023-38545" data-linktype="external">CVE-2023-38545</a></li>\n</ul>\n<h3 id="outlook-1">Outlook</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-38020" data-linktype="external">CVE-2024-38020</a></li>\n</ul>\n<h3 id="office-suite-3">Office Suite</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-38021" data-linktype="external">CVE-2024-38021</a></li>\n</ul>\n<h2 id="june-11-2024">June 11, 2024</h2>\n<p>Current Channel: Version 2405 (Build 17628.20144)<br>\nMonthly Enterprise Channel: Version 2404 (Build 17531.20190)<br>\nMonthly Enterprise Channel: Version 2403 (Build 17425.20258)<br>\nSemi-Annual Enterprise Channel (Preview): Version 2402 (Build 17328.20414)<br>\nSemi-Annual Enterprise Channel: Version 2308 (Build 16731.20716)<br>\nSemi-Annual Enterprise Channel: Version 2302 (Build 16130.21026)<br>\nOffice 2019 Retail: Version 2405 (Build 17628.20144)<br>\n{OFFICE_2016_RETAIL}<br>\nOffice LTSC 2021 Volume Licensed: Version 2108 (Build 14332.20721)<br>\nOffice 2019 Volume Licensed: Version 1808 (Build 10411.20011)</p>\n<h3 id="office-suite-4">Office Suite</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-30101" data-linktype="external">CVE-2024-30101</a></li>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-30104" data-linktype="external">CVE-2024-30104</a></li>\n</ul>\n<h3 id="word-1">Word</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-30102" data-linktype="external">CVE-2024-30102</a></li>\n</ul>\n<h3 id="outlook-2">Outlook</h3>\n<ul>\n<li><a href="https://portal.msrc.microsoft.com/en-us/security-guidance/advisory/CVE-2024-30103" data-linktype="external">CVE-2024-30103</a></li>\n</ul>\n"""

class CreateCVETranslationsTestCase(TestCase):
    def setUp(self):
        self.office_2016_key = self.get_app_key(OFFICE_2016_RETAIL)
        self.word_retail_2016_key = self.office_2016_key.replace('office', 'word')
        self.office_ltsc_2024_key = self.get_app_key(OFFICE_LTSC_2024)
        self.excel_ltsc_2024_key = self.office_ltsc_2024_key.replace('office ltsc', 'excel')

    def get_app_key(self, key):
        build = extract_channel_builds(key)[0]
        name, year = extract_name_and_year(build['channel'])
        actual_version = f"16.0.{build['build']}"
        return f"{name.lower()}:{year}:{actual_version}:{build['build']}"

    def test_extract_channel_builds(self):
        self.assertEqual(
            extract_channel_builds(OFFICE_2024_RETAIL),
            [{'channel': 'Office 2024 Retail', 'version': '2410', 'build': '18129.20158'}]
        )
        self.assertEqual(
            extract_channel_builds(OFFICE_LTSC_2024),
            [{'channel': 'Office LTSC 2024 Volume Licensed', 'version': '2408', 'build': '17932.20162'}]
        )
        self.assertEqual(
            extract_channel_builds('Current Channel: Version 2409 (Build 18025.20140)'),
            []
        )
        self.assertEqual(
            extract_channel_builds('Microsoft 365 Apps on Windows 7: Version 2002 (Build 12527.22270)'),
            []
        )
        self.assertEqual(
            extract_channel_builds('something else'),
            []
        )

    def test_extract_name_and_year(self):
        self.assertEqual(extract_name_and_year('Office 2024 Retail'), ('Office', '2024'))
        self.assertEqual(extract_name_and_year('Office 2024 Volume Licensed'), ('Office', '2024'))
        self.assertEqual(extract_name_and_year('Office LTSC 2021 Volume Licensed'), ('Office LTSC', '2021'))

    def test_microsoft_security_release_notes(self):
        with patch('cyber_smart.apps.vulnerabilities.utils.requests.get') as mock_get:
            mock_get.return_value.status_code = 200
            mock_get.return_value.text = TEXT
            build_cve_map = microsoft_security_release_notes()
        cves = build_cve_map.keys()
        self.assertEqual(len(cves), len(set(re.findall(r'CVE-\d{4}-\d{4,7}', TEXT))))
        apps = build_cve_map.get('CVE-2024-30102')
        self.assertIn(self.office_2016_key, apps)
        self.assertIn(self.word_retail_2016_key, apps)
        apps = build_cve_map.get('CVE-2024-30101')
        # word should not have this CVE, since it was only for office suite, so it should only be in main distribution
        self.assertIn(self.office_2016_key, apps)
        self.assertNotIn(self.word_retail_2016_key, apps)
        apps = build_cve_map.get('CVE-2024-49026')
        self.assertIn(self.office_ltsc_2024_key, apps)
        self.assertIn(self.excel_ltsc_2024_key, apps)

    def test_get_cves_with_data_from_microsoft_security_release_notes(self):
        cves = ['CVE-2024-49026', 'CVE-2024-49027', 'CVE-2024-49033', 'CVE-2024-30103']
        expected_data = [
            ('excel 2019', '16.0.10416.20007', ['CVE-2024-49033', 'CVE-2024-30103']),
            ('office ltsc 2024', '16.0.17932.20162', ['CVE-2024-30103']),
            ('word 2019', '16.0.10416.20007', ['CVE-2024-49026', 'CVE-2024-49027', 'CVE-2024-30103']),
            # newer versions should not have any previous CVEs
            ('office 2016', '16.0.18129.20158', []),
            # not patched versions
            ('excel 2019', '16.0.10416.20001', cves),
            ('word 2019', '16.0.10414.20007', cves),
            ('office 2016', '16.0.15831.20208', cves),
            # not LTSC
            ('office 2024', '16.0.17932.20162', cves),
        ]
        with patch('cyber_smart.apps.vulnerabilities.utils.requests.get') as mock_get:
            mock_get.return_value.status_code = 200
            mock_get.return_value.text = TEXT
            for data in expected_data:
                cleaned_cves = get_actual_cves_data_from_microsoft_security_release_notes(data[0], data[1], set(cves))
                self.assertEqual(cleaned_cves, set(data[2]))

class AggregateInstalledSoftwareTest(SoftwarePackageHelperTestMixin, TestCase):
    def test_aggregate_installed_software(self):
        app_install = AppInstallFactory()
        self._create_test_packages(app_install=app_install)

        queryset = InstalledSoftwareOrganisationIndividual.objects.filter(
            organisation_id=app_install.app_user.organisation_id,
        ).order_by("-highest_severity")

        total, safe, vulnerable, opswat, regular = aggregate_installed_software_counts(
            queryset, include_source_counts=True
        )

        self.assertEqual(total, 12)
        self.assertEqual(safe, 1)
        self.assertEqual(vulnerable, 10)
        self.assertEqual(opswat, 11)
        self.assertEqual(regular, 1)
