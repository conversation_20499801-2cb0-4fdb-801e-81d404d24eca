import re

import waffle
from django.core.exceptions import ValidationError
from django.http import JsonResponse
from django.shortcuts import redirect
from django.template.loader import render_to_string
from django.utils.translation import gettext
from upload_validator import FileTypeValidator

from organisations.models import OrganisationPolicy, OrganisationPolicyVersion
from .utils import ALLOWED_TYPES, ALLOWED_EXTENSIONS


class PoliciesNewUIViewMixin:
    """
    Mixin for the policies new UI.
    """
    def dispatch(self, request, *args, **kwargs):
        """
        Dispatches the request.
        """
        if not waffle.switch_is_active("smart-policies-new-ui"):
            return redirect("dashboard:policies", org_id=kwargs["org_id"])
        return super().dispatch(request, *args, **kwargs)


class PolicyViewMixin:
    """
    Mixin for policy views.
    """
    @staticmethod
    def has_no_policies_support(organisation) -> bool or JsonResponse:
        """
        Checks if the organisation has no policies support.
        """
        if not organisation.policies_support:
            return JsonResponse({
                "success": False,
                "error": gettext("Smart Policies is included with CyberSmart Active Protect")
            }, status=400)
        return False

    def get_validator(self) -> FileTypeValidator:
        """
        Returns a FileTypeValidator instance.
        """
        return FileTypeValidator(
            allowed_types=ALLOWED_TYPES,
            allowed_extensions=ALLOWED_EXTENSIONS
        )

    @staticmethod
    def get_policy_file(request) -> JsonResponse or None:
        """
        Returns the policy file from the request.
        """
        policy_file = request.FILES.get("policy_file")
        if not policy_file:
            return JsonResponse({
                "success": False,
                "error": gettext("Please select a file.")
            }, status=400)
        return policy_file

    def validate_policy_file(self, policy_file, validator) -> JsonResponse or None:
        """
        Validates the policy file.
        """
        try:
            validator(policy_file)
        except ValidationError:
            return JsonResponse({
                "success": False,
                "error": gettext(
                    "Invalid File type - File must be either pdf, document, spreadsheet, presentation or image."
                    f" Allowed extensions are {', '.join(ALLOWED_EXTENSIONS)}."
                )
            }, status=400)
        return None

    def get_success_response(self) -> JsonResponse:
        """
        Returns the success response.
        """
        html = render_to_string(
            "smart_policies/policy_row.html",
            {"policy": self.policy, "groups_support": not self.organisation.is_bulk_enrollment_type, "organisation": self.organisation}
        )
        return JsonResponse({"success": True, "html": html})

    @staticmethod
    def validate_policy_version(
            policy: OrganisationPolicy, version: float, check_for_existence: bool = True
    ) -> JsonResponse or None:
        """
        Validates the policy version.
        """
        try:
            version = float(version)
            if version < 0:
                return JsonResponse({
                    "success": False,
                    "error": gettext("Negative versions are not allowed")
                }, status=400)
        except ValueError:
            return JsonResponse({
                "success": False,
                "error": gettext("Invalid version number")
            }, status=400)
        if check_for_existence and policy.versions.filter(version=version).count() > 0:
            return JsonResponse({
                "success": False,
                "error": gettext("This version already exists")
            }, status=400)
        return None

    def create_policy_version(self, request, policy, policy_file) -> None:
        """
        Creates a new policy version.
        """
        version = request.POST.get("policy_version")

        if error := self.validate_policy_version(policy, version):
            return error

        OrganisationPolicyVersion.objects.create(
            policy=policy,
            document=policy_file,
            version=1.0 if not version else version,
            main=True
        )

    @staticmethod
    def get_policy_name(request, policy_file=None) -> str:
        """
        Returns the policy name.
        """
        return request.POST.get("policy_name") or (policy_file.name.split('.')[0] if policy_file else "")

    @staticmethod
    def validate_policy_name(name: str) -> JsonResponse or None:
        if not name.strip():
            return JsonResponse({
                "success": False,
                "name": "name",
                "error": "Policy name cannot be blank"
            })
        if name and not re.match(r"^[-\w\s_.\[\]]*$", name):
            return JsonResponse({
                "success": False,
                "name": "name",
                "error": "We do not allow special characters in the policy name. Please change the policy name."
            })

    @staticmethod
    def validate_policy_visibility(visibility: str) -> JsonResponse or None:
        if visibility not in [True, False]:
            return JsonResponse({
                "success": False,
                "error": "Invalid visibility."
            })

    def get_or_create_policy(self, request, organisation, policy_file) -> OrganisationPolicy:
        """
        Returns a policy instance or creates a new one.
        """
        return OrganisationPolicy.objects.create(
            organisation=organisation,
            name=request.POST.get("policy_name") or policy_file.name.split('.')[0]
        )

    @staticmethod
    def update_groups(request, policy) -> None:
        """
        Updates the policy groups.
        """
        # first clear all groups
        policy.groups.clear()
        # then add the new ones
        if group_ids := request.POST.get("policy_groups"):
            group_ids = group_ids.split(",")
        else:
            group_ids = []
        if groups := policy.organisation.groups.filter(id__in=group_ids):
            policy.groups.add(*groups)
