from urllib.parse import urlparse

from accounts.permissions import (
    CERTIFICATES_AND_INSURANCE_PERMISSION,
    DEVICES_PERMISSION,
    PEOPLE_AND_ORGANISATION_PERMISSION,
)
from accounts.templatetags.check_permissions import (
    has_access_to_dashboard_access_page,
    has_access_to_manage_app_users_page,
)
from django.contrib import messages
from django.contrib.auth.mixins import PermissionRequiredMixin
from django.core.exceptions import ImproperlyConfigured
from django.shortcuts import redirect
from django.urls import resolve
from django.urls.exceptions import Resolver404
from organisations.models import Organisation
from rest_framework.permissions import BasePermission
from django.utils.translation import gettext as _


class PermissionRoleRequiredMixin(PermissionRequiredMixin):
    """
    Mixin to check if the user has the required permission to access the view.

    Use this in template views like:
    class MyView(PermissionRoleRequiredMixin, View):
        permission_required = 'my_permission'  # or a list of permissions like ['my_permission', 'my_other_permission']

    If multiple permissions are used in permission_required, this will check if the user has any of the permissions.
    Instead of the parent class PermissionRequiredMixin, which will check if the user has all the permissions.
    """

    def dispatch(self, request, *args, **kwargs):
        previous_url = request.headers.get('referer')

        if not self.has_permission():
            return self.handle_no_permission(previous_url)

        # this is so the dispatch of PermissionRequiredMixin is not called
        # but rather the next class on the view that this mixin lives on
        mro = type(self).mro()
        next_class = mro[mro.index(PermissionRequiredMixin) + 1]

        return next_class.dispatch(self, request, *args, **kwargs)

    def has_permission(self):
        """ Check if the user has any of the required permissions. """
        perms = self.get_permission_required()
        return any(self.request.user.has_perm(perm) for perm in perms)

    @property
    def has_people_and_organisation_permission(self):
        return self.request.user.has_perm(PEOPLE_AND_ORGANISATION_PERMISSION)

    @property
    def has_certificates_and_insurance_permission(self):
        return self.request.user.has_perm(CERTIFICATES_AND_INSURANCE_PERMISSION)

    @property
    def has_devices_permission(self):
        return self.request.user.has_perm(DEVICES_PERMISSION)

    def add_message(self, previous_url):
        """ Add a message to the user to let them know they did not have permission to view the page.
        Only show this message if the user is not coming from the login page.
        """
        if previous_url:
            try:
                resolved_path = resolve(urlparse(previous_url)[2]).url_name
            except Resolver404:
                resolved_path = ''
            if resolved_path == 'account_login':
                return
        messages.add_message(
            self.request,
            messages.ERROR,
            _('Your role does not have permission to view this page.')
        )

    def handle_no_permission(self, previous_url=None):
        """ Depending on the users permissions, redirect them to the correct page. """
        if self.request.user.is_authenticated:
            self.add_message(previous_url)
            current_view_name = self.request.resolver_match.view_name

            if self.request.user.profile.is_circle_community_user:
                # redirect Circle Community users to the profile page
                # they should not have access to any other pages

                # clear django messages about restricted access
                messages.get_messages(self.request)
                return redirect("settings:profile")

            if 'org_id' in self.kwargs:
                # redirect to Manage Users or Dashboard Access Page
                if self.has_people_and_organisation_permission:
                    organisation = Organisation.objects.filter(secure_id=self.kwargs['org_id']).first()
                    # redirect user to manage users or dashboard access depending on organisation settings
                    if has_access_to_manage_app_users_page([PEOPLE_AND_ORGANISATION_PERMISSION], organisation):
                        return redirect('organisations:manage-users', org_id=self.kwargs['org_id'])
                    elif has_access_to_dashboard_access_page([PEOPLE_AND_ORGANISATION_PERMISSION], organisation):
                        return redirect('organisations:dashboard-access', org_id=self.kwargs['org_id'])
                    return redirect('dashboard:manage-organisation', org_id=self.kwargs['org_id'])
                # redirect to Certificates Page
                elif (self.has_certificates_and_insurance_permission
                      and current_view_name != 'organisations:certificates'):
                    return redirect('organisations:certificates', org_id=self.kwargs['org_id'])
                # redirect to Devices Page
                elif self.has_devices_permission and current_view_name != 'organisations:devices':
                    return redirect('organisations:devices', org_id=self.kwargs['org_id'])
            # redirect to Dashboard Page that should handle other redirections if necessary
            return redirect('dashboard:home')

        return super().handle_no_permission()


class PermissionRoleRequired(BasePermission):
    """
    Allows access only to users with the specified permission.

    Use this in API views like:
    class MyAPIView(APIView):
        permission_classes = (PermissionRoleRequired,)
        permission_required = 'my_permission'  # or a list of permissions like ['my_permission', 'my_other_permission']

    If multiple permissions are used in permission_required, this will check if the user has any of the permissions.
    """

    def has_permission(self, request, view):
        if not hasattr(view, 'permission_required'):
            raise ImproperlyConfigured(
                'PermissionRoleRequired requires the view to define a permission_required attribute'
            )
        if isinstance(view.permission_required, str):
            perms = (view.permission_required,)
        else:
            perms = view.permission_required
        return any(request.user.has_perm(perm) for perm in perms)
