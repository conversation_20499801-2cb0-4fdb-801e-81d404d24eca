from celery.utils.log import get_task_logger
import json
import requests
from waffle import switch_is_active


from .addresses import ROLE_BASED_ADDRESSES
from organisations.models import OrganisationUserSync

micrsoft_access_url = 'https://login.microsoftonline.com/common/oauth2/v2.0/token?'
google_access_url = 'https://www.googleapis.com/oauth2/v4/token?'

logger = get_task_logger(__name__)

def refresh_access_token(refresh_token, client_id, client_secret, provider):
    data = {
        'refresh_token': refresh_token,
        'client_id': client_id,
        'client_secret': client_secret,
        'grant_type': 'refresh_token'
    }
    if provider == OrganisationUserSync.PROVIDER_GOOGLE:
        url = google_access_url
    else:
        url = micrsoft_access_url
    r = requests.post(url, data=data)
    log_errors = switch_is_active('social_users_import_log_errors')
    try:
        response = r.json()
    except json.JSONDecodeError:
        if log_errors:
            logger.info(
                f"Social import: Error while refreshing access token during JSON decode: {r.text}",
                extra={
                    "provider": provider,
                    "response": r.text,
                },
            )
        return ''
    else:
        if 'error' not in response:
            return response['access_token']
        else:
            if log_errors:
                logger.info(
                    f"Social import: Error while refreshing access token: {response}",
                    extra={
                        "provider": provider,
                        "response": response,
                },
                )
    return ''


def convert_social_users_to_common_format(users, provider, organisation):
    if provider == OrganisationUserSync.PROVIDER_GOOGLE:
        return convert_google_users_to_common_format(users, organisation)
    elif provider == OrganisationUserSync.PROVIDER_MICROSOFT:
        return convert_office_users_to_common_format(users, organisation)
    return {'users': [], 'imported': 0, 'not_imported': 0, 'deactivated': 0}


def convert_google_users_to_common_format(users, organisation):
    """
    Converts google users json to common format.
    :param users: google users list
    :type users: dict
    :param organisation: if passed then user object will contain information related to organisation
    :type organisation: organisations.Organisation
    :return: common users format
    :rtype: dict
    """
    common_format = {'users': [], 'imported': 0, 'not_imported': 0, 'deactivated': 0}
    for user in users['users']:
        email_prefix = user['primaryEmail'].split('@')[0].lower() + '@'
        if email_prefix not in ROLE_BASED_ADDRESSES:
            user_object = {
                'full_name': user['name']['fullName'],
                'email': user['primaryEmail'],
            }
            if user.get('suspended'):
                continue

            if organisation:
                try:
                    app_user = organisation.app_users.filter(email=user['primaryEmail'])[0]
                except IndexError:
                    common_format['not_imported'] += 1
                    user_object.update({
                        'imported': False,
                        'active': False,
                        'installed': False
                    })
                else:
                    common_format['imported'] += 1
                    user_object.update({
                        'imported': True,
                        'active': app_user.active,
                        'installed': bool(app_user.raw_installed_devices.count())
                    })
                    if not app_user.active:
                        common_format['deactivated'] += 1

            common_format['users'].append(user_object)
    return common_format


def convert_office_users_to_common_format(users, organisation):
    """
    Converts office users json to common format.
    :param users: office users list
    :type users: dict
    :param organisation: if passed then user object will contain information related to organisation
    :type organisation: organisations.Organisation
    :return: common users format
    :rtype: dict
    """
    common_format = {'users': [], 'imported': 0, 'not_imported': 0, 'deactivated': 0}
    for user in users['value']:
        if 'mail' in user and not user['mail']:
            # skip users with no email
            continue
        email_prefix = user['mail'].split('@')[0].lower() + '@'
        if email_prefix not in ROLE_BASED_ADDRESSES:
            user_object = {
                'full_name': user['displayName'],
                'email': user['mail']
            }
            if 'accountEnabled' in user and not user['accountEnabled']:
                continue

            if organisation:
                try:
                    app_user = organisation.app_users.filter(email=user['mail'])[0]
                except IndexError:
                    common_format['not_imported'] += 1
                    user_object.update({
                        'imported': False,
                        'active': False
                    })
                else:
                    common_format['imported'] += 1
                    user_object.update({
                        'imported': True,
                        'active': app_user.active
                    })
                    if not app_user.active:
                        common_format['deactivated'] += 1

            common_format['users'].append(user_object)
    return common_format


def pervade_support(user):
    """
    Returns True if passed user has Pervade API custom token and org name support otherwise returns False.
    :param user: django user
    :type user: get_user_model()
    :return: True or False
    :rtype: bool
    """
    if user.profile.is_partner:
        if user.profile.partner.iasme_cb:
            return True
    return False


def is_trial_email(email_address: str) -> bool:
    """ Returns True if email address is a trial email address otherwise returns False. """
    import re
    return re.search(r'trial@[a-f0-9]{32}\.com', str(email_address)) is not None
