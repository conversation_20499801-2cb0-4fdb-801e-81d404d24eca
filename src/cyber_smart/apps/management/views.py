from urllib.parse import urlencode

from django.db.models import Count, Q
from django.http import HttpResponseRedirect
from django.views.generic import ListView

from accounts.models import UserStripe
from analytics.models import ManagementAnalytics
from appusers.models import (
    AppUser, AppInstall,
    CheckManualFix, CheckAutoFix
)
from billing.models import DirectCustomer
from common.mixins import StaffRequiredMixin
from organisations.models import Organisation
from billing.providers.chargebee.plans import PRO_IDS, REGULAR_IDS
from partners.models import PartnerUser, Partner
from rulebook.models import (
    IssuedCertification, CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS, IASME_GOVERNANCE, GDPR
)


class DashboardView(StaffRequiredMixin, ListView):
    http_method_names = ('get', 'post')
    model = Organisation
    template_name = 'admin/management/dashboard.html'
    context_object_name = 'organisations'
    paginate_by = 30
    ordering = ('-created',)

    @staticmethod
    def post(request):
        return HttpResponseRedirect('')

    def paginate_queryset(self, queryset, page_size):
        paginator, page, queryset, is_paginated = super(
            DashboardView, self
        ).paginate_queryset(queryset, page_size)
        prefetched_queryset = Organisation.objects.filter(
            id__in=page.object_list.values_list('pk', flat=True)
        ).select_related('partner').select_related('analytics').defer(
            'email_message'
        ).annotate(
            partners_orgs_count=Count('partner__organisations', distinct=True)
        ).annotate(
            certifications_count=Count('certifications', distinct=True)
        ).annotate(
            appusers_count=Count('app_users', distinct=True, filter=Q(app_users__active=True))
        ).annotate(
            appinstalls_count=Count('app_users__installs', distinct=True, filter=Q(app_users__installs__inactive=False))
        )
        return paginator, page, prefetched_queryset, page.has_other_pages()

    def get_queryset(self):
        order = []
        queryset = super(DashboardView, self).get_queryset().filter(is_test=False)

        org_name = self.request.GET.get('name')
        org_name = True if org_name == 'True' else False if org_name == 'False' else None
        if org_name is not None:
            order.append('name' if org_name else '-name')

        partner = self.request.GET.get('partner')
        partner = True if partner == 'True' else False if partner == 'False' else None
        if partner is not None:
            order.append('partner' if partner else '-partner')

        bulk = self.request.GET.get('bulk')
        bulk = True if bulk == 'True' else False if bulk == 'False' else None
        if bulk is not None:
            order.append('bulk_install' if bulk else '-bulk_install')

        created = self.request.GET.get('created')
        created = True if created == 'True' else False if created == 'False' else None
        if created is not None:
            order.append('partner__organisations' if created else '-partner__organisations')

        users = self.request.GET.get('users')
        users = True if users == 'True' else False if users == 'False' else None
        if users is not None:
            order.append('app_users_count' if users else '-app_users_count')

        installs = self.request.GET.get('installs')
        installs = True if installs == 'True' else False if installs == 'False' else None
        if installs is not None:
            order.append('appinstalls_count' if installs else '-appinstalls_count')

        certificates = self.request.GET.get('certificates')
        certificates = True if certificates == 'True' else False if certificates == 'False' else None
        if certificates is not None:
            order.append('certifications_count' if certificates else '-certifications_count')

        percentage = self.request.GET.get('percentage')
        percentage = True if percentage == 'True' else False if percentage == 'False' else None
        if percentage is not None:
            order.append('analytics__pass_percentage' if percentage else '-analytics__pass_percentage')

        return queryset.order_by(*order)

    def get_context_data(self, **kwargs):
        context = super(DashboardView, self).get_context_data(**kwargs)
        query = self.request.GET.dict()
        if 'page' in query:
            query.pop('page')

        # get management analytics
        counters_fields = filter(
            lambda x: x.endswith('_count'),
            [field.name for field in ManagementAnalytics._meta.get_fields()]
        )
        analytics = ManagementAnalytics.objects.only(*counters_fields).first()
        if not analytics:
            # execute this task avoiding celery to get immediate results
            analytics = ManagementAnalytics.objects.only(*counters_fields).first()

        context.update({
            'has_permission': True,
            'get_params': self.request.GET.dict(),
            'params_query': urlencode(query),
            'modified': analytics.modified,
            'organisations_count': analytics.organisations_count,
            'organisations_bulk_count': analytics.bulk_enrollment_organisations_count,
            'organisations_individual_count': analytics.individual_enrollment_organisations_count,
            'organisations_partner_count': analytics.partner_organisations_count,
            'organisations_paid_stripe_count': analytics.stripe_users_count,
            'organisations_paid_chargebee_count': analytics.chargebee_organisations_count,
            'organisations_basic_plan': analytics.chargebee_basic_organisations_count,
            'organisations_pro_plan': analytics.chargebee_pro_organisations_count,
            'organisations_issued_cert': analytics.issued_organisations_count,
            'issued_cert': analytics.issued_certifications_count,
            'cs_issued_cert': analytics.cs_cyber_essentials_certifications_count,
            'gdpr_issued_cert': analytics.cs_gdpr_certifications_count,
            'api_cs_issued_cert': analytics.api_cyber_essentials_certifications_count,
            'api_gdpr_issued_cert': analytics.api_gdpr_certifications_count,
            'partners_with_iasme': analytics.iasme_partners_count,
            'partners_organisations': analytics.iasme_organisations_count,
            'partners_clients': analytics.iasme_clients_organisations_count,
            'partners_with_pervade_token': analytics.partners_with_pervade_token_count,
            'partners_client_pro_apps': analytics.iasme_pro_app_installs_count,
            'partners_clients_basic_apps': analytics.iasme_basic_app_installs_count,
            'iasme_partners_organisations_basic_app_installs':
                analytics.iasme_partners_organisations_basic_app_installs_count,
            'iasme_partners_organisations_pro_app_installs':
                analytics.iasme_partners_organisations_pro_app_installs_count,
            'iasme_clients_organisations_basic_app_installs':
                analytics.iasme_clients_organisations_basic_app_installs_count,
            'iasme_clients_organisations_pro_app_installs':
                analytics.iasme_clients_organisations_pro_app_installs_count,
            'partner_users_count': analytics.partner_users_count,
            'app_installs_count': analytics.app_installs_count,
            'app_installs_basic': analytics.app_installs_basic_count,
            'app_installs_pro': analytics.app_installs_pro_count,
            'app_installs_without_reports_count': analytics.app_installs_no_reports_count,
            'app_installs_inactive_count': analytics.app_installs_inactive_count,
            'app_installs_inactive_basic_count': analytics.app_installs_inactive_basic_count,
            'app_installs_inactive_pro_count': analytics.app_installs_inactive_pro_count,
            'app_users_count': analytics.app_users_count,
            'app_users_enrolled_count': analytics.app_users_enrolled_count,
            'app_users_active_count': analytics.app_users_active_count,
            'app_installs_average': analytics.app_installs_average_count,
            'app_installs_median': analytics.app_installs_median_count,
            'app_installs_active_count': analytics.app_installs_count
        })
        return context


class OrganisationsView(StaffRequiredMixin, ListView):
    http_method_names = ('get',)
    model = Organisation
    template_name = 'admin/management/organisations.html'
    context_object_name = 'organisations'
    paginate_by = 30
    ordering = ('-created',)

    def get_queryset(self):
        queryset = super(OrganisationsView, self).get_queryset().filter(is_test=False)
        filters = {}
        exclude = {}
        bulk_install = self.request.GET.get('bulk_install')
        partner = self.request.GET.get('partner')
        issued = self.request.GET.get('issued')
        cb = self.request.GET.get('cb')
        cb_clients = self.request.GET.get('cb_clients')

        if bulk_install:
            bulk_install = True if bulk_install == 'True' else False if bulk_install == 'False' else None
            if bulk_install is not None:
                filters['bulk_install'] = bulk_install
        if partner:
            partner = True if partner == 'True' else False if partner == 'False' else None
            if partner is not None:
                filters['partner__isnull'] = not partner
        if issued:
            issued = True if issued == 'True' else False if issued == 'False' else None
            if issued is not None:
                filters['certifications__issued_certification__isnull'] = not issued
        if cb:
            cb = True if cb == 'True' else False if cb == 'False' else None
            if cb is not None:
                filters['pk__in'] = [p.organisations.all()[0].pk for p in Partner.objects.filter(
                    iasme_cb=True
                ) if p.organisations.all().count()]
        if cb_clients:
            cb_clients = True if cb_clients == 'True' else False if cb_clients == 'False' else None
            if cb_clients is not None:
                iasme_organisations = Organisation.objects.filter(
                    is_test=False,
                    pk__in=[p.organisations.all()[0].pk for p in Partner.objects.filter(
                        iasme_cb=True
                    ) if p.organisations.all().count()]
                )
                filters['partner__iasme_cb'] = True
                exclude['pk__in'] = iasme_organisations
        return queryset.filter(**filters).exclude(**exclude)

    def get_context_data(self, **kwargs):
        context = super(OrganisationsView, self).get_context_data(**kwargs)
        context.update({'has_permission': True})
        return context


class AppInstallsView(StaffRequiredMixin, ListView):
    http_method_names = ('get',)
    model = AppInstall
    template_name = 'admin/management/appinstalls.html'
    context_object_name = 'app_installs'
    paginate_by = 30
    ordering = ('-id',)

    def get_queryset(self):
        queryset = super(AppInstallsView, self).get_queryset().filter(app_user__organisation__is_test=False)
        filters = {}
        exclude = {}
        inactive = self.request.GET.get('inactive')
        without_reports = self.request.GET.get('without_reports')
        # cb_partners_basic_apps = self.request.GET.get('cb_partners_basic')
        # cb_partners_pro_apps = self.request.GET.get('cb_partners_pro')
        # cb_clients_basic_apps = self.request.GET.get('cb_clients_basic')
        # cb_clients_pro_apps = self.request.GET.get('cb_clients_pro')

        if inactive:
            inactive = True if inactive == 'True' else False if inactive == 'False' else None
            if inactive is not None:
                filters['inactive'] = inactive
        # if inactive_basic:
        #     filters['inactive'] = True
        #     filters['app_user__organisation__policies_support'] = False
        # if inactive_pro:
        #     filters['inactive'] = True
        #     filters['app_user__organisation__policies_support'] = True
        if without_reports:
            without_reports = True if without_reports == 'True' else False if without_reports == 'False' else None
            if without_reports is not None:
                filters['reports__isnull'] = True
                filters['inactive'] = False
        # if pro_apps:
        #     filters['app_user__organisation__policies_support'] = True
        #     filters['inactive'] = False
        # if cb_pro_apps:
        #     filters['app_user__organisation__policies_support'] = True
        #     filters['app_user__organisation__partner__iasme_cb'] = True
        #     filters['inactive'] = False
        # if basic_apps:
        #     filters['app_user__organisation__policies_support'] = False
        #     filters['inactive'] = False
        # if cb_basic_apps:
        #     filters['app_user__organisation__policies_support'] = False
        #     filters['app_user__organisation__partner__iasme_cb'] = True
        #     filters['inactive'] = False
        # if cb_partners_pro_apps or cb_partners_basic_apps or cb_clients_pro_apps or cb_clients_basic_apps:
        #     iasme_organisations = Organisation.objects.filter(
        #         is_test=False,
        #         pk__in=[p.organisations.all()[0].pk for p in Partner.objects.filter(
        #             iasme_cb=True
        #         ) if p.organisations.all().count()]
        #     )
            # iasme_clients_organisations = Organisation.objects.filter(
            #     is_test=False,
            #     partner__iasme_cb=True
            # ).exclude(pk__in=iasme_organisations)
            # if cb_partners_basic_apps:
            #     filters['app_user__organisation__policies_support'] = False
            #     filters['app_user__organisation__is_test'] = False
            #     filters['app_user__organisation__partner__iasme_cb'] = True
            #     filters['inactive'] = False
            #     exclude['app_user__organisation__in'] = iasme_clients_organisations
            # if cb_partners_pro_apps:
            #     filters['app_user__organisation__policies_support'] = True
            #     filters['app_user__organisation__is_test'] = False
            #     filters['app_user__organisation__partner__iasme_cb'] = True
            #     filters['inactive'] = False
            #     exclude['app_user__organisation__in'] = iasme_clients_organisations
            # if cb_clients_basic_apps:
            #     filters['app_user__organisation__policies_support'] = False
            #     filters['app_user__organisation__is_test'] = False
            #     filters['app_user__organisation__partner__iasme_cb'] = True
            #     filters['inactive'] = False
            #     exclude['app_user__organisation__in'] = iasme_organisations
            # if cb_clients_pro_apps:
            #     filters['app_user__organisation__policies_support'] = True
            #     filters['app_user__organisation__is_test'] = False
            #     filters['app_user__organisation__partner__iasme_cb'] = True
            #     filters['inactive'] = False
            #     exclude['app_user__organisation__in'] = iasme_organisations
        return queryset.filter(**filters).exclude(**exclude)

    def get_context_data(self, **kwargs):
        context = super(AppInstallsView, self).get_context_data(**kwargs)
        context.update({'has_permission': True})
        return context


class AppUsersView(StaffRequiredMixin, ListView):
    http_method_names = ('get',)
    model = AppUser
    template_name = 'admin/management/appusers.html'
    context_object_name = 'app_users'
    paginate_by = 30
    ordering = ('-created',)

    def get_queryset(self):
        queryset = super(AppUsersView, self).get_queryset().filter(organisation__is_test=False)
        filters = {}
        active = self.request.GET.get('active')

        if active in ['True', 'False']:
            filters['active'] = True if active == 'True' else False
        return queryset.filter(**filters)

    def get_context_data(self, **kwargs):
        context = super(AppUsersView, self).get_context_data(**kwargs)
        context.update({'has_permission': True})
        return context


class AppAutoFixesView(StaffRequiredMixin, ListView):
    http_method_names = ('get',)
    model = CheckAutoFix
    template_name = 'admin/management/appfixes.html'
    context_object_name = 'app_fixes'
    paginate_by = 30
    ordering = ('-created',)

    def get_queryset(self):
        return super(AppAutoFixesView, self).get_queryset().filter(
            app_install__app_user__organisation__is_test=False
        )

    def get_context_data(self, **kwargs):
        context = super(AppAutoFixesView, self).get_context_data(**kwargs)
        context.update({'has_permission': True})
        return context


class AppManualFixesView(AppAutoFixesView):
    model = CheckManualFix


class PaidStripeDirectCustomersView(StaffRequiredMixin, ListView):
    http_method_names = ('get',)
    paginate_by = 30
    ordering = ('-created',)
    model = UserStripe
    template_name = 'admin/management/paid-stripe-customers.html'
    context_object_name = 'customers'

    def get_queryset(self):
        return super(PaidStripeDirectCustomersView, self).get_queryset()

    def get_context_data(self, **kwargs):
        context = super(PaidStripeDirectCustomersView, self).get_context_data(**kwargs)
        context.update({'has_permission': True})
        return context


class PaidChargebeeDirectCustomersView(StaffRequiredMixin, ListView):
    http_method_names = ('get',)
    paginate_by = 30
    ordering = ('-created',)
    model = DirectCustomer
    template_name = 'admin/management/paid-chargebee-customers.html'
    context_object_name = 'customers'

    def get_queryset(self):
        filters = {'subscriptions__isnull': False, 'organisation__is_test': False}
        if self.request.GET.get('plan') == 'pro':
            filters['subscriptions__addon_id__in'] = PRO_IDS
        elif self.request.GET.get('plan') == 'basic':
            filters['subscriptions__addon_id__in'] = REGULAR_IDS
        return super(
            PaidChargebeeDirectCustomersView, self
        ).get_queryset().order_by('organisation').distinct('organisation').filter(**filters)

    def get_context_data(self, **kwargs):
        context = super(PaidChargebeeDirectCustomersView, self).get_context_data(**kwargs)
        context.update({'has_permission': True})
        return context


class PartnersView(StaffRequiredMixin, ListView):
    http_method_names = ('get',)
    paginate_by = 30
    ordering = ('-created',)
    model = Partner
    template_name = 'admin/management/partners.html'
    context_object_name = 'partners'

    def get_queryset(self):
        filters = {}
        cb = {'True': True, 'False': False}.get(self.request.GET.get('cb'), False)
        if cb:
            filters['iasme_cb'] = cb
        api_linked = {'True': True, 'False': False}.get(self.request.GET.get('api_linked'), False)
        if api_linked:
            filters['users_pervade_token__isnull'] = False
            filters['iasme_cb'] = True

        return super(PartnersView, self).get_queryset().filter(**filters)

    def get_context_data(self, **kwargs):
        context = super(PartnersView, self).get_context_data(**kwargs)
        context.update({'has_permission': True})
        return context


class PartnerUsersView(StaffRequiredMixin, ListView):
    http_method_names = ('get',)
    paginate_by = 30
    ordering = ('-created',)
    model = PartnerUser
    template_name = 'admin/management/partner-users.html'
    context_object_name = 'users'

    def get_queryset(self):
        return super(PartnerUsersView, self).get_queryset().order_by('user').distinct('user')

    def get_context_data(self, **kwargs):
        context = super(PartnerUsersView, self).get_context_data(**kwargs)
        context.update({'has_permission': True})
        return context


class CertificationsView(StaffRequiredMixin, ListView):
    http_method_names = ('get',)
    paginate_by = 30
    ordering = ('-created',)
    model = IssuedCertification
    template_name = 'admin/management/certifications.html'
    context_object_name = 'certifications'

    def get_queryset(self):
        filters = {'certificate__organisation__is_test': False}
        cybersmart_cyber_essentials = {'True': True, 'False': False}.get(
            self.request.GET.get('cybersmart_cyber_essentials'), False
        )
        cybersmart_gdpr = {'True': True, 'False': False}.get(self.request.GET.get('cybersmart_gdpr'), False)
        api_cyber_essentials = {'True': True, 'False': False}.get(self.request.GET.get('api_essentials'), False)
        api_gdpr = {'True': True, 'False': False}.get(self.request.GET.get('api_gdpr'), False)

        if cybersmart_cyber_essentials:
            filters['custom_cert_sent'] = True
            filters['certificate__version__type__type__in'] = [CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS]
        if cybersmart_gdpr:
            filters['custom_cert_sent'] = True
            filters['certificate__version__type__type__in'] = [IASME_GOVERNANCE, GDPR]
        if api_cyber_essentials:
            filters['custom_cert_sent'] = False
            filters['certificate__version__type__type__in'] = [CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS]
        if api_gdpr:
            filters['custom_cert_sent'] = False
            filters['certificate__version__type__type__in'] = [IASME_GOVERNANCE, GDPR]

        return super(CertificationsView, self).get_queryset().filter(**filters)

    def get_context_data(self, **kwargs):
        context = super(CertificationsView, self).get_context_data(**kwargs)
        context.update({'has_permission': True})
        return context
