import logging
import os
import re
import sys
import warnings
from base64 import b64encode
from dataclasses import dataclass
from uuid import uuid4

import environ
import requests
import sentry_sdk
from celery.schedules import crontab
from django.contrib.messages import constants as messages
from sentry_sdk.integrations.celery import CeleryIntegration
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.redis import RedisIntegration

env = environ.Env()
logger = logging.getLogger(__name__)


def create_list_of_emails(emails_string):
    """
    Creates list of emails.
    """
    try:
        emails_list = emails_string.split(',')
        return emails_list
    except Exception as error:
        logger.error(f'An error occured while creating list: {error}')
        raise error
    return []


def get_from_env(key, default_val, throw_on_not_set=False):
    """
    get key value from the environment variables; if it's not set, fall back to the provided default value
    """

    management_commands_without_need_for_env_vars = [
        'collectstatic',
        'compilemessages',
        'compilejsi18n',
        'clearsessions',
        'sync_pgviews',
        'migrate'
    ]
    # During Docker build, some environment variables might not be present.
    # We only want to validate these variables when the Docker container is running.
    # Therefore, we skip the validation for certain management commands that are
    # typically run during the build process.
    if any(command in sys.argv for command in management_commands_without_need_for_env_vars):
        return os.environ.get(key, default_val)

    value = os.environ.get(key)
    if throw_on_not_set and value is None:
        raise ValueError(f"Environment variable {key} must be set.")
    return value if value is not None else default_val


def bool_from_env(key, default_val):
    """
    cast bool value from the environment variables; if it's not set, fall back to the provided default value
    """
    return cast_to_boolean(os.environ.get(key, default_val))


def int_from_env(key, default_val):
    """
    cast int value from the environment variables; if it's not set, fall back to the provided default value
    """
    return int(os.environ.get(key, default_val))


def float_from_env(key, default_val):
    """
    cast float value from the environment variables; if it's not set, fall back to the provided default value
    """
    return float(os.environ.get(key, default_val))


def cast_to_boolean(key: str):
    # env vars are strings, so this casts them to a python Boolean
    try:
        key = key.strip()
    except (AttributeError, SyntaxError):
        pass
    if key in ('yes', 'y', 'True', 'true', '1'):
        return True
    if key in ('no', 'n', 'False', 'false', '0', ''):
        return False
    if key in ('none',):
        return None
    return key


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # cyber-smart/src/cyber_smart/
sys.path.append(os.path.join(PROJECT_DIR, 'apps/'))  # cyber-smart/src/cyber_smart/apps/
BASE_DIR = os.path.dirname(os.path.dirname(PROJECT_DIR))  # cyber-smart/
SRC_DIR = os.path.join(BASE_DIR, 'src')  # cyber-smart/src
ROOT_LOCATION = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

ONBOARDING_NOTIFICATIONS = create_list_of_emails(
    get_from_env(
        "ONBOARDING_NOTIFICATIONS",
        ""
    )
)

SUBMISSION_NOTIFICATIONS = create_list_of_emails(
    get_from_env(
        "SUBMISSION_NOTIFICATIONS",
        ""
    )
)

# Setting Development as an default environment
ENVIRONMENT_NAME = get_from_env('ENVIRONMENT_NAME', "Development")
ENVIRONMENT_COLOR = get_from_env('ENVIRONMENT_COLOR', "#808080")
SITE_ID = int(get_from_env('SITE_ID', 1))
# Geographical location of where this Django instance is running
GEO = get_from_env('GEO', '')

IS_UK_GEO = GEO == 'UK'

# Application definition
DEBUG = bool_from_env('DEBUG', True)
IS_DEVELOP = ENVIRONMENT_NAME == "develop"
IS_STAGE = bool_from_env('IS_STAGE', False)
IS_PROD = bool_from_env('IS_PROD', False)
CI_TEST = bool_from_env('CIRCLECI', False)
IS_LOCAL_AUTO_TEST = bool_from_env('IS_LOCAL_AUTO_TEST', False)
AUTO_TEST_ORG_PREFIX = 'AUTOMATEDTEST_org'
AUTO_TEST_TRUSTD_ORG_PREFIX = 'AUTOMATEDTEST_Trustd_Org'
TRUSTD_AUTO_TEST_PARTNER_PREFIX = "Trustd Automated Test Do Not Delete"

if CI_TEST:
    IS_TEST = True
    DEBUG = False
else:
    IS_TEST = bool_from_env('IS_TEST', False)

if ENVIRONMENT_NAME == "Development":
    IS_DEV = True
else:
    IS_DEV = False

CACHALOT_ENABLED = bool_from_env('CACHALOT_ENABLED', False)

CELERY_BEAT_SCHEDULE = {
    # Run every 15 minutes on production.
    # Run more frequently in stage and develop environments to facilitate QA
    # and because the jobs take little time to complete because of small data in the
    # test databases.
    "refresh_installed_software_summary_partner": {
        "task": "dashboard.tasks.refresh_installed_software_summary_partner",
        "schedule": crontab(minute="*/2") if (IS_STAGE or IS_DEVELOP) else crontab(minute="10-59/15"),  # Run every 15 minutes, starting at minute 10
    }
}
# NOTE - as above, fires in all environments e.g. prod, stage, CMMC, BAU, SME
if not IS_DEV:
    CELERY_BEAT_SCHEDULE.update({
        'every_monday_report': {
            'task': 'emails.cron.every_monday_report',
            'schedule': crontab(minute=15, hour=9, day_of_week=1)
        },
        'sync_users': {
            'task': 'organisations.tasks.sync_users',
            'schedule': crontab(minute=20)
        },
        "update_cpe_repository": {
            "task": "vulnerabilities.tasks.update_CPEDB",
            'schedule': crontab(minute="0", hour="*/2")  # every 2 hours
        },
        "update_cve_repository": {
            "task": "vulnerabilities.tasks.update_CVEDB",
            'schedule': crontab(minute="0", hour="*/2")  # every 2 hours
        },
        "check_software_for_recent_vulnerabilities": {
            "task": "appusers.tasks.check_software_for_recent_vulnerabilities",
            "schedule": crontab(minute="0", hour="22")  # once a day at 10 PM
        },
        "check_new_software_for_vulnerabilities": {
            "task": "appusers.tasks.check_new_software_for_vulnerabilities",
            "schedule": crontab(minute='*/15')  # every 15 min
        },
        'pervade_invalid_credentials_notification_task': {
            'task': 'partners.tasks.pervade_invalid_credentials_notification_task',
            'schedule': crontab(minute=59)
        },
        'chargebee_daily_update': {
            'task': 'appusers.tasks.chargebee_daily_update',
            'schedule': crontab(minute=0, hour=0)
        },
        'update_partner_org_quantities': {
            'task': 'billing.tasks.update_partner_org_quantities',
            'schedule': crontab(minute=0, hour=20, day_of_month='28-31')
        },
        'update_partner_subscription_details': {
            'task': 'billing.tasks.update_partner_subscription_details',
            'schedule': crontab(minute=0, hour=0)
        },
        'update_partner_billing_history': {
            'task': 'billing.tasks.update_partner_billing_history',
            'schedule': crontab(minute="10", hour="10", day_of_month='1')
        },
        'invoice_direct_partners_for_unbilled_charges': {
            'task': 'billing.tasks.invoice_direct_partners_for_unbilled_charges',
            'schedule': crontab(minute=0, hour=8, day_of_month='1')
        },
        'send_declaration_signing_reminder': {
            'task': 'organisations.tasks.send_declaration_signing_reminder',
            'schedule': crontab(minute=0, hour=10, day_of_week='MON,WED,FRI')
        },
        'update_android_security_patches': {
            'task': 'security_patches.tasks.update_android_security_patches',
            'schedule': crontab(minute=0, hour=2)
        },
        'migrate_old_issued_certificates': {
            'task': 'rulebook.tasks.migrate_old_issued_certificates',
            'schedule': crontab(minute=0, hour=1)
        },
        'update_certificates_status': {
            'task': 'rulebook.tasks.update_certificates_status',
            'schedule': crontab(minute=0, hour=2)  # once per day
        },
        'send_notification_about_certificates_due_for_renewal': {
            'task': 'rulebook.tasks.send_notification_about_certificates_due_for_renewal',
            'schedule': crontab(minute=0, hour=1, day_of_month=2)
        },
        'send_notification_about_expired_certificates': {
            'task': 'rulebook.tasks.send_notification_about_expired_certificates',
            'schedule': crontab(minute=0, hour=1, day_of_month=2)
        },
        'send_notification_about_in_progress_certificates': {
            'task': 'rulebook.tasks.send_notification_about_in_progress_certificates',
            'schedule': crontab(minute=0, hour=9, day_of_week='*/2')  # roughly every 2 days (divisible by 2)
        },
        'send_notification_about_certificates_with_unsigned_declaration': {
            'task': 'rulebook.tasks.send_notification_about_certificates_with_unsigned_declaration',
            'schedule': crontab(minute=0, hour=10)  # once per day
        },
        'send_notification_about_device_has_outdated_version_and_not_checkedin': {
            'task': 'appusers.tasks.send_notification_about_device_has_outdated_version_and_not_checkedin',
            'schedule': crontab(minute=0, hour=10, day_of_week='0')  # every sunday
        },
        'send_notification_about_device_has_outdated_version': {
            'task': 'appusers.tasks.send_notification_about_device_has_outdated_version',
            'schedule': crontab(minute=0, hour=12, day_of_week='*/2')  # roughly every 2 days (divisible by 2)
        },
        'send_notification_about_device_has_not_checkedin': {
            'task': 'appusers.tasks.send_notification_about_device_has_not_checkedin',
            'schedule': crontab(minute=0, hour=11)  # once per day
        },
        'send_notification_about_user_has_enrolled_but_cap_not_installed': {
            'task': 'appusers.tasks.send_notification_about_user_has_enrolled_but_cap_not_installed',
            'schedule': crontab(minute=0, hour=14)  # once per day
        },
        'send_notification_about_user_has_unagreed_policies': {
            'task': 'appusers.tasks.send_notification_about_user_has_unagreed_policies',
            'schedule': crontab(minute=0, hour=12)  # once per day
        },
        'send_notification_about_user_training_modules': {
            'task': 'lms.tasks.send_notification_about_user_training_modules',
            'schedule': crontab(minute=0, hour=13)  # once per day
        },
        'cert_os_require_attention_status': {
            'task': 'emails.tasks.tasks.cert_os_require_attention_status',
            'schedule': crontab(minute=0, hour=8, day_of_week='1-5')  # only week days
        },
        'update_app_version': {
            'task': 'appusers.cron.update_app_version',
            'schedule': crontab(minute='*/30')
        },
        'upgrade_direct_customers_quantity': {
            'task': 'billing.tasks.upgrade_direct_customers_quantity',
            'schedule': crontab(minute=0, hour=1)
        },
        'upgrade_direct_customers_quantity_v6': {
            'task': 'billing.tasks.upgrade_direct_customers_quantity_v6',
            'schedule': crontab(minute=0, hour=1)
        },
        'security_control': {
            'task': 'partners.tasks.security_control',
            'schedule': crontab(minute=0, hour=8)
        },
        'remove_trial_organisations': {
            'task': 'organisations.tasks.remove_trial_organisations',
            'schedule': crontab(minute=0, hour=18)
        },
        'init_trainings_notify': {
            'task': 'lms.tasks.init_trainings_notify',
            'schedule': crontab(minute=30, hour=6)
        },
        'os_check_end_of_life': {
            'task': 'rulebook.tasks.os_check_end_of_life',
            'schedule': crontab(minute=30, hour=1)
        },
        'send_monthly_superscript_report': {
            'task': 'insurance.tasks.send_monthly_superscript_report',
            'schedule': crontab(minute=50, hour=7, day_of_month='1')
        },
        'certification_billing_renewal_report_emails': {
            'task': 'emails.tasks.tasks.certification_billing_renewal_report_emails',
            'schedule': crontab(minute=0, hour=8, day_of_month='1')
        },
        'update_awareness_training_components': {
            'task': 'smart_score.tasks.update_awareness_training_components',
            'schedule': crontab(minute="*/30", hour="*/1")  # every hour
        },
        'enroll_failed_appusers_to_lms': {
            'task': 'lms.tasks.enroll_failed_appusers_to_lms',
            'schedule': crontab(minute='*/10')
        },
        "update_operating_systems": {
            "task": "operating_systems.tasks.update_operating_systems",
            'schedule': crontab(minute=0, hour=0)  # once per day
        },
        'remind_direct_orgs_of_uninstalled_cap': {
            'task': 'emails.tasks.tasks.remind_direct_orgs_of_uninstalled_cap',
            'schedule': crontab(minute=0, hour=10, day_of_week='1-5/3')  # every 3 days on week days only
        },
        'remind_direct_orgs_of_not_started_certificates': {
            'task': 'emails.tasks.tasks.remind_direct_orgs_of_not_started_certificates',
            'schedule': crontab(minute=0, hour=10, day_of_week='1-5/3')  # every 3 days on week days only
        },
        'remind_direct_orgs_of_incomplete_survey': {
            'task': 'emails.tasks.tasks.remind_direct_orgs_of_incomplete_survey',
            'schedule': crontab(minute=0, hour=9, day_of_week='1')  # every monday
        },
        'remind_direct_orgs_to_book_cep_audit': {
            'task': 'emails.tasks.tasks.remind_direct_orgs_to_book_cep_audit',
            'schedule': crontab(minute=0, hour=8)  # once per day
        },
        'direct_orgs_ce_is_overdue_reminders': {
            'task': 'emails.tasks.tasks.direct_orgs_ce_is_overdue_reminders',
            'schedule': crontab(minute=0, hour=14)  # once per day
        },
        "inactive_devices_cleaning": {
            "task": "organisations.tasks.devices_cleaning",
            'schedule': crontab(minute="10", hour="*/1")  # once per hour
        },
        'delete_old_app_reports': {
            'task': 'analytics.tasks.delete_old_app_reports',
            'schedule': crontab()  # once per minute
        },
        "salesforce_sync": {
            "task": "sales.tasks.sync_salesforce_asm",
            'schedule': crontab(minute="0", hour="*/4")  # every 4 hours
        },
        'delete_old_email_logs': {
            'task': 'emails.tasks.tasks.delete_old_email_logs',
            'schedule': crontab(minute=0, hour=1, day_of_month='28')
        },
        "update_lms_analytic": {
            "task": "lms.tasks.update_analytic_data",
            'schedule': crontab(minute="0", hour="*/4")  # every 4 hours
        },
        'clean_duplicate_history': {
            'task': 'common.tasks.clean_duplicate_history',
            'schedule': crontab(minute=0)  # every hour
        },
        'update_ce_subscription_billing_dates': {
            'task': 'billing.tasks.update_ce_subscription_billing_dates',
            'schedule': crontab(minute=0, hour=20)  # every day
        },
        'os_end_of_life_sync': {
            'task': 'common.tasks.os_end_of_life_sync',
            'schedule': crontab(minute=0, hour=1)  # every day
        },
        'send_distributor_subscriptions_daily_updates_email': {
            'task': 'emails.tasks.tasks.send_distributor_subscriptions_updates_email',
            'schedule': crontab(minute=0, hour=9)  # every day at 9 AM
        },
        'delete_old_daily_subscriptions_updates_csv': {
            'task': 'common.tasks.delete_old_daily_subscriptions_updates_csv',
            'schedule': crontab(minute=10, hour=9)  # every day at 9:10 AM
        },
        'update_devices_risk_state': {
            'task': 'trustd.tasks.update_devices_risk_state',
            'schedule': crontab(minute='0', hour='22')
        },
        'sync_trustd_data': {
            'task': 'trustd.tasks.sync_trustd_data',
            'schedule': crontab(minute='0', hour='23')
        },
        "cleanup_acceptance_test_users": {
            "task": "appusers.tasks.cleanup_acceptance_test_users",
            'schedule': crontab(minute=30, hour=3)
        },
        'cleanup_acceptance_test_orgs': {
            'task': 'organisations.tasks.cleanup_acceptance_test_orgs',
            'schedule': crontab(hour=2, minute=0),
        },
        "send_insurance_sutcliffe_report": {
            "task": "insurance.tasks.send_insurance_sutcliffe_report",
            "schedule": crontab(minute=0, hour=2)
        },
        "clean_silk_old_data": {
            "task": "common.tasks.clean_silk_old_data",
            "schedule": crontab(minute=0, hour=3)
        },
        "delete_old_django_admin_shell_records": {
            "task": "common.tasks.delete_old_django_admin_shell_records",
            "schedule": crontab(minute="0", hour="4", day_of_week="0")
        },
        "delete_old_user_login_attempts": {
            "task": "accounts.tasks.delete_old_user_login_attempts",
            "schedule": crontab(minute=0, hour=4, day_of_month='3')
        },
        "migrate_v5_related_models_to_main_app_install": {
            "task": "appusers.tasks.migrate_v5_related_models_to_main_app_install",
            "schedule": crontab(minute=0, hour=4)
        },
        "send_expiring_partner_certificates_email": {
            "task": "emails.tasks.certification_emails.send_expiring_partner_emails",
            "schedule": crontab(minute=0, hour=13)
        },
        "send_expiring_certification_direct_customer_emails": {
            "task": "emails.tasks.certification_emails.send_expiring_certification_direct_customer_emails",
            "schedule": crontab(minute=0, hour=13)
        },
        "send_expiring_certification_monthly_brigantia_emails": {
            "task": "emails.tasks.certification_emails.send_monthly_expiring_brigantia_partner_emails",
            "schedule": crontab(minute=0, hour=13, day_of_month="1-3")
        },
        "send_expiring_certification_monthly_non_brigantia_emails": {
            "task": "emails.tasks.certification_emails.send_monthly_expiring_non_brigantia_partner_emails",
            "schedule": crontab(minute=0, hour=13, day_of_month="1-3")
        },
        "send_expired_certification_direct_customers_to_ux_team": {
            "task": "emails.tasks.certification_emails.send_expired_direct_customers_certification_to_ux_team",
            "schedule": crontab(minute=0, hour=9)
        },
        "send_expired_certification_partner_customers_to_account_managers": {
            "task": "emails.tasks.certification_emails.send_expired_partner_certificates_to_account_managers",
            "schedule": crontab(minute=0, hour=9)
        },
        "send_certification_upsell_emails_to_partners": {
            "task": "emails.tasks.upsell_emails.send_certification_upsell_emails_to_partners",
            "schedule": crontab(minute=0, hour=13, day_of_month="25-31")
        },
        "send_direct_customer_cap_upsell_email": {
            "task": "emails.tasks.upsell_emails.send_direct_customer_cap_upsell_email",
            "schedule": crontab(minute=0, hour=13)
        },
        "send_direct_customer_cep_upsell_email": {
            "task": "emails.tasks.upsell_emails.send_direct_customer_cep_upsell_email",
            "schedule": crontab(minute=0, hour=13)
        },
        "fetch_opswat_source_file_task": {
            "task": "opswat_patch.tasks.fetch_opswat_source_file.fetch_opswat_source_file_task",
            "schedule": crontab(minute=0, hour="1")
        },
        "timeout_scheduled_installers_task": {
            "task": "opswat_patch.tasks.timeout_scheduled_installers.timeout_scheduled_installers",
            "schedule": crontab(minute=14, hour="*")
        },
        "update_installed_software_individual_for_all_organisations": {
            "task": "appusers.tasks.update_installed_software_individual_for_all_organisations",
            "schedule": crontab(minute=42, hour=4)
        },
    })

# production-specific tasks
if IS_PROD:
    CELERY_BEAT_SCHEDULE.update({
        'users_policies_notify': {
            'task': 'emails.tasks.tasks.users_policies_notify',
            'schedule': crontab(minute=0, hour=4, day_of_week=1)  # once per week
        },
    })

# staging-specific tasks
if not IS_PROD:
    CELERY_BEAT_SCHEDULE.update({
        'cleanup_test_trustd_customers': {
            'task': 'trustd.tasks.cleanup_test_trustd_customers',
            'schedule': crontab(minute="0", hour="4")
        },
    })

CELERY_BEAT_SCHEDULE_TASKS = [schedule["task"] for schedule in CELERY_BEAT_SCHEDULE.values()]
CELERY_BEAT_MAX_LOOP_INTERVAL = 60
REDBEAT_LOCK_TIMEOUT = CELERY_BEAT_MAX_LOOP_INTERVAL * 5

INSTALLED_APPS = [
    # translation of model field values
    'modeltranslation',
    # Django Apps
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',
    "django.contrib.flatpages",
    "django.forms",
    "django.contrib.postgres",
    # 3rd Party Apps
    'storages',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',
    'allauth.socialaccount.providers.microsoft',
    "allauth.socialaccount.providers.salesforce",
    "django_migrations_ci",
    # Konsento integration
    'simple_sso.sso_server',
    'starling',
    'rest_framework',
    'widget_tweaks',
    'ckeditor',
    'ckeditor_uploader',
    'corsheaders',
    'django_extensions',
    'debug_toolbar',
    'silk',
    'import_export',
    'impersonate',
    'django_ratelimit',
    # Configure the django-otp package.
    'django_otp',
    'django_otp.plugins.otp_totp',
    'django_otp.plugins.otp_static',
    'django_otp.plugins.otp_email',
    # Enable two-factor auth.
    'allauth_2fa',
    'drf_yasg',
    'password_policies',
    'oauth2_provider',
    # Models Docs.
    # 'django.contrib.admindocs',
    'django_json_widget',
    'colorful',
    'django_user_agents',
    'waffle',
    'django_admin_shell',
    'admin_auto_filters',
    'simple_history',
    'django_pgviews',
    'versionfield',
    'more_admin_filters',
    'django_celery_results',
    'django_select2',
    'admin_cursor_paginator',
    'citext',
    # translation
    'statici18n',
    'django_deep_translator',
    # Local Apps
    'trustd',
    'beta_features',
    'notifications',
    'common.apps.CommonConfig',
    'analytics.apps.AnalyticsConfig',
    'accounts.apps.AccountsConfig',
    'organisations.apps.OrganisationsConfig',
    'appusers.apps.AppusersConfig',
    'rulebook.apps.RulebookConfig',
    'distributors',
    'lms',
    'learn',
    'partners',
    'security_patches',
    'sales',
    'vodafone',
    'billing.apps.BillingConfig',
    'api.apps.ApiConfig',
    'emails.apps.EmailsConfig',
    'health_check.apps.HealthCheckConfig',
    'social_tags.apps.SocialTagsConfig',
    'insurance.apps.InsuranceConfig',
    'smart_score.apps.SmartScoreConfig',
    "operating_systems.apps.OperatingSystemsConfig",
    'archive.apps.ArchiveConfig',
    'vulnerabilities.apps.VulnerabilitiesConfig',
    'history.apps.HistoryConfig',
    'svg',
    "smart_policies",
    "opswat.apps.OpswatConfig",
    "regions",
    "opswat_patch",
    'articles',
    'appuser_recovery',
    "software_inventory",
    "websocket_integration",
]
if DEBUG and not IS_STAGE and not IS_DEVELOP:
    INSTALLED_APPS = ['whitenoise.runserver_nostatic', 'template_profiler_panel'] + INSTALLED_APPS
if CACHALOT_ENABLED:
    INSTALLED_APPS = ['cachalot', ] + INSTALLED_APPS

CACHALOT_FINAL_SQL_CHECK = True

UNCACHABLE_TABLES = [
    'appusers_appinstallnetworkinterface',
    'appusers_apposinstalledsoftware',
    'appusers_appreport',
    'appusers_checkinappresult',
    'appusers_checkresult',
    'appusers_appinstallcheckstatus',
    'appusers_userloginhistory',
    'trustd_trustddeviceriskstate',
    'django_migrations',
    'django_session',
    'silk_request',
    'silk_response',
    'opswat_cve_product_version',
    'opswat_installedproduct',
    'opswat_installedproductversion',
    'opswat_product',
    'opswat_productversion',
    'appusers_installedsoftwareappinstallindividual',
    'software_inventory_installedsoftwareorganisationindividual',
    'appusers_installedsoftwareappinstallsummary',
    'appusers_installedsoftwareorganisationsummary',
    'appusers_installedsoftwarepartnersummary',
]
# temporarily introduced to allow for env var list
CACHALOT_UNCACHABLE_TABLES = env.list('CACHALOT_UNCACHABLE_TABLES', default=UNCACHABLE_TABLES)

CACHALOT_UNCACHABLE_APPS = [
    'archive',
    'history',
]

if bool_from_env('CACHALOT_DEBUG', False):
    CACHALOT_TABLE_KEYGEN = 'common.utils.get_table_cache_key'
    CACHALOT_QUERY_KEYGEN = 'common.utils.get_query_cache_key'

CACHALOT_TIMEOUT = int_from_env('CACHALOT_TIMEOUT', 60 * 60 * 24)  # None is forever / keep cache for 1 day (setting in seconds)

MIDDLEWARE = [
    'common.middleware.HealthCheckMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'waffle.middleware.WaffleMiddleware',
    'common.middleware.CustomSilkyMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    # our custom locale middleware does not need to be before CommonMiddleware
    # since we do not use language redirects from i18n_patterns
    'common.middleware.CustomLocaleMiddleware',
    "common.middleware.TokenAuthenticationMiddleware",
    # 'django.contrib.auth.middleware.SessionAuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    # Configure the django-otp package. Note this must be after the
    # AuthenticationMiddleware.
    'django_otp.middleware.OTPMiddleware',
    'debug_toolbar.middleware.DebugToolbarMiddleware',
    "django.contrib.flatpages.middleware.FlatpageFallbackMiddleware",

    'common.middleware.Enforce2FAMiddleware',
    # 'accounts.middleware.OneSessionPerUserMiddleware',
    'common.middleware.CustomImpersonateMiddleware',
    'accounts.middleware.AffiliateMiddleware',
    "django_user_agents.middleware.UserAgentMiddleware",
    'simple_history.middleware.HistoryRequestMiddleware',
    "allauth.account.middleware.AccountMiddleware",
    'common.middleware.CustomMiddleware',
]

DATA_UPLOAD_MAX_NUMBER_FIELDS = get_from_env('DATA_UPLOAD_MAX_NUMBER_FIELDS', None)
# Setting Data upload max memory size to 100MB(in bytes) to match our nginx config limits to 100MB
DATA_UPLOAD_MAX_MEMORY_SIZE = get_from_env('DATA_UPLOAD_MAX_MEMORY_SIZE', *********)
FILE_UPLOAD_MAX_MEMORY_SIZE = get_from_env('FILE_UPLOAD_MAX_MEMORY_SIZE', *********)
ROOT_URLCONF = get_from_env('ROOT_URLCONF', 'cyber_smart.urls')

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            os.path.join(BASE_DIR, 'templates'),
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'common.context_processors.app_settings',
                'django.template.context_processors.i18n',
            ],
            'libraries': {
                'staticfiles': 'django.templatetags.static',
            }
        },
    },
]

USER_BACKEND = 'accounts.backends.RoleBackend'
AUTHENTICATION_BACKENDS = (
    # Needed to login by username in Django admin, regardless of `allauth`
    USER_BACKEND,
    # `allauth` specific authentication methods, such as login by e-mail
    'allauth.account.auth_backends.AuthenticationBackend',
    'sesame.backends.ModelBackend'
)
WSGI_APPLICATION = 'cyber_smart.wsgi.application'


# Client Applications in order for them to authenticate against our API.
# Please refer to https://cybersmart.atlassian.net/wiki/spaces/PE/pages/**********/Oauth+Client+Application
# for more information and if you need to make any changes to the details below.
TRUSTD_CLIENT_ID = get_from_env('TRUSTD_CLIENT_ID', "pquyHAaG9OsiZHVkCnQdbfgRhu7TxFAHdstNgGnq")
TRUSTD_CLIENT_SECRET = get_from_env('TRUSTD_CLIENT_SECRET', "")
STARLING_CLIENT_ID = get_from_env('STARLING_CLIENT_ID', "7qCuXBDAoYmsNn0vnPTbGo68aq7wr4NeQhgA3ECt")
STARLING_CLIENT_SECRET = get_from_env('STARLING_CLIENT_SECRET', "")
CIRCLE_SO_CLIENT_ID = get_from_env('CIRCLE_SO_CLIENT_ID', "EGhYytZhP8G0L5iR44U0Oj4q2nvgIalhlnFvy75M")
CIRCLE_SO_CLIENT_SECRET = get_from_env('CIRCLE_SO_CLIENT_SECRET', "")
CIRCLE_SO_REDIRECT_URL = get_from_env('CIRCLE_SO_REDIRECT_URL', "https://www.cybersmart.community/oauth2/callback")


def is_pkce_required(client_id):
    if client_id in [STARLING_CLIENT_ID, CIRCLE_SO_CLIENT_ID]:
        return False
    return True


OAUTH2_PROVIDER = {
    'OAUTH2_BACKEND_CLASS': 'oauth2.oauth2_libcore.MultipleContentTypeOAuthLibCore',
    "PKCE_REQUIRED": is_pkce_required,
    'APPLICATION_ADMIN_CLASS': 'oauth2.admin.CustomApplicationAdmin',
    'SCOPES': {"read": "Reading scope", "write": "Writing scope", "user:read": "Read name and email"},
}

# django-allauth Configuration
# http://django-allauth.readthedocs.io/en/latest/configuration.html

LOGIN_URL = get_from_env('LOGIN_URL', '/login/')
# LOGIN_REDIRECT_URL = '/a/signup/profile/'
LOGIN_REDIRECT_URL = get_from_env('LOGIN_REDIRECT_URL', '/')
ACCOUNT_ADAPTER = get_from_env('ACCOUNT_ADAPTER', 'common.adapter.CustomDefaultAccountAdapter')
# (="username" | "email" | "username_email")
ACCOUNT_AUTHENTICATION_METHOD = get_from_env('ACCOUNT_AUTHENTICATION_METHOD', 'email')

ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_EMAIL_VERIFICATION = get_from_env('ACCOUNT_EMAIL_VERIFICATION', 'none')  # (="mandatory" | "optional" | "none")
ACCOUNT_EMAIL_SUBJECT_PREFIX = get_from_env('ACCOUNT_EMAIL_SUBJECT_PREFIX', 'CyberSmart ')
ACCOUNT_FORMS = {
    'reset_password_from_key': 'common.forms.ResetPasswordKeyCustomForm',
    'change_password': 'common.forms.ChangePasswordCustomForm',
    'set_password': 'common.forms.SetPasswordCustomForm'
}
ACCOUNT_EMAIL_CONFIRMATION_HMAC = False
ACCOUNT_EMAIL_CONFIRMATION_COOLDOWN = 60 * 60  # 1 hour
ACCOUNT_LOGIN_ATTEMPTS_LIMIT = get_from_env('ACCOUNT_LOGIN_ATTEMPTS_LIMIT', 5)
ACCOUNT_LOGIN_ATTEMPTS_TIMEOUT = get_from_env('ACCOUNT_LOGIN_ATTEMPTS_TIMEOUT', 1800)  # 30 minutes
ACCOUNT_LOGOUT_ON_GET = True
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_USER_MODEL_USERNAME_FIELD = 'username'
ACCOUNT_USER_MODEL_EMAIL_FIELD = 'email'
ACCOUNT_USERNAME_MIN_LENGTH = 5
ACCOUNT_SESSION_REMEMBER = None
ACCOUNT_AUTHENTICATED_LOGIN_REDIRECTS = True
ACCOUNT_LOGIN_REDIRECT_URL = '/'
ACCOUNT_SIGNUP_PASSWORD_ENTER_TWICE = False

# Konsento URL
PRIVACY_TOOLBOX_URL = get_from_env('PRIVACY_TOOLBOX_URL', 'http://0.0.0.0:8111/client/')

ACCOUNT_RATE_LIMITS = {
    # Change password view (for users already logged in)
    "change_password": "5/m",
    # Email management (e.g. add, remove, change primary)
    "manage_email": "10/m",
    # Request a password reset, global rate limit per IP
    "reset_password": "20/m",
    # Rate limit measured per individual email address
    "reset_password_email": "5/m",
    # Password reset (the view the password reset email links to).
    "reset_password_from_key": "20/m",
    # Signups.
    "signup": "20/m",
    # NOTE: Login is already protected via `ACCOUNT_LOGIN_ATTEMPTS_LIMIT`
}

SOCIALACCOUNT_LOGIN_ON_GET = True
SOCIALACCOUNT_ADAPTER = 'common.adapter.MyAdapter'
SOCIALACCOUNT_STORE_TOKENS = True

SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'SCOPE': [
            'https://www.googleapis.com/auth/admin.directory.user.readonly',
            'profile',
            'email',
        ],
        'AUTH_PARAMS': {
            'prompt': 'consent',
            'access_type': 'offline',  # required for offline sync
            'include_granted_scopes': 'true',
        }
    },
    'microsoft': {
        'SCOPE': [
            'openid',
            'profile',
            'email',
            'User.read',
            'User.ReadBasic.All',
            'offline_access',
        ],
    },
}

# OTP Configuration
OTP_EMAIL_TOKEN_VALIDITY = 3600
ALLAUTH_2FA_ALWAYS_REVEAL_BACKUP_TOKENS = True

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
    {
        'NAME': 'common.password_validation.HasAtleastOneNumberPasswordValidator',
    },
    {
        'NAME': 'common.password_validation.UppercaseValidator',
    },
    {
        'NAME': 'common.password_validation.LowercaseValidator',
    },
]

# Localisation
# When adding a new language, please run makemigrations to update dynamic translation fields.
# See https://cybersmart.atlassian.net/wiki/spaces/PE/pages/4935254042/How+to+add+update+translations#Dynamic-text
LANGUAGES = [
    ("en-gb", "English"),
    ("sv", "Svenska"),
    ('de', 'Deutsch'),
    ('fr', 'Français'),
    ('it', 'Italiano'),
    ('nl', 'Nederlands'),
    ('pl', 'Polski'),
]
PO_TRANSLATOR_SERVICE = 'django_deep_translator.services.DeeplTranslatorService'
# only used when translating messages with "python manage.py translate_messages"
DEEPL_TRANSLATE_KEY = get_from_env('DEEPL_TRANSLATE_KEY', "fake_key")
DEEPL_FREE_API = False
TIME_ZONE = get_from_env('TIME_ZONE', 'Europe/London')
USE_TZ = True
LANGUAGE_CODE = 'en-gb'
USE_I18N = True
LOCALE_PATHS = [
    os.path.join(BASE_DIR, 'locale'),
    os.path.join(BASE_DIR, 'templates', 'locale'),
    os.path.join(BASE_DIR, 'templates', 'account', 'locale'),
    os.path.join(BASE_DIR, 'templates', 'dashboard', 'locale'),
    os.path.join(BASE_DIR, 'templates', 'distributors', 'locale'),
    os.path.join(BASE_DIR, 'templates', 'partials', 'locale'),
    os.path.join(BASE_DIR, 'templates', 'partners', 'locale'),
    os.path.join(BASE_DIR, 'templates', 'rulebook', 'locale'),
    os.path.join(BASE_DIR, 'templates', 'signup', 'locale'),
    os.path.join(BASE_DIR, 'templates', 'socialaccount', 'locale'),
    os.path.join(BASE_DIR, 'static', 'js', 'locale'),
]
MODELTRANSLATION_AUTO_POPULATE = 'default'

# CKEDITOR
CKEDITOR_JQUERY_URL = '/static/js/jquery.2.2.4.min.js'
CKEDITOR_UPLOAD_PATH = 'uploads/'
AWS_QUERYSTRING_AUTH = False
# CKEditor became a paid software and marked newer versions as insecure.
# So, we limit CKEditor usage only for internal users (such as in Django Admin).
# And ignore the vulnerability check.
# https://github.com/django-ckeditor/django-ckeditor/issues/761
CKEDITOR_CONFIGS = {
    'html_only': {
        'startupMode': 'source',
    },
    'default': {
        'versionCheck': False,
        'toolbar': 'full',
        'extraPlugins': 'youtube,html5video',
    },
}

# AWS config
MIGRATECI_STORAGE = get_from_env('MIGRATECI_STORAGE', 'cyber_smart.migrationci_storage.MigrateCIStorage')
AWS_DEFAULT_REGION = get_from_env('AWS_DEFAULT_REGION', None)
AWS_ACCESS_KEY_ID = get_from_env('AWS_ACCESS_KEY_ID', None)
AWS_SECRET_ACCESS_KEY = get_from_env('AWS_SECRET_ACCESS_KEY', None)
# AWS assume role config
AWS_ROLE_ARN = get_from_env('AWS_ROLE_ARN', None)
CS_AWS_CAP_V5_ASSUME_ROLE = get_from_env('CS_AWS_CAP_V5_ASSUME_ROLE', None)
AWS_SESSION_NAME = get_from_env('AWS_SESSION_NAME', None)
# SQS
SQS_ENDPOINT_URL = get_from_env('SQS_ENDPOINT_URL', None)
DEVICES_SQS_QUEUE_URL = get_from_env('DEVICES_SQS_QUEUE_URL', None)
# DynamoDB
WEB_NOTIFICATIONS_TABLE_NAME = get_from_env('WEB_NOTIFICATIONS_TABLE_NAME', None)

# Cloudflare R2 Storage Settings
CLOUDFLARE_ACCOUNT_ID = get_from_env('CLOUDFLARE_ACCOUNT_ID', '6f1033ac32bcaf9c99013a8ef939e755')
CLOUDFLARE_ACCESS_KEY_ID = get_from_env('CLOUDFLARE_ACCESS_KEY_ID', 'bc6ba733f3f66d8815ab78c0beb28dc9')
CLOUDFLARE_SECRET_ACCESS_KEY = get_from_env('CLOUDFLARE_SECRET_ACCESS_KEY', '')
CLOUDFLARE_DB_BUCKET = get_from_env('CLOUDFLARE_DB_BUCKET', 'cs-opswat-db-develop')
CLOUDFLARE_DB_REGION = get_from_env('CLOUDFLARE_DB_REGION', 'auto')

STORAGES = {
    "default": {
        "BACKEND": get_from_env('DEFAULT_FILE_STORAGE', 'django.core.files.storage.FileSystemStorage'),
    },
    "staticfiles": {
        "BACKEND": get_from_env('STATICFILES_STORAGE', 'django.contrib.staticfiles.storage.StaticFilesStorage'),
    },
    "cloudflare_r2": {
        "BACKEND": "storages.backends.s3.S3Storage",
        "OPTIONS": {
            "access_key": CLOUDFLARE_ACCESS_KEY_ID,
            "secret_key": CLOUDFLARE_SECRET_ACCESS_KEY,
            "bucket_name": CLOUDFLARE_DB_BUCKET,
            "region_name": CLOUDFLARE_DB_REGION,
            "endpoint_url": f"https://{CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com",
            "file_overwrite": True,
        },
    },
}

WHITENOISE_ROOT = os.path.join(BASE_DIR, 'static_root')
AWS_S3_ENCRYPTION = bool_from_env('AWS_S3_ENCRYPTION', True)
# S3 (MEDIA FILES ONLY) django-storages + boto3
AWS_STORAGE_BUCKET_NAME = get_from_env('AWS_STORAGE_BUCKET_NAME', None)
AWS_S3_CUSTOM_DOMAIN = get_from_env('AWS_S3_CUSTOM_DOMAIN', None)
AWS_DEFAULT_ACL = get_from_env('AWS_DEFAULT_ACL', None)

STATICFILES_DIRS = (
    os.path.join(BASE_DIR, 'static'),
)

STATIC_URL = get_from_env('STATIC_URL', '/static/')
MEDIA_URL = get_from_env('MEDIA_URL', '/media/')
STATIC_ROOT = os.path.join(BASE_DIR, 'public', 'static')
# this is required to include the i18n files in the static manifest
STATICI18N_ROOT = os.path.join(BASE_DIR, 'static')
MEDIA_ROOT = os.path.join(BASE_DIR, 'public', 'media')
# this is for celery using rabbitmq and the i18 plugin - fixes inability to run tests
STATICI18N_DOMAIN = 'djangojs'
STATICI18N_NAMESPACE = None
STATICI18N_PACKAGES = ('django.conf')
STATICI18N_OUTPUT_DIR = 'jsi18n'
STATICI18N_FILENAME_FUNCTION = 'statici18n.utils.default_filename'
# folder for temporary files
FILE_UPLOAD_TEMP_DIR = get_from_env('FILE_UPLOAD_TEMP_DIR', '/tmp')
IMAGE_ALLOWED_EXTENSIONS = ['jpeg', 'png', 'jpg']

DEFAULT_HANDLERS = ['log_file', 'console'] if DEBUG else ['log_file']
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    # 'root': {
    #     'level': 'WARNING',
    #     'handlers': ['sentry'],
    # },
    'formatters': {
        'verbose': {
            'format': '[%(asctime)s] %(levelname)s %(message)s (%(module)s %(process)d %(thread)d)'
        },
    },
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse'
        },
        'queries_above_300ms': {
            '()': 'django.utils.log.CallbackFilter',
            'callback': lambda record: record.duration > 0.3  # output slow queries only
        },
    },
    'handlers': {
        'log_file': {
            'level': 'DEBUG',
            'class': 'common.utils.DeferredFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'django.log'),
            'maxBytes': 1024 * 1024 * 1024,  # 1 GB
            'backupCount': 5,
            'formatter': 'verbose',
        },
        'slow_q_log_file': {
            'level': 'DEBUG',
            'class': 'common.utils.DeferredFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'slow_q_log_file.log'),
            'maxBytes': 50000,
            'backupCount': 2,
            'formatter': 'verbose',
            'filters': ['queries_above_300ms'],
        },
        'mail_admins': {
            'level': 'ERROR',
            'class': 'django.utils.log.AdminEmailHandler',
            'filters': ['require_debug_false'],
            'include_html': True,
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        }
    },
    'loggers': {
        'default': {
            'handlers': DEFAULT_HANDLERS,
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': True,
        },
        'django.request': {
            'handlers': DEFAULT_HANDLERS,
            'level': 'ERROR',
            'propagate': False,
        },
        'django.db.backends': {
            'level': 'ERROR',
            'handlers': ['console'],
            'propagate': False,
        },
        'django.db': {
            'handlers': ['slow_q_log_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'sentry.errors': {
            'level': 'DEBUG',
            'handlers': ['console'],
            'propagate': False,
        },
        'opswat_patch': {
            'handlers': DEFAULT_HANDLERS,
            'level': 'DEBUG' if DEBUG else 'ERROR',
            'propagate': True,
        },
    },
}

ACCOUNT_USERNAME_REQUIRED = False
SOCIALACCOUNT_QUERY_EMAIL = True

IMPERSONATE = {
    'CUSTOM_ALLOW': 'common.middleware.can_user_impersonate',
    'CUSTOM_USER_QUERYSET': 'common.middleware.users_impersonable',
    'REDIRECT_FIELD_NAME': 'next',
    'SEARCH_FIELDS': ['first_name', 'last_name', 'email', 'groups__name'],
}
if not IS_PROD:
    IMPERSONATE['ADMIN_DELETE_PERMISSION'] = True


# SILK
def if_user_is_superuser(user):
    return user.is_superuser


# Define patterns for interception
API_PATTERN = re.compile(r'^/api/')
OAUTH2_PATTERN = re.compile(r'^/oauth2/')
STARLING_PATTERN = re.compile(r'^/starling/')


def silk_intercept(request):
    """
    Determine if the request should be intercepted by Silk.

    Interception is based on the request path and environment settings.
    Requests to paths starting with '/api/', '/oauth2/', or '/starling/' are intercepted.
    """
    # Skip interception if running tests
    if IS_TEST:
        return False

    # Check if the request path matches any of the defined patterns
    path = request.path
    if API_PATTERN.match(path) or OAUTH2_PATTERN.match(path) or STARLING_PATTERN.match(path):
        return True

    return False


SILKY_AUTHENTICATION = bool_from_env("SILKY_AUTHENTICATION", True)
SILKY_AUTHORISATION = bool_from_env("SILKY_AUTHORISATION", True)
SILKY_PERMISSIONS = if_user_is_superuser
SILKY_INTERCEPT_FUNC = silk_intercept
SILKY_ANALYZE_QUERIES = bool_from_env("SILKY_ANALYZE_QUERIES", False)
SILKY_SENSITIVE_KEYS = {str(uuid4())}
SILKY_MAX_RECORDED_REQUESTS = int_from_env("SILKY_MAX_RECORDED_REQUESTS", 1_000)
SILKY_INTERCEPT_PERCENT = int_from_env("SILKY_INTERCEPT_PERCENT", 100)
SILKY_MAX_REQUEST_BODY_SIZE = int_from_env("SILKY_MAX_REQUEST_BODY_SIZE", -1)
SILKY_MAX_RESPONSE_BODY_SIZE = int_from_env("SILKY_MAX_RESPONSE_BODY_SIZE", -1)
# Ignore analyzing queries in Silk by default, as it's currently used just for error tracing.
# https://github.com/jazzband/django-silk/pull/284#issuecomment-389060471
SILKY_IGNORE_QUERIES = env.list("SILKY_IGNORE_QUERIES", default=None) or ['']
SILKY_META = bool_from_env("SILKY_META", False)
SILKY_PYTHON_PROFILER = bool_from_env("SILKY_PYTHON_PROFILER", False)
SILKY_CACHE_LIFETIME = int_from_env("SILKY_CACHE_LIFETIME", 600)
SILKY_ENABLED = bool_from_env("SILKY_ENABLED", True)
SILKY_MAX_RECORDED_REQUESTS_CHECK_PERCENT = int_from_env("SILKY_MAX_RECORDED_REQUESTS_CHECK_PERCENT", 0)
# Number of days to keep data before it is eligible for deletion
SILKY_DATA_RETENTION_DAYS = int_from_env("SILKY_DATA_RETENTION_DAYS", 30)

# CELERY Stuff
# redis on prod uses allkeys-lfu: Keeps frequently used keys; removes least frequently used (LFU) keys - regardless of Expiry
REDIS_URL = get_from_env('REDIS_URL', 'redis://localhost:6379')
# perm redis uses volatile-lru: this only writes over keys that have Expiry set
REDIS_PERSISTENT_URL = get_from_env('REDIS_PERSISTENT_URL', REDIS_URL)
CELERY_BROKER_URL = get_from_env('CELERY_BROKER_URL', 'amqp://admin:password@rabbitmq:5672/vhost')

# When RabbitMQ is stressed (out of memory or disk space)
# task.delay/task.apply_async calls will fail silently if confirm_publish is set to False,
# which is the default. It's better to fail loud than silently when this happens See:
# https://github.com/celery/celery/issues/5410
# If confirm_timeout passes, a socket.timeout exception is raised:
CELERY_BROKER_TRANSPORT_OPTIONS = {"confirm_publish": True, "confirm_timeout": 5.0}

# celery results, enabled by default, but can be overridden in custom base task classes
CELERY_TASK_IGNORE_RESULT = False
CELERY_RESULT_BACKEND = get_from_env('CELERY_RESULT_BACKEND', 'django-db')
CELERY_RESULT_EXTENDED = bool_from_env('CELERY_RESULT_EXTENDED', True)
CELERY_RESULT_EXPIRES = int_from_env('CELERY_RESULT_EXPIRES', 604800)

CELERY_TIMEZONE = TIME_ZONE
CELERY_ENABLE_UTC = False
CELERY_TASK_ACKS_LATE = bool_from_env("CELERY_TASK_ACKS_LATE", False)
# Reject tasks if a worker is lost to prevent silent retries or task loss.
CELERY_TASK_REJECT_ON_WORKER_LOST = bool_from_env("CELERY_TASK_REJECT_ON_WORKER_LOST", True)
# this is for memory leaks
CELERY_WORKER_MAX_TASKS_PER_CHILD = int_from_env('CELERY_MAX_TASKS_PER_CHILD', 1000)
CELERY_WORKER_POOL_RESTARTS = True
CELERY_WORKER_CONCURRENCY = int_from_env('CELERY_WORKER_CONCURRENCY', 4)
# for queue settings, see celery_app.py
CELERY_REDBEAT_REDIS_URL = REDIS_PERSISTENT_URL + '/4'

# DRF
REST_FRAMEWORK = {
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
    ),
    'DEFAULT_PARSER_CLASSES': (
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser'
    ),
    "DEFAULT_THROTTLE_RATES": {
        "partner": get_from_env("PARTNER_THROTTLE_RATE", "100/hour")
    }
}
SWAGGER_SETTINGS = {
    'LOGOUT_URL': '/logout/',
    "VALIDATOR_URL": None
}

# SECURITY
SECURE_BROWSER_XSS_FILTER = True
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_HSTS_SECONDS = int_from_env('SECURE_HSTS_SECONDS', 0)

# Security SSL
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_SSL_REDIRECT = bool_from_env('SECURE_SSL_REDIRECT', False)

# Lead registration url for iframe
LEAD_REGISTRATION_URL = os.environ.get('LEAD_REGISTRATION_URL',
                                       'https://share.hsforms.com/1zU3cncfdS_Sl79XKwpIXPw2p7bx?')

# Affiliate
AFFILIATE_PARAM_NAME = get_from_env('AFFILIATE_PARAM_NAME', 'a')
AFFILIATE_REWARD_PERCENTAGE = get_from_env('AFFILIATE_REWARD_PERCENTAGE', False)
AFFILIATE_REWARD_AMOUNT = get_from_env('AFFILIATE_REWARD_AMOUNT', 50)

# AID patterns
AFFILIATE_PATTERNS = {
    'IASME': [True, 20]  # AID: [AFFILIATE_REWARD_PERCENTAGE, AFFILIATE_REWARD_AMOUNT]
}


@dataclass(frozen=True)
class BUILD_TYPES:
    MSI = 'msi'
    PKG = 'pkg'


# live links to get transformed built files - no forward slash
BULK_LIVE_LINK_SERVERS = {
    BUILD_TYPES.MSI: get_from_env('BULK_DOWNLOAD_MSI_SERVER', 'https://staging-desktop-msi.cybersmart.co.uk', throw_on_not_set=not IS_TEST),
    BUILD_TYPES.PKG: get_from_env('BULK_DOWNLOAD_PKG_SERVER', 'https://staging-desktop-pkg.cybersmart.co.uk', throw_on_not_set=not IS_TEST)
}
# this is used to set the last 2 paramaters on bulk package creation
# e.g. 'app/nl' > master-nl desktop app, reporting to nl.cybersmart.com
# e.g. 'thats/nl-staging' > staging-nl desktop app, reporting to nl-staging.cybersmart.com
# e.g. 'bau/nl-bau' = BAU-dev-nl desktop app, reporting to nl-bau.cybersmart.com
# in legacy UK environments where region is not set, this is not required - final urls will follow format /{uuid}/{sub_domain}
BULK_LIVE_LINK_SUFFIX = get_from_env('BULK_LIVE_LINK_SUFFIX', False)

CAP_V5_BULK_LIVE_LINK_SERVERS = {
    BUILD_TYPES.MSI: get_from_env('CAP_V5_BULK_DOWNLOAD_MSI_SERVER', 'https://cs-develop-windows-distribution.cybersmart.co.uk', throw_on_not_set=not IS_TEST),
    BUILD_TYPES.PKG: get_from_env('CAP_V5_BULK_DOWNLOAD_PKG_SERVER', 'https://cs-develop-macos-distribution.cybersmart.co.uk', throw_on_not_set=not IS_TEST)
}

APP_BUILD_S3_BUCKET_NAME = get_from_env('APP_BUILD_S3_BUCKET_NAME', 'cs-cap-v5-builds', throw_on_not_set=not IS_TEST)
APP_BUILD_S3_FILE_NAME_PREFIX = get_from_env('APP_BUILD_S3_FILE_NAME_PREFIX', 'CyberSmart', throw_on_not_set=not IS_TEST)

# Add Bootstrap CSS classes to Django ones
# Django classes for the admin pages
# Bootstrap classes for the rest
MESSAGE_TAGS = {
    messages.DEBUG: 'alert-info info',
    messages.INFO: 'alert-info info',
    messages.SUCCESS: 'alert-success success',
    messages.WARNING: 'alert-warning warning',
    messages.ERROR: 'alert-danger error',
}
DEBUG_TOOLBAR_CONFIG = {
    'SHOW_TOOLBAR_CALLBACK': 'common.middleware.show_toolbar',
    'SHOW_COLLAPSED': True,
    'RESULTS_CACHE_SIZE': 100,
    'DISABLE_PANELS': [
        'debug_toolbar.panels.history.HistoryPanel',
        'debug_toolbar.panels.versions.VersionsPanel',
        'debug_toolbar.panels.settings.SettingsPanel',
        'debug_toolbar.panels.headers.HeadersPanel',
        'debug_toolbar.panels.sql.SQLPanel',
        'debug_toolbar.panels.staticfiles.StaticFilesPanel',
        'debug_toolbar.panels.templates.TemplatesPanel',
        'debug_toolbar.panels.cache.CachePanel',
        'debug_toolbar.panels.signals.SignalsPanel',
        'debug_toolbar.panels.logging.LoggingPanel',
        'debug_toolbar.panels.redirects.RedirectsPanel',
        'debug_toolbar.panels.profiling.ProfilingPanel',
    ]
}
# DISABLE_PANELS hides the panels by default, and disables the collector, can be enabled by the user for non AJAX requests
# In order to store these requests, we must default enable the panels for Development
# Disabling RENDER_PANELS stores all requests in memory and loads panels over AJAX
if IS_DEV:
    DEBUG_TOOLBAR_CONFIG['DISABLE_PANELS'].remove('debug_toolbar.panels.history.HistoryPanel')
    DEBUG_TOOLBAR_CONFIG['DISABLE_PANELS'].remove('debug_toolbar.panels.sql.SQLPanel')
    DEBUG_TOOLBAR_CONFIG['RENDER_PANELS'] = False

DJANGO_TOOLBAR_PROD = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
]
DEBUG_TOOLBAR_PANELS = [
    'debug_toolbar.panels.history.HistoryPanel',
    'debug_toolbar.panels.versions.VersionsPanel',
    'debug_toolbar.panels.timer.TimerPanel',
    'debug_toolbar.panels.settings.SettingsPanel',
    'debug_toolbar.panels.headers.HeadersPanel',
    'debug_toolbar.panels.request.RequestPanel',
    'debug_toolbar.panels.sql.SQLPanel',
    'debug_toolbar.panels.staticfiles.StaticFilesPanel',
    'debug_toolbar.panels.templates.TemplatesPanel',
    'debug_toolbar.panels.cache.CachePanel',
    'debug_toolbar.panels.signals.SignalsPanel',
    'debug_toolbar.panels.logging.LoggingPanel',
    'debug_toolbar.panels.redirects.RedirectsPanel',
    'debug_toolbar.panels.profiling.ProfilingPanel',
]
if CACHALOT_ENABLED:
    DEBUG_TOOLBAR_PANELS = ['cachalot.panels.CachalotPanel', ] + DEBUG_TOOLBAR_PANELS

if DEBUG:
    DEBUG_TOOLBAR_PANELS += ['template_profiler_panel.panels.template.TemplateProfilerPanel']

# Hacky way to turn off runtime warning
# a naive datetime while time zone support is active.
warnings.filterwarnings(action='ignore', category=RuntimeWarning)

SILENCED_SYSTEM_CHECKS = []

CHARGEBEE = {
    'API_KEY': get_from_env('CHARGEBEE_API_KEY', 'test_aWoiNxdX4F5J1k7iocpcurwZBCsmCCIAh'),
    'SITE_NAME': get_from_env('CHARGEBEE_SITE_NAME', 'cybersmart-test'),
    'GW_ACCOUNT_ID': get_from_env('CHARGEBEE_GW_ACCOUNT_ID', 'gw_Hr5516gR3qcMwJpMA'),
}

AVIVA_DEFAULT_PARTNER_NAME = get_from_env('AVIVA_DEFAULT_PARTNER_NAME', "CyberSmart Direct")
AVIVA_DISTRIBUTOR_NAME = get_from_env('AVIVA_DISTRIBUTOR_NAME', "CyberSmart Direct")

# authentication token life time
SESAME_MAX_AGE = get_from_env('SESAME_MAX_AGE', 600)
# in seconds

CORS_ALLOW_ALL_ORIGINS = bool_from_env('CORS_ORIGIN_ALLOW_ALL', True)

# CYBERSMART DIST NAME
DEFAULT_CS_DIST_NAME = get_from_env('DEFAULT_CS_DIST_NAME', 'CyberSmart')
DEFAULT_CS_DIRECT_DIST_NAME = get_from_env('DEFAULT_CS_DIRECT_DIST_NAME', 'CyberSmart Direct')
DEFAULT_CS_PARTNER_NAME = get_from_env('DEFAULT_CS_PARTNER_NAME', 'CyberSmart Direct')

# CYBERSMART CANCELLED DISTRIBUTORS AND PARTNERS
CS_DIST_CANCELLED_DIRECT_CUSTOMER_NAME = 'CyberSmart Cancelled (Direct customer)'
CS_DIST_CANCELLED_PARTNER_CUSTOMER_NAME = 'CyberSmart Cancelled (Partner customer)'
CS_PARTNER_CANCELLED_DIRECT_CUSTOMER_NAME = 'CyberSmart Cancelled (Direct customer)'
CS_PARTNER_CANCELLED_PARTNER_CUSTOMER_NAME = 'CyberSmart Cancelled (Partner customer)'
CANCELLED_PARTNER_NAMES = [CS_PARTNER_CANCELLED_PARTNER_CUSTOMER_NAME, CS_PARTNER_CANCELLED_DIRECT_CUSTOMER_NAME]
# CyberSmart Demo Mode Partner
CS_DEMO_PARTNER_NAME = get_from_env('CS_DEMO_PARTNER_NAME', 'Demo Mode Partner')
# SYNAXON DIST NAME
SYNAXON_DIST_NAME = get_from_env('SYNAXON_DIST_NAME', 'Synaxon')
EXERTIS_DIST_NAME = get_from_env('EXERTIS_DIST_NAME', 'Exertis')

WINDOWS_APP_VERSION_URL = get_from_env(
    "WINDOWS_APP_VERSION_URL",
    "https://cs-desktop-auto-update-public.s3.eu-west-2.amazonaws.com/staging/latest/version-msi.txt"
)
MACOS_APP_VERSION_URL = get_from_env(
    "MACOS_APP_VERSION_URL",
    "https://cs-desktop-auto-update-public.s3.eu-west-2.amazonaws.com/staging/latest/version-pkg.txt"
)
TRUSTD_ANDROID_MARKETPLACE_URL = get_from_env(
    "TRUSTD_ANDROID_MARKETPLACE_URL",
    "https://play.google.com/store/apps/details?id=com.cybersmart.activeprotect"
)
TRUSTD_IOS_MARKETPLACE_URL = get_from_env(
    "TRUSTD_IOS_MARKETPLACE_URL",
    "https://apps.apple.com/ua/app/cybersmart-active-protect/id6504763737"
)

GOCARDLESS_TOKEN = get_from_env('GOCARDLESS_TOKEN', 'sandbox_D9VVTHYxVAxE9IOdGcG3A1U8EhF4CbX1BZJlhv94')
PHONENUMBER_DB_FORMAT = get_from_env('PHONENUMBER_DB_FORMAT', "NATIONAL")
PHONENUMBER_DEFAULT_REGION = get_from_env('PHONENUMBER_DEFAULT_REGION', "GB")
HEALTH_CHECK_ORGANISATION_NAME = get_from_env('HEALTH_CHECK_ORGANISATION_NAME', "Health check Application Home")

VODAFONE_CERT_CREDENTIALS = {
    'CLIENT_ID': get_from_env('VODAFONE_CERT_CLIENT_ID', 'X5NutQAWgg'),
    'CLIENT_SECRET': get_from_env('VODAFONE_CERT_CLIENT_SECRET', 'psxLAG7qeYcx0HAFJVP1'),
    'USERNAME': get_from_env('VODAFONE_CERT_USERNAME', '<EMAIL>'),
    'PASSWORD': get_from_env('VODAFONE_CERT_PASSWORD', '1234567')
}
VODAFONE_CSC_CREDENTIALS = {
    'CLIENT_ID': get_from_env('VODAFONE_CSC_CLIENT_ID', 'X5NutQAWgg'),
    'CLIENT_SECRET': get_from_env('VODAFONE_CSC_CLIENT_SECRET', 'psxLAG7qeYcx0HAFJVP1'),
    'USERNAME': get_from_env('VODAFONE_CSC_USERNAME', '<EMAIL>'),
    'PASSWORD': get_from_env('VODAFONE_CSC_PASSWORD', '1234567')
}

ALLOWED_HOSTS = env.list('ALLOWED_HOSTS', default=[
    'demo.cybersmart.co.uk',
    'cs-demo-web-app-alb-385587436.eu-west-2.elb.amazonaws.com',
    '127.0.0.1',
    '*'
])
EC2_PRIVATE_IP = None
try:
    EC2_PRIVATE_IP = requests.get('http://***************/latest/meta-data/local-ipv4', timeout=0.01).text
except requests.exceptions.RequestException:
    pass

if EC2_PRIVATE_IP:
    ALLOWED_HOSTS.append(EC2_PRIVATE_IP)

CORS_ALLOW_CREDENTIALS = bool_from_env('CORS_ALLOW_CREDENTIALS', True)
# LOGGING
LOG_ROOT = get_from_env('LOGGING_ROOT', os.path.join(BASE_DIR, 'logs/'))

REDIS_CACHE_ENABLE = bool_from_env('REDIS_CACHE_ENABLE', True)

WEBSOCKET_REDIS_CONNECTION = {
    'url': REDIS_URL + '/1',  # Same database as cache
    'alias': 'default'
}
if IS_TEST:
    # django_ratelimit does not support LocMemCache, however in the most common tests
    # we do not care about rate-limiting, so ignore the exception and warning.
    SILENCED_SYSTEM_CHECKS += ['django_ratelimit.E003', 'django_ratelimit.W001']
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        },
        'dummy_cache': {
            'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
        }
    }
    WAFFLE_CACHE_NAME = 'dummy_cache'

    CELERY_BROKER_URL = 'memory://'

    # WebSocket Redis connection - None in test environment
    WEBSOCKET_REDIS_CONNECTION = None
elif REDIS_CACHE_ENABLE:
    CACHES = {
        'default': {
            'BACKEND': 'django_redis.cache.RedisCache',
            'LOCATION': REDIS_URL + '/1',
            'OPTIONS': {
                'CLIENT_CLASS': 'django_redis.client.DefaultClient'
            },
            'KEY_PREFIX': 'django'
        },
        'cachalot': {
            'BACKEND': 'django_redis.cache.RedisCache',
            'LOCATION': REDIS_URL + '/2',
            'OPTIONS': {
                'CLIENT_CLASS': 'django_redis.client.DefaultClient'
            },
            'KEY_PREFIX': 'cachalot'
        }
    }
    CACHALOT_CACHE = 'cachalot'
else:
    CACHES = {
        'default': {
            'BACKEND': get_from_env('CACHE_BACKEND', 'django.core.cache.backends.dummy.DummyCache'),
            'LOCATION': get_from_env(
                'CACHE_LOCATION', os.path.join(ROOT_LOCATION, get_from_env('CACHE_FOLDER_NAME', 'caches'))
            ),
        }
    }

# how many minutes should the result from API web notifications last_seen request be cached for
MINUTES_TO_CACHE_LAST_SEEN_REQUEST = get_from_env('MINUTES_TO_CACHE_LAST_SEEN_REQUEST', 15)

SECRET_KEY = get_from_env('SECRET_KEY', 'xxxxxx')

DATABASES = {
    'default': {
        'ENGINE': get_from_env('DB_ENGINE', 'django.db.backends.postgresql'),
        'NAME': get_from_env('DB_NAME', 'cybersmart'),
        'USER': get_from_env('DB_USER', 'cybersmart'),
        'PASSWORD': get_from_env('DB_PASSWORD', 'password'),
        'HOST': get_from_env('DB_HOST', 'postgres'),
        'PORT': get_from_env('DB_PORT', '5432'),
        'CONN_MAX_AGE': get_from_env('DB_CONN_MAX_AGE', 600),
        'CONN_HEALTH_CHECKS': get_from_env('DB_CONN_HEALTH_CHECKS', True),
    },
    'archive': {
        'ENGINE': get_from_env('ARCHIVE_DB_ENGINE', 'django.db.backends.postgresql'),
        'NAME': get_from_env('ARCHIVE_DB_NAME', 'cybersmart_archive'),
        'USER': get_from_env('ARCHIVE_DB_USER', 'cybersmart'),
        'PASSWORD': get_from_env('ARCHIVE_DB_PASSWORD', 'password'),
        'HOST': get_from_env('ARCHIVE_DB_HOST', 'postgres'),
        'PORT': get_from_env('ARCHIVE_DB_PORT', '5432'),
        'CONN_MAX_AGE': get_from_env('ARCHIVE_DB_CONN_MAX_AGE', 600),
        'CONN_HEALTH_CHECKS': get_from_env('DB_CONN_HEALTH_CHECKS', True),
    }
}

if IS_TEST:
    # in testing, use the default database for the history models
    DATABASE_ROUTERS = [
        "cyber_smart.settings.database_routing.SilkRouter",
        'cyber_smart.settings.database_routing.ArchiveRouter',
        'cyber_smart.settings.database_routing.DefaultRouter'
    ]
else:
    # in all other casess, use archive database for archive and history apps
    DATABASE_ROUTERS = [
        "cyber_smart.settings.database_routing.SilkRouter",
        'cyber_smart.settings.database_routing.ArchiveRouter',
        'cyber_smart.settings.database_routing.HistoryRouter',
        'cyber_smart.settings.database_routing.DefaultRouter'
    ]

# Sentry configuration
SENTRY_DSN = get_from_env(
    'SENTRY_DSN',
    ''
    # Only replace with below value if you are testing Sentry configuration.
    # Otherwise all local errors end up on sentry, local test sentry project is cybersmart-web-test
    # 'https://3c9ae053d4fe47f286dc10365b73828a:<EMAIL>/1216267'
)


def sentry_traces_sampler(sampling_context):
    # Examine provided context data (including parent decision, if any)
    # along with anything in the global namespace to compute the sample rate
    # or sampling decision for this transaction

    # Capture 0.1% of performance traces by default
    traces_sample_rate = float_from_env('TRACES_SAMPLE_RATE', 0.001)

    wsgi_env = sampling_context.get("wsgi_environ")
    if wsgi_env and wsgi_env.get("PATH_INFO") in ["/health-check", "/api/health-check"]:
        # Drop samples for health checks
        return 0
    else:
        # Default sample rate
        return traces_sample_rate

if not DEBUG:
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        integrations=[DjangoIntegration(), RedisIntegration(), CeleryIntegration(monitor_beat_tasks=(IS_PROD and IS_UK_GEO))],
        # Set error sample_rate to 1.0 to capture 100%
        sample_rate=1.0,

        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production,
        traces_sampler=sentry_traces_sampler,

        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,

        # even when just logging, attach stack trace for better debugging
        attach_stacktrace=True,

        # try to reduce "missed check-in" and "timeout check-in" errors
        keep_alive=True,
    )

PERVADE_API_URL = get_from_env('PERVADE_API_URL', 'https://ref-portal.iasme.co.uk/OPAUDIT/API/index.php')
PERVADE_API_TOKEN = get_from_env('PERVADE_API_TOKEN',
                                 'ed826880505bdfe98178842eed5bf74d9df772f53b38522fec6994cdfc5bf74b')
PERVADE_ORGANISATION_NAME = get_from_env('PERVADE_ORGANISATION_NAME', 'Tresor Security Ltd')
PERVADE_API_TOKEN_IASME = get_from_env('PERVADE_API_TOKEN',
                                       'ed826880505bdfe98178842eed5bf74d9df772f53b38522fec6994cdfc5bf74b')
PERVADE_ORGANISATION_NAME_IASME = get_from_env('PERVADE_ORGANISATION_NAME', 'Tresor Security Ltd')

# CORS ORIGIN
CORS_ALLOWED_ORIGINS = env.list(
    'CORS_ORIGIN_WHITELIST', default=[
        'https://static-thats.cybersmart.co.uk',
        'https://media-thats.cybersmart.co.uk'
    ])

# CSRF
if not CI_TEST and not IS_DEV:
    CSRF_COOKIE_HTTPONLY = bool_from_env('CSRF_COOKIE_HTTPONLY', True)
    CSRF_COOKIE_SECURE = bool_from_env('CSRF_COOKIE_SECURE', True)
    CSRF_COOKIE_AGE = get_from_env('CSRF_COOKIE_AGE', 31449600)  # default
    CSRF_USE_SESSIONS = bool_from_env('CSRF_USE_SESSIONS',
                                      True)  # Django uses double CSRF token, but this passes infosec audit
    SESSION_COOKIE_SECURE = bool_from_env('SESSION_COOKIE_SECURE',
                                          True)  # disabled for local, always set True for live
    SESSION_COOKIE_AGE = int_from_env('SESSION_COOKIE_AGE', 86400)  # default is 24 hours to session expiry
    SESSION_SAVE_EVERY_REQUEST = bool_from_env('SESSION_SAVE_EVERY_REQUEST', True)
    SESSION_EXPIRE_AT_BROWSER_CLOSE = bool_from_env('SESSION_EXPIRE_AT_BROWSER_CLOSE', True)
    CSRF_TRUSTED_ORIGINS = env.list('CSRF_TRUSTED_ORIGINS', default=[
        'https://*.cybersmart.co.uk',
        'https://*.cybersmart.com'
    ])
    SESSION_ENGINE = 'django.contrib.sessions.backends.cached_db'

if IS_LOCAL_AUTO_TEST:
    SESSION_COOKIE_SECURE = bool_from_env('SESSION_COOKIE_SECURE', False)
    SESSION_SAVE_EVERY_REQUEST = bool_from_env('SESSION_SAVE_EVERY_REQUEST', True)
    CSRF_USE_SESSIONS = bool_from_env('CSRF_USE_SESSIONS', True)

# Custom CSRF failure view for better UX on login page
CSRF_FAILURE_VIEW = 'common.views.csrf_failure_view'

# EMAIL SETTINGS
SENDGRID_SANDBOX_MODE_IN_DEBUG = False
SENDGRID_API_KEY = get_from_env('SENDGRID_API_KEY',
                                "*********************************************************************")

DEFAULT_FROM_EMAIL = get_from_env('DEFAULT_FROM_EMAIL', 'CyberSmart Stage <<EMAIL>>')
SERVER_EMAIL = get_from_env('SERVER_EMAIL', 'CyberSmart Stage Server <<EMAIL>>')
# testing email address for when we want to test emails are being sent and not target actual users
QA_EMAIL_ADDRESS = get_from_env('QA_EMAIL_ADDRESS', '<EMAIL>')

# Django Emails specific settings
EMAIL_SUBJECT_PREFIX = get_from_env('EMAIL_SUBJECT_PREFIX', '')
EMAIL_BACKEND = get_from_env('EMAIL_BACKEND', 'django.core.mail.backends.console.EmailBackend')

STRIPE_KEY = get_from_env('STRIPE_KEY', 'pk_test_FcmXmG6YUgyziggXA7mbpnHe')
STRIPE_SECRET = get_from_env('STRIPE_SECRET', 'sk_test_vuJTxV3m4fh1GiImXGYiGyML')

# SOCIAL
FACEBOOK_TOKEN = get_from_env('FACEBOOK_TOKEN', '')
ACCOUNT_DEFAULT_HTTP_PROTOCOL = get_from_env('ACCOUNT_DEFAULT_HTTP_PROTOCOL', 'https')

# facebook pixel
FACEBOOK_PIXEL_ID = get_from_env('FACEBOOK_PIXEL_ID', '****************')
FACEBOOK_TEST_SERVER_CODE = "TEST86258"

LINKEDIN_KEY_PAGE_VIEW_URL = get_from_env(
    'LINKEDIN_KEY_PAGE_VIEW_URL', 'https://px.ads.linkedin.com/collect/?pid=48073&conversionId=2854425&fmt=gif')
LINKEDIN_LEAD_URL = get_from_env(
    'LINKEDIN_LEAD_URL', 'https://px.ads.linkedin.com/collect/?pid=48073&conversionId=2854433&fmt=gif')
LINKEDIN_SIGNUP_URL = get_from_env(
    'LINKEDIN_SIGNUP_URL', 'https://px.ads.linkedin.com/collect/?pid=48073&conversionId=2854441&fmt=gif')
LINKEDIN_ADD_TO_CART_URL = get_from_env(
    'LINKEDIN_ADD_TO_CART_URL', 'https://px.ads.linkedin.com/collect/?pid=48073&conversionId=2854449&fmt=gif')
LINKEDIN_PURCHASE_URL = get_from_env(
    'LINKEDIN_PURCHASE_URL', 'https://px.ads.linkedin.com/collect/?pid=48073&conversionId=2854457&fmt=gif')
LINKEDIN_OTHER_URL = get_from_env(
    'LINKEDIN_OTHER_URL', 'https://px.ads.linkedin.com/collect/?pid=48073&conversionId=2854465&fmt=gif')

BING_AD_URL = get_from_env('BING_AD_URL', 'https://bat.bing.com/action/0?Ver=2&vids=0&en=Y&evt=custom&msclkid=N&ea=')

LMS_JWT_LOGIN_CLIENT_ID = get_from_env('LMS_JWT_LOGIN_CLIENT_ID', 'JWT_LOGIN_CLIENT_ID')
LMS_DATA_CACHE_TIME = int_from_env('LMS_DATA_CACHE_TIME', 30)

# SCORM cloud integration
SCORM_APP_ID = get_from_env('SCORM_APP_ID', 'SCORM_APP_ID')
SCORM_SECRET_KEY = get_from_env('SCORM_SECRET_KEY', 'SCORM_SECRET_KEY')
SCORM_CLOUD_USERNAME = get_from_env('SCORM_CLOUD_USERNAME', 'scormcloud')
SCORM_CLOUD_PASSWORD = get_from_env('SCORM_CLOUD_PASSWORD', 'SCORM_CLOUD_PASSWORD')

# django-import-export
IMPORT_EXPORT_IMPORT_PERMISSION_CODE = get_from_env("IMPORT_EXPORT_IMPORT_PERMISSION_CODE", 'add')
IMPORT_EXPORT_EXPORT_PERMISSION_CODE = get_from_env("IMPORT_EXPORT_EXPORT_PERMISSION_CODE", 'view')
IMPORT_EXPORT_USE_TRANSACTIONS = get_from_env("IMPORT_EXPORT_USE_TRANSACTIONS", True)
IMPORT_EXPORT_SKIP_ADMIN_LOG = get_from_env("IMPORT_EXPORT_SKIP_ADMIN_LOG", False)

WAFFLE_CREATE_MISSING_SWITCHES = bool_from_env("WAFFLE_CREATE_MISSING_SWITCHES", True)

# SUPERSCRIPT API
SUPERSCRIPT_STAGING_API_HOST = get_from_env(
    'SUPERSCRIPT_STAGING_API_HOST', 'https://new-partnership-service-stage.herokuapp.com')
SUPERSCRIPT_PRODUCTION_API_HOST = get_from_env(
    'SUPERSCRIPT_PRODUCTION_API_HOST', 'https://partners.gosuperscript.com')

# AUTH
SUPERSCRIPT_STAGING_API_USERNAME = get_from_env('SUPERSCRIPT_STAGING_API_USERNAME', 'cybersmart')
SUPERSCRIPT_STAGING_API_PASSWORD = get_from_env('SUPERSCRIPT_STAGING_API_PASSWORD', 'FEEH.tull3lemp*cleb')
SUPERSCRIPT_PRODUCTION_API_USERNAME = get_from_env('SUPERSCRIPT_PRODUCTION_API_USERNAME', 'cybersmart')
SUPERSCRIPT_PRODUCTION_API_PASSWORD = get_from_env('SUPERSCRIPT_PRODUCTION_API_PASSWORD', '')
SUPERSCRIPT_STAGING_API_KEY = 'Basic ' + str(b64encode(
    f'{SUPERSCRIPT_STAGING_API_USERNAME}:{SUPERSCRIPT_STAGING_API_PASSWORD}'.encode('utf-8')).decode('utf-8'))
SUPERSCRIPT_PRODUCTION_API_KEY = 'Basic ' + str(b64encode(
    f'{SUPERSCRIPT_PRODUCTION_API_USERNAME}:{SUPERSCRIPT_PRODUCTION_API_PASSWORD}'.encode('utf-8')).decode('utf-8'))

# Django Admin Shell
ADMIN_SHELL_ONLY_DEBUG_MODE = bool_from_env("ADMIN_SHELL_ONLY_DEBUG_MODE", True)
ADMIN_SHELL_ENABLE = bool_from_env("ADMIN_SHELL_ENABLE", True)
ADMIN_SHELL_ONLY_FOR_SUPERUSER = bool_from_env("ADMIN_SHELL_ONLY_FOR_SUPERUSER", True)

KNOWLEDGE_BASE_URL = get_from_env('KNOWLEDGE_BASE_URL', 'https://help.cybersmart.co.uk/portal/en-gb/home')

SIMPLE_HISTORY_REVERT_DISABLED = True

ACCOUNT_LOGIN_ON_PASSWORD_RESET = bool_from_env('ACCOUNT_LOGIN_ON_PASSWORD_RESET', True)


class DisableMigrations(object):

    def __contains__(self, item):
        return True

    def __getitem__(self, item):
        return None


DISABLE_MIGRATIONS = bool_from_env('DISABLE_MIGRATIONS', False)

if DISABLE_MIGRATIONS:
    MIGRATION_MODULES = DisableMigrations()  # disable migrations for when running tests locally

DEFAULT_AUTO_FIELD = "django.db.models.AutoField"

FORM_RENDERER = "django.forms.renderers.TemplatesSetting"

# Vulnerabilities
NVD_API_KEY = get_from_env('NVD_API_KEY', '0be9e2f2-b82d-4dbb-a399-7096972649be')
CVE_HOOK_URL = get_from_env('CVE_HOOK_URL', 'http://cve-develop.cybersmart.co.uk/hooks/')

# Salesforce CE+ request audit
SALESFORCE_CE_PLUS_AUDIT_RECORD_TYPE_ID = get_from_env(
    "SALESFORCE_CE_PLUS_AUDIT_RECORD_TYPE_ID", "0128e000000bpSZAAY"
)

SVG_DIRS = [
    os.path.join(BASE_DIR, 'static')
]

DIRECT_CUSTOMER_SIGNUP_BOOK_A_DEMO_URL = get_from_env(
    "DIRECT_CUSTOMER_SIGNUP_BOOK_A_DEMO_URL",
    "https://calendly.com/sales_cs/book-a-discovery-call-v2"
)

JOTFORM_URL = get_from_env(
    "JOTFORM_URL",
    "https://form.jotform.com/233122694395056"
)

# Trustd API configuration (to create Customers and Devices)
TRUSTD_API_CONFIG = {
    "API_KEY": get_from_env('TRUSTD_API_KEY', "test_123"),
    "API_URL": get_from_env('TRUSTD_API_URL', "https://integrations.control.traced.app"),
    "API_VERSION": get_from_env('TRUSTD_API_VERSION', "v1"),
}
# Trustd Mobile App IDs
TRUSTD_IOS_APP_ID = get_from_env('TRUSTD_IOS_APP_ID', "1519403888")
TRUSTD_ANDROID_APP_ID = get_from_env('TRUSTD_ANDROID_APP_ID', "app.traced")
# CyberSmart New & Improved mobile (powered by Trustd) App IDs
CAP_BY_TRUSTD_IOS_APP_ID = get_from_env('CAP_BY_TRUSTD_IOS_APP_ID', "")
CAP_BY_TRUSTD_ANDROID_APP_ID = get_from_env('CAP_BY_TRUSTD_ANDROID_APP_ID', "com.cybersmart.activeprotect")

TRUSTD_INSTALLATION_EMAIL_ID = get_from_env("TRUSTD_INSTALLATION_EMAIL_ID", "d-6a388f01da3a4f94b4c42c7a7ec70fcb")
TRUSTD_ENROLMENT_URL = get_from_env("TRUSTD_ENROLMENT_URL", "https://enrol.control.traced.app/cybersmart.html?")

PLAY_STORE_APP_DETAILS_URL = get_from_env('PLAY_STORE_APP_DETAILS_URL', "https://play.google.com/store/apps/details?id=")
APPLE_STORE_APP_DETAILS_URL = get_from_env('APPLE_STORE_APP_DETAILS_URL', "https://apps.apple.com/us/app/id")

IS_AUS_GEO = GEO == 'AUS'
if IS_AUS_GEO:
    # AUS environment does not have the IS_STAGE boolean property set,
    # so we use the ENVIRONMENT_NAME for now.
    IS_STAGE = ENVIRONMENT_NAME == 'au-staging'

# Note: please use is_eu_geo() instead of this in the code
IS_EU_GEO = GEO == 'EU'

# PostHog API Key
POSTHOG_API_KEY = get_from_env('POSTHOG_API_KEY', '')


# Sutcliffe
SUTCLIFFE_REPORT_EMAIL_TEMPLATE_ID = get_from_env(
    "SUTCLIFFE_100K_REPORT_EMAIL_TEMPLATE_ID", "d-9e82dede64944bddbb9797633a920f5a"
)
SUTCLIFFE_REPORT_EMAIL_RECIPIENT = get_from_env(
    "SUTCLIFFE_100K_REPORT_EMAIL_RECIPIENT", QA_EMAIL_ADDRESS
)
UX_TEAM_MAIL_RECIPIENT = get_from_env(
    "UX_TEAM_MAIL_RECIPIENT", QA_EMAIL_ADDRESS
)

SELECT2_CACHE_BACKEND = "default"

ADMIN_SHELL_CALLBACK = 'common.utils.save_django_admin_shell_command_execution'


OFFLINE_RULEBOOK_FILES = os.path.join(SRC_DIR, 'cyber_smart', 'apps', 'rulebook', 'offline_mode', 'files')
MARKETING_WEBSITE_URL = get_from_env('MARKETING_WEBSITE_URL', 'https://cybersmart.co.uk')

OPSWAT_TOKEN = get_from_env('OPSWAT_TOKEN', '')
# Note: works only in combination with the OPSWAT_TOKEN
OPSWAT_URL_BASE = get_from_env('OPSWAT_URL_BASE', "https://vcr.opswat.com/gw/file/download/analog.zip?type=1&token=")
