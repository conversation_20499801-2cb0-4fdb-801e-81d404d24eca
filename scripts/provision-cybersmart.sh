#!/bin/bash
# Down any running containers
COUNT_CONTAINERS=$(docker ps -q | wc -l)
if command -v docker compose &> /dev/null
then
    DOCKER_COMPOSE_CMD="docker compose"
else
    DOCKER_COMPOSE_CMD="docker-compose"
fi

if [ "$COUNT_CONTAINERS" -gt "0" ]; then
  echo "Downing containers..."
  $DOCKER_COMPOSE_CMD --compatibility down &&
    echo "Containers down"
fi

# Enable alias shortcut commands
source ./scripts/aliases/.zsh

# Build container images
echo "Building container images..."
DOCKER_BUILDKIT=1 $DOCKER_COMPOSE_CMD --compatibility build --parallel &&
echo "Container images built"

# Create archive database if it doesn't exist
echo "Starting postgres container"
$DOCKER_COMPOSE_CMD --compatibility up -d postgres
echo "CREATE archive database"
$DOCKER_COMPOSE_CMD exec postgres psql -U cybersmart -d postgres -c "CREATE DATABASE cybersmart_archive"
echo "Stopping postgres container"
$DOCKER_COMPOSE_CMD --compatibility stop postgres

echo "Provisioning completed"
echo "Running cybersmart..."
./scripts/run-cybersmart.sh
