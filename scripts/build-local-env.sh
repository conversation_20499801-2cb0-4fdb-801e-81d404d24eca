#!/bin/bash

# The solution has been designed to take a snapshot via AWS SSM and then download it to the local machine, as our Staging DB is not publicly accessible.
# The dump_stage_db.sh used in this script is from https://github.com/cyber-smart/cs-staging-db-snapshot/blob/main/dump_stage_db.sh and is present on the EC instance(cs-staging-bastion) in AWS Staging environment.

DUMP_DB=$1
PASSWORD=$2
AWS_PROFILE=$3
FILE_NAME="./cyberdb.dump.bz2"
INSTANCE_ID="i-0e9774e0e520f5095"
SUCCESS="Success"
USER=$(whoami)


if command -v docker compose &> /dev/null
then
    DOCKER_COMPOSE_CMD="docker compose"
else
    DOCKER_COMPOSE_CMD="docker-compose"
fi

RUN_TRANSLATIONS=0
for arg in "$@"; do
  if [[ "$arg" == "--run-translations" ]]; then
    RUN_TRANSLATIONS=1
    break
  fi
done
export RUN_TRANSLATIONS

IS_DUMP_DB=0
if [[ "$DUMP_DB" = "dumpdb" || "$DUMP_DB" = "DUMPDB" ]]; then
  IS_DUMP_DB=1
fi

if [[ $IS_DUMP_DB -ne 1 && -z "$PASSWORD" && -z "$AWS_PROFILE" ]]; then
    if [[ -f "$FILE_NAME" ]]
    then
        echo "Continuing with old db_dump, no input is detected."
    else 
        echo "ERROR: No old db_dump is detected, Please continue with new db dump --> < Usage: ./build-local-env.sh dumpdb db_password aws_profile >"
        exit 1
    fi 
elif [[ $IS_DUMP_DB -eq 1 &&  -n "$PASSWORD" && -n "$AWS_PROFILE" ]]; then
    echo "Donwloading latest staging db dump."
    echo "Using AWS Profile $AWS_PROFILE to download the db Dump."
    #Check if you can connect to AWS
    CHECK_CONNECTIVITY=$(aws s3 ls s3://cs-staging-db-dump-backup --profile $AWS_PROFILE | wc -l)

    if [[ $CHECK_CONNECTIVITY -gt 0 ]]; then
        echo "Successfully connected to AWS."
        EXECUTION_ID=$(aws ssm send-command --document-name "AWS-RunShellScript" --document-version "1" --targets '[{"Key":"InstanceIds","Values":["'$INSTANCE_ID'"]}]' --parameters '{"workingDirectory":["/opt/cs-staging-db-snapshot"],"commands":["./dump_stage_db.sh '$PASSWORD' '$USER'"]}' --comment "Talking to RDS instance via Bastion box." --timeout-seconds 1500 --max-concurrency "15" --max-errors "0" --output-s3-bucket-name "cs-aws-system-manager-output" --region eu-west-2 --profile $AWS_PROFILE --output json --query 'Command.CommandId')
        ID=$( echo "$EXECUTION_ID" | sed -e 's/^"//' -e 's/"$//' )

        sleep 5
        while true
        do
            STATUS=$(aws ssm get-command-invocation --command-id "$ID" --instance-id "$INSTANCE_ID" --profile $AWS_PROFILE --query 'Status')
            VALUE=$( echo "$STATUS" | sed -e 's/^"//' -e 's/"$//' )
            if [[ "$VALUE" == "$SUCCESS" ]]; then
                echo "Successfully completed DB Backup."
                break; 
            fi
            sleep 5
            echo "The Current Status of DB_DUMP is --> $STATUS."
        done
        aws s3 cp s3://cs-staging-db-dump-backup/${USER}/${USER}.cyberdb.dump.bz2 ./cyberdb.dump.bz2 --profile $AWS_PROFILE
        echo "Successfully downloaded the db dump in ./scripts."
    else 
        echo "ERROR: Something went wrong authenticating you to AWS. Use the following command to authenticate to AWS. -> Usage: < aws-google-auth -p cs-aws-staging-s3 > "
        exit 1
    fi
else
    echo "Continuing with old db_dump, correct input is not detected."
fi

if [[ $IS_DUMP_DB -ne 1 && -z "$PASSWORD" && -z "$AWS_PROFILE" ]]; then
    echo "Continuing with old db volume"
else 
    echo "Removing old containers and volume to start the application on a clean slate "
    $DOCKER_COMPOSE_CMD --compatibility down -v
fi 

COUNT_CONTAINERS=$(docker ps -q | wc -l)

if [ "$COUNT_CONTAINERS" -gt "0" ]; then
  echo "Downing containers..."
  $DOCKER_COMPOSE_CMD --compatibility down
  echo "Containers down"
fi
echo "Running Docker build"
DOCKER_BUILDKIT=1 $DOCKER_COMPOSE_CMD --compatibility build --parallel &&

# Create archive database if it doesn't exist
echo "Starting postgres container"
$DOCKER_COMPOSE_CMD --compatibility up -d postgres

echo "Checking postgres readiness"
# function to check postgres readiness
is_postgres_ready(){
    docker exec cybersmart-platform-core_postgres_1 pg_isready -U $1 -d $2
}

# wait for postgres to be ready
while ! is_postgres_ready cybersmart postgres
do
    echo "$(date) - waiting for database to start"
    sleep 2
done

# create the database
echo "$(date) - creating database"
docker exec -it cybersmart-platform-core_postgres_1 psql -U cybersmart -d postgres -c "CREATE DATABASE cybersmart_archive"

$DOCKER_COMPOSE_CMD --compatibility stop postgres

if [[ $IS_DUMP_DB -ne 1 && -z "$PASSWORD" && -z "$AWS_PROFILE" ]]; then
    echo "Restoring DB is not required, Continuing with old db volume."
else 
    echo "********************-Restoring local DB-***********************"
    ./scripts/restore_local_db.sh
fi

echo "********************Starting_The_Cybersmart_Application***********************"
./scripts/run-cybersmart.sh
