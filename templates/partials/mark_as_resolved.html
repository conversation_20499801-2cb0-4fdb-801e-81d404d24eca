{% load static %}
{% load i18n %}
{% load l10n %}
{% load divide_tags %}

<button class="tw-min-h-8 tw-flex tw-w-full lg:tw-w-fit tw-text-nowrap tw-justify-center tw-items-center tw-gap-1 tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0" data-toggle="modal" data-target="#mark-resolved-modal-{{ app_check.id }}" type="button">
    <svg class="tw-w-4 tw-h-4">
        <use href="{% static 'icons/icons-sprite.svg' %}#icon-check"></use>
    </svg>
    {% trans 'Mark as resolved' %}
</button>

<div class="modal fade" id="mark-resolved-modal-{{ app_check.id }}" tabindex="-1" role="dialog" >
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            {% include 'partials/manually_resolve_reasons.html' with app_check=app_check %}
            <div class="modal-footer">
                <button type="button" class="btn btn-default waves-effect text-left" data-dismiss="modal">{% trans "Cancel" %}</button>
                <button onclick="manuallyResolveCheck(this)" data-app-check-id="{{ app_check.id }}" data-url="{% call_method app_install 'url_resolve' org_id=organisation.secure_id user_uuid=app_install.app_user.uuid device_id=app_install.device_id serial_number=app_install.url_encoded_serial_number %}" class="btn btn-primary align-left has-spinner" id="resolve-issue-{{ app_check.id }}" type="button" >
                    {% trans 'Resolve Issue' %}
                    <span class="spinner"><div class="cssload-speeding-wheel" style="width:15px;height:15px;"></div></span>
                </button>
            </div>
        </div>
    </div>
</div>

{% block extra-js %}
    <script type="application/javascript">
        function manuallyResolveCheck(element) {
            // show spinner and disable button
            $('.has-spinner').attr("disabled", "disabled");
            element.classList.add('active');
            const app_check_id = element.dataset.appCheckId;
            const url = element.dataset.url;
            const reason = document.getElementById('mark-resolved-reason-' + app_check_id);
            $.ajax({
                url: url,
                method: "POST",
                data: {
                    'csrfmiddlewaretoken' : "{{ csrf_token }}",
                    'check_id': app_check_id,
                    'reason': reason.value,
                    'manual_resolved': true,
                }
            }).done(function(response) {
                window.location.reload();
            });
        }
    </script>
{% endblock %}