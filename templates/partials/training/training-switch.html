{% load i18n %}
<label class="trainings-switch">
    <input type="checkbox"
        {% if field.value %}checked{% endif %}
        name="{{ field.name }}" id="{{field.name}}"
        class="training-input-switcher">
    <span class="trainings-switch-slider">{% if field.value %} {% trans "Yes" %} {% else %} {% trans "No" %} {% endif %}</span>
</label>

{% block extra-js %}
<script>
    $('.training-input-switcher').on('change', function (event) {
        var sliderText = event.target.checked ? gettext('Yes') : gettext('No');
        $('.trainings-switch-slider').html(sliderText);
    });
</script>
{% endblock %}
