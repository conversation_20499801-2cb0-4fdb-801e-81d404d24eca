{% load i18n %}
{% load waffle_tags %}

<div class="modal app-deployment" id="trustd-deployment-modal" tabindex="-1" role="dialog"
    aria-labelledby="version-channel-modal" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    {% if organisation.is_trustd_mobile_available %}
                        {% trans "Active Protect Mobile Bulk Deployment" %}
                    {% else %}
                        {% trans "Active Protect Mobile Bulk Deployment (Beta)" %}
                    {% endif %}
                </h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>
                    <strong>
                        {% blocktrans trimmed %}
                        To distribute the CyberSmart Active Protect mobile app, send the following instructions to your users via your preferred
                        method of communication.
                        {% endblocktrans %}
                    </strong>
                </p>
                <p class="warning">
                    {% blocktrans trimmed %}
                    Please ensure that you do not change the link or token. Any changes can cause installation or dashboard issues.
                    {% endblocktrans %}
                </p>
                <div class="instructions-label">
                    <h6><strong>{% trans 'Instructions to send' %}</strong></h6>
                    <button class="copy">
                        <i class="fa fa-clone fa-fw"></i>
                        <span>{% trans 'Copy' %}</span>
                    </button>
                </div>
                <div class="instructions">
                    <p>
                        {% blocktrans trimmed %}
                        The CyberSmart Active Protect mobile app powered by Trustd
                        is designed to safeguard your device and company data from mobile
                        security threats. It is a privacy-first app that protects you from
                        harm without compromising your personal data.
                        {% endblocktrans %}
                    </p>
                    <h4><strong>{% trans 'How to get started' %}</strong></h4>
                    <div>
                        <p>
                            <strong>
                                {% trans 'If you are on mobile device:' %}
                            </strong>
                        </p>
                        <ol>
                            <li>
                                {% trans 'Open this unique link' %}
                                <code>
                                    {{ organisation.absolute_trustd_registration_url }}
                                </code>
                            </li>
                            <li>
                                {% trans 'When prompted, enter your device name and the below token:' %}
                                <code {% if organisation.token %}class="token"{% endif %}>
                                    {% if organisation.token.token %}
                                        {{ organisation.token.token|upper }}
                                    {% else %}
                                        {% trans "No token present, please contact support" %}
                                    {% endif %}
                                </code>
                            </li>
                            <li>
                                {% trans 'Follow the on-screen-instructions' %}
                            </li>
                            <li>
                                {% trans 'Follow the in-app instructions' %}
                            </li>
                        </ol>
                    </div>
                    <div>
                        <p>
                            <strong>
                                {% trans 'If you are on a desktop or laptop:' %}
                            </strong>
                        </p>
                        <ol>
                            <li>
                                {% trans 'Open this unique link' %}
                                <code>
                                    {{ organisation.absolute_trustd_registration_url }}
                                </code>
                            </li>
                            <li>
                                {% blocktrans trimmed %}
                                When prompted enter your <strong>mobile</strong> device name and the below token:
                                {% endblocktrans %}
                                <code class="token">
                                    {% if organisation.token.token %}
                                        {{ organisation.token.token|upper }}
                                    {% else %}
                                        {% trans "No token present, please contact support" %}
                                    {% endif %}
                                </code>
                            </li>
                            <li>
                                {% trans 'Follow the on-screen instructions' %}
                            </li>
                            <li>
                                {% trans 'Follow the in-app instructions' %}
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>