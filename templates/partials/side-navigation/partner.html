{% load static %}
{% load i18n %}
{% load waffle_tags %}
{% load side_navigation %}

{% if is_partner or distributor_viewing_partner_page %}
    {% if is_partner and partner.partner.frozen %}
        {# Only show partner dashboard #}
        <li class="collapse-container">
            <a href="{% if distributor_viewing_partner_page %}{% url 'partners:dashboard' partner_id_kwarg %}{% else %}{% url 'partners:dashboard' %}{% endif %}">
                <div class="nav-element {% active_nav request 'active' 'partners:dashboard' %}">
                    <img alt="icon-home" class="icon" src="{% static 'icons/side-navigation/Home.svg' %}">
                        <span class="title">{% trans "Partner Dashboard" %}</span>
                </div>
            </a>
        </li>
    {% else %}
        {% open_section request 'partners,rulebook,dashboard:home,partners:create-organisation' as is_open %}
        <li class="collapse-container">
            {% if distributor_viewing_partner_page %}
                {% url 'partners:dashboard' partner_id_kwarg as href %}
            {% else %}
                {% url 'partners:dashboard' as href %}
            {% endif %}
            {% new_feature href as is_new_feature %}
            <a href="{{href}}">
                <div class="nav-element {% active_nav request 'active' 'partners:dashboard' %} {% if is_new_feature %} new {% endif %}">
                    <svg class="icon">
                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-boxes"></use>
                    </svg>
                    {% if distributor_viewing_partner_page %}
                        <span class="title">{% trans "Dashboard" %} <div class="extra">{{ partner.partner.name }}</div></span>
                    {% else %}
                        <span class="title">{% trans "Partner dashboard" %}</span>
                    {% endif %}
                </div>
            </a>
            <button type="button" aria-label="expand partner menu" class="btn btn-link btn-sm btn-icon btn-pure btn-outline btn-collapse-control {% if not is_open %}collapsed{% endif %}"
            data-toggle="collapse" href="#collapsePartner" aria-expanded="{% if is_open %}true{% else %}false{% endif %}" aria-controls="collapsePartner">
                <i class="fa {% if is_open %}fa-angle-up{% else %}fa-angle-down{% endif %}"></i>
            </button>
            <ul class="nested-nav collapse {% if is_open %}in{% endif %} tw-relative group-[.minimized:not(.minimized--wide)]:before:tw-hidden before:tw-content-[''] before:tw-absolute before:tw-left-[15px] before:tw-top-[0px] before:tw-h-[calc(100%+10px)] before:tw-w-px before:tw-bg-slate-700" id="collapsePartner" {% if is_open %}data-open-by-default{% endif %} data-parent="#cs-sidenav-ul">
                <li>
                    {% if distributor_viewing_partner_page %}
                        {% url 'partners:organisations' partner_id_kwarg as href %}
                    {% else %}
                        {% url 'partners:organisations' as href %}
                    {% endif %}
                    {% new_feature href as is_new_feature %}
                    <a href="{{href}}">
                        <div class="nav-element {% active_nav request 'active' 'partners:organisations' %} {% if is_new_feature %} new {% endif %}">
                            <svg class="icon">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-boxes"></use>
                            </svg>
                            <span class="title">{% trans "Organisations" %}</span>
                        </div>
                    </a>
                </li>
                <li>
                    {% if distributor_viewing_partner_page %}
                        {% url 'partners:devices' partner_id_kwarg as href %}
                    {% else %}
                        {% url 'partners:devices' as href %}
                    {% endif %}
                    {% new_feature href as is_new_feature %}
                    <a href="{{href}}?status=active">
                        <div class="nav-element {% active_nav request 'active' 'partners:devices' %} {% if is_new_feature %} new {% endif %}">
                            <svg class="icon">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-laptop-minimal"></use>
                            </svg>
                            <span class="title">{% trans "Devices" %}</span>
                        </div>
                    </a>
                </li>
                {% switch "show_partner_reports" %}
                <li>
                    {% if distributor_viewing_partner_page %}
                        {% url 'partners:end-of-life-report' partner_id_kwarg as href %}
                    {% else %}
                        {% url 'partners:end-of-life-report' as href %}
                    {% endif %}
                    {% new_feature href as is_new_feature %}
                    <a href="{{href}}">
                        <div class="nav-element {% active_nav request 'active' 'partners:end-of-life-report' %} {% active_nav request 'active' 'partners:software-report' %} {% if is_new_feature %} new {% endif %}">
                            <svg class="icon">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-file-text"></use>
                            </svg>
                            <span class="title">{% trans "Partner reports" %}</span>
                        </div>
                    </a>
                </li>
                {% endswitch %}
                <li>
                    {% if distributor_viewing_partner_page %}
                        {% url 'partners:certificates' partner_id_kwarg as href %}
                    {% else %}
                        {% url 'partners:certificates' as href %}
                    {% endif %}
                    {% new_feature href as is_new_feature %}
                    <a href="{{href}}">
                        <div class="nav-element {% active_nav request 'active' 'partners:certificates' %} {% if is_new_feature %} new {% endif %}">
                            <svg class="icon">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-award"></use>
                            </svg>
                            <span class="title">{{ CERTIFICATIONS_TITLE }}</span>
                        </div>
                    </a>
                </li>
                {% if distributor_viewing_partner_page %}
                    </ul>
                {% else %}
                    {% if not ps.disable_payments %}
                        {% if is_direct_partner and not ps.disable_payments %}
                            <li>
                                {% url 'partners:subscriptions-overview' as href %}
                                {% new_feature href as is_new_feature %}
                                <a href="{{href}}">
                                    <div class="nav-element {% active_nav request 'active' 'partners:subscriptions-overview' %} {% if is_new_feature %} new {% endif %}">
                                        <svg class="icon">
                                            <use href="{% static 'icons/icons-sprite.svg' %}#icon-refresh-cw"></use>
                                        </svg>
                                        <span class="title">{% trans "Subscriptions overview" %}</span>
                                    </div>
                                </a>
                            </li>
                            <li>
                                {% url 'partners:subscriptions-overview' as href %}
                                {% new_feature href as is_new_feature %}
                                <a href="javascript:void(0)" id="partner-sidebar-finance">
                                    <div class="nav-element {% if is_new_feature %} new {% endif %}">
                                        <svg class="icon">
                                            <use href="{% static 'icons/icons-sprite.svg' %}#icon-credit-card"></use>
                                        </svg>
                                        <span class="title">{% trans "Finance" %}</span>
                                    </div>
                                </a>
                            </li>
                        {% endif %}
                        {% switch "channel-subscriptions-overview" %}
                            {% if is_channel_partner %}
                                <li>
                                    {% url 'partners:channel-subscriptions-overview' as href %}
                                    {% new_feature href as is_new_feature %}
                                    <a href="{{href}}">
                                        <div class="nav-element {% active_nav request 'active' 'partners:channel-subscriptions-overview' %} {% if is_new_feature %} new {% endif %}">
                                            <svg class="icon">
                                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-refresh-cw"></use>
                                            </svg>
                                            <span class="title">{% trans "Subscriptions overview" %}</span>
                                        </div>
                                    </a>
                                </li>
                            {% endif %}
                        {% endswitch %}
                    {% endif %}
                    {% if is_partner and not has_insurer_distributor %}
                        {% if is_iasme_cb_partner or user.is_staff %}
                            <li>
                                {% url 'rulebook:certos:dashboard' as href %}
                                {% new_feature href as is_new_feature %}
                                <a href="{{href}}">
                                    <div class="nav-element {% active_nav request 'active' 'rulebook:certos:dashboard,rulebook:certos:certified,rulebook:certos:verify-ranking,rulebook:certos:live-marking-scheme,rulebook:certos:generate-report,rulebook:certos:get-sample-report,rulebook:certos:get-report,rulebook:certos:issue-certificate' %} {% if is_new_feature %} new {% endif %}">
                                        <svg class="icon">
                                            <use href="{% static 'icons/icons-sprite.svg' %}#icon-square-kanban"></use>
                                        </svg>
                                        <span class="title">{% trans "CertOS" %}</span>
                                    </div>
                                </a>
                            </li>
                        {% endif %}
                    {% endif %}
                        <li>
                            {% url 'partners:security-controls' as href %}
                            {% new_feature href as is_new_feature %}
                            <a href="{{href}}">
                                <div class="nav-element {% active_nav request 'active' 'partners:security-controls' %} {% if is_new_feature %} new {% endif %}">
                                    <svg class="icon">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-settings-2"></use>
                                    </svg>
                                    <span class="title">{% trans "Settings" %}</span>
                                </div>
                            </a>
                        </li>
                </ul>
            {% endif %}
        </li>
    {% endif %}

    {% block extra-js %}
        <script src="https://js.chargebee.com/v2/chargebee.js" data-cb-site="{{chargebee_site_name}}" data-cb-gtm-enabled="true"> </script>
        <script type="application/javascript">
            $( document ).ready(function() {
                $("#partner-sidebar-finance").click(function(){
                    $('html').css("overflow", "hidden");
                    const chargebeeInstance = Chargebee.getInstance();
                    chargebeeInstance.setPortalSession(function(){
                        return $.ajax({
                            url: '{% url 'partners:subscriptions-overview' %}',
                            method: 'GET'
                        });
                    });
                    var chargebeePortalInstance = chargebeeInstance.createChargebeePortal();
                    chargebeePortalInstance.open({
                        close: function() {
                            $('html').css("overflow-y", "scroll");
                            chargebeeInstance.logout();
                        }
                    });
                });
            });
        </script>
    {% endblock %}
{% endif %}
