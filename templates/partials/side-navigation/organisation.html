{% load static %}
{% load i18n %}
{% load waffle_tags %}
{% load side_navigation %}
{% load last_visited_page %}
{% load divide_tags %}
{% load check_permissions %}

{% if not is_distributor and not is_partner and not request.user.profile.is_admin_for_single_org %}
    <li id="side-navigation_organisation_organisations">
        <a href="{% url 'dashboard:home' %}">
            <div class="nav-element {% active_nav request 'active' 'dashboard:home' %}">
                <svg class="icon">
                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-boxes"></use>
                </svg>
                <span class="title">{% trans "Organisations" %}</span>
            </div>
        </a>
    </li>
{% endif %}

{% if organisation and "certos" not in request.resolver_match.namespaces %}
    {% open_section request 'dashboard,smart-score,organisations,smart_policies,lms,learn' as is_open %}
    <li class="collapse-container">
        {% url 'dashboard:organisation' org_id=organisation.secure_id as href %}
        {% new_feature href as is_new_feature %}
        <a {% if perms|has_access_to_view:DEVICES_PERMISSION %}href="{{href}}"{% else %}data-toggle="collapse" data-target="#collapseOrg" aria-expanded="{% if is_open %}true{% else %}false{% endif %}" aria-controls="collapseOrg"{% endif %}>
            <div class="nav-element {% active_nav request 'active' 'dashboard:organisation' %} {% if is_new_feature %} new {% endif %}">
                <svg class="icon">
                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-box"></use>
                </svg>
                <span class="title">{{ organisation.name|truncatechars:"24" }}</span>
            </div>
        </a>
        <button type="button" aria-label="expand organisation menu" class="btn btn-link btn-sm btn-icon btn-pure btn-outline btn-collapse-control {% if not is_open %}collapsed{% endif %}"
        data-toggle="collapse" data-target="#collapseOrg" aria-expanded="{% if is_open %}true{% else %}false{% endif %}" aria-controls="collapseOrg">
            <i class="fa {% if is_open %}fa-angle-up{% else %}fa-angle-down{% endif %}"></i>
        </button>
        <ul class="nested-nav collapse {% if is_open %}in{% endif %} group-[.minimized:not(.minimized--wide)]:before:tw-hidden tw-relative before:tw-content-[''] before:tw-absolute before:tw-left-[15px] before:tw-top-[0px] before:tw-h-[calc(100%-10px)] before:tw-w-px before:tw-bg-slate-700" id="collapseOrg" {% if is_open %}data-open-by-default{% endif %} data-parent="#cs-sidenav-ul">
            {% if perms|has_access_to_view:DEVICES_PERMISSION %}
                {% switch "organisation-devices-page" %}
                    {% if organisation.has_software_support %}
                        <li id="side-navigation_organisation_devices">
                            {% url 'organisations:devices' org_id=organisation.secure_id as href %}
                            {% new_feature href as is_new_feature %}
                            <a href="{{href}}?status=active">
                                <div class="nav-element {% active_nav request 'active' 'organisations:devices' %} {% if is_new_feature %} new {% endif %}">
                                    <svg class="icon">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-laptop-minimal"></use>
                                    </svg>
                                    <span class="title">{% trans "Devices" %}</span>
                                </div>
                            </a>
                        </li>
                    {% endif %}
                {% endswitch %}
                {% waffle_flag_is_active_for_user "opswat_patch" request.user as is_opswat_patch_active %}
                {% if is_opswat_patch_active %}
                    {% if organisation.has_software_support %}
                        <li id="side-navigation_organisation_installed_software">
                            {% url "organisations:installed-software" org_id=organisation.secure_id as href %}
                            {% new_feature href as is_new_feature %}
                            <a href="{{href}}">
                                <div class="nav-element {% active_nav request 'active' 'organisations:installed-software' %} {% if is_new_feature %} new {% endif %}">
                                    <svg class="icon tw-text-gray-400">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-package-sm"></use>
                                    </svg>
                                    <span class="title">{% trans "Software" %}</span>
                                </div>
                            </a>
                        </li>
                    {% endif %}
                {% endif %}
                <li id="side-navigation_organisation_reports">
                    {% url 'dashboard:check_report' org_id=organisation.secure_id as href %}
                    {% new_feature href as is_new_feature %}
                    <a href="{{href}}">
                        <div class="nav-element {% active_nav request 'active' 'dashboard:check_report,dashboard:software-report,smart_policies:policies-report,smart-score:smart-score-report' %} {% if is_new_feature %} new {% endif %}">
                            <svg class="icon">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-file-text"></use>
                            </svg>
                            <span class="title">{% trans "Reports" %}</span>
                        </div>
                    </a>
                </li>
                {% if organisation.policies_support %}
                    <li id="side-navigation_organisation_smart-policies">
                        {% url 'smart_policies:main' org_id=organisation.secure_id as href %}
                        {% new_feature href as is_new_feature %}
                        <a href="{{href}}">
                            <div class="nav-element {% active_nav request 'active' 'smart_policies:main' %} {% if is_new_feature %} new {% endif %}">
                                <svg class="icon">
                                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-file-check"></use>
                                </svg>
                                <span class="title">{% trans "Smart Policies" %}</span>
                            </div>
                        </a>
                    </li>
                {% endif %}
                {% if organisation.learn_lite_enabled %}
                    <li id="side-navigation_organisation_academy">
                        {% last_visited_training_page request organisation as href %}
                        {% new_feature href as is_new_feature %}
                        <a href="{{href}}">
                            <div class="nav-element {% active_nav request 'active' 'lms:courses,dashboard:training-welcome,dashboard:training-summary-confirm,dashboard:training-journey-complete,dashboard:training-progress-modules,dashboard:training-progress-users,dashboard:training-progress-data' %} {% if is_new_feature %} new {% endif %}">
                                <svg class="icon">
                                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-graduation-cap"></use>
                                </svg>
                                <span class="title">{% trans "Learn" %}</span>
                            </div>
                        </a>
                    </li>
                {% elif organisation.learn_enabled %}
                    <li id="side-navigation_cybersmart_learn">
                        {% url 'learn:cybersmart_learn' org_id=organisation.secure_id as href %}
                        {% new_feature href as is_new_feature %}
                        <a href="{{href}}">
                            <div class="nav-element {% active_nav request 'active' 'learn:cybersmart_learn' %} {% if is_new_feature %} new {% endif %}">
                                <svg class="icon">
                                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-graduation-cap"></use>
                                </svg>
                                <span class="title">{% trans "Learn" %}</span>
                            </div>
                        </a>
                    </li>
                {% endif %}
            {% endif %}
            {% if ps.is_certifications_enabled and not organisation.is_superscript_customer and not organisation.is_starling_customer and perms|has_access_to_view:CERTIFICATES_AND_INSURANCE_PERMISSION %}
                <li id="side-navigation_organisation_certifications">
                    {% url 'organisations:certificates' org_id=organisation.secure_id as href %}
                    {% new_feature href as is_new_feature %}
                    <a href="{{href}}">
                        <div class="nav-element {% active_nav request 'active' 'organisations:certificates' org_id=organisation.secure_id %} {% if is_new_feature %} new {% endif %}">
                            <svg class="icon">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-award"></use>
                            </svg>
                            <span class="title">{{ CERTIFICATIONS_TITLE }}</span>
                        </div>
                    </a>
                </li>
            {% endif %}
            {% if perms|has_access_to_view:CERTIFICATES_AND_INSURANCE_PERMISSION %}
                {% switch "enable_new_org_insurance_page" %}
                    {% if not organisation.partner.iasme_cb %}
                        {% if is_partner or is_distributor or has_partner %}
                            <li id="side-navigation_organisation_cyber-insurance">
                                {% url 'organisations:cyber-insurance' org_id=organisation.secure_id as href %}
                                {% new_feature href as is_new_feature %}
                                <a href="{{href}}">
                                    <div class="nav-element {% active_nav request 'active' 'organisations:cyber-insurance' org_id=organisation.secure_id %} {% if is_new_feature %} new {% endif %}">
                                        <svg class="icon">
                                            <use href="{% static 'icons/icons-sprite.svg' %}#icon-umbrella"></use>
                                        </svg>
                                        <span class="title">{% trans "Cyber insurance" %}</span>
                                    </div>
                                </a>
                            </li>
                        {% endif %}
                    {% endif %}
                {% endswitch %}
                {% if organisation.data_privacy_support %}
                    <li id="side-navigation_organisation_privacy-toolbox">
                        {% url 'dashboard:privacy-toolbox' org_id=organisation.secure_id as href %}
                        {% new_feature href as is_new_feature %}
                        <a href="{{href}}">
                            <div class="nav-element {% active_nav request 'active' 'dashboard:privacy-toolbox' org_id=organisation.secure_id %} {% if is_new_feature %} new {% endif %}">
                                <svg class="icon">
                                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-clipboard-check"></use>
                                </svg>
                                <span class="title">{% trans "Privacy Toolbox" %}</span>
                            </div>
                        </a>
                    </li>
                {% endif %}
                {% if organisation.is_r_n_r_toolbox_enabled %}
                    <li id="side-navigation_organisation_ransomware-and-recovery-toolbox">
                        {% url 'dashboard:r_n_r_toolbox' org_id=organisation.secure_id as href %}
                        {% new_feature href as is_new_feature %}
                        <a href="{{href}}">
                            <div class="nav-element {% active_nav request 'active' 'dashboard:r_n_r_toolbox' org_id=organisation.secure_id %} {% if is_new_feature %} new {% endif %}">
                                <svg class="icon">
                                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-briefcase"></use>
                                </svg>
                                <span class="title">{% trans "Ransomware & Recovery Toolbox" %}</span>
                            </div>
                        </a>
                    </li>
                {% endif %}
                {% switch 'cap_v_five_general_availability' %}
                    {% flag 'general_availabibility_legacy_app_management' %}
                        {% if organisation.has_combined_deprecated_installs %}
                        <li id="side-navigation_organisation_legacy-app-management">
                    
                        {% url 'organisations:deprecated-devices' org_id=organisation.secure_id as href %}
                        {% new_feature href as is_new_feature %}
                            <a href="{{href}}">
                                <div class="nav-element {% active_nav request 'active' 'organisations:deprecated-devices' %} {% if is_new_feature %} new {% endif %}">
                                    <svg class="icon">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-circle-alert"></use>
                                    </svg>
                                    <span class="title">{% trans "App version management" %}</span>
                                </div>
                            </a>
                        </li>
                        {% endif %}
                    {% endflag %}
                {% endswitch %}
                {% if organisation.partner.distributor.enable_vss_tab or organisation.get_partner_vss_subscription %}
                    <!-- Distributor flag to show globally for prospects, always show for subscribed customers -->
                    <li id="side-navigation_organisation_vulnerability-scanning-service">
                        {% url 'dashboard:vss' org_id=organisation.secure_id as href %}
                        {% new_feature href as is_new_feature %}
                        <a href="{{href}}">
                            <div class="nav-element {% active_nav request 'active' 'dashboard:vss' %} {% if is_new_feature %} new {% endif %}">
                                <svg class="icon">
                                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-target"></use>
                                </svg>
                                <span class="title">{% trans "Vulnerability scanning service" %}</span>
                            </div>
                        </a>
                    </li>
                {% endif %}
            {% endif %}
            {% if perms|has_access_to_manage_app_users_page:organisation %}
                {% url 'organisations:manage-users' org_id=organisation.secure_id as href %}
                {% new_feature href as is_new_feature %}
                {% translate "Manage users" as manage_users_title %}
                {% include "partials/side-navigation/manage-users-nav.html" with href=href is_new_feature=is_new_feature title=manage_users_title %}
            {% elif perms|has_access_to_dashboard_access_page:organisation %}
                {% url 'organisations:dashboard-access' org_id=organisation.secure_id as href %}
                {% new_feature href as is_new_feature %}
                {% translate "Dashboard access" as manage_users_title %}
                {% include "partials/side-navigation/manage-users-nav.html" with href=href is_new_feature=is_new_feature title=manage_users_title %}
            {% endif %}
            {% if perms|has_access_to_view:PEOPLE_AND_ORGANISATION_PERMISSION %}
                <li id="side-navigation_organisation_manage-org">
                    {% url 'dashboard:manage-organisation' org_id=organisation.secure_id as href %}
                    {% new_feature href as is_new_feature %}
                    <a href="{{href}}">
                        <div class="nav-element {% active_nav request 'active' 'dashboard:manage-organisation,dashboard:manage-organisation-billing,dashboard:approved-domains,dashboard:starling,dashboard:manage-notifications,dashboard:manage-features' %} {% if is_new_feature %} new {% endif %}">
                            <svg class="icon">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-settings-2"></use>
                            </svg>
                            <span class="title">{% trans "Manage org" %}</span>
                        </div>
                    </a>
                </li>
            {% endif %}
        </ul>
    </li>
{% endif %}
