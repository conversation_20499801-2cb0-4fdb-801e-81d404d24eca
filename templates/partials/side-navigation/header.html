{% load static %}
{% load i18n %}
{% load waffle_tags %}

{% block extra-css %}
    <link href="{% static 'css/notifications.css' %}" rel="stylesheet" type="text/css">
{% endblock extra-css %}

<!-- Top Navigation -->
<nav class="navbar navbar-default tw-z-50 tw-flex tw-content-center tw-h-[60px] tw-mb-0 tw-bg-slate-800 tw-rounded-none">
    <div class="navbar-header tw-pl-4 tw-flex tw-items-center tw-justify-end">
        {% if "signup" in request.resolver_match.app_names or show_logo %}
        <div class="top-left-part tw-mr-auto">
            <a class="logo" href="/">
                <img src="{% static 'images/0076-CyberSmartLogo-Secondary-Light.png' %}" alt="icon-{% trans "home" %}" />
            </a>
        </div>
        {% endif %}

        {% if request.user.is_staff or request.impersonator.is_staff %}
        <div class="tw-h-full tw-flex tw-gap-2 tw-items-center tw-mr-2">
            {% if not user.is_impersonate %}
                <a href="{% url 'impersonate-search' %}" class="tw-flex tw-items-center tw-w-fit tw-mx-auto tw-min-h-8 tw-px-2 tw-py-1 tw-rounded-md tw-font-sans tw-font-medium tw-text-slate-200 tw-border tw-border-transparent tw-transition-colors tw-duration-300 tw-ease-in-out hover:tw-text-white focus:tw-text-white focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-gray-300 active:tw-ring-0">
                    {% trans "Assist users" %}
                </a>
                {% switch "partner-demo" %}
                <a href="{% url 'impersonate-search' %}?q=Demo Accounts" class="tw-flex tw-items-center tw-w-fit tw-mx-auto tw-min-h-8 tw-px-2 tw-py-1 tw-rounded-md tw-font-sans tw-font-medium tw-text-slate-200 tw-border tw-border-transparent tw-transition-colors tw-duration-300 tw-ease-in-out hover:tw-text-white focus:tw-text-white focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-gray-300 active:tw-ring-0">
                    {% trans "Enter demo mode" %}
                </a>
                {% endswitch %}
            {% else %}
                <a href="{% url 'impersonate-stop' %}" class="tw-flex tw-items-center tw-w-fit tw-mx-auto tw-min-h-8 tw-px-2 tw-py-1 tw-rounded-md tw-font-sans tw-font-medium tw-text-red-400 tw-border tw-border-red-400 hover:tw-border-red-500 hover:tw-bg-red-500 tw-transition-colors tw-duration-300 tw-ease-in-out hover:tw-text-white focus:tw-text-white focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-gray-300 active:tw-ring-0">
                    <span class="tw-w-2.5 tw-h-2.5 tw-mr-2 tw-animate-pulse tw-bg-red-400 tw-rounded-full"></span>
                    {% trans "End impersonation" %}
                </a>
            {% endif %}
        </div>
        {% elif request.user.profile.is_partner or request.impersonator.profile.is_partner %}
        <div class="tw-h-full tw-flex tw-gap-2 tw-items-center tw-ml-auto">
            {% switch "partner-demo" %}
                {% if not user.is_impersonate %}
                    <a href="{% url 'impersonate-list' %}" class="tw-flex tw-items-center tw-w-fit tw-mx-auto tw-min-h-8 tw-px-2 tw-py-1 tw-rounded-md tw-font-sans tw-font-medium tw-text-slate-200 tw-border tw-border-transparent hover:tw-border-gray-500 tw-transition-colors tw-duration-300 tw-ease-in-out hover:tw-text-white focus:tw-text-white focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-gray-300 active:tw-ring-0">
                        {% trans "Enter demo mode" %}
                    </a>
                {% else %}
                    <a href="{% url 'impersonate-stop' %}" class="tw-flex tw-items-center tw-w-fit tw-mx-auto tw-min-h-8 tw-px-2 tw-py-1 tw-rounded-md tw-font-sans tw-font-medium tw-text-red-400 tw-border tw-border-red-400 hover:tw-border-red-500 hover:tw-bg-red-500 tw-transition-colors tw-duration-300 tw-ease-in-out hover:tw-text-white focus:tw-text-white focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-gray-300 active:tw-ring-0">
                        <span class="tw-w-2.5 tw-h-2.5 tw-mr-2 tw-animate-pulse tw-bg-red-400 tw-rounded-full"></span>
                        {% trans "End demo" %}
                    </a>
                {% endif %}
            {% endswitch %}
        </div>
        {% endif %}

        <div class="tw-relative xl:tw-pt-0 tw-inline-block tw-text-left tw-border tw-border-transparent hover:tw-border-slate-500 focus-within:tw-border-slate-500 tw-rounded-md tw-transition">
            <!-- Button -->
            <button data-dropdown-button="help-resources" type="button" class="tw-flex tw-w-fit tw-gap-1 tw-items-center tw-justify-center tw-min-h-8 tw-pl-2 tw-pr-1 tw-py-0.5 tw-rounded-md tw-text-slate-200 tw-font-sans focus:tw-outline-none active:tw-ring-0 tw-transition-colors tw-duration-300 tw-ease-in-out" data-toggle="dropdown" aria-expanded="false">            
                {% trans 'Help and resources' %}
                <span class="tw-ml-0.5">
                    <svg class="tw-w-[18px] tw-h-[18px] tw-fill-white">
                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-chevron-down"></use>
                    </svg>
                </span>
            </button>
            <!-- Dropdown Menu -->
            <ul data-dropdown-menu="help-resources" class="tw-hidden tw-absolute tw-mt-1.5 tw-min-w-[200px] tw-w-full tw-z-10 tw-right-0 tw-p-2 tw-bg-white tw-rounded-md tw-shadow-lg tw-ring-1 tw-ring-black tw-ring-opacity-5 tw-font-sans tw-list-none">
                <li>
                    <a href="{{ knowledge_base_url }}" target="_blank" class="tw-flex tw-px-2 tw-py-1.5 tw-rounded-md tw-justify-between tw-w-full tw-text-black hover:tw-text-black hover:tw-bg-zinc-100 focus:tw-text-black">
                        <div class="tw-flex tw-gap-2 tw-items-center">
                            <svg class="tw-w-[18px] tw-h-[18px]">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-life-buoy"></use>
                            </svg>
                            {% trans 'Knowledge base' %}
                        </div>
                    </a>
                </li>
                {% if request.user.profile.is_partner %}
                <li>
                    <a href="https://cybersmart.community" target="_blank" class="tw-flex tw-px-2 tw-py-1.5 tw-rounded-md tw-justify-between tw-w-full tw-text-black hover:tw-text-black hover:tw-bg-zinc-100 focus:tw-text-black">
                        <div class="tw-flex tw-gap-2 tw-items-center">
                            <svg class="tw-w-[18px] tw-h-[18px]">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-messages-square"></use>
                            </svg>
                            {% trans 'Community' %}
                        </div>
                    </a>
                </li>
                {% endif %}
                <li>
                    <a href="https://updates.cybersmart.co.uk/en" target="_blank" class="tw-flex tw-px-2 tw-py-1.5 tw-rounded-md tw-justify-between tw-w-full tw-text-black hover:tw-text-black hover:tw-bg-zinc-100 focus:tw-text-black">
                        <div class="tw-flex tw-gap-2 tw-items-center">
                            <svg class="tw-w-[18px] tw-h-[18px]">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-megaphone"></use>
                            </svg>
                            {% trans 'Product updates' %}
                        </div>
                    </a>
                </li>
            </ul>
        </div>
        
        {% if request.user.is_authenticated %}
            <form action="{% url 'account_logout' %}" method="post" id="logout_form" class="tw-absolute">
                {% csrf_token %}
            </form>
            <script>
                function logout() {
                    if(typeof posthog == 'object'){
                        posthog.reset();
                    }
                    document.getElementById('logout_form').submit();
                }
            </script>
            
            <ul class="nav navbar-top-links navbar-right tw-flex tw-items-center tw-justify-center tw-m-0">
                {% if request.user.profile.has_web_notifications_access and not user.is_impersonate %}
                    <li id="notifications" class="dropdown tw-grow tw-font-sans">
                        <a onclick="handleBellButtonClick('{{ request.user.profile.last_seen_web_notifications_isoformat }}', '{{ request.user.profile.id }}', {{ request.user.profile.web_notifications_enabled_categories|safe }}, '{{ csrf_token }}', {{ ps.web_notifications_config|safe }}, '{{ request.user.profile.get_user_type }}', '{{ request.user.profile.web_notifications_organisation_access }}')" class="dropdown-toggle" id="notifications_dropdown_toggle" data-toggle="dropdown" href="#">
                            <svg class="tw-relative tw-w-[18px] tw-h-[18px] tw-fill-slate-200 hover:tw-fill-white tw-transition">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-bell"></use>
                            </svg>
                        </a>
                        <ul class="dropdown-menu dropdown-notification" id="notifications_dropdown_menu">
                            <div class="notification-bell-header">
                                <div>{% trans 'Notifications' %}<span class="notification-bell-header-dot" style="display: none"></span><button type="button" class="close dropdown-notification"><span aria-hidden="true">&times;</span></button></div>
                            </div>
                            <span class="spinner" id="notification_bell_spinner_id" style="display: block"><div class="cssload-speeding-wheel" style="width:20px;height:20px;"></div></span>
                            <div id="notification_bell_list_error_id" class="alert alert-danger text-center" style="display: none">
                                {% trans 'We are unable to show notifications. Please try again later.' %}
                            </div>
                            <div id="notification_bell_list_id">
                                {# Notifications will go here after JS call #}
                            </div>
                            <div class="notification-bell-footer">
                                <div><a id="seeAllNotifications" href="{% url 'notifications:notifications-list' %}">{% trans 'See all notifications' %} <i class="fa fa-arrow-right" aria-hidden="true"></i></a></div>
                            </div>
                        </ul>
                    </li>
                {% endif %}

                <li class="tw-relative xl:tw-pt-0 tw-inline-block tw-text-left tw-pr-4">
                    <!-- Button -->
                    <button data-dropdown-button="settings" type="button" class="tw-flex tw-w-fit tw-gap-1 tw-items-center tw-justify-center tw-min-h-8 tw-pl-2 tw-pr-1 tw-py-0.5 tw-rounded-md tw-text-slate-200 tw-font-sans focus:tw-outline-none active:tw-ring-0 tw-transition-colors tw-duration-300 tw-ease-in-out" data-toggle="dropdown" aria-expanded="false">            
                        <div class="tw-flex tw-items-center tw-w-[32px] tw-h-[32px] tw-rounded-full tw-bg-[#14D19C] tw-text-green-900 tw-justify-center tw-text-sm tw-font-sans tw-text-center tw-font-medium tw-uppercase">{{ request.user.first_name|first }}{{ request.user.last_name|first }}</div>
                        <svg class="tw-w-[18px] tw-h-[18px] tw-fill-gray-200">
                            <use href="{% static 'icons/icons-sprite.svg' %}#icon-chevron-down"></use>
                        </svg>
                    </button>
                    <!-- Dropdown Menu -->
                    <ul data-dropdown-menu="settings" class="tw-hidden tw-absolute tw-mt-1 tw-min-w-[200px] tw-w-full tw-z-10 tw-right-4 tw-p-2 tw-bg-white tw-rounded-md tw-shadow-lg tw-ring-1 tw-ring-black tw-ring-opacity-5 tw-font-sans tw-list-none">
                        <li>
                            <a id="account_settings_button" href="{% url 'settings:profile' %}" class="tw-flex tw-px-2 tw-py-1.5 tw-rounded-md tw-justify-between tw-w-full tw-text-black hover:tw-text-black hover:tw-bg-zinc-100">
                                <div class="tw-flex tw-gap-2 tw-items-center">
                                    <svg class="tw-w-[18px] tw-h-[18px]">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-settings-2"></use>
                                    </svg>
                                    {% trans "Account Settings" %}
                                </div>
                            </a>
                        </li>
                        <li>
                            <a id="logout_button" href="#" onclick="logout()" class="tw-flex tw-px-2 tw-py-1.5 tw-rounded-md tw-justify-between tw-w-full tw-text-black hover:tw-text-black hover:tw-bg-zinc-100">
                                <div class="tw-flex tw-gap-2 tw-items-center">
                                    <svg class="tw-w-[18px] tw-h-[18px]">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-log-out"></use>
                                    </svg>
                                    {% trans "Logout" %}
                                </div>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        {% endif %}
    </div>
</nav>
<!-- End Top Navigation -->
{% block extra-js %}

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const buttons = document.querySelectorAll('[data-dropdown-button]');

        buttons.forEach((button) => {
            const menu = document.querySelector(`[data-dropdown-menu="${button.dataset.dropdownButton}"]`);

            const handleClick = (e) => {
                e.stopPropagation();

                // Close all other menus
                document.querySelectorAll('[data-dropdown-menu]').forEach((m) => {
                    if (m !== menu) m.classList.add('tw-hidden');
                });

                // Toggle this one
                menu.classList.toggle('tw-hidden');

                const close = (ev) => {
                    if (!button.contains(ev.target) && !menu.contains(ev.target)) {
                        menu.classList.add('tw-hidden');
                        document.removeEventListener('click', close);
                    }
                };

                if (!menu.classList.contains('tw-hidden')) {
                    document.addEventListener('click', close);
                } else {
                    document.removeEventListener('click', close);
                }
            };

            button.addEventListener('click', handleClick);
        });
    });
</script>

    {# Top navigation notifications JS #}
    {% if request.user.profile.has_web_notifications_access and not user.is_impersonate %}
        <script type="text/javascript" src="{% static 'js/notifications/notifications_common.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/notifications/notifications_bell.js' %}"></script>
        <script type="application/javascript">
            hasNewNotifications("{{ request.user.is_authenticated }}", "{{ request.user.profile.has_web_notifications_access }}", "{{ request.user.profile.last_seen_web_notifications_isoformat }}", {{ request.user.profile.web_notifications_enabled_categories|safe }}, {{ ps.web_notifications_config|default:"[]"|safe }}, "{{ request.user.profile.get_user_type }}");
        </script>
    {% endif %}
{% endblock %}
