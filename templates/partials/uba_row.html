{% load static %}
{% load divide_tags %}
{% load i18n %}
{% load waffle_tags %}

<!-- If CAP max users has been reached, then do not show details -->
{% if app_user.id in extra_app_users_id %}
    <td><button disabled class="btn btn-outline btn-warning host-name-button">{% trans 'User limit reached' %} <i class="fa fa-arrow-right pull-right"></i></button></td>#}
    <td align='center' >---</td>
    {% if policies_support %}
        <td align='center' >---</td>
    {% else %}
        <td></td>
    {% endif %}
    <td align='center' >---</td>
<!-- else, just carry on -->
{% else %}
    <td headers="first_install_text_precomputed">
        {% with install.reports.all.0 as report %}
            <div class="cell-container">
            {% if install and install.inactive == True or install and not report %}
                <div class="btn btn-outline btn-primary no-report-hover host-name-button">{{ install.hostname }} [{{ install.display_os }}{% if install.machine_model %} - {{ install.machine_model }}{% endif %}] ({% trans "app " %}v{{install.get_app_version}}) {% if install.is_beta %}<span class="beta-pill"></span>{% endif %}</div>
            {% elif install %}
                <a href="{% call_method install 'url' org_id=organisation.secure_id user_uuid=app_user.uuid device_id=install.device_id serial_number=install.url_encoded_serial_number %}" class="btn btn-outline btn-primary host-name-button">{{ install.hostname }} [{{ install.display_os }}{% if install.machine_model %} - {{ install.machine_model }}{% endif %}] ({% trans "app " %}v{{install.get_app_version}}) {% if install.is_beta %}<span class="beta-pill"></span>{% endif %}<i class="fa fa-arrow-right pull-right"></i></a>
            {% endif %}
            </div>
        {% endwith %}
    </td>
    <td headers="status_precomputed" align='center'>
        {% if install %}
            <div class="cell-container">
                {% if install.reports.all.0 %}
                    {% if install.inactive %}
                        <div class="sort_td" count_sort="0"></div>
                        {% if not install.is_trustd_app %}
                        <button type="button" url="{{ install.url_app_install_api }}" app_install_id="{{ install.id }}" class="btn btn-primary btn-rounded reactivate-button" >
                            <span class="default-resend-text">{% trans "Reactivate" %}</span>
                        </button>
                        {% else %}
                        <span>{% trans 'Reinstall required' %}</span>
                        {% endif %}
                    {% elif install.analytics.latest_pass_percentage %}
                        <div class="sort_td" count_sort="{{install.analytics.latest_pass_percentage}}"></div>
                        <div class="count_sort_child" >
                            <span class="percent_complete {% percent_to_css_class install.analytics.latest_pass_percentage "0-50:text-danger,51-99:text-warning,100:text-success"%} {% if not install.analytics.latest_pass_percentage %}opacity_0{% endif %}" custom-loaded-data="{{install.analytics.latest_pass_percentage |floatformat:'0'}}%"></span>
                            <div class="progress m-b-0">
                                <div class="progress-bar {% percent_to_css_class install.analytics.latest_pass_percentage %}" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width:0%;" custom-loaded-data="{{install.analytics.latest_pass_percentage}}%" custom-loaded-data-target='{"css": "width"}'></div>
                            </div>
                        </div>
                    {% else %}
                            <div class="sort_td" count_sort="0"></div>
                            <div class="count_sort_child" >
                                <span class="percent_complete text-danger" custom-loaded-data="0%"></span>
                                <div class="progress m-b-0">
                                    <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width:0%;" custom-loaded-data="{{install.analytics.latest_pass_percentage}}%" custom-loaded-data-target='{"css": "width"}'></div>
                                </div>
                            </div>
                            {% if install.analytics.latest_pass_percentage is None %}
                                <button type="button" class="btn btn-primary btn-rounded resend_email_to_user {% if app_user.disable_resend_email %} disabled {% endif %}" {% if app_user.disable_resend_email %} disabled {% endif %} resend-email-uuid="{{app_user.uuid}}">
                                    <span style="display: none" class="success-resend-text">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check-square"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                    <span class="default-resend-text">{% trans "Resend email" %}</span>
                                </button>
                            {% endif %}
                    {% endif %}
                {% else %}
                    {% trans "Install pending" %}
                {% endif %}
            </div>
        {% else %}
            <div class="cell-container">{% if not app_user.active %}{% trans "Inactive" %}{% else %}{% trans "Awaiting install" %}{% endif %}</div>
        {% endif %}
    {% flag "disable_add_fake_app_installs_button" %}
    {% else %}
        {% if can_add_fakes %}
            <button type="button" data-toggle="modal" data-target="#fakeAppInstallsModal-{{ app_user.uuid }}" class="btn has-spinner btn-default">
                <span class="btn-label"><i class="fa fa-plus"></i></span>{% trans "Add Fake Devices" %}
            </button>
            {% include "app_users/add_fake_installs.html" with app_user=app_user %}
        {% endif %}
    {% endflag %}
    </td>
    {% include "dashboard/dashboard-uba-policy-display.html" with install=install %}
    <td headers="most_recent_check_in_precomputed">
        {% if install %}
            <div class="cell-container datetime">
                <div>{{ install.last_check_in|date:"j M Y, h:ia" }}</div>
            </div>
        {% else %}
            <div class="cell-container">{% trans "No apps installed" %}</div>
        {% endif %}
    </td>
{% endif %}