{% load static %}
{% load i18n %}
{% load l10n %}
{% load divide_tags %}

{% with response.get_manual_fix as manually_fixed_check %}
{% if manually_fixed_check %}
    <div class="m-l-40" style="display:block">
        <i class="glyphicon glyphicon-info-sign text-danger m-t-10" style="color:blue; vertical-align:top"></i>
        <h5 class="manually-resolved text-danger">{% trans 'Manually resolved' %}</h5>
        <div class="manually-resolved-reason text-muted">
            <small class="m-l-30" style="color:silver">
                {% trans 'Reason' %}: {% if manually_fixed_check.reason %}{% trans manually_fixed_check.reason %}{% else %}{% trans 'Other' %}{% endif %}
            </small>
            <button class="btn btn-primary revert-button revert_manual has-spinner" data-url="{% call_method app_install 'url_revert' org_id=organisation.secure_id user_uuid=app_install.app_user.uuid device_id=app_install.device_id serial_number=app_install.url_encoded_serial_number %}" id_response="{{response.id}}" check-id="{{response.app_check.id}}">
                {% trans 'Revert' %}
                <span class="spinner"><div class="cssload-speeding-wheel" style="width:15px;height:15px;"></div></span>
            </button>
        </div>
    </div>
{% endif %}
{% endwith %}

{% with response.get_permanent_fix as permanently_fixed_check %}
{% if not response.is_manual_fix and permanently_fixed_check %}
    <div class="m-l-40" style="display:block">
        <i class="glyphicon glyphicon-info-sign text-danger m-t-10" style="color:blue; vertical-align:top"></i>
        <h5 class="manually-resolved text-danger">{% trans 'Permanently resolved' %}</h5>
        <div class="manually-resolved-reason text-muted">
            <small class="m-l-30" style="color:silver">
                {% trans 'Reason' %}: {% if permanently_fixed_check.reason %}{% trans permanently_fixed_check.reason %}{% else %}{% trans 'Other' %}{% endif %}
            </small>
        </div>
    </div>
{% endif %}
{% endwith %}

{% block extra-js %}
    <script type="application/javascript">
        $(".revert_manual").click(function(e) {
            // show spinner and disable button
            $(this).attr("disabled", "disabled");
            $(this).addClass('active');
            var csrfmiddlewaretoken = "{{ csrf_token }}";
            var id_response = $(this).attr('id_response');
            var check_id = $(this).attr('check-id');
            check_id = parseInt(check_id);
            $.ajax({
                url: $(this).data('url'),
                method: "POST",
                data: {
                    'csrfmiddlewaretoken' : csrfmiddlewaretoken,
                    'check_id': check_id ,
                    'id_response': id_response,
                    'manual_resolved': 'true'
                }
            }).done(function(response) {
                location.reload();
            });
        });
    </script>
{% endblock %}