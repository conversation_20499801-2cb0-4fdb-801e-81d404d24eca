{% load i18n %}
{% load divide_tags %}
{% load static %}
{% load check_permissions %}

{% block extra-css %}
    <style type="text/css">
        #user-attribution-modal td{
            border: none;
            padding: 8px;
        }
        .btn-primary{
            border: 1px solid #446EE2;
        }
    </style>
{% endblock %}

<div class="modal fade user-attribution-modal" id="user-attribution-modal-{{ app_install.pk }}" data-app-install-hostname="{{ app_install.hostname }}" data-app-install-pk="{{ app_install.pk }}" data-original-app-user-uuid="{{ app_install.app_user.uuid }}" data-current-app-user-uuid="{{ app_install.app_user.uuid }}" data-device-assign-user-url="{{ app_install.url_device_assign_user }}" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
      <div class="tw-bg-white tw-p-6 tw-rounded-lg">
          
        <div class="tw-relative">
            <h4 class="tw-font-sans tw-font-semibold tw-text-lg tw-my-0">
                {% trans "Assign an Active Protect user to this machine" %}
            </h4>

            <p class="tw-text-sm tw-text-gray-600">
                {{ app_install.hostname }}
            </p>
        </div>

        <div>
            {% if organisation.user_attribution_appusers %}
                <div class="tw-mt-6 form-inline">
                    <div class="tw-flex tw-justify-between tw-w-full tw-gap-4 tw-mb-4">
                        <div class="tw-flex tw-self-start tw-justify-between tw-content-center tw-items-center tw-w-full tw-relative tw-font-sans">
                            <input type="text" placeholder="{% trans 'Search for user...' %}" class="tw-w-full tw-border tw-pl-10 tw-border-gray-300 tw-p-2 tw-rounded-full hover:tw-border-gray-400 focus:tw-border-gray-300 focus:tw-shadow-[0_0px_0px_3px_rgba(0,0,0,0.07)] tw-transition demo-input-search2" autocomplete="off">
                            <svg class="tw-w-[20px] tw-h-[20px] tw-absolute tw-left-3 tw-fill-gray-600">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-search"></use>
                            </svg>
                        </div>
                    </div>
                </div>
            
                <table class="table demo-foo-addrow table-hover toggle-circle tw-mt-6" data-page-size="5">
                    <tbody>
                        {% for app_user in organisation.user_attribution_appusers %}
                            <tr>
                                <td class="text-left">
                                    <div class="tw-font-semibold">{{ app_user.first_name }} {{ app_user.last_name }}</div>
                                    <div class="tw-text-gray-600">{{ app_user.email }}</div>
                                </td>
                                <td style="width:55%" class="tw-text-right">
                                    {% if app_install.app_user == app_user %}
                                        <button class="btn btn-success assign-device-to-user tw-mt-0.5" disabled type="button" data-app-user-uuid="{{ app_user.uuid }}">
                                            {% trans "Assigned" %}
                                        </button>
                                    {% else %}
                                        <button class="btn btn-primary assign-device-to-user tw-mt-0.5" type="button" data-app-user-uuid="{{ app_user.uuid }}">
                                            {% trans "Assign" %}
                                        </button>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="2">
                                <div class="m-t-10 tw-font-sans">
                                    <ul class="pagination"></ul>
                                </div>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            {% else %}
            <div class="tw-mt-4 tw-flex tw-flex-col tw-gap-2 tw-bg-white tw-border tw-border-dashed tw-border-gray-300 tw-rounded-md tw-place-content-center tw-items-center tw-h-full tw-py-10 tw-mb-6 tw-font-sans tw-text-center">
                <p class="tw-font-semibold">{% trans "No Active Protect users have been added" %}</p>
                <p class="tw-font-medium tw-text-gray-600">{% trans "Add an Active Protect user to assign to this machine." %}</p>
                <a href="{% call_method organisation "get_manage_users_page" perms %}" target="_blank" class="tw-bg-brand-600 tw-w-fit tw-mt-2 hover:tw-bg-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out tw-h-9 tw-flex tw-items-center tw-justify-center tw-px-4 tw-py-3 tw-gap-1 tw-rounded-md tw-text-white tw-text-center tw-font-sans tw-font-medium tw-shadow-sm hover:tw-text-white focus:tw-ring-4 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0">
                    <svg class="tw-w-4 tw-h-4 tw-fill-white">
                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-plus"></use>
                    </svg>
                    {%  trans 'Add users' %}
                </a>
            </div>
            {% endif %}
        </div>

        <div class="tw-flex tw-justify-between">
            {% if organisation.user_attribution_appusers %}
            <div class="tw-flex tw-gap-2">
                <button class="tw-flex tw-text-nowrap tw-justify-center tw-items-center tw-gap-1 tw-min-h-10 tw-px-4 tw-py-1 tw-rounded-md tw-bg-red-600 tw-text-white tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-red-600/90 focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none active:tw-bg-gray-200 active:tw-ring-0 {% if app_install.app_user == organisation.main_app_user %}hidden{% endif %} unassign-from-user">
                    {% trans 'Unassign from user' %}
                </button>
    
                {% if perms|has_access_to_manage_app_users_page:organisation %}
                <a class="tw-flex tw-text-nowrap tw-justify-center tw-items-center tw-gap-1 tw-min-h-10 tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0" type="button" href="{% call_method organisation "get_manage_users_page" perms %}" target="_blank">
                    <svg class="tw-w-4 tw-h-4">
                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-plus"></use>
                    </svg>
                    {% trans "Add users" %}
                </a>
                {% endif %}
            </div>
            {% endif %}

            <button class="tw-flex tw-text-nowrap tw-justify-center tw-ml-auto tw-items-center tw-gap-1 tw-min-h-10 tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 assign-done" >{% trans 'Close' %}</button>
        </div>
      </div>
  </div>
</div>