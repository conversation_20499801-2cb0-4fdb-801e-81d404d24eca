{% load static %}
{% load i18n %}
{% load l10n %}
{% load divide_tags %}

<button data-toggle="modal" data-target="#mark-resolved-modal-{{ app_check.id }}" class="btn btn-success mark_as_resolved has-spinner">
    <span class="btn-label"><i class="fa fa-check"></i></span>{% trans "Manual resolve" %}
</button>

<div class="modal fade" id="mark-resolved-modal-{{ app_check.id }}" tabindex="-1" role="dialog" >
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            {% include 'partials/manually_resolve_reasons.html' with app_check=app_check %}
            <div class="modal-footer">
                <button type="button" class="btn btn-default waves-effect text-left" data-dismiss="modal">{% trans "Cancel" %}</button>
                <button onclick="permanentlyResolveCheck(this)" data-app-check-id="{{ app_check.id }}" class="btn btn-primary align-left" id="permanently-resolve-issue-{{ app_check.id }}" type="button" >
                    {% trans 'Permanently Resolve' %}
                </button>
                <button onclick="manuallyResolveCheck(this)" data-app-check-id="{{ app_check.id }}" class="btn btn-primary align-left" id="resolve-issue-{{ app_check.id }}" type="button" >
                    {% trans 'Only On Current Devices' %}
                </button>
            </div>
        </div>
    </div>
</div>

{% block extra-js %}
   <script type="text/javascript" src="{% static 'js/partials/resolve_checks.js' %}"></script>
{% endblock %}
