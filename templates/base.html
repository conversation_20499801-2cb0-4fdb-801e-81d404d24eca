{% load static %}
{% load i18n %}
{% load statici18n %}
{% load waffle_tags %}

<!DOCTYPE html>
{% get_current_language as LANGUAGE_CODE %}
<html lang="{{ LANGUAGE_CODE }}">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{% block head_description %}{% endblock %}">
    <meta name="author" content="CyberSmart">

    <link rel="apple-touch-icon" sizes="144x144" href="{% static 'icons/new/apple-touch-icon.png' %}">
    <link rel="icon" type="image/png" sizes="32x32" href="{% static 'icons/new/favicon-32x32.png' %}">
    <link rel="icon" type="image/png" sizes="16x16" href="{% static 'icons/new/favicon-16x16.png' %}">
    <link rel="manifest" href="{% static 'icons/new/site.webmanifest' %}">
    <link rel="mask-icon" href="{% static 'icons/new/CSComplete.svg' %}" color="#000000">
    <link rel="shortcut icon" href="/favicon.ico">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-config" content="{% static 'icons/new/browserconfig.xml' %}">
    <meta name="theme-color" content="#ffffff">

    <title>{% block head_title %}{% endblock %} | CyberSmart</title>


    <script type="application/javascript">
        let is_prod = "{{ is_prod }}" === "True";
        let is_stage = "{{ is_stage }}" === "True";
        let is_develop = "{{ is_develop }}" === "True";
    </script>
    <!-- For translated JS files -->
    <script src="{% statici18n LANGUAGE_CODE %}"></script>
    {% include 'partials/google-tag.html' %}
    <!-- Bootstrap Core CSS -->
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet" >
    <!-- Footable CSS -->
    <link href="https://cdn.jsdelivr.net/gh/fooplugins/FooTable@V2.0.1.4/css/footable.core.min.css" rel="stylesheet">
    <!-- served via CDN for font support -->
    <link href="{% static 'css/bootstrap-select.min.css' %}" rel="stylesheet" />
    <!-- Menu CSS -->
    <link href="{% static 'css/sidebar-nav.min.css' %}" rel="stylesheet">
    <!-- animation CSS -->
    <link href="{% static 'css/animate.css' %}" rel="stylesheet">
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Lato:300,400,500,600,700" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/gh/lykmapipo/themify-icons@0.1.2/css/themify-icons.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/gh/linea-io/Linea-Iconset@1.0/_arrows/_ICONFONT/styles.min.css"
        rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/gh/linea-io/Linea-Iconset@1.0/_basic_elaboration/_ICONFONT/styles.min.css"
        rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/gh/linea-io/Linea-Iconset@1.0/_basic/_ICONFONT/styles.min.css"
        rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    <link href="{% static 'css/spinners.css' %}" rel="stylesheet">

    <!-- Waffle JS -->
    <script src="{% url 'wafflejs' %}"></script>

    <!--switchery -->
    <link href="{% static 'css/switchery.min.css' %}" rel="stylesheet" />
    <!--alerts CSS -->
    <link href="{% static 'css/sweetalert.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/base.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/tailwind-output.css' %}" rel="stylesheet" />
    <!-- jQuery -->
    <script src="{% static 'js/jquery.2.2.4.min.js' %}"></script>
    <script src="{% static 'js/htmx-2.0.4.min.js' %}"></script>
    <!-- moved to header for SWAL -->

    {% flag "user-guiding" %}
        {% if request.user.is_authenticated %}
            <script>
                (function(g,u,i,d,e,s){g[e]=g[e]||[];var f=u.getElementsByTagName(i)[0];var k=u.createElement(i);k.async=true;k.src='https://static.userguiding.com/media/user-guiding-'+s+'-embedded.js';f.parentNode.insertBefore(k,f);if(g[d])return;var ug=g[d]={q:[]};ug.c=function(n){return function(){ug.q.push([n,arguments])};};var m=['previewGuide','finishPreview','track','identify','hideChecklist','launchChecklist'];for(var j=0;j<m.length;j+=1){ug[m[j]]=ug.c(m[j]);}})(window,document,'script','userGuiding','userGuidingLayer','UO0821362ALID');
            </script>
        {% endif %}
    {% endflag %}

    {% block extra-css %}
    {% endblock %}

    {% block extra-head %}
    {% endblock %}
    <style type="text/css">
        .switchery:before, .switch input+.slider::after {
            content: "{% trans 'No' %}";
        }
        .js-switch:checked+.switchery:before, .switch input:checked+.slider::after {
            content: "{% trans 'Yes' %}";
        }
    </style>
</head>

<body>
    {% include 'partials/google-tag-body.html' %}
    <!-- Preloader -->
    <div class="preloader" style="display: none;">
    <div class="cssload-speeding-wheel"></div>
    </div> 

    {% block content %}
    {% if request.user.profile.is_partner and request.user.profile.partner.trial_account and not request.user.profile.is_distributor %}
    <div class="alert alert-warning align-center">
        {% trans "This is a Trial Account and has limited functionality" %}
    </div>
    {% endif %}
    <main>
        <div id="wrapper">
            {% block header_and_nav %}
                {% if not hide_header_and_nav %}
                    {% block header_nav %}
                    {% include 'partials/side-navigation/header.html' %}
                    {% include 'partials/inactiivty.html' %}
                    {% endblock %}
                    {% block sub_nav %}
                    {% include 'partials/sub-header-partner.html' %}
                    {% endblock %}
                    {% if not hide_nav_bar %}
                        {% include "partials/side-navigation/main.html" %}
                    {% endif %}
                {% endif %}
            {% endblock %}
            <div id="page-wrapper" class="{{ cs_sidenav }} tw-relative tw-max-w-[2000px] tw-mx-auto tw-transition-all">
                {% include 'partials/notification.html' %}
                {% switch "marketing_banner" %}
                    {% if marketing_banner %}
                        {% include 'partials/marketing_banner.html' %}
                    {% endif %}
                {% endswitch %}
                {% include 'partials/loading.html' with loader_id='base-loading-partial' %}
                {% block main-content %}{% endblock %}
                {% if not hide_footer %}
                    {% include 'partials/footer.html' %}
                {% endif %}
            </div>
        </div>
    </main>
    {% endblock %}

    <!-- Bootstrap Core JavaScript -->
    <script src="{% static 'js/bootstrap.min.js' %}" ></script>
    <!-- Menu Plugin JavaScript -->
    <script src="{% static 'js/sidebar-nav.min.js' %}"></script>
    <!--slimscroll JavaScript -->
    <script src="{% static 'js/jquery.slimscroll.js' %}"></script>
    <!--Wave Effects -->
    <script src="{% static 'js/waves.js' %}"></script>
    <!-- Custom Theme JavaScript -->
    <script src="{% static 'js/custom.js' %}"></script>
    <!-- Footable -->
    <script src="{% static 'plugins/footable/js/footable.all.min.js' %}"></script>
    <script src="{% static 'js/bootstrap-select.min.js' %}" type="text/javascript"></script>
    <!--FooTable init-->
    <script src="{% static 'js/jquery.throttle-debounce.min.js' %}"></script>
    <script src="{% static 'js/footable-init.js' %}"></script>
    <!--switchery -->
    <script src="{% static 'js/switchery.min.js' %}"></script>
    <!-- Sweet-Alert  -->
    <script src="{% static 'js/sweetalert.min.js' %}"></script>

    <script type="text/javascript" src="{% static 'js/jquery.validate.min.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/bootbox.min.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/utils.js' %}"></script>
    <!--  http://szimek.github.io/signature_pad/ -->
    <script type="text/javascript" src="{% static 'js/signature_pad.min.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/inactivity.js' %}"></script>
    <script>
        {# save all switchery instances to this object #}
        let switcheryObjects = {};
        jQuery(document).ready(function () {
            // Switchery
            document.querySelectorAll('.js-switch').forEach(function (element) {
                switcheryObjects[element.id] = new Switchery(element, { color: '#1ac568' });
            });

            /*
            a workround for sending csrf token through Ajax requests.
            */
            function getCookie(name) {
                var cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    var cookies = document.cookie.split(';');
                    for (var i = 0; i < cookies.length; i++) {
                        var cookie = cookies[i].trim();
                        // Does this cookie string begin with the name we want?
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
            var csrftoken = getCookie('csrftoken');
            if (!csrftoken) {
                var csrftoken = jQuery("[name=csrfmiddlewaretoken]").val();
            }

            function csrfSafeMethod(method) {
                // these HTTP methods do not require CSRF protection
                return (/^(GET|HEAD|OPTIONS|TRACE)$/.test(method));
            }
            $.ajaxSetup({
                beforeSend: function (xhr, settings) {
                    if (!csrfSafeMethod(settings.type) && !this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", csrftoken);
                    }
                }
            });
            function set_cookie(name, value) {
              document.cookie = name +'='+ value +'; Path=/;';
            }
            function delete_cookie(name) {
              document.cookie = name +'=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
            }
            // cleanup old inactivity cookies
            delete_cookie("last_activity");
            delete_cookie("session_expiry");
            // set timeout for inactivity popup
            set_cookie("session_expiry", "{{ SESSION_EXPIRY_TIME }}");
        });
    </script>

    {% block extra-js %}
    {% endblock %}

    {% block extra-body %}
        {% if not user.is_impersonate and not is_bot_or_auto_test %}
            {% include "partials/javascript_trackers.html" %}
        {% endif %}
    {% endblock %}

    {% switch "enable-salesforce-chat" %}
    <!-- Salesforce -->
    <script type='text/javascript'>
        function initEmbeddedMessaging() {
            try {
                embeddedservice_bootstrap.settings.language = 'en_GB';
    
                embeddedservice_bootstrap.init(
                    '00D4H000001IJMx',
                    'Chatbot_Messages',
                    'https://cybersmart.my.site.com/ESWChatbotMessages1750343587575',
                    {
                        scrt2URL: 'https://cybersmart.my.salesforce-scrt.com'
                    }
                );
            } catch (err) {
                console.error('Error loading Embedded Messaging: ', err);
            }
        };
        {% if request.user.is_authenticated %}
        // prepopulate prechat fields
        window.addEventListener("onEmbeddedMessagingReady", e => {
            embeddedservice_bootstrap.prechatAPI.setVisiblePrechatFields({
                "First Name": {
                    "value": "{{ request.user.first_name }}"
                },
                "Last Name": {
                    "value": "{{ request.user.last_name }}"
                },
                "Email": {
                    "value": "{{ request.user.email }}"
                }
            });
        });
        {% endif %}
    </script>
    <script type='text/javascript' src='https://cybersmart.my.site.com/ESWChatbotMessages1750343587575/assets/js/bootstrap.min.js' onload='initEmbeddedMessaging()'></script>
    <!-- end Salesforce -->
    {% endswitch %}

  </body>
</html>
