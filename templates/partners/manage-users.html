{% extends 'base.html' %}
{% load divide_tags %}
{% load static %}
{% load i18n %}
{% block extra-head %}
    <script>
        $(document).ready(function () {
            $("#less-info-button").on("click", function(e) {
                $(".extra-info").slideUp();
                $(this).hide();
                $("#more-info-button").show();
            });
            $("#more-info-button").on("click", function(e) {
                $(".extra-info").slideDown();
                $(this).hide();
                $("#less-info-button").show();
            });
        });
    </script>
{% endblock extra-head %}
{% block head_title %}{% trans "Manage Partner Users" %}{% endblock %}
{% block main-content %}
    <div class="container-fluid">
        {% trans 'Settings' as page_title %}
        {% trans 'Manage notifications, partner users, and branding.' as page_description %}
        {% include "partials/header_default.html" with page_title=page_title page_description=page_description page_icon="settings-2" %}

        {% include "partners/security_control/settings_page_switch.html" with manage_user=True %}
        <div class="row white-box">
            <div>
                <h4>{{ partner.partner.name }}</h4>
                <p>{% trans "These users have full access to the partner dashboard and all organisations" %}</p>
                <ul style="padding-left: 0">
                    {% for partner in partner.partner.users.all %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {{ partner.user }} ({{ partner.user.email }})
                            {% if request.user == partner.user %}
                                <button class="btn btn-danger btn-sm pull-right disabled" data-toggle="tooltip" data-placement="left" title="Cannot remove own account">{% trans "Remove" %}</button>
                            {% else %}
                                <button class="btn btn-danger btn-sm pull-right" data-toggle="modal" data-target="#delete-modal{{ partner.secure_id }}" style="margin-top: -5px;">{% trans "Remove" %}</button>
                                <div class="modal fade" id="delete-modal{{ partner.secure_id }}" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                                <h4 class="modal-title" id="myModalLabel">{% trans "Remove" %}</h4>
                                            </div>
                                            <div class="modal-body">
                                                {% blocktrans trimmed with partner.user as user and partner.user.email as email %}
                                                    Do you want to remove <b>{{ user }} ({{ email }})</b> ?
                                                {% endblocktrans %}
                                            </div>
                                            <div class="modal-footer">
                                                <form action="{% url 'partners:delete' partner.secure_id %}" method="POST">
                                                    {% csrf_token %}
                                                    <button class="btn btn-default" data-dismiss="modal">{% trans "No" %}</button>
                                                    <button type="submit" class="btn btn-primary">{% trans "Yes" %}</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
                <h4>{% trans "Add partner user" %}</h4>
                <form action="{% url 'partners:create' %}" method="POST">
                    {% csrf_token %}
                    <input name="email" class="form-control" style="width: 200px; display: inline-block; height: 35px" placeholder="Email" type="text">
                    <button class="btn btn-primary">{% trans "Add" %}</button>
                </form>
            </div>
        </div>
    </div>
{% endblock main-content %}