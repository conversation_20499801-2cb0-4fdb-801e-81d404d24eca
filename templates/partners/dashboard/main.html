{% extends 'base.html' %}
{% load divide_tags %}
{% load static %}
{% load i18n %}
{% block extra-css %}
    <link href="{% static 'css/partner-dashboard.css' %}" rel="stylesheet" />
{% endblock extra-css %}
{% block head_title %}{% trans "Partner Dashboard" %}{% endblock %}
{% block main-content %}
    {# When a distributor is viewing a partner page, then we need to pass the partner ID for API requests #}
    <span id="partner_id_kwarg" hidden data-partner_id_kwarg="{{ partner_id_kwarg }}"></span>

    <div class="container-fluid">
        {% if partner.partner.frozen %}
            <div class="card bg-danger text-white font-bold m-t-20 p-10 text-center">
                <span class="glyphicon glyphicon-exclamation-sign"></span>
                {% blocktrans %}
                    This partner account has been frozen and locked. No organisation associated can submit any
                    questionnaire. Please contact your distributor for more information.
                {% endblocktrans %}
            </div>
            <br>
        {% endif %}
        <div {% if partner.partner.frozen %}class="blocked-blur"{% endif %}>
            <div class="tw-flex tw-flex-col tw-text-center xl:tw-flex-row xl:tw-text-left tw-items-center tw-justify-between tw-py-8">
                <!-- Left section -->
                <div class="tw-flex tw-flex-col xl:tw-flex-row tw-items-center tw-gap-3">
                    <!-- Icon -->
                    <div class="tw-flex tw-items-center tw-justify-center">
                        <svg class="tw-w-[38px] tw-h-[38px]">
                            <use href="{% static 'icons/icons-sprite.svg' %}#icon-boxes"></use>
                        </svg>
                    </div>
                    <!-- Text content -->
                    <div>
                        <h1 class="tw-font-sans tw-text-xl tw-font-semibold tw-text-gray-900 !tw-my-0">{% trans "Partner dashboard" %}</h1>
                        <p class="tw-font-sans tw-text-sm tw-text-gray-600">{% trans "A centralised view of key information across your organisations." %}</p>
                    </div>
                </div>
                <!-- Right section -->
                <div class="tw-flex tw-pt-2 tw-gap-2 xl:tw-pt-0 tw-font-sans">
                    <a href="{% url 'partners:create-organisation' %}" class="btn has-spinner btn-primary" type="button">
                        {% trans "Add organisation" %}
                    </a>
                </div>
            </div>
            
            <div class="row">
                {% include "partners/partials/cap_banner.html" %}
                {% include "partners/dashboard/widgets/overall.html" %}
                <div class="col-lg-12 big-widgets-block">
                    {% if ps.is_certifications_enabled and not IS_EU_GEO %}
                        <div class="row">
                            <div class="col-lg-6">
                                {% include "partners/dashboard/widgets/certifications.html" %}
                            </div>
                            <div class="col-lg-6" id="expiring-cert">
                                {% include "partners/dashboard/widgets/certifications_expiring.html" %}
                            </div>
                        </div>
                    {% endif %}
                    {% if not partner.partner.frozen %}
                        <div class="row">
                            {% if ps.is_smart_score_enabled %}
                            <div class="col-lg-6">
                                {% include "partners/dashboard/widgets/organisations_smart_score.html" %}
                            </div>
                            {% endif %}
                            <div class="col-lg-6">
                                {% include "partners/dashboard/widgets/organisations_devices.html" %}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock main-content %}
{% block extra-js %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/rxjs/7.5.6/rxjs.umd.min.js" integrity="sha512-yUlloKtrX4U41iKbaafsHrrwII0n1uAX9+3KzuaTjkfgLicBplGfr0vv7FtfwC5kgead1Dmr7sjunFFp0a+s9Q==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js" integrity="sha512-+H4iLjY3JsKiF2V6N366in5IQHj2uEsGV7Pp/GRcm0fn76aPAk5V8xB6n8fQhhSonTqTXs/klFz4D0GIn6Br9g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="{% static 'js/modules/partner/dashboard/widget-filter.js' %}"></script>
    <script src="{% static 'js/modules/partner/dashboard/pagination.js' %}"></script>
{% endblock extra-js %}