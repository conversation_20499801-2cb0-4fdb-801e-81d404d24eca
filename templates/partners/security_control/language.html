{% load i18n %}

<div class="row tw-pl-3">
    <div>
        <h4 class="tw-font-sans tw-font-semibold tw-text-base tw-p-0 tw-m-0">{% trans "Language" %}</h4>
        <div class="tw-font-sans tw-font-normal tw-text-sm tw-p-0 tw-mt-1 tw-rounded-md tw-gap-3 tw-px-3 tw-py-2 tw-border tw-border-green-700 tw-bg-green-50 tw-text-green-700 tw-w-auto" id="success-message" style="display:none;">
            {% trans "Successfully updated the default language to" %} <span id="success-language"></span>
        </div>
        <div class="tw-font-sans tw-font-normal tw-text-sm tw-p-0 tw-mt-1 tw-rounded-md tw-gap-3 tw-px-3 tw-py-2 tw-border tw-border-red-custom tw-bg-red-50 tw-text-red-custom tw-w-auto" id="error-message" style="display:none;">
            {% trans "Unable to set the default language. Try again or contact support if the issue persists." %}
        </div>
        <div class="tw-font-sans tw-font-normal tw-text-sm tw-p-0 tw-mt-3 tw-line-height-normal ">
            {% blocktranslate trimmed %}
                Set the default language for your partner dashboard and any new organisations you create.
                <br/>
                This will apply to email notifications and assessment exports.
                <br/>
                Individual organisations can update their own default language at any time.
            {% endblocktranslate %}
        </div>
        <br/>
        <label class="tw-block tw-text-base tw-mb-2 tw-font-medium ">{% trans "Default language" %}</label>
        <select class="tw-w-80 tw-border tw-px-2 tw-py-3 tw-rounded-md tw-font-sans tw-font-normal tw-text-sm" id="language_selected" name="default_language" required>
            <option value="">{% trans "Select a language" %}</option>
            {% for code, name in languages %}
                <option value="{{ code }}" {% if code == partner.partner.default_language %}selected{% endif %}>
                    {{ name }}
                </option>
            {% endfor %}
        </select>
    </div>
    <button type="submit" class="btn m-t-20 btn-primary" id="update_partner_settings"><i class="fa fa-save"></i> {% trans "Save" %}</button>
</div>
{% block extra-js %}
<script type="text/javascript">
    $(document).ready(function() {
        const updateButton = document.getElementById("update_partner_settings");
        updateButton.addEventListener("click", function(event) {
            event.preventDefault();
            const selectedLanguage = document.getElementById("language_selected").value;
            const selectedLanguageText = $("#language_selected option:selected").text();
            $.ajax({
                url: "{% url 'api-v3:partners:settings' partner.secure_id %}",
                method: "PATCH",
                data: {
                    "default_language": selectedLanguage,
                    "csrfmiddlewaretoken": "{{ csrf_token }}"
                },
                success: function(response) {
                    $("#error-message").hide()
                    $("#success-language").text(selectedLanguageText);
                    $("#success-message").show();
                },
                error: function(xhr, status, error) {
                    $("#success-message").hide()
                    $("#error-message").show();
                }
            });
        });
    });
</script>
{% endblock %}