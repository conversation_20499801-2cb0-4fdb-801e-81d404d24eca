{% extends 'base.html' %}
{% load divide_tags %}
{% load static %}
{% load i18n %}
{% block head_title %}
    {% trans "Notifications and language" %}
{% endblock %}
{% block main-content %}
    <div class="container-fluid">
        {% trans 'Settings' as page_title %}
        {% trans 'Manage notifications, language, partner users, and branding.' as page_description %}
        {% include "partials/header_default.html" with page_title=page_title page_description=page_description page_icon="settings-2" %}
        {% include "partners/security_control/settings_page_switch.html" with security_control=True %}
        <div class="row white-box tw-border">
            <form action="." method="post">
                {% csrf_token %}
                {% include "partners/security_control/overall_settings.html" %}
                {% include "partners/security_control/failed_check.html" %}
                {% include "partners/security_control/device_report.html" %}
                <button type="submit" class="btn m-t-20 btn-primary"><i class="fa fa-save"></i> {% trans "Save" %}</button>
            </form>
            <hr/>
            {% include "partners/security_control/language.html" %}
        </div>
    </div>
{% endblock main-content %}
{% block extra-js %}
    <script type="text/javascript">
        $(document).ready(function() {
            $(document).on("click", ".remove-email", function () {
                var pk = $(this).data("pk");
                $.ajax({
                    url: $(this).data("url"),
                    method: "DELETE",
                    success: function(data) {
                        if (data.success) {
                            $("#email-container-" + pk).slideUp();
                        } else {
                            swal(
                                { title: gettext("Failure"), text: gettext("Unable to delete this email"), type: "error", html: true },
                                function() {
                                }
                            );
                        }
                    },
                    error: function(xhr,errmsg,err) {
                        console.log(err);
                    }
                });
                return false;
            });
            $(".submit-email").click(function () {
                var type = $(this).attr("email-type");
                var email = $("#id_security_control_email_" + type).val();
                $.ajax({
                    url: $(this).attr("url"),
                    method: "POST",
                    data: {
                        "email": email,
                        "type": type === "report"? 1: 2
                    },
                    success: function(data) {
                        if (data.success) {
                            $(".list-group-" + type).append($(
                                '<li class="list-group-item no-border" id="email-container-'+ data.pk +'">\n' +
                                '<div class="pull-left">\n' +
                                '<i class="glyphicon glyphicon-envelope text-primary"></i>\n' +
                                email + '\n' +
                                '</div>\n' +
                                '<div class="pull-right">\n' +
                                '<a class="remove-email clickable" data-pk="' + data.pk + '" data-url="' + data.delete_url +'"><i class="fa fa-trash text-primary"></i></a>\n' +
                                '</div>\n' +
                                '<div class="clear"></div>\n' +
                                '</li>'
                            ))
                        } else {
                            var error = null;
                            if ("email" in data) {
                                error = data.error["email"][0];
                            } else {
                                error = data.error["__all__"][0]
                            }
                            swal(
                                { title: gettext("Failure"), text: error, type: "error", html: true },
                                function() {
                                }
                            );
                        }
                        $('.add-email-modal').modal('hide');
                    },
                    error: function(xhr,errmsg,err) {
                        console.log(err);
                    }
                });
            });
        });
    </script>
{% endblock extra-js %}