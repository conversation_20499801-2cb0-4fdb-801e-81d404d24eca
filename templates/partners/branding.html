{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block head_title %}{% trans "Manage Partner Branding" %}{% endblock %}
{% block main-content %}
    <div class="container-fluid">
        {% trans 'Settings' as page_title %}
        {% trans 'Manage notifications, partner users, and branding.' as page_description %}
        {% include "partials/header_default.html" with page_title=page_title page_description=page_description page_icon="settings-2" %}
        {% include "partners/security_control/settings_page_switch.html" with partner_branding=True %}
        <div class="row white-box tw-border">
            <h3>{% trans "Manage Partner Branding" %}</h3>
            <p>{% trans "For the CyberSmart Dashboard, Active Protect apps and emails please ensure both light and dark versions of your organisation’s logo are uploaded." %}</p>
            <div class="well well-sm text-muted">
                {% trans 'Both logos should be PNG or JPG, sized at 400px by 120px, preferably with a transparent background.' %}
            </div>
            <br>
            <form method="POST" action="." enctype="multipart/form-data">
                {% csrf_token %}
                {% for err in form.non_field_errors  %}
                    <div class="alert alert-danger alert-dismissible" role="alert">
                        {{err}}
                    </div>
                {% endfor %}
                <div class="form-group {% if form.light_logo.errors %}has-error{% endif %} ">
                    <label class="control-label"><b>{% trans 'Light Logo' %}</b></label>
                    <div class="row">
                        <div class="upload-preview col-md-4">
                            <div class="upload-text">
                                <p>{% trans "To be used on a dark coloured background" %}</p>
                                <input accept="image/*" {% if not object.light_logo %}required{% endif %} onchange="validateFile(this.value, {{ form.EXTENSIONS|safe }}, 'invalid_warning_light', true)" type="file" name="light_logo" class="form-control" id="id_light_logo">
                                <small hidden id="invalid_warning_light" class="text-danger">{% trans 'Invalid file extension, please provide one of ' %}{{ form.EXTENSIONS|safe }}</small>
                                {% if form.light_logo.errors %}
                                    {% for err in form.light_logo.errors%}
                                        <small class="text-danger">{{err}}</small>
                                    {%endfor%}
                                {% endif %}
                                {% if object.light_logo %}
                                    <div class="col-md-6 logo-preview-container">
                                        <div class="tw-bg-[#0f222d] tw-p-2">
                                            <img src="{{ object.light_logo.url }}" alt="{% trans 'Light Logo Preview' %}" class="logo-preview">
                                        </div>
                                        <p class="text-muted">{% trans "Current light logo" %}</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-8 branding-icons-container">
                            <img src="{% static 'images/icons/partner_logo_instructions_dashboard_dark.svg' %}" alt="CyberSmart Dashboard Logo Preview" class="logo-preview">
                            <img src="{% static 'images/icons/partner_logo_instructions_cap_dark.svg' %}" alt="Active Protect Desktop Logo Preview" class="logo-preview">
                            <img src="{% static 'images/icons/partner_logo_instructions_mobile_dark.svg' %}" alt="Active Protect Mobile Logo Preview" class="logo-preview">
                            <p class="text-muted">{% trans "Used for CyberSmart Dashboard and Active Protect Desktop in dark mode and Mobile" %}</p>
                        </div>
                    </div>
                </div>

                <div class="form-group {% if form.dark_logo.errors %}has-error{% endif %}">
                    <label class="control-label"><b>{% trans 'Dark Logo' %}</b></label>
                    <div class="row">
                        <div class="upload-preview col-md-4">
                            <div class="upload-text">
                                <p>{% trans "To be used on a light coloured background" %}</p>
                                <input accept="image/*" {% if not object.dark_logo %}required{% endif %} onchange="validateFile(this.value, {{ form.EXTENSIONS|safe }}, 'invalid_warning_dark', true)" type="file" name="dark_logo" class="form-control" id="id_dark_logo">
                                <small hidden id="invalid_warning_dark" class="text-danger">{% trans 'Invalid file extension, please provide one of ' %}{{ form.EXTENSIONS|safe }}</small>
                                {% if form.dark_logo.errors %}
                                    {% for err in form.dark_logo.errors %}
                                        <small class="text-danger">{{ err }}</small>
                                    {% endfor %}
                                {% endif %}
                                {% if object.dark_logo %}
                                    <div class="col-md-6 logo-preview-container">
                                        <img src="{{ object.dark_logo.url }}" alt="{% trans 'Dark Logo Preview' %}" class="logo-preview">
                                        <p class="text-muted">{% trans "Current dark logo" %}</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-8 branding-icons-container">
                            <img src="{% static 'images/icons/partner_logo_instructions_cap_light.svg' %}" alt="Dark Logo Preview" class="logo-preview">
                            <p class="text-muted">{% trans "Used for Active Protect Desktop in light mode and emails" %}</p>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" name="action" class="btn btn-primary" id="save_button" disabled>{% trans 'Save' %}</button>
                </div>
            </form>
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
{% endblock %}

{% block extra-css %}
    <style>
        .well {
            background-color: #CDE6EC;
            color: #3E6B89;
            border-radius: 8px;
            border: 1px solid #CDE6EC;
        }
        
        .logo-preview {
            width: 120px;
        }
        .logo-preview-container {
            padding-top: 10px;
            font-style: italic;
        }
        .branding-icons-container, .branding-icons-container img {
            font-style: italic;
            max-height: 86px;
        }
        .branding-icons-container > img:first-child {
            margin-right: 25px;
        }
    </style>
{% endblock %}

{% block extra-js %}
<script>
    $(document).ready(function() {
        let formDirty = false;
        // Track form changes
        $('form :input').on('change', function() {
            formDirty = true;
        });
        window.addEventListener('beforeunload', function(e) {
            if (formDirty) {
                e.preventDefault();
                e.returnValue = '{% trans "You have unsaved changes. Are you sure you want to leave?" %}';
            }
        });
        // Don't show warning when form is submitted
        $('form').on('submit', function() {
            formDirty = false;
        });
    });
</script>
{% endblock %}
