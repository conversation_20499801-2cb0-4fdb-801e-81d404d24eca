{% extends 'base.html' %}
{% load divide_tags %}
{% load plans %}
{% load dist_tags %}
{% load static %}
{% block extra-css %}
    <link href="{% static 'css/subscriptions-overview.css' %}" rel="stylesheet" />
{% endblock extra-css %}
{% load i18n %}
{% block head_title %}{% if partner_view %}{% trans "Direct Partner - Subscriptions" %}{% else %}{% trans "Distributor - Subscriptions" %}{% endif %}{% endblock %}
{% block main-content %}
    <div id="api_message" role="alert" style="margin-left:20%;margin-right: 20%; margin-top: 10px; margin-bottom: 10px;display: none">
        <span class="glyphicon glyphicon-certificate"></span>
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">
            <span aria-hidden="true">×</span>
        </button>
        <span></span>
    </div>
    <div class="container-fluid">
        {% trans "Subscriptions overview" as page_title %}
        {% trans "Track certification and software subscriptions across your organisations." as page_description %}
        {% include "partials/header_default.html" with page_title=page_title page_description=page_description page_icon="clipboard-check" %}
        <div class="row white-box tw-border">
            {% if renewals %}<h3>{% trans "Upcoming Renewals" %}</h3>{% endif %}
            <h4>{% if partner_view %}{{ partner.partner.name }}{% else %}{{ distributor.distributor.name }}{% endif %}</h4>
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade active in" id="add" role="tabpanel" aria-labelledby="add-tab">
                    <div class="row">
                        <div class="col-lg-12">
                            <div>
                                {% blocktrans trimmed %}
                                    This is an overview of all your CyberSmart product subscriptions. All Certification and Software subscriptions are shown below by Organisation.
                                {% endblocktrans %}
                            </div>
                            <h5 test-id="vat_info">{% trans "Note: All prices exclude VAT" %}</h5>
                            {% if not is_prod %}
                             <div class="pull-right">
                                <br />
                                <strong class='text-danger'>{% trans "FOR TESTING ONLY" %}</strong>
                                <button class="btn btn-primary" type="button" id="update-quntities-button">
                                    {% trans "Chargebee: Update quantities & prices" %}
                                </button>
                                <br />
                            </div>

                            {% endif %}
                            </div>
                            <div class="content-box">
                                <div class="search-box">
                                    {% if not partner_view %}
                                        <div class="pull-right">
                                            <div class="btn-group">
                                                <button style="width: 160px" type="button" class="btn btn-default dropdown-toggle m-r-10 m-l-20" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-toggle="tooltip" title="{% trans 'This will generate a new downloadable CSV file containing the information currently shown in this dashboard. Please note that this may take a few minutes before it can be downloaded..' %}">
                                                    <span class="btn-label"><i class="fa fa-download"></i></span>
                                                    {% trans "Download File" %}
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right">
                                                    <li class="dropdown-header">{% trans "Choose Format" %}</li>
                                                    <li><a href="{% url 'distributors:billing-file' %}" class="download_billing_csv">{% trans "CSV" %}</a></li>
                                                    <li><a href="{% url 'distributors:billing-file' %}" format="xlsx_link" class="download_billing_csv">{% trans "XLSX" %}</a></li>
                                                </ul>
                                            </div>
                                            <button id="regenerate_billing_csv" href="{{ pagination_url }}?regenerate_csv=1" class="btn has-spinner btn-default" type="button" data-toggle="tooltip" title="{% trans 'Regenerate CSV file' %}">
                                                <span class="btn-label"><i class="fa fa-refresh"></i></span>
                                                {% trans 'Regenerate CSV file' %}
                                            </button>
                                        </div>
                                    {% endif %}
                                    <div class="pull-left">
                                        {% trans "Search for organisation name" as search_help_text %}
                                        {% include "partials/search.html" with help_icon=help_icon search_button_style="width: 100px; height: 36px" search_help_text=search_help_text %}
                                    </div>
                                </div>
                                <br />
                                <div class="col-lg-12 m-t-20" id="accordions-container">
                                    {% include 'partners/subscriptions-overview/billing-accordion-body.html' with organisations=organisations %}
                                </div>
                                <div class="clear-fix"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock main-content %}
{% block extra-js %}
    <script type="text/javascript">
        $(document).ready(function () {
            {% if not is_prod %}
            $("#update-quntities-button").on("click", function() {
                $.ajax({
                url: '{% url "distributors:update-quantities" %}',
                method: 'POST',
                data: {
                    'csrfmiddlewaretoken': '{{ csrf_token }}',
                },
                success: function(data) {
                    swal({ title: gettext("Success!"), text: gettext("Please give it a minute"), type: "success", html: true });
                },
                error: function(xhr,errmsg,err) {
                    swal({ title: gettext("Error!"), text: gettext("Please try again!"), type: "error", html: true });
                }
                });
            });
            {% endif %}

            const message = document.getElementById('api_message');

            // PO number creation
            $(".add-po-form").submit(function (e){
                e.preventDefault();
                let formData = new FormData(this);
                $.ajax({
                    url: "{% url 'api-v2:purchase-order' %}",
                    type: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function () {
                        const close = document.getElementById('close-po-modal-' + formData.get('subscription'));
                        close.click()
                        message.style["display"] = "block";
                        message.className = "alert alert-success success alert-dismissable text-center";
                        message.getElementsByTagName('span')[2].textContent = "PO number successfully set";
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                        setTimeout(() => { location.reload(); }, 1000);
                    },
                    error: function (response) {
                        alert(response);
                    }
                });
            })

            const regenerate_billing_csv = document.getElementById('regenerate_billing_csv');
            const download_billing_csv = document.getElementsByClassName("download_billing_csv");

            if (download_billing_csv) {
                regenerate_billing_csv && regenerate_billing_csv.addEventListener('click', function(event) {
                    event.preventDefault();
                    let url = "/distributors/billing-file/";
                    fetch(url, {
                        method: "POST",
                        headers: {
                            'Content-Type': 'application/json',
                            "X-CSRFToken": '{{ csrf_token }}',
                        },
                        body: JSON.stringify({
                            'distributor_id': '{{ distributor.distributor.id }}',
                            'organisation_ids': '{{ organisation_ids }}'
                        })
                    }).then(response => {
                        if (response.ok) {
                            return response.json()
                        } else {
                            throw response
                        }
                    }).then(data => {
                        message.style["display"] = "block";
                        if (data.error) {
                            message.className = "alert alert-warning warning alert-dismissable text-center";
                            message.getElementsByTagName('span')[2].textContent = data.error;
                        } else {
                            message.className = "alert alert-info info alert-dismissable text-center";
                            message.getElementsByTagName('span')[2].textContent = data.message;
                        }
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    }).catch(error =>{
                        console.log(error)
                    });
                });

                Object.values(download_billing_csv).forEach( link => {
                    link.addEventListener('click', function(event) {
                        event.preventDefault();
                        let url = event.target.getAttribute('href') + "?distributor_id={{ distributor.distributor.id }}";
                        let format = event.target.getAttribute('format');
                        if (format) {
                            url += "&format=" + format;
                        }
                        fetch(url, {
                            method: "GET",
                            headers: {
                                'Content-Type': 'application/json',
                                "X-CSRFToken": '{{ csrf_token }}',
                            },
                        }).then(response => {
                            if (response.ok) {
                                return response.json()
                            } else {
                                throw response
                            }
                        }).then(data => {
                            if (data.location) {
                                window.open(data.location, '_blank');
                            } else if (data.error) {
                                message.style["display"] = "block";
                                message.className = "alert alert-warning warning alert-dismissable text-center";
                                message.getElementsByTagName('span')[2].textContent = data.error;
                                window.scrollTo({ top: 0, behavior: 'smooth' });
                            } else {
                                message.style["display"] = "block";
                                message.className = "alert alert-info info alert-dismissable text-center";
                                message.getElementsByTagName('span')[2].textContent = data.message;
                                window.scrollTo({ top: 0, behavior: 'smooth' });
                            }
                        }).catch(error =>{
                            console.log(error)
                        });
                    });
                }
            )}
            $(".cep-request-audit").click(function (){
                $(".loading-container").show();
            });
        });
    </script>
{% endblock extra-js %}
