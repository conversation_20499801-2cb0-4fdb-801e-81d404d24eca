{% load i18n %}
{% load static %}
{% load waffle_tags %}

{% block extra-css %}
<style>
.bit-larger-modal {
    /* As modal-lg is too large */
    @media (min-width: 768px) {
        width: 700px;
    }
}
</style>
{% endblock %}


<div class="modal fade" id="request-ce-plus-audit-modal-{{ org.pk }}" tabindex="-1" role="dialog" complete-url="{% url 'salesforce:complete-cep-audit-booking' org.secure_id %}">
    <div class="modal-dialog bit-larger-modal" role="document">
        <div class="modal-content">
            {% flag 'cep-jotform-enabled' %}
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                    </button>
                </div>
                {% if org.cep_audit_date %}
                    {% include 'partners/subscriptions-overview/details/cep_audit_reschedule_jotform.html' %}
                {% else %}
                    {% include 'partners/subscriptions-overview/details/cep_audit_request_jotform.html' %}
                {% endif %}
            {% else %}
                {% include "partials/loading.html" with loading_text="Please wait" %}
                <form class="request-ce-plus-audit-form" id="request-ce-plus-audit-form-{{ org.pk }}" method="post" action="{% url 'partners:request-ce-plus-audit' org.secure_id %}">
                    {% csrf_token %}
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                        </button>
                        <h4 class="modal-title text-center">
                            {% trans "Please check this information is correct" %}
                        </h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="m-b-15 font-bold">
                                {% trans "Does the business requiring an audit have a valid Cyber Essentials certificate, awarded within the last 90 days?" %}
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-control" name="valid_ce_certificate">
                                <option {% if not org.ce_certification.free_of_charge %}selected{% endif %}>{% trans "Yes, from CyberSmart" %}</option>
                                <option {% if org.ce_certification.free_of_charge %}selected{% endif %}>{% trans "Yes, from another provider" %}</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="font-bold">
                                {% trans "Organisation Name" %}
                            </label>
                            <input name="organisation_name" type="text" class="form-control" value="{{ org.name }}" placeholder="Organisation Name" maxlength="255" disabled>
                            <input name="organisation_name" type="hidden" class="form-control" value="{{ org.name }}" placeholder="Organisation Name" maxlength="255">
                        </div>
                        <div class="form-group">
                            <label class="font-bold">
                                {% trans "Date your Cyber Essentials was achieved" %}
                            </label>
                            <input name="ce_date_achieved" class="form-control" type="date" value="{{ org.ce_certification.certified_date|date:"Y-m-d" }}"/>
                        </div>
                        <div class="form-group">
                            <label class="font-bold">{% trans "Name" %}</label>
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <input name="first_name" type="text" class="form-control" value="{{ user.first_name }}" placeholder="First Name" maxlength="255">
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <input name="last_name" type="text" class="form-control" value="{{ user.last_name }}" placeholder="Last Name" maxlength="255">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="font-bold">{% trans "Email" %}</label>
                            <input name="email" type="email" class="form-control" value="{{ user.email }}" placeholder="Email" maxlength="255">
                        </div>
                        <div class="form-group">
                            <label class="font-bold">{% trans "Desktop Devices in Scope" %}</label>
                            <textarea name="desktop_devices_in_scope" class="form-control" placeholder="List all your desktop devices">{{ org.ce_desktop_devices_quantities|striptags|escape }}</textarea>
                            <small class="text-muted">{% trans "You must enter the number of devices in scope, their operating systems and build version (e.g. 12x Windows 10 21H2)" %}</small>
                        </div>
                        <div class="form-group">
                            <label class="font-bold">{% trans "Mobile Devices in Scope" %}</label>
                            <textarea name="mobile_devices_in_scope" class="form-control" placeholder="List all your mobile devices">{{ org.ce_mobile_devices_quantities|striptags|escape }}</textarea>
                            <small class="text-muted">{% trans "You must enter the number of mobile devices in scope (e.g. 2x iPhone13)" %}</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary align-left cep-request-audit" type="submit" form="request-ce-plus-audit-form-{{ org.pk }}">
                            {% flag 'cep-jotform-enabled' %}
                                {% trans 'Book Audit' %}
                            {% else %}
                                {% trans 'Request Audit' %}
                            {% endflag %}
                        </button>
                        <button type="button" class="btn btn-default waves-effect text-left" data-dismiss="modal">
                            {% trans "Cancel" %}
                        </button>
                    </div>
                </form>
            {% endflag %}
        </div>
    </div>
</div>

<script>
    const IFRAME_MIN_SIZE = document.documentElement.clientHeight * 0.9;
    $(document).ready(function () {
        var ifrs = $(".jot-form-iframe");
        let formSubmitted = false;


        ifrs.each(function (_, ifr) {
            var ifr$ = $(ifr)
            ifr.style.height = '90vh';
            $('#request-ce-plus-audit-modal-{{ org.pk }}').on('shown.bs.modal', function (e) {
                if (ifr) {
                    var src = ifr.src;
                    var iframeParams = [];
                    if (window.location.href && window.location.href.indexOf("?") > -1) {
                        iframeParams = iframeParams.concat(window.location.href.substr(window.location.href.indexOf("?") + 1).split('&'));
                    }
                    if (src && src.indexOf("?") > -1) {
                        iframeParams = iframeParams.concat(src.substr(src.indexOf("?") + 1).split("&"));
                        src = src.substr(0, src.indexOf("?"))
                    }
                    iframeParams.push("isIframeEmbed=1");
                    ifr.src = src + "?" + iframeParams.join('&');
                }
                window.handleIFrameMessage = function (e) {
                    if (typeof e.data === 'object') {
                        return;
                    }
                    var args = e.data.split(":");
                    if (!ifr) {
                        return;
                    }
                    switch (args[0]) {
                        case "scrollIntoView":
                            ifr.scrollIntoView();
                            break;
                        case "setHeight":
                            if (!isNaN(args[1])) {
                                // Because the jotform modal does not send setHeight on URL redirect to Calendly selector,
                                // we are setting minimum height to the one that will fit the Calendly calendar.
                                const newHeight = Math.max(parseInt(args[1]), IFRAME_MIN_SIZE);
                                ifr.style.height = newHeight + "px";
                            }
                            break;
                        case "collapseErrorPage":
                            if (ifr.clientHeight > window.innerHeight) {
                                ifr.style.height = window.innerHeight + "px";
                            }
                            break;
                        case "reloadPage":
                            window.location.reload();
                            break;
                        case "loadScript":
                            if (!window.isPermitted(e.origin, ['jotform.com', 'jotform.pro'])) {
                                break;
                            }
                            var src = args[1];
                            if (args.length > 3) {
                                src = args[1] + ':' + args[2];
                            }
                            var script = document.createElement('script');
                            script.src = src;
                            script.type = 'text/javascript';
                            document.body.appendChild(script);
                            break;
                        case "exitFullscreen":
                            if (window.document.exitFullscreen) window.document.exitFullscreen();
                            else if (window.document.mozCancelFullScreen) window.document.mozCancelFullScreen();
                            else if (window.document.webkitExitFullscreen) window.document.webkitExitFullscreen();
                            else if (window.document.msExitFullscreen) window.document.msExitFullscreen();
                            break;
                    }
                    var isJotForm = (e.origin.indexOf("jotform") > -1) ? true : false;
                    if (isJotForm && "contentWindow" in ifr && "postMessage" in ifr.contentWindow) {
                        var urls = {
                            "docurl": encodeURIComponent(document.URL),
                            "referrer": encodeURIComponent(document.referrer)
                        };
                        ifr.contentWindow.postMessage(JSON.stringify({"type": "urls", "value": urls}), "*");
                    }
                };
                window.isPermitted = function (originUrl, whitelisted_domains) {
                    var url = document.createElement('a');
                    url.href = originUrl;
                    var hostname = url.hostname;
                    var result = false;
                    if (typeof hostname !== 'undefined') {
                        whitelisted_domains.forEach(function (element) {
                            if (hostname.slice((-1 * element.length - 1)) === '.'.concat(element) || hostname === element) {
                                result = true;
                            }
                        });
                        return result;
                    }
                };
                if (window.addEventListener) {
                    window.addEventListener("message", handleIFrameMessage, false);
                } else if (window.attachEvent) {
                    window.attachEvent("onmessage", handleIFrameMessage);
                }
            })

            {% flag 'cep-jotform-enabled' %}
                // Listen for messages from JotForm to check when the form was submitted
                window.addEventListener("message", function(event) {
                    if (!event.origin.includes("submit.jotform.com")) {
                        return;
                    }

                    const data = event.data;
                    if (data.action === "submission-completed" && data.formID === "{{ CEP_JOTFORM_ID }}" && !formSubmitted) {
                        formSubmitted = true;
                        onJotFormSubmitted();
                    }
                });

                function onJotFormSubmitted() {
                    let completeURL = $("#request-ce-plus-audit-modal-{{ org.pk }}").attr("complete-url");

                    if (!completeURL) {
                        return
                    }

                    // Show loading spinner before ajax call
                    $("#base-loading-partial").css("display", "block");

                    // start the audit booking task
                    function startAuditBooking() {
                        fetch(completeURL, {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRFToken": "{{ csrf_token }}"
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.task_id) {
                                pollAuditBookingStatus(data.task_id);
                            }
                        });
                    }

                    startAuditBooking();

                    // poll the audit booking status
                    function pollAuditBookingStatus(taskId) {
                        const interval = [5000, 10000, 20000, 40000, 60000];  // Retry intervals in milliseconds
                        let attempt = 0;

                        function checkStatus() {
                            if (attempt >= interval.length) {
                                swal("Error", "Failed to request a Cyber Essentials Plus audit after multiple attempts.", "error");
                                return;
                            }

                            fetch(completeURL + "?task_id=" + taskId, {
                                method: "GET",
                                headers: {
                                    "X-CSRFToken": getCookie("csrftoken")
                                }
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Remove loading spinner after ajax call is complete
                                    $("#base-loading-partial").css("display", "none");

                                    if (data.status === "BOOKED" || data.status === "RESCHEDULED") {
                                        if (data.status === "BOOKED") {
                                            swal("Success", "Your Cyber Essentials Plus audit has been successfully booked.", "success");
                                        } else {
                                            swal("Success", "Your Cyber Essentials Plus audit has been successfully rescheduled.", "success");
                                        }
                                    } else if (data.status === "FAILED") {
                                        swal("Error", "Failed to request a Cyber Essentials Plus audit.", "error");
                                    }

                                    location.reload();
                                } else {
                                    if (data.status === "PENDING") {
                                        setTimeout(checkStatus, interval[attempt]);
                                        attempt++;
                                    } else {
                                        swal("Error", "An error occurred while requesting the Cyber Essentials Plus audit.", "error");
                                        location.reload();
                                    }
                                }
                            });
                        }
                        checkStatus();
                    }
                }
            {% endflag %}
        })
        {% flag 'cep-jotform-enabled' %}
        window.addEventListener('beforeunload', function (e) {
            const event = e || window.event;
            if (!$('#request-ce-plus-audit-modal-{{ org.pk }}').hasClass('in')) {
                return;
            }
            event.preventDefault();
            
            event.returnValue = true
            return true;
        });
        {% endflag %}
    });
</script>