{% load static %}
{% load i18n %}
{% load l10n %}

{% if can_download_csv %}
<div class="tw-flex tw-flex-row tw-justify-between tw-gap-2">
    <div id="csv_download_message" role="alert" style="display: none">
        <span class="glyphicon glyphicon-certificate"></span>
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">
            <span aria-hidden="true">×</span>
        </button>
        <span>{% trans "Processing file" %}</span>
    </div>

    <div class="tw-flex tw-gap-2 tw-mt-2">
        <div class="btn-group">
            <button type="button" class="tw-min-h-10 tw-flex tw-text-nowrap tw-justify-center tw-items-center tw-gap-1 tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-toggle="tooltip" title="{% trans 'This will download the most recently generated file.' %}">
                {% trans "Download file" %}
                <svg class="tw-w-[18px] tw-h-[18px]">
                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-chevron-down"></use>
                </svg>
            </button>
            <ul class="dropdown-menu dropdown-menu-right">
                <li class="dropdown-header">{% trans "Choose Format" %}</li>
                <li><a class="csv_download_link tw-cursor-pointer">{% trans "Download CSV" %}</a></li>
                <li><a format="xlsx_link" class="csv_download_link tw-cursor-pointer">{% trans "Download XLSX" %}</a></li>
            </ul>
        </div>
        <button id="csv_regenerate_button" class="tw-min-h-10 tw-flex tw-text-nowrap tw-justify-center tw-items-center tw-gap-1 tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0" type="button" data-toggle="tooltip" title="{% trans 'This will generate a new downloadable file containing the information currently shown in this dashboard. Please note that this may take a few minutes before it can be downloaded..' %}">
            <svg class="tw-w-[18px] tw-h-[18px]">
                <use href="{% static 'icons/icons-sprite.svg' %}#icon-refresh-cw"></use>
            </svg>
            {% trans 'Regenerate file' %}
        </button>
    </div>
</div>
{% endif %}

{% block extra-js %}
<script type="text/javascript">
    $(document).ready(function() {
        const csv_regenerate_button = document.getElementById('csv_regenerate_button');
        const csv_download_links = document.getElementsByClassName('csv_download_link');
        const csv_download_message = document.getElementById('csv_download_message');

        csv_regenerate_button && csv_regenerate_button.addEventListener('click', function(event) {
            event.preventDefault();
            let url = '{{ api_endpoint }}';
            let main_entity = '{{ main_entity_name }}' + '_id';
            fetch(url, {
                method: "POST",
                headers: {
                    'Content-Type': 'application/json',
                    "X-CSRFToken": '{{ csrf_token }}',
                },
                body: JSON.stringify({
                    [main_entity]: '{{ main_entity.id }}',
                    'device_ids': '{{ device_ids }}'
                })
            }).then(response => {
                if (response.ok) {
                    return response.json()
                } else {
                    throw response
                }
            }).then(data => {
                csv_download_message.style["display"] = "block";
                if (data.error) {
                    csv_download_message.className = "alert alert-warning warning alert-dismissable text-center";
                    csv_download_message.getElementsByTagName('span')[2].textContent = data.error;
                } else {
                    csv_download_message.className = "alert alert-info info alert-dismissable text-center";
                    csv_download_message.getElementsByTagName('span')[2].textContent = data.message;
                }
            }).catch(error =>{
                console.log(error)
            });
        });

        Object.values(csv_download_links).forEach( link => {
            link.addEventListener('click', function(event) {
                event.preventDefault();
                let format = event.target.getAttribute('format');
                let url = '{{ api_endpoint }}' + "?"+ '{{ main_entity_name }}' + "_id={{ main_entity.id }}";
                // add a format to url if not empty
                if (format) {
                    url += "&format=" + format;
                }
                fetch(url, {
                    method: "GET",
                    headers: {
                        'Content-Type': 'application/json',
                        "X-CSRFToken": '{{ csrf_token }}',
                    },
                }).then(response => {
                    if (response.ok) {
                        return response.json()
                    } else {
                        throw response
                    }
                }).then(data => {
                    if (data.location) {
                        window.open(data.location, '_blank');
                    } else if (data.error) {
                        csv_download_message.style["display"] = "block";
                        csv_download_message.className = "alert alert-warning warning alert-dismissable text-center";
                        csv_download_message.getElementsByTagName('span')[2].textContent = data.error;
                    } else {
                        csv_download_message.style["display"] = "block";
                        csv_download_message.className = "alert alert-info info alert-dismissable text-center";
                        csv_download_message.getElementsByTagName('span')[2].textContent = data.message;
                    }
                }).catch(error =>{
                    console.log(error)
                });
            });
        });

    })
</script>
{% endblock %}