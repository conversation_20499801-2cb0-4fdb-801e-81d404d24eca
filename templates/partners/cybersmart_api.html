{% extends 'base.html' %}

{% load static %}
{% load i18n %}

{% block head_title %}{% trans "CyberSmart API" %}{% endblock %}
{% block main-content %}
    <div class="container-fluid">
        {% trans 'Settings' as page_title %}
        {% trans 'Manage notifications, partner users, and branding.' as page_description %}
        {% include "partials/header_default.html" with page_title=page_title page_description=page_description page_icon="settings-2" %}
        {% include "partners/security_control/settings_page_switch.html" with cybersmart_api=True %}
        <div class="row white-box">
            <h3>{% trans "CyberSmart API" %}</h3>
                {% blocktranslate %}
                <p>CyberSmart API for partners and distributors.</p>
                <p>In order to use the CyberSmart API endpoints you need to have the OAuth2 <b>client_id</b> and <b>client_secret</b>.</p>
                {% endblocktranslate %}
                {% trans "Once you have the " %}<b>client_id</b> {% trans "and" %} <b>client_secret</b>, {% trans "you can use them, for more details on how please see the" %} <a target="_blank" href="{% url 'partner-swagger-ui' %}">{% trans "documentation" %}</a> .<br/>
            {% if oauth_app %}
                <div id="existing_client_id_and_secret">
                    <p class="m-t-10">
                    <div class="form-group">
                        <label>Client ID</label>
                        <div class="input-group">
                            <span class="input-group-addon copy-to-clipboard">
                                <i class="fa fa-copy"></i>
                            </span>
                            <input type="text" class="form-control" value="{{ oauth_app.client_id }}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>{% trans "Client Secret" %}</label>
                        <div class="input-group">
                            <span class="input-group-addon copy-to-clipboard">
                                <i class="fa fa-lock"></i>
                            </span>
                            <input type="text" class="form-control" value="{% trans 'Hashed' %}" disabled>
                        </div>
                    </div>
                    <p class="text-danger">
                        {% blocktranslate %}
                            For security reasons, the client secret cannot be displayed again.<br/>
                            If you lost it, you can regenerate it by clicking the "Regenerate" button below.<br/>
                            This will invalidate the current client secret.
                        {% endblocktranslate %}
                    </p>
                </div>
            {% endif %}
            {% trans 'Regenerate' as regenerate_text %}
            {% trans 'Generate' as generate_text %}
            <p class="m-t-10">
                <button id="generate-oauth-app" class="btn btn-primary">{% if oauth_app %}{{ regenerate_text }}{% else %}{{ generate_text }}{% endif %}</button>
            <div class="row m-t-20" id="client_id_and_secret" style="display: none">
                <div class="col-lg-6 col-md-8 col-sm-12">
                    <p class="text-danger">
                        {% blocktranslate %}
                            <b>Important Notice:</b>
                            Your Client Secret has been generated and is displayed below.
                            This is the only time it will be available for you to copy.
                            For security reasons, the Client Secret will not be displayed again.
                            If you lose this information, you will need to regenerate credentials again, which will invalidate the current one.
                            Please copy and securely store it now.
                        {% endblocktranslate %}
                    </p>
                    <div class="form-group">
                        <label for="client_id">{% trans "Client ID" %}</label>
                        <div class="input-group">
                            <span class="input-group-addon copy-to-clipboard">
                                <i class="fa fa-copy"></i>
                            </span>
                            <input type="text" class="form-control" id="client_id">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="client_secret">{% trans "Client Secret" %}</label>
                        <div class="input-group">
                            <span class="input-group-addon copy-to-clipboard">
                                <i class="fa fa-copy"></i>
                            </span>
                            <input type="text" class="form-control" id="client_secret">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block extra-js %}
    <script type="application/javascript">
        $(document).ready(function (){
            let generateButton = $("#generate-oauth-app");
            generateButton.click(function(){
                swal({
                    title: gettext("Generate API Credentials"),
                    text: gettext("Copy the client_id and client_secret and store them securely"),
                    type: "info",
                    showCancelButton: false,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: gettext("Ok, got it!"),
                    closeOnConfirm: true
                }, function(){
                    generateButton.prop("disabled", true);
                    $.ajax({
                        url: ".",
                        method: "POST",
                    }).done(function(response) {
                        $("#client_id").val(response.client_id);
                        $("#client_secret").val(response.client_secret);
                        $("#client_id_and_secret").show();
                        generateButton.hide();
                        $("#existing_client_id_and_secret").hide();
                        
                    }).error(function (response) {
                        swal(gettext("Something went wrong"), gettext("Cannot generate OAuth2 App"), "error");
                        generateButton.prop("disabled", false);
                        window.location.reload();
                    })
                });
                return false;
            });
            $(".copy-to-clipboard").click(function(){
                const input = $(this).siblings("input");
                input.select();
            
                try {
                    const successful = document.execCommand("copy");
                    if (successful) {
                        swal(gettext("Copied!"), gettext("The value has been copied to your clipboard"), "success");
                    } else {
                        swal(gettext("Copy failed!"), gettext("There was an error copying the value"), "error");
                    }
                } catch (err) {
                    console.error("Fallback: Oops, unable to copy", err);
                    swal(gettext("Copy failed!"), gettext("There was an error copying the value"), "error");
                }
                input.blur();
            });
        })
    </script>
{% endblock extra-js %}