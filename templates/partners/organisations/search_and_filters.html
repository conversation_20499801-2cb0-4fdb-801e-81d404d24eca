{% load i18n %}
{% load waffle_tags %}
{% load beta_feature_enrolment %}
<div class="form-inline padding-bottom-15">
    {% if not distributor_viewing_partner_page %}
        <div class="row m-0 m-b-20">
            <div class="pull-right">
                {% if is_partner and has_insurer_distributor %}
                    {% switch "ENABLE_AVIVA_DASHBOARD" %}
                        <div class="table-bordered col-md-7 col-md-offset-3 text-center" style="border-color:grey;">
                            {% trans "Copy the unique link below into your email to invite customer to sign up for the CyberSmart solution." %}
                            <br />
                            <span style="color:black;">
                                {{request.user.partner.partner.get_org_signup_url}}
                            </span>
                        </div>
                    {% endswitch %}
                {% endif %}
                {% if not has_insurer_distributor %}
                    {% if is_partner or request.user.is_staff %}
                        <div id="partner_orgs_file_message" role="alert" style="margin-top: 10px; margin-bottom: 10px !important;display: none">
                            <span class="glyphicon glyphicon-certificate"></span>
                            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">
                                <span aria-hidden="true">×</span>
                            </button>
                            <span>{% trans "Processing file" %}</span>
                        </div>
                        <div class="">
                            {% if organisation_ids %}
                                <div class="pull-right">
                                    <div class="btn-group">
                                        <button style="width: 160px" type="button" class="btn btn-default dropdown-toggle m-r-10 m-l-20" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-toggle="tooltip" title="{% trans 'This will download the most recently generated file.' %}">
                                            <span class="btn-label"><i class="fa fa-download"></i></span>
                                            {% trans "Download File" %}
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-right">
                                            <li class="dropdown-header">{% trans "Choose Format" %}</li>
                                            <li><a href="{% url 'partners:organisations-csv-report' %}" class="download_partner_orgs_csv">{% trans "CSV" %}</a></li>
                                            <li><a href="{% url 'partners:organisations-csv-report' %}" format="xlsx_link" class="download_partner_orgs_csv">{% trans "XLSX" %}</a></li>
                                        </ul>
                                    </div>
                                    <button id="regenerate_partner_orgs_csv" href="{{ pagination_url }}?regenerate_csv=1" class="btn has-spinner btn-default" type="button" data-toggle="tooltip" title="{% trans 'This will generate a new downloadable CSV file containing the information currently shown in this dashboard. Please note that this may take a few minutes before it can be downloaded..' %}">
                                        <span class="btn-label"><i class="fa fa-refresh"></i></span>
                                        {% trans 'Regenerate CSV file' %}
                                    </button>
                                </div>
                            {% endif %}
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    {% endif %}
    <div class="pull-left">
        <div class="m-b-20">
            {% trans "Search for organisation name" as search_help_text %}
            {% include "partials/search.html" with help_icon=help_icon search_button_style="width: 100px; height: 36px" search_help_text=search_help_text %}
        </div>
    </div>
    
    <div class="pull-right">
        <div class="pull-left org-filter-block">
            <a data-filter="all" href="{{ organisations_url }}?filter=all{% if param_ordering %}&order_by={{ param_ordering }}{% endif %}{% if param_search %}&search={{ param_search }}{% endif %}" class="btn btn-default check-report-device-type {% if param_filtering == "all" or not param_filtering %}active-filter{% endif %}" type="button">
                {% trans 'All' %}
            </a>
            <a data-filter="security_issues" href="{{ organisations_url }}?filter=security_issues{% if param_ordering %}&order_by={{ param_ordering }}{% endif %}{% if param_search %}&search={{ param_search }}{% endif %}" class="btn btn-default check-report-device-type {% if param_filtering == "security_issues" %}active-filter{% endif %}" type="button">
                {% trans 'Security Issues' %}
            </a>
            <a data-filter="trial_account" href="{{ organisations_url }}?filter=trial_account{% if param_ordering %}&order_by={{ param_ordering }}{% endif %}{% if param_search %}&search={{ param_search }}{% endif %}" class="btn btn-default check-report-device-type {% if param_filtering == "trial_account" %}active-filter{% endif %}" type="button">
                {% trans 'Trial Account' %}
            </a>

            {# Beta features #}
            {% if has_any_enrolled_beta_features %}
                {% for beta_feature in enrolled_beta_features %}
                    <a
                        data-filter="{{ beta_feature.kind }}"
                        href="{{ organisations_url }}?filter={{ beta_feature.kind }}{% if param_ordering %}&order_by={{ param_ordering }}{% endif %}{% if param_search %}&search={{ param_search }}{% endif %}"
                        class="btn btn-default check-report-device-type {% if param_filtering == beta_feature.kind  %}active-filter{% endif %}"
                        type="button"
                    >
                        {{ beta_feature.get_kind_display }}
                    </a>
                {% endfor %}
            {% endif %}

            {% if ps.is_certifications_enabled and not IS_EU_GEO %}
                <a data-filter="expiring_soon" href="{{ organisations_url }}?filter=expiring_soon{% if param_ordering %}&order_by={{ param_ordering }}{% endif %}{% if param_search %}&search={{ param_search }}{% endif %}" class="btn btn-default check-report-device-type {% if param_filtering == "expiring_soon" %}active-filter{% endif %}" type="button">
                   {{ CERTIFICATES_LABEL }} {% trans 'Expiring' %}
                </a>
            {% endif %}
        </div>
    </div>
    <div class="clearfix"></div>
</div>
{% block extra-js %}
<script type="text/javascript">
    $(document).ready(function() {
        const regenerate_partner_orgs_csv = document.getElementById('regenerate_partner_orgs_csv');
        const download_partner_orgs_csv = document.getElementsByClassName("download_partner_orgs_csv");
        const partner_orgs_file_message = document.getElementById('partner_orgs_file_message');

        regenerate_partner_orgs_csv.addEventListener('click', function(event) {
            event.preventDefault();
            let url = "/partners/organisations-csv-report/";
            fetch(url, {
                method: "POST",
                headers: {
                    'Content-Type': 'application/json',
                    "X-CSRFToken": '{{ csrf_token }}',
                },
                body: JSON.stringify({
                    'partner_id': '{{ request.user.partner.partner.id }}',
                    'organisation_ids': '{{ organisation_ids }}'
                })
            }).then(response => {
                if (response.ok) {
                    return response.json()
                } else {
                    throw response
                }
            }).then(data => {
                partner_orgs_file_message.style["display"] = "block";
                if (data.error) {
                    partner_orgs_file_message.className = "alert alert-warning warning alert-dismissable text-center";
                    partner_orgs_file_message.getElementsByTagName('span')[2].textContent = data.error;
                } else {
                    partner_orgs_file_message.className = "alert alert-info info alert-dismissable text-center";
                    partner_orgs_file_message.getElementsByTagName('span')[2].textContent = data.message;
                }
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }).catch(error =>{
                console.log(error)
            });
        });

        Object.values(download_partner_orgs_csv).forEach(link => {
            link.addEventListener('click', function(event) {
                event.preventDefault();
                let url = event.target.getAttribute('href') + "?partner_id={{ request.user.partner.partner.id }}";
                let format = event.target.getAttribute('format');
                if (format) {
                    url += "&format=" + format;
                }
                fetch(url, {
                    method: "GET",
                    headers: {
                        'Content-Type': 'application/json',
                        "X-CSRFToken": '{{ csrf_token }}',
                    },
                }).then(response => {
                    if (response.ok) {
                        return response.json()
                    } else {
                        throw response
                    }
                }).then(data => {
                    if (data.location) {
                        window.open(data.location, '_blank');
                    } else if (data.error) {
                        partner_orgs_file_message.style["display"] = "block";
                        partner_orgs_file_message.className = "alert alert-warning warning alert-dismissable text-center";
                        partner_orgs_file_message.getElementsByTagName('span')[2].textContent = data.error;
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    } else {
                        partner_orgs_file_message.style["display"] = "block";
                        partner_orgs_file_message.className = "alert alert-info info alert-dismissable text-center";
                        partner_orgs_file_message.getElementsByTagName('span')[2].textContent = data.message;
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    }
                }).catch(error =>{
                    console.log(error)
                });
            });
        });

    })
</script>
{% endblock %}
