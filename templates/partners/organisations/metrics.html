{% load i18n %}
{% load static %}
{% load divide_tags %}

<div class="tw-flex tw-flex-col lg:tw-flex-row tw-basis-full tw-justify-between tw-gap-3 tw-mb-3">
    <!-- Orgs enrolled -->
    {% trans "Organisations enrolled" as widget_title %}
    {% include "partials/widget_metric.html" with widget_id="widgetOrgsEnrolled" widget_title=widget_title widget_metric=analytic.organisations_count %}
    <!-- Orgs secured -->
    {% trans "Organisations secure" as widget_title %}
    {% include "partials/widget_metric.html" with widget_id="widgetOrgsSecure" widget_title=widget_title widget_metric=analytic.secure_organisations_count %}
    <!-- Orgs certified -->
    {% if ps.is_certifications_enabled and not IS_EU_GEO %}
        {% trans 'Organisations certified' as widget_title %}
        {% include "partials/widget_metric.html" with widget_id="widgetOrgsCertified" widget_title=widget_title widget_metric=analytic.certified_organisations_count %}
    {% endif %}
    <!-- Orgs with issues -->
    <div id="{{ widgetOrgsInsecure }}" class="tw-flex tw-basis-1/2 lg:tw-basis-full tw-flex-col tw-bg-white tw-rounded-lg tw-p-4 tw-border tw-border-grey-800 tw-relative">
        <div class="tw-flex tw-flex-col tw-flex-1">
            <div class="tw-flex tw-flex-row tw-mb-2">
                <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">
                    {% trans "Organisations with issues" %}
                </div>
                
                {% if analytic.insecure_organisations_count >= 1 %}
                    <svg class="tw-w-[24px] tw-h-[24px] tw-fill-red-600">
                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-circle-alert"></use>
                    </svg>
                {% endif %}
            </div>
            <div class="tw-font-sans tw-mt-auto">
                <span class="tw-text-4xl tw-font-semibold">{{ analytic.insecure_organisations_count }}</span>
            </div>
        </div>
    </div>
    <!-- Certs expiring -->
    {% if ps.is_certifications_enabled and not IS_EU_GEO %}
        {% trans 'Certificates expiring soon' as widget_title %}
        {% include "partials/widget_metric.html" with widget_title=widget_title widget_metric=analytic.expiring_soon_organisations_count %}
    {% endif %}
</div>
