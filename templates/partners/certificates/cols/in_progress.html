{% load i18n %}
{% load static %}
<div class="col-lg-4 p-t-20">
    <div class="col-lg-10 col-lg-offset-1">
    {% include "partners/certificates/headers/sorting.html" with container=".in-progress-certs-container" cert_element=".in-progress-cert" %}
        <div class="in-progress-certs-container">
            {% for cert in in_progress_certs %}
                <div class="organisation in-progress-cert certifications {% if not cert.user_has_access %}no-access{% endif %}" data-date-created="{{ cert.created.date.isoformat }}">
                    <div class="content">
                        <div class="organisation-name">
                            <a href="{{ cert.url }}">
                                <span {% if cert.organisation.name|length >= 40 %}data-toggle="tooltip" data-placement="top" title="{{ cert.organisation.name }}"{% endif %} aria-hidden="true">
                                    <div class="collapsed-text" style="text-align: left; padding-left: 5px">
                                        {{ cert.organisation.name }}
                                    </div>
                                </span>
                            </a>
                        </div>
                        <div class="organisation-certification">
                            <div class="">
                                {% if cert.type == CYBER_ESSENTIALS %}
                                    <img class="icon icon-title cert-icon" alt="CyberEssentials" src="{% static 'icons/page-title/CyberEssentials.svg' %}">
                                {% elif cert.type == CYBER_ESSENTIALS_PLUS %}
                                    <img class="icon icon-title cert-icon" alt="CyberEssentialsPlus" src="{% static 'icons/page-title/CyberEssentialsPlus.svg' %}">
                                {% elif cert.type == GDPR %}
                                    <img class="icon icon-title cert-icon" alt="GDPR" src="{% static 'icons/page-title/GDPR.svg' %}">
                                {% elif cert.type == CYBERSMART_COMPLETE %}
                                    <img class="icon icon-title cert-icon" alt="CSComplete" src="{% static 'icons/page-title/Complete-temp.svg' %}">
                                {% else %}
                                    <i class="fa fa-trophy" aria-hidden="true"></i>
                                {% endif %}
                                {{ cert.full_title }}
                            </div>
                            <div class="m-b-10">
                                <i class="fa fa-calendar" aria-hidden="true"></i>
                                {% trans "Created:" %} <span data-toggle="tooltip" data-placement="top" title="{{ cert.created }}">{{ cert.created|timesince }} {% trans "ago" %}</span>
                            </div>
                            {% if cert.type == GDPR %}
                                <div class="m-b-10" style="margin-top: -10px">
                                    <img class="icon icon-title cert-icon m-0" style="width: 14px;height: auto;" alt="Deprecated GDPR cert" src="{% static 'icons/partners/folder-x.png' %}">
                                    <span class="text-primary">
                                        {% trans "CyberSmart no longer supports this product" %}
                                    </span>
                                </div>
                            {% endif %}
                            <div class="m-b-10">
                                <div class="progress progress-md" style="border-radius: 5px">
                                    <div class="progress-bar progress-bar-success progress-bar-striped" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width:{{ cert.progress_percentage }}%">{{ cert.progress_percentage }}%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% empty %}
                <h4 class="text-center">
                    {% blocktranslate with label=CERTIFICATIONS_LABEL|lower %}There are no {{ label }}{% endblocktranslate %}
                </h4>
            {% endfor %}
        </div>
    </div>
</div>