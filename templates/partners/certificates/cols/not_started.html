{% load i18n %}
{% load static %}
<div class="col-lg-4 p-t-20">
    <div class="col-lg-10 col-lg-offset-1">
        {% include "partners/certificates/headers/sorting.html" with container=".not-started-certs-container" cert_element=".not-started-cert" %}
        <div class="not-started-certs-container">
            {% for cert in not_started_certs %}
                <div class="organisation not-started-cert certifications {% if not cert.user_has_access %}no-access{% endif %}" data-date-created="{{ cert.created.isoformat }}">
                    <div class="content">
                        <div class="organisation-name">
                            <div class="pull-left" style="width: 70%">
                                <a {% if not cert.user_has_access %}class="no-access"{% endif %} href="{{ cert.url }}">
                                    <span {% if cert.organisation.name|length >= 40 %}data-toggle="tooltip" data-placement="top" title="{{ cert.organisation.name }}"{% endif %} aria-hidden="true">
                                        <div class="collapsed-text" style="text-align: left; padding-left: 5px">
                                            {{ cert.organisation.name }}
                                        </div>
                                    </span>
                                </a>
                            </div>
                            <div class="pull-right">
                                {% if cert.expired %}
                                    <div class="status-label {% if cert.renewed %}m-b-5{% endif %}">
                                        {% trans "Expired" %}
                                    </div>
                                {% endif %}
                                {% if cert.renewed %}
                                    <div class="status-label">
                                        {% trans "Renewed" %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="organisation-certification">
                            <div class="">
                                {% if cert.type == CYBER_ESSENTIALS %}
                                    <img class="icon icon-title cert-icon" alt="CyberEssentials" src="{% static 'icons/page-title/CyberEssentials.svg' %}">
                                {% elif cert.type == CYBER_ESSENTIALS_PLUS %}
                                    <img class="icon icon-title cert-icon" alt="CyberEssentialsPlus" src="{% static 'icons/page-title/CyberEssentialsPlus.svg' %}">
                                {% elif cert.type == GDPR %}
                                    <img class="icon icon-title cert-icon" alt="GDPR" src="{% static 'icons/page-title/GDPR.svg' %}">
                                {% elif cert.type == CYBERSMART_COMPLETE %}
                                    <img class="icon icon-title cert-icon" alt="CSComplete" src="{% static 'icons/page-title/Complete-temp.svg' %}">
                                {% else %}
                                    <i class="fa fa-trophy" aria-hidden="true"></i>
                                {% endif %}
                                {{ cert.full_title }}
                            </div>
                            <div {% if not cert.expired %}class="m-b-10"{% endif %}>
                                <i class="fa fa-calendar" aria-hidden="true"></i>
                                {% trans "Created:" %} <span data-toggle="tooltip" data-placement="top" title="{{ cert.created }}">{{ cert.created|timesince }} {% trans "ago" %}</span>
                            </div>
                            {% if cert.type == GDPR %}
                                <div {% if not cert.expired %}class="m-b-10"{% endif %} style="margin-top: -10px">
                                    <img class="icon icon-title cert-icon m-0" style="width: 14px;height: auto;" alt="Deprecated GDPR cert" src="{% static 'icons/partners/folder-x.png' %}">
                                    <span class="text-primary">
                                        {% trans "CyberSmart no longer supports this product" %}
                                    </span>
                                </div>
                            {% endif %}
                            {% if cert.expired %}
                                <div class="m-b-10 text-danger">
                                    ❌
                                    <span>{% trans "Expired on:" %} {{ cert.renewal_end_date }}</span>
                                    <br/>
                                </div>
                            {% endif %}
                            {% if not distributor_viewing_partner_page %}
                                {% include "partners/certificates/renewal_button.html" %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% empty %}
                <h4 class="text-center">
                    {% blocktranslate with label=CERTIFICATIONS_LABEL|lower %}There are no {{ label }}{% endblocktranslate %}
                </h4>
            {% endfor %}
        </div>
    </div>
</div>