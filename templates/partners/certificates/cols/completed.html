{% load i18n %}
{% load static %}
<div class="col-lg-4 p-t-20">
    <div class="col-lg-10 col-lg-offset-1">
    {% include "partners/certificates/headers/sorting.html" with container=".completed-certs-container" cert_element=".completed-cert" %}
        <div class="completed-certs-container">
            {% for cert in completed_certs %}
                <div class="organisation completed-cert certifications {% if not cert.user_has_access %}no-access{% endif %}" data-date-created="{{ cert.created.date.isoformat }}">
                    <div class="content">
                        <div class="organisation-name">
                            <div class="pull-left" style="width: 70%">
                                <a href="{{ cert.url }}">
                                    <span {% if cert.organisation.name|length >= 40 %}data-toggle="tooltip" data-placement="top" title="{{ cert.organisation.name }}"{% endif %} aria-hidden="true">
                                        <div class="collapsed-text" style="text-align: left; padding-left: 5px">
                                            {{ cert.organisation.name }}
                                        </div>
                                    </span>
                                </a>
                            </div>
                            <div class="pull-right">
                                {% if cert.expiring %}
                                    <div class="status-label">
                                        {% trans "Expiring" %}
                                    </div>
                                {% endif %}
                                {% if cert.expired %}
                                    <div class="status-label">
                                        {% trans "Expired" %}
                                    </div>
                                {% endif %}
                                {% if cert.renewed %}
                                    <div class="renewed">
                                        {% trans "Renewed" %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="organisation-certification">
                            <div class="">
                                {% if cert.type == CYBER_ESSENTIALS %}
                                    <img class="icon icon-title cert-icon" alt="CyberEssentials" src="{% static 'icons/page-title/CyberEssentials.svg' %}">
                                {% elif cert.type == CYBER_ESSENTIALS_PLUS %}
                                    <img class="icon icon-title cert-icon" alt="CyberEssentialsPlus" src="{% static 'icons/page-title/CyberEssentialsPlus.svg' %}">
                                {% elif cert.type == GDPR %}
                                    <img class="icon icon-title cert-icon" alt="GDPR" src="{% static 'icons/page-title/GDPR.svg' %}">
                                {% elif cert.type == CYBERSMART_COMPLETE %}
                                    <img class="icon icon-title cert-icon" alt="CSComplete" src="{% static 'icons/page-title/Complete-temp.svg' %}">
                                {% else %}
                                    <i class="fa fa-trophy {% if not cert.expired %}text-warning{% endif %}" aria-hidden="true"></i>
                                {% endif %}
                                {{ cert.full_title }}
                            </div>
                            <div class="{% if not cert.expiring %} m-b-10 {% endif %} text-success">
                                <i class="fa fa-calendar" aria-hidden="true"></i>
                                <span data-toggle="tooltip" data-placement="top" title="{{ cert.certified_date }}">{% trans "Certified:" %} {{ cert.certified_date|timesince }} {% trans "ago" %}</span>
                                <br/>
                            </div>
                            {% if cert.expiring %}
                                <div class="m-b-10 text-danger">
                                    ❌
                                    <span>{% if cert.expiring %} {% trans "Expiring on:" %} {% endif %} {{ cert.renewal_end_date }}</span>
                                    <br/>
                                </div>
                            {% endif %}
                            {% if cert.expired %}
                                <div class="m-b-10 text-danger">
                                    ❌
                                    <span>{% trans "Expired on:" %} {{ cert.renewal_end_date }}</span>
                                    <br/>
                                </div>
                            {% endif %}
                            {% if not distributor_viewing_partner_page %}
                                {% include "partners/certificates/renewal_button.html" %}
                            {% endif %}
                            {% if cert.type == GDPR %}
                                <div class="m-b-10" style="margin-top: -10px">
                                    <img class="icon icon-title cert-icon m-0" style="width: 14px;height: auto;" alt="Deprecated GDPR cert" src="{% static 'icons/partners/folder-x.png' %}">
                                    <span class="text-primary">
                                        {% trans "CyberSmart no longer supports this product" %}
                                    </span>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% empty %}
                <h4 class="text-center">
                    {% blocktranslate with label=CERTIFICATIONS_LABEL|lower %}There are no {{ label }}{% endblocktranslate %}
                </h4>
            {% endfor %}
        </div>
    </div>
</div>