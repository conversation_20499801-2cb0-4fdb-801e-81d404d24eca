
{% extends 'base.html' %}

{% load static %}
{% load divide_tags %}
{% load i18n %}

{% block head_title %}{% trans "Dashboard" %}{% endblock %}

{% block main-content %}
	<div class="container-fluid">
      	<div class="tw-flex tw-flex-col tw-text-center xl:tw-flex-row xl:tw-text-left tw-items-center tw-justify-between tw-py-8">
        	<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
          		<h4 class="tw-font-sans tw-text-xl tw-font-semibold tw-text-gray-900 !tw-my-0">{% trans "Search users "%}</h4>
        	</div>
      	</div>
      <!-- /row -->

      <!-- row -->
      	<div class="row">
        	<div class="col-lg-12">
          		<div class="white-box align-center tw-border tw-font-sans">
					<div class="row">
						<div class="tw-mx-auto tw-max-w-[600px] list_userimpertsonate">
							<div class="clearfix"></div>
							<form action="{% url 'impersonate-search' %}" method="GET">
								<div class="tw-relative group tw-mb-4">
									<label class="tw-font-medium">{% trans "Enter search query:" %}</label>
									<input type="text" name="q" value="{% if query %}{{ query }}{% endif %}" class="tw-w-full tw-font-sans tw-border tw-transition tw-duration-300 tw-ease-in-out tw-h-9 tw-pl-4 tw-border-gray-300 tw-p-2 tw-rounded-full hover:tw-border-gray-400 focus:tw-border-gray-400 focus:tw-shadow-[0_0_0_3px_rgba(0,0,0,0.07)]">
									{{redirect_field}}
									<button class="tw-absolute tw-bottom-1 tw-right-1 tw-rounded-full tw-bg-gray-200 hover:tw-bg-gray-300 tw-h-7 tw-w-7 tw-transition-colors tw-duration-300 tw-ease-in-out" type="submit" value="{% trans "Search" %}">
										<svg class="tw-mt-1 tw-w-4 tw-h-4 tw-fill-gray-500 tw-mb-1">
                							<use href="/static/icons/icons-sprite.svg#icon-search"></use>
            							</svg>
									</button>
								</div>
							</form>
							{% if query and page.object_list %}
							  <ul class="tw-p-0 tw-list-none tw-text-left">
							    {% for user in page.object_list %}
								<li class="tw-min-h-9 tw-flex tw-text-nowrap tw-justify-between tw-items-center tw-gap-1 tw-border-b tw-px-2 tw-bg-white tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 dropdown-toggle">
									<a href="{% url 'impersonate-start' user.id %}{{redirect}}" class="tw-flex tw-justify-between tw-w-full tw-text-black tw-py-4 hover:tw-text-brand-600">
										<span class="tw-truncate">{{ user.email }}({{user.username}})</span>
										<span class="tw-text-gray-500">{% trans "Impersonate" %}</span>
									</a>
								</li>
							    {% endfor %}
							  </ul>
							{% endif %}

							<div class="tw-flex tw-gap-2 tw-py-2 tw-justify-between tw-items-center">
								<p class="tw-font-sans tw-text-sm tw-text-gray-600">{% if query %}{% blocktrans %}Page {{ page_number }}{% endblocktrans %}{% endif %}</p>
								
								<div class="tw-flex tw-gap-2">
									{% if query and page.has_previous %}
									<a href="?page={{ page.previous_page_number }}&q={{ query|urlencode }}" class="tw-min-h-9 tw-flex tw-text-nowrap tw-justify-center tw-items-center tw-gap-1 tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 dropdown-toggle">{% trans "Previous page" %}</a>
									{% endif %}

									{% if query and page.has_next %}
									<a href="?page={{ page.next_page_number }}&q={{ query|urlencode }}" class="tw-min-h-9 tw-flex tw-text-nowrap tw-justify-center tw-items-center tw-gap-1 tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 dropdown-toggle">{% trans "Next page" %}</a>
									{% endif %}
								</div>
							</div>
						</div>
					</div>
          		</div>
          	</div>
        </div>
	</div>
<!-- /.container-fluid -->
{% endblock %}
{% block extra-js %}
{% endblock %}


