{% extends 'base.html' %}

{% load static %}
{% load divide_tags %}
{% load i18n %}

{% block head_title %}{% trans "Dashboard" %}{% endblock %}

{% block extra-css %}
	<style type="text/css">
		.list_userimpertsonate ul {
			padding: 0;
			margin: 0;
			margin-bottom: 30px;
		}
		.list_userimpertsonate ul li {
			background: #fff;
			padding: 0 15px;
			border-bottom: 1px solid #ddd;
			list-style: none;
			overflow: hidden;
			clear: both;
		}
		.list_userimpertsonate ul li a {
			line-height: 40px;
			width: 70%;
			float: left;
			display: inline-block;
		}
		.list_userimpertsonate ul li:hover {
		background-color: #ddd; 
		}
		.list_userimpertsonate li:nth-child(odd) {
		}

		.list_userimpertsonate li:nth-child(even) { 
		background-color: transparent;  
		}
		.list_userimpertsonate ul li span {
			float: right;
			line-height: 40px;
		}
		.search_impersonate {
			margin: 10px 0;
			line-height: 25px;
		}
	</style>
{% endblock %}
{% block main-content %}
	<div class="container-fluid">
      	<div class="tw-flex tw-flex-col tw-text-center xl:tw-flex-row xl:tw-text-left tw-items-center tw-justify-between tw-py-8">
        	<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
          		<h4 class="page-title"><i class="fa-user fa-fw fa"></i>{% blocktrans %}Account List - Page {{ page_number }}{% endblocktrans %}</h4>
        	</div>
        	<!-- /.col-lg-12 -->
      	</div>
      <!-- /row -->

      <!-- row -->
      	<div class="row">
        	<div class="col-lg-12">
          		<div class="white-box align-center">
					<div class="row">
						<div class="col-md-6 col-md-offset-3 list_userimpertsonate" >
							<br>
							<!-- this simply hides the button, it's not a security mechanism -->
							{% if request.user.is_staff %}
								<a class="pull-right search_impersonate btn btn-primary" href="{% url 'impersonate-search' %}">{% trans "Search" %}</a>
							{% endif %}
							{% if page.object_list %}
							  <ul>
							    {% for user in page.object_list %}
							      <li class="text-left">
							      	<a href="{% url 'impersonate-start' user.id %}{{redirect}}">{{ user.email }}({{user.username}})</a> <span>{% trans "Impersonate" %}</span>
							      </li>
							    {% endfor %}
							  </ul>
							{% endif %}

							{% if page.has_previous %}
							<a href="?page={{ page.previous_page_number }}">{% trans "Previous Page" %}</a> &nbsp;
							{% endif %}

							{% if page.has_next %}
							<a href="?page={{ page.next_page_number }}">{% trans "Next Page" %}</a> &nbsp;
							{% endif %}
						</div>
					</div>
          		</div>
          	</div>
        </div>
    </div>

<!-- /.container-fluid -->
{% endblock %}
{% block extra-js %}
{% endblock %}

