{% load i18n admin_urls admin_modify static %}
{% block extrahead %}
    <script src="{% static 'js/jquery.2.2.4.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}" ></script>
    <link href="{% static 'css/admin/modal.css' %}" rel="stylesheet">
{% endblock %}
<li>
    <a href="#" data-toggle="modal" data-target="#{{ modal_id }}" class="grp-state-focus addlink">{% trans "Upload (JSON)" %}</a>
</li>
<!-- Modal -->
<div class="modal fade" id="{{ modal_id }}" tabindex="-1" role="dialog" aria-labelledby="{{ modal_id }}Label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form name="{{ form_name }}" method="post" action="{{ action_url }}" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">{% trans "Upload JSON file" %}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <label for="json-file">{% trans "Choose file to upload" %}</label>
                    <input type="file" id="json-file" name="json_file" accept=".json">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "Close" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "Upload" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>
