{% extends "admin/base.html" %}

{% load i18n %}
{% load static %}
{% load divide_tags %}
{% block title %}{% trans 'Management page' %}{% endblock %}
{% block extrastyle %}
    <link href="{% static 'css/themify-icons.min.css' %}" rel="stylesheet">
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    <style>
        body {
            background: none !important;
            font-family: Arial, Helvetica, sans-serif;
        }
        .detailed-page-link {
            text-decoration: underline !important;
            text-decoration-color: #70b5ee !important;
        }
    </style>
{% endblock extrastyle %}
{% block breadcrumbs %}
    <div class="breadcrumbs">
        <a href="{% url 'admin:index' %}"> {% trans 'Home' %} </a>&rsaquo;
        {% trans 'Management' %}
    </div>
{% endblock %}
{% block content %}
    {% csrf_token %}
    <div class="col-md-12 col-lg-12 col-sm-12">
        <div class="white-box">
            <div class="row">
                <div class="col-lg-6">
                    <h3 class="text-center pull-left">{% trans 'Last update' %}: {{ modified }}</h3>
                </div>
                <div class="col-lg-6">
                    <button id="force_update" class="btn btn-success pull-right btn-sm">{% trans 'Force update' %}</button>
                </div>
            </div>
            <div class="row">
                {% include 'partials/loading.html' with show=False loading_text='Updating' %}
                <div class="emailuser_dash col-lg-3 col-sm-3 row-in-br">
                    <div class="col-in row">
                        <div class="col-md-8 col-sm-8 col-xs-8">
                            <i class="ti-briefcase"></i>
                            <h5 class="text-muted vb"><a class="detailed-page-link" href="{% url 'management:organisations' %}">{% trans 'Organisations' %}</a></h5>
                        </div>
                        <div class="col-md-4 col-sm-4 col-xs-4">
                            <h3 class="counter text-right m-t-15 text-muted text-success">{{ organisations_count }}</h3>
                        </div>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <small><i class="ti-angle-double-down text-info"></i> <a class="detailed-page-link" href="{% url 'management:organisations' %}?bulk_install=True">{% trans 'Bulk deployment' %}</a>: <b>{{ organisations_bulk_count }}</b></small><br/>
                            <small><i class="ti-user text-success"></i> <a class="detailed-page-link" href="{% url 'management:organisations' %}?bulk_install=False">{% trans 'Individual enrolment' %}</a>: <b>{{ organisations_individual_count }}</b></small><br/>
                            <small><i class="ti-plus text-info"></i> <a class="detailed-page-link" href="{% url 'management:organisations' %}?partner=True">{% trans 'Partners' %}</a>: <b>{{ organisations_partner_count }}</b></small><br/>
                            <small><i class="ti-plus text-info"></i> <a class="detailed-page-link" href="{% url 'management:partner-users' %}?partner=True">{% trans 'Partner Users' %}</a>: <b>{{ partner_users_count }}</b></small><br/>
                            <small><i class="ti-crown text-warning"></i> <a class="detailed-page-link" href="{% url 'management:paid-stripe-customers' %}">{% trans 'Paid Customers (Stripe)' %}</a>: <b>{{ organisations_paid_stripe_count }}</b></small><br/>
                            <small><i class="ti-crown text-warning"></i> <a class="detailed-page-link" href="{% url 'management:paid-chargebee-customers' %}">{% trans 'Paid Customers (Chargebee)' %}</a>: <b>{{ organisations_paid_chargebee_count }}</b></small><br/>
                            <small><i class="ti-crown text-warning"></i> <a class="detailed-page-link" href="{% url 'management:paid-chargebee-customers' %}?plan=basic">{% trans 'Paid Customers (Chargebee) Software Basic' %}</a>: <b>{{ organisations_basic_plan }}</b></small><br/>
                            <small><i class="ti-crown text-warning"></i> <a class="detailed-page-link" href="{% url 'management:paid-chargebee-customers' %}?plan=pro">{% trans 'Paid Customers (Chargebee) Software Pro' %}</a>: <b>{{ organisations_pro_plan }}</b></small><br/>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="emailuser_dash col-lg-3 col-sm-3 row-in-br">
                        <div class="col-in row">
                            <div class="col-md-8 col-sm-8 col-xs-8">
                                <i class="ti-download"></i>
                                <h5 class="text-muted vb"><a class="detailed-page-link" href="{% url 'management:appinstalls' %}?inactive=False">{% trans 'Active App installs' %}</a></h5>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-4">
                                <h3 class="counter text-right m-t-15 text-muted text-primary">{{ app_installs_active_count }}</h3>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <small><i class="ti-check-box text-success"></i> <a class="detailed-page-link" href="{% url 'management:appinstalls' %}?basic=True">{% trans 'Basic Apps' %}</a>: <b>{{ app_installs_basic }}</b></small><br/>
                                <small><i class="ti-na text-danger"></i> <a class="detailed-page-link" href="{% url 'management:appinstalls' %}?inactive_basic=True">{% trans 'Inactive Basic Apps' %}</a>: <b>{{ app_installs_inactive_basic_count }}</b></small><br/>
                                <small><i class="ti-check-box text-success"></i> <a class="detailed-page-link" href="{% url 'management:appinstalls' %}?pro=True">{% trans 'Pro Apps' %}</a>: <b>{{ app_installs_pro }}</b></small><br/>
                                <small><i class="ti-na text-danger"></i> <a class="detailed-page-link" href="{% url 'management:appinstalls' %}?inactive_pro=True">{% trans 'Inactive Pro Apps' %}</a>: <b>{{ app_installs_inactive_pro_count }}</b></small><br/>
                                <small><i class="ti-check-box text-success"></i> <a class="detailed-page-link" href="{% url 'management:appinstalls' %}">{% trans 'Total' %}</a>: <b>{{ app_installs_count }}</b> ({% trans "excluding inactive" %})</small><br/>
                                <small><i class="ti-na text-danger"></i> <a class="detailed-page-link" href="{% url 'management:appinstalls' %}?inactive=True">{% trans 'Inactive' %}</a>: <b>{{ app_installs_inactive_count }}</b></small><br/>
                                <small><i class="ti-alert text-danger"></i> <a class="detailed-page-link" href="{% url 'management:appinstalls' %}?without_reports=True">{% trans 'Without reports' %}</a>: <b>{{ app_installs_without_reports_count }}</b></small>
                                <small class="m-l-10">{% trans 'Average' %}: <b>{{ app_installs_average }}</b></small>
                                <small>{% trans 'Median' %}: <b>{{ app_installs_median }}</b></small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="emailuser_dash col-lg-3 col-sm-3 row-in-br">
                            <div class="col-in row">
                                <div class="col-md-6 col-sm-6 col-xs-6">
                                    <i class="ti-user"></i>
                                    <h5 class="text-muted vb"><a class="detailed-page-link" href="{% url 'management:appusers' %}">{% trans 'App users' %}</a></h5>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-6">
                                    <h3 class="counter text-right m-t-15 text-muted text-primary">{{ app_users_count }}</h3>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <small><i class="ti-check-box text-success"></i> <a class="detailed-page-link" href="{% url 'management:appusers' %}?active=True">{% trans 'Active' %}</a>: <b>{{ app_users_active_count }}</b></small>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="emailuser_dash col-lg-3 col-sm-3 row-in-br">
                                <div class="col-in row">
                                    <div class="col-md-6 col-sm-6 col-xs-6">
                                        <i class="ti-wand"></i>
                                        <h5 class="text-muted vb"><a class="detailed-page-link" href="{% url 'management:issued-certifications' %}">{% trans 'Certifications' %}</a></h5>
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-6">
                                        <h3 class="counter text-right m-t-15 text-muted text-info">{{ issued_cert }}</h3>
                                    </div>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <small><i class="ti-hand-drag text-warning"></i> <a class="detailed-page-link" href="{% url 'management:organisations' %}?issued=True">{% trans 'Organisations' %}</a>: <b>{{ organisations_issued_cert }}</b></small><br/>
                                        <small><i class="ti-cloud-down text-success"></i> <a class="detailed-page-link" href="{% url 'management:issued-certifications' %}?cybersmart_cyber_essentials=True">{% trans 'CS: Cyber Essentials' %}</a>: <b>{{ cs_issued_cert }}</b></small><br/>
                                        <small><i class="ti-cloud-down text-success"></i> <a class="detailed-page-link" href="{% url 'management:issued-certifications' %}?cybersmart_gdpr=True">{% trans 'CS: GDPR' %}</a>: <b>{{ gdpr_issued_cert }}</b></small><br/>
                                        <small><i class="ti-cloud-down text-success"></i> <a class="detailed-page-link" href="{% url 'management:issued-certifications' %}?api_essentials=True">{% trans 'API: Cyber Essentials' %}</a>: <b>{{ api_cs_issued_cert }}</b></small><br/>
                                       <small><i class="ti-cloud-down text-success"></i> <a class="detailed-page-link" href="{% url 'management:issued-certifications' %}?api_gdpr=True">{% trans 'API: GDPR' %}</a>: <b>{{ api_gdpr_issued_cert }}</b></small>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="emailuser_dash col-lg-3 col-sm-3">
                                    <div class="col-in row">
                                        <div class="col-md-6 col-sm-6 col-xs-6">
                                            <i class="ti-link"></i>
                                            <h5 class="text-muted vb">{% trans 'API CB ORGS' %}</h5>
                                        </div>
                                        <div class="col-md-6 col-sm-6 col-xs-6">
                                            <h3 class="counter text-right m-t-15 text-muted text-info">{{ partners_with_iasme }}</h3>
                                        </div>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                        <small><i class="ti-cloud-down text-success"></i> <a class="detailed-page-link" href="{% url 'management:partners' %}?cb=True">{% trans 'CB partners' %}</a>: <b>{{ partners_with_iasme }}</b></small><br/>
                                        <small><i class="ti-cloud-down text-success"></i> <a class="detailed-page-link" href="{% url 'management:organisations' %}?cb=True">{% trans 'CB organisations' %}</a>: <b>{{ partners_organisations }}</b></small><br/>
                                        <small><i class="ti-cloud-down text-success"></i> <a class="detailed-page-link" href="{% url 'management:organisations' %}?cb_clients=True">{% trans 'CB clients' %}</a>: <b>{{ partners_clients }}</b></small><br/>
                                        <small><i class="ti-cloud-down text-success"></i> <a class="detailed-page-link" href="{% url 'management:partners' %}?api_linked=True">{% trans 'Linked APIs' %}</a>: <b>{{ partners_with_pervade_token }}</b></small><br/>
                                            <small><i class="ti-cloud-down text-success"></i> <a class="detailed-page-link" href="{% url 'management:appinstalls' %}?cb_pro=True">{% trans 'Pro apps' %}</a>: <b>{{ partners_client_pro_apps }}</b> (<a class="detailed-page-link" href="{% url 'management:appinstalls' %}?cb_partners_pro=True">{% trans 'Partners' %}</a>: <b>{{ iasme_partners_organisations_pro_app_installs }}</b> | <a class="detailed-page-link" href="{% url 'management:appinstalls' %}?cb_clients_pro=True">{% trans 'Clients' %}</a>: <b>{{ iasme_clients_organisations_pro_app_installs }}</b>)</small><br/>
                                            <small><i class="ti-cloud-down text-success"></i> <a class="detailed-page-link" href="{% url 'management:appinstalls' %}?cb_basic=True">{% trans 'Basic apps' %}</a>: <b>{{ partners_clients_basic_apps }}</b> (<a class="detailed-page-link" href="{% url 'management:appinstalls' %}?cb_partners_basic=True">{% trans 'Partners' %}</a>: <b>{{ iasme_partners_organisations_basic_app_installs }}</b> | <a class="detailed-page-link" href="{% url 'management:appinstalls' %}?cb_clients_basic=True">{% trans 'Clients' %}</a>: <b>{{ iasme_clients_organisations_basic_app_installs }}</b>)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <table id="result_list" class="table table-responsive text-center">
                        <thead>
                        <tr>
                            <th scope="col" class="sortable">
                                <div class="text-center">
                                    {% if get_params.name == 'True' %}
                                        <a href="?name=False">{% trans 'Organisation name' %} <i class="ti-arrow-up font-bold"></i></a>
                                    {% elif get_params.name == 'False' %}
                                        <a href="?name=True">{% trans 'Organisation name' %} <i class="ti-arrow-down font-bold"></i></a>
                                    {% else %}
                                        <a href="?name=True">{% trans 'Organisation name' %} <i class="font-bold"></i></a>
                                    {% endif %}
                                </div>
                                <div class="clear"></div>
                            </th>
                            <th scope="col" class="sortable">
                                <div class="text-center">
                                    {% trans 'Organisation Industry' %}
                                </div>
                                <div class="clear"></div>
                            </th>
                            <th scope="col" class="sortable">
                                <div class="text-center">
                                    {% if get_params.partner == 'True' %}
                                        <a href="?partner=False">{% trans 'Partner first org' %} <i class="ti-arrow-up font-bold"></i></a>
                                    {% elif get_params.partner == 'False' %}
                                        <a href="?partner=True">{% trans 'Partner first org' %} <i class="ti-arrow-down font-bold"></i></a>
                                    {% else %}
                                        <a href="?partner=True">{% trans 'Partner first org' %} <i class="font-bold"></i></a>
                                    {% endif %}
                                </div>
                            </th>
                            <th scope="col" class="sortable">
                                <div class="text-center">
                                    {% if get_params.bulk == 'True' %}
                                        <a href="?bulk=False">{% trans 'Bulk deployment status' %} <i class="ti-arrow-up font-bold"></i></a>
                                    {% elif get_params.bulk == 'False' %}
                                        <a href="?bulk=True">{% trans 'Bulk deployment status' %} <i class="ti-arrow-down font-bold"></i></a>
                                    {% else %}
                                        <a href="?bulk=True">{% trans 'Bulk deployment status' %} <i class="font-bold"></i></a>
                                    {% endif %}
                                </div>
                                <div class="clear"></div>
                            </th>
                            <th scope="col" class="sortable">
                                <div class="text-center">
                                    {% if get_params.users == 'True' %}
                                        <a href="?users=False">{% trans "App users" %} <i class="ti-arrow-up font-bold"></i></a>
                                    {% elif get_params.users == 'False' %}
                                        <a href="?users=True">{% trans "App users" %} <i class="ti-arrow-down font-bold"></i></a>
                                    {% else %}
                                        <a href="?users=True">{% trans "App users" %} <i class="font-bold"></i></a>
                                    {% endif %}
                                </div>
                                <div class="clear"></div>
                            </th>
                            <th scope="col" class="sortable">
                                <div class="text-center">
                                    {% if get_params.installs == 'True' %}
                                        <a href="?installs=False">{% trans 'App installs' %} <i class="ti-arrow-up font-bold"></i></a>
                                    {% elif get_params.installs == 'False' %}
                                        <a href="?installs=True">{% trans 'App installs' %} <i class="ti-arrow-down font-bold"></i></a>
                                    {% else %}
                                        <a href="?installs=True">{% trans 'App installs' %} <i class="font-bold"></i></a>
                                    {% endif %}
                                </div>
                                <div class="clear"></div>
                            </th>
                            <th scope="col" class="sortable">
                                <div class="text-center">
                                    {% if get_params.certificates == 'True' %}
                                        <a href="?certificates=False">{% trans 'Certificates' %} <i class="ti-arrow-up font-bold"></i></a>
                                    {% elif get_params.certificates == 'False' %}
                                        <a href="?certificates=True">{% trans 'Certificates' %} <i class="ti-arrow-down font-bold"></i></a>
                                    {% else %}
                                        <a href="?certificates=True">{% trans 'Certificates' %} <i class="font-bold"></i></a>
                                    {% endif %}
                                </div>
                                <div class="clear"></div>
                            </th>
                            <th scope="col" class="sortable">
                                <div class="text-center">
                                    {% if get_params.percentage == 'True' %}
                                        <a href="?percentage=False">{% trans 'Pass percentage' %} <i class="ti-arrow-up font-bold"></i></a>
                                    {% elif get_params.percentage == 'False' %}
                                        <a href="?percentage=True">{% trans 'Pass percentage' %} <i class="ti-arrow-down font-bold"></i></a>
                                    {% else %}
                                        <a href="?percentage=True">{% trans 'Pass percentage' %} <i class="font-bold"></i></a>
                                    {% endif %}
                                </div>
                                <div class="clear"></div>
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for org in organisations %}
                            <tr>
                                <td>
                                    {{ org.name|default:"-" }}
                                </td>
                                <td>
                                    {{ org.get_industry_display }}
                                </td>
                                <td>
                                    {% if org.has_partner %}
                                        <i class="glyphicon glyphicon-ok text-success"></i>
                                    {% else %}
                                        <i class="glyphicon glyphicon-remove text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if org.bulk_install %}
                                        <i class="glyphicon glyphicon-ok text-success"></i>
                                    {% else %}
                                        <i class="glyphicon glyphicon-remove text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ org.enrolled_users_count }}
                                </td>
                                <td>
                                    {{ org.installed_devices_count }}
                                </td>
                                <td>
                                    {{ org.certifications_count }}
                                </td>
                                <td>
                                    {% if org.analytics %}
                                        <div class="{% percent_to_css_class org.analytics.get_pass_percentage_int "0-50:text-danger,51-99:text-warning,100:text-success"%}">
                                            <b>{{ org.analytics.get_pass_percentage_int }}%</b>
                                        </div>
                                    {% else %}
                                        {% trans 'no analytics' %}
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                    {% include 'partials/pagination.html' with params=params_query %}
                </div>
            </div>
        </div>
    </div>
{% endblock content %}
{% block extrahead %}
    <script src="{% static 'js/jquery.2.2.4.min.js' %}"></script>
    <script type="application/javascript">
        $(document).ready(function () {
            $("#force_update").click(function () {
                $('.loading-container').show();
                $.ajax({
                    url: $(location).attr('href'),
                    type: 'POST',
                    beforeSend: function(request) {
                        request.setRequestHeader("X-CSRFTOKEN", $('input[name=csrfmiddlewaretoken]').val());
                    },
                    success: function() {
                        window.location.reload();
                    }
                })
            })
        });
    </script>
{% endblock extrahead %}

{% block extra-js %}
    
{% endblock %}
