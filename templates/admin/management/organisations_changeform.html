{% extends 'admin/change_form.html' %}
{% load i18n %}

{% block field_sets %}
    {% if opt_in and opt_in.bound %}
        {% if original.has_ce_100k_r_and_r_toolbox_insurance and not original.has_r_and_r_toolbox_scheduled_pause_subscription %}
            <label style="margin-left: 10px" for="cancel-superscript">{% trans 'Downgrade superscript opt-in' %}:</label>
            <input style="margin-left: 9px; background-color: darkorange" type="submit" value="{% trans "Schedule pause for R&R toolbox subscription" %}" name="cancel-superscript">
            <div class="help" style="margin-left: 170px; padding-left: 10px;">Note that this will also downgrade SS from CE 100k to CE 25k on subscription end term</div>
        {% elif original.has_r_and_r_toolbox_scheduled_pause_subscription %}
            <label style="margin-left: 10px" for="cancel-superscript">{% trans 'Downgrade superscript opt-in' %}:</label>
            <input disabled style="margin-left: 9px; background-color: darkorange" type="submit" value="{% trans "R&R toolbox subscription pause already scheduled" %}" name="cancel-superscript">
            <div class="help" style="margin-left: 170px; padding-left: 10px;">Note that this will downgrade SS from CE 100k to CE 25k on subscription pause date</div>
        {% elif has_25k or has_100k %}
            <label style="margin-left: 10px" for="cancel-superscript">{% trans 'Cancel superscript opt-in' %}:</label>
            <input style="margin-left: 9px; background-color: darkorange" type="submit" value="{% trans "Cancel Superscript" %}" name="cancel-superscript">
        {% endif %}
    {% endif %}


    <div class="form-row">
        <div>
            <label for="change-deployment-type"><b>{% trans 'Bulk install' %}:</b></label>
            <span style="margin-left: 20px">{{ original.get_bulk_install_display }}</span>
            {% if can_change_deployment_type %}
                <input style="margin-left: 15px; background-color: darkorange" type="submit" value="{% trans "Change to bulk deployment" %}" name="change-deployment-type">
            {% endif %}
        </div>
    </div>
    <script type="text/javascript">
        function nullifyCepAuditRequestDate() {
        {% if original %}
            if (confirm("Are you sure you want to nullify the cep_audit_request_date?")) {
                fetch("{% url 'api-v2:nullify-cep-audit-request-date' original.secure_id %}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: JSON.stringify({})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert('cep_audit_request_date has been nullified.');
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            }
        {% endif %}
    }
    </script>


    {{ block.super }}
{% endblock %}
{% block after_field_sets %}
    {% if original %}
        <div class="form-row">
            <div>
                <label for="reset-cep-audit"><b>{% trans 'CE+ Audit' %}:</b></label>
                <button style="margin-left: 15px; background-color: #17a2b8; color: white; padding: 5px 15px; border: none; border-radius: 3px; cursor: pointer;" type="button" onclick="nullifyCepAuditRequestDate()">Reset CE+ audit date</button>
                <div class="help">This will reset the CE+ audit request date to allow for a new audit request</div>
            </div>
        </div>
        <div class="form-row">
            <div>
                <label for="recalculate-analytics"><b>{% trans 'Smart Score' %}:</b></label>
                <input style="margin-left: 15px; background-color: #17a2b8; color: white; padding: 5px 15px; border: none; border-radius: 3px; cursor: pointer;" type="submit" value="{% trans "Recompute smart score" %}" name="recalculate-analytics">
                <div class="help">This will recompute all analytics metrics including pass percentages, device stats, and policy agreements</div>
            </div>
        </div>
    {% endif %}
{% endblock %}

