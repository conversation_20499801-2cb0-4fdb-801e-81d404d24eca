{% extends "admin/base.html" %}
{% load i18n %}
{% load static %}
{% load divide_tags %}
{% block title %}{% trans 'App Users' %}{% endblock %}
{% block extrastyle %}
    <link href="{% static 'css/themify-icons.min.css' %}" rel="stylesheet">
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    <style>
        body {
            background: none !important;
            font-family: Arial, Helvetica, sans-serif;
        }
    </style>
{% endblock extrastyle %}
{% block breadcrumbs %}
    <div class="breadcrumbs">
        <a href="{% url 'admin:index' %}"> {% trans 'Home' %} </a>&rsaquo;
        <a href="{% url 'management:dashboard' %}"> {% trans 'Management' %} </a>&rsaquo;
        {% trans "App Users" %} [{{ page_obj.paginator.count }}]
    </div>
{% endblock %}
{% block content %}
    <table id="result_list" class="table table-responsive text-center">
        <thead>
        <tr>
            <th scope="col" class="sortable">
                <div class="text-center"><span>{% trans 'Organisation' %}</span></div>
                <div class="clear"></div>
            </th>
            <th scope="col" class="sortable">
                <div class="text-center"><span>UUID</span></div>
                <div class="clear"></div>
            </th>
            <th scope="col" class="sortable">
                <div class="text-center"><span>{% trans 'Email' %}</span></div>
                <div class="clear"></div>
            </th>
            <th scope="col" class="sortable">
                <div class="text-center"><span>{% trans 'First Name' %}</span></div>
                <div class="clear"></div>
            </th>
            <th scope="col" class="sortable">
                <div class="text-center"><span>{% trans 'Last Name' %}</span></div>
                <div class="clear"></div>
            </th>
            <th scope="col" class="sortable">
                <div class="text-center"><span>{% trans 'Is Admin' %}</span></div>
                <div class="clear"></div>
            </th>
            <th scope="col" class="sortable">
                <div class="text-center"><span>{% trans 'Report' %}</span></div>
                <div class="clear"></div>
            </th>
            <th scope="col" class="sortable">
                <div class="text-center"><span>{% trans 'Active' %}</span></div>
                <div class="clear"></div>
            </th>
        </tr>
        </thead>
        <tbody>
        {% for user in app_users %}
            <tr>
                <td>
                    {{ user.organisation|default:"-" }}
                </td>
                <td>
                    {{ user.uuid|default:"-" }}
                </td>
                <td>
                    {{ user.email|default:"-" }}
                </td>
                <td>
                    {{ user.first_name|default:"-" }}
                </td>
                <td>
                    {{ user.last_name|default:"-" }}
                </td>
                <td>
                    {% if user.is_admin %}
                        <i class="glyphicon glyphicon-ok text-success"></i>
                    {% else %}
                        <i class="glyphicon glyphicon-remove text-danger"></i>
                    {% endif %}
                </td>
                <td>
                    {{ user.report|default:"-" }}
                </td>
                <td>
                    {% if user.active %}
                        <i class="glyphicon glyphicon-ok text-success"></i>
                    {% else %}
                        <i class="glyphicon glyphicon-remove text-danger"></i>
                    {% endif %}
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
    {% include 'partials/pagination.html' %}
{% endblock content %}