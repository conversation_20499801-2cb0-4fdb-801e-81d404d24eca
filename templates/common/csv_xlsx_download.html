{% load i18n %}
<div class="btn-group">
    <button style="height:36px" type="button" class="tw-min-h-9 tw-flex tw-items-center tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        {% trans "Export" %}
        <i class="fa fa-caret-down m-l-5"></i>
    </button>
    <ul class="dropdown-menu dropdown-menu-right">
        <li class="dropdown-header">{% trans "Choose Format" %}</li>
        <li><a href="{{ csv_xlsx_download_url }}{% if csv_xlxs_query_parameters %}?{{ csv_xlxs_query_parameters }}{% endif %}">{% trans "CSV" %}</a></li>
        <li><a href="{{ csv_xlsx_download_url }}?format=xlsx{% if csv_xlxs_query_parameters %}&{{ csv_xlxs_query_parameters }}{% endif %}">{% trans "XLSX" %}</a></li>
    </ul>
</div>