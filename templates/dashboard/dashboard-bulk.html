{% extends 'base.html' %}

{% load static %}
{% load divide_tags app_version_tags %}
{% load i18n %}
{% load waffle_tags %}

{% block head_title %}{% trans "Dashboard" %}{% endblock %}

{% block sub_nav %}
    {% include 'partials/sub-header-org.html' %}
{% endblock %}

{% block extra-css %}
    <link rel="stylesheet" href="{% static 'css/org-dashboard.css' %}">
{% endblock %}

{% block main-content %}
    <div class="container-fluid">
        {% flag "disable_add_fake_app_installs_button" %}
        {% else %}
            {% if can_add_fakes %}
                {% include "app_users/add_fake_installs.html" with app_user=organisation.main_app_user %}
            {% endif %}
        {% endflag %}

        {% with organisation.name|add:" - "|add:_("Dashboard") as page_title %}
            {% include "partials/header_default.html" with page_title=page_title page_icon="box" download_cap=True %}
        {% endwith %}

        {% include "smart_score/dashboard_widget.html" %}
        <div style="position: relative">
            {% include 'partials/access-restriction.html' with restricted_page='dashboard-bulk' %}
            {% if not agent_link %}
                <div class="row">
                    <div class="col-md-12 col-lg-12 col-sm-12">
                        <div class="white-box tw-border">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="row row-in">
                                        <div class="col-lg-12 col-sm-12">
                                            <div class="col-in row">
                                                <h5 class="text-muted vb" style="font-size: 20px;margin-top: 12px;">{% trans "We are building your custom desktop app installer now - this will take 5-10 minutes. Please check your email for a step-by-step platform guide." %}</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="tw-flex tw-flex-col lg:tw-flex-row tw-basis-full tw-justify-between tw-gap-3 tw-mb-3">
                <a class="dash-entities-all tw-grow tw-text-black hover:tw-brand-600">
                    {% trans "Devices installed" as widget_title %}
                    {% include "partials/widget_metric.html" with widget_id="widgetDevicesInstalled" widget_title=widget_title widget_metric=total_devices widget_icon="download" %}
                </a>

                <a class="dash-entities-passing tw-grow tw-text-black hover:tw-brand-600">
                    {% trans "Devices passing" as widget_title %}
                    {% include "partials/widget_metric.html" with widget_id="widgetDevicesPassing" widget_title=widget_title widget_metric=total_device_passing widget_icon="circle-check" %}
                </a>

                <a class="dash-entities-failing tw-grow tw-text-black hover:tw-brand-600">
                    {% trans "Devices failing" as widget_title %}
                    {% include "partials/widget_metric.html" with widget_id="widgetDevicesFailing" widget_title=widget_title widget_metric=total_device_failing widget_icon="circle-x" %}
                </a>
            </div>

            {% include 'partials/app-deployment/index.html' %}
            
            {% include "partials/evaluation_prompt.html" %}
            <!-- .row -->
            <div class="row">
                <div class="col-lg-12">
                    <div class="white-box tw-border">
                        <div class="tw-flex tw-flex-col lg:tw-flex-row tw-justify-between tw-mb-4">
                            <div class="flex tw-items-center tw-content-center">
                                <h3 class="tw-font-sans tw-text-lg tw-items-center tw-font-medium tw-content-center tw-basis-full p-0 m-0">{% trans "Devices in scope" %}</h3>
                            </div>
                            
                            <div class="tw-flex tw-gap-2 tw-mt-2 lg:tw-mt-0">
                                {% flag "disable_add_fake_app_installs_button" %}
                                {% else %}
                                    {% if can_add_fakes %}
                                        <button type="button" data-toggle="modal" data-target="#fakeAppInstallsModal-{{ organisation.main_app_user.uuid }}" class="tw-min-h-9 tw-flex tw-items-center tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0">
                                            {% trans "Add fake devices" %}
                                        </button>
                                    {% endif %}
                                {% endflag %}
                                {% include "organisations/dashboard/common/beta_devices_filter.html" %}

                                {% url 'dashboard:dashboard-csv-download' organisation.secure_id as csv_xlsx_download_url %}
                                {% include "common/csv_xlsx_download.html" with csv_xlsx_download_url=csv_xlsx_download_url %}
                            </div>
                        </div>

                        <div class="tw-flex tw-mb-4">
                            {% translate "Search by Host Name, Last User, Operating System, Serial & Device ID" as search_help_text %}
                            {% include "organisations/users/blocks/filters_and_search.html" with help_icon=True search_button_style="width: 100px; " search_help_text=search_help_text %}
                        </div>

                        <div>
                            {% include "partials/loading.html" with loader_id="devices_filter_loader" %}
                            <table id="dashboard-table" class="table table-bordered table-hover toggle-circle tw-font-sans tw-text-xs lg:tw-text-sm">
                                <thead>
                                <tr>
                                    <th class="thead-sortable" onclick="orderTable(this)" id="hostname">{% trans "Host Name" %} <i class="fa fa-sort"></i></th>
                                    <th data-hide="phone" class="thead-sortable" onclick="orderTable(this)" id="last_login_username_precomputed">{% trans "Last User" %} <i class="fa fa-sort"></i></th>
                                    <th data-hide="phone" class="thead-sortable" onclick="orderTable(this)" id="os_title">{% trans "Operating System" %} <i class="fa fa-sort"></i></th>
                                    <th data-hide="phone" class="thead-sortable" onclick="orderTable(this)" id="serial_number_and_device_id">{% trans "Serial & Device ID" %} <i class="fa fa-sort"></i></th>
                                    <th data-hide="phone" class="thead-sortable" onclick="orderTable(this)" id="app_version">{% trans "App Version" %} <i class="fa fa-sort"></i></th>
                                    <th class="thead-sortable" style="min-width: 80px;" data-type="numeric" class="thead-sortable" onclick="orderTable(this)" id="status_precomputed">{% trans "Status" %} <i class="fa fa-sort"></i></th>
                                    {% if policies_support %}
                                        <th id="policies" data-sorted="true" data-hide="phone">{% trans "Policies" %}</th>
                                    {% endif %}
                                    <th data-hide="phone" data-type="date" style="min-width:80px" class="thead-sortable" onclick="orderTable(this)" id="most_recent_check_in_precomputed">{% trans "Last Checked In" %} <i class="fa fa-sort"></i></th>
                                </tr>
                                </thead>
                                
                                {% if extra_app_users_id %}
                                    <p class="text-warning">{% trans 'You are currently at your organisation limit of enroled users with Active Protect. If you wish to upgrade please contact Customer Support.' %} </p>
                                {% endif %}
                                <tbody>
                                {% for app_install in devices %}
                                    <tr sort_tr="{{device.id}}">
                                        <!-- If CAP max users has been reached, then do not show details -->
                                        {% if app_install.app_user.id in extra_app_users_id %}
                                            <td><button disabled class="btn btn-outline btn-warning host-name-button">{% trans 'User limit reached' %} <i class="fa fa-arrow-right pull-right"></i></button></td>
                                            <td>
                                                {% if app_install.latest_local_user_is_admin_account_precomputed is True %}
                                                    <i class="fa fa-user text-warning" data-toggle="tooltip" title="{% trans "Local admin" %}"></i>
                                                {% elif app_install.latest_local_user_is_admin_account_precomputed is False %}
                                                    <i class="fa fa-user text-success" data-toggle="tooltip" title="{% trans "Local user" %}"></i>
                                                {% endif %}
                                                {{ app_install.last_login_username_precomputed }}
                                            </td>
                                            <td align='center' >---</td>
                                            <td align='center' >---</td>
                                            <td align='center' >---</td>
                                            <td align='center' >---</td>
                                            <td align='center' >---</td>
                                            {% if policies_support %}
                                                <td align='center' >---</td>
                                            {% endif %}
                                        {% else %}
                                            <td headers="hostname" class="text-align-c">
                                                {% if app_install.inactive %}
                                                    <div class="btn btn-outline btn-primary no-report-hover host-name-button">{{ app_install.hostname }} {% if app_install.machine_model %}[{{ app_install.machine_model }}]{% endif %}</div>
                                                    {% if not app_install.is_trustd_app %}
                                                    <button type="button" url="{{ app_install.url_app_install_api }}" app_install_id="{{ app_install.id }}" class="btn btn-primary btn-rounded reactivate-button m-t-5" >
                                                        <span class="default-resend-text">{% trans "Reactivate" %}</span>
                                                    </button>
                                                    {% endif %}
                                                {% else %}
                                                    <a href="{% call_method app_install 'url' org_id=organisation.secure_id user_uuid=app_install.app_user.uuid device_id=app_install.device_id serial_number=app_install.url_encoded_serial_number  %}" class="btn btn-outline btn-primary host-name-button">{{ app_install.hostname }}{% if app_install.machine_model %} [{{ app_install.machine_model }}]{% endif %} <i class="fa fa-arrow-right pull-right"></i></a>
                                                {% endif %}
                                            </td>
                                            <td headers="last_login_username_precomputed">
                                                {% if app_install.latest_local_user_is_admin_account_precomputed %}
                                                    <i class="fa fa-user text-warning" data-toggle="tooltip" title="{% trans "Local admin" %}"></i>
                                                {% elif app_install.latest_local_user_is_admin_account_precomputed is False %}
                                                    <i class="fa fa-user text-success" data-toggle="tooltip" title="{% trans "Local user" %}"></i>
                                                {% endif %}
                                                {{ app_install.last_login_username_precomputed }}
                                            </td>
                                            <td headers="os_title">{{ app_install.os_title }}</td>
                                            <td headers="serial_number_and_device_id">
                                                <samp style='font-size: 13px'>{{ app_install.serial_number|default:'Unknown serial number' }}</samp><br>
                                                <samp style='font-size: 13px'>{{ app_install.device_id }}</samp>
                                            </td>
                                            <td headers="app_version">v{{ app_install.get_app_version }} {% if app_install.is_beta_precomputed %}<span class="beta-pill"></span>{% endif %}</td>
                                            {% if app_install.inactive and app_install.is_trustd_app %}
                                                <td headers="status_precomputed" align='center' data-value="0">
                                                    <div class="status-container">{% trans "Reinstall required" %}</div>
                                                </td>
                                            {% elif app_install.analytics.latest_pass_percentage %}
                                                <td headers="status_precomputed" align='center' data-value="{{app_install.analytics.latest_pass_percentage|add:"0"}}">
                                                    <div class="status-container">
                                                        <div count_sort="{{app_install.analytics.latest_pass_percentage}}"></div>
                                                        <div class="count_sort_child devices" >
                                                            <span class="percent_complete {% percent_to_css_class app_install.analytics.latest_pass_percentage "0-50:text-danger,51-99:text-warning,100:text-success"%} {% if not app_install.analytics.latest_pass_percentage %}opacity_0{% endif %}" custom-loaded-data="{{app_install.analytics.latest_pass_percentage|floatformat:'0'}}%"></span>
                                                            <div class="progress m-b-0">
                                                                <div class="progress-bar {% percent_to_css_class app_install.analytics.latest_pass_percentage %}" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width:0%;" custom-loaded-data="{{app_install.analytics.latest_pass_percentage}}%" custom-loaded-data-target='{"css": "width"}'></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            
                                            {% else %}
                                                <td headers="status_precomputed" align='center' data-value="0">
                                                    <div class="status-container">{% trans "Install pending" %}</div>
                                                </td>
                                            {% endif %}
                                            {% if policies_support %}
                                                <td headers="policies" align='center'>
                                                    {% if policies_count %}
                                                        {% for os_user in app_install.os_users.all %}
                                                            <div class="status-container" {% if app_install.os_users.all.count > 1 %}style="height: 100%" {% endif %}>
                                                                {% with perc=os_user.agreed_policies_count|percentage:policies_count %}
                                                                    {% if app_install.os_users.all.count > 1 %}<div>{{ os_user.name }}</div>{% endif %}
                                                                    <div class="count_sort_child" >
                                                                        <span class="percent_complete {% percent_to_css_class perc "0-50:text-danger,51-99:text-warning,100:text-success"%}">{{ os_user.agreed_policies_count }} / {{ policies_count }}</span>
                                                                        <div class="progress m-b-0">
                                                                            <div class="progress-bar {% percent_to_css_class perc %}" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width:0%;" custom-loaded-data="{{perc}}%" custom-loaded-data-target='{"css": "width"}'></div>
                                                                        </div>
                                                                    </div>
                                                                {% endwith %}
                                                            </div>
                                                        {% empty %}
                                                            <div class="status-container">
                                                                {% with perc=app_install.agreed_policies_count|percentage:policies_count %}
                                                                    <div class="count_sort_child" >
                                                                        <span class="percent_complete {% percent_to_css_class perc "0-50:text-danger,51-99:text-warning,100:text-success"%}">{{ app_install.agreed_policies_count }} / {{ policies_count }}</span>
                                                                        <div class="progress m-b-0">
                                                                            <div class="progress-bar {% percent_to_css_class perc %} bar" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width:0%;" custom-loaded-data="{{perc}}%" custom-loaded-data-target='{"css": "width"}'></div>
                                                                        </div>
                                                                    </div>
                                                                {% endwith %}
                                                            </div>
                                                        {% endfor %}
                                                    {% else %}
                                                        <div class="status-container">
                                                            <a href="{% url 'smart_policies:main' organisation.secure_id %}" class="btn btn-sm btn-default btn-outline">{% trans "upload " %}<i class="fa fa-arrow-right"></i></a>
                                                        </div>
                                                    {% endif %}
                                                </td>
                                            {% endif %}
                                            <td headers="most_recent_check_in_precomputed"
                                                 align='center'
                                                 data-value='{{ app_install.last_check_in|date:"Y-m-d-h-i" }}'
                                                 data-format-string="YYYY-MM-DD-HH-mm"
                                                 style="vertical-align: top">
                                                <div>{{ app_install.most_recent_check_in_precomputed|date:"j M Y, h:ia" }}</div>
                                            </td>
                                        {% endif %}
                                    </tr>
                                {% endfor %}
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="6">
                                        {% include 'partials/pagination.html' with params=params_query page_obj=devices %}
                                    </td>
                                    <td colspan="2">
                                        {% if show_hidden_apps == True %}
                                            <button id="hide-inactive-apps" class="btn btn-outline btn-primary">{% trans "Hide Inactive Applications" %}</button>
                                        {% else %}
                                            <button id="show-inactive-apps" class="btn btn-outline btn-primary">{% trans "Show Inactive Applications" %}</button>
                                        {% endif %}
                                    </td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->

{% endblock %}

{% block extra-js %}

    {% include 'partials/download-loader.html' %}

    <script src="{% static 'js/dashboard_data.js' %}"></script>
    <script src="{% static 'js/dashboard_custom.js' %}"></script>
    <script src="{% static 'js/modules/organisation/dashboard/dashboard_filters.js' %}"></script>
     <script type="text/javascript">
        // Reactivate a device
        $('.reactivate-button').click(function(){
            let app_install_id = $(this).attr('app_install_id');
            let url = $(this).attr('url');
            let csrfmiddlewaretoken = "{{ csrf_token }}";
            reactivateDevice(app_install_id, url, csrfmiddlewaretoken)
            return false;
        });

        </script>
{% endblock %}
