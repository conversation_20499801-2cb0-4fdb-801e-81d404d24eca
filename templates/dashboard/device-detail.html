{% extends 'base.html' %}

{% load static %}
{% load i18n %}
{% load l10n %}
{% load waffle_tags %}
{% load divide_tags %}

{% block head_title %}{% trans 'Device Detail' %}{% endblock %}

{% block sub_nav %}
    {% include 'partials/sub-header-org.html' %}
{% endblock %}

{% block header_nav %}
    {% include 'partials/side-navigation/header.html' %}
    {% include 'partials/inactiivty.html' %}
{% endblock %}

{% block extra-css %}
    <link type="text/css" rel="stylesheet" href="{% static 'css/device-detail.css' %}">
{% endblock %}

{% block main-content %}
{% include "partials/send_installer_button_modal.html" %}
<div class="container-fluid">
    <div class="row overflow-visible">
        {% if organisation.is_trustd_mobile_available %}
            <div class="col-xs-12">
                <div class="tw-flex tw-flex-col tw-text-center xl:tw-flex-row xl:tw-text-left tw-items-center tw-justify-between tw-py-8">
                    <div class="tw-flex tw-flex-col xl:tw-flex-row tw-items-center tw-gap-3">
                        <div class="tw-flex tw-items-center tw-justify-center">
                            <svg class="tw-w-[38px] tw-h-[38px]">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-laptop-minimal"></use>
                            </svg>
                        </div>

                        <div>
                            <h1 data-html2canvas-ignore="true" class="tw-font-sans tw-text-xl tw-font-semibold tw-text-gray-900 !tw-my-0">{% trans 'Device details' %}</h1>
                        </div>
                    </div>
                    <div class="tw-flex tw-flex-wrap tw-pt-2 tw-gap-2 xl:tw-pt-0 tw-font-sans">
                        {% if latest_report and not latest_report.is_corrupted %}
                            {% if not agent_link %}
                                {% trans 'Send Active Protect Installer' as button_label %}
                                {% with button_class="btn-primary" wrapper_class="pull-right" %}
                                    {% include "partials/send_installer_button.html" with organisation=organisation button_label=button_label button_class=button_class %}
                                    {% include "partials/installer_modal.html" %}
                                {% endwith %}
                                {% if not ssptr == 100 %}
                                    <button id="sendPdf" class="tw-min-h-9 tw-flex tw-text-nowrap tw-justify-center tw-items-center tw-gap-1 tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0" type="button">
                                        <i class="fa fa-paper-plane pr-1.5"></i>{% trans 'Send Report to User' %}
                                    </button>
                                {% endif %}
                                <a target="_blank" href="{{ app_install.url_checks_pdf }}" class="tw-min-h-9 tw-flex tw-text-nowrap tw-justify-center tw-items-center tw-gap-1 tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0">
                                    <i class="fa fa-download pr-1.5"></i>{% trans 'Download Report' %}
                                </a>
                            {% endif %}
                        {% endif %}

                        {% if agent_link %}
                        <a target="_blank" href="{{ app_install.url_checks_pdf }}" class="tw-min-h-9 tw-flex tw-text-nowrap tw-justify-center tw-items-center tw-gap-2 tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0" type="button">
                            <svg class="tw-w-4 tw-h-4">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-download"></use>
                            </svg>
                            {% trans 'Download report' %}
                        </a>
                        {% endif %}

                        {% if agent_link and organisation.is_uba_enabled and app_install.app_user == organisation.main_app_user %}
                            <div id="assign-user-box-{{ app_install.pk }}">
                                <button class="tw-gap-1 tw-bg-brand-600 tw-font-sans hover:tw-bg-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out tw-min-h-9 tw-flex tw-items-center tw-pl-4 tw-pr-3 tw-rounded-md tw-text-white hover:tw-text-white focus:tw-bg-brand-900 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0" type="button" data-toggle="modal" data-target="#user-attribution-modal-{{ app_install.pk }}">
                                    {% trans 'Assign user' %}
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% else %}
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                <h4 class="page-title" data-html2canvas-ignore="true"><i class="linea-icon linea-basic fa-fw" data-icon="7"></i>{% trans 'Device Detail Screen' %}</h4>
            </div>
        {% endif %}
    </div>
    {% if not latest_report or latest_report.is_corrupted %}
    <div class="alert alert-warning text-center">
        <h5>{% trans "CyberSmart Active Protect hasn't generated a report yet. Or the report is corrupted" %}.</h5>
        <i class="fa fa-heartbeat beating-animation p-30" style="font-size: 35px"></i>
    </div>
    {% else %}
    {% if app_install_duplicate %}
        <input id="sibling_install_url" type="hidden" value="{{ app_install_duplicate.absolute_url }}">
        <div class="row m-b-15">
            <div class="col-md-12 col-lg-12 col-sm-12">
                <div class="alert alert-info text-black beta-device-alert">
                    {% if is_beta_device %}
                        {% trans 'beta release' as release_note %}
                    {% else %}
                        {% trans 'public release' as release_note %}
                    {% endif %}
                    <div>
                        {% trans 'You are viewing device information reported by the' %} <strong>{{ release_note }}</strong> {% trans 'of CyberSmart Active Protect '%}
                    </div>
                    <div class="beta-device_label">
                        <div>
                            <label for="beta-device-switch">
                                <strong>{% trans 'View beta release' %}</strong>
                            </label>
                        </div>
                        <div>
                            <label class="switch switch-tiny">
                                <input type="checkbox" class="success" id="beta-device-switch" {% if is_beta_device %}checked{% endif %}>
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
    <div class="row">
        <div class="col-md-12 col-lg-12 col-sm-12">
            {% if not agent_link and not organisation.is_trustd_mobile_available %}
                <div id="generatePdf" class="white-box tw-border" data-html2canvas-ignore="true">
                    <div class="row">
                        <div class="col-lg-6">
                            {% trans 'Send Active Protect Installer' as button_label %}
                            {% with button_class="btn-primary" %}
                                {% include "partials/send_installer_button.html" with organisation=organisation button_label=button_label button_class=button_class %}
                                {% include "partials/installer_modal.html" %}
                            {% endwith %}
                        </div>
                        <div class="col-lg-6 text-right">
                            <a target="_blank" href="{{ app_install.url_checks_pdf }}" class="btn has-spinner pull-right m-l-10" type="button" style='background: #81c6ec; color: white'>
                                <span class="btn-label"><i class="fa fa-download"></i></span>
                                {% trans 'Export PDF' %}
                                <span class="spinner"><div class="cssload-speeding-wheel" style="width:15px;height:15px;"></div></span>
                            </a>
                            {% if not ssptr == 100 %}
                                <button id="sendPdf" class="btn has-spinner" type="button" style='background: #a4c95b; color: white'>
                                    <span class="btn-label"><i class="fa fa-paper-plane"></i></span>{% trans 'Send PDF to User' %}
                                    <span class="spinner"><div class="cssload-speeding-wheel" style="width:15px;height:15px;"></div></span>
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}
            <div class="white-box tw-border">
                <div class="tw-flex tw-flex-col lg:tw-flex-row tw-gap-4 lg:tw-gap-12 tw-font-sans">
                    {% if agent_link %}
                        {% if organisation.is_uba_enabled %}
                            {% if app_install.app_user == organisation.main_app_user %}
                            {% else %}
                                <div id="change-user-box-{{ app_install.pk }}">
                                    <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">{% trans 'User' %}</div>
                                    <div class="tw-flex tw-items-center tw-gap-2">
                                        <span class="tw-truncate tw-max-w-[260px]">{{ app_install.app_user.email }}</span>
                                        <button class="tw-flex tw-align-center tw-w-[26px] tw-h-[26px] tw-text-black tw-align-middle tw-border tw-border-gray-200 tw-rounded tw-p-1 tw-shadow-sm tw-transition-colors tw-duration-300 hover:tw-border-gray-300 hover:tw-bg-gray-200" type="button" data-toggle="modal" data-target="#user-attribution-modal-{{ app_install.pk }}">
                                            <svg class="tw-w-4 tw-h-4">
                                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-pen-line"></use>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            {% endif %}
                            {% include 'partials/user_attribution_assign.html' %}
                        {% endif %}
                        <div class="tw-flex tw-flex-col">
                            <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">{% trans 'Machine' %}</div>
                            {{app_install.hostname }}{% if not app_install.app_user.organisation.bulk_install %}\{{app_install.app_user.get_full_name }}{% endif %}
                        </div>
                    {% else %}
                        <div class="tw-flex tw-flex-col">
                            <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">{% trans 'Name' %}</div>
                            <p class="tw-text-nowrap">{{app_install.app_user.get_full_name }}</p>
                        </div>

                        <div class="tw-flex tw-flex-col">
                            <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">{% trans 'Email' %}</div>
                            <p>{{app_install.app_user.email }}</p>
                        </div>
                    {% endif %}
                    <div class="tw-flex tw-flex-col">
                        <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">{% trans 'App version' %}</div>
                        <p>v{{app_install.get_app_version }}</p>
                    </div>

                    <div class="tw-flex tw-flex-col">
                        <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">{% trans 'OS release' %}</div>
                        <p>{{app_install.release }}</p>
                    </div>
                    {% if agent_link %}
                        {% if app_install.serial_number %}
                            <div class="tw-flex tw-flex-col">
                                <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">{% trans 'Device ID' %}</div>
                                <div class="tw-font-sans tw-text-sm tw-content-center tw-basis-full">{{app_install.device_id }} {% if app_install.machine_model %}[{{ app_install.machine_model }}]{% endif %}</div>
                            </div>
                            <div class="tw-flex tw-flex-col">
                                <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">{% trans 'Serial number' %}</div>
                                <p>{{ app_install.serial_number }}</p>
                            </div>
                        {% else %}
                            <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">{% trans 'Device ID' %}</div>
                            <p>{{app_install.device_id }} {% if app_install.machine_model %}[{{ app_install.machine_model }}]{% endif %}</p>
                        {% endif %}
                    {% else %}
                        {% if app_install.serial_number %}
                            <div class="tw-flex tw-flex-col">
                                <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">{% trans 'Device ID' %}</div>
                                <p>{{app_install.device_id }} {% if app_install.machine_model %}[{{ app_install.machine_model }}]{% endif %}</p>
                            </div>

                            <div class="tw-flex tw-flex-col">
                                <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">{% trans 'Serial number' %}</div>
                                <p class="tw-break-all">{{ app_install.serial_number }}</p>
                            </div>
                        {% else %}
                            <div class="tw-flex tw-flex-col">
                                <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full">{% trans 'Device ID' %}</div>
                                <p>{{app_install.device_id }} {% if app_install.machine_model %}[{{ app_install.machine_model }}]{% endif %}</p>
                            </div>
                        {% endif %}
                    {% endif %}
                    </div>
                </div>

                <div class="white-box tw-border">
                    <div class="row row-in">

                        <div class="col-lg-3 col-sm-6 row-in-br">
                            <div class="col-in row">
                                {% if app_install.latest_local_user %}
                                    <div class="col-md-4 col-sm-4 col-xs-4"> <i class="ti-user"></i>
                                        <h5 class="text-muted vb">{% trans 'LAST USER' %}</h5>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-8 last-user">
                                        <h4 class="counter text-right m-t-15">{{ app_install.last_login_username }}<br>
                                            {% if app_install.latest_local_user.is_admin_account == True %}
                                                <span class='text-warning' style='opacity:0.75'>{% trans 'Local admin' %}</span>
                                                {% elif app_install.latest_local_user.is_admin_account == False %}
                                                <span class='text-success' style='opacity:0.75'>{% trans 'Local user' %}</span>
                                            {% endif %}
                                        </h4>
                                    </div>
                                {% else %}
                                    <div class="col-md-6 col-sm-6 col-xs-6"> <i class="ti-user"></i>
                                        <h5 class="text-muted vb">{% trans 'ENROLLED' %}</h5>
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-6">
                                        <h3 class="counter text-right m-t-15 text-success">{% trans 'YES' %}</h3>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-lg-3 col-sm-6 row-in-br">
                            <div class="col-in row">
                                <div class="col-md-6 col-sm-6 col-xs-6"> <i class="ti-download"></i>
                                    <h5 class="text-muted vb">{% trans 'LAST CHECK IN' %}</h5>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-6">
                                    <h4 class="counter text-right m-t-15 text-primary">{{ app_install.last_check_in|date:"d M Y, h:i a" }}</h4>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-sm-6 row-in-br  b-r-none">
                            <div class="col-in row">
                                <div class="col-md-6 col-sm-6 col-xs-6"> <i class="ti-check-box"></i>
                                    <h5 class="text-muted vb">{% trans 'CHECKS PASSING' %}</h5>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-6">
                                    <h3 class="counter text-right m-t-15 text-success">{{has_successful_check}}<span class='faded'>/{{responses_count}}</span></h3>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width:{{ssptr|unlocalize}}%"> <span class="sr-only">{{app_install.percent_complete}}% {% trans 'Complete (success)' %}</span> </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-sm-6 row-in-br b-0">
                            <div class="col-in row">
                                <div class="col-md-6 col-sm-6 col-xs-6"> <i class="ti-na"></i>
                                    <h5 class="text-muted vb">{% trans 'CHECKS FAILING' %}</h5>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-6">
                                    <h3 class="counter text-right m-t-15 text-danger">{{has_steps_to_secure}}<span  class='faded'>/{{responses_count}}</span></h3>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width: {{faiptr|unlocalize}}%"> <span class="sr-only">{{app_install.percent_uncomplete}}% {% trans 'Complete (success)' %}</span> </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <!-- .row -->
        <div class="row">
            {% if has_steps_to_secure > 0 %}
            <div class="col-lg-12">
                <div id="convert_img" class="white-box tw-border">
                    <h3 class="tw-font-sans tw-text-lg tw-font-semibold tw-my-0">{% trans "Steps to secure" %}</h3>
                    {% for response in responses %}
                    {% if not response.response and not response.is_manual_fix and not response.is_permanent_fix and not response.is_auto_fix %}
                    {% with response.get_steps_to_fix as steps_to_fix %}
                    <h3 class="title-accordion tw-flex tw-flex-col lg:tw-flex-row tw-gap-4 tw-py-3 lg:tw-px-2 lg:tw-gap-0 tw-justify-between lg:tw-items-center tw-border-b tw-border-dashed tw-m-0 tw-min-h-[64px] lg:hover:tw-bg-gray-50 tw-transition-colors">
                        <div class="tw-flex tw-items-center tw-gap-3">
                            <div class="tw-flex tw-items-center tw-justify-center tw-w-6 tw-h-6 tw-bg-red-100 tw-rounded-full">
                                <svg class="tw-w-[16px] tw-h-[16px] tw-fill-red-600">
                                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-x"></use>
                                </svg>
                            </div>

                            <div class="tw-flex tw-flex-col">
                                <span class="tw-text-sm tw-font-semibold tw-font-sans">{{response.app_check.title}}</span>
                                {% if response.app_check.extra_info %}
                                <div class="tw-flex tw-items-center tw-gap-1">
                                    {% autoescape off %}
                                    <small class="tw-text-gray-600 tw-text-sm">
                                        {{ response.app_check.extra_info|linebreaks }}
                                    </small>
                                    {% endautoescape %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        {% if response.app_check.code == 'OSSUPPORT' %}
                            {% if app_install.os_info %}
                                <small class="tw-text-gray-600 tw-text-sm">
                                    {{ app_install.os_info.release }}
                                    {{ app_install.os_info.build }}
                                    &nbsp;
                                </small>
                            {% endif %}
                        {% endif %}

                        <div data-html2canvas-ignore="true" class="tw-flex tw-flex-wrap tw-gap-2 tw-max-w-4xl">
                            {% if response.can_be_fixed_remotely %}
                                <button class="tw-h-8 tw-flex tw-w-full lg:tw-w-fit tw-text-nowrap tw-justify-center tw-items-center tw-gap-1 tw-px-4 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 remote-fix-button" type="button" url="{% call_method app_install 'url_resolve' org_id=organisation.secure_id user_uuid=app_install.app_user.uuid device_id=app_install.device_id serial_number=app_install.url_encoded_serial_number %}" id_response="{{response.id}}" check-id="{{response.app_check.id}}" show="{{response.is_auto_fix}}">
                                    <svg class="tw-w-4 tw-h-4">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-wand-sparkles"></use>
                                    </svg>
                                    {% trans 'Remote fix' %}
                                    <span class="spinner"><div class="cssload-speeding-wheel" style="width:15px;height:15px;"></span>
                                </button>
                            {% endif %}
                            <button class="tw-h-8 tw-flex tw-w-full lg:tw-w-fit tw-text-nowrap tw-justify-center tw-items-center tw-gap-1 tw-px-4 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-text-sm tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-" type="button" data-toggle="collapse" data-target="#accordion_{{response.app_check.id}}" aria-expanded="true" aria-controls="accordion">
                                {% trans 'Show steps to fix' %}
                                <svg class="tw-w-4 tw-h-4">
                                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-chevron-down"></use>
                                </svg>
                            </button>
                            {% if allow_to_manually_resolve %}
                                {% include 'partials/mark_as_resolved.html' with app_check=response.app_check %}
                            {% endif %}
                        </div>
                    </h3>

                    <div class="well panel-collapse collapse tw-prose tw-rounded-md tw-shadow-none tw-mt-2 tw-border" id="accordion_{{response.app_check.id}}">
                        {{ steps_to_fix|safe }}
                    </div>
                    {% endwith %}
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <div class="col-lg-12">
                <div id="convert_img" class="white-box tw-border">
                    <h3 class="tw-font-sans tw-text-lg tw-font-semibold tw-my-0">{% trans 'Successful checks' %}</h3>
                    {% for response in responses %}
                    {% if response.response or response.is_manual_fix or response.is_permanent_fix or response.is_auto_fix %}
                    <div tetst_id="{{response.app_check.id}}" class="tw-px-2">
                        {% if response.app_check.title == 'Anti-malware installed' or response.app_check.title == 'Firewall enabled' %}
                        <h3 class="success_qs tw-pb-2">
                            <div class="tw-flex tw-gap-3">
                                <div class="tw-flex tw-items-center tw-justify-center tw-w-6 tw-h-6 tw-bg-green-100 tw-rounded-full">
                                    <svg class="tw-w-[16px] tw-h-[16px] tw-fill-green-600">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-check"></use>
                                    </svg>
                                </div>
                                <span class="tw-text-sm tw-font-semibold tw-font-sans">{{response.app_check.title}}</span>
                            </div>

                            {% if response.inapp_response.product_name %}<small class="m-l-10" style="color: silver">{{ response.inapp_response.product_name }}&nbsp;</small>{% endif %}
                            {% if response.app_check.extra_info %}
                            <div class="m-l-40" style="display:block">
                                <i class="glyphicon glyphicon-info-sign text-danger m-t-10" style="color:blue; vertical-align:top"></i>
                                {% autoescape off %}
                                <div class="text-muted" style="display:inline">
                                    <small class="m-t-10" style="display:inline-block; vertical-align:top; color:silver">
                                        {{ response.app_check.extra_info|linebreaks }}
                                    </small>
                                </div>
                                {% endautoescape %}
                            </div>
                            {% endif %}
                            {% include 'partials/manually_resolved.html' %}
                        </h3>
                        {% else %}
                        <h3 class="success_qs tw-pb-2">
                            <div class="tw-flex tw-items-center tw-gap-3">
                                <div class="tw-flex tw-items-center tw-justify-center tw-w-6 tw-h-6 tw-bg-green-100 tw-rounded-full">
                                    <svg class="tw-w-[16px] tw-h-[16px] tw-fill-green-600">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-check"></use>
                                    </svg>
                                </div>

                                <div class="tw-flex tw-flex-col">
                                    <span class="tw-text-sm tw-font-semibold tw-font-sans">{{response.app_check.title}}</span>

                                    {% if response.app_check.extra_info %}
                                    <div>
                                        {% autoescape off %}
                                        <small class="tw-text-gray-600 tw-text-sm">
                                            {{ response.app_check.extra_info|linebreaks }}
                                        </small>
                                        {% endautoescape %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            {% include 'partials/manually_resolved.html' %}
                        </h3>
                        {% endif %}
                        {% if not response.response and response.is_auto_fix %}
                            <a class="permanent">{% trans "Remotely Resolved" %}</a>
                            <a class="revert revert_auto" data-url="{% call_method app_install 'url_revert' org_id=organisation.secure_id user_uuid=app_install.app_user.uuid device_id=app_install.device_id serial_number=app_install.url_encoded_serial_number %}" id_response="{{response.id}}" check-id="{{response.app_check.id}}">{% trans 'Revert' %}</a>
                        {% endif %}
                        <div class="clearfix"></div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
            {% if policies_support %}
                {% if agreed_policies or pending_policies or read_policies %}
                    <div class="col-lg-12">
                        <div id="convert_img" class="white-box tw-border">
                            {% if agreed_policies %}
                                <h3 class="box-title m-b-0">{% trans 'Agreed Policies' %}</h3>
                                {% for agreement in agreed_policies %}
                                    <h5><i class="glyphicon glyphicon-ok text-success"></i><span class="policy-name-agreed">{{ agreement.version.policy.name }}</span> <span class="m-l-10 policy-user"><i class="ti-user m-r-10"></i>{{ agreement.get_user }}</span> <span class="text-muted m-l-10"> v{{ agreement.version.version }} {% trans "agreed" %} {{ agreement.agreed_date }}</span></h5>
                                    <div class="clearfix"></div>
                                {% endfor %}
                            {% endif %}
                            {% if read_policies %}
                                <h3 class="box-title m-b-0">{% trans 'Read Policies' %}</h3>
                                {% for agreement in read_policies %}
                                    <div class="m-b-15" style="display: inline-block; float: left">
                                        <i class="glyphicon glyphicon-eye-open text-info"></i>
                                        <span class="policy-name-read">{{ agreement.version.policy.name }}</span> <span class="policy-user m-l-10"><i class="ti-user m-r-10"></i>{{ agreement.get_user }}</span> <span class="text-muted m-l-10"> v{{ agreement.version.version }} {% trans "read" %} {{ agreement.read_date }}</span>
                                    </div>
                                    <div class="clearfix"></div>
                                {% endfor %}
                            {% endif %}
                            {% if pending_policies %}
                                <h3 class="box-title m-b-0">{% trans 'Pending Policies' %}</h3>
                                {% for version in pending_policies %}
                                    <div class="m-b-15" style="display: inline-block; float: left">
                                        <i class="glyphicon glyphicon-remove text-danger"></i>
                                        <span class="policy-name-not-agreed">{{ version.policy.name }}</span>
                                        {% for user in version.pending_users %}
                                            <span class="policy-user m-l-10"><i class="ti-user m-r-10"></i>{{ user.name }}</span> {% if version.pending_users|length > 1 and not forloop.last %} <b>,</b>{% endif %}
                                        {% endfor %}
                                    </div>
                                    <a class="policy_unread">{% trans 'Not read and agreed' %}</a>
                                    <div class="clearfix"></div>
                                {% endfor %}
                            {% endif %}
                            <h5>{% trans 'For a full list of policy status across your organisation, visit' %} <a href="{% url 'smart_policies:policies-report' org_id=organisation.secure_id %}">{% trans 'policies report' %}</a></h5>
                        </div>
                    </div>
                {% endif %}
            {% elif organisation.is_billable %}
                <div class="col-lg-12">
                    <div class="white-box tw-border">
                        <div class="row">
                            <div class="col-lg-7">
                                <h3 class="box-title m-b-0">{% trans 'Smart Policies' %}</h3>
                                <div style="margin-top: 13%" class="text-center">
                                    <h4>{% trans 'Upgrade to CyberSmart Active Protect to manage your user policies digitally' %}.</h4>
                                    <h4>{% trans 'You can now distribute and sign policies - all within CyberSmart Active Protect!' %}</h4>
                                    <br/>
                                    <a href="{{ organisation.checkreport_url }}" class="btn btn-primary">{% trans 'Upgrade' %}</a>
                                </div>
                            </div>
                            <div class="col-lg-5">
                                <img src="{% static 'images/app2.png' %}">
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            {% if organisation.policies_support and app_install.supports_vss_section %}
                <div class="col-lg-12 m-b-20">
                    <div class="white-box tw-border">
                        {% waffle_flag_is_active_for_user "opswat_patch" request.user as is_opswat_patch_active %}
                        {% if is_opswat_patch_active %}
                            {% include "software_inventory/components/common/checkbox_persistent_state.html" %}
                            {# new installed software view lazy load #}
                            <div id="app-install-installed-software"
                                 hx-get="{{ app_install.url_installed_software }}"
                                 hx-trigger="revealed"
                                 hx-target="#app-install-installed-software"
                                 hx-swap="innerHTML"
                            >
                                <div id="installed-software-loader" class="cssload-speeding-wheel"></div>
                            </div>
                        {% else %}
                            <h3 class="m-b-0 tw-font-sans tw-font-semibold">{% trans 'Installed software' %}</h3>
                            {% if apps %}
                                <h5 class="tw-font-sans">{% trans "For a full list of software across the organisation, visit the" %} <a href="{% url 'dashboard:software-report' org_id=organisation.secure_id %}">{% trans 'software report' %}</a></h5>
                                {% include 'partials/device-page-software-filter.html' %}
                                <div class="row p-t-30 box-scroll" >
                                    {% include 'partials/software-table.html' with is_device_page=True %}
                                </div>
                            {% else %}
                            <div class="tw-flex tw-flex-col tw-gap-2 tw-bg-white tw-rounded-md tw-place-content-center tw-items-center tw-h-full tw-pt-5 tw-pb-10 tw-font-sans tw-text-center">
                                <p class="tw-font-semibold tw-text-black">{% trans 'Software data has not yet been received' %}</p>
                                <p class="tw-font-medium tw-text-gray-600 tw-p-0">{% trans 'Ensure CyberSmart Active Protect is installed and fully operational.' %}</p>
                                <p class="tw-font-medium tw-text-gray-600 tw-p-0">{% trans 'Initial scanning and reporting may take a short time after installation.' %}</p>
                            </div>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            {% endif %}

            <div class="col-lg-12 m-b-20">
                {% if app_install.system_info %}
                    <a class="btn btn-default" data-toggle="collapse" href="#collapseTechnicalInfo" role="button" aria-expanded="false" aria-controls="collapseExample">
                        <span class="btn-label"><i class="fa fa-info-circle"></i></span>
                        {% trans 'Technical information' %}
                    </a>
                {% endif %}
                <button url="{{ app_install.url_app_install_api }}" app_install_id="{{ app_install.id }}" org_url="{{ app_install.app_user.organisation.url }}" id="deactivate-button" class="btn btn-danger btn-md waves-effect waves-light pull-right" style="margin-left:2px; line-height: 34px; padding-top:0; padding-bottom:0;" type="button">
                    <span class="btn-label"><i class="fa fa-remove"></i></span>{% trans 'Deactivate this device' %}
                </button>
            </div>
        </div>
        <div class="collapse" id="collapseTechnicalInfo">
            <div class="white-box tw-border">
                        <pre class='tech-info'>
                            {{ app_install.system_info }}
                        </pre>
            </div>
        </div>
        {% csrf_token %}
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
    <div class="modal fade bs-example-modal-lg" id="cve-modal" tabindex="-1" role="dialog" aria-labelledby="cve-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                {% include "partials/loading.html" with show=True loader_class="loading-container-new" ellipsis_class="centered" loader_id="cve-id" %}
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="myLargeModalLabel">{% trans "CVE Summary" %}</h4>
                </div>
                <div class="modal-body">
                    <div class="cve-error-message" style="display: none">
                        <div class="alert alert-danger text-center">
                            We are unable to retrieve the CVE list at the moment. Try again later.
                        </div>
                    </div>
                    <div class="cve-list"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default waves-effect text-left" data-dismiss="modal">{% trans "Close" %}</button>
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    {% endif %}
    {% endblock %}
    {% block extra-js %}
        <script src="{% static 'js/rxjs.umd.min.js' %}"></script>
        <script src="{% static 'js/html2canvas.js' %}"></script>
        <script src="{% static 'js/user_attribution_assign.js' %}"></script>
        <script src="{% static 'js/modules/common/installer_modal.js' %}"></script>
        <script src="{% static 'js/software_inventory.js' %}"></script>
        <script src="{% static 'js/checkbox_state_manager.js' %}"></script>
        <script src="{% static 'js/dashboard/device-detail.js' %}"></script>
        <script src="{% static 'js/modules/patch-management/patch_button_and_selection.js' %}"></script>

        <script>
            $(document).ready(function() {
                const { BehaviorSubject } = rxjs;
                window.csApp = {
                    ...(window.csApp ?? {}),
                };
                const data = JSON.parse(document.getElementById('app_users_to_send_email_to').textContent);
                window.csApp.appUsersToSendEmailTo$ = new BehaviorSubject(data);
            });
            $("body").on('click', '.cve-summary-link', function() {
                $(".cve-error-message").hide();
                $("#cve-id").show();
                $(".cve-list").empty();
                $.ajax({
                    method: "GET",
                    url: $(this).data("href"),
                    success: function (data) {
                        $(".cve-list").html(data);
                    },
                    error: function (xhr, textStatus, error) {
                        $(".cve-error-message").show();
                    },
                    complete: function () {
                        $("#cve-id").hide();
                    },
                    timeout: 15000
                })
            });

            $('#cve-modal').on('hidden.bs.modal', function (e) {
                $(".modal-body cve").html("");
            })

            //=============================

            $(".revert_auto").click(function(e) {
                var csrfmiddlewaretoken = "{{ csrf_token }}";
                var id_response = $(this).attr('id_response');
                var check_id =$(this).attr('check-id');
                check_id = parseInt(check_id);
                $.ajax({
                    url: $(this).data('url'),
                    method: "POST",
                    data: {
                        'csrfmiddlewaretoken' : csrfmiddlewaretoken,
                        'check_id': check_id,
                        'id_response': id_response,
                        'automatically_resolved': 'true'
                    }
                }).done(function(response) {
                    location.reload();
                });
            });


            // ============================
            var name = '{{app_install.os.title }}' + '{{app_install.device_id }}' + '.pdf';
            var html = $('#wrapper');
            var cache_width = html.width();
            var a4 = [595.28, 841.89];
            $('#convert_img').find('img').each(function() {
                var image = this;
                getDataUri(image.src, function(dataUri) {
                    image.src = dataUri;
                });
            });
            function getDataUri(url, cb){
                var image = new Image();
                image.setAttribute('crossOrigin', 'anonymous'); //getting images from external domain
                image.onload = function () {
                    var canvas = document.createElement('canvas');
                    canvas.width = this.naturalWidth;
                    canvas.height = this.naturalHeight;
                    var ctx = canvas.getContext('2d');
                    ctx.fillStyle = '#fff';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    canvas.getContext('2d').drawImage(this, 0, 0);
                    cb(canvas.toDataURL('image/jpeg'));
                };
                image.src = url;
            }
            //==================================

            function openAccordion() {
                var acc = document.getElementsByClassName("accordion");
                for (var i = 0; i < acc.length; i++) {
                    var panel = acc[i].nextElementSibling;

                    if(panel.style.display !== 'block'){
                        panel.style.display = "block";
                    }
                }
            }

            function scrollTopClickButton (elem){
                elem.addClass('active');
                elem.attr('disabled','disabled');
                $("html, body").delay(50).animate({
                    scrollTop: $('#wrapper').offset().top
                }, 'slow');
                $("body").css("overflow", "hidden");
            }

            $('#beta-device-switch').change(function () {
                const url = new URL($('#sibling_install_url').val());
                location.href = url.href;
            });

        </script>
        {{ app_users_to_send_email_to|json_script:"app_users_to_send_email_to" }}

    {% endblock %}
