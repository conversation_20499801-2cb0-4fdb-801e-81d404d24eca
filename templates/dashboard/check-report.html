{% extends 'base.html' %}

{% load i18n %}
{% load static %}
{% load divide_tags %}
{% load appusers %}
{% load device_report %}
{% load beta_feature_enrolment %}
{% block head_title %}{% trans 'Check-Report' %}{% endblock %}
{% load waffle_tags %}

{% block sub_nav %}
    {% include 'partials/sub-header-org.html' %}
{% endblock %}

{% block extra-css %}
    <style type="text/css">
        .ex-steps >li.current {
            background: #03a9f3;
        }
        .question_child
        {height: 300px; overflow-y: auto; }
        .question_child.question_pass
        { border: 1px solid #1ac568; }
        .question_child.question_fail {
            border: 1px solid #fb9678;
        }

        .bulk_resolve {
            border-radius: 5px;
            background: #fff;
            text-decoration: none;
            text-transform: uppercase;
            font-weight: 400;
        }

        .success_circle {
            border: 1px solid #1ac568;
            border-radius: 0.8em;
            -moz-border-radius: 0.8em;
            -webkit-border-radius: 0.8em;
            color: #1ac568;
            display: inline-block;
            font-weight: bold;
            line-height: 1.6em;
            margin-right: 5px;
            margin-left: 10px;
            text-align: center;
            min-width: 1.7em;
            padding: 0 0.3em;
        }
        .failed_circle {
            border: 1px solid #fb9678;
            border-radius: 0.8em;
            -moz-border-radius: 0.8em;
            -webkit-border-radius: 0.8em;
            color: #fb9678;
            display: inline-block;
            font-weight: bold;
            line-height: 1.6em;
            margin-right: 5px;
            margin-left: 10px;
            text-align: center;
            min-width: 1.7em;
            padding: 0 0.3em;
        }
    </style>
{% endblock %}

{% block main-content %}
{% with organisation_has_any_enrolled_beta_features=organisation|has_any_enrolled_beta_features %}
<div class="container-fluid check-report-page">
    <div class="row">
        {% trans 'Reports' as page_title %}
        {% include "partials/header_default.html" with page_title=page_title page_icon="file-text" %}
    </div>

    <div class="row bg-title">
        <div style="position: relative;min-height: 700px;">
            {% include 'partials/access-restriction.html' with restricted_page='check-report' %}
            <div class="sttabs tabs-style-iconbox">
                {% include 'partials/report_nav.html' with report_view='dashboard:check_report' %}
                {% include 'partials/loading.html' with show=False %}
                <div class="content-wrap">
                    <section id="section-iconbox-1" class="content-current" style="padding-right: 0;padding-left:0">
                        <div class="col-lg-12" style="padding-right: 0;padding-left: 0">
                            <div class="white-box tw-border">
                                <div class="tw-flex tw-gap-1 tw-mb-4">
                                    <h2 class="tw-font-sans tw-text-lg tw-font-semibold tw-my-0 tw-text-black">{% trans 'Device report' %}</h2>
                                </div>
                                {% if not total_installs %}
                                <div class="no-policies tw-flex tw-flex-col tw-gap-2 tw-place-content-center tw-items-center tw-h-full tw-mt-4 tw-py-10 tw-font-sans tw-font-normal tw-border tw-border-gray-300 tw-border-dashed tw-rounded-md tw-text-center">
                                    <p class="tw-text-sm tw-font-semibold !tw-p-0">
                                        {% trans 'Security report unavailable' %}
                                    </p>
                                    {% if not organisation.is_bulk_enrollment_type %}
                                        <p class="tw-text-sm tw-text-gray-600 !tw-p-0 tw-font-medium">
                                            {% trans 'Check which devices are passing or failing security checks. ' %}
                                        </p>

                                        <p class="tw-text-sm tw-text-gray-600 !tw-p-0 tw-font-medium">
                                            {% trans "Active Protect must be installed on users' devices to generate a report." %}
                                        </p>

                                        <a href="{% url 'organisations:manage-users' org_id=organisation.secure_id %}" class="tw-mt-2 tw-bg-brand-600 hover:tw-bg-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out tw-h-9 tw-flex tw-items-center tw-justify-center tw-px-4 tw-py-3 tw-gap-1 tw-rounded-md tw-text-white tw-text-center tw-font-sans tw-font-medium tw-shadow-sm hover:tw-text-white focus:tw-ring-4 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0">
                                            {% trans 'Go to Manage users' %} 
                                        </a>
                                    {% else %}
                                        <p class="tw-text-sm tw-text-gray-600 !tw-p-0 tw-font-medium">
                                            {% trans 'Check which devices are passing or failing security checks.' %}
                                        </p>

                                        <p class="tw-text-sm tw-text-gray-600 !tw-p-0 tw-font-medium">
                                            {% trans 'Active Protect must be installed on devices to generate a report.' %}
                                        </p>

                                        <a href="{% url 'dashboard:organisation' org_id=organisation.secure_id %}" class="tw-mt-2 tw-bg-brand-600 hover:tw-bg-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out tw-h-9 tw-flex tw-items-center tw-justify-center tw-px-4 tw-py-3 tw-gap-1 tw-rounded-md tw-text-white tw-text-center tw-font-sans tw-font-medium tw-shadow-sm hover:tw-text-white focus:tw-ring-4 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0">
                                            {% trans 'Deploy Active Protect' %} 
                                        </a>
                                    {% endif %}   
                                </div>  
                                {% else %}
                                    <div class="col-sm-10 col-sm-offset-1">
                                        <div class="row">
                                            <div class="pull-left">
                                                <a onclick="filterChecks(this)" id="all_filter" class="btn btn-default check-report-device-type active-filter" type="button">
                                                    {% trans 'View all' %}
                                                </a>
                                                <a onclick="filterChecks(this)" id="desktop_filter" class="btn btn-default check-report-device-type" type="button">
                                                    {% trans 'Desktop' %}
                                                </a>
                                                <a onclick="filterChecks(this)" id="mobile_filter" class="btn btn-default check-report-device-type" type="button">
                                                    {% trans 'Mobile' %}
                                                </a>
                                                {% if enrolled_beta_features %}
                                                    {% for beta_feature in enrolled_beta_features %}
                                                        <a onclick="filterChecks(this)" id="{{ beta_feature.kind }}_filter" class="btn btn-default check-report-device-type" type="button">
                                                            {{ beta_feature.get_kind_display }}
                                                        </a>
                                                    {% endfor %}
                                                {% endif %}
                                            </div>
                                            <div class="pull-right">
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <span class="btn-label"><i class="fa fa-plus"></i></span>{% trans 'Export CSV Report' %}
                                                    </button>
                                                    <div class="dropdown-menu" style="text-align: center; padding: 5px">
                                                        <a class="dropdown-item csv-report-download-item" href="{% url 'dashboard:device-csv-report' organisation.secure_id %}">{% trans "Full" %}</a>
                                                        <a class="dropdown-item csv-report-download-item" href="{% url 'dashboard:device-csv-report' organisation.secure_id %}?only_failing_checks=yes">{% trans "Only failed checks" %}</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        </br>
    
                                        <div class="row panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                                            <div class="panel panel-default devices-table total-devices">
                                                <div class="panel-heading" role="tab" id="heading">
                                                    <h4 class="panel-title">
                                                        <div class="collapsed" aria-expanded="false" >
                                                            <div class="row">
                                                                <div class="col-md-4">{% trans "Total devices" %}: {{ nr_installs }} </div>
                                                                <div class="col-md-4">{% trans "Protected" %}: {{ count_passed_devices }} </div>
                                                                <div class="col-md-4">{% trans "With Security Issues" %}: {{ count_failed_devices }} </div>
                                                            </div>
                                                        </div>
                                                    </h4>
                                                </div>
                                            </div>
                                            <div class="panel panel-default devices-table">
                                                <div class="panel-heading p-0" role="tab" id="heading">
                                                    <h4 class="panel-title m-0">
                                                        <div class="collapsed" aria-expanded="false" >
                                                            <div class="row">
                                                                <div class="col-md-9 p-y-15 p-x-20 b-r-1">{% trans "Security Check" %}</div>
                                                                <div class="col-md-3 text-align-c p-y-15 p-x-20">{% trans "Failing" %}</div>
                                                            </div>
                                                        </div>
                                                    </h4>
                                                </div>
                                            </div>
                                            {% for check in report.checks %}
                                            {% with report.checks_stat|get_item:check.id as checks_stat %}
                                            {% with checks_stat|get_item:'failed' as failed and checks_stat|get_item:'passed' as passed %}
                                            {% if failed|add:passed > 0 %}
                                            <div class="panel panel-default devices-table">
                                                <div class="panel-heading p-0" role="tab" id="heading_{{ check.id }}">
                                                    <div class="panel-title m-0">
                                                        {% if failed > 0 or check.id in permanent_fixed_checks.keys %}<a class="collapsed" role="button" data-toggle="collapse" data-parent="#accordion" href="#{{ check.id }}" aria-expanded="false" aria-controls="{{ check.id }}">{% endif %}
                                                        <div class="row">
                                                            <div class="col-md-9 p-y-20 p-x-20 b-r-1">{{ check.title }}</div>
                                                            <div class="col-md-3 text-align-c p-y-20 p-x-20 {% if failed %}text-danger{% else %}text-success{% endif %}" style="text-align: end;" >{{ failed }}/{{ failed|add:passed }}</div>
                                                            {% if check.extra_info %}
                                                                {% autoescape off %}
                                                                    <div class="text-muted col-md-9 p-x-20 b-r-1 extra-info-check">
                                                                        <small >
                                                                            {{ check.extra_info|linebreaks }}
                                                                        </small>
                                                                    </div>
                                                                {% endautoescape %}
                                                            {% endif %}
                                                        </div>
                                                        {% if failed > 0 or check.id in permanent_fixed_checks.keys %}</a>{% endif %}
                                                    </div>
                                                </div>
                                                <div id="{{ check.id }}" class="panel-collapse collapse" role="tabpanel" aria-labelledby="heading_{{ check.id }}">
                                                    <div class="panel-body">
                                                        
                                                        <table class="table">
                                                            <thead>
                                                            <tr>
                                                                <th>{% trans "Device OS" %}</th>
                                                                <th>{% trans "Device hostname" %}</th>
                                                                <th>{% trans 'User' %}</th>
                                                                <th>{% trans 'Issue detected' %}</th>
                                                                <th>{% trans 'Last check-in' %}</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                                
                                                            </tbody>
                                                        </table>
                                                        {% if failed or passed or check.id in permanent_fixed_checks.keys and not check.indicator_aggregation %} {# do not allow to resolve trustd app checks #}
                                                            {# Button for modal to resolve failed checks #}
                                                            {% if failed %}
                                                                <div class="pull-right" style="padding-right: 20px">
                                                                    {% if not is_beta_device_type_filter %}
                                                                        {% comment %}
                                                                            We do not want to allow manually resolving any checks for Beta applications.
                                                                            Because Beta checks will not be visible on the "All" (non-filtered) view,
                                                                            hiding the button here suffices in preventing manual resolving for Beta applications. #}
                                                                        {% endcomment %}
                                                                        {% include 'partials/mark_as_bulk_resolved.html' with app_check=check %}
                                                                    {% endif %}
                                                                </div>
                                                            {% elif check.id in permanent_fixed_checks.keys %}
                                                                <div class="m-l-20" style="display:block">
                                                                    <h5 class="manually-resolved text-danger">{% trans 'Permanently resolved' %}</h5>
                                                                    <div class="manually-resolved-reason text-muted">
                                                                        <small style="color:silver">
                                                                            {% with fixed_check=permanent_fixed_checks|get_item:check.id %}
                                                                                {% trans 'Reason' %}: {% if fixed_check %}{% trans fixed_check %}{% else %}{% trans 'Other' %}{% endif %}
                                                                            {% endwith %}
                                                                        </small>
                                                                        <button class="btn btn-primary btn-sm permanently-resolve-undo m-l-10" data-question-pk="{{ check.id }}" data-question-title="{{ check.title }}">{% trans "Revert" %}</button>                                                                                        </div>
                                                                </div>
                                                            {% endif %}
                                                            {# end button #}
                                                        {% endif %}
                                                        <div class="m-t-20 preload-indicator">
                                                            <div class="cssload-speeding-wheel"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% endwith %}
                                            {% endwith %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                {% endif %}
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </div>
</div>

{% endwith %}
{% endblock %}

{% block extra-js %}
    <script type="text/javascript">

        $(".permanently-resolve-undo").click(function () {
            var question_pk = $(this).data('question-pk');
            var question_title = $(this).data('question-title');
            $.ajax({
                url: "{% url 'dashboard:device-bulk-permanent-resolve-remove' organisation.secure_id %}",
                method: "POST",
                data: {
                    'csrfmiddlewaretoken' : "{{ csrf_token }}",
                    'question_pk': question_pk
                }
            }).done(function(response) {
                if (response.status === 1) {
                    swal(question_title, gettext("Has been removed from permanently resolved"), "success");
                    location.reload();
                } else {
                    swal(gettext("Error"), gettext("Something went wrong"), "error");
                    location.reload();
                }
            });
            return false;
        });
        $('.question').each(function(index, element) {
            var _this = $(this);
            var question_child = _this.find('.question_child tr');
            if(question_child.length === 0){
                _this.remove();
            }
        });
        const queryString = window.location.search;
        const urlParams = new URLSearchParams(queryString);
        const filter_buttons = document.getElementsByClassName('check-report-device-type')
        // make all buttons as not active
        for (let filter_button of filter_buttons) {
            filter_button.classList.remove('active-filter');
        }
        // make button active if we are filtering by check type or just activate the "view all" button
        const device_type = urlParams.get('device_type')
        let current_button
        if (!device_type || device_type === 'all') {
            current_button = document.getElementById('all_filter')
        } else {
            current_button = document.getElementById(device_type + '_filter')
        }
        current_button.classList.add('active-filter');

        // when clicking on the view all, desktop or mobile filter buttons
        function filterChecks(element) {
            // show loading page
            const loading_container = document.getElementsByClassName('loading-container')[0]
            loading_container.style["display"] = "block";
            // perform search request
            urlParams.set('device_type', element.id.split('_filter')[0]);
            window.location.search = urlParams;
        }
        
        $('.devices-table .panel-heading').click((event) => {
            const $panelHeading = $(event.currentTarget);
            const checkId = $panelHeading.attr('id').substring(8);
            const $panelBody = $(`#${checkId}`)
            const $table = $panelBody.find('table tbody');
            const $loadingContainer = $panelBody.find('.preload-indicator')

            if ($loadingContainer.length === 0) {
                // if no loading container, then we have already loaded the data
                return;
            }
            
            $.ajax({
                url: `/api/v3/organisations/{{ organisation.secure_id }}/report-failed-responses/${checkId}`,
                method: "GET",
                data: {                    
                    'device_type': urlParams.get('device_type'),
                    'check_id': checkId
                }
            }).done((response) => {
                const dateFormatter = new Intl.DateTimeFormat('en', { year: 'numeric', month: 'short', day: '2-digit' });
                for (let device of response.failed_responses) {
                    const os = device.report__app_install__caption === 'Unknown' || !device.report__app_install__caption ?
                        device.report__app_install__os__title :
                        device.report__app_install__caption;
                    const user = "{{ organisation.is_bulk_enrollment_type }}" ?
                        device.last_os_user_username || device.last_os_user_domain :
                        device.report__app_install__app_user__email;

                    $table.append(`
                        <tr>
                            <td><i style="margin-right: 15px;" class="glyphicon glyphicon-remove text-danger"></i>${os}</td>
                            <td><a href="${device.app_install_url}">${device.report__app_install__hostname}</a></td>
                            <td>${user}</td>
                            <td>${dateFormatter.format(new Date(device.latest_status_changed))}</td>
                            <td>${dateFormatter.format(new Date(device.report__created))}</td>
                        </tr>
                    `);
                }
                $loadingContainer.remove();
            });
        });
    </script>
{% endblock %}
