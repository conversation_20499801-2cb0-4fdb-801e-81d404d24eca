{% extends "account/base.html" %}
{% load static %}
{% load i18n %}
{% load static account socialaccount %}
{% load waffle_tags %}
{% load divide_tags %}

{% block head_title %}{% trans "Create Organisation" %}{% endblock %}
{% block extra-css %}
    <link href="{% static 'plugins/custom-select/custom-select.css' %}" rel="stylesheet" type="text/css" />
    <!-- FormValidation -->
    <link type="text/css" rel="stylesheet" href="{% static 'css/onboarding.css' %}">
    <style>
        #id_size li{
            list-style-type: none;
        }
        .message_suggestion_answers {
            position: relative;
        }
        .suggestion_answers {
            width: 100%;
            position: absolute;
            /*top: 56px;*/
            left: 0;
            padding: 0 7px;
            display: none;
            cursor: pointer;
            z-index: 9;
        }
        .suggestion_answers ul {
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            overflow: auto;
            height: 200px;
        }
        .suggestion_answers ul li {
            padding: 10px;
            border-bottom: 1px solid #f5f5f5;
            text-align: left;
            list-style: none;
        }
        .suggestion_answers ul li:hover {
            background: #ddd;
        }

        #s2id_id_industry a {
            height: 100% !important;
        }
        .software-body {
            display: none;
        }
        .bundle-software-body {
            display: none;
        }
        
        .organisation_size_selector {
            padding-left: 0;
        }
        .cyber-insurance-info {
            color: #000000;
        }
        .new-and-recommended {
            display: flex;
            align-items: center;
        }
        .new-and-recommended::after {
            content: "{% trans "new & recommended" %}";
            display: inline-block;
            font-size: 11px;
            text-transform: uppercase;
            color: #fff;
            background: #16A34A;
            padding: 2px 8px;
            margin-left: 5px;
            border-radius: 20px;
        }
    </style>
{% endblock %}
{% block main-content %}
    {% waffle_flag_is_active_for_user 'qa_prepopulate_org_creation' request.user as allow_debug_prepopulation %}
    <div class="container-fluid">
        {% trans "Add organisation" as page_title %}
        {% include "partials/header_default.html" with page_title=page_title page_icon="box" %}

        {% if is_partner and partner.partner.bulk_create_organisations %}
            {% include "partials/bulk_create_organisations.html" %}
        {% endif %}

        <!-- /row -->
        <!-- .row -->
        <div class="row">
            <div class="col-md-9">
                <div class="panel panel-bordered settings">
                    <div class="tw-flex tw-max-w-[900px] tw-rounded-lg tw-p-10 tw-bg-white tw-border settings tw-font-sans">
                        <div class="col-md-12">
                            {% if form.non_field_errors %}
                                {% for err in form.non_field_errors %}
                                    <small class="text-danger">{{err}}</small>
                                {% endfor %}
                            {% endif %}
                            <form method="POST" enctype="multipart/form-data" action="{% url 'partners:create-organisation' %}" id="create-org-form">
                                {% csrf_token %}
                                <div class="form-group {% if form.name.errors %}has-error{% endif %}">
                                    <label class="control-label">{{ form.name.label }}</label>
                                    <input type="text" required="" class="form-control on-invalid-text" name="name" {% if form.name.value %}value="{{ form.name.value }}"{% endif %}/>
                                    {% if form.name.errors %}
                                        {% for err in form.name.errors%}
                                            <small class="text-danger">{{err|safe}}</small>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                <div class="form-group {% if form.industry.errors %}has-error{% endif %}" style="position: relative">
                                    <label class="control-label">{{ form.industry.label }}</label>
                                    <input 
                                        type="text"
                                        class="form-control input_tmp on-invalid-text"
                                        required
                                        name="industry"
                                        style="position: absolute;width: 100%;bottom: 0;left: 0;z-index: 0; border-color: none;"
                                        value="{% if form.industry.value %}{{ form.industry.value }}{% endif %}"
                                    />
                                    {{ form.industry }}
                                    {% if form.industry.errors %}
                                        {% for err in form.industry.errors%}
                                            <small class="text-danger">{{err|safe}}</small>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                <div class="form-group {% if form.industry_description.errors %}has-error{% endif %}" id="id_industry_description_form" >
                                    <label for="id_industry_description" class="control-label">{{ form.industry_description.label }} *</label>
                                    <textarea maxlength="{{ form.fields.industry_description.max_length }}" class="form-control" name="industry_description" id="id_industry_description">{% if form.industry_description.value %}{{ form.industry_description.value }}{% endif %}</textarea>
                                    {% if form.industry_description.errors %}
                                        {% for err in form.industry_description.errors%}
                                            <small class="text-danger">{{err|safe}}</small>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                <div class="form-group {% if form.org_website_domain.errors %}has-error{% endif %}" id="id_website_domain_form" >
                                    <label for="id_org_website_domain" class="control-label">{{ form.org_website_domain.label }}</label>
                                    <input type="text" class="form-control" name="org_website_domain" id="id_org_website_domain" placeholder="example.com"/>
                                    {% if form.org_website_domain.errors %}
                                        {% for err in form.org_website_domain.errors%}
                                            <small class="text-danger">{{err}}</small>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                <div class="form-group {% if form.pre_populated_admins.errors %}has-error{% endif %}">
                                    <label class="control-label">{% trans 'Add additional Dashboard users (optional)' %}</label>
                                    {% if has_insurer_distributor %}
                                        <br>
                                        <label class="control-label">{% trans 'Email' %}</label>
                                    {% endif %}
                                    {% if custom_admins %}
                                        <div class="new_admins_input" data-count="{{ custom_admins|length }}">
                                            {% for admin in custom_admins %}
                                                <div class="row admin_row" style="margin-bottom: 10px;">
                                                    <div class="col-md-9">
                                                        <input type="email" class="form-control new_admin_input" id="new_admin_{{ forloop.counter }}" name="custom_admins" value="{{ admin }}"/>
                                                    </div>
                                                    {% if not has_insurer_distributor %}
                                                        <div class="col-md-3 last_addbutton">
                                                            <input id="remove_new_admin_{{ forloop.counter }}" style="float: left; margin-left: 2px; line-height: 36px; padding: 0px 8px; {% if forloop.counter == 1 %}display: none;{% endif %}" class="btn btn-default remove_row" value="- {% trans "Remove" %}" type="button">
                                                            <input id="add_new_admin_{{ forloop.counter }}" style="float:left; margin-left:2px; line-height: 36px; padding-top:0; padding-right:8px; padding-left:8px; padding-bottom:0;" class="btn btn-default add_row" value="+ {% trans "Add Row" %}" type="button">
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <div class="new_admins_input" data-count="1">
                                            <div class="row admin_row" style="margin-bottom: 10px;">
                                                <div class="col-md-9">
                                                    <input type="email" class="form-control new_admin_input" id="new_admin_1" name="custom_admins"/>
                                                </div>
                                                {% if not has_insurer_distributor %}
                                                    <div class="col-md-3 last_addbutton">
                                                        <input id="remove_new_admin_1" style="float: left; margin-left: 2px; line-height: 36px; padding: 0px 8px; display: none;" class="btn btn-default remove_row" value="- {% trans "Remove" %}" type="button">
                                                        <input id="add_new_admin_1" style="float:left; margin-left:2px; line-height: 36px; padding-top:0; padding-right:8px; padding-left:8px; padding-bottom:0;" class="btn btn-default add_row" value="+ {% trans "Add Row" %}" type="button">
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}
                                    {% if form.pre_populated_admins.errors %}
                                        {% for err in form.pre_populated_admins.errors%}
                                            <small class="text-danger">{{err}}</small>
                                        {% endfor %}
                                    {% endif %}
                                    {% if not has_insurer_distributor %}
                                        <div class="text-primary">{% trans 'Note: You and all' %} <a href="{% url "partners:manage-users" %}" target='_blank' style='text-decoration: underline'>{% trans 'partner users' %}</a> {% trans 'will have automatic admin access to this organisations dashboard' %}</div>
                                    {% endif %}
                                </div>
                                {% if has_insurer_distributor %}
                                    <div class="form-group {% if form.first_name.errors %}has-error{% endif %}">
                                        <label class="control-label">{{ form.first_name.label }}</label>
                                        <div class="row">
                                            <div class="col-md-9">
                                                <input type="text" required="" class="form-control new_admin_input" name="first_name" {% if form.first_name.value %}value="{{ form.first_name.value }}"{% endif %}/>
                                            </div>
                                        </div>
                                        {% if form.first_name.errors %}
                                            {% for err in form.first_name.errors%}
                                                <small class="text-danger">{{err}}</small>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                    <div class="form-group {% if form.last_name.errors %}has-error{% endif %}">
                                        <label class="control-label">{{ form.last_name.label }}</label>
                                        <div class="row">
                                            <div class="col-md-9">
                                                <input type="text" required="" class="form-control" name="last_name" {% if form.last_name.value %}value="{{ form.last_name.value }}"{% endif %}/>
                                            </div>
                                        </div>
                                        {% if form.last_name.errors %}
                                            {% for err in form.last_name.errors%}
                                                <small class="text-danger">{{err}}</small>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                {% endif %}
                                <div class="form-group {% if form.size.errors %}has-error{% endif %}" style="overflow: hidden">
                                    <div class="col-sm-8 col-md-8 col-lg-7 organisation_size_selector">
                                        <label class="control-label" for="id_size">{{ form.size.label }}</label>
                                        <select class="form-control on-invalid-select" name="{{ form.size.name }}" id="id_size" required>
                                            <option value="" {% if not form.size.value %}selected{% endif %}>{% trans 'Select the approximate staff size' %}</option>
                                            {% for choice in form.size.field.choices %}
                                                <option value="{{ choice.0 }}" {% if choice.0 == form.size.value %}selected{% endif %}>{{ choice.1 }}</option>
                                            {% endfor %}
                                        </select>
                                        {% if form.size.errors %}
                                            {% for err in form.size.errors %}
                                                <small class="text-danger">{{ err }}</small>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                </div>
                                {% if partner.partner.is_bundle_enabled and form.bundle and form.setting_bundles_enabled %}
                                    <div id="bundle-form">
                                        <div class="form-group">
                                            <h5>{% trans "Please select your CyberSmart plan" %}</h5>
                                            <div class="radio radio-info">
                                                <input type="radio" id="id_cybersmart_complete_bundle" name="bundle" value="{{ form.CYBERSMART_COMPLETE_BUNDLE }}" {% if form.bundle_selected == form.CYBERSMART_COMPLETE_BUNDLE %}checked{% endif %} class="plan-input !tw-mt-2">
                                                <label for="id_cybersmart_complete_bundle">
                                                    <p class="new-and-recommended"><b>{% trans "Complete" %}</b></p>
                                                    <p class="text-muted">{% trans "Cyber Essentials, Cyber Essentials Plus, CyberSmart Active Protect, £250k insurance and more..." %}</p>
                                                </label>
                                            </div>
                                            <div class="radio radio-info">
                                                <input type="radio" id="id_cybersmart_bundle" name="bundle" value="{{ form.CYBERSMART_BUNDLE }}" {% if form.bundle_selected == form.CYBERSMART_BUNDLE %}checked{% endif %} class="plan-input">
                                                <label for="id_cybersmart_bundle">
                                                    <p><b>{% switch "rename_bundle_to_plan" %}{% trans "Core" %}{% else %}{% trans "Bundle" %}{% endswitch %}</b></p>
                                                    <p class="text-muted">{% trans "Cyber Essentials, CyberSmart Active Protect, £100k insurance" %}</p>
                                                </label>
                                            </div>
                                            {% if not partner.partner.is_v5_only_available %}
                                                <!-- No Bundle -->
                                                <div class="radio radio-info">
                                                    <input type="radio" id="id_no_bundle" name="bundle" value="{{ form.NO_BUNDLE }}" {% if form.bundle_selected == form.NO_BUNDLE %}checked{% endif %} class="plan-input">
                                                    <label for="id_no_bundle">
                                                        <p><b>{% switch "rename_bundle_to_plan" %}{% trans "Custom" %}{% else %}{% trans "No plan" %}{% endswitch %}</b></p>
                                                        <p class="text-muted">{% trans "Choose which features you want (e.g. Cyber Essentials only)" %}</p>
                                                    </label>
                                                </div>
                                            {% endif %}
                                        </div>

                                        <div class="bundle-body" style="display: none;">
                                            {% include 'partials/bundle_whats_included.html' with bundle_whats_included_id='bundle-whats-included-complete' initial_class='' is_complete=True %}
                                            {% include 'partials/bundle_whats_included.html' with bundle_whats_included_id='bundle-whats-included' initial_class='hidden' %}
                                            <div class='text-primary col-lg-12 m-t-5 m-b-30'>
                                                {% trans 'Note: Organisation size affects the plan price. Refer to your distributor or partner portal for pricing.' %}
                                            </div>
                                            <div class="form-group m-t-10">
                                                <!-- Term -->
                                                <label>{% trans "Term" %}</label><br/>
                                                <button class="btn btn-primary term-button" id="bundle-term-annual-btn">{% trans 'Annual' %}</button>
                                                <button class="btn btn-secondary term-button" id="bundle-term-monthly-btn">{% trans 'Monthly' %}</button>
                                                <input type="checkbox" name="{{ form.bundle_price_monthly.name }}" {% if form.bundle_price_monthly.value %} checked {% endif %} id="bundle-monthly-switch" style="display:none;">

                                                <!-- Commitment -->
                                                <div id="bundle-commitment" style="display:none;">
                                                    <br><label class="m-t-20">{{ form.bundle_commitment.label }}</label><br/>
                                                    {% for choice in form.bundle_commitment.field.choices %}
                                                        <div class="radio radio-info">
                                                            <input class="bundle-input bundle-input-commitment" type="radio" name="{{ form.bundle_commitment.name }}" id="radio_bundle_commitment_{{choice.0}}" value="{{choice.0}}" {% if choice.0|slugify == form.bundle_commitment.value|slugify %} checked {% endif %}>
                                                            <label for="radio_bundle_commitment_{{choice.0}}">{{choice.1}} </label>
                                                        </div>
                                                    {% endfor %} {% if form.bundle_commitment.errors %} {% for err in form.bundle_commitment.errors%}
                                                    <small class="text-danger">{{err}}</small> {% endfor %}<br> {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endif %}
                                <div class="all-else-below-plans" {% if partner.partner.is_bundle_enabled %}style="display: none;"{% endif %}>
                                {% if not has_insurer_distributor %}
                                    {% if has_partner and partner.partner.is_bundle_enabled and form.custom_bundle and form.setting_bundles_enabled %}
                                        <div id="custom-bundle-form">
                                            <label>{{ form.custom_bundle.label }}</label><br/>
                                            <input class="js-switch" type="checkbox" name="custom-bundle-switch" id="custom-bundle-switch" {% if form.custom_bundle.value or form.custom_bundle.errors or form.custom_bundle_price.value or form.custom_bundle_price.errors or form.custom_bundle_software_licenses.value or form.custom_bundle_software_licenses.errors %}checked {% endif %} {% if not request.user.is_impersonate %}disabled{% endif %}>
                                            {% if not request.user.is_impersonate %}
                                                <div class='text-primary m-t-15'>
                                                    {% switch "rename_bundle_to_plan" %}
                                                        {% trans 'Note: If you have agreed a custom plan with your account manager, please ask them to setup this organisation' %}
                                                    {% else %}
                                                        {% trans 'Note: If you have agreed a custom bundle with your account manager, please ask them to setup this organisation' %}
                                                    {% endswitch %}
                                                </div>
                                            {% endif %}
                                            <div class="custom-bundle-body" {% if form.custom_bundle.errors or form.custom_bundle_price.value or form.custom_bundle_price.errors or form.custom_bundle_software_licenses.value or form.custom_bundle_software_licenses.errors %}style="display: block" {% endif %}>
                                                <br>
                                                <div class="form-group m-t-10">
                                                    <!-- Bundle -->
                                                    {% for choice in form.custom_bundle.field.choices %}
                                                        <div class="radio radio-info">
                                                            <input class="custom-bundle-input" type="radio" name="{{ form.custom_bundle.name }}" id="radio_bundle_{{choice.0}}" value="{{choice.0}}" {% if choice.0|slugify == form.custom_bundle.value|slugify %} checked {% endif %}>
                                                            <label for="radio_bundle_{{choice.0}}">{{choice.1}} </label>
                                                        </div>
                                                    {% endfor %} {% if form.custom_bundle.errors %} {% for err in form.custom_bundle.errors%}
                                                    <small class="text-danger">{{err}}</small> {% endfor %}<br> {% endif %}

                                                    <!-- Custom Bundle Price -->
                                                    <br><label for="bundle-price-input">{{ form.custom_bundle_price.label }} <a class="text-muted" id="bundle-vat-switch" style="cursor: pointer"></a></label><br>
                                                    <input type="checkbox" name="{{ form.custom_bundle_price_vat_included.name }}" id="vat-included" style="display:none" checked>
                                                    <input class="form-control input-md input-money" type="text"name="{{ form.custom_bundle_price.name }}" id="bundle-price-input" style="width:55%" required="" {% if form.custom_bundle_price.value %}value="{{ form.custom_bundle_price.value }}"{% endif %} />
                                                    {% if form.custom_bundle_price.errors %} {% for err in form.custom_bundle_price.errors%}
                                                        <small class="text-danger">{{err}}</small> {% endfor %}{% endif %}
                                                    <br><span class="text-muted"><span id="bundle-price-no-vat" class="font-bold"></span> + <span class="font-bold" id="bundle-price-plus-vat"></span> VAT (@ 20%)</span><span class="font-bold" id="bundle-price-vat-total"></span></span>
                                                </div>
                                                <!-- Term -->
                                                <label>{% trans "Term" %}</label><br/>
                                                <button class="btn btn-primary custom-term-button" id="custom-bundle-term-annual-btn">{% trans 'Annual' %}</button>
                                                <button class="btn btn-secondary custom-term-button" id="custom-bundle-term-monthly-btn">{% trans 'Monthly' %}</button>

                                                <input type="checkbox" name="{{ form.bundle_price_monthly.name }}" id="custom-bundle-monthly-switch" style="display:none;">
                                                <!-- Commitment -->
                                                <div id="custom-bundle-commitment" style="display:none;">
                                                    <br><label class="m-t-20">{{ form.bundle_commitment.label }}</label><br/>
                                                    {% for choice in form.bundle_commitment.field.choices %}
                                                        <div class="radio radio-info">
                                                            <input class="bundle-input custom-bundle-input-commitment" type="radio" name="{{ form.bundle_commitment.name }}" id="radio_bundle_commitment_{{choice.0}}" value="{{choice.0}}" {% if choice.0|slugify == form.bundle_commitment.value|slugify %} checked {% endif %}>
                                                            <label for="radio_bundle_commitment_{{choice.0}}">{{choice.1}} </label>
                                                        </div>
                                                    {% endfor %} {% if form.bundle_commitment.errors %} {% for err in form.bundle_commitment.errors%}
                                                    <small class="text-danger">{{err}}</small> {% endfor %}<br> {% endif %}
                                                </div>
                                                <!-- Software -->
                                                <br><label class="m-t-30">{% trans 'Include Apps' %}</label><br>
                                                <input class="js-switch" type="checkbox" name="custom-bundle-software-switch" id="custom-bundle-software-switch" {% if form.custom_bundle_software_licenses.value or form.custom_bundle_software_licenses.errors %}checked{% endif %}>
                                                <div class="bundle-software-body" class="m-b-30" {% if form.custom_bundle_software_licenses.value or form.custom_bundle_software_licenses.errors %}style="display:block"{% endif %}>
                                                    <br><label for="bundle-software-licenses-input">{{ form.custom_bundle_software_licenses.label }}</label><br>
                                                    <input class="form-control input-md input-quantity" type="number" name="{{ form.custom_bundle_software_licenses.name }}" id="bundle-software-licenses-input" style="width:55%" {% if form.custom_bundle_software_licenses.value %}value="{{ form.custom_bundle_software_licenses.value }}"{% endif %} />
                                                    {% if form.custom_bundle_software_licenses.errors %} {% for err in form.custom_bundle_software_licenses.errors%}
                                                        <small class="text-danger">{{err}}</small> {% endfor %} {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    {% endif %}
                                    {% if form.setting_certifications_auto_selected %}
                                        <div id="certs-form" class="m-t-30">
                                            <div class="form-group {% if form.certifications.errors %}has-error{% endif %}" style="overflow: hidden">
                                                <label>{{ form.certifications.label }}</label><br/>
                                                <label>{{ form.certifications.field.choices.0.1 }}</label><br/>
                                                <div class="certifications-body" {% if form.certifications.errors %}style="display: block"{% endif %}>
                                                    <div class="m-t-20">
                                                    {% for choice in form.certifications.field.choices %}
                                                        <div class="radio radio-info">
                                                            <input class="certifications-input" type="hidden" name="{{ form.certifications.name }}" id="radio_certifications_{{choice.0}}" value="{{choice.0}}" {% if choice.0|slugify == form.certifications.value|slugify %} checked {% endif %}>
                                                        </div>
                                                    {% endfor %}
                                            </div>
                                        </div>
                                    {% else %}
                                        {% if form.certifications %}
                                        <div id="certs-form" class="m-t-30" {% if form.custom_bundle.value or form.custom_bundle.errors or form.custom_bundle_price.value or form.custom_bundle_price.errors or form.custom_bundle_software_licenses.value or form.custom_bundle_software_licenses.errors %}style="display: none"{% endif %}>
                                            <div class="form-group {% if form.certifications.errors %}has-error{% endif %}" style="overflow: hidden">
                                                <div class="certifications-body" {% if form.certifications.errors %}style="display: block"{% endif %}>
                                                    <h5 class="control-label">{% trans "Please select your options" %}</h5>
                                                    <div class="m-t-20">
                                                        {% for choice in form.certifications.field.choices %}
                                                            <div class="radio radio-info">
                                                                <input class="certifications-input" type="radio" name="{{ form.certifications.name }}" id="radio_certifications_{{choice.0}}" value="{{choice.0}}" {% if choice.0|slugify == form.certifications.value|slugify %} checked {% endif %}>
                                                                <label for="radio_certifications_{{choice.0}}">{{choice.1}} </label>
                                                            </div>
                                                        {% endfor %}
                                                         {% if form.certifications.errors %} {% for err in form.certifications.errors%}
                                                            <small class="text-danger">{{err}}</small> {% endfor %} {% endif %}
                                                        {% if partner.partner.distributor_is_brigantia %}
                                                            <div class='text-danger'>
                                                                {% trans 'Note: adding Cyber Essentials or ' %}
                                                                    {% trans 'Privacy Toolbox' %}
                                                                {% trans ' will appear on your next invoice' %}
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endif%}
                                    {% endif %}
                                    <div id="software-form">
                                        <div id="software-form-header" {% if form.custom_bundle.value or form.custom_bundle.errors or form.custom_bundle_price.value or form.custom_bundle_price.errors or form.custom_bundle_software_licenses.value or form.custom_bundle_software_licenses.errors %}style="display:none;"{% endif %}>
                                            <label>CyberSmart Active Protect</label><br/>
                                            <input class="js-switch" type="checkbox" name="software-switch" id="software-switch" {% if form.smart_policies.errors or form.bulk_install.errors or form.email_message.errors or form.approved_domain.errors or form.bulk_install.value or form.custom_bundle_software_licenses.value or form.custom_bundle_software_licenses.errors or form.setting_cap_auto_selected %} checked {% endif %} {% if form.setting_cap_auto_selected %} disabled {% endif %}>
                                        </div>
                                        <div class="software-body" {% if form.smart_policies.errors or form.po_number.errors or form.bulk_install.errors or form.email_message.errors or form.approved_domain.errors %}style="display: block" {% endif %}>
                                            <br/>
                                            <div class="form-group {% if form.smart_policies.errors %}has-error{% endif %} m-t-10" style="overflow: hidden; display: none;">
                                                <div class="">
                                                    {% for choice in form.smart_policies.field.choices %}
                                                        <div class="radio radio-info">
                                                            <input class="software-input" type="radio" name="{{ form.smart_policies.name }}" id="radio_smart_policies{{choice.0}}" value="{{choice.0}}" {% if choice.0|yesno:"True,False" == form.smart_policies.value %} checked {% endif %}>
                                                            <label for="radio_smart_policies{{choice.0}}">{{choice.1}} </label>
                                                        </div>
                                                    {% endfor %} {% if form.smart_policies.errors %} {% for err in form.smart_policies.errors%}
                                                    <small class="text-danger">{{err}}</small> {% endfor %} {% endif %}
                                                </div>
                                            </div>
                                            <div class="form-group {% if form.bulk_install.errors %}has-error{% endif %}">
                                                <label class="control-label">{{ form.bulk_install.label }}</label>
                                                {% for choice_bulk in form.bulk_install.field.choices %}
                                                    <div class="radio radio-info">
                                                        <input class="bulk_install" type="radio" name="{{ form.bulk_install.name}}" id="bulk-deployment-{{choice_bulk.0}}" value="{{choice_bulk.0}}" {% if choice_bulk.0|slugify == form.bulk_install.value|slugify %} checked {% endif %}>
                                                        <label for="bulk-deployment-{{choice_bulk.0}}">{{choice_bulk.1}} </label>
                                                    </div>
                                                {% endfor %}
                                                {% if form.bulk_install.errors %}
                                                    {% for err in form.bulk_install.errors%}
                                                        <small class="text-danger">{{err}}</small>
                                                    {% endfor %}
                                                {% endif %}
                                                {% if is_partner %}
                                                    <div class="help_silver_text" id="id_bulk_install_no" style="display: none">
                                                        {% trans 'Individual enrolment is designed for organisations with user-managed devices or no centralised IT management. This method will add a unique message within the download link sent to team members so they can install CyberSmart Active Protect and report back with one click' %}
                                                    </div>
                                                    <div class="help_silver_text" id="id_bulk_install_yes" style="display: none">
                                                        {% trans 'Bulk deployment is designed for organisations with centralised device management (via Group policy, Mobile Device Management or RMM). This method creates a single package for organisation wide deployment' %}
                                                    </div>
                                                {% else %}
                                                    <div style="background: #edf1f5;padding: 5px 10px;">{{form.bulk_install.field.help_text}}</div>
                                                {% endif %}
                                            </div>
                                            <div {% if form.bulk_install.value %}style="display: none"{% endif %} class="form-group {% if form.email_message.errors %}has-error{% endif %} message_suggestion_answers">
                                                <label class="control-label">{{ form.email_message.label }}<span class="text-muted text-right"> (<span id="char-count">0</span>/{% blocktrans with max_length=form.fields.email_message.max_length %}{{ max_length }} characters{% endblocktrans %})</span></label>
                                                <textarea class="form-control email-input" name="email_message" maxlength="{{ form.fields.email_message.max_length }}">{% if not is_prod and allow_debug_prepopulation %}[QA] Pre-filled placeholder. {% endif %}{% if form.email_message.value %}{{ form.email_message.value }}{% endif %}</textarea>
                                                {% if form.email_message.errors %}
                                                    {% for err in form.email_message.errors%}
                                                        <small class="text-danger">{{err|safe}}</small>
                                                    {% endfor %}
                                                {% endif %}
                                                {% switch "disable-email-suggestion-answers" %}{% else %}
                                                    <div class="suggestion_answers">
                                                        <ul class="select_suggestion_answers">
                                                            <li>{% trans "Hello all, you are required to install this software as part of our compliance implementation. It doesn't take long, it's only a few clicks, I recommend you do this now. The secret word is [SECRET]" %}</li>
                                                            <li>{% trans 'This new software is required for our certification. Can everyone please install this asap. The secret word is [SECRET]' %}</li>
                                                            <li>{% trans 'We are currently undergoing our certification assessment. This software will help us all pass the criteria. Please download and install. The secret word is [SECRET]' %}</li>
                                                            <li>{% trans 'Our compliance project is happening! You need to download this software for us to pass! Thank you. The secret word is [SECRET]' %}</li>
                                                            <li>{% trans 'Please download this software. Everyone is required to install this in order for us to achieve our accreditation. The secret word is [SECRET]' %}</li>
                                                        </ul>
                                                    </div>
                                                {% endswitch %}
                                            </div>
                                            <div class="form-group" {% if not form.bulk_install.value %}style="display: none"{% endif %} id="approved-domain-block">
                                                <label class="control-label">{{ form.approved_domain.label }}</label>
                                                <input type="text" class="form-control" name="approved_domain" id="id_approved_domain" placeholder="example.com" value="{{ form.approved_domain.value }}"/>
                                                {% if form.approved_domain.errors %}
                                                    {% for err in form.approved_domain.errors%}
                                                        <small class="text-danger">{{err}}</small>
                                                    {% endfor %}
                                                {% endif %}
                                                <div class="help_silver_text">
                                                    {% trans 'Set the client company email domain to allow users to enrol their mobile devices.' %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Purchase Order only for direct partners -->
                                    {% if partner.partner.is_direct_partner and form.setting_po_enabled %}
                                        <div class="row m-b-10 m-t-20"><label>{% trans "Purchase Order" %}</label> </div>
                                        <div class="row">
                                            <div class="col-md-10" {% if form.certifications.errors %}style="display: none"{% endif %}>
                                                <label for="po-number-input">{{ form.po_number.label }} </label>
                                                <input class="form-control po-input po-number" type="text" name="{{ form.po_number.name }}" id="po-number-input" {% if form.po_number.value %}value="{{ form.po_number.value }}"{% endif %} />
                                                {% if form.po_number.errors %}
                                                    {% for err in form.po_number.errors %}
                                                        <small class="text-danger m-l-5">{{err}}</small>
                                                    {% endfor %}
                                                {% endif %}
                                                <br>
                                                <label for="po-file-input">{{ form.po_file.label }} <span class="text-muted">{% trans '(optional)' %}</span></label>
                                                <input type="file" accept="application/pdf" class="form-control po-input" name="{{ form.po_file.name }}" id="po-file-input" >
                                            </div>
                                        </div>
                                        <div class='text-primary m-t-15 m-b-20'>{% trans 'Note: We recently added PO numbers to help us with invoicing. You can also add a PDF later in the Subscriptions page.' %}</div>
                                    {% endif %}
                                    {% if partner.partner.can_input_coupon_during_org_creation %}
                                        <div class="row m-b-10 m-t-20"><label>{% trans "Redeem Voucher" %}</label> </div>
                                        <div class="group group_stripe_code" >
                                            <div class="row">
                                                <div class="col-md-10">
                                                    <label for="stripe_code">{% trans "Voucher Code" %}</label>
                                                    <input id="stripe_code" name="coupon" class="form-control po-input m-l-30" type="text" {% if form.coupon.value %}value="{{ form.coupon.value }}"{% endif %} placeholder="{% trans "Enter voucher code here" %}" />
                                                    <div id="coupon_message"></div>
                                                    <div class="applied_coupon text-success m-t-10"></div>
                                                </div>
                                            </div>
                                        </div>
                                        {% if form.coupon.errors %}
                                            {% for err in form.coupon.errors %}
                                                <small class="text-danger m-l-5">{{err}}</small>
                                            {% endfor %}
                                        {% endif %}
                                    {% endif %}
                                {% endif %}
                                {% if not is_prod and form.setting_issue_ce_certification_debug and allow_debug_prepopulation %}
                                    <div class="form-group">
                                        <label for="id_issue_ce_certification_debug">{% trans "[QA] Issue Cyber Essentials Certification" %}</label>
                                        {{form.issue_ce_certification_debug}}
                                    </div>
                                {% endif %}
                                {% if not is_prod and form.setting_issue_ce_and_cep_certification_debug and allow_debug_prepopulation %}
                                    <div class="form-group">
                                        <label for="id_issue_ce_and_cep_certification_debug">{% trans "[QA] Issue Cyber Essentials and Cyber Essentials Plus Certifications" %}</label>
                                        {{form.issue_ce_and_cep_certification_debug}}
                                    </div>
                                {% endif %}
                                {% if not is_prod and allow_debug_prepopulation %}
                                    <div class="form-group">
                                        <label for="create_app_installs_debug">{% trans "[QA] Fake App Users To Create" %}</label>
                                        <input type="number" class="form-control po-input" name="{{ form.create_app_installs_debug.name }}" id="create_app_installs_debug" value="{{ form.create_app_installs_debug.value|default_if_none:'' }}" min="0">
                                        {% if form.create_app_installs_debug.errors %}
                                            {% for error in form.create_app_installs_debug.errors %}
                                                <small class="text-danger m-l-5">{{ error }}</small>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                {% endif %}

                                </div>
                                <div class="form-group m-t-40">
                                    <button type="submit" id="submit-btn" name="action" class="btn btn-primary">{% trans "Create Organisation" %}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
{% endblock %}
{% block extra-js %}
    {% waffle_flag_is_active_for_user 'qa_prepopulate_org_creation' request.user as allow_debug_prepopulation %}
    <script src="{% static 'plugins/custom-select/custom-select.min.js' %}" type="text/javascript"></script>
    <script src="{% static 'js/jquery.inputmask.bundle.min.js' %}"></script>
    <script src="{% static 'js/modules/dashboard/organisation-create.js' %}" type="text/javascript"></script>
    <script>
        $(document).ready(function() {
            const allow_debug_prepopulation = {% if not is_prod and allow_debug_prepopulation %}true{% else %}false{% endif %};
            function handleBulkSwitch () {
                var bulkInstall = $('input[name="bulk_install"]:checked').val() === "True";
                const website_domain_elem = document.getElementById('id_org_website_domain')
                const approved_domain_elem = document.getElementById('id_approved_domain')
                if (bulkInstall) {
                    // add input from website domain to approved domain when bulk install
                    approved_domain_elem.value = website_domain_elem.value
                    // show respective help text when bulk install
                    $("#id_bulk_install_no").fadeOut("fast", function () {
                        $("#id_bulk_install_yes").fadeIn();
                        $("#approved-domain-block").slideDown();
                        $(".message_suggestion_answers").slideUp();
                        if ($("#software-switch").is(":checked")) {
                            $("#id_approved_domain").prop("required", true);
                        if (allow_debug_prepopulation) {
                                $("#id_approved_domain").val(Array(20).fill().map(() => 'abcdefghijklmnopqrstuvwxyz0123456789'[Math.floor(Math.random() * 36)]).join('') + '.com');
                            }
                            $(".email-input").prop("required", false);
                        }
                    });
                } else {
                    // clear input from domain when individual deployment
                    approved_domain_elem.value = null
                    // show respective help text when individual deployment
                    $("#id_bulk_install_yes").fadeOut("fast", function () {
                        $("#id_bulk_install_no").fadeIn();
                        $("#approved-domain-block").slideUp();
                        $(".message_suggestion_answers").slideDown();
                        if ($("#software-switch").is(":checked")) {
                            $("#id_approved_domain").prop("required", false);
                            $(".email-input").prop("required", true);
                        }
                    });
                }
            }
            $('input[name="bulk_install"]').ready(handleBulkSwitch);
            $('input[name="bulk_install"]').change(handleBulkSwitch);

            $("#new_admin_1").focusout(function () {
                $("#id_approved_domain").val($(this).val().replace(/.*@/, ""));
            });

            const industrySelect = $("#id_industry");
            setIndustryFieldRules(industrySelect.val());

            industrySelect.change(function() {
                $('.input_tmp').remove();

                setIndustryFieldRules($(this).val());
            });
            // Set the first value for industrySelect
            if (allow_debug_prepopulation) {
                industrySelect.val('APRE').trigger('change');
                // Set debug value for organisation name field
                const currentDateTime = new Date().toISOString().replace('T', ' ').slice(0, 16);
                $('input[name="name"]').val(`[QA {{request.user.username}}] ${currentDateTime}`);
                // Set debug value for bulk install field
                $('#bulk-deployment-False').prop('checked', true);
                // Initialize the default organisation size
                // Get all options and select the 3rd one (index 2)
                const sizeSelect = $('#id_size');
                const thirdOption = sizeSelect.find('option:eq(2)').val();
                sizeSelect.val(thirdOption).trigger('change');
            }

            function setIndustryFieldRules (currentIndustryValue) {
                const $IndustryDescription = $('#id_industry_description_form');
                if (currentIndustryValue === 'OTHE') {
                    $IndustryDescription.show();
                    $IndustryDescription.find('textarea').prop('required', true);
                } else {
                    $IndustryDescription.hide()
                    $IndustryDescription.find('textarea').prop('required', false).val('')
                }
            }
            $("textarea[name='email_message']").click(function(e) {
                $('.suggestion_answers').css('display','block');
            });
            $(".suggestion_answers ul li").click(function() {
                $(".suggestion_answers").css('display','none');
                var _data = $(this).text();
                var message_suggestion_answers = $(".message_suggestion_answers textarea");
                message_suggestion_answers.val(_data).change();
            });
            $('.message_suggestion_answers textarea').bind('input', function() {
                $(".suggestion_answers").css('display','none');
            });
            // CS-636
            $('.message_suggestion_answers textarea').bind('input', function() {
                $(".suggestion_answers").css('display','none');
            });
            if($('#radio_True').attr('checked') == 'checked'){
                $('.message_suggestion_answers').css('display','none');
            };
            $('body').on('click', '#radio_False', function() {
                $('.message_suggestion_answers textarea[name="email_message"]').text('');
                $('.message_suggestion_answers').slideDown();
            });
            $('body').on('click', '#radio_True', function() {
                $('.message_suggestion_answers').slideUp();
            });

            $('body').on('click', '.remove_row', function () {
                var admin_rows = $('.new_admins_input').data('count');
                if (admin_rows > 1) {
                    var container = $(this).parent().parent();
                    container.remove();
                    $('.new_admins_input').data('count', admin_rows - 1);
                    if (admin_rows == 1) {
                        $('.remove_new_admin_1').style('display', 'none');
                    }
                }
            });
            $('body').on('click', '.add_row', function () {
                var admin_rows = $('.new_admins_input').data('count');
                $('.new_admins_input').data('count', admin_rows + 1);
                var container = $(this).parent().parent();
                var newContainer = container.clone();
                newContainer.find('.new_admin_input').attr('id', 'new_admin_' + (parseInt(admin_rows) + 1));
                newContainer.find('.new_admin_input').val('');
                {#newContainer.find('.new_admin_input').attr('name', 'new_admin_' + (parseInt(admin_rows) + 1));#}
                newContainer.find('.add_row').attr('id', 'add_new_admin_' + (parseInt(admin_rows) + 1));
                newContainer.find('.remove_row').attr('id', 'remove_new_admin_' + (parseInt(admin_rows) + 1));
                newContainer.find('.remove_row').css('display', 'block');
                newContainer.appendTo('.new_admins_input')
            });

            const software_switch = $('#software-switch');
            const custom_bundle_switch = $('#custom-bundle-switch');
            const bundle_software_switch = $("#custom-bundle-software-switch");
            const bundle_monthly_switch = $("#bundle-monthly-switch");
            const custom_bundle_monthly_switch = $("#custom-bundle-monthly-switch");
            const bundle_term_annual_btn = $('#bundle-term-annual-btn');
            const bundle_term_monthly_btn = $('#bundle-term-monthly-btn');

            const showAllElseBelowPlans = () => {
                $('.all-else-below-plans').slideDown();
            }

            var bundle_monthly = ("{{ form.bundle_price_monthly.value }}" === "True");
            if ($("#software-switch").is(":checked")){
                if (bundle_monthly) custom_switch_term();
            }

            bundle_term_monthly_btn.on('click', function() {
                if (!bundle_monthly_switch.is(':checked')) switch_term();
            })
            bundle_term_annual_btn.on('click', function() {
                if (bundle_monthly_switch.is(':checked')) switch_term();
            })

            function switch_term(skipCheckboxClick) {
                bundle_term_monthly_btn.toggleClass('btn-secondary btn-primary');
                bundle_term_annual_btn.toggleClass('btn-secondary btn-primary');
                if (!skipCheckboxClick) bundle_monthly_switch.click();
                // commitment only for monthly
                // TODO: remove this when implementing annual commitment
                // not going to add logic here to change commitment for term
                // will just assume 1 year for annual term in backend
                if (bundle_monthly_switch.is(':checked')) {
                    $('#bundle-commitment').slideDown();
                    $('.bundle-input-commitment').prop("required", true);
                    // Select the 1 Year commitment radio button by default
                    $('#radio_bundle_commitment_1.bundle-input-commitment').prop('checked', true);
                }
                else {
                    $('#bundle-commitment').slideUp();
                    $('.bundle-input-commitment').prop("required", false);
                    // Remove the selected value from all radio buttons in the bundle-commitment group
                    $('input[name="bundle_commitment"]').prop('checked', false);
                };
            }

            const switches = [
                bundle_software_switch,
                custom_bundle_switch,
                software_switch,
            ];
            const switchHandlersMap = new Map();
            switchHandlersMap.set(custom_bundle_switch, handleCustomBundleSwitchChange);
            switchHandlersMap.set(bundle_software_switch, handleBundleSoftwareSwitchChange);
            switchHandlersMap.set(software_switch, handleSoftwareSwitchChange);

            const certificationOptions = $('.certifications-input');
            const certificationOptionsWithoutNone = certificationOptions.not('[value="no-certification"]');

            const submitBtn = $('#submit-btn');

            switches.forEach(function (item) {
                item.change(function () {
                    enableOrDisableSubmitBtn();

                    // concrete handler call
                    const handler = switchHandlersMap.get(item);
                    if (handler) {
                        handler.apply(item, arguments)
                    }
                })
            })

            function enableOrDisableSubmitBtn() {
                {% if partner.partner.is_bundle_enabled %}
                    const anyPlanSelected = $('.plan-input:checked').length > 0;
                    if (!anyPlanSelected) {
                        submitBtn.prop('disabled', true);
                        return;
                    }
                {% endif %}
                const anyCertificationSelected = certificationOptionsWithoutNone.is(":checked");
                const areAllSwitchesDisabled = switches.every(function (element) {
                    return !element.is(":checked");
                }) && !anyCertificationSelected;
                if (areAllSwitchesDisabled) {
                    submitBtn.prop('disabled', true);
                } else {
                    submitBtn.prop('disabled', false);
                }
            }

            function handleSoftwareSwitchChange () { //todo
                if ($(this).is(":checked")) {
                    $('.software-body').slideDown('slow');
                } else {
                    $('.software-body').slideUp('slow');
                    $('.software-input').prop('checked', false);
                    $('.bulk_install').prop('checked', false);
                    $('.help_silver_text').hide();
                    $('.email-input').val('');
                    $("#id_approved_domain").prop("required", false);
                    $(".email-input").prop("required", false);
                }
            }

            function handleBundleSwitchChange () {
                onBundleSwitchChange($(this).is(":checked"))
            }

            function onBundleSwitchChange(checked) {
                if (checked) {
                    $('.bundle-body').slideDown('slow');
                    // Deselect software and custom bundle if bundle is selected
                    // Note that we do not deselect certifications here
                    // as that would unselect the default partner value on initial load.
                    if (software_switch.prop('checked')) software_switch.trigger('click');
                    if (custom_bundle_switch.prop('checked')) custom_bundle_switch.trigger('click');

                    $('#certs-form').hide();
                    $('#custom-bundle-form').hide();
                    $('#software-form').show();
                    if (!software_switch.prop('checked')) software_switch.trigger('click');
                    $('#software-form-header').hide();
                } else {
                    // deselect bundle software on deselect bundle
                    $('.bundle-body').slideUp('slow');
                    $('.bundle-input').prop('checked', false);
                    $('#certs-form').show();
                    $('#custom-bundle-form').show();
                    $('.custom-bundle-body').hide();
                    if (software_switch.prop('checked')) software_switch.trigger('click');
                    $('#software-form-header').show();
                }
            }

            function handleCustomBundleSwitchChange () {
                if ($(this).is(":checked")) {
                    $('.custom-bundle-body').slideDown('slow');
                    // deselect certs & software if custom bundle selected
                    $('#certs-form').hide();
                    $('.certifications-input').prop('checked', false);
                    $('#software-form').hide();
                    if (software_switch.prop('checked')) software_switch.trigger('click');
                    $('#bundle-form').hide();
                } else {
                    // Reset and hide custom bundle options
                    if (bundle_software_switch.prop('checked')) bundle_software_switch.trigger('click');
                    $('.custom-bundle-body').slideUp('slow');
                    $('.custom-bundle-input').prop('checked', false);
                    // Show standard options again
                    $('#certs-form').show();
                    $('#software-form').show();
                    $('#bundle-form').show();
                }
            }

            function handleBundleSoftwareSwitchChange () {
                if ($(this).is(":checked")) {
                    $('.bundle-software-body').slideDown('slow');
                    $('#software-form').show();
                    if (!software_switch.prop('checked')) software_switch.trigger('click');
                    $('#software-form-header').hide();
                } else {
                    $('.bundle-software-body').slideUp('slow');
                    if (software_switch.prop('checked')) software_switch.trigger('click');
                    $('#software-form').hide();
                    $('#software-form-header').show();
                }
            }

            function custom_switch_term() {
                $('#custom-bundle-term-monthly-btn').toggleClass('btn-secondary btn-primary');
                $('#custom-bundle-term-annual-btn').toggleClass('btn-secondary btn-primary');
                custom_bundle_monthly_switch.click();
                
                if (custom_bundle_monthly_switch.is(':checked')) {
                    $('#custom-bundle-commitment').slideDown();
                    $('.custom-bundle-input-commitment').prop("required", true);
                    // Select the 1 Year commitment radio button by default
                    $('#radio_bundle_commitment_1.custom-bundle-input-commitment').prop('checked', true);
                } else {
                    $('#custom-bundle-commitment').slideUp();
                    $('.custom-bundle-input-commitment').prop("required", false);
                    // Remove the selected value from all radio buttons in the bundle-commitment group
                    $('input[name="bundle_commitment"]').prop('checked', false);
                }
            }
            function show_complete_bundle() {
                onBundleSwitchChange(true);
                $("#bundle-whats-included-complete").show();
                $("#bundle-whats-included").hide();
            };
            function show_bundle() {
                onBundleSwitchChange(true);
                $("#bundle-whats-included-complete").hide();
                $("#bundle-whats-included").show();
            };
            $('.term-button').click(function(e) {
                e.preventDefault();
                if (bundle_software_switch.is(':checked')) switch_term();
            });
            $('.custom-term-button').click(function(e) {
                e.preventDefault();
                custom_switch_term();
            });
            handleInitialFormState();

            function handleInitialFormState() {
                $('.custom-bundle-body').hide();
                if (bundle_monthly_switch.is(':checked')) {
                    switch_term(true);
                };

                if ($('#id_cybersmart_complete_bundle').is(':checked')) {
                    show_complete_bundle();
                }
                if ($('#id_cybersmart_bundle').is(':checked')) {
                    show_bundle();
                }
                if ($('.plan-input:checked').length > 0) {
                    // if any plan is selected, show the section below the plans
                    showAllElseBelowPlans();
                }

                const activeSwitches = switches.filter(item => item.is(':checked'));
                if (!activeSwitches.length) {
                    // If no switches are enabled, enable cybersmart copmlete bundle
                    const cybersmart_complete_bundle = $('#id_cybersmart_complete_bundle');
                    // check if it exists
                    if (cybersmart_complete_bundle.length) {
                        enableOrDisableSubmitBtn();
                    }
                }
                activeSwitches.forEach(function (activeSwitch) {
                    if (activeSwitch) {
                        activeSwitch.prop('checked', false);
                        setTimeout(() => {
                            activeSwitch.prop('checked', true);
                        })
                    }

                    const handler = switchHandlersMap.get(activeSwitch);

                    if (handler) {
                        setTimeout(() => { // next tick
                            handler.call(activeSwitch)
                        })
                    }
                })
                $('.bulk_install[checked]').click();
            }

            // hide the bundle switch so it can't be unchecked for v5 bundle only partners
            // also hide the bundle options (not future proof!)
            {% if partner.partner.is_v5_only_available %}
                $('#bundle-form .switchery').css('display', 'none');
                $('#radio_bundle_1, label[for=radio_bundle_1]').css('display', 'none');
            {% endif %}

            // money input
            let moneyInput = $(".input-money");
            const moneyPrefix = "£ ";
            let initialMoneyConfig = {
                alias: "decimal",
                autoGroup: true,
                clearMaskOnLostFocus: false,
                digits: 2,
                digitsOptional: false,
                placeholder: "0",
                allowPlus: false,
                allowMinus: false,
                prefix: moneyPrefix,
                rightAlign: false,
                removeMaskOnSubmit: true,
                defaultValue: '0.00'
            };
            // initialize input mask
            moneyInput.inputmask(initialMoneyConfig);
            // VAT switch
            let includeVAT = "{{ form.custom_bundle_price_vat_included.value }}" === "True";
            function switchVAT() {
                includeVAT = !includeVAT;
                $("#vat-included").val(includeVAT);
                displayVAT();
            }
            // set VAT values
            function getMoneyValue(val) {return parseFloat(val.substring(2))};
            function formatMoney(val) {return moneyPrefix + (Math.round(val * 100) / 100).toFixed(2);}
            let bundlePriceWithoutVAT = '';
            function displayVAT() {
                if (includeVAT) $("#bundle-vat-switch").text("{% trans 'incl. VAT' %}");
                else $("#bundle-vat-switch").text("{% trans 'excl. VAT' %}");
                let bundlePrice = getMoneyValue($(".input-money").val() || '0.00') || 0;
                let bundlePriceVAT;
                if (includeVAT) {
                    bundlePriceWithoutVAT = bundlePrice / 1.2 || 0;
                    bundlePriceVAT = bundlePrice - bundlePriceWithoutVAT;
                    $("#bundle-price-no-vat").text(formatMoney(bundlePriceWithoutVAT));
                    $("#bundle-price-vat-total").text("");
                } else {
                    let totalPrice = bundlePrice * 1.2;
                    bundlePriceVAT = totalPrice - bundlePrice || 0;
                    bundlePriceWithoutVAT = bundlePrice;
                    $("#bundle-price-no-vat").text(formatMoney(bundlePrice));
                    $("#bundle-price-vat-total").text(
                        " = " + formatMoney(totalPrice) + " total"
                    );
                }
                $("#bundle-price-plus-vat").text(formatMoney(bundlePriceVAT));
            }
            displayVAT();
            // bind functions
            $("#bundle-vat-switch").on('click', switchVAT);
            $("#bundle-price-input").on('keydown input', displayVAT);


            let quantityInput = $(".input-quantity");
            let initialQuantityConfig = {
                alias: "integer",
                autoGroup: true,
                clearMaskOnLostFocus: false,
                placeholder: "",
                allowPlus: false,
                allowMinus: false,
                rightAlign: false
            };
            // initialize input mask
            quantityInput.inputmask(initialQuantityConfig);

            $(".software-input").change(function () {
                $("#bulk-deployment-False").prop("checked", true);
                // trigger change event
                $('input[name="bulk_install"]').trigger("change");
            });
            $('textarea[name="email_message"]').on("change paste keyup input", function (evt) {
                let charCount = evt.target.value.length;
                $('#char-count').html(charCount);
            })
            $("#create-org-form").submit(function () {
                if (!coupon_valid) {
                    $("#stripe_code").val("");
                }
                // base case to avoid recursion
                if (moneyInput.val() == parseFloat(formatMoney(bundlePriceWithoutVAT).substring(2))) return true;
                if (includeVAT) {
                    // submit price without VAT - it is added in chargebee
                    moneyInput.val(formatMoney(bundlePriceWithoutVAT));
                    // trigger remove mask on submit again for new value
                    $("#create-org-form").submit();
                }
            });

            let coupon_valid = false;
            // if there is a prepopulated coupon code show it
            if ("{{ form.coupon.value }}" !== 'None') {
                $("#stripe_code").val("{{ form.coupon.value }}");
                setTimeout(function () {
                    $("#stripe_code").trigger("keyup");
                }, 500)
            }

            $("#stripe_code").on("keyup", function() {
                const coupon_message = $('#coupon_message');
                const coupon = $(this).val();
                let params = {
                    // here we add a random string, since the coupon used should be applicable to the invoice
                    // and not a specific plan
                    "plans_id": ['random_string'],
                    "coupon": coupon
                };
                if (coupon) {
                    $.ajax({
                        url: '{% url "signup:get-coupon" %}?' + $.param(params),
                        method: 'GET',
                        success: function(c) {
                            if (!c.valid) {
                                couponError();
                                coupon_valid = false;
                            } else {
                                $.each(c.applicable_to, function (index, plan_id) {
                                    if (c.percent_off) {
                                        $(".applied_coupon").text(c.percent_off + '% discount');
                                    } else {
                                        $(".applied_coupon").text('- £' + (c.amount_off / 100) + ' discount');
                                    }
                                })
                                $('#coupon_message').find('div').remove();
                                coupon_valid = true;
                            }
                        },
                        error: function(xhr,errmsg,err) {
                            couponError();
                            $(".applied_coupon").text('')
                        }
                    });
                } else {
                    coupon_message.find('span').remove();
                }
            });
            $('input[id="id_no_bundle"]').change(function() {
                if ($(this).is(":checked")) {
                    onBundleSwitchChange(false);
                    enableOrDisableSubmitBtn();
                    showAllElseBelowPlans();
                }
            });
            $("#id_cybersmart_complete_bundle").change(function() {
                if ($(this).is(":checked")) {
                    show_complete_bundle();
                    showAllElseBelowPlans();
                } 
            });
            $("#id_cybersmart_bundle").change(function() {
                if ($(this).is(":checked")) {
                    show_bundle();
                    showAllElseBelowPlans();
                } 
            });

            


            // Since certificationOptionsWithoutNone is a jQuery object, use jQuery each() instead of forEach
            certificationOptions.each(function() {
                $(this).on('change', function() {
                    enableOrDisableSubmitBtn(); 
                });
            });
        });
    </script>
    <script type="text/javascript">
        $(".select2").select2();
    </script>
{% endblock %}