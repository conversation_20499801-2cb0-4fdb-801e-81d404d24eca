{% extends 'base.html' %}

{% load i18n %}
{% load static %}
{% load divide_tags %}

{% block head_title %}{{ manage_users_page_name }}{% endblock %}

{% block sub_nav %}
    {% include 'partials/sub-header-org.html' %}
{% endblock %}

{% block extra-css %}
    <link href="{% static 'plugins/custom-select/custom-select.css' %}" rel="stylesheet" type="text/css" />
    <link type="text/css" rel="stylesheet" href="{% static 'css/jsgrid.min.css' %}" />
    <link type="text/css" rel="stylesheet" href="{% static 'css/jsgrid-theme.min.css' %}" />
    <link type="text/css" rel="stylesheet"  href="{% static 'css/onboarding.css' %}">
    <link type="text/css" rel="stylesheet"  href="{% static 'css/sync-users.css' %}">
{% endblock %}
{% block main-content %}
    <div class="container-fluid">
        {% include "breadcrumbs/organisation-stuff.html" with page_name=manage_users_page_name %}
        <div class="row bg-title">
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                <h4 class="page-title"><img class="icon icon-title" alt="Users" src="{% static 'icons/page-title/Users.svg' %}"> {{ manage_users_page_name }}</h4>
            </div>
        </div>
        <!-- /row -->
        {% if not is_bulk %}
            <div class="white-box">
                <div class="row row-in">
                    <div class="emailuser_dash col-lg-3 col-sm-6 row-in-br">
                        <div class="col-in row">
                            <div class="col-md-7 col-sm-7 col-xs-7"> <i class="ti-user"></i>
                                <h5 class="text-muted vb">{% trans 'USERS ENROLLED' %}</h5>
                            </div>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <h3 class="counter text-right m-t-15 text-muted" custom-loaded-data="{{total_app_users}}" id="users-enrolled-counter">0</h3>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <!-- <div class="progress">
                                  <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width: 58.3333333%"> <span class="sr-only">40% Complete (success)</span> </div>
                                </div> -->
                            </div>
                        </div>
                    </div>
                    <div class="emailuser_dash col-lg-3 col-sm-3 row-in-br">
                        <div class="col-in row">
                            <div class="col-md-6 col-sm-6 col-xs-6"> <i class="ti-email"></i>
                                <h5 class="text-muted vb">{% trans "EMAILS SENT" %}</h5>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-6">
                                <h3 class="counter text-right m-t-15 text-muted"><span custom-loaded-data="{{ total_app_users }}" id="email-sent">0</span><span class="op">/<span custom-loaded-data="{{total_app_users}}" id="email-sent-all">0</span></span></h3>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class="descriptionemailsent">
                                    <p>{% trans 'To enrol your team, we need to send each of them a custom email' %}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="emailuser_dash col-lg-3 col-sm-6 row-in-br">
                        <div class="col-in row">
                            <div class="col-md-6 col-sm-6 col-xs-6"> <i class="ti-download"></i>
                                <h5 class="text-muted vb">{% trans 'USERS INSTALLED' %}</h5>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-6">
                                <h3 class="counter text-right m-t-15 text-primary"><span custom-loaded-data="{{installed_app_users}}">0</span><span class="op">/<span custom-loaded-data="{{total_app_users}}" id="users-installed-all">0</span></span></h3>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-primary" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="0%" custom-loaded-data="{{installed_app_users|divide:total_app_users}}%" custom-loaded-data-target='{"css": "width"}'></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="emailuser_dash col-lg-3 col-sm-6 row-in-br  b-r-none">
                        <div class="col-in row">
                            <div class="col-md-7 col-sm-7 col-xs-7"> <i class="ti-check-box"></i>
                                <h5 class="text-muted vb">{% trans 'USERS PASSING' %}</h5>
                            </div>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <h3 class="counter text-right m-t-15 text-success" custom-loaded-data="{{user_passing}}">0 </h3>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">

                            </div>
                        </div>
                    </div>
                    <div class="emailuser_dash col-lg-3 col-sm-6 row-in-br b-0">
                        <div class="col-in row">
                            <div class="col-md-6 col-sm-6 col-xs-6"> <i class="ti-na"></i>
                                <h5 class="text-muted vb">{% trans 'USERS FAILING' %}</h5>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-6">
                                <h3 class="counter text-right m-t-15 text-danger" custom-loaded-data="{{user_failing}}">0</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
        <!-- row -->
        <div class="row">
            <div class="col-lg-12">
                {% include "partials/access-restriction.html" with restricted_page="manage-users" %}
                <div class="white-box align-center">
                    <!-- .row -->
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="white-box">
                                <div id="exampleValidator" org_id="{{organisation.secure_id}}" >
                                    <div class="clearfix"></div>
                                    <table id="demo-foo-addrow" class="table table-bordered table-hover toggle-circle"
                                           data-page-size="20">
                                        <thead>
                                        <tr>
                                            <th data-hide="phone">{% trans 'Dashboard Role' %}</th>
                                            <th data-sort-initial="true" data-hide="phone">{% trans 'Name' %}</th>
                                            <th data-toggle="true">{% trans 'Email' %}</th>
                                            {% if not is_bulk %}
                                                <th style="text-align: center;">{% trans 'Enrol' %}</th>
                                                <th style="text-align: center;" data-hide="phone">{% trans 'Installed' %}?</th>
                                            {% endif %}
                                            <th data-sort-ignore="true" style="text-align: center;">{% trans 'Edit' %}</th>
                                        </tr>
                                        </thead>
                                        <div class="form-inline padding-bottom-15">
                                            <div class="row">
                                                <div class="col-sm-6 add-users-buttons">
                                                    <button id="add-users-button" class="btn btn-primary" type="button" data-toggle="modal" data-target="#add-new-users-modal">
                                                        <span class="btn-label"><em class="fa fa-plus"></em></span>{% trans 'Add new users' %}
                                                    </button>
                                                    {% if can_add_fakes %}
                                                        <button type="button" data-toggle="modal" data-target="#fakeAppUsersModal" class="btn has-spinner btn-default btn-outline btn-md">
                                                            <span class="btn-label"><i class="fa fa-plus"></i></span>{% trans "Add Fake Users" %}
                                                        </button>
                                                        {% include "app_users/add_fake_users.html" %}
                                                    {% endif %}
                                                    {% if not is_bulk and not organisation.is_vodafone_customer %}
                                                        <button id="sync-users-button" class="btn btn-default btn-outline btn-md waves-effect waves-light sync-users-button" type="button" data-toggle="modal" data-target="#sync-users-modal">
                                                            {% trans 'Sync Settings' %}
                                                        </button>
                                                    {% endif %}
                                                </div>
                                                <div class="col-sm-6 text-right m-b-20">
                                                    <div class="form-group">
                                                        <input id="demo-input-search2" type="text" placeholder="{% trans "Search" %}" class="form-control" autocomplete="off">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <tbody>
                                        {% for app_user in app_users %}
                                            <tr id="{{app_user.id}}" uuid="{{app_user.uuid}}" is_admin="{% if app_user.is_admin %}admin_{% else %}user_{% endif %}">
                                                <td class="is__admin">
                                                    {% if app_user.id in extra_app_users_id %}
                                                        <span class="label label-table label-warning label-outline" style="display: inline;">{% trans "User limit reached" %}</span>
                                                    {% else %}
                                                        {% if app_user.is_admin %}
                                                            <span class="label label-table label-primary cl_admin" style="display: inline;">{% trans "Dashboard Admin" %}</span>
                                                        {% else %}
                                                            <span class="label label-table label-primary label-outline cl_user" style="display: inline;">{% trans 'User' %}</span>
                                                        {% endif %}
                                                    {% endif %}
                                                </td>
                                                {% if app_user.id in extra_app_users_id %}
                                                    <td class="name">---</td>
                                                {% else %}
                                                    <td class="name">{{app_user.get_full_name}}</td>
                                                {% endif %}
                                                <td class="email">{{app_user.email}}</td>
                                                {% if not is_bulk %}
                                                    {% if app_user.id in extra_app_users_id %}
                                                        <td class="enroll">---</td>
                                                        <td>---</td>
                                                    {% else %}
                                                        <td style="text-align: center">
                                                            {% if app_user.active_installs.count %}
                                                                <span style='display:none'>1</span><span class="glyphicon glyphicon-floppy-saved text-success" aria-hidden="true"></span>
                                                            {% else %}
                                                                <span style='display:none'>0</span><span class="glyphicon glyphicon-floppy-remove text-danger" aria-hidden="true"></span>
                                                            {% endif %}
                                                        </td>
                                                    {% endif %}
                                                {% endif %}
                                                {% if app_user.id in extra_app_users_id %}
                                                     <td>---</td>
                                                {% else %}
                                                    <td style="text-align: center;">
                                                        <a href="" id="editappuser" useredit="{{app_user.uuid}}">
                                                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                                                        </a>
                                                    </td>
                                                {% endif %}
                                                <td style="text-align: center;">
                                                    {% if app_user.email != request.user.email %}
                                                        <a href="" id="delappuser">
                                                            <i class="fa fa-times" aria-hidden="true"></i>
                                                        </a>
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                        <tfoot>
                                        <tr>
                                            <td colspan="6">
                                                <div>
                                                    <ul class="pagination"></ul>
                                                </div>
                                            </td>
                                        </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /.row -->
                </div>
            </div>

        </div>
        <!-- /.row -->
    </div>
    <!-- Modal Editform -->
    <div  userid=""  class="modal fade Formedit" id="myModal2" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" org_id="{{organisation.secure_id}}">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form method="PUT">
                    {% csrf_token %}
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel">{% trans 'Edit records' %}</h4>
                    </div>
                    <div class="modal-body" >
                        <input type="hidden" class="form-control" type="text" name="id" value="">
                        <div class="form-group">
                            <label class="col-sm-3">{% trans "First Name" %}</label>
                            <div class="col-sm-9">
                                <input class="form-control" type="text" name="firstname" value="">
                                <div class="text-danger" id="first_name_error"></div>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3">{% trans "Last Name" %}</label>
                            <div class="col-sm-9">
                                <input class="form-control" type="text" name="lastname" value="">
                                <div class="text-danger" id="last_name_error"></div>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3">{% trans "Email" %}</label>
                            <div class="col-sm-9">
                                <input class="form-control" type="email" name="email">
                                <p class="error" style="display: none; color: #ff6849;"></p>
                                <div class="text-danger" id="email_error"></div>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        {% if is_bulk or organisation.has_software_support %}
                            <div class="form-group pupop_plaform_admin">
                                <label class="col-sm-3">{% trans 'Dashboard admin' %}</label>
                                <div class="col-sm-9">
                                    <input name="is_admin" type="checkbox" class="js-switch" data-color="#99d683" id="chkChange">
                                </div>
                                <div class="clearfix"></div>
                            </div>
                        {% endif %}
                    </div>
                    <div class="modal-footer">
                        <div class="pull-right">
                            <button type="button" class="btn btn-default" data-dismiss="modal">{% trans 'Close' %}</button>
                            <button id="btn-submit-edit" type="submit" class="btn btn-primary">{% trans 'Save changes' %}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- Upload CSV modal -->
    <div class="modal fade" id="csv-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="csv-modal-close" type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">{% trans 'Upload CSV' %}</h4>
                </div>
                <div class="modal-body">
                    <div class="m-b-20">
                        {% static "samples/sample-user-list.csv" as sample_csv %}
                        {% blocktrans %}Got a <a href={{ sample_csv }}>User/Email CSV</a>? Select here to import:{% endblocktrans %}
                    </div>
                    <input type="file" id="csvFileInput" onchange="uploadCSV(this.files)" accept=".csv">
                </div>
            </div>
        </div>
    </div>
    <!-- Add new users modal -->
    <div class="modal fade modal-open" id="add-new-users-modal" tabindex="-1" role="dialog" aria-labelledby="add-new-users-label">
        <div class="modal-dialog sync-users" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="cancel-add-new-users" type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="add-new-users-label">{% trans 'Add new users' %}</h4>
                </div>
                <div class="modal-body">
                    <form action="{{ organisation.appusers_url}}" org_id="{{organisation.secure_id}}" method="post" data-toggle="validator" role="form">
                        {% csrf_token %}
                        <div class="wizard-content">
                            <div class="wizard-pane active" role="tabpanel">
                                {% for err in form.non_field_errors  %}
                                    <p class="text-danger well">{{err}}</p>
                                {% endfor %}
                                <!-- If there are non form errors, is because we are submitting more users than allowed -->
                                {% if form.non_form_errors  %}
                                    <p class="text-danger well">{% trans 'Due to your Organisation restriction of enroled users with Active Protect only ' %}{{ nr_new_users_allowed }}{% if nr_new_users_allowed == 1 %}{% trans ' user' %}{% else %}{% trans ' users' %}{% endif %}{% trans ' can be added. If you wish to upgrade please contact Customer Support.' %}</p>
                                {% endif %}
                                <div>
                                    {% if nr_new_users_allowed != 0 %}
                                        <div class="csv-upload-button">
                                            <button id="add-users-csv-upload" class="btn btn-default btn-outline btn-md waves-effect waves-light" type="button" data-toggle="modal" data-target="#csv-modal">
                                                <span class="btn-label">
                                                  <i class="fa fa-table"></i>
                                                </span>
                                                {% trans 'CSV upload' %}
                                            </button>
                                        </div>
                                    {% endif %}
                                    <div>
                                        {{ form.management_form }}
                                        <div id="form_set">
                                            <div class="empty_form_ctn">
                                                {% for each_form in form %}
                                                    {% include 'partials/each_input_inline.html' with values=each_form %}
                                                    <div class="col-md-2 last_addbutton">
                                                        <input type="button" class="btn btn-default remove_more_r" value="- {% trans "Remove" %}" >
                                                        <input type="button" class="btn btn-default add_more" value="+ {% trans "Add Row" %}">
                                                    </div>
                                                    <div class="clearfix"></div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                        <div id="empty_form" style="display:none">
                                            <div class="empty_form_ctn ">
                                                <table class='no_error'>
                                                    {% include 'partials/each_input_inline.html' with values=form.empty_form %}
                                                    <div class="col-md-2 last_addbutton">
                                                        <input type="button" class="btn btn-default remove_more_r" value="- {% trans "Remove" %}" >
                                                        <input type="button" class="btn btn-default add_more" value="+ {% trans "Add Row" %}" >
                                                    </div>
                                                </table>
                                                <div class="clearfix"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="clearfix"></div>
                                    <br>
                                    <div id="userGrid"></div>
                                    <!-- Warn customer how many users they are allowed to add -->
                                    {% if nr_new_users_allowed > 0 %}
                                        <p class="text-info"> {% trans 'Due to your Organisation restriction of enroled users with Active Protect only ' %}{{ nr_new_users_allowed }}{% if nr_new_users_allowed == 1 %}{% trans ' user' %}{% else %}{% trans ' users' %}{% endif %}{% trans ' can be added. If you wish to upgrade please contact Customer Support.' %}</p>
                                    {% elif nr_new_users_allowed == 0 %}
                                        <p class="text-danger"> {% trans 'Due to your Organisation restriction of enroled users with Active Protect you are not allowed to add any new users. If you wish to upgrade please contact Customer Support.' %}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% if nr_new_users_allowed != 0 %}
                        <button class="btn btn-primary btn-rounded submitformadd" type="submit" role="button">{% trans 'Submit' %}</button>
                    {% endif %}
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Sync Users modal -->
    <div class="modal fade" id="sync-users-modal" tabindex="-1" role="dialog" aria-labelledby="syncLabel">
        <div class="modal-dialog sync-users" role="document">
            {% with organisation.organisationusersync.social_token.account as account %}
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="close_sync_modal"><span aria-hidden="true">&times;</span></button>
                    {% if provider and account %}
                        <h4 class="modal-title" id="syncLabel">{% trans 'Which users would you like to import to CyberSmart?' %}</h4>
                    {% else %}
                        <h4 class="modal-title" id="syncLabel">{% trans 'Sync Users' %}</h4>
                    {% endif %}
                </div>
                <div class="modal-body">
                    <div class="m-b-20">
                        <div class="panel-body users-sync-modal">
                            {% if provider and account %}
                                <div class='row'>
                                    {% if organisation.organisationusersync.users_data|get_item:'imported' %}
                                        <p><strong>{% if provider == 'google' %}Google Workspace{% else %}Microsoft 365{% endif %}</strong> <span class="badge badge-success enabled-sync">{% trans 'Sync Enabled' %}</span></p>
                                        {% if "email" in account.extra_data %}
                                            <p>
                                                {% trans 'Connected account:' %} <strong>{{ account.extra_data.email }}</strong>
                                            </p>
                                        {% else %}
                                            <p>
                                                {% trans 'Connected account:' %} <strong>{{ account.extra_data.mail }}</strong>
                                            </p>
                                        {% endif %}
                                        <p>
                                            {% trans 'Last sync:' %} <strong>{{ organisation.organisationusersync.last_synced_time }}</strong>
                                        </p>
                                        <p>{% trans 'The CyberSmart integration pulls all users within your ' %}{% if provider == 'google' %} Google Workspace{% else %} Microsoft 365{% endif %}{% trans '. This can include suspended accounts, shared team email address or guests.' %}</p>
                                        <p>{% trans 'You can omit these accounts from being invited to CyberSmart using the table below, and confirming with the Sync Users button' %}</p>
                                        <br>
                                    {% else %}
                                        <p>{% trans 'The CyberSmart integration pulls all users within your ' %}{% if provider == 'google' %} Google Workspace{% else %} Microsoft 365{% endif %}{% trans '. This can include suspended accounts, shared team email address or guests.' %}</p>
                                        <p>{% trans 'You can omit these accounts from being invited to CyberSmart using the table below, and confirming with the Sync Users button' %}</p>
                                    {% endif %}
                                    <table id="add-row-google" class="table table-bordered table-hover table-striped toggle-circle text-center">
                                        <thead>
                                        <tr>
                                            <th data-sort-ignore="true" >
                                                <input onclick="selectAllUsers(this)" type="checkbox" name="select_all_users" id="id_select_all_users">
                                            </th>
                                            <th data-sort-initial="true" class="text-center">{% trans 'Full Name' %}</th>
                                            <th class="text-center">{% trans 'Email Address' %}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {% for user in list_users.users %}
                                            <tr>
                                                <td>
                                                    <input type="checkbox" data-full_name="{{user.full_name}}" data-email="{{user.email}}" name="enrol_user" id="id_enrol_user_{{ user.email }}" {% if user.imported and user.active or not user.imported and user.active or user.email == request.user.email %}checked{% endif %} {% if user.email == request.user.email %}disabled{% endif %}>
                                                </td>
                                                <td>{{user.full_name}}</td>
                                                <td>{{user.email}}</td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="6">
                                                    <div>
                                                        <ul class="pagination"></ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                    <div class="row">
                                        <div class="col-md-2">
                                            {% if not organisation.organisationusersync.users_data|get_item:'imported' %}
                                                <button class="btn btn-default" type="button" data-dismiss="modal" aria-label="Close">
                                                    {% trans 'Cancel' %}
                                                </button>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 col-md-offset-2 sync-button">
                                            <p>{% trans 'By clicking “Sync Users” it will send an email to all the selected users above and invite them to join CyberSmart' %}{% if organisation.has_software_support %}{% trans ' and install Active Protect' %}{% endif %}</p>
                                        </div>
                                        <div class="col-md-2 sync-button">
                                            <button class="btn btn-primary" type="button" id="enrol_users_button">
                                                {% trans 'Sync Users' %}
                                            </button>
                                        </div>
                                    </div>
                                    {% if organisation.organisationusersync.users_data|get_item:'imported' %}
                                        <hr>
                                        <div class="row">
                                            <p><strong>{% trans 'Disconnect from ' %}{% if provider == 'google' %}Google Workspace{% else %}Microsoft 365{% endif %}</strong></p>
                                            <p>
                                                {% trans 'If you no longer want to sync users, you can disconnect your account below' %}
                                            </p>
                                            <button class="btn btn-danger btn-outline" id="disconnect-sync" data-url="{% url 'dashboard:users-sync-disconnect' organisation.secure_id %}">{% trans "Disconnect" %}</button>
                                        </div>
                                    {% endif %}
                                </div>
                            {% else %}
                                <div class='row'>
                                    <p>{% trans "You can connect your Google Workspace or Microsoft 365 for Business account to easily import all your users to CyberSmart." %}</p>
                                    <p>{% trans "To get started, sign in with your chosen account:" %}</p>
                                    <br/>
                                    <a class="btn btn-google btn-social" id="connect_with_google" href="{% url 'dashboard:users-sync-connect' org_id=organisation.secure_id %}?provider=google">
                                        <i class="fa fa-google-plus m-r-5"></i> {% trans "Connect with Google OAuth API" %}
                                    </a>
                                    <a class="btn btn-windows btn-social" id="connect_with_microsoft" href="{% url 'dashboard:users-sync-connect' org_id=organisation.secure_id %}?provider=microsoft">
                                        <i class="fa fa-windows m-r-5"></i> {% trans "Connect with Microsoft" %}
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endwith %}
        </div>
    </div>
    <!-- /.container-fluid -->
{% endblock %}

{% block extra-js %}
    <script type="text/javascript" src="{% static 'js/jquery.scrollTo.min.js' %}"></script>
    <script src="{% static 'js/validator.js' %}"></script>

    <script src="{% static 'js/dashboard_data.js' %}"></script>
    <script src="{% static 'js/dashboard_custom.js' %}"></script>
    <script>
        // Fallback for gettext if djangojs fails to load
        if (typeof gettext === 'undefined') {
            window.gettext = function(msg) { return msg; };
        }
        
        {# Open Sync Users modal when query param is passed #}
        {% if open_sync_users_modal %}
            document.getElementById('sync-users-button').click()
        {% endif %}

        {# When selecting all users in sync users modal #}
        function selectAllUsers(element) {
            const checkboxes = document.querySelectorAll("input[id^='id_enrol_user_']");
            selectAll(element, checkboxes)
        }

        let csv_upload = document.getElementById('add-users-csv-upload')
        if (csv_upload) {
            csv_upload.addEventListener('click', function (){
                document.getElementById('cancel-add-new-users').click()
            })
        }

        {# Disconnect social account #}
        $("#disconnect-sync").on("click", function(e) {
            document.getElementById('close_sync_modal').click()
            swal({
                title: gettext("Disconnect this account?"),
                type: "warning",
                confirmButtonColor: "#DD6B55",
                showCancelButton: true,
                confirmButtonText: gettext("Yes, disconnect"),
                cancelButtonText: gettext("Cancel"),
                closeOnConfirm: false
            }, function(){
                $.ajax({
                    url: $("#disconnect-sync").data('url'),
                    method: 'GET',
                    success: function (response) {
                        if(response.status > 0){
                            location.reload();
                        }
                    },
                });
            })
        });

        {# Import users from social accounts #}
        $(document).on('click', '#enrol_users_button', function(e) {
                    e.preventDefault();
                    const csrfmiddlewaretoken = $('input[name=csrfmiddlewaretoken]').val();
                    const checkboxes = document.querySelectorAll("input[id^='id_enrol_user_']");
                    let data = {
                        'form-0-email': '',
                        'form-0-first_name': '',
                        'form-0-last_name': '',
                        'form-0-is_social': "True",
                        'form-INITIAL_FORMS': 0,
                        'form-MAX_NUM_FORMS': 1000,
                        'form-MIN_NUM_FORMS': 0,
                        'form-TOTAL_FORMS': 1
                    };
                    let count = 0
                    for (let user_checkbox of checkboxes) {
                        data['form-' + count + '-email'] = user_checkbox.dataset.email;
                        const nameParts = user_checkbox.dataset.full_name.split(' ');
                        if (nameParts.length === 2) {
                            // Exactly 2 words: first name, last name
                            data['form-' + count + '-first_name'] = nameParts[0];
                            data['form-' + count + '-last_name'] = nameParts[1];
                        } else {
                            // Not 2 words: everything goes to first name, last name is blank
                            data['form-' + count + '-first_name'] = user_checkbox.dataset.full_name;
                            data['form-' + count + '-last_name'] = '';
                        }
                        data['form-' + count + '-is_social'] = "True";
                        data['form-' + count + '-sync_user'] = user_checkbox.checked;
                        count += 1
                    }
                    data['form-TOTAL_FORMS'] = count

                    // object to form data
                    let form_data = new FormData();
                    for ( const key in data ) {
                        form_data.append(key, data[key]);
                    }

                    $.ajax({
                        method: 'POST',
                        url: window.location.pathname,
                        data: form_data,
                        cache: false,
                        contentType: false,
                        processData: false,
                        async: false,
                        beforeSend: function(request) {
                            request.setRequestHeader("X-CSRFTOKEN", csrfmiddlewaretoken);
                        }
                    }).error(function(e){
                        alert(gettext('Something went wrong'));
                    }).done(function(response){
                        window.location = window.location.href.split('?')[0];
                    });
        });

        $("#manual-entry-button").on("click", function(e) {
            $('.manual-table').show(function() {
                $(window).scrollTo('.manual-table', 750, {offset:-100 });
            });
        });
        if($("#id_form-TOTAL_FORMS").val()==1) {
            $(".remove_more_r").css('display','none');
        }
        function uploadCSV(files) {

            function escapeCSVCell(cell) {
                if ($.inArray(cell[0], ['@', '+', '-', '=', '|']) !== -1) {
                    cell = "'" + cell;
                }
                cell = cell.replace("|", "\\|");
                return cell
            }

            function escapeCSVFileContent(content) {
                // first split by lines
                var lines = content.split('\n');
                // escaped lines
                var escaped_lines = [];
                $.each(lines, function (index, value) {
                    // then split by cells
                    var cells = value.split(',');
                    // escaped cells
                    var escaped_cells = [];
                    $.each(cells, function (cell_index, cell_value) {
                        escaped_cells.push(escapeCSVCell(cell_value));
                    });
                    // join escaped cells to escaped line
                    escaped_lines.push(escaped_cells.join(','));
                });
                // join escaped lines to csv content and return
                return escaped_lines.join('\n');
            }

            if (window.FileReader) {
                fileToRead = files[0];
                var reader = new FileReader();
                reader.readAsText(fileToRead);
                reader.onload = function loadHandler(event) {
                    var strData = event.target.result;
                    strData = escapeCSVFileContent(strData);
                    strDelimiter = ",";
                    var objPattern = new RegExp(
                        (
                            "(\\" + strDelimiter + "|\\r?\\n|\\r|^)" +
                            "(?:\"([^\"]*(?:\"\"[^\"]*)*)\"|" +
                            "([^\"\\" + strDelimiter + "\\r\\n]*))"
                        ),
                        "gi"
                    );


                    var arrData = [[]];
                    var arrMatches = null;
                    while (arrMatches = objPattern.exec( strData )){
                        var strMatchedDelimiter = arrMatches[ 1 ];
                        if (
                            strMatchedDelimiter.length &&
                            strMatchedDelimiter !== strDelimiter
                        ){
                            arrData.push( [] );
                        }
                        var strMatchedValue;
                        if (arrMatches[ 2 ]){
                            strMatchedValue = arrMatches[ 2 ].replace(
                                new RegExp( "\"\"", "g" ),
                                "\""
                            );
                        } else {
                            strMatchedValue = arrMatches[ 3 ];
                        }
                        arrData[ arrData.length - 1 ].push( strMatchedValue );
                    }
                    $('#form_set .empty_form_ctn:first-child').remove();
                    $('#id_form-TOTAL_FORMS').val(parseInt($('#id_form-TOTAL_FORMS').val()) - 1);
                    $.each(arrData, function (index, element) {
                        if (element.length > 1) {
                            var firstName = element[0];
                            var lastName = element[1];
                            var email = element[2];
                            $(".remove_more_r").css('display', 'block');
                            var form_idx = $('#id_form-TOTAL_FORMS').val();
                            var newForm = $('#empty_form').html().replace(/__prefix__/g, form_idx);
                            $('#form_set').append(newForm);
                            $('#id_form-TOTAL_FORMS').val(parseInt(form_idx) + 1);
                            var form_length = $('#form_set')[0].children.length-1;
                            var new_form =$('#form_set')[0].children[form_length];
                            var find_checkbox = $(new_form).find('.js-switch')[0];
                            $(find_checkbox).parent().find('span').remove()
                            new Switchery($(new_form).find('.js-switch')[0]);
                            // remove add button
                            var countadd = 1;
                            $("#form_set .empty_form_ctn").each(function () {
                                $(this).attr('countremoveadd', countadd);
                                $(this).find('.add_more').hide();
                                countadd++;
                            });
                            $("#id_form-" + form_idx + "-first_name").val(firstName);
                            $("#id_form-" + form_idx + "-last_name").val(lastName);
                            $("#id_form-" + form_idx + "-email").val(email);
                        }
                    });
                    $('#csv-modal').modal('hide');
                    var lastRow = $('#form_set .empty_form_ctn:last-child');
                    lastRow.find('.add_more').show();
                    document.getElementById('add-users-button').click();
                };
                reader.onerror = function errorHandler(evt) {
                    if(evt.target.error.name == "NotReadableError") {
                        alert(gettext("Cannot read file !"));
                    }
                };
            } else {
                alert(gettext('FileReader are not supported in this browser.'));
            }
        }
        $('body').on('click', '.add_more', function() {

            $(".remove_more_r").css('display','block');
            var form_idx = $('#id_form-TOTAL_FORMS').val();
            $('#form_set').append($('#empty_form').html().replace(/__prefix__/g, form_idx));
            $('#id_form-TOTAL_FORMS').val(parseInt(form_idx) + 1);
            // remove add button
            var countadd = 1;
            $("#form_set .empty_form_ctn").each(function() {
                $(this).attr('countremoveadd', countadd);
                countadd++;
            });

            if($("#form_set .empty_form_ctn").attr('countremoveadd') != parseInt(form_idx)+1) {
                $(this).css('display','none');
            }
            var form_length = $('#form_set')[0].children.length-1;
            var new_form =$('#form_set')[0].children[form_length];
            var find_checkbox = $(new_form).find('.js-switch')[0];
            $(find_checkbox).parent().find('span').remove()
            new Switchery($(new_form).find('.js-switch')[0]);
            $('#form_set .empty_form_ctn:last-child').trigger('input');
        });
        $('body').on('click', '.remove_more', function() {
            var form_idx = $('#id_form-TOTAL_FORMS').val();
            if (form_idx>1) {
                $('#form_set .empty_form_ctn:last-child').remove();
                $('#id_form-TOTAL_FORMS').val(parseInt(form_idx) - 1);
            }
        });
        $('body').on('click', '.remove_more_r', function(e) {
            e.preventDefault();
            var form_idx = $('#id_form-TOTAL_FORMS').val();
            if (form_idx>1) {
                $('#id_form-TOTAL_FORMS').val(parseInt(form_idx) - 1);
                $(this).parents('#form_set .empty_form_ctn').remove();
            }
            if (form_idx==2) {
                $(".remove_more_r").css('display','none');
            }
            count = 0;
            $("#form_set .empty_form_ctn").each(function() {
                $(this).attr('count', count);
                $(this).find('input.first_nameCL').attr('name', 'form-'+count+'-first_name');
                $(this).find('input.last_nameCL').attr('name', 'form-'+count+'-last_name');
                $(this).find('input.emailCL').attr('name', 'form-'+count+'-email');
                count++;
            });
            // remove add button
            $("#form_set .empty_form_ctn:last-child .add_more").css('display','block');
            $('#form_set .empty_form_ctn:last-child').trigger('input');
        });
        $('body').on('click', '#editappuser', function(e){
            e.preventDefault();
            $('#myModal2').modal();
            $('.Formedit').attr('userid', $(this).attr('useredit'));
            $('#send-new-app').attr('uuid', $(this).attr('useredit'));
            $('.Formedit').attr('is_admin', $(this).parents('tr').attr('is_admin'));
            var id = $(this).parents('tr').attr('id');
            var name = $(this).parents('tr').find('td.name').text();
            search_ = name.indexOf(" ");
            var firstname = name.substr(0, search_);
            var lastname = name.substr(search_);
            var name = $(this).parents('tr').find('td.name').text();
            var email = $(this).parents('tr').find('td.email').text();
            $('.Formedit input[name=id]').val(id);
            $('.Formedit input[name=firstname]').val(firstname);
            $('.Formedit input[name=lastname]').val(lastname);
            $('.Formedit input[name=email]').val(email);
            // don't allow edit of email if the user doesn't have a password set
            {% if not has_usable_password %}
                if (email == '{{request.user.email}}'){
                    $('.Formedit input[name=email]').attr('disabled', 'disabled');
                } else {
                    $('.Formedit input[name=email]').removeAttr('disabled');
                }
            {% endif %}
            $("#email_error").text("");
            // don't show user admin / enroll switches for themselves
            if (email == '{{request.user.email}}'){
                $(".pupop_plaform_admin").hide();
            } else {
                $(".pupop_plaform_admin").show();
            }
            if($('.Formedit').attr('is_admin')== 'admin_'){
                // console.log('admin')
                // $('.pupop_plaform_admin input=[type="checkbox"]').attr('checked', 'checked');
                // $('.pupop_plaform_admin .js-switch').trigger('click');
                var special = document.querySelector('#chkChange');
                // $(special).attr("checked", true);
                special.checked = true;
                if (typeof Event === 'function' || !document.fireEvent) {
                    var event = document.createEvent('HTMLEvents');
                    event.initEvent('change', true, true);
                    special.dispatchEvent(event);
                } else {
                    special.fireEvent('onchange');
                }
                // $('.pupop_plaform_admin input[type="checkbox"]').addClass('disabled');
            } else {
                var special = document.querySelector('#chkChange');
                //$(special).attr("checked", false);
                special.checked = false;
                if (typeof Event === 'function' || !document.fireEvent) {
                    var event = document.createEvent('HTMLEvents');
                    event.initEvent('change', true, true);
                    special.dispatchEvent(event);
                } else {
                    special.fireEvent('onchange');
                }
            }
            if($('.Formedit').attr('enroll')== gettext('Yes')){
                // $('.pupop_plaform_admin input=[type="checkbox"]').attr('checked', 'checked');
                // $('.pupop_plaform_admin .js-switch').trigger('click');
                var special = document.querySelector('#chkChange_enroll');
                // $(special).attr("checked", true);
                special.checked = true;
                if (typeof Event === 'function' || !document.fireEvent) {
                    var event = document.createEvent('HTMLEvents');
                    event.initEvent('change', true, true);
                    special.dispatchEvent(event);
                } else {
                    special.fireEvent('onchange');
                }
                // $('.pupop_plaform_admin input[type="checkbox"]').addClass('disabled');
            } else {
                var special = document.querySelector('#chkChange_enroll');
                if(special){
                    special.checked = false;
                    if (typeof Event === 'function' || !document.fireEvent) {
                        var event = document.createEvent('HTMLEvents');
                        event.initEvent('change', true, true);
                        special.dispatchEvent(event);
                    } else {
                        special.fireEvent('onchange');
                    }
                }
            }
        });
        $('body').on('click', '#send-new-app', function(e) {
            e.preventDefault();
            window.location.href = $(this).attr('url') + $(this).attr('uuid');
        });
        // click button submit edit form
        $('body').on('click', '#btn-submit-edit', function(e){
            e.preventDefault();

            var csrfmiddlewaretoken = $('input[name=csrfmiddlewaretoken]').val(),
                id = $('.Formedit input[name=id]').val(),
                firstname = $('.Formedit input[name=firstname]').val(),
                lastname = $('.Formedit input[name=lastname]').val(),
                email = $('.Formedit input[name=email]').val(),
                is_admin = $('.Formedit input[name=is_admin]').is(":checked"),
                org_id = $('.Formedit').attr("org_id");
            var formData = new FormData();
            formData.append('email', email);
            formData.append('first_name', firstname);
            formData.append('last_name', lastname);
            formData.append('is_admin', is_admin);
            // console.log(formData)
            $("#first_name_error").text("");
            $("#last_name_error").text("");
            $.ajax({
                method: 'PUT',
                url: '/api/dashboard/'+org_id+'/appuser/'+id+'/',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                beforeSend: function(request) {
                    request.setRequestHeader("X-CSRFTOKEN", csrfmiddlewaretoken);
                },
            }).error(function(e){
                $.each(e.responseJSON, function (key, value) {
                    if (key === 'first_name') {
                        $("#first_name_error").text(e.responseJSON['first_name'])
                    } else if (key === 'last_name') {
                        $("#last_name_error").text(e.responseJSON['last_name'])
                    } else if (key === 'email') {
                        $("#email_error").text(e.responseJSON['email'])
                    }
                })
            }).done(function(response){
                if (response.success==0) {
                    $('p.error').css('display','block');
                    $('p.error').html(gettext('This email address is already in use, please use another'));
                } else if (response.success==-1) {
                    $('p.error').css('display','block');
                    $('p.error').html(gettext('You do not have access'));
                } else if (response.success==-2) {
                    // bug task cs-202
                    data = response.data;
                    $('#myModal2').modal('hide')
                    $('#demo-foo-addrow tr#'+data.pk+' td.name').text(data.first_name + " " + data.last_name);
                    var usersEnrolledCounter = $('#users-enrolled-counter');
                    var currentCounter = parseInt(usersEnrolledCounter.text());
                    if(data.enroll==true) {
                        usersEnrolledCounter.text(currentCounter + 1);
                        $('#demo-foo-addrow tr#'+data.pk+' td.enroll').text(gettext("Yes"));
                    } else {
                        usersEnrolledCounter.text(currentCounter - 1);
                        $('#demo-foo-addrow tr#'+data.pk+' td.enroll').text(gettext("No"));
                    }
                    if(data.is_admin == true) {
                        $('#demo-foo-addrow tr#'+data.pk+' td.is__admin').html('<span class="label label-table label-primary cl_admin" style="display: inline;">' + gettext("Dashboard Admin") + '</span>');
                        $('#demo-foo-addrow tr#'+data.pk).attr('is_admin', 'admin_');
                    } else {
                        $('#demo-foo-addrow tr#'+data.pk+' td.is__admin').html('<span class="label label-table label-primary label-outline cl_user" style="display: inline;">' + gettext("User") + '</span>');
                        $('#demo-foo-addrow tr#'+data.pk).attr('is_admin', 'user_');
                    }
                } else if (response.success==-3) {
                    $('p.error').css('display','block');
                    $('p.error').html(gettext('you must edit users in G Suite / Microsoft 365 and these changes will sync here'));
                } else if (response.success==-4) {
                    $('p.error').css('display','block');
                    $('p.error').html(gettext('This email address is already in use, please use another'));
                }else if (response.success==1) {
                    data = response.data;
                    $('#myModal2').modal('hide')
                    $('#demo-foo-addrow tr#'+data.pk+' td.name').text(data.first_name + " " + data.last_name);
                    $('#demo-foo-addrow tr#'+data.pk+' td.email').text(data.email);
                    var usersEnrolledCounter = $('#users-enrolled-counter');
                    var emailSent = $('#email-sent');
                    var emailSentAll = $('#email-sent-all');
                    var usersInstalledAll = $('#users-installed-all');
                    var currentCounter = parseInt(usersEnrolledCounter.text());
                    if(data.enroll==true) {
                        usersEnrolledCounter.text(currentCounter + 1);
                        emailSent.text(parseInt(emailSent.text()) + 1);
                        emailSentAll.text(parseInt(emailSentAll.text()) + 1);
                        usersInstalledAll.text(parseInt(usersInstalledAll.text()) + 1);
                        $('#demo-foo-addrow tr#'+data.pk+' td.enroll').text(gettext("Yes"));
                    } else {
                        usersEnrolledCounter.text(currentCounter - 1);
                        emailSent.text(parseInt(emailSent.text()) - 1);
                        emailSentAll.text(parseInt(emailSentAll.text()) - 1);
                        usersInstalledAll.text(parseInt(usersInstalledAll.text()) - 1);
                        $('#demo-foo-addrow tr#'+data.pk+' td.enroll').text(gettext("No"));
                    }
                    if(data.is_admin == true) {
                        $('#demo-foo-addrow tr#'+data.pk+' td.is__admin').html('<span class="label label-table label-primary cl_admin" style="display: inline;">' + gettext("Dashboard Admin") + '</span>');
                        $('#demo-foo-addrow tr#'+data.pk).attr('is_admin', 'admin_');
                    } else {
                        $('#demo-foo-addrow tr#'+data.pk+' td.is__admin').html('<span class="label label-table label-primary label-outline cl_user" style="display: inline;">' + gettext("User") + '</span>');
                        $('#demo-foo-addrow tr#'+data.pk).attr('is_admin', 'user_');
                    }
                }
            });
        });
        // click button del record
        $('body').on('click', '#delappuser', function(e){
            e.preventDefault();
            var csrfmiddlewaretoken = $('input[name=csrfmiddlewaretoken]').val();
            var id = $(this).parents('tr').attr('id');
            var uuid = $(this).parents('tr').attr('uuid');
            var name = $(this).parents('tr').find('td.name').text();
            var search_ = name.indexOf(" ");
            var firstname = name.substr(0, search_);
            var lastname = name.substr(search_);
            var email = $(this).parents('tr').find('td.email').text();
            var org_id = $('#exampleValidator').attr('org_id');
            var formData = new FormData();
            formData.append('id', id);
            formData.append('uuid', uuid);
            bootbox.confirm({
                message: gettext("Are you sure you want to remove this user?"),
                buttons: {
                    confirm: {
                        label: gettext('OK'),
                    },
                    cancel: {
                        label: gettext('Cancel'),
                    }},
                callback: function(result) {
                    if (result) {
                        $.ajax({
                            method: 'DELETE',
                            url: '/api/dashboard/' + org_id + '/appuser/' + id + '/',
                            data: formData,
                            cache: false,
                            contentType: false,
                            processData: false,
                            beforeSend: function (request) {
                                request.setRequestHeader("X-CSRFTOKEN", csrfmiddlewaretoken);
                            },
                        }).error(function (e) {
                        }).done(function (response) {
                            if (response.success == 1) {
                                $('tr#' + id).remove();
                                window.location.reload();
                            } else if (response.success == -1) {
                                bootbox.alert(gettext("You don't have access to perform this operation"), function () {
                                });
                            } else if (response.success == -2) {
                                bootbox.alert(gettext("You are unable to delete yourself"), function () {
                                });
                            } else if (response.success == -3) {
                                bootbox.alert(gettext("The organisation creator cannot be removed, however you may change the name and email address to re-assign this account to another user"), function () {
                                });
                            }

                        });
                    }
                }
            });

        });
        $("#add-row-google").footable();
    </script>
{% endblock %}
