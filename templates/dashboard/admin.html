{% extends "base.html" %}
{% load divide_tags %}
{% load i18n %}

{% block title %}{% trans 'Users & Devices' %}{% endblock %}
{% block head_title %}{% trans 'Users & Devices' %}{% endblock %}

{% block main-content %}
    <div class="container-fluid">
        <div class="row bg-title">
            <div class="col-lg-6 col-xs-12">
                <h4 class="page-title"><span class="hide-menu">{% trans 'Users & Devices' %}</span></h4>
            </div>
            <div class="col-lg-6 col-xs-12 text-right">
                <h4 class="page-title"><span class="hide-menu">{{ org_name }}</span></h4>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10 col-md-offset-1">
                {% for app_user in object_list %}
                    <div class="row">
                        <ul class="list-group">
                            <li class="list-group-item" style="height: 80px">
                                <div class="pull-left">
                                    <div><i class="glyphicon {% if app_user.is_dashboard_admin %}glyphicon-tower text-danger{% else %}glyphicon-user text-primary{% endif %}"></i><b {% if app_user.is_dashboard_admin %}class="text-danger"{% endif %}>{{ app_user.email }} {{ app_user.full_name }}</b></div>
                                    <div style="margin-left: 23px">
                                        {% trans 'Active' %}: {% if app_user.active %}<i class="glyphicon glyphicon-ok text-success"></i>{% else %}<i class="glyphicon glyphicon-remove text-danger"></i>{% endif %}
                                    </div>
                                    <div class="text-muted" style="margin-left: 23px">
                                        {{ app_user.uuid }}
                                    </div>
                                </div>
                                <div class="pull-right text-muted">
                                    <div style="margin-left: 23px">
                                        {% trans 'Created' %}: {{ app_user.created }}
                                    </div>
                                    <div style="margin-left: 23px">
                                        {% trans 'Last Check In' %}: {{ app_user.most_recent_check_in|default:"Not recorded" }}
                                    </div>
                                </div>
                            </li>
                            <ul class="list-group" style="margin-left: 50px">
                                {% for app_install in app_user.raw_installed_devices.all %}
                                    <li class="list-group-item" style="height: 115px">
                                        <div style="width: 20px; height: 100px; display: inline-block; float: left">
                                            {% include "partials/device-icon.html" with icon_style="font-size:20px" %}
                                            {% include "partials/platform-icon.html" with icon_style="font-size:20px; margin-top: 35px" icon_colored=True %}
                                        </div>
                                        <div style="width: 300px; height: 100%; display: block; float: left; padding: 5px">
                                            <div>
                                                <span class="m-l-5 text-primary"><b>
                                                <a href="{% call_method app_install "url" org_id=organisation.secure_id user_uuid=app_user.uuid device_id=app_install.device_id serial_number=app_install.url_encoded_serial_number %}">{{ app_install.hostname }}</a>
                                                </b></span>
                                            </div>
                                            <div style="margin-left: 10px">
                                                {% trans 'Active' %}: {% if not app_install.inactive %}<i class="glyphicon glyphicon-ok text-success"></i>{% else %}<i class="glyphicon glyphicon-remove text-danger"></i>{% endif %}
                                            </div>
                                            <div style="margin-left: 10px">
                                                {% trans 'Device Type' %}: {{ app_install.get_device_type_display }}
                                            </div>
                                            <div style="margin-left: 10px">
                                                {% trans 'Operating System' %}: <b>{{ app_install.display_os }}</b>
                                            </div>
                                            <div class="text-muted" style="margin-left: 23px">
                                                {{ user.uuid }}
                                            </div>
                                        </div>
                                        <div style="width: 250px; height: 100%; display: inline-block; float: right; text-align: right">
                                            <div class="text-muted">
                                                {% trans 'Created' %}: {{ app_install.created }}
                                            </div>
                                        </div>
                                    </li>
                                {% endfor %}
                            </ul>
                        </ul>
                    </div>
                {% endfor %}
                {% include 'partials/pagination.html' %}
            </div>
        </div>
    </div>
{% endblock %}