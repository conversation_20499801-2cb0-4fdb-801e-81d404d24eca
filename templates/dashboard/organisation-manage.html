{% extends 'base.html' %}

{% load static %}
{% load i18n %}
{% load divide_tags %}
{% load waffle_tags %}

{% block head_title %}{% trans 'Organisation' %}{% endblock %}

{% block sub_nav %}
    {% include 'partials/sub-header-org.html' %}
{% endblock %}

{% block extra-css %}
    <link href="{% static 'plugins/custom-select/custom-select.css' %}" rel="stylesheet" type="text/css" />
    <link type="text/css" rel="stylesheet" href="{% static 'css/onboarding.css' %}">
    <style>
        #id_size li{
            list-style-type: none;
        }
        .message_suggestion_answers {
            position: relative;
        }
        .suggestion_answers {
            width: 100%;
            position: absolute;
            left: 0;
            padding: 0 7px;
            display: none;
            cursor: pointer;
            z-index: 9;
        }
        .suggestion_answers ul {
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            overflow: auto;
            height: 200px;
        }
        .suggestion_answers ul li {
            padding: 10px;
            border-bottom: 1px solid #f5f5f5;
            text-align: left;
            list-style: none;
        }
        .suggestion_answers ul li:hover {
            background: #ddd;
        }
        .active_label {
            pointer-events: none;
            margin: 10px;
            margin-left: 30px;
        }
    </style>
{% endblock %}
{% block main-content %}

    <div class="container-fluid">
        {% translate "Manage organisation" as page_title %}
        {% include "partials/header_default.html" with page_title=page_title page_icon="settings-2" %}
        <div class="row">
            <div class="col-md-3">
                <div class="panel panel-bordered">
                    {% include 'partials/manage_org_nav.html' with current_view='dashboard:manage-organisation' %}
                </div>
            </div>
            <div class="col-md-9">
                <div class="panel panel-bordered settings">
                    <div class="panel-heading">
                        <p class="panel-small-title">{% trans 'Organisation Details' %}</p>
                    </div>
                    <div class="panel-body settings">
                        <div class="col-md-10 ">
                            <form method="POST" action="{{ organisation.manageorganisation_url}}">
                                {% csrf_token %}
                                <div class="row white-box tw-border">
                                    <div class="form-group {% if form.name.errors %}has-error{% endif %}">
                                        <label><strong>{{ form.name.label }}</strong></label>
                                        <input type="text" class="form-control hidden" name="id__" {% if form.id__.value %}value="{{ form.id__.value }}"{% endif %}/>
                                        <input type="text" class="form-control" name="name" {% if form.name.value %}value="{{ form.name.value }}"{% endif %}/>
                                        {% if form.name.errors %}
                                            {% for err in form.name.errors%}
                                                <small class="text-danger">{{err|safe}}</small>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                    <div class="form-group {% if form.industry.errors %}has-error{% endif %}">
                                        <label class="control-label"><strong>{{ form.industry.label }}</strong></label>
                                        {{ form.industry }}
                                        {% if form.industry.errors %}
                                            {% for err in form.industry.errors%}
                                                <small class="text-danger">{{err}}</small>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                    <div class="form-group {% if form.industry_description.errors %}has-error{% endif %}" id="id_industry_description_form" >
                                        <label for="id_industry_description" class="control-label">
                                            <strong>{{ form.industry_description.label }} *</strong>
                                        </label>
                                        <textarea maxlength="{{ form.fields.industry_description.max_length }}" class="form-control" name="industry_description" id="id_industry_description">{% if form.industry_description.value %}{{ form.industry_description.value }}{% endif %}</textarea>
                                        {% if form.industry_description.errors %}
                                            {% for err in form.industry_description.errors%}
                                                <small class="text-danger">{{err}}</small>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                    <div class="form-group {% if form.default_language.errors %}has-error{% endif %}">
                                        <label class="control-label"><strong>{{ form.default_language.label }}</strong></label>
                                        {{ form.default_language }}
                                        {% if form.default_language.errors %}
                                            {% for err in form.default_language.errors%}
                                                <small class="text-danger">{{err}}</small>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                </div>
                                {# Email notifications #}
                                {% if organisation.learn_lite_tab_displayed %}
                                    <br/>
                                    <div class="row white-box tw-border switch-box">
                                        <div class="form-group no-border pull-right m-t-20 m-b-15">
                                            <label class="switch">
                                                <input type="checkbox" name="lms_send_email_notifications" id="id_lms_send_email_notifications" class="success" {% if form.lms_send_email_notifications.value %}checked{% endif %}>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                        <div class="col-md-10">
                                            <h4 class="switch-label">{{ form.lms_send_email_notifications.label }}</h4>
                                            <p class="switch-help-text">{{ form.lms_send_email_notifications.help_text }}</p>
                                        </div>
                                    </div>
                                    <br/>
                                {% endif %}
                                {# End email notifications #}
                                {% switch "enforce-mfa-switch" %}
                                {% if not partner and organisation.is_direct_customer %}
                                {# Enforce MFA #}
                                <div class="row white-box tw-border switch-box">
                                    <div class="col-md-8">
                                        <h4 class="switch-label">{{ form.enforce_mfa.label }}</h4>
                                        <p class="switch-help-text">{{ form.enforce_mfa.help_text|safe }}</p>
                                    </div>
                                    <div class="col-md-4 pull-right m-t-20 m-b-15">
                                        <div style="display: inline-block; float: right">
                                            <label class="switch">
                                                <input onclick="enableExtraSettings(this)" type="checkbox" name="enforce_mfa" id="id_enforce_mfa" class="success" {% if form.enforce_mfa.value %}checked{% endif %}>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>

                                </div>
                                {% endif %}
                                {% endswitch %}
                                {# End Enforce MFA #}
                                {# Device cleaning #}
                                <div class="row white-box tw-border switch-box" {% if not organisation.is_devices_cleaning_switch_visible %}hidden{% endif %}>
                                    <div class="col-md-8">
                                        <h4 class="switch-label">{{ form.devices_cleaning_enabled.label }}</h4>
                                        <p class="switch-help-text">{{ form.devices_cleaning_enabled.help_text|safe }}</p>
                                    </div>
                                    <div class="col-md-4 pull-right m-t-20 m-b-15">
                                        <div style="display: inline-block; float: right">
                                            <label class="switch">
                                                <input onclick="enableExtraSettings(this)" type="checkbox" name="devices_cleaning_enabled" id="id_devices_cleaning_enabled" class="success" {% if form.devices_cleaning_enabled.value %}checked{% endif %}>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-lg-12 device-cleaning-extra-settings" style="{% if not form.devices_cleaning_enabled.value %}display: none{% endif %}" id="id_device_cleaning_extra_settings">
                                        <div class="smart-delimiter">
                                          <div class="text">
                                              {% trans "extra settings" %}
                                          </div>
                                        </div>
                                        <div class="text-muted">
                                            <div class="m-t-10">
                                                <label for="devices_cleaning_days">{% trans "Amount of days a device is considered as active (if it checks in)" %}</label>
                                                <input min="30" type="number" placeholder="days" class="nice-number-input m-r-20" value="{{ form.devices_cleaning_days.value }}" name="devices_cleaning_days">
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                {# End device cleaning #}
                                <div class="clearfix"></div>
                                <div class="form-group">
                                    <button type="submit" name="action" class="btn btn-primary">{% trans 'Update Organisation' %}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
     </div>






{% endblock %}
{% block extra-js %}
    <script src="{% static 'plugins/custom-select/custom-select.min.js' %}" type="text/javascript"></script>
    <script>
        $(document).ready(function() {
            $('input[name="bulk_install"]').change(function () {
                var bulkInstall = $(this).val() === "True";
                if (bulkInstall) {
                    $("#id_bulk_install_no").fadeOut("fast", function () {
                        $("#id_bulk_install_yes").fadeIn();
                    });
                } else {
                    $("#id_bulk_install_yes").fadeOut("fast", function () {
                        $("#id_bulk_install_no").fadeIn();
                    });
                }
            });
            $('.smart-policies-input').click(function () {
                var smartPolicies = $(this).val() === "True";
                if (smartPolicies) {
                    swal({
                        title: "CyberSmart Pro",
                        text: {% blocktrans %}"Organisation software account will be updated to CyberSmart Pro. Are you sure?"{% endblocktrans %},
                        type: "success",
                        showCancelButton: true,
                        confirmButtonText: "Yes",
                        cancelButtonText: "No",
                        closeOnConfirm: false
                    }, function(){
                        $('#radio_smart_policies_True').prop('checked', true);
                        swal.close();
                    });
                } else {
                    swal({
                        title: "CyberSmart",
                        text: {% blocktrans %}"Organisation software account will be downgraded to regular software plan. Are you sure?"{% endblocktrans %},
                        type: "success",
                        showCancelButton: true,
                        confirmButtonColor: "#DD6B55",
                        confirmButtonText: "Yes",
                        cancelButtonText: "No",
                        closeOnConfirm: false
                    }, function(){
                        $('#radio_smart_policies_False').prop('checked', true);
                        swal.close();
                    });
                }
                return false;
            });
            $("textarea[name='email_message']").click(function(e) {
                $('.suggestion_answers').css('display','block');
            });
            $(".suggestion_answers ul li").click(function() {
                $(".suggestion_answers").css('display','none');
                $("#preview_email_template").css('display','block');
                var _data = $(this).text();
                var message_suggestion_answers = $(".message_suggestion_answers textarea");
                message_suggestion_answers.val(_data);
            });
            // CS-636
            $('.message_suggestion_answers textarea').bind('input', function() {
                $(".suggestion_answers").css('display','none');
            });
            if($('#radio_True').attr('checked') == 'checked'){
                $('.message_suggestion_answers').css('display','none');
            };
            $('body').on('click', '#radio_False', function() {
                $('.message_suggestion_answers textarea[name="email_message"]').val('');
                $('.message_suggestion_answers').slideDown();
            });
            $('body').on('click', '#radio_True', function() {
                $('.message_suggestion_answers textarea[name="email_message"]').val({% blocktrans %}'This is a custom bulk deployment package to be installed on all your devices'{% endblocktrans %});
                $('.message_suggestion_answers').slideUp();

            });
        });
        $(document).mouseup(function(e)
        {
            var container = $(".suggestion_answers");

            // if the target of the click isn't the container nor a descendant of the container
            if (!container.is(e.target) && container.has(e.target).length === 0)
            {
                container.hide();
            }
        });
    </script>

    <script type="text/javascript">
        $(".select2").each(function(){
            var $this = $(this);
            $this.select2({
                placeholder: "Select " + $this.attr("name")
            });
        });
        // remove html5 validation and default to django for select2
        $("select.select2").attr('required', false);

        function enableExtraSettings(element) {
            const extra_settings = document.getElementById('id_device_cleaning_extra_settings');
            extra_settings.style.display = element.checked ? 'block' : 'none';
        }
    </script>
{% endblock %}
