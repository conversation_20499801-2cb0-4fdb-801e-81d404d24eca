{% extends 'base.html' %}

{% load static %}
{% load divide_tags %}
{% load i18n %}
{% load l10n %}
{% load beta_feature_enrolment %}
{% load waffle_tags %}


{% block head_title %}
    {% if not organisation.is_trial %}{{organisation.name}} {% endif %}{% trans "Dashboard" %}
{% endblock %}

{% block sub_nav %}
    {% include 'partials/sub-header-org.html' %}
{% endblock %}

{% block extra-css %}
    <link rel="stylesheet" href="{% static 'css/org-dashboard.css' %}">
    <style type="text/css">
        .status-container {
            /* height: 34px; */
            /* display: block; */
            /* margin-bottom: 5px; */
            /* vertical-align: bottom; */
        }
        .deploy-button-group {
            padding-right: 10px;
        }
        .resend-active-protect-button {
            padding-top: 7px;
            padding-bottom: 7px;
        }
    </style>
{% endblock %}

{% block main-content %}
    {% csrf_token %}
    {% with organisation_has_any_enrolled_beta_features=organisation|has_any_enrolled_beta_features %}
    <div class="container-fluid">

        {% with organisation.name|add:" - "|add:_("Dashboard") as page_title %}
            {% include "partials/header_default.html" with page_title=page_title page_icon="box" download_cap=True %}
        {% endwith %}
        
        {% include "smart_score/dashboard_widget.html" %}
        <div style="position: relative">
            {% include 'partials/access-restriction.html' with restricted_page='dashboard-org' %}
            <div class="row">
                <div class="col-md-12 col-lg-12 col-sm-12">
                    <div class="white-box tw-border tw-font-sans">
                        <div class="row row-in">
                            <div class="emailuser_dash col-lg-3 col-sm-6 row-in-br">
                                <div class="col-in row">
                                    <div class="col-md-7 col-sm-7 col-xs-7"> <i class="ti-user"></i>
                                        <h5 class="text-muted vb tw-font-sans">{% trans "USERS ENROLLED" %}</h5>
                                    </div>
                                    <div class="col-md-5 col-sm-5 col-xs-5">
                                        <h3 class="counter text-right m-t-15 text-muted dash-entities-all tw-font-sans" custom-loaded-data="{{total_app_users}}">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="emailuser_dash col-lg-3 col-sm-3 row-in-br">
                                <div class="col-in row">
                                    <div class="col-md-6 col-sm-6 col-xs-6"> <i class="ti-email"></i>
                                        <h5 class="text-muted vb tw-font-sans">{% trans "EMAILS SENT" %}</h5>
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-6">
                                        <h3 class="counter text-right m-t-15 text-muted tw-font-sans"><span custom-loaded-data="{{ total_app_users }}">0</span><span class="op">/<span custom-loaded-data="{{total_app_users}}">0</span></span></h3>
                                    </div>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <div class="descriptionemailsent">
                                            <p>{% trans "To enrol your team, we need to send each of them a custom email" %}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="emailuser_dash col-lg-3 col-sm-6 row-in-br">
                                <div class="col-in row">
                                    <div class="col-md-6 col-sm-6 col-xs-6"> <i class="ti-download"></i>
                                        <h5 class="text-muted vb tw-font-sans">{% trans "USERS INSTALLED" %}</h5>
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-6">
                                        <h3 class="counter text-right m-t-15 text-primary tw-font-sans"><span custom-loaded-data="{{installed_app_users}}">0</span><span class="op">/<span custom-loaded-data="{{total_app_users}}">0</span></span></h3>
                                    </div>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-primary" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="0%" custom-loaded-data="{{installed_app_users|divide:total_app_users}}%" custom-loaded-data-target='{"css": "width"}'></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="emailuser_dash col-lg-3 col-sm-6 row-in-br  b-r-none">
                                <div class="col-in row">
                                    <div class="col-md-7 col-sm-7 col-xs-7"> <i class="ti-check-box"></i>
                                        <h5 class="text-muted vb tw-font-sans">{% trans "USERS PASSING" %}</h5>
                                    </div>
                                    <div class="col-md-5 col-sm-5 col-xs-5">
                                        <h3 class="counter text-right m-t-15 text-success dash-entities-passing" custom-loaded-data="{{user_passing}}">0</h3><div class='text-success text-right filter-text filter-text-passing'></div>
                                    </div>
                                </div>
                            </div>
                            <div class="emailuser_dash col-lg-3 col-sm-6 row-in-br b-0">
                                <div class="col-in row">
                                    <div class="col-md-7 col-sm-7 col-xs-7"> <i class="ti-na"></i>
                                        <h5 class="text-muted vb tw-font-sans">{% trans "USERS FAILING" %}</h5>
                                    </div>
                                    <div class="col-md-5 col-sm-5 col-xs-5">
                                        <h3 class="counter text-right m-t-15 text-danger dash-entities-failing tw-font-sans" custom-loaded-data="{{user_failing}}">0</h3><div class='text-danger text-right filter-text filter-text-failing'></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% include "partials/evaluation_prompt.html" %}
            <!-- .row -->
            <div class="row">
                <div class="col-lg-12">
                    <div class="white-box tw-border">
                        {% if not organisation.is_trial and organisation.is_direct_customer_v4 and not organisation.has_software_subscription and not organisation.is_allowed_to_install_one_app or not organisation.is_trustd_mobile_available %}
                            <div class="row">
                                <div class="col-xs-12 col-sm-6">
                                    {% if not organisation.is_trial and organisation.is_direct_customer_v4 and not organisation.has_software_subscription and not organisation.is_allowed_to_install_one_app %}
                                        <h5 class="text-primary" style="font-weight: bolder">{% trans "To protect all of your team you can invite them to install CyberSmart on their devices" %}</h5>
                                        <button type="button" data-toggle="modal" data-target="#upgradeSoftwarePlanModal" class="btn has-spinner btn-primary">{% trans "Invite My Team" %}</button>
                                    {% endif %}
                                    <br>
                                    <br>
                                </div>
                                {% if not organisation.is_trustd_mobile_available %}
                                    <div class="col-xs-12 col-sm-6 text-right m-b-20">
                                        {% url 'dashboard:dashboard-csv-download' organisation.secure_id as csv_xlsx_download_url %}
                                        {% include "common/csv_xlsx_download.html" with csv_xlsx_download_url=csv_xlsx_download_url %}
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}
                        <!-- <p class="text-muted m-b-20">Here are all the users we retrieved automatically from [Google/Microsoft]. / Here are all the users you added.</p> -->
                        <div class="form-inline padding-bottom-15">
                            <div class="row">
                                <div class="col-sm-12 m-b-20">
                                    <div class="form-group">
                                        {% translate "Search by Name, Email User, Device" as search_help_text %}
                                        {% include "organisations/users/blocks/filters_and_search.html" with help_icon=True search_button_style="width: 100px; height: 36px" search_help_text=search_help_text %}
                                    </div>
                                    {% include "organisations/dashboard/common/beta_devices_filter.html" %}
                                    {% if organisation.is_trustd_mobile_available %}
                                        <div class="form-group pull-right">
                                            {% url 'dashboard:dashboard-csv-download' organisation.secure_id as csv_xlsx_download_url %}
                                            {% include "common/csv_xlsx_download.html" with csv_xlsx_download_url=csv_xlsx_download_url %}
                                            <a href="{% url 'organisations:manage-users' org_id=organisation.secure_id %}#manage-users" class="tw-flex tw-items-center tw-min-h-9 tw-px-4 tw-py-1 tw-ml-2 tw-rounded-md tw-border tw-border-gray-300 tw-bg-white tw-text-black tw-font-sans hover:tw-bg-gray-100 hover:tw-text-black focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 tw-transition-colors tw-duration-300 tw-ease-in-out tw-float-end">
                                                <span class="tw-text-sm">{% trans "Manage Active Protect users" %}</span>
                                            </a>
                                        </div>
                                    {% else %}
                                        {% if awaiting_install_count %}
                                            <a href="{% url 'dashboard:send-apps-awaiting' org_id=organisation.secure_id %}" id="send-apps-awaiting" class="btn btn-primary pull-right resend-active-protect-button" title="{% trans "Send reminder email to users without CyberSmart Active Protect installed." %}">
                                                {% trans "Remind pending users" %}
                                            </a>
                                        {% endif %}
                                    {% endif %}
                                    {% if organisation_has_any_enrolled_beta_features and not organisation.is_trustd_mobile_available %}
                                        {% trans 'Deploy to all' as button_label %}
                                        {% with button_class="btn-default" %}
                                            <div class="btn-group pull-right deploy-button-group">
                                                {% include "partials/send_installer_button.html" with organisation=organisation button_label=button_label %}
                                            </div>
                                            {% include "partials/send_installer_button_modal.html" %}
                                        {% endwith %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% if extra_app_users_id %}
                            <p class="text-warning">{% trans 'You are currently at your organisation limit of enroled users with Active Protect. If you wish to upgrade please contact Customer Support.' %} </p>
                        {% endif %}
                        <div>
                            {% include "partials/loading.html" with loader_id="devices_filter_loader" %}
                            <div class="table-responsive">
                                <table id="dashboard-table" class="table table-bordered table-hover toggle-circle tw-font-sans tw-text-xs lg:tw-text-sm"
                                    data-page-size="20">
                                    <thead>
                                    <tr>
                                        <th data-hide="phone"  style="min-width:75px" class="thead-sortable" onclick="orderTable(this)" id="name_precomputed">{% trans "Name" %} <i class="fa fa-sort"></i></th>
                                        <th data-toggle="true" class="thead-sortable" onclick="orderTable(this)" id="email">{% trans "Email" %} <i class="fa fa-sort"></i></th>
                                        <th data-hide="phone"  class="thead-sortable" onclick="orderTable(this)" id="first_install_text_precomputed">{% trans "Device" %} <i class="fa fa-sort"></i></th>
                                        <th data-type="numeric" class="thead-sortable" onclick="orderTable(this)" id="status_precomputed">{% trans "Status" %} <i class="fa fa-sort"></i></th>
                                        {% if policies_support %}
                                            <th data-hide="phone" style="min-width:80px" class="tw-text-black" >{% trans "Policies" %}</th>
                                        {% endif %}
                                        <th data-hide="phone" style="width: 150px" data-type="date" class="thead-sortable tw-text-right" onclick="orderTable(this)" id="most_recent_check_in_precomputed">{% trans "Last checked in" %} <i class="fa fa-sort"></i></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for app_user in app_users %}
                                        <tr sort_tr="{{ forloop.counter }}" data-app-user-pk="{{ app_user.pk }}" class="appusers">
                                            <td headers="name_precomputed" class="tw-text-xs lg:tw-text-sm">{{ app_user.name_precomputed }}</td>
                                            <td headers="email"><span class="tw-inline-block tw-text-wrap tw-hyphens-auto">{{ app_user.email }}</span></td>
                                            <!-- If CAP max users has been reached, then do not show details -->
                                            {% if app_user.id in extra_app_users_id %}
                                                <td headers="first_install_text_precomputed"><button disabled class="btn btn-outline btn-warning host-name-button">{% trans 'User limit reached' %} <i class="fa fa-arrow-right pull-right"></i></button></td>
                                                <td headers="status_precomputed" align='center' >---</td>
                                                {% if policies_support %}
                                                    <td headers="policies" align='center' >---</td>
                                                {% endif %}
                                                <td headers="most_recent_check_in_precomputed" align='center' >---</td>
                                            {% else %}
                                                <td headers="first_install_text_precomputed" class="!tw-py-2">
                                                    {% for install in app_user.installs.all %}
                                                        {% with install.reports.all.0 as report %}
                                                            {% if install.inactive == True or not report %}
                                                                <div class="tw-flex tw-items-center tw-min-h-9 tw-mb-1 tw-px-4 tw-py-1 tw-rounded-md tw-border tw-border-gray-200 tw-bg-white tw-text-gray-400 tw-font-sans tw-cursor-not-allowed">{{ install.hostname }} [{{ install.display_os }}{% if install.machine_model %} - {{ install.machine_model }}{% endif %}] ({% trans "app " %}v{{install.get_app_version}}) {% flag 'disable_beta_pill' %} {% else %} {% if organisation_has_any_enrolled_beta_features and install.is_beta_precomputed %}<span class="beta-pill"></span>{% endif %} {% endflag %}</div>
                                                            {% else %}
                                                                <a href="{% call_method install 'url' org_id=organisation.secure_id user_uuid=app_user.uuid device_id=install.device_id serial_number=install.url_encoded_serial_number %}" class="tw-flex tw-items-center tw-min-h-9 tw-mb-1 tw-px-4 tw-py-1 tw-rounded-md tw-border tw-border-gray-300 tw-bg-white tw-text-black tw-font-sans hover:tw-bg-gray-100 hover:tw-text-black focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 tw-transition-colors tw-duration-300 tw-ease-in-out">{{ install.hostname }} [{{ install.display_os }}{% if install.machine_model %} - {{ install.machine_model }}{% endif %}] ({% trans "app " %}v{{install.get_app_version}}) {% flag 'disable_beta_pill' %} {% else %} {% if organisation_has_any_enrolled_beta_features and install.is_beta_precomputed %}<span class="beta-pill"></span>{% endif %} {% endflag %}</a>
                                                            {% endif %}
                                                        {% endwith %}
                                                    {% endfor %}
                                                </td>
                                                <td  headers="status_precomputed" align='center' class="count_sort_parent" data-value="{{app_user.analytics.pass_percentage|add:"0"}}" style="vertical-align: top">
                                                    {% for install in app_user.installs.all %}
                                                        {% if install.reports.all.0 %}
                                                            {% if install.inactive %}
                                                                <div class="status-container tw-flex tw-items-center tw-justify-center tw-h-[36px] tw-mb-1">
                                                                    <div class="sort_td" count_sort="0"></div>
                                                                    {% if not install.is_trustd_app %}
                                                                    <button type="button" url="{{ install.url_app_install_api }}" app_install_id="{{ install.id }}" class="btn btn-primary btn-rounded reactivate-button" >
                                                                        <span class="default-resend-text">{% trans "Reactivate" %}</span>
                                                                    </button>
                                                                    {% else %}
                                                                    <div>{% trans 'Reinstall required' %}</div>
                                                                    {% endif %}
                                                                </div>
                                                            {% elif install.analytics.latest_pass_percentage %}
                                                                <div class="status-container tw-block tw-w-full justify-center tw-h-[36px] tw-mb-1">
                                                                    <div class="sort_td" count_sort="{{install.analytics.latest_pass_percentage}}"></div>
                                                                    <div class="count_sort_child" >
                                                                        <span class="percent_complete {% percent_to_css_class install.analytics.latest_pass_percentage "0-50:text-danger,51-99:text-warning,100:text-success"%} {% if not install.analytics.latest_pass_percentage %}opacity_0{% endif %}" custom-loaded-data="{{install.analytics.latest_pass_percentage |floatformat:'0'}}%"></span>
                                                                        <div class="progress m-b-0">
                                                                            <div class="progress-bar {% percent_to_css_class install.analytics.latest_pass_percentage %}" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width:0%;" custom-loaded-data="{{install.analytics.latest_pass_percentage|unlocalize}}%" custom-loaded-data-target='{"css": "width"}'></div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            {% else %}
                                                                <div class="status-container tw-block tw-items-center tw-h-[36px] tw-mb-1">
                                                                    <div class="sort_td" count_sort="0"></div>
                                                                    <div class="count_sort_child" >
                                                                        <span class="percent_complete text-danger" custom-loaded-data="0%"></span>
                                                                        <div class="progress m-b-0">
                                                                            <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width:0%;" custom-loaded-data="{{install.analytics.latest_pass_percentage}}%" custom-loaded-data-target='{"css": "width"}'></div>
                                                                        </div>
                                                                    </div>
                                                                    {% if install.analytics.latest_pass_percentage is None %}
                                                                        <button type="button" class="btn btn-primary btn-rounded resend_email_to_user {% if app_user.disable_resend_email %} disabled {% endif %}" {% if app_user.disable_resend_email %} disabled {% endif %} resend-email-uuid="{{app_user.uuid}}">
                                                                            <span style="display: none" class="success-resend-text">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check-square"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                            <span class="default-resend-text">{% trans "Resend email" %}</span>
                                                                        </button>
                                                                    {% endif %}
                                                                </div>
                                                            {% endif %}
                                                        {% else %}
                                                            <div class="status-container tw-h-[36px] tw-flex tw-items-center tw-justify-center tw-mb-1 tw-text-xs">{% trans "Install pending" %}</div>
                                                        {% endif %}
                                                    {% empty %}
                                                        <div class="status-container tw-text-xs tw-mb-1">{% if not app_user.active %}{% trans "Inactive" %}{% else %}{% trans "Awaiting install" %}{% endif %}</div>
                                                    {% endfor %}
                                                    {% flag "disable_add_fake_app_installs_button" %}
                                                    {% else %}
                                                        {% if can_add_fakes %}
                                                            <button type="button" data-toggle="modal" data-target="#fakeAppInstallsModal-{{ app_user.uuid }}" class="btn has-spinner btn-default btn-block">
                                                                {% trans "Add Fake Devices" %}
                                                            </button>
                                                            {% include "app_users/add_fake_installs.html" with app_user=app_user %}
                                                        {% endif %}
                                                    {% endflag %}
                                                </td>
                                                {% if policies_support %}
                                                    <td  headers="policies" align='center'>
                                                        {% if policies_support %}
                                                            {% with policies_count=app_user.analytics.active_policies_count %}
                                                                {% if policies_count %}
                                                                    <div class="status-container tw-pt-1.5">
                                                                        {% with agreed_policies_count=app_user.analytics.agreed_policies_count %}
                                                                            {% with perc=agreed_policies_count|percentage:policies_count %}
                                                                                <div>
                                                                                    <span class="percent_complete {% percent_to_css_class perc "0-50:text-danger,51-99:text-warning,100:text-success"%}">{{ agreed_policies_count }} / {{ policies_count }}</span>
                                                                                    <div class="progress m-b-0">
                                                                                        <div class="progress-bar {% percent_to_css_class perc %} bar" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width:0%;" custom-loaded-data="{{perc}}%" custom-loaded-data-target='{"css": "width"}'></div>
                                                                                    </div>
                                                                                </div>
                                                                            {% endwith %}
                                                                        {% endwith %}
                                                                    </div>
                                                                {% else %}
                                                                    <div class="status-container">
                                                                        <a href="{% url 'smart_policies:main' organisation.secure_id %}" class="tw-flex tw-items-center tw-justify-center tw-min-h-9 tw-mb-1 tw-px-2 tw-py-1 tw-rounded-md tw-border tw-border-gray-300 tw-bg-white tw-text-black tw-text-center tw-font-sans hover:tw-bg-gray-100 hover:tw-text-black focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 tw-transition-colors tw-duration-300 tw-ease-in-out">{% trans "Upload" %}</a>
                                                                    </div>
                                                                {% endif %}
                                                            {% endwith %}
                                                        {% else %}
                                                            <a href="{{ organisation.billing_url }}" class="btn btn-default btn-outline btn-sm">{% trans "Upgrade" %}</a>
                                                        {% endif %}
                                                    </td>
                                                {% endif %}
                                                <td  headers="most_recent_check_in_precomputed" 
                                                    data-value="{{ app_user.most_recent_check_in_precomputed|date:"Y-m-d-h-i"|default_if_none:'1970-01-01-00-00' }}"
                                                    data-format-string="YYYY-MM-DD-HH-mm"
                                                    style="vertical-align: top">
                                                    {% for install in app_user.installs.all %}
                                                        <div class="tw-flex tw-flex-col tw-justify-center tw-h-[36px] tw-text-right tw-mb-1">
                                                            <span>{{ install.last_check_in|date:"j M Y" }}</span>
                                                            <span class="tw-text-xs">{{ install.last_check_in|date:"h:i A" }}</span>
                                                        </div>
                                                    {% empty %}
                                                        <div class="tw-h-[36px] tw-flex tw-items-center tw-justify-end tw-mb-1 tw-text-xs">{% trans "No apps installed" %}</div>
                                                    {% endfor %}
                                                </td>
                                            {% endif %}
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                    <tfoot>
                                    <tr>
                                        <td colspan="4">
                                            {% include 'partials/pagination.html' with params=params_query page_obj=app_users %}
                                        </td>
                                        <td colspan="2">
                                            {% if show_hidden_apps == True %}
                                                <button id="hide-inactive-apps" class="btn btn-outline btn-primary">{% trans "Hide Inactive Applications" %}</button>
                                            {% else %}
                                                <button id="show-inactive-apps" class="btn btn-outline btn-primary">{% trans "Show Inactive Applications" %}</button>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
    {% endwith %}
{% endblock %}

{% block extra-js %}
    <script src="{% static 'js/rxjs.umd.min.js' %}"></script>
    <script src="{% static 'js/dashboard_data.js' %}"></script>
    <script src="{% static 'js/dashboard_custom.js' %}"></script>
    <script src="{% static 'js/modules/organisation/dashboard/dashboard_filters.js' %}"></script>
    <script src="{% static 'js/modules/common/installer_modal.js' %}"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            var csrfmiddlewaretoken = "{{ csrf_token }}";

            const { BehaviorSubject } = rxjs;
            window.csApp = {
                ...(window.csApp ?? {}),
            };
            const data = JSON.parse(document.getElementById('app_users_to_send_email_to').textContent);
            window.csApp.appUsersToSendEmailTo$ = new BehaviorSubject(data);

            $('body').on('click', '.resend_email_to_user', function(){
                var _this = $(this);
                _this.addClass('resend_email_to_user_clicked');
                var _get_uuid = _this.attr('resend-email-uuid');
                _this.attr("disabled", true);
                $.ajax({
                    url: '{{ organisation.resend_email_to_user_url }}',
                    method: 'POST',
                    data: {
                        'csrfmiddlewaretoken': csrfmiddlewaretoken,
                        'uuid': _get_uuid,
                        'force': true
                    },
                    success: function(data) {
                        $(_this).removeClass('btn-primary');
                        $(_this).addClass('btn-success');
                        $(_this).find('.default-resend-text').hide();
                        $(_this).find('.success-resend-text').show(function () {
                            setTimeout(function () {
                                $(_this).removeClass('btn-success');
                                $(_this).addClass('btn-primary');
                                $(_this).find('.success-resend-text').hide();
                                $(_this).find('.default-resend-text').show();
                            }, 500);
                        });
                    },
                    error: function(data) {
                        _this.attr("disabled", false);
                    }
                });
            });

            // Reactivate a device
            $('.reactivate-button').click(function(){
                let app_install_id = $(this).attr('app_install_id');
                let url = $(this).attr('url');
                let csrfmiddlewaretoken = "{{ csrf_token }}";
                reactivateDevice(app_install_id, url, csrfmiddlewaretoken);
                return false;
            });

            
        });
    </script>

    {% if showmodal == True %}
        <script type="text/javascript" src="{% static 'js/partials/check_inbox_swal.js' %}"></script>
    {% endif %}
    {{ app_users_to_send_email_to|json_script:"app_users_to_send_email_to" }}

{% endblock %}
