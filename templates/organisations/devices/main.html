{% extends 'base.html' %}

{% load static %}
{% load divide_tags app_version_tags %}
{% load i18n %}
{% load l10n %}
{% load appusers %}

{% block head_title %}{% trans "Organisation Devices" %}{% endblock %}

{% block sub_nav %}
    {% include 'partials/sub-header-org.html' %}
{% endblock %}

{% block extra-css %}
    <link href="{% static 'css/partner-devices.css' %}" rel="stylesheet" />
{% endblock extra-css %}

{% block main-content %}
    {% csrf_token %}
    <div class="container-fluid">
        {% trans "Organisation devices" as devices_title %}
        {% include "partials/header_default.html" with page_title=devices_title page_icon="laptop-minimal" %}
        <div class="row">
            {% include "organisations/devices/analytic.html" %}
            <div class="col-md-12">
            <div class="white-box tw-border">
                <h2 class="tw-mb-4 tw-font-sans tw-text-lg tw-font-semibold tw-my-0 tw-text-black">{% trans "Devices" %}</h2>
                {% if not analytic.devices_analytic_is_ready %}
                    <div class="tw-flex tw-flex-col tw-gap-2 tw-bg-white tw-border tw-border-dashed tw-border-gray-300 tw-rounded-md tw-place-content-center tw-items-center tw-h-full tw-py-10 tw-font-sans">
                        {% if not organisation.is_bulk_enrollment_type %}
                        <p class="tw-text-sm tw-font-semibold">{% trans "No Active Protect devices to report" %}</p>
                        <p class="tw-text-sm tw-text-gray-700">{% trans "View insights into users' device security and activity." %}</p>
                        <p class="tw-text-sm tw-text-gray-700">{% trans "Get started by inviting users to install Active Protect." %}</p>
                        {% else %}
                        <p class="tw-text-sm tw-font-medium">{% trans "There are no Active Protect devices to report" %}</p>
                        <p class="tw-text-sm tw-text-gray-700">{% trans "View useful insight into device security and activity." %}</p>
                        <p class="tw-text-sm tw-text-gray-700">{% trans "Get started by deploying Active Protect to devices." %}</p>
                        {% endif %}
                        {% if not organisation.is_bulk_enrollment_type %}
                            <a href="{% url 'organisations:manage-users' org_id=organisation.secure_id %}" class="tw-mt-2 tw-bg-brand-600 hover:tw-bg-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out tw-h-9 tw-flex tw-items-center tw-justify-center tw-px-4 tw-py-3 tw-gap-1 tw-rounded-md tw-text-white tw-text-center tw-font-sans tw-shadow-sm hover:tw-text-white focus:tw-ring-4 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0">
                                {% trans 'Go to Manage users' %} 
                            </a>
                        {% else %}
                            <a href="{% url 'dashboard:organisation' org_id=organisation.secure_id %}" class="tw-mt-4 tw-bg-brand-600 hover:tw-bg-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out tw-h-10 tw-flex tw-items-center tw-justify-center tw-px-4 tw-py-3 tw-rounded-md tw-text-white tw-text-center tw-font-sans tw-shadow-sm hover:tw-text-white focus:tw-ring-4 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0">
                                {% trans 'Deploy Active Protect' %} 
                            </a>
                        {% endif %}      
                    </div>
                {% endif %}

                {% if analytic.devices_analytic_is_ready %}
                    <div class="cs-devices">
                        <div class="row m-b-20">
                            {% include "organisations/devices/blocks/filters_and_search.html" with help_icon=True %}
                        </div>
                        <form class="selectable-table-wrap" action="{% url 'api-v2:app-install' organisation.secure_id %}">
                            {% include 'partials/selectable-table/header.html' with params=params_query ids=device_ids %}
                            <div class="table-responsive">
                                <table class="table table-hover tw-overflow-hidden tw-mb-0">
                                    {% include "partners/devices/blocks/table_thead.html" with selectable=True %}
                                    {% translate "Unnamed" as unnamed_label %}
                                    <tbody>
                                    {% for device in devices %}
                                        {% with report=device.get_latest_report %}
                                            <tr>
                                                {% include 'partials/selectable-table/td.html' with entity=device %}
                                                <td class="!tw-align-middle">
                                                    {% if device.is_not_assigned_to_user %}
                                                        <div class="tw-flex tw-items-center tw-gap-2" id="assign-user-box-{{ device.pk }}">
                                                            <button class="tw-min-h-8 tw-flex tw-items-center tw-gap-1 tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0" type="button" data-toggle="modal" data-target="#user-attribution-modal-{{ device.pk }}">
                                                                {% trans 'Assign user' %}
                                                            </button>
                                                        </div>
                                                        {% include "partials/user_attribution_assign.html" with app_install=device %}
                                                    {% elif not device.app_user.organisation.bulk_install or device.is_assigned_to_user %}
                                                        <span data-toggle="tooltip" data-placement="right" title={{ device.app_user.email }}>{{ device.app_user.get_full_name|default:device.app_user.email }}</span>
                                                    {% else %}
                                                        <span data-toggle="tooltip" data-placement="right" title={{ device.last_hist_login_username|default:unnamed_label }}>{{ device.last_hist_login_username|default:unnamed_label }}</span>
                                                    {% endif %}
                                                </td>
                                                <td class="!tw-align-middle">
                                                    {{ device.device_id|default:"N/A" }}
                                                </td>
                                                {% if report %}
                                                    <td class="!tw-align-middle">
                                                        {% if not device.inactive %}
                                                            <a href="{% call_method device "url" org_id=device.app_user.organisation.secure_id user_uuid=device.app_user.uuid device_id=device.device_id serial_number=device.url_encoded_serial_number %}" class="tw-flex tw-justify-start tw-items-center tw-text-black tw-underline tw-underline-offset-2 tw-decoration-black hover:tw-text-brand-600 hover:tw-underline hover:tw-fill-brand-600 tw-transition">
                                                                {% include "organisations/devices/device-icon.html" with app_install=device %}
                                                                {{ device.hostname|default:"N/A" }}
                                                            </a>
                                                        {% else %}
                                                            <span class="tw-cursor-not-allowed">{{ device.hostname|default:"N/A" }}</span>
                                                        {% endif %}
                                                    </td>
                                                {% else %}
                                                    <td class="!tw-align-middle">
                                                        {% include "organisations/devices/device-icon.html" with app_install=device %}
                                                        <svg class="tw-relative -tw-left-1 tw-w-4 tw-h-4 tw-fill-red-600" data-toggle="tooltip" title="{% trans 'No report' %}">
                                                            <use href="{% static 'icons/icons-sprite.svg' %}#icon-circle-alert"></use>
                                                        </svg>

                                                    {{ device.hostname|default:"N/A" }}
                                                </td>
                                            {% endif %}
                                            <td class="!tw-align-middle tw-text-gray-800">
                                                {% display_os_version device %}
                                            </td>
                                            {% include "common/devices/devices_data.html" with device=device report=report %}
                                        </tr>
                                    {% endwith %}
                                {% empty %}
                                    <tr>
                                        <td class="tw-font-semibold tw-text-center" colspan="7" >
                                            {% trans 'No results found' %}
                                        </td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </form>
                    </div>
                    <div class="tw-flex tw-flex-col md:tw-flex-row tw-gap-2 tw-justify-between">
                        {% include 'partials/pagination.html' with params=params_query %}
                        {% include "partners/devices/blocks/download_csv.html" with api_endpoint=devices_csv_url main_entity=organisation %}
                    </div>
                {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block extra-js %}
    <script src="{% static 'js/rxjs.umd.min.js' %}"></script>
    <script src="{% static 'js/user_attribution_assign.js' %}"></script>
    <script type="module" src="{% static 'js/selectable-table.js' %}"></script>
    <script type="module" src="{% static 'js/modules/partner/devices-control.js' %}"></script>
{% endblock extra-js %}