{% load i18n %}
{% load svg %}
{% load static %}

<table id="dashboard-users-table" class="table table-hover toggle-circle demo-foo-addrow"
       data-page-size="20">
    <thead>
    <tr class="tw-border-b tw-bg-gray-50">
        {% include 'partials/selectable-table/th.html' %}
        <th data-sort-initial="true" class="tw-font-sans tw-text-sm tw-font-semibold tw-text-gray-600 tw-w-auto hover:tw-bg-gray-100">{% trans 'Name' %}</th>
        <th class="tw-font-sans tw-text-sm tw-font-semibold tw-text-gray-600 tw-w-auto hover:tw-bg-gray-100">{% trans 'Email' %}</th>
        <th class="tw-font-sans tw-text-sm tw-font-semibold tw-text-gray-600 tw-w-auto hover:tw-bg-gray-100">{% trans 'Role' %}</th>
        <th class="tw-font-sans tw-text-sm tw-font-semibold tw-text-gray-600 tw-text-right tw-w-auto" width="70" data-sort-ignore="true">{% trans 'Actions' %}</th>
    </tr>
    </thead>
    {% include 'organisations/users-and-groups/dashboard-access/components/dashboard-users-selectable-header.html' %}
    <tbody>
        {% for admin in organisation_admins.all %}
            {% with user=admin.user %}
                {% trans "This is the last user in the Full Dashboard Admin role and cannot be edited. Add another user to this role in order to edit this user" as translated_title %}
                <tr id="dashboard-user-row-{{ user.pk }}" {% if admin.id == last_default_role_user_id %}data-toggle="tooltip" data-placement="top" title="{{ translated_title }}"{% endif %}>
                    {% if admin.id != last_default_role_user_id %}
                        {% include 'partials/selectable-table/td.html' with entity=user %}
                    {% else %}
                        <td></td>
                    {% endif %}
                    <td class="name tw-font-medium tw-text-black">{{ user.get_full_name|default:"-" }}</td>
                    <td class="email tw-font-medium tw-text-black"><span class="tw-inline-block tw-truncate tw-max-w-[120px] lg:tw-max-w-full">{{ user.email }}</span></td>
                    <td class="role tw-font-medium tw-text-black">
                        {% trans "Unassigned" as default_unassigned %}
                        {{ admin.get_role|default:default_unassigned }}
                    </td>
                    <td style="text-align: center;">
                        <div class="tw-flex tw-gap-2 tw-justify-end">
                            <a class="{% if admin.id != last_default_role_user_id %}edit{% else %}disabled last-default-role-user{% endif %} tw-flex tw-align-center tw-w-[26px] tw-h-[26px] tw-text-black tw-shadow-sm tw-align-middle tw-border tw-border-gray-200 tw-rounded tw-p-1 tw-transition-colors tw-duration-300 hover:tw-border-gray-300 hover:tw-bg-gray-200" href="" data-toggle="modal" data-target="#editDashboardUserModal-{{ user.pk }}">
                                <svg class="tw-w-[16px] tw-h-[16px]">
                                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-pen-line"></use>
                                </svg>
                            </a>
                            {% if admin.id != last_default_role_user_id %}
                                <a href="" class="delete tw-flex tw-align-center tw-w-[26px] tw-h-[26px] tw-text-black tw-align-middle tw-border tw-border-gray-200 tw-rounded tw-p-1 tw-shadow-sm tw-transition-colors tw-duration-300 hover:tw-border-gray-300 hover:tw-bg-gray-200" data-toggle="modal" data-target="#delete-user-{{ user.pk }}" data-name="{{group.name}}" data-id="{{group.pk}}">
                                    <svg class="tw-w-[16px] tw-h-[16px]">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-trash-2"></use>
                                    </svg>
                                </a>
                            {% else %}
                                <a class="disabled">
                                    {% svg 'icons/trash-can-grey' %}
                                </a>
                            {% endif %}
                            {% if admin.id != last_default_role_user_id %}
                                {% include 'organisations/users-and-groups/dashboard-access/components/confirm-delete-user.html' %}
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% if admin.id != last_default_role_user_id %}
                    {# start edit user modal #}
                    {% include "organisations/users-and-groups/dashboard-access/components/edit-dashboard-user-modal.html" with user=user %}
                    {# end edit user modal #}
                {% endif %}
            {% endwith %}
        {% endfor %}
    </tbody>
    <tfoot>
    <tr>
        <td colspan="6">
            <div>
                <ul class="pagination"></ul>
            </div>
        </td>
    </tr>
    </tfoot>
</table>