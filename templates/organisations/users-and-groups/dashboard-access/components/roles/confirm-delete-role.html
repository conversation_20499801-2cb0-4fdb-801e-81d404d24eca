{% load i18n %}

<div  class="modal fade" id="delete-role-{{ role.pk }}" tabindex="-1" role="dialog" aria-labelledby="delete_dashboard_role">
    <div class="modal-dialog modal-md text-left" role="document">
        <form method="DELETE" action="{{ delete_url }}">
            <div class="modal-content modal-content p-x-36 p-y-20 br-8">
                <div class="modal-header">
                  <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title">{% trans 'Delete Role' %}</h4>
                </div>
                <div class="modal-body p-t-20">
                    <p>
                        {% trans 'Are you sure you want to remove ' %}
                        <span class="role-name font-bold">{{ role.name }}</span>
                    </p>
                    <p>
                        {% blocktranslate trimmed with default_role=DEFAULT_ROLE_NAME %}
                        	All users assigned in this role will automatically be assigned under the <b>{{ default_role }}</b> role
                        {% endblocktranslate %}
                    </p>
                </div>
                    <div class="modal-footer">
                        <div class="pull-left">
                            <button type="button" class="btn btn-default" data-dismiss="modal">{% trans 'Cancel' %}</button>
                        </div>
                        <div class="pull-right">
                            <button type="submit" role-name="{{ role.name }}" url="{% url 'api-v2:role-detail' organisation.secure_id role.pk %}" data-attr="modal-role-delete-btn" class="btn btn-danger delete-dashboard-role" data-dismiss="modal">{% trans 'Confirm' %}</button>
                        </div>
                    </div>
            </div>
        </form>
    </div>
</div>
