{% extends 'base.html' %}

{% load i18n %}
{% load static %}
{% load divide_tags %}
{% load waffle_tags %}
{% load check_permissions %}

{% block head_title %}{{ manage_users_and_groups_page_name }}{% endblock %}

{% block sub_nav %}
    {% include 'partials/sub-header-org.html' %}
{% endblock %}

{% block extra-css %}
    <link href="{% static 'plugins/custom-select/custom-select.css' %}" rel="stylesheet" type="text/css" />
    <link type="text/css" rel="stylesheet" href="{% static 'css/jsgrid.min.css' %}" />
    <link type="text/css" rel="stylesheet" href="{% static 'css/jsgrid-theme.min.css' %}" />
    <!-- Custom -->
    <link type="text/css" rel="stylesheet" href="{% static 'css/manage-users-and-groups.css' %}" />
    <link type="text/css" rel="stylesheet"  href="{% static 'css/onboarding.css' %}">
    <link type="text/css" rel="stylesheet"  href="{% static 'css/sync-users.css' %}">
{% endblock %}
{% block main-content %}
    <div class="container-fluid">
        {% translate "Manage users" as page_title %}
        {% include "partials/header_default.html" with page_title=page_title page_icon="users" %}

        {% if perms|has_access_to_dashboard_access_page:organisation %}
            {% include "organisations/users-and-groups/components/manage-nav.html" with current_view='cap_users_and_groups' %}
        {% endif %}
        <!-- /row -->
        <!-- row -->
        {% include "organisations/users-and-groups/components/groups.html" %}
        <!-- /.row -->
        <!-- row -->
        {% include "organisations/users-and-groups/components/users.html" %}
        <!-- /.row -->
    </div>
{% endblock %}

{% block extra-js %}
    <script src="{% static 'js/rxjs.umd.min.js' %}"></script>
    <script src="{% static 'js/validator.js' %}"></script>
    <script src="{% static 'js/dashboard_data.js' %}"></script>
    <script src="{% static 'js/dashboard_custom.js' %}"></script>
    <script src="{% static 'js/selectable-table.js' %}"></script>
    <script src="{% static 'js/manage-users-and-groups.js' %}"></script>

<script>
        {# Open Sync Users modal when query param is passed #}
        {% if open_sync_users_modal %}
            document.getElementById('sync-users-button').click()
        {% endif %}

        {# When selecting all users in sync users modal #}
        function selectAllUsers(element) {
            const checkboxes = document.querySelectorAll("input[id^='id_enrol_user_']");
            selectAll(element, checkboxes)
        }

        $(`#empty_form select[name="form-__prefix__-groups"]`).html(
            $('#form_set [name="form-0-groups"]').html()
        )

        let csv_upload = document.getElementById('add-users-csv-upload')
        csv_upload.addEventListener('click', function (){
            document.getElementById('cancel-add-new-users').click()
        })

        {# Disconnect social account #}
        $("#disconnect-sync").on("click", function(e) {
            document.getElementById('close_sync_modal').click()
            swal({
                title: gettext("Disconnect this account?"),
                type: "warning",
                confirmButtonColor: "#DD6B55",
                showCancelButton: true,
                confirmButtonText: gettext("Yes, disconnect"),
                cancelButtonText: gettext("Cancel"),
                closeOnConfirm: false
            }, function(){
                $.ajax({
                    url: $("#disconnect-sync").data('url'),
                    method: 'GET',
                    success: function (response) {
                        if(response.status > 0){
                            location.reload();
                        }
                    },
                });
            })
        });

        {# Import users from social accounts #}
        const enrol_users_button = document.getElementById('enrol_users_button');
        if (enrol_users_button) {
            document.getElementById('enrol_users_button').addEventListener(
                "click",
                function (e) {
                    e.preventDefault();
                    const csrfmiddlewaretoken = $('input[name=csrfmiddlewaretoken]').val();
                    const checkboxes = document.querySelectorAll("input[id^='id_enrol_user_']");
                    let data = {
                        'form-0-email': '',
                        'form-0-first_name': '',
                        'form-0-last_name': '',
                        'form-0-is_social': "True",
                        'form-INITIAL_FORMS': 0,
                        'form-MAX_NUM_FORMS': 1000,
                        'form-MIN_NUM_FORMS': 0,
                        'form-TOTAL_FORMS': 1
                    };
                    let count = 0
                    for (let user_checkbox of checkboxes) {
                        data['form-' + count + '-email'] = user_checkbox.dataset.email;
                        data['form-' + count + '-first_name'] = user_checkbox.dataset.full_name.split(' ')[0];
                        data['form-' + count + '-last_name'] = user_checkbox.dataset.full_name.split(' ').slice(1);
                        data['form-' + count + '-is_social'] = "True";
                        data['form-' + count + '-sync_user'] = user_checkbox.checked;
                        count += 1
                    }
                    data['form-TOTAL_FORMS'] = count

                    // object to form data
                    let form_data = new FormData();
                    for ( const key in data ) {
                        form_data.append(key, data[key]);
                    }

                    $.ajax({
                        method: 'POST',
                        url: window.location.pathname,
                        data: form_data,
                        cache: false,
                        contentType: false,
                        processData: false,
                        async: false,
                        beforeSend: function(request) {
                            request.setRequestHeader("X-CSRFTOKEN", csrfmiddlewaretoken);
                        }
                    }).error(function(e){
                        alert(gettext('Something went wrong'));
                    }).done(function(response){
                        window.location = window.location.href.split('?')[0];
                });
                }
            )
        }

        $("#manual-entry-button").on("click", function(e) {
            $('.manual-table').show(function() {
                $(window).scrollTo('.manual-table', 750, {offset:-100 });
            });
        });
        if($("#id_form-TOTAL_FORMS").val()==1) {
            $(".remove_more_r").css('display','none');
        }
        function uploadCSV(files) {

            $("#add-new-users-modal").modal("show");

            function escapeCSVCell(cell) {
                if ($.inArray(cell[0], ['@', '+', '-', '=', '|']) !== -1) {
                    cell = "'" + cell;
                }
                cell = cell.replace("|", "\\|");
                return cell
            }

            function escapeCSVFileContent(content) {
                // first split by lines
                var lines = content.split('\n');
                // escaped lines
                var escaped_lines = [];
                $.each(lines, function (index, value) {
                    // then split by cells
                    var cells = value.split(',');
                    // escaped cells
                    var escaped_cells = [];
                    $.each(cells, function (cell_index, cell_value) {
                        escaped_cells.push(escapeCSVCell(cell_value));
                    });
                    // join escaped cells to escaped line
                    escaped_lines.push(escaped_cells.join(','));
                });
                // join escaped lines to csv content and return
                return escaped_lines.join('\n');
            }

            if (window.FileReader) {
                fileToRead = files[0];
                var reader = new FileReader();
                reader.readAsText(fileToRead);
                reader.onload = function loadHandler(event) {
                    var strData = event.target.result;
                    strData = escapeCSVFileContent(strData);
                    strDelimiter = ",";
                    var objPattern = new RegExp(
                        (
                            "(\\" + strDelimiter + "|\\r?\\n|\\r|^)" +
                            "(?:\"([^\"]*(?:\"\"[^\"]*)*)\"|" +
                            "([^\"\\" + strDelimiter + "\\r\\n]*))"
                        ),
                        "gi"
                    );


                    var arrData = [[]];
                    var arrMatches = null;
                    while (arrMatches = objPattern.exec( strData )){
                        var strMatchedDelimiter = arrMatches[ 1 ];
                        if (
                            strMatchedDelimiter.length &&
                            strMatchedDelimiter !== strDelimiter
                        ){
                            arrData.push( [] );
                        }
                        var strMatchedValue;
                        if (arrMatches[ 2 ]){
                            strMatchedValue = arrMatches[ 2 ].replace(
                                new RegExp( "\"\"", "g" ),
                                "\""
                            );
                        } else {
                            strMatchedValue = arrMatches[ 3 ];
                        }
                        arrData[ arrData.length - 1 ].push( strMatchedValue );
                    }
                    $('#form_set .empty_form_ctn:first-child').remove();
                    $('#id_form-TOTAL_FORMS').val(parseInt($('#id_form-TOTAL_FORMS').val()) - 1);
                    $.each(arrData, function (index, element) {
                        if (element.length > 1) {
                            var firstName = element[0];
                            var lastName = element[1];
                            var email = element[2];
                            $(".remove_more_r").css('display', 'block');
                            var form_idx = $('#id_form-TOTAL_FORMS').val();
                            var newForm = $('#empty_form').html().replace(/__prefix__/g, form_idx);
                            $('#form_set').append(newForm);
                            $('#id_form-TOTAL_FORMS').val(parseInt(form_idx) + 1);

                            // remove add button
                            var countadd = 1;
                            $("#form_set .empty_form_ctn").each(function () {
                                $(this).attr('countremoveadd', countadd);
                                $(this).find('.add_more').hide();
                                countadd++;
                            });
                            $("#id_form-" + form_idx + "-first_name").val(firstName);
                            $("#id_form-" + form_idx + "-last_name").val(lastName);
                            $("#id_form-" + form_idx + "-email").val(email);
                        }
                    });
                    var addRowButton = '<input type="button" class="btn btn-default add_more" value="+ {% trans "Add Row" %}">';
                    var lastRow = $('#form_set .empty_form_ctn:last-child');
                    lastRow.find('.last_addbutton').append(addRowButton);
                    $('#csv-modal').modal('hide');
                    $('#form_set .empty_form_ctn:last-child').trigger('input');

                };
                reader.onerror = function errorHandler(evt) {
                    if(evt.target.error.name == "NotReadableError") {
                        alert(gettext("Cannot read file !"));
                    }
                };
            } else {
                alert(gettext('FileReader are not supported in this browser.'));
            }
        }
        $('body').on('click', '.add_more', function() {

            $(".remove_more_r").css('display','block');
            var form_idx = $('#id_form-TOTAL_FORMS').val();
            $('#form_set').append($('#empty_form').html().replace(/__prefix__/g, form_idx));
            $(`select[name="form-${form_idx}-groups"]`).html(
                $('[name="form-0-groups"]').html()
            )
            $('#id_form-TOTAL_FORMS').val(parseInt(form_idx) + 1);
            // remove add button
            var countadd = 1;
            $("#form_set .empty_form_ctn").each(function() {
                $(this).attr('countremoveadd', countadd);
                countadd++;
            });

            if($("#form_set .empty_form_ctn").attr('countremoveadd') != parseInt(form_idx)+1) {
                $(this).css('display','none');
            }
            var form_length = $('#form_set')[0].children.length-1;
            var new_form =$('#form_set')[0].children[form_length];
            var find_checkbox = $(new_form).find('.js-switch')[0];
            $(find_checkbox).parent().find('span').remove()
            new Switchery($(new_form).find('.js-switch')[0]);
            $('#form_set .empty_form_ctn:last-child').trigger('input');
        });
        $('body').on('click', '.remove_more', function() {
            var form_idx = $('#id_form-TOTAL_FORMS').val();
            if (form_idx>1) {
                $('#form_set .empty_form_ctn:last-child').remove();
                $('#id_form-TOTAL_FORMS').val(parseInt(form_idx) - 1);
            }
        });
        $('body').on('click', '.remove_more_r', function(e) {
            e.preventDefault();
            var form_idx = $('#id_form-TOTAL_FORMS').val();
            if (form_idx>1) {
                $('#id_form-TOTAL_FORMS').val(parseInt(form_idx) - 1);
                $(this).parents('#form_set .empty_form_ctn').remove();
            }
            if (form_idx==2) {
                $(".remove_more_r").css('display','none');
            }
            count = 0;
            $("#form_set .empty_form_ctn").each(function() {
                $(this).attr('count', count);
                $(this).find('input.first_nameCL').attr('name', 'form-'+count+'-first_name');
                $(this).find('input.last_nameCL').attr('name', 'form-'+count+'-last_name');
                $(this).find('input.emailCL').attr('name', 'form-'+count+'-email');
                count++;
            });
            // remove add button
            $("#form_set .empty_form_ctn:last-child .add_more").css('display','block');
            $('#form_set .empty_form_ctn:last-child').trigger('input');
        });
        // $('#syncform').validator();
        $('body').on('click', '#editappuser', function(e){
            e.preventDefault();
            $('.pupop_plaform_admin .js-switch').attr('checked');
            $('.pupop_plaform_admin').load();
            $('#myModal2').modal();
            $('.Formedit').attr('userid', $(this).attr('useredit'));
            $('#send-new-app').attr('uuid', $(this).attr('useredit'));
            $('.Formedit').attr('is_admin', $(this).parents('tr').attr('is_admin'))
            var id = $(this).parents('tr').attr('id');
            var name = $(this).parents('tr').find('td.name').text();
            search_ = name.indexOf(" ");
            var firstname = name.substr(0, search_);
            var lastname = name.substr(search_);
            var name = $(this).parents('tr').find('td.name').text();
            var email = $(this).parents('tr').find('td.email').text();
            var group = $(this).parents('tr').find('td.group').data('id');

            const {initialUserData$} = window.csApp ?? {};
            initialUserData$.next({
                name,
                id,
                firstname,
                lastname,
                email,
                group,
            })
            $('.Formedit input[name=id]').val(id);
            $('.Formedit input[name=firstname]').val(firstname);
            $('.Formedit input[name=lastname]').val(lastname);
            $('.Formedit input[name=email]').val(email);
            $('.Formedit select[name=group]').val(group || '');
            // don't allow edit of email if the user doesn't have a password set
            {% if not has_usable_password %}
                if (email == '{{request.user.email}}'){
                    $('.Formedit input[name=email]').attr('disabled', 'disabled');
                } else {
                    $('.Formedit input[name=email]').removeAttr('disabled');
                }
            {% endif %}
            $("#email_error").text("");
            // don't show user admin switches for themselves
            if (email == '{{request.user.email}}'){
                $(".pupop_plaform_admin").hide();
            } else {
                $(".pupop_plaform_admin").show();
            }
            if($('.Formedit').attr('is_admin') === 'admin_'){
                // $('.pupop_plaform_admin input=[type="checkbox"]').attr('checked', 'checked');
                // $('.pupop_plaform_admin .js-switch').trigger('click');
                var special = document.querySelector('#chkChange');
                // $(special).attr("checked", true);
                special.checked = true;
                if (typeof Event === 'function' || !document.fireEvent) {
                    var event = document.createEvent('HTMLEvents');
                    event.initEvent('change', true, true);
                    special.dispatchEvent(event);
                } else {
                    special.fireEvent('onchange');
                }
                // $('.pupop_plaform_admin input[type="checkbox"]').addClass('disabled');
            } else {
                var special = document.querySelector('#chkChange');
                //$(special).attr("checked", false);
                special.checked = false;
                if (typeof Event === 'function' || !document.fireEvent) {
                    var event = document.createEvent('HTMLEvents');
                    event.initEvent('change', true, true);
                    special.dispatchEvent(event);
                } else {
                    special.fireEvent('onchange');
                }
            }
        });
        $('body').on('click', '#send-new-app', function(e) {
            e.preventDefault();
            window.location.href = $(this).attr('url') + $(this).attr('uuid');
        });

        // click button del record
        $('body').on('click', '#delappuser', function(e){
            e.preventDefault();

            const modal = $(this).closest('td').find('.modal');
            modal.modal('show');

            const confirmBtn = modal.find('.delete-cap-user');
            confirmBtn.on('click', deleteUserConfirmed.bind(this));

            modal.on('show.bs.modal', function () {
                confirmBtn.off('click');
            })

            function deleteUserConfirmed(e) {
                e.preventDefault();
                var csrfmiddlewaretoken = $('input[name=csrfmiddlewaretoken]').val();
                var id = $(this).parents('tr').attr('id');
                var uuid = $(this).parents('tr').attr('uuid');
                var name = $(this).parents('tr').find('td.name').text();
                var search_ = name.indexOf(" ");
                var firstname = name.substr(0, search_);
                var lastname = name.substr(search_);
                var email = $(this).parents('tr').find('td.email').text();
                var org_id = $('#exampleValidator').attr('org_id');
                var group = $(this).parents('tr').find('td.group').data('id');
                let successMessage = $("#alert-success-delete-app-user");
                const {initialUserData$} = window.csApp ?? {};
                initialUserData$.next({
                    name,
                    id,
                    firstname,
                    lastname,
                    email,
                    group,
                })

                var formData = new FormData();
                formData.append('id', id);
                formData.append('uuid', uuid);

                $.ajax({
                    method: 'DELETE',
                    url: '/api/dashboard/' + org_id + '/appuser/' + id + '/',
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    beforeSend: function (request) {
                        request.setRequestHeader("X-CSRFTOKEN", csrfmiddlewaretoken);
                    },
                }).error(function (e) {
                }).done(function (response) {
                    if (response.success == 1) {
                        $('tr#' + id).remove();

                        const initialGroup = +initialUserData$.getValue().group;
                        const userName = initialUserData$.getValue().name
                        const groupCountCell = $(`#groups-table [data-id="${initialGroup}"]`).closest('tr').find('.amount');
                        groupCountCell.text(+groupCountCell.text() - 1)
                        successMessage.find(".user-name").text(userName);
                        successMessage.show();
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);

                        window.location.reload();
                    } else if (response.success == -1) {
                        bootbox.alert(gettext("You don't have access to perform this operation"), function () {
                        });
                    } else if (response.success == -2) {
                        bootbox.alert(gettext("You are unable to delete yourself"), function () {
                        });
                    } else if (response.success == -3) {
                        bootbox.alert(gettext("The organisation creator cannot be removed, however you may change the name and email address to re-assign this account to another user"), function () {
                        });
                    }
                });
            }
        });
        $("#add-row-google").footable();
    </script>
    {{ app_users_to_send_email_to|json_script:"app_users_to_send_email_to" }}
    {{ app_users_without_app_installs|json_script:"app_users_without_app_installs" }}
{% endblock %}
