{% load i18n %}
{% load static %}
{% load divide_tags %}
{% load svg %}

<!-- Modal Editform -->
<div  userid=""  class="modal fade Formedit" id="myModal2" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" org_id="{{organisation.secure_id}}">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content p-x-36 p-y-20 br-8">
            <form method="PUT">
                {% csrf_token %}
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">{% trans 'Edit App User' %}</h4>
                </div>
                <div class="modal-body" >
                    <div class="row inputs-row">
                        <input type="hidden" class="form-control" type="text" name="id" value="">
                        <div class="form-group col-sm-2">
                            <label for="firstname">{% trans "First Name" %}</label>
                            <input class="form-control" type="text" name="firstname" id="firstname" value="">
                            <div class="text-danger" id="first_name_error"></div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="form-group col-sm-2">
                            <label for="lastname">{% trans "Last Name" %}</label>
                            <input class="form-control" type="text" name="lastname" id="lastname" value="">
                            <div class="text-danger" id="last_name_error"></div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="form-group col-sm-2">
                            <label  for="email">{% trans "Email" %}</label>
                            <input class="form-control" id="email" type="email" name="email">
                            <p class="error" style="display: none; color: #ff6849;"></p>
                            <div class="text-danger" id="email_error"></div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="form-group col-sm-2">
                            <label for="group">
                                {% trans 'Assign To Group' %}
                            </label>
                            <select name="group" class="form-control" id="group">
                                <option value="" selected="">{% trans "Unassigned" %}</option>
                                {% for group in groups %}
                                <option value="{{group.id}}">{{group.name}}</option>
                                {% endfor %}
                            </select>
                            <p class="error" style="display: none; color: #ff6849;"></p>
                            <div class="text-danger" id="group_error"></div>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                        <button type="button" class="btn btn-default pull-left" data-dismiss="modal">{% trans 'Close' %}</button>
                        <button id="btn-submit-edit" type="submit" class="btn btn-primary">{% trans 'Save changes' %}</button>
                </div>
            </form>
        </div>
    </div>
</div>
