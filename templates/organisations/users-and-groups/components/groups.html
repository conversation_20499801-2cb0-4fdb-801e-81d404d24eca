{% load i18n %}
{% load static %}
{% load divide_tags %}
{% load svg %}

<div class="tw-bg-white tw-p-4 tw-border tw-border-grey-800 tw-rounded-lg tw-mt-4" id="groups">
    <div org_id="{{organisation.secure_id}}">
        <div class="clearfix"></div>

        <div class="tw-flex tw-flex-col lg:tw-flex-row tw-basis-full tw-justify-between lg:tw-items-start tw-mb-4">
            <div class="tw-flex tw-flex-col">
                <div class="tw-flex tw-gap-1">
                    <h2 class="tw-font-sans tw-text-lg tw-font-semibold tw-my-0 tw-text-black">{% trans 'Groups' %}</h2>
                    <div class="tw-flex tw-items-center tw-relative">
                        <svg class="tw-w-[20px] tw-h-[20px]">
                            <use href="{% static 'icons/icons-sprite.svg' %}#icon-info"></use>
                        </svg> 
                        <div class="tw-font-sans" data-icon-help="true">
                            {% trans "Groups help you organise Active Protect users in a way that makes sense—for example, by team or department." %}
                        </div>
                    </div>
                </div>
                <div class="tw-font-sans tw-text-gray-600">
                    Total: <span custom-loaded-data="{{groups.count}}" class="tw-font-semibold">0</span>
                </div>
            </div>

            {% if groups %}
            <button id="add-groups-button" class="btn btn-default btn-outline btn-md lg:tw-mt-0" type="button" data-toggle="modal" data-target="#addUserGroup">
                {% trans 'Add groups' %}
            </button>
            {% endif %}
        </div>

        <div id="alert-success-edit" class="alert alert-success alert-dismissible !tw-mb-4" style="display:none;" role="alert">
            <span>{% trans 'Group edited successfully.' %}</span>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>

        <div id="alert-success-deleted" class="alert alert-success alert-dismissible !tw-mb-4" style="display:none;" role="alert">
            <span>{% trans 'Group deleted successfully.' %}</span>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>

        {% if not groups %}
            <div class="tw-flex tw-flex-col tw-gap-2 tw-place-content-center tw-items-center tw-h-full tw-mt-4 tw-py-10 tw-font-sans tw-border tw-border-gray-300 tw-border-dashed tw-rounded-md">
                <p class="tw-text-sm tw-text-center tw-font-semibold">
                    {% trans "You have no groups" %}
                </p>
                <p class="tw-text-sm tw-text-center tw-max-w-lg tw-text-gray-600">
                    {% trans "Groups help you organise Active Protect users in a way that makes sense—for example, by team or department." %}
                </p>
                <p class="tw-text-sm tw-text-center tw-max-w-lg tw-text-gray-600">
                    {% trans "To get started, add a group." %}
                </p>    
                <button id="add-groups-button" class="tw-min-h-9 tw-mt-2 tw-flex tw-items-center tw-gap-1 tw-px-4 tw-py-1 tw-rounded-md tw-bg-white tw-border tw-border-gray-300 tw-text-black tw-font-sans tw-transition-colors tw-duration-300 tw-ease-in-out tw-shadow-sm hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0" type="button" data-toggle="modal" data-target="#addUserGroup">
                    <svg class="tw-w-[20px] tw-h-[20px]">
                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-plus"></use>
                    </svg>
                    {% trans 'Add groups' %}
                </button>            
            </div>
        {% endif %}
        
        {% if groups %}
        <table id="groups-table" class="table table-hover toggle-circle demo-foo-addrow"
            data-page-size="20">
            <thead class="header-users-and-groups">
            <tr class="tw-border-b tw-bg-gray-50">
                <th data-sort-initial="true" class="tw-font-sans tw-text-sm tw-font-semibold lg:tw-min-w-[200px] hover:tw-bg-gray-100">{% trans 'Name' %}</th>
                <th class="tw-font-sans tw-text-sm tw-font-semibold tw-w-auto hover:tw-bg-gray-100">{% trans 'Description' %}</th>
                <th class="tw-font-sans tw-text-sm tw-font-semibold tw-text-right tw-w-auto hover:tw-bg-gray-100">{% trans 'Users' %}</th>
                <th class="tw-font-sans tw-text-sm tw-font-semibold tw-text-right tw-w-[100px]" data-sort-ignore="true">{% trans 'Actions' %}</th>
            </tr>
            </thead>
            <tbody class="tw-font-sans tw-font-normal tw-text-gray-800">
            {% for group in groups %}
            <tr class="tw-text-xs tw-font-medium md:tw-text-sm">
                <td class="name !tw-align-middle tw-text-black">{{group.name}}</td>
                <td class="description !tw-align-middle"><span class="tw-inline-block tw-truncate tw-max-w-[200px] lg:tw-max-w-full !tw-align-middle tw-text-black">{{group.description}}</span></td>
                <td class="amount !tw-align-middle !tw-text-right tw-text-black">{{group.app_users.all.count}}</td>
                <td class="!tw-align-middle !tw-text-right">
                    <div class="tw-flex tw-gap-2 tw-justify-end">
                        <a href="" class="edit tw-flex tw-align-center tw-w-[26px] tw-h-[26px] tw-text-black tw-align-middle tw-border tw-border-gray-200 tw-rounded tw-p-1 tw-shadow-sm tw-transition-colors tw-duration-300 hover:tw-border-gray-300 hover:tw-bg-gray-200" data-toggle="modal" data-target="#addUserGroup" data-name="{{group.name}}" data-description="{{group.description}}" data-id="{{group.pk}}">
                            <svg class="tw-w-[16px] tw-h-[16px]">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-pen-line"></use>
                            </svg>
                        </a>

                        <a href="" class="delete tw-flex tw-align-center tw-w-[26px] tw-h-[26px] tw-text-black tw-align-middle tw-border tw-border-gray-200 tw-rounded tw-p-1 tw-shadow-sm tw-transition-colors tw-duration-300 hover:tw-border-gray-300 hover:tw-bg-gray-200" data-toggle="modal" data-target="#confirm-delete" data-name="{{group.name}}" data-id="{{group.pk}}">
                            <svg class="tw-w-[16px] tw-h-[16px]">
                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-trash-2"></use>
                            </svg>
                        </a>
                    </div>
                </td>
            </tr>
            {% endfor %}
            </tbody>
            <tfoot>
            <tr>
                <td colspan="6">
                    <div class="tw-mt-4">
                        <ul class="pagination"></ul>
                    </div>
                </td>
            </tr>
            </tfoot>
        </table>
        {% endif %}
        
        {% include "organisations/users-and-groups/components/add-groups-modal.html" %}
        {% trans "Delete" as delete_action %}
        {% trans "Delete Group" as title %}
        {% trans "Are you sure you want to remove <span id=\"group-name\"></span>?" as translated_body %}
        {% include "partials/confirm-dialog.html" with action=delete_action id="confirm-delete" title=title body=translated_body %}

    </div>
</div>
