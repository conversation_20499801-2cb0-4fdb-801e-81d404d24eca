{% load i18n %}

<div id="installed-software-content">
    {% include "software_inventory/components/common/page_header.html" %}
    <div class="tw-border-b tw-border-gray-300 tw-mb-4 tw-font-sans">
        <div class="tw-flex tw-space-x-2 tw-font-sans">
            <button class="tw-w-fit tw-py-2 tw-px-4 tw-border-b-4 tw-border-blue-500 tw-font-semibold" id="tab-main-software">
                {% trans "Software" %}
            </button>
            <button class="tw-relative tw-flex tw-items-center tw-border-b-4 tw-border-transparent tw-justify-center tw-gap-2 tw-w-fit tw-py-2 tw-px-4 tw-text-gray-600 hover:tw-text-gray-700 tw-group" id="tab-main-patch-history"
                {% if view_level == "app_install" %}
                    hx-get="{{ app_install.url_patch_history }}"
                {% else %}
                    hx-get="{% url 'organisations:patch-history' organisation.secure_id %}"
                    hx-push-url="true"
                {% endif %}
                hx-target="#installed-software-content"
                hx-swap="outerHTML"
            >
                <span>{% trans "Patch history" %}</span>
            </button>
        </div>
    </div>
    <div class="patch-summary-modal-container"></div>
    {% include "software_inventory/components/common/outdated_app_banner.html" %}
    {% include "software_inventory/components/common/summary_counters.html" %}
    {% include "software_inventory/components/package_list/tab_content.html" %}
</div>