{% load i18n %}
{% load static %}
{% load divide_tags %}

{% trans "Unknown" as unknown %}
{% trans "Yes,No" as yesno_text %}
{% trans "Severity" as severity_title %}
{% trans "Vulnerabilities" as vulnerabilities_title %}
{% trans "Devices" as devices_title %}
<div class="tw-mb-6">
    {% if not installed_devices_count %}
        <div class="tw-flex tw-justify-center tw-items-center">
            <div class="tw-w-1/3 tw-bg-white tw-py-6 tw-text-center tw-text-gray-600">
                {% if bulk_enrollment %}
                    {% include "software_inventory/components/common/empty_states/bulk_no_cap_installed.html" %}
                {% else %}
                    {% include "software_inventory/components/common/empty_states/individual_no_cap_installed.html" %}
                {% endif %}
            </div>
        </div>
    {% else %}
        <div class="tw-flex tw-flex-col md:tw-flex-row md:tw-items-center tw-gap-4 tw-mb-4">
            <div class="tw-flex tw-flex-wrap tw-items-center tw-gap-2">
                {% trans "Search for software name" as search_placeholder %}
                <div class="tw-relative">
                    {% include "software_inventory/components/common/search.html" with placeholder=search_placeholder %}
                </div>
                {% include "software_inventory/components/package_list/table/filters/main.html" %}
            </div>
            <div class="tw-flex tw-flex-wrap tw-items-center tw-gap-2 tw-justify-start md:tw-ml-auto">
                {% url 'organisations:installed-software-report' organisation.secure_id as organisation_report_url %}
                {% include "software_inventory/components/common/export_button.html" with app_install_report_url=app_install.url_installed_software_report organisation_report_url=organisation_report_url %}
                <div class="tw-flex tw-items-center tw-ml-auto">
                    {% if view_level == "app_install" %}
                        {% include "software_inventory/components/package_list/view_level/app_install/patch_button.html" %}
                    {% elif view_level == "organisation" and organisation.has_patch_management_multiselection_enabled %}
                        {% include "software_inventory/components/package_list/view_level/organisation/patch_button.html" %}
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="tw-border tw-rounded-lg tw-overflow-x-auto">
            {{ patchable_packages_ids|json_script:"id_patchable_packages" }}
            <table class="tw-text-left tw-w-full tw-min-w-max tw-text-sm">
                <thead class="tw-border-b tw-bg-gray-100 tw-text-gray-600 tw-h-10">
                    <tr
                        hx-target="#tab-main-content"
                        hx-swap="outerHTML"
                        {% if not view_level == "app_install" %}
                            hx-push-url="true"
                        {% endif %}
                    >
                        {% if view_level == "app_install" or view_level == "organisation" and organisation.has_patch_management_multiselection_enabled %}
                            <th scope="col" class="tw-px-2 tw-py-2 tw-w-12">
                                <input id="id_selected_package_all" name="selected_package_all" type="checkbox" class="tw-form-checkbox tw-h-4 tw-w-4 tw-text-blue-600 tw-border-gray-300 tw-rounded focus:tw-ring-blue-500 focus:tw-ring-offset-gray-100" />
                            </th>
                        {% endif %}
                        <th scope="col" class="tw-px-2 tw-py-2 tw-w-32">
                            {% include "software_inventory/components/package_list/table/th_with_ordering.html" with th_width=12 th_order_by="highest_severity" th_title=severity_title %}
                        </th>
                        <th scope="col" class="tw-px-2 tw-py-2 tw-min-w-52">{% trans "Name" %}</th>
                        <th scope="col" class="tw-px-2 tw-py-2 tw-w-56">{% trans "Version" %}</th>
                        <th scope="col" class="tw-px-2 tw-py-2 tw-w-[8.5rem]">{% trans "Vendor" %}</th>
                        <th scope="col" class="tw-px-2 tw-py-2 tw-w-[8.5rem] tw-justify-end">
                        {% include "software_inventory/components/package_list/table/th_with_ordering.html" with th_width=12 th_order_by="cve_count" th_title=vulnerabilities_title th_justify_class="tw-justify-end"%}
                        </th>
                        {% if view_level != "app_install" %}
                        <th scope="col" class="tw-px-2 tw-py-2 tw-w-24">
                        {% include "software_inventory/components/package_list/table/th_with_ordering.html" with th_width=9 th_order_by="device_count" th_title=devices_title th_justify_class="tw-justify-end"%}
                        </th>
                        {% endif %}

                        <th scope="col" class="tw-px-2 tw-py-2 tw-w-36">
                            <span class="tw-flex tw-items-center tw-gap-x-1">
                                {% trans "Patch detected" %}
{#                                <svg class="tw-w-4 tw-h-4">#}
{#                                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-arrow-up-down"></use>#}
{#                                </svg>#}
                            </span>
                        </th>
                        <th scope="col" class="tw-w-12"></th>
                    </tr>
                </thead>
                <tbody class="tw-text-left tw-bg-white tw-text-gray-800">
                    {% for package in packages %}
                        <tr class="tw-border-b tw-h-12 last:tw-border-b-0 tw-cursor-pointer package hover:tw-bg-gray-50"
                        {% if view_level == "app_install" %}
                            hx-get="{% call_method app_install "url_package_details" package_id=package.id %}"
                        {% else %}
                            hx-get="{% url 'organisations:package-details' organisation.secure_id package.id %}"
                        {% endif %}
                        hx-target=".details-drawer-container"
                        hx-swap="innerHTML"
                        hx-trigger="click"
                        hx-indicator=".package-details-loader-{{ forloop.counter }}"
                        >
                            {% if view_level == "app_install" or view_level == "organisation" and organisation.has_patch_management_multiselection_enabled %}
                                <td class="tw-px-2 tw-py-2">
                                    {% include "software_inventory/components/package_list/table/rows/checkbox.html" %}
                                </td>
                            {% endif %}
                            <td>
                                <div class="tw-px-2 tw-py-2">
                                    {% include "software_inventory/components/common/severity_display.html" with base_score=package.highest_severity source=package.source %}
                                </div>
                            </td>
                            <td class="tw-px-2 tw-py-2 tw-truncate tw-whitespace-nowrap tw-overflow-hidden tw-max-w-[12.5rem]">
                                {% if package.product|length > 17 %}
                                    {% include "software_inventory/components/common/tooltip.html" with tooltip_text=package.product %}
                                {% endif %}
                                {{ package.product|default:unknown }}
                            </td>
                            <td class="tw-px-2 tw-py-2">
                                {% include "software_inventory/components/package_list/table/rows/version.html" %}
                            </td>
                            <td class="tw-px-2 tw-py-2">
                                {% if package.vendor|length > 17 %}
                                    {% include "software_inventory/components/common/tooltip.html" with tooltip_text=package.vendor %}
                                {% endif %}
                                {{ package.vendor|default:unknown }}
                            </td>
                            <td class="tw-px-2 tw-py-2 tw-text-right">{{ package.cve_count }}</td>
                            {% if view_level != "app_install" %}
                                <td class="tw-px-2 tw-py-2 tw-text-right">{{ package.device_count|default:0 }}</td>
                            {% endif %}
                            <td class="tw-px-2 tw-py-2">
                                {% include "software_inventory/components/package_list/table/rows/patch_detected_date.html" %}
                            </td>
                            <td class="tw-px-2 tw-py-2 tw-text-center">
                                <div class="load-package-details package-details-loader-{{ forloop.counter }}">
                                    <svg class="tw-w-4 tw-h-4">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-chevron-right"></use>
                                    </svg>
                                    <div class="cssload-speeding-wheel hidden tw-w-4 tw-h-4"></div>
                                </div>
                            </td>
                        </tr>
                    {% empty %}
                        <tr class="package">
                            {% include "software_inventory/components/package_list/no_records.html" %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% include "software_inventory/components/common/pagination.html" %}
    {% endif %}
</div>