{% load static %}

{% block extra-css %}
    <style>
        tr.package {
            transform: translateY(5px); /* Start below */
            animation: bounceIn 0.2s ease-out forwards;
        }
        @keyframes bounceIn {
            from { transform: translateY(-5px); }
            50% { transform: translateY(5px); }
            to { transform: translateY(0); }
        }
        .load-package-details.htmx-request > .cssload-speeding-wheel {
            display: inline-block !important;
        }
        .load-package-details.htmx-request > svg {
            display: none !important;
        }
        #search-loader.htmx-request > .cssload-speeding-wheel {
            display: inline-block !important;
        }
    </style>
{% endblock extra-css %}

<div class="details-drawer-container"></div>
<div class="tw-p-4 white-box tw-border tw-font-sans" id="tab-main-content">
    <div class="tabs">
        <div id="tab-content-main-software">
            {% include "software_inventory/components/package_list/table_and_controls.html" %}
        </div>
    </div>
</div>