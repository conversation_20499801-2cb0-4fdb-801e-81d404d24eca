{% load static %}
{% load url_params %}

{% with check_option_value=option_value|default:option_name|lower %}
    <label class="tw-flex tw-items-center tw-justify-between tw-w-full tw-cursor-pointer">
        <div class="tw-flex tw-items-center">
            <input type="checkbox" class="!tw-mr-2"
                {% if check_option_value in applied_options %}
                    checked
                {% endif %}
                hx-get="{{ pagination_url }}{% toggle_query_param_value filter_name check_option_value %}"
            />
            <span class="tw-flex tw-items-center tw-justify-center tw-gap-1">
                {% if option_icon %}
                    <svg class="tw-w-6 tw-h-6 tw-scale-[0.7] tw-text-gray-500">
                        <use href="{% static 'icons/icons-sprite.svg' %}#{{ option_icon }}"></use>
                    </svg>
                {% endif %}
                <span class="tw-font-medium">{{ option_name }}</span>
            </span>
        </div>
        {% if option_counter is not None %}
            <span class="tw-text-right">{{ option_counter }}</span>
        {% endif %}
    </label>
{% endwith %}