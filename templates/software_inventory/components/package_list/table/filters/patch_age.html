{% load i18n %}
{% load static %}
{% load url_params %}

{% trans "Last 14 days" as days_14 %}
{% trans "Last 30 days" as month_1 %}
{% trans "Over 90 days" as month_3_plus %}

{% with filters_applied.filter_patch_age|length as patch_age_filters_applied %}
    <div class="tw-relative tw-inline-block tw-text-left tw-group">
        <button class="tw-border tw-border-dashed tw-h-8 tw-px-3 tw-py-2 tw-rounded-md tw-flex tw-items-center tw-gap-x-2 tw-text-sm hover:tw-bg-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar-icon lucide-calendar"><path d="M8 2v4"/><path d="M16 2v4"/><rect width="18" height="18" x="3" y="4" rx="2"/><path d="M3 10h18"/></svg>
            <span>{% trans "Patch detected" %}</span>
            {% if patch_age_filters_applied %}
                <span class="tw-text-xs tw-font-medium tw-text-gray-600 tw-bg-gray-100 tw-px-2 tw-py-0.5 tw-rounded-full">{{ patch_age_filters_applied }}</span>
            {% endif %}
        </button>

        <div class="tw-absolute tw-z-10 tw-top-full tw-w-56 tw-origin-top-left tw-bg-white tw-border tw-border-gray-200 tw-rounded-md tw-shadow-lg tw-py-2 tw-hidden group-hover:tw-block"
            hx-target="#tab-main-content"
            hx-swap="outerHTML"
            {% if not view_level == "app_install" %}
                hx-push-url="true"
            {% endif %}
        >
            <div class="tw-px-4 tw-space-y-2">
                {% include "software_inventory/components/package_list/table/filters/checkbox_input.html" with filter_name="filter_patch_age" option_name=days_14 applied_options=filters_applied.filter_patch_age %}
                {% include "software_inventory/components/package_list/table/filters/checkbox_input.html" with filter_name="filter_patch_age" option_name=month_1 applied_options=filters_applied.filter_patch_age %}
                {% include "software_inventory/components/package_list/table/filters/checkbox_input.html" with filter_name="filter_patch_age" option_name=month_3_plus applied_options=filters_applied.filter_patch_age %}
            </div>
        </div>
    </div>
{% endwith %}