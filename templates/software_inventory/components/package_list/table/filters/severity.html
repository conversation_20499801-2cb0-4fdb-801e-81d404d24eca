{% load i18n %}
{% load static %}
{% load url_params %}

{% trans "Critical" as critical %}
{% trans "High" as high %}
{% trans "Medium" as medium %}
{% trans "Low" as low %}
{% trans "Info" as info %}
{% trans "Unknown" as unknown %}

{% with filters_applied.filter_severity|length as severity_filters_applied %}
    <div class="tw-relative tw-inline-block tw-text-left tw-group">
        <button class="tw-border tw-border-dashed tw-h-8 tw-px-3 tw-py-2 tw-rounded-md tw-flex tw-items-center tw-gap-x-2 tw-text-sm hover:tw-bg-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-plus-icon lucide-circle-plus"><circle cx="12" cy="12" r="10"/><path d="M8 12h8"/><path d="M12 8v8"/></svg>
            <span>{% trans "Severity" %}</span>
            {% if severity_filters_applied %}
                <span class="tw-text-xs tw-font-medium tw-text-gray-600 tw-bg-gray-100 tw-px-2 tw-py-0.5 tw-rounded-full">{{ severity_filters_applied }}</span>
            {% endif %}
        </button>

        <div class="tw-absolute tw-z-10 tw-top-full tw-w-56 tw-origin-top-left tw-bg-white tw-border tw-border-gray-200 tw-rounded-md tw-shadow-lg tw-py-2 tw-hidden group-hover:tw-block"
            hx-target="#tab-main-content"
            hx-swap="outerHTML"
            {% if not view_level == "app_install" %}
                hx-push-url="true"
            {% endif %}
        >
            <div class="tw-px-4 tw-space-y-2">
                {% include "software_inventory/components/package_list/table/filters/checkbox_input.html" with filter_name="filter_severity" option_name=critical applied_options=filters_applied.filter_severity option_counter=severity_counts.critical option_icon="icon-cloud-lightning" %}
                {% include "software_inventory/components/package_list/table/filters/checkbox_input.html" with filter_name="filter_severity" option_name=high applied_options=filters_applied.filter_severity option_counter=severity_counts.high option_icon="icon-cloud-rain-wind" %}
                {% include "software_inventory/components/package_list/table/filters/checkbox_input.html" with filter_name="filter_severity" option_name=medium applied_options=filters_applied.filter_severity option_counter=severity_counts.medium option_icon="icon-cloud" %}
                {% include "software_inventory/components/package_list/table/filters/checkbox_input.html" with filter_name="filter_severity" option_name=low applied_options=filters_applied.filter_severity option_counter=severity_counts.low option_icon="icon-cloud-sun" %}
                {% include "software_inventory/components/package_list/table/filters/checkbox_input.html" with filter_name="filter_severity" option_name=info applied_options=filters_applied.filter_severity option_counter=severity_counts.info option_icon="icon-sun" %}
                {% include "software_inventory/components/package_list/table/filters/checkbox_input.html" with filter_name="filter_severity" option_name=unknown applied_options=filters_applied.filter_severity option_counter=severity_counts.unknown    option_icon="icon-search-code" %}
            </div>

            <div class="tw-border-t tw-mt-2 tw-pt-2 tw-text-center">
                <button class="tw-text-sm tw-text-blue-600 tw-font-medium hover:tw-underline"
                    hx-get="{{ pagination_url }}{% toggle_query_param_value 'filter_severity' clear=True %}"
                >{% trans "Clear filters" %}</button>
            </div>
        </div>
    </div>
{% endwith %}