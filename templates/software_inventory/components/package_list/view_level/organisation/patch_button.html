{% load i18n %}

<button id="patch-package-button" class="tw-group tw-bg-blue-500 tw-text-white tw-px-4 tw-py-2 tw-rounded-md tw-font-semibold tw-flex tw-items-center tw-gap-x-1 disabled:tw-bg-gray-300 disabled:tw-text-gray-500 disabled:tw-cursor-not-allowed" disabled
    hx-post="{% url 'organisations:multi-selection-patch-summary' organisation.secure_id %}"
    hx-target=".patch-summary-modal-container"
    hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
    hx-swap="innerHTML"
    hx-trigger="click"
    hx-vals="js:{'selected_packages': CheckboxStateManager.getSelected('selected-packages-to-patch-{{ organisation.secure_id }}') }"
>
    <span class="button-label group-[.htmx-request]:tw-hidden">
        {% trans "Patch Now" %}<span></span>
    </span>
    <div class="cssload-speeding-wheel tw-border-l-transparent tw-border-r-transparent tw-border-t-white tw-border-b-white tw-hidden group-[.htmx-request]:tw-block tw-w-5 tw-h-5"></div>
</button>