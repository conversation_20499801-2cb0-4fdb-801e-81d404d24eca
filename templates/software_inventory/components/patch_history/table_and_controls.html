{% load i18n %}
{% load static %}
{% load divide_tags %}

{% trans "Unknown" as unknown %}
{% include "software_inventory/components/common/message_block.html" with tag="patch-history" %}
<div class="tw-mb-6">
    {% if not attempts_count %}
        {% include "software_inventory/components/patch_history/no_records.html" %}
    {% else %}
        <div class="tw-flex tw-gap-2 tw-mb-4">
            {% trans "Search for software name" as search_placeholder %}
            {% include "software_inventory/components/common/search.html" with placeholder=search_placeholder %}
            {% include "software_inventory/components/patch_history/filters/main.html" %}
        </div>
        <div class="tw-border tw-rounded-lg tw-overflow-hidden">
            <table class="tw-w-full tw-text-left tw-table-fixed tw-text-sm">
                <thead class="tw-border-b tw-bg-gray-50">
                    <tr>
                        <th class="tw-w-[20%] tw-px-4 tw-py-2 tw-font-semibold">{% trans "Name" %}</th>
                        <th class="tw-w-[10%] tw-px-4 tw-py-2 tw-font-semibold">{% trans "Version" %}</th>
                        <th class="tw-w-[18%] tw-px-4 tw-py-2 tw-font-semibold">{% trans "Vendor" %}</th>
                        <th class="tw-w-[10%] tw-px-4 tw-py-2 tw-font-semibold">{% trans "Patch status" %}</th>
                        <th class="tw-w-[15%] tw-px-4 tw-py-2 tw-font-semibold">{% trans "Started" %}</th>
                        <th class="tw-w-[15%] tw-px-4 tw-py-2 tw-font-semibold">{% trans "Finished" %}</th>
                        <th class="tw-w-[4%]"></th>
                    </tr>
                </thead>
                <tbody class="tw-text-left tw-bg-white">
                    {% for attempt in attempts %}
                        <tr class="tw-border-b last:tw-border-b-0 tw-h-12 tw-cursor-pointer patch hover:tw-bg-gray-50 tw-hover:tw-border-b"
                        {% if view_level == "app_install" %}
                            hx-get="{% call_method app_install "url_patch_installer_details" attempt_id=attempt.id %}"
                        {% else %}
                            hx-get="{% url 'organisations:patch-installer-details' organisation.secure_id attempt.id %}"
                        {% endif %}
                        hx-target=".details-drawer-container"
                        hx-swap="innerHTML"
                        hx-trigger="click"
                        hx-indicator=".patch-details-loader-{{ forloop.counter }}"
                        >
                            <td class="tw-px-4 tw-py-2 tw-text-gray-800 tw-truncate tw-whitespace-nowrap tw-overflow-hidden tw-group">
                                {% with product_name=attempt.product.name|default:unknown %}
                                    {% if product_name|length > 17 %}
                                        {% include "software_inventory/components/common/tooltip.html" with tooltip_text=product_name %}
                                    {% endif %}
                                    {{ product_name }}
                                {% endwith %}
                            </td>
                            <td class="tw-px-4 tw-py-2 tw-text-gray-800 tw-group">
                                {{ attempt.from_version }}
                                <span class="tw-ml-1">
                                    <svg class="tw-w-6 tw-h-6 tw-scale-[0.7] tw-text-green-700">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-trending-up"></use>
                                    </svg>
                                    {{ attempt.to_version }}
                                </span>
                            </td>
                            <td class="tw-px-4 tw-py-2 tw-text-gray-800 tw-truncate tw-whitespace-nowrap tw-overflow-hidden">
                                {% with product_vendor=attempt.product.vendor.name|default:unknown %}
                                    {% if product_vendor|length > 17 %}
                                        {% include "software_inventory/components/common/tooltip.html" with tooltip_text=product_vendor %}
                                    {% endif %}
                                    {{ product_vendor }}
                                {% endwith %}
                            </td>
                            <td class="tw-px-4 tw-py-2 tw-text-blue-400 tw-whitespace-nowrap">
                                {% include "software_inventory/components/patch_history/common/short_status_display.html" with status_display=attempt.short_status status_value=attempt.aggregated_status %}
                            </td>
                            <td class="tw-px-4 tw-py-2 tw-text-gray-800 tw-truncate tw-whitespace-nowrap tw-overflow-hidden tw-group">
                                {{ attempt.created|date:"d/m/Y H:i" }}
                            </td>
                            <td class="tw-px-4 tw-py-2 tw-text-gray-800 tw-truncate tw-whitespace-nowrap tw-overflow-hidden tw-group">
                                {% if attempt.is_finished %}
                                    {{ attempt.finished_at|date:"d/m/Y H:i" }}
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td class="tw-px-4 tw-w-12">
                                <div class="load-patch-details patch-details-loader-{{ forloop.counter }}">
                                    <svg class="tw-w-6 tw-h-6 tw-scale-[0.7]">
                                        <use href="{% static 'icons/icons-sprite.svg' %}#icon-chevron-right"></use>
                                    </svg>
                                    <div class="cssload-speeding-wheel hidden tw-w-6 tw-h-6"></div>
                                </div>
                            </td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="8" class="tw-px-4 tw-py-8 tw-text-center tw-text-gray-500">
                                {% trans "No patch history records found." %}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

    {% include "software_inventory/components/common/pagination.html" with hide_selection=True %}
</div>