{% load i18n %}

<div id="installed-software-content">
    {% include "software_inventory/components/common/page_header.html" %}
    <div class="tw-border-b tw-border-gray-300 tw-mb-4">
        <div class="tw-flex tw-space-x-2 tw-font-sans">
            <button class="tw-relative tw-flex tw-items-center tw-justify-center tw-gap-2 tw-border-b-4 tw-border-transparent tw-py-2 tw-w-fit tw-px-4 tw-text-gray-500 hover:tw-text-gray-700 tw-group" id="tab-main-software"
                {% if view_level == "app_install" %}
                    hx-get="{{ app_install.url_installed_software }}"
                {% else %}
                    hx-get="{% url 'organisations:installed-software' organisation.secure_id %}"
                    hx-push-url="true"
                {% endif %}
                hx-target="#installed-software-content"
                hx-swap="outerHTML"
            >
                <span>{% trans "Software" %}</span>
            </button>
            <button class="tw-w-fit tw-py-2 tw-px-4 tw-border-b-4 tw-border-blue-500 tw-font-semibold" id="tab-main-patch-history">
                {% trans "Patch history" %}
            </button>
        </div>
    </div>
    {% include "software_inventory/components/common/outdated_app_banner.html" %}
    {% include "software_inventory/components/patch_history/tab_content.html" %}
</div>