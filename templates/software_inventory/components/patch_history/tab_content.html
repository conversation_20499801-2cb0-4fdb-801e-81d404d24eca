{% load static %}

{% block extra-css %}
    <style>
        tr.patch {
            transform: translateY(5px); /* Start below */
            animation: bounceIn 0.2s ease-out forwards;
        }
        @keyframes bounceIn {
            from { transform: translateY(-5px); }
            50% { transform: translateY(5px); }
            to { transform: translateY(0); }
        }
        .load-patch-details.htmx-request > .cssload-speeding-wheel {
            display: inline-block !important;
        }
        .load-patch-details.htmx-request > svg {
            display: none !important;
        }
        #search-loader.htmx-request > .cssload-speeding-wheel {
            display: inline-block !important;
        }
    </style>
{% endblock extra-css %}

<div class="details-drawer-container"></div>
<div class="white-box tw-border" id="tab-main-content">
    <div class="tabs">
        <div id="tab-content-main-patch-history" class="tw-font-sans">
            {% include "software_inventory/components/patch_history/table_and_controls.html" %}
        </div>
    </div>
</div>