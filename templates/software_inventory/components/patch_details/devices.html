{% load i18n %}
{% load static %}
{% trans "Unknown" as unknown %}
{% load divide_tags %}

{% waffle_flag_is_active_for_user "debug_patch_event_log_details" request.user as debug_patch_event_log_details_enabled_for_user %}

<div class="tw-border tw-rounded-lg tw-overflow-hidden tw-mx-6">
    <div class="tw-overflow-auto">
        <table class="tw-w-full tw-text-left tw-table-fixed tw-overflow-auto">
            <thead class="tw-border-b tw-bg-gray-100">
                <tr>
                    <th class="tw-w-[20%] tw-px-4 tw-py-2 tw-font-bold">{% trans "Device" %}</th>
                    <th class="tw-w-[20%] tw-px-4 tw-py-2 tw-font-bold">{% trans "Product" %}</th>
                    <th class="tw-w-[20%] tw-px-4 tw-py-2 tw-font-bold">{% trans "Date/Time" %}</th>
                    <th class="tw-w-[20%] tw-px-4 tw-py-2 tw-font-bold">{% trans "Patch status" %}</th>
                    <th class="tw-w-[35%] tw-px-4 tw-py-2 tw-font-bold">{% trans "Details" %}</th>
                    <th class="tw-w-[5%] tw-px-4 tw-py-2 tw-font-bold"></th>
                </tr>
            </thead>
            <tbody class="tw-bg-white">
                {% for attempt in patch_attempts %}
                    <tr class="hover:tw-bg-gray-50 tw-cursor-pointer" onclick="toggleDetails(this)">
                        <td class="tw-px-4 tw-py-2 tw-text-gray-800 tw-truncate tw-max-w-[12.5rem]">
                            {{ attempt.app_install.hostname|default:unknown }}
                        </td>
                        <td class="tw-px-4 tw>
                            <span class="tw-text-gray-800 tw-truncate tw-max-w-[12.5rem]">
                                {{ attempt.product.name }}
                            </span>
                        </td>
                        <td class="tw-px-4 tw-py-2 tw-text-gray-800">
                            {{ attempt.latest_event.created|date:"d/m/Y H:i" }}
                        </td>
                        <td class="tw-px-4 tw-py-2">
                            {% include "software_inventory/components/patch_history/common/short_status_display.html" with status_display=attempt.short_status status_value=attempt.aggregated_status %}
                        </td>
                        <td class="tw-px-4 tw-py-2 tw-text-gray-800 tw-truncate tw-max-w-[12.5rem]">
                            {% with event=attempt.latest_event %}
                                {% if event.get_error_message %}
                                    {{ event.get_error_message }}
                                {% else %}
                                    {{ event.get_status_display }}
                                {% endif %}
                            {% endwith %}
                        </td>
                        <td class="tw-px-4 tw-py-2">
                            <svg class="tw-w-6 tw-h-6 tw-scale-[0.7]">
                                {% include "software_inventory/components/common/toggle_icon.html" %}
                            </svg>
                        </td>
                    </tr>
                    <tr class="tw-border-b last:tw-border-b-0">
                        <td colspan="5">
                            <div class="details-row tw-bg-gray-50 tw-px-0">
                                <table class="tw-w-full tw-table-fixed tw-bg-white">
                                    <tbody class="tw-text-sm tw-border-t">
                                        {% for event in attempt.event_logs.all %}
                                            <tr class="tw-bg-gray-50">
                                                <td class="tw-w-[20%] tw-px-4 tw-py-3"></td>
                                                <td class="tw-w-[20%] tw-px-4 tw-py-3">{{ event.created|date:"d/m/Y H:i" }}</td>
                                                <td class="tw-w-[20%] tw-px-4 tw-py-3">
                                                    {% include "software_inventory/components/patch_history/common/short_status_display.html" with status_display=event.short_status status_value=event.aggregated_status %}
                                                </td>
                                                <td class="tw-w-[35%] tw-px-4 tw-py-3 tw-max-w-[12.5rem] tw-truncate">
                                                    {% if event.get_error_message %}
                                                        {{ event.get_error_message }}
                                                    {% else %}
                                                        {{ event.get_status_display }}
                                                    {% endif %}
                                                </td>
                                                <td class="tw-w-[5%]"></td>
                                            </tr>
                                            {% if event.details_public_facing or event.details and debug_patch_event_log_details_enabled_for_user %}
                                                <tr class="tw-bg-gray-50 tw-text-gray-700">
                                                    <td colspan="5">
                                                        <div class="tw-mx-2 tw-py-3 tw-px-4 tw-max-h-[300px] tw-overflow-y-auto tw-rounded-md tw-bg-gray-100 tw-border tw-border-gray-300 tw-text-sm tw-font-mono">
                                                            {% if event.error_code %}
                                                                <div class="tw-mb-1">{{ event.get_error_message }}</div>
                                                            {% endif %}
                                                            {{ event.details_public_facing|default:"..." }}
                                                            {% if debug_patch_event_log_details_enabled_for_user %}
                                                                {% include "software_inventory/components/patch_details/event_debug_info.html" %}
                                                            {% endif %}
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endif %}
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

{% block extra-css %}
<style>
    /* Animate toggle for detail rows */
    .details-row {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out;
    }
</style>
{% endblock extra-css %}
