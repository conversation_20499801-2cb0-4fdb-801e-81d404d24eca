{% load i18n %}
{% load static %}

{% trans "Unknown" as unknown %}

<div class="tw-flex tw-justify-between tw-items-start tw-px-6 tw-py-4">
    <div class="tw-flex tw-flex-col tw-gap-1">
        <h2 class="tw-text-xl tw-font-bold tw-text-gray-900 tw-m-0">{{ patch_attempt.scheduled_product_installer.opswat_product_patch_installer.product.name }}</h2>
        <p class="tw-text-sm tw-text-gray-500 tw-font-semibold">{{ patch_attempt.from_version }}</p>
    </div>
    <button class="tw-text-gray-500 hover:tw-text-gray-700 tw-text-xl" onclick="closeDetailsDrawer()">
        <svg class="tw-w-4 tw-h-4">
            <use href="/static/icons/icons-sprite.svg#icon-x"></use>
        </svg>
    </button>
</div>

<div class="tw-px-6 tw-py-4">
    <div class="tw-flex tw-items-center tw-mt-2">
        <p class="tw-text-gray-700 tw-w-[70px]">{% trans "Version" %}:</p>
        <span class="tw-text-gray-600 tw-font-bold">v{{ patch_attempt.to_version }}</span>
    </div>
    <div class="tw-flex tw-items-center tw-mt-2">
        <p class="tw-text-gray-700 tw-w-[70px]">{% trans "Started" %}:</p>
        <span class="tw-text-gray-600 tw-font-bold">{{ patch_attempt.created|date:"d/m/Y H:i" }}</span>
    </div>
    <div class="tw-flex tw-items-center tw-mt-2">
        <p class="tw-text-gray-700 tw-w-[70px]">{% trans "Finished" %}:</p>
        <span class="tw-text-gray-600 tw-font-bold">
            {% if patch_attempt.is_finished %}
                {{ patch_attempt.finished_at|date:"d/m/Y H:i" }}
            {% else %}
            -
            {% endif %}
        </span>
    </div>
</div>