{% load i18n %}
{% load static %}

{% if source == SOURCE_OPSWAT %}
    {% if base_score > 8.0 and base_score <= 10.0 %}
        <span class="tw-bg-red-50 tw-text-red-700 tw-py-1 tw-px-2.5 tw-gap-1 tw-text-xs tw-rounded-xl tw-inline-flex tw-items-center tw-whitespace-nowrap">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-lightning-icon lucide-cloud-lightning"><path d="M6 16.326A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 .5 8.973"/><path d="m13 12-3 5h4l-3 5"/></svg>
            {{ base_score }} {% if not short %}{% trans "Critical" %}{% endif %}
        </span>
    {% elif base_score > 6.0 and base_score <= 8.0 %}
        <span class="tw-bg-red-50 tw-text-red-700 tw-py-1 tw-px-2.5 tw-gap-1 tw-text-xs tw-rounded-xl tw-inline-flex tw-items-center tw-whitespace-nowrap">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-rain-wind-icon lucide-cloud-rain-wind"><path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"/><path d="m9.2 22 3-7"/><path d="m9 13-3 7"/><path d="m17 13-3 7"/></svg>
            {{ base_score }} {% if not short %}{% trans "High" %}{% endif %}
        </span>
    {% elif base_score > 4.0 and base_score <= 6.0 %}
        <span class="tw-bg-orange-50 tw-text-orange-700 tw-py-1 tw-px-2.5 tw-gap-1 tw-text-xs tw-rounded-xl tw-inline-flex tw-items-center tw-whitespace-nowrap">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-icon lucide-cloud"><path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z"/></svg>
            {{ base_score }} {% if not short %}{% trans "Medium" %}{% endif %}
        </span>
    {% elif base_score > 0 and base_score <= 4.0 %}
        <span class="tw-bg-gray-50 tw-text-gray-700 tw-py-1 tw-px-2.5 tw-gap-1 tw-text-xs tw-rounded-xl tw-inline-flex tw-items-center tw-whitespace-nowrap">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-sun-icon lucide-cloud-sun"><path d="M12 2v2"/><path d="m4.93 4.93 1.41 1.41"/><path d="M20 12h2"/><path d="m19.07 4.93-1.41 1.41"/><path d="M15.947 12.65a4 4 0 0 0-5.925-4.128"/><path d="M13 22H7a5 5 0 1 1 4.9-6H13a3 3 0 0 1 0 6Z"/></svg>
            {{ base_score }} {% if not short %}{% trans "Low" %}{% endif %}
        </span>
    {% elif base_score == 0.0 %}
        <span class="tw-bg-green-50 tw-text-green-700 tw-py-1 tw-px-2.5 tw-gap-1 tw-text-xs tw-rounded-xl tw-inline-flex tw-items-center tw-whitespace-nowrap">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sun-icon lucide-sun"><circle cx="12" cy="12" r="4"/><path d="M12 2v2"/><path d="M12 20v2"/><path d="m4.93 4.93 1.41 1.41"/><path d="m17.66 17.66 1.41 1.41"/><path d="M2 12h2"/><path d="M20 12h2"/><path d="m6.34 17.66-1.41 1.41"/><path d="m19.07 4.93-1.41 1.41"/></svg>
            {% trans "Info" %}
        </span>
    {% endif %}
{% else %}
    {% if base_score >= 9.0 and base_score <= 10.0 %}
        <span class="tw-bg-red-50 tw-text-red-700 tw-py-1 tw-px-2.5 tw-gap-1 tw-text-xs tw-rounded-xl tw-inline-flex tw-items-center tw-whitespace-nowrap">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-lightning-icon lucide-cloud-lightning"><path d="M6 16.326A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 .5 8.973"/><path d="m13 12-3 5h4l-3 5"/></svg>
            {{ base_score }} {% if not short %}{% trans "Critical" %}{% endif %}
        </span>
    {% elif base_score >= 7.0 and base_score < 9.0 %}
        <span class="tw-bg-red-50 tw-text-red-700 tw-py-1 tw-px-2.5 tw-gap-1 tw-text-xs tw-rounded-xl tw-inline-flex tw-items-center tw-whitespace-nowrap">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-rain-wind-icon lucide-cloud-rain-wind"><path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"/><path d="m9.2 22 3-7"/><path d="m9 13-3 7"/><path d="m17 13-3 7"/></svg>
            {{ base_score }} {% if not short %}{% trans "High" %}{% endif %}
        </span>
    {% elif base_score >= 4.0 and base_score < 7.0 %}
        <span class="tw-bg-orange-50 tw-text-orange-700 tw-py-1 tw-px-2.5 tw-gap-1 tw-text-xs tw-rounded-xl tw-inline-flex tw-items-center tw-whitespace-nowrap">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-icon lucide-cloud"><path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z"/></svg>
            {{ base_score }} {% if not short %}{% trans "Medium" %}{% endif %}
        </span>
    {% elif base_score >= 0.1 and base_score < 4.0 %}
        <span class="tw-bg-gray-50 tw-text-gray-700 tw-py-1 tw-px-2.5 tw-gap-1 tw-text-xs tw-rounded-xl tw-inline-flex tw-items-center tw-whitespace-nowrap">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-sun-icon lucide-cloud-sun"><path d="M12 2v2"/><path d="m4.93 4.93 1.41 1.41"/><path d="M20 12h2"/><path d="m19.07 4.93-1.41 1.41"/><path d="M15.947 12.65a4 4 0 0 0-5.925-4.128"/><path d="M13 22H7a5 5 0 1 1 4.9-6H13a3 3 0 0 1 0 6Z"/></svg>
            {{ base_score }} {% if not short %}{% trans "Low" %}{% endif %}
        </span>
    {% elif base_score == 0.0 %}
        <span class="tw-bg-blue-50 tw-text-blue-700 tw-py-1 tw-px-2.5 tw-gap-1 tw-text-xs tw-rounded-xl tw-inline-flex tw-items-center tw-whitespace-nowrap">
            {% trans "Unknown" %}
        </span>
    {% endif %}
{% endif %}