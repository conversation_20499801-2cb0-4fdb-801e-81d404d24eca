{% load i18n %}
{% load static %}

<div class="btn-group tw-flex tw-items-center tw-ml-auto">
    <button type="button" class="tw-min-h-9 tw-flex tw-items-center tw-px-4 tw-py-1 tw-pl-2 tw-pr-3 tw-ml-auto tw-rounded-md tw-bg-white tw-border tw-text-black tw-transition-colors tw-duration-300 tw-ease-in-out hover:tw-bg-gray-100 hover:tw-text-black focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <svg class="tw-w-6 tw-h-6 tw-scale-[0.7]">
            <use href="{% static 'icons/icons-sprite.svg' %}#icon-download"></use>
        </svg>
        {% trans "Export" %}
    </button>
    <ul class="dropdown-menu dropdown-menu-right">
        <li class="dropdown-header">{% trans "Choose Format" %}</li>
        {% if view_level == "app_install" %}
            <li><a href="{{ app_install_report_url }}">{% trans "CSV" %}</a></li>
            <li><a href="{{ app_install_report_url }}?format=xlsx">{% trans "XLSX" %}</a></li>
        {% else %}
            <li><a href="{{ organisation_report_url }}">{% trans "CSV" %}</a></li>
            <li><a href="{{ organisation_report_url }}?format=xlsx">{% trans "XLSX" %}</a></li>
        {% endif %}
    </ul>
</div>