{% load static %}
{% load i18n %}

{% with zero_or_dash=installed_devices_count|yesno:"0,-" %}
    <div class="tw-flex tw-mb-3">
        <div class="tw-flex tw-flex-col lg:tw-flex-row tw-basis-full tw-justify-between">
            <div class=" tw-flex tw-gap-3 tw-flex-col lg:tw-flex-row tw-basis-full tw-justify-between">
                <div class="tw-flex tw-basis-1/2 lg:tw-basis-full tw-flex-col tw-bg-white tw-rounded-lg tw-p-4 tw-border tw-border-grey-800 tw-relative">
                    <div class="tw-flex tw-flex-row">
                        <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full tw-text-black">{% trans "All" %}</div>
                    </div>
                    <div class="tw-font-sans tw-mt-auto">
                        <span class="tw-text-4xl tw-font-semibold">{{ total_count|default:zero_or_dash }}</span>
                    </div>
                </div>

                <div class="tw-flex tw-basis-1/2 lg:tw-basis-full tw-flex-col tw-bg-white tw-rounded-lg tw-p-4 tw-border tw-border-grey-800 tw-relative">
                    <div class="tw-flex tw-flex-row">
                        <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full tw-text-black">{% trans "Vulnerable" %}</div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-lightning-icon lucide-cloud-lightning tw-text-red-600 tw-flex-shrink-0"><path d="M6 16.326A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 .5 8.973"/><path d="m13 12-3 5h4l-3 5"/></svg>
                    </div>

                    <div class="tw-font-sans tw-mt-auto">
                        <span class="tw-text-4xl tw-font-semibold tw-text-red-600">{{ vulnerable_count|default:zero_or_dash }}</span>
                    </div>
                </div>

                <div class="tw-flex tw-basis-1/2 lg:tw-basis-full tw-flex-col tw-bg-white tw-rounded-lg tw-p-4 tw-border tw-border-grey-800 tw-relative">
                    <div class="tw-flex tw-flex-row">
                        <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full tw-text-black">{% trans "No known vulnerabilities" %}</div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sun-icon lucide-sun tw-text-green-600 tw-flex-shrink-0"><circle cx="12" cy="12" r="4"/><path d="M12 2v2"/><path d="M12 20v2"/><path d="m4.93 4.93 1.41 1.41"/><path d="m17.66 17.66 1.41 1.41"/><path d="M2 12h2"/><path d="M20 12h2"/><path d="m6.34 17.66-1.41 1.41"/><path d="m19.07 4.93-1.41 1.41"/></svg>
                    </div>

                    <div class="tw-font-sans tw-mt-auto">
                        <span class="tw-text-4xl tw-font-semibold tw-text-green-600">{{ safe_count|default:zero_or_dash }}</span>
                    </div>
                </div>

                <div class="tw-flex tw-basis-1/2 lg:tw-basis-full tw-flex-col tw-bg-white tw-rounded-lg tw-p-4 tw-border tw-border-grey-800 tw-relative tw-group">
                    <div class="tw-hidden tw-absolute tw-bottom-[100px] tw-left-0 tw-px-3 tw-py-2 tw-border tw-rounded-lg tw-shadow-[1px_1px_1px_#ccc] tw-bg-white tw-font-sans tw-invisible tw-opacity-0 tw-transition-opacity tw-duration-200 group-hover:tw-inline-block group-hover:tw-visible group-hover:tw-opacity-100">
                        {% trans 'Other software, is software we have detected, however it’s not on our list of checks for vulnerabilities.' %}
                    </div>

                    <div class="tw-flex tw-flex-row">
                        <div class="tw-font-sans tw-text-sm tw-font-semibold tw-content-center tw-basis-full tw-text-black">{% trans "Other software" %}</div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-icon lucide-file tw-text-blue-500 tw-flex-shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/><path d="M14 2v4a2 2 0 0 0 2 2h4"/></svg>
                    </div>

                    <div class="tw-font-sans tw-mt-auto">
                        <span class="tw-text-4xl tw-font-semibold tw-text-blue-500">{{ other_count|default:zero_or_dash }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endwith %}