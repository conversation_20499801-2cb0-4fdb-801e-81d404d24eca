{# ------------------------------------------------------------------------------------------------- #}
{#  Persistent Checkbox State                                                                        #}
{# ------------------------------------------------------------------------------------------------- #}
{# This template block provides a unique storage key for checkbox selection state.                   #}
{# The key is saved in localStorage and depends on the context:                                      #}
{# - Device level: uses app_install.pk as a unique identifier                                        #}
{# - Organisation level: falls back to organisation.secure_id                                        #}
{# This ensures that checkbox state persists even after a page reload.                               #}
{# This template eventually renders a hidden input:                                                  #}
{# <input type="hidden" id="id_packages_to_patch_storage_key" value="selected-packages-to-patch-ID"> #}
{# This Storage Key subsequently will be used by patch_button_and_selection.js script                #}
{# ------------------------------------------------------------------------------------------------- #}
{% with identifier=app_install.pk|default:organisation.secure_id|stringformat:"s" %}
  {% with storage_key="selected-packages-to-patch-"|add:identifier %}
    {{ storage_key|json_script:"id_packages_to_patch_storage_key" }}
  {% endwith %}
{% endwith %}
