{% load i18n %}
{% load static %}


<!-- Full-screen overlay mask -->
<div class="details-drawer-mask tw-fixed tw-inset-0 tw-bg-black/50 tw-z-40" onclick="closeDetailsDrawer()"></div>

<div class="details-drawer tw-font-sans tw-fixed tw-top-0 tw-right-0 tw-pb-6 tw-w-1/2 tw-h-full tw-bg-white tw-shadow-lg tw-overflow-scroll tw-z-50 slide-in">
    <div class="tw-flex tw-justify-between tw-items-start tw-px-6 tw-py-4">
        <div class="tw-flex tw-flex-col tw-gap-1">
            <h2 class="tw-text-xl tw-font-bold tw-text-gray-900 tw-m-0">{{ package.product }}</h2>
            <p class="tw-text-sm tw-text-gray-500 tw-font-semibold">v{{ package.version }}</p>
        </div>
        <button class="tw-text-gray-500 hover:tw-text-gray-700 tw-text-xl" onclick="closeDetailsDrawer()">
            <svg class="tw-w-4 tw-h-4">
                <use href="{% static 'icons/icons-sprite.svg' %}#icon-x"></use>
            </svg>
        </button>
    </div>

    <div class="tw-px-6 tw-py-4">
        <div class="tw-flex tw-items-center tw-mt-2">
            <p class="tw-text-gray-700 tw-w-[120px]">{% trans "Severity" %}:</p>
            {% include "software_inventory/components/common/severity_display.html" with base_score=package.highest_severity source=package.source %}
        </div>
        <div class="tw-flex tw-items-center tw-mt-2">
            <p class="tw-text-gray-700 tw-w-[120px]">{% trans "Patch detected" %}:</p>
            <span class="tw-text-gray-600 tw-font-bold">
                {% include "software_inventory/components/package_list/table/rows/patch_detected_date.html" %}
            </span>
        </div>
        <div class="tw-flex tw-items-center tw-mt-2">
            <p class="tw-text-gray-700 tw-w-[120px]">{% trans "Action" %}:</p>
            <span class="tw-text-gray-600 tw-font-bold">
                {% if package.is_regular_source %}
                    {% trans "Check online for latest updates" %}
                {% else %}
                    {% if package.is_patchable %}
                        {% trans "Update to latest version" %} ({{ package.installer_version }})
                    {% else %}
                        {% if not cves_count %}
                            {% trans "Up to date - no action required" %}
                        {% else %}
                            {% trans "Check online for latest updates" %}
                        {% endif %}
                    {% endif %}
                {% endif %}
            </span>
        </div>
    </div>
    {% include "software_inventory/components/package_details/tab_controllers.html" %}


    <div class="tabs">
        {% include "software_inventory/components/package_details/vulnerabilities_tab.html" %}
        {% include "software_inventory/components/package_details/devices_tab.html" %}
    </div>
</div>