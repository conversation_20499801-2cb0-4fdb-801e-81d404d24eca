{% load i18n %}
{% load static %}

{% trans "Unknown" as unknown %}

<div id="tab-content-devices">
    {% include "software_inventory/components/package_details/search.html" %}
    <div class="tw-border tw-rounded-lg tw-overflow-hidden tw-mx-6">
        <div class="tw-overflow-auto tw-max-h-[calc(100vh-400px)]">
            <table class="tw-w-full tw-text-sm tw-text-left tw-table-fixed" id="package-devices-table">
                <thead class="tw-border-b tw-bg-gray-100">
                    <tr>
                        {% if view_level != "app_install" %}
                            <th class="tw-w-0 tw-px-4 tw-py-2 tw-font-bold tw-group tw-relative">
                                <input type="checkbox" id="all-devices-checkbox" class="tw-form-checkbox tw-h-4 tw-w-4 tw-text-blue-600 tw-border-gray-300 tw-rounded focus:tw-ring-blue-500 focus:tw-ring-offset-gray-100"
                                {% if not package.is_patchable %}
                                    disabled
                                {% endif %}
                                />
                                {% include "software_inventory/components/package_details/select_all_records_checkbox_tooltip.html" %}
                            </th>
                        {% endif %}
                        <th class="{% if any_device_without_patching_capabilities %}tw-w-[22.5%]{% else %}tw-w-[30%]{% endif %} tw-px-4 tw-py-2 tw-font-bold">
                            <span class="tw-flex tw-items-center tw-gap-x-1">{% trans "Device" %}</span>
                        </th>
                        <th class="{% if any_device_without_patching_capabilities %}tw-w-[22.5%]{% else %}tw-w-[30%]{% endif %} tw-px-4 tw-py-2 tw-font-bold">
                            <span class="tw-flex tw-items-center tw-gap-x-1">{% trans "Last user" %}</span>
                        </th>
                        {% if any_device_without_patching_capabilities %}
                        <th class="tw-w-[22.5%] tw-px-4 tw-py-2 tw-font-bold">
                            <span class="tw-flex tw-items-center tw-gap-x-1">{% trans 'Active Protect version' %}</span>
                        </th>
                        {% endif %}
                        <th class="{% if any_device_without_patching_capabilities %}tw-w-[22.5%]{% else %}tw-w-[30%]{% endif %} tw-px-4 tw-py-2 tw-font-bold">
                            <span class="tw-flex tw-items-center tw-gap-x-1">{% trans "Patch status" %}</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="tw-text-left tw-bg-white">
                    {% for device in devices %}
                        {% with is_scheduled=device.is_scheduled %}
                            <tr class="tw-border-b last:tw-border-b-0 tw-h-12 hover:tw-bg-gray-50 device">
                                {% if view_level != "app_install" %}
                                    <td class="tw-px-4 tw-py-2 tw-group tw-relative">
                                        <input type="checkbox" name="devices_id" data-device-id="{{ device.id }}" class="device-checkbox tw-form-checkbox tw-h-4 tw-w-4 tw-text-blue-600 tw-border-gray-300 tw-rounded focus:tw-ring-blue-500 focus:tw-ring-offset-gray-100"
                                        {% if not package.is_patchable or is_scheduled %}
                                            disabled
                                        {% endif %}
                                        />
                                        {% include "software_inventory/components/package_details/package_checkbox_tooltip.html" %}
                                    </td>
                                {% endif %}
                                <td class="tw-w-2/5 tw-px-2 tw-py-2 tw-text-gray-800 tw-truncate tw-whitespace-nowrap tw-overflow-hidden">
                                    <a href="{{ device.url }}" class="tw-inline-flex tw-items-center tw-px-2 tw-py-1 tw-text-gray-800 tw-underline">
                                        {{ device.hostname|default:unknown }}
                                    </a>
                                </td>
                                <td class="tw-px-4 tw-py-2 tw-text-gray-800">
                                    {{ device.last_username|default:unknown }}
                                </td>
                                {% if any_device_without_patching_capabilities %}
                                <td class="tw-px-4 tw-py-2 {% if device.supports_opswat_patch %}tw-text-gray-600{% else %}tw-text-red-600{% endif %}">
                                    {% if device.supports_opswat_patch %}
                                        {% trans "Up to date" %}
                                    {% else %}
                                        {% trans "Out of date" %}
                                    {% endif %}
                                </td>
                                {% endif %}
                                <td class="tw-px-4 tw-py-2 tw-text-gray-800">
                                    {% if is_scheduled %}
                                        <span class="tw-text-blue-600">{% trans "In Progress" %}</span>
                                    {% else %}
                                        {% if package.is_patchable %}
                                            <span class="tw-text-green-700">{% trans "Available" %}</span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                        {% endwith %}
                    {% empty %}
                        <tr>
                            <td colspan="{% if view_level != "app_install" %}5{% else %}4{% endif %}" class="tw-px-4 tw-py-6 tw-text-gray-500 tw-text-center">
                                <div class="tw-mb-2">{% trans "No devices with this package detected" %}</div>
                                <div>{% trans "We were unable to find any devices where this package is currently installed." %}</div>
                            </td>
                        </tr>
                    {% endfor %}
                    <tr id="no-search-results" class="tw-hidden">
                        <td colspan="{% if view_level != "app_install" %}5{% else %}4{% endif %}" class="tw-px-4 tw-py-6 tw-text-gray-500 tw-text-center">
                            <div class="tw-mb-2">{% trans "No matching devices found" %}</div>
                            <div>{% trans "Try adjusting your search query to find what you're looking for." %}</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

{% block extra-js %}
    <script>
        var $btn = $('#patch-devices-button');
        var $label = $btn.find('.button-label');
        var $masterCheckbox = document.getElementById('all-devices-checkbox');
        var deviceCheckboxes = document.querySelectorAll('.device-checkbox:not(:disabled)');

        function updateButtonState() {
            const checkedCount = Array.from(deviceCheckboxes).filter(cb => cb.checked).length;
            $label.text(`Patch Now${checkedCount ? ` (${checkedCount})` : ''}`);
            $btn.prop('disabled', checkedCount === 0);

            $masterCheckbox.checked = checkedCount === deviceCheckboxes.length;
            $masterCheckbox.indeterminate = checkedCount > 0 && checkedCount < deviceCheckboxes.length;
        }

        $masterCheckbox.addEventListener('change', function () {
            deviceCheckboxes.forEach(cb => {
                cb.checked = this.checked;
            });
            updateButtonState();
        });

        deviceCheckboxes.forEach(cb => {
            cb.addEventListener('change', updateButtonState);
        });

        // disabled master checkbox if no devices are available or all are scheduled
        if (deviceCheckboxes.length === 0 || Array.from(deviceCheckboxes).every(cb => cb.disabled)) {
            $masterCheckbox.disabled = true;
        }

    </script>
{% endblock extra-js %}