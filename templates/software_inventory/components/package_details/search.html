{% load i18n %}

{% if view_level != "app_install" %}
    <div class="tw-px-6 tw-pb-4 tw-flex tw-items-center tw-justify-between tw-gap-2">
        <label for="package-devices-search" class="tw-sr-only">{% trans "Search" %}</label>
        <input type="text" name="search" id="package-devices-search" placeholder="{% trans 'Search by device or last user'%}"
           class="box-border tw-border tw-px-3 tw-pr-10 tw-py-1.5 tw-rounded-md tw-placeholder-gray-500 tw-h-8 tw-text-sm tw-w-[16.875rem]"
        >
        <button id="patch-devices-button" class="tw-group tw-gap-1 tw-bg-brand-600 tw-font-sans hover:tw-bg-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out tw-min-h-9 tw-flex tw-items-center tw-pl-4 tw-pr-3 tw-rounded-md tw-ml-2 tw-text-white hover:tw-text-white focus:tw-bg-brand-900 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0 disabled:tw-bg-gray-300 disabled:tw-text-gray-500 disabled:tw-cursor-not-allowed" disabled
        hx-post="{% url 'organisations:patch-summary' organisation.secure_id %}"
            hx-target=".patch-summary-modal-container"
            hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
            hx-swap="innerHTML"
            hx-trigger="click"
            hx-vals="js:...getPatchRequestData()"
        >
            <span class="button-label group-[.htmx-request]:tw-hidden">
                {% trans "Patch Now" %}<span></span>
            </span>
            <div class="cssload-speeding-wheel tw-border-l-transparent tw-border-r-transparent tw-border-t-white tw-border-b-white tw-hidden group-[.htmx-request]:tw-block tw-w-4 tw-h-4"></div>
            {% include "software_inventory/components/package_details/patch_button_tooltip.html" %}
        </button>
    </div>
{% endif %}

{% block extra-js %}
    <script type="text/javascript">
        // Debounce function to limit the rate of search input processing
        var debounceTimeout;
        document.getElementById("package-devices-search").addEventListener("input", function() {
            clearTimeout(debounceTimeout);

            debounceTimeout = setTimeout(function() {
                const filter = document.getElementById("package-devices-search").value.toLowerCase();
                const rows = document.querySelectorAll("#package-devices-table tbody tr.device");
                const noResults = document.getElementById("no-search-results");

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = text.includes(filter) ? "" : "none";
                });

                const visibleRows = Array.from(rows).some(row => row.style.display !== "none");
                // toggle tw-hidden class based on visible rows
                if (visibleRows) {
                    noResults.classList.add("tw-hidden");
                } else {
                    noResults.classList.remove("tw-hidden");
                }
            }, 300);
        });

        // Get and return the data for the patch request
        function getPatchRequestData() {
            const checkedCheckboxes = document.querySelectorAll('.device-checkbox:checked');
            const deviceIds = Array.from(checkedCheckboxes).map(cb => cb.dataset.deviceId);
            const packageId = "{{ package.id }}";
            return {
                package_id: packageId,
                devices_id: deviceIds
            };
        }
    </script>
{% endblock extra-js %}