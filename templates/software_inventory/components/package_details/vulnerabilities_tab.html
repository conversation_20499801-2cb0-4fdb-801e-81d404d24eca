{% load i18n %}
{% load static %}

{% trans "Unknown" as unknown %}
<div id="tab-content-vulnerabilities" class="tw-hidden">
    {% if not cves_count %}
        {% if package.is_regular_source %}
            <div class="tw-text-sm tw-text-gray-600 tw-p-5">
                {% blocktrans %}
                    This software has been detected on devices in your organisation, however it's not on our list of
                    checks for vulnerabilities. For more insights check online for updates or issues.
                {% endblocktrans %}
            </div>
        {% else %}
            <div class="tw-flex tw-justify-center tw-items-center tw-h-full">
                <div class="tw-w-1/3 tw-bg-white tw-py-6 tw-text-center">
                    <h2 class="tw-text-lg tw-font-bold tw-mb-2">{% trans "No known vulnerabilities" %}</h2>
                    <p class="tw-text-sm tw-text-gray-600">
                        {% if package.is_patchable %}
                            {% blocktrans %}
                                No security issues have been detected by our scanning tools yet.
                            {% endblocktrans %}
                        {% else %}
                            {% blocktrans %}
                                This software is up to date, and no security issues
                                have been detected by our scanning tools yet.
                            {% endblocktrans %}
                        {% endif %}
                    </p>
                </div>
            </div>
        {% endif %}
    {% else %}
        <div class="tw-border tw-rounded-lg tw-overflow-x-auto tw-mx-6">
                <table class="tw-text-left tw-text-sm tw-w-full tw-table-fixed">
                    <thead class="tw-border-b tw-bg-gray-100 tw-text-gray-600 tw-text-sm tw-h-10">
                    <tr>
                        <th scope="col" class="tw-p-2 tw-min-w-52">{% trans "Description" %}</th>
                        <th scope="col" class="tw-p-2 tw-w-[8.5rem]">{% trans "Issue ID" %}</th>
                        <th scope="col" class="tw-p-2 tw-w-[8.5rem]">{% trans "Published date" %}</th>
                        <th scope="col" class="tw-p-2 tw-w-32">{% trans "Severity" %}</th>
                        <th scope="col" class="tw-p-2 tw-w-12"></th>
                    </tr>
                    </thead>
                    <tbody class="tw-text-left tw-bg-white tw-text-gray-800">
                    {% for cve in cves %}
                        <tr class="tw-border-b tw-h-12 last:tw-border-b-0 tw-cursor-pointer hover:tw-bg-gray-50" onclick="toggleDetails(this)">
                            <td class="tw-p-2 tw-truncate tw-whitespace-nowrap tw-overflow-hidden tw-max-w-[12.5rem]">
                                {{ cve.description|default:unknown }}
                            </td>
                            <td class="tw-p-2">{{ cve.cve_id }}</td>
                            <td class="tw-p-2">{{ cve.published_at|date:"d M Y" }}</td>
                            <td class="tw-p-2">
                                {% include "software_inventory/components/common/severity_display.html" with base_score=cve.base_score source=package.source %}
                            </td>
                            <td>
                                <svg class="tw-w-4 tw-h-4">
                                    {% include "software_inventory/components/common/toggle_icon.html" %}
                                </svg>
                            </td>
                        </tr>
                        <tr class="tw-border-b last:tw-border-b-0">
                            <td colspan="5">
                                <div class="details-row tw-bg-gray-50 tw-px-6 tw-py-4">
                                    <div class="tw-text-lg tw-font-bold tw-text-gray-800">{{ cve.cve_id|default:unknown }}</div>
                                    <div class="tw-text-sm tw-text-gray-700 tw-flex tw-items-center tw-mt-2">
                                        <span class="tw-font-bold tw-w-32">{% trans "Severity" %}:</span>
                                        {% include "software_inventory/components/common/severity_display.html" with base_score=cve.base_score source=package.source %}
                                    </div>
                                    <div class="tw-text-sm tw-text-gray-700  tw-flex tw-items-center tw-mt-2">
                                        <span class="tw-font-bold tw-w-32">{% trans "Publish date" %}:</span>
                                        <span>{{ cve.published_at }}</span>
                                    </div>
                                    <p class="tw-text-sm tw-text-gray-600 tw-mt-5">
                                        {{ cve.description|default:unknown }}
                                    </p>
                                    <div class="tw-text-sm tw-text-gray-700 tw-mt-3">
                                        <span class="tw-font-bold">{% trans "References" %}:</span>
                                        {% for ref in cve.references %}
                                            <p class="tw-mb-1">
                                                <a href="{{ ref.url }}" target="_blank"
                                                   class="tw-text-black tw-underline tw-underline-offset-2 tw-decoration-black hover:tw-text-brand-600 hover:tw-underline hover:tw-fill-brand-600 tw-transition">
                                                    {{ ref.url }}
                                                </a>
                                            </p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
        </div>
    {% endif %}
</div>

{% block extra-css %}
    <style>
        .details-row {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out;
            padding: 0 1rem;
        }
        .details-row.active {
            /* max-height set by toggleDetails for smooth transition */
            padding: 1rem;
        }
    </style>
{% endblock extra-css %}