{% load i18n %}

{% trans "Unknown" as unknown %}
<div class="tw-fixed tw-inset-0 tw-bg-black/30 tw-flex tw-items-center tw-justify-center tw-z-[55] tw-font-sans">
    <div class="tw-bg-white tw-rounded-2xl tw-shadow-lg tw-w-full tw-max-w-2xl tw-p-6 tw-relative">

        <button class="tw-absolute tw-top-4 tw-right-4 tw-text-gray-500 hover:tw-text-black tw-text-lg"
        onclick="document.querySelector('.patch-summary-modal-container').innerHTML = '';"
        >
            &times;
        </button>

        <h2 class="tw-text-xl tw-font-semibold tw-mt-0 tw-mb-1 tw-font-sans">{% trans "Patch Software" %}</h2>
        <p class="tw-text-gray-600 tw-mb-4">{% trans "This will install the latest version(s) of all selected software" %}</p>

        <div class="tw-overflow-x-auto">
            {{ packages.count }} {% blocktrans count device_count=devices_count %}software version updates will be applied to {{ device_count }} device.{% plural %}software version updates will be applied to {{ device_count }} devices.{% endblocktrans %}
        </div>

        <div class="tw-mt-6 tw-flex tw-justify-end tw-space-x-2">
            <button class="tw-gap-1 tw-bg-white tw-font-sans hover:tw-bg-gray-100 tw-transition-colors tw-duration-300 tw-ease-in-out tw-min-h-9 tw-flex tw-items-center tw-pl-4 tw-pr-3 tw-rounded-md tw-ml-2 focus:tw-bg-brand-900 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0"
            onclick="document.querySelector('.patch-summary-modal-container').innerHTML = '';">
                {% trans "Cancel" %}
            </button>
            <button class="tw-group tw-gap-1 tw-bg-brand-600 tw-font-sans hover:tw-bg-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out tw-min-h-9 tw-flex tw-items-center tw-pl-4 tw-pr-3 tw-rounded-md tw-ml-2 tw-text-white hover:tw-text-white focus:tw-bg-brand-900 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0"
            hx-post="{% url 'organisations:patch-history' organisation.secure_id %}"
            hx-push-url="true"
            hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
            hx-target="#installed-software-content"
            hx-swap="outerHTML"
            hx-vals="js:{'packages': [{% for package in packages %}'{{ package.id }}',{% endfor %}], 'app_installs': [{% for device_id in devices_ids %}'{{ device_id }}',{% endfor %}] }"
            >
                <span class="button-label group-[.htmx-request]:tw-hidden">
                    {% trans "Start patch" %}
                </span>
                <div class="cssload-speeding-wheel tw-border-l-transparent tw-border-r-transparent tw-border-t-white tw-border-b-white tw-hidden group-[.htmx-request]:tw-block tw-w-5 tw-h-5"></div>
            </button>
        </div>
    </div>
</div>