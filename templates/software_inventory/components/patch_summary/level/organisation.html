{% load i18n %}

{% trans "Unknown" as unknown %}
<div class="tw-fixed tw-inset-0 tw-bg-black/30 tw-flex tw-items-center tw-justify-center tw-z-[55] tw-font-sans">
    <div class="tw-bg-white tw-rounded-2xl tw-shadow-lg tw-w-full tw-max-w-2xl tw-p-6 tw-relative">

        <button class="tw-absolute tw-top-4 tw-right-4 tw-text-gray-500 hover:tw-text-black tw-text-lg"
        onclick="document.querySelector('.patch-summary-modal-container').innerHTML = '';"
        >
            &times;
        </button>

        <h2 class="tw-text-xl tw-font-semibold tw-mt-0 tw-mb-1 tw-font-sans">{% trans "Patch" %} {{ package.product|default:unknown }}</h2>
        <p class="tw-text-gray-600 tw-mb-4">{% trans "This will install the latest known version" %} ({{ package.installer_version }}) {% trans "on selected devices" %}</p>

        <div class="tw-overflow-x-auto">
            <table class="tw-min-w-full tw-text-sm tw-text-left tw-border-collapse">
                <thead>
                <tr class="tw-border-b tw-border-gray-200">
                    <th class="tw-px-4 tw-py-2 tw-font-semibold">{% trans "Device" %}</th>
                    <th class="tw-px-4 tw-py-2 tw-font-semibold">{% trans "Last user" %}</th>
                </tr>
                </thead>
                <tbody>
                {% for device in devices %}
                    <tr class="tw-border-b tw-border-gray-100">
                        <td class="tw-px-4 tw-py-2">{{ device.hostname|default:unknown }}</td>
                        <td class="tw-px-4 tw-py-2">{{ device.last_login_username }}</td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="tw-mt-6 tw-flex tw-justify-end tw-space-x-2">
            <button class="tw-gap-1 tw-bg-white tw-font-sans hover:tw-bg-gray-100 tw-transition-colors tw-duration-300 tw-ease-in-out tw-min-h-9 tw-flex tw-items-center tw-pl-4 tw-pr-3 tw-rounded-md tw-ml-2 focus:tw-bg-brand-900 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0"
            onclick="document.querySelector('.patch-summary-modal-container').innerHTML = '';">
                {% trans "Cancel" %}
            </button>
            <button class="tw-group tw-gap-1 tw-bg-brand-600 tw-font-sans hover:tw-bg-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out tw-min-h-9 tw-flex tw-items-center tw-pl-4 tw-pr-3 tw-rounded-md tw-ml-2 tw-text-white hover:tw-text-white focus:tw-bg-brand-900 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0"
            {% if view_level == "app_install" %}
                hx-post="{{ app_install.url_patch_history }}"
            {% else %}
                hx-post="{% url 'organisations:patch-history' organisation.secure_id %}"
                hx-push-url="true"
            {% endif %}
            hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
            hx-target="#installed-software-content"
            hx-swap="outerHTML"
            hx-vals="js:{'packages': ['{{ package.id }}'], 'app_installs': [{% for device in devices %}{{ device.id }},{% endfor %}] }"
            >
                <span class="button-label group-[.htmx-request]:tw-hidden">
                    {% trans "Start patch" %}
                </span>
                <div class="cssload-speeding-wheel tw-border-l-transparent tw-border-r-transparent tw-border-t-white tw-border-b-white tw-hidden group-[.htmx-request]:tw-block tw-w-5 tw-h-5"></div>
            </button>
        </div>
    </div>
</div>