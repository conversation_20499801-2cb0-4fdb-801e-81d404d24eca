{% extends "account/base.html" %}

{% load i18n %}
{% load static account socialaccount social_provider_tag %}
{% load waffle_tags %}


{% block head_description %}{% trans 'CyberSmart Dashboard Log In' %}{% endblock %}

{% block head_title %}{% trans "Log In" %}{% endblock %}

{% block extra-css %}
    <link href="{% static 'css/login.css' %}" rel="stylesheet">
    <link href="{% static 'css/btn-override.css' %}" rel="stylesheet">
    <link href="{% static 'css/spinners.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
    <section id="wrapper" class="tw-flex login-register tw-font-sans tw-p-0 tw-bg-slate-50">
        <div class="tw-relative tw-flex tw-justify-center tw-items-center tw-w-full tw-min-h-svh">
            <div class="tw-relative tw-flex tw-justify-center tw-items-center tw-flex-col tw-gap-4 tw-min-h-svh tw-w-full tw-p-2 lg:tw-p-8">
                <div class="tw-flex tw-flex-col tw-gap-8 tw-items-center tw-w-full">
                    <img src="{% static 'images/0076-CyberSmartLogo-Secondary-Dark.png' %}" width="180" />

                    <div class="tw-flex tw-flex-col tw-items-center tw-gap-5 tw-p-6 tw-bg-white tw-rounded-xl tw-border tw-w-full md:tw-w-[380px]">    
                        <form class="tw-flex tw-flex-col tw-gap-2 tw-w-full" method="POST" id="loginform" action="{% url 'account_login' %}">
                            <input type="hidden" name="next" value="{{ next }}" />
                            <h1 class="tw-font-sans tw-text-lg tw-text-center tw-font-medium tw-mt-0 tw-mb-3">Log in</h1>
                            <!-- SSO options -->
                            <div class="tw-flex tw-flex-col tw-gap-2">
                                <div class="tw-flex tw-flex-col tw-gap-2">
                                    {% csrf_token %}
                                    {% get_providers as socialaccount_providers %}
                                    {% if socialaccount_providers|has_provider:"google" %}
                                        <a href="{% provider_login_url "google" %}" class="tw-flex tw-items-center tw-justify-center tw-gap-2 tw-min-h-9 tw-px-4 tw-py-2 tw-rounded-md tw-border tw-border-gray-300 tw-bg-white tw-text-black tw-font-sans hover:tw-bg-gray-100 hover:tw-text-black focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 tw-transition-colors tw-duration-300 tw-ease-in-out">
                                            <img src="{% static 'images/logo-google.svg' %}" class="tw-w-[16px]">
                                            Continue with Google
                                        </a>
                                    {% endif %}
                                    {% if socialaccount_providers|has_provider:"microsoft" %}
                                        <a href="{% provider_login_url "microsoft" %}" class="tw-flex tw-items-center tw-justify-center tw-gap-2 tw-min-h-9 tw-px-4 tw-py-2 tw-rounded-md tw-border tw-border-gray-300 tw-bg-white tw-text-black tw-font-sans hover:tw-bg-gray-100 hover:tw-text-black focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 tw-transition-colors tw-duration-300 tw-ease-in-out">
                                            <img src="{% static 'images/logo-microsoft.svg' %}" class="tw-w-[16px]">
                                            Continue with Microsoft
                                        </a>
                                    {% endif %}
                                </div>
                                {% if socialaccount_providers|has_provider:"starling" %}
                                <div id="partner-dropdown" class="tw-relative xl:tw-pt-0 tw-inline-block tw-text-left">
                                    <!-- Button -->
                                    <button id="continueWithPartnersButton" type="button" class="tw-flex tw-w-full tw-items-center tw-justify-center tw-min-h-9 tw-px-4 tw-py-2 tw-rounded-md tw-border tw-border-gray-300 tw-bg-white tw-text-black tw-font-sans hover:tw-bg-gray-100 hover:tw-text-black focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-gray-300 focus:tw-text-black active:tw-bg-gray-200 active:tw-ring-0 tw-transition-colors tw-duration-300 tw-ease-in-out" data-toggle="dropdown" aria-expanded="false">            
                                        Continue with partners
                                        <span class="tw-ml-1">
                                            <svg class="tw-w-[18px] tw-h-[18px] tw-fill-black">
                                                <use href="{% static 'icons/icons-sprite.svg' %}#icon-chevron-down"></use>
                                            </svg>
                                        </span>
                                    </button>
                                    <!-- Dropdown Menu -->
                                    <ul id="continueWithPartnersMenu" class="tw-hidden tw-absolute tw-mt-2 tw-w-full tw-z-10 tw-right-0 tw-p-2 tw-bg-white tw-rounded-md tw-shadow-lg tw-ring-1 tw-ring-black tw-ring-opacity-5 tw-font-sans tw-list-none">
                                        <li>
                                            <a id="office_login" href="{% url 'starling_login' %}" class="tw-flex tw-px-2 tw-py-2 tw-rounded-md tw-justify-between tw-w-full tw-text-black hover:tw-text-black hover:tw-bg-zinc-100">
                                                <div class="tw-flex tw-gap-2 tw-items-center">
                                                    <img class="m-r-10" src="{% static 'images/starling.png' %}" style="height: 25px;">
                                                    {% trans 'Starling' %}
                                                </div>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                {% endif %}
                            </div>
    
                            <div class="tw-flex tw-w-full tw-justify-center tw-relative before:tw-content-[' '] before:tw-absolute before:tw-left-0 before:tw-top-2.5 before:tw-h-[1px] before:tw-w-full before:tw-bg-gray-200 tw-mt-2 tw-text-gray-500">
                                <span class="tw-z-0 tw-px-2 tw-bg-white">or</span>
                            </div>
                            
                            <div class="tw-flex tw-flex-col tw-gap-4">
                                {% for err in form.non_field_errors  %}
                                <div class="tw-px-3 tw-py-2 tw-rounded-lg tw-border tw-border-red-300 tw-bg-red-50">
                                    <p class="tw-w-full tw-text-red-600">{{err}}</p>
                                </div>
                                {% endfor %}
                                <div class=" {% if form.login.errors %}has-error{% endif %}">
                                    <div>
                                        <label for="email_field" class="tw-block tw-font-medium">Email</label>
                                        <input 
                                            id="email_field"
                                            name="login"
                                            type="email"
                                            required
                                            class="tw-w-full tw-font-sans tw-border tw-transition tw-duration-300 tw-ease-in-out tw-h-10 tw-border-gray-300 tw-p-2 tw-rounded-md hover:tw-border-gray-400 focus:tw-border-gray-400 focus:tw-shadow-[0_0px_0px_3px_rgba(0,0,0,0.07)]"
                                            autocomplete="email"
                                            {% if form.login.value %} value="{{ form.login.value }}" {% endif %}
                                            aria-describedby="email-error"
                                            aria-invalid="{% if form.login.errors %}true{% else %}false{% endif %}"
                                        >
                                        
                                        {% if form.login.errors %}
                                            <div id="email-error" class="tw-mt-1 tw-text-sm tw-text-red-600" role="alert">
                                                {% for err in form.login.errors %}
                                                    <p>{{ err }}</p>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="{%if form.password.errors%}has-error{%endif%}">
                                    <div>
                                        <label for="password_field" class="tw-block tw-font-medium">Password</label>
                                        <input 
                                            id="password_field"
                                            name="password"
                                            type="password"
                                            required
                                            class="tw-w-full tw-font-sans tw-border tw-transition tw-duration-300 tw-ease-in-out tw-h-10 tw-border-gray-300 tw-p-2 tw-rounded-md tw-tracking-[0.2em] hover:tw-border-gray-400 focus:tw-border-gray-400 focus:tw-shadow-[0_0px_0px_3px_rgba(0,0,0,0.07)]"
                                            autocomplete="current-password"
                                            aria-describedby="password-error"
                                            aria-invalid="{% if form.password.errors %}true{% else %}false{% endif %}"
                                        >
                                        
                                        {% if form.password.errors %}
                                            <div id="password-error" class="tw-mt-1 tw-text-sm tw-text-red-600" role="alert">
                                                {% for err in form.password.errors %}
                                                    <p>{{ err }}</p>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
    
                                {% if redirect_field_value %}
                                    <input id="redirect-field" type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
                                {% endif %}
                                
                                <div class="tw-flex tw-gap-2 tw-items-center tw-content-center">
                                    <input id="inputCheckbox" class="!tw-m-0 tw-w-4 tw-h-4 tw-border focus:tw-border-gray-400 focus:tw-shadow-[0_0px_0px_3px_rgba(0,0,0,0.07)]" type="checkbox" name="remember">
                                    <label for="inputCheckbox" class="tw-m-0"> {% trans 'Remember me' %} </label>
                                </div>
                                
                                <button class="tw-w-full tw-bg-brand-600 hover:tw-bg-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out tw-h-10 tw-flex tw-items-center tw-justify-center tw-px-4 tw-py-3 tw-rounded-md tw-text-white tw-text-center tw-font-sans hover:tw-text-white focus:tw-ring-4 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0" type="submit" id="login_button">{% trans "Log in" %}</button>
                            </div>
    
                            <div class="tw-flex tw-flex-col tw-items-center tw-gap-1 tw-mt-4">
                                <a href="javascript:void(0)" id="to-recover" class="tw-text-brand-600 hover:tw-to-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out">{% trans 'Forgot password?' %}</a>
                                {% if not ps.disable_direct_customer_signup %}
                                <p class="tw-text-gray-600">{% trans "Don't have an account? " %}<a id="signup" href="{% switch 'cyber-rewards-welcome' %}{% url "account_signup" %}{% else %}{% url "signup:plans" %}{% endswitch %}" class="tw-text-brand-600 hover:tw-to-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out">{% trans "Sign up" %}</a></p>
                                {% endif %}
                            </div>
                        </form>
                        
                        <form class="tw-flex tw-flex-col" method="POST" id="recoverform" action="{% url 'account_reset_password' %}">
                            {% csrf_token %}
                            <div class="tw-mb-6">
                                <h3 class="tw-p-0 tw-mt-0 tw-font-sans tw-text-xl tw-font-semibold tw-text-gray-900">{% trans "Reset password" %}</h3>
    
                                <p class="tw-text-gray-900">
                                    Enter your email address and we'll send you instructions to reset your password.
                                </p>
                            </div>
    
                            <div class="tw-mb-3">
                                <label>{% trans "Email" %}</label>
    
                                <input class="tw-w-full tw-font-sans tw-border tw-transition-colors tw-duration-300 tw-ease-in-out tw-h-10 tw-border-gray-300 tw-p-2 tw-rounded-lg hover:tw-border-gray-400 focus:tw-border-gray-400 focus:tw-shadow-[0_0px_0px_3px_rgba(0,0,0,0.07)]" name="email" type="email" required="">
                            </div>
                            
                            <button id="reset-button" class="tw-w-full tw-bg-brand-600 hover:tw-bg-brand-700 tw-transition-colors tw-duration-300 tw-ease-in-out tw-h-10 tw-flex tw-items-center tw-justify-center tw-px-4 tw-py-3 tw-rounded-lg tw-text-white tw-text-center tw-font-sans hover:tw-text-white focus:tw-ring-4 focus:tw-ring-gray-300 focus:tw-outline-none focus:tw-text-white active:tw-bg-brand-900 active:tw-ring-0" type="submit">{% trans "Send reset instructions" %}</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="tw-relative tw-hidden xl:tw-flex tw-items-center tw-justify-center tw-flex-col tw-gap-20 tw-rounded-xl tw-m-2 tw-min-h-[calc(100%-16px)] tw-w-full tw-py-12 tw-bg-slate-800 tw-overflow-hidden">
                <div class="tw-relative tw-flex tw-flex-col tw-items-center tw-gap-2 tw-text-center tw-max-w-[400px]">
                    <img src="{% static 'images/quote.svg' %}" class="tw-absolute tw-w-[40px] tw-left-0 -tw-top-3 tw-opacity-20"/>
                    <p class="tw-text-xl tw-text-slate-200">"I could see how good our cybersecurity was across the whole company."</p>
                    <p class="tw-text-sm tw-text-slate-400 tw-antialiased">Neil S, Chief Technology Officer, UK</p>
                </div>

                <div class="tw-absolute tw-bottom-12 tw-w-full tw-flex tw-flex-col tw-items-center tw-justify-center tw-gap-4 tw-px-4">
                    <p class="tw-text-slate-400 tw-text-center tw-text-sm tw-antialiased tw-max-w-72">5,000+ businesses around the world trust CyberSmart to deliver, including:</p>

                    <div class="tw-flex tw-items-center tw-gap-10">
                        <img src="{% static 'images/logo-sharp.svg' %}" class="tw-w-[100px] tw-opacity-80" />
                        <img src="{% static 'images/logo-hitachi.svg' %}" class="tw-w-[100px] tw-opacity-80" />
                        <img src="{% static 'images/logo-wiser.svg' %}" class="tw-w-[72px] tw-opacity-80" />
                        <img src="{% static 'images/logo-e92.svg' %}" class="tw-w-[104px] tw-opacity-80" />
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const dropdownButton = document.getElementById('continueWithPartnersButton');
            const dropdownMenu = document.getElementById('continueWithPartnersMenu');

            function toggleDropdown(event) {
                dropdownMenu.classList.toggle('tw-hidden');

                if (!dropdownMenu.classList.contains('tw-hidden')) {
                    document.addEventListener('click', closeDropdown);
                } else {
                    document.removeEventListener('click', closeDropdown);
                }
            }

            function closeDropdown(event) {
                if (!dropdownButton.contains(event.target) && !dropdownMenu.contains(event.target)) {
                    dropdownMenu.classList.add('tw-hidden');
                    document.removeEventListener('click', closeDropdown); // Clean up event listener
                }
            }

            dropdownButton.addEventListener('click', toggleDropdown);

            // Prevent double-clicking login button
            const loginForm = document.getElementById('loginform');
            const loginButton = document.getElementById('login_button');

            if (loginForm && loginButton) {
                // Store original button content
                const originalButtonContent = loginButton.innerHTML;

                loginForm.addEventListener('submit', function(e) {
                    // Disable the button to prevent double-clicking
                    // Sometimes, they use auto-fill with Password manager extensions
                    // which triggers auto-login.
                    // Then at the same time (while it's logging in in the background),
                    // they click the login button. This prevents that.
                    loginButton.disabled = true;

                    // Replace button content with white spinner
                    loginButton.innerHTML = '<div class="cssload-speeding-wheel tw-w-4 tw-h-4 tw-mx-auto !tw-border-2 !tw-border-white !tw-border-l-transparent !tw-border-r-transparent"></div>';
                    loginButton.classList.add('tw-opacity-75', 'tw-cursor-not-allowed');

                    // Set a timeout to reset the button if something goes wrong
                    setTimeout(function() {
                        if (loginButton.disabled) {
                            resetLoginButton();
                        }
                    }, 7000); // Reset after 7 seconds if still disabled
                });

                function resetLoginButton() {
                    if (loginButton) {
                        loginButton.disabled = false;
                        loginButton.innerHTML = originalButtonContent;
                        loginButton.classList.remove('tw-opacity-75', 'tw-cursor-not-allowed');
                    }
                }
            }
        });
    </script>
{% endblock %}