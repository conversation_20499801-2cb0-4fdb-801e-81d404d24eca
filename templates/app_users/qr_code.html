{% load static %}
{% load i18n %}
{% load statici18n %}
<!DOCTYPE html>
{% get_current_language as LANGUAGE_CODE %}
<html lang="{{ LANGUAGE_CODE }}">
	<head>
        <script src="{% statici18n LANGUAGE_CODE %}"></script>
        <script src="{% static 'js/jquery.2.2.4.min.js' %}"></script>
        <!-- Bootstrap Core CSS -->
        <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet" >
        <!-- served via CDN for font support -->
        <link href="{% static 'css/bootstrap-select.min.css' %}" rel="stylesheet" />
        <!-- Fonts -->
        <link href="https://fonts.googleapis.com/css?family=Lato:300,400,500,600,700" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/gh/lykmapipo/themify-icons@0.1.2/css/themify-icons.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/gh/linea-io/Linea-Iconset@1.0/_arrows/_ICONFONT/styles.min.css"
            rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/gh/linea-io/Linea-Iconset@1.0/_basic_elaboration/_ICONFONT/styles.min.css"
            rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/gh/linea-io/Linea-Iconset@1.0/_basic/_ICONFONT/styles.min.css"
            rel="stylesheet">
        <link type="text/css" rel="stylesheet" href="{% static 'css/qr_code.css' %}">
	</head>
	<body class="qr-page">
		<div class="row">
			<img class="cs-icon" src="{% static 'images/0076-CyberSmartLogo-Secondary-Dark.png' %}">
            <hr class="first-hr"/>
            {% if expired %}
                <h1 class="qr-title"> {% trans 'Sorry, this link has now expired :(' %} </h1>
            {% elif invalid_uuid %}
                <h1 class="qr-title"> {% trans 'Your link is incorrect. Please contact support' %} </h1>
            {% else %}
                <h1 class="qr-title"> {% trans 'Log into CyberSmart Active Protect on your mobile device' %} </h1>
            {% endif %}
		</div>
        {% if expired %}
            <div class="row qr-code-row">
                <div class="col-md-8">
                    <p class="expired-link">{% trans 'Please go back to CyberSmart Active Protect on your mobile device and request a new link' %} </p>
                </div>
            </div>
        {% elif not invalid_uuid %}
            <div class="row qr-code-row">
                <div class="col-md-5">
                    <img class="qr-code" src="{{ qr_code_url }}" alt="qr_code">
                </div>
                <div class="col-md-4 qr-code-details">
                    <h3 class="details-header">{% trans 'Use your mobile device to scan the QR code' %}</h3>
                    <p>{% trans 'Once you have logged in successfully, you may close this page' %} </p>
                </div>
            </div>
        {% endif %}
        <div class="row qr-code-details knowledge-base">
            <hr class="second-hr"/>
            <div class="col-md-6">
                <p>{% trans 'For any support or to learn more about CyberSmart Active Protect, please visit our Knowledge Base' %} </p>
                <a href="{{ knowledge_base_url }}" class="btn btn-primary">{% trans 'Visit Knowledge Base' %}</a>
            </div>
        </div>
        {% if not expired and not invalid_uuid %}
            <div class="row qr-code-details mobile-images-section">
                <div class="col-md-5">
                    <img class="mobile-image" src="{% static 'images/mobile_security_checks.png' %}" alt="mobile_security_checks">
                    <h4>{% trans 'Security Checks' %} </h4>
                    <p>{% trans 'CyberSmart Active Protect checks your laptop and phone for key security requirements, reports on any problems, and gives you the tools to fix them - all delivered in simple, jargon-free language' %} </p>
                </div>
                <div class="col-md-5">
                    <img class="mobile-image" src="{% static 'images/mobile_policies.png' %}" alt="mobile_policies">
                    <h4>{% trans 'Policies' %} </h4>
                    <p>{% trans 'Do your staff know what to do in a crisis? Use our policy manager to generate, distribute, and check who’s reading your data and security policies' %} </p>
                </div>
            </div>
        {% endif %}
	</body>
</html>
