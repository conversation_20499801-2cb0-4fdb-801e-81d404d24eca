#!/bin/bash
set -euo pipefail

if [ "$#" -ne 1 ]; then
  echo "Usage: $0 <language_codes>" >&2
  echo "  language_codes: Single language code or comma-separated list (e.g., 'fr' or 'fr,de,it')" >&2
  echo "  Example languages: sv,de,fr,it,nl,pl" >&2
  exit 1
fi

LANGUAGES_INPUT="$1"

# Convert comma-separated list to array
IFS=',' read -ra LANGUAGES <<< "$LANGUAGES_INPUT"


for LANGUAGE in "${LANGUAGES[@]}"; do
  echo "=========================================="
  echo "Processing language: $LANGUAGE"
  echo "=========================================="

  echo "Translating messages to $LANGUAGE"
  python3 manage.py translate_messages --untranslated -l $<PERSON>NGUAGE

  echo "Fix messages with python variables"
  python3 /app/compose/translation/fix_po_variables.py "./**/$LANGUAGE" "../locale/$LANGUAGE" --recursive

  echo "Compiling messages"
  python3 manage.py compilemessages --use-fuzzy -l $LANGUAGE
  echo "*****************************************************************************************"
  python3 manage.py compilejsi18n -l $LANGUAGE
  echo "*****************************************************************************************"
  echo "Completed processing for $LANGUAGE"
done


echo ""
echo "=========================================="
echo "All translations have been generated for $LANGUAGES_INPUT"
echo "=========================================="
