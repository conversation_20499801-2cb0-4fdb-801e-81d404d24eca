import glob

import polib
import re
import os
import argparse
from pathlib import Path


# Pattern %(variable_name)format_type ex. %(variable)s, %(variable)d
PATTERN = r'%\((\w+)\)([sd])'

def find_format_variable_positions(text):
    if not text:
        return []

    matches = []
    for match in re.finditer(PATTERN, text):
        matches.append({
            'start': match.start(),
            'end': match.end(),
            'variable_name': match.group(1),
            'format_type': match.group(2),
            'full_match': match.group(0)
        })
    return matches


def fix_translated_variables(msgid, msgstr):
    """Fix translated format variables in msgstr using original variables from msgid."""
    if not msgid or not msgstr:
        return msgstr

    original_vars = find_format_variable_positions(msgid)
    if not original_vars:
        return msgstr

    translated_vars = find_format_variable_positions(msgstr)

    if len(original_vars) != len(translated_vars):
        return msgstr

    fixed_msgstr = msgstr
    offset = 0

    for i, (orig_var, trans_var) in enumerate(zip(original_vars, translated_vars)):
        if orig_var['variable_name'] != trans_var['variable_name']:
            original_format = f"%({orig_var['variable_name']}){orig_var['format_type']}"
            start_pos = trans_var['start'] + offset
            end_pos = trans_var['end'] + offset
            fixed_msgstr = (fixed_msgstr[:start_pos] +
                            original_format +
                            fixed_msgstr[end_pos:])
            offset += len(original_format) - len(trans_var['full_match'])
    return fixed_msgstr


def process_po_file(file_path, dry_run=False):
    print(f"\nProcessing: {file_path}")

    try:
        po = polib.pofile(file_path)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return False

    changes_made = 0

    for entry in po:
        if entry.msgid and entry.msgstr:
            if isinstance(entry.msgstr, str):
                original_msgstr = entry.msgstr
                fixed_msgstr = fix_translated_variables(entry.msgid, entry.msgstr)

                if fixed_msgstr != original_msgstr:
                    if not dry_run:
                        entry.msgstr = fixed_msgstr
                    changes_made += 1

            elif isinstance(entry.msgstr_plural, dict):
                for plural_form, msgstr in entry.msgstr_plural.items():
                    source_msgid = entry.msgid_plural if plural_form != '0' and entry.msgid_plural else entry.msgid

                    original_msgstr = msgstr
                    fixed_msgstr = fix_translated_variables(source_msgid, msgstr)

                    if fixed_msgstr != original_msgstr:
                        if not dry_run:
                            entry.msgstr_plural[plural_form] = fixed_msgstr
                        changes_made += 1

    if changes_made > 0:
        print(f"  {changes_made} changes to be made")
        if not dry_run:
            try:
                po.save(file_path)
            except Exception as e:
                print(f"  Error saving {file_path}: {e}")
                return False
    else:
        print("  No changes needed")
    return True


def find_po_files(directory):
    """Find all .po files in the given directory and subdirectories."""
    po_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.po'):
                po_files.append(os.path.join(root, file))
    return po_files


def collect_po_files_from_paths(paths, recursive=False):
    """Collect .po files from a list of paths (files or directories)."""
    po_files = []
    for path_str in paths:
        path = Path(path_str)
        if path.is_file() and path.suffix == '.po':
            po_files.append(str(path))
        elif path.is_dir():
            if recursive:
                po_files.extend(find_po_files(str(path)))
            else:
                po_files.extend([str(f) for f in path.glob('*.po')])
    return po_files


def main():
    parser = argparse.ArgumentParser(description='Fix translated Python format variables in .po files')
    parser.add_argument('paths', nargs='+', help='Path(s) to .po file(s) or directory(ies). Supports glob patterns like ./*/locale/de/')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be changed without making changes')
    parser.add_argument('--recursive', '-r', action='store_true', help='Process all .po files in subdirectories')

    args = parser.parse_args()

    # Expand glob patterns for all provided paths
    all_paths = []
    for path_pattern in args.paths:
        if '*' in path_pattern or '?' in path_pattern or '[' in path_pattern:
            expanded_paths = glob.glob(path_pattern, recursive=args.recursive)
            if expanded_paths:
                all_paths.extend(expanded_paths)
        else:
            if Path(path_pattern).exists():
                all_paths.append(path_pattern)

    if not all_paths:
        print("Error: No valid paths found")
        return 1

    # Collect all .po files from the expanded paths
    po_files = collect_po_files_from_paths(all_paths, args.recursive)

    if not po_files:
        print("No .po files found in the specified paths")
        return 1

    print(f"Found {len(po_files)} .po file(s)")

    if args.dry_run:
        print("DRY RUN MODE - No changes will be made")

    success_count = 0
    for po_file in po_files:
        if process_po_file(po_file, args.dry_run):
            success_count += 1

    print(f"\nProcessed {success_count}/{len(po_files)} files successfully")

    return 0


if __name__ == '__main__':
    exit(main())
