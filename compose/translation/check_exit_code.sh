#!/bin/bash
set -euo pipefail

BRANCH=$1
GITHUB_TOKEN=$2

exit_code=0
containers=()
# Step 1: Get names of exited containers matching the pattern
while IFS= read -r name; do
  containers+=("$name")
done < <(docker ps -a --filter "status=exited" --filter "name=translation-worker-*" --format '{{.Names}}')

echo "Found ${#containers[@]} stopped translation workers."

# Step 2: Loop through containers and check exit codes
for container in "${containers[@]}"; do
  echo "Checking container: $container"

  code=$(docker inspect --format='{{.State.ExitCode}}' "$container")

  echo "Exit code for $container: $code"

  if [ "$code" -ne 0 ]; then
    echo "$container exited with non-zero code: $code"
    exit "$code"
  fi
done

echo "All translation-worker containers exited successfully."

echo "===================Pushing changes to Github.====================="
./compose/translation/push_changes.sh $BRANCH $GITHUB_TOKEN
echo "===================Pushing changes to Github Complete.====================="
