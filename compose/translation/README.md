# Running the Translation Solution Locally with <PERSON><PERSON> Compose

This guide explains how to set up and run the translation workflow locally using Docker Compose.

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/) installed on your machine
- [Docker Compose](https://docs.docker.com/compose/install/) (if not included with Docker Desktop)
- (Optional) Git, if you need to clone the repository

## Please run the steps in sequence to successfully generate missing translation

## Steps

### 1. Clone the Repository (if you haven’t already)

```sh
git clone <your-repo-url>
cd cybersmart-platform-core
```

### 2. Build the Docker Images

```sh
docker compose -f docker-compose-translation.yml build build_translation --no-cache
```
If it fails because of system architecture simply change the docker base image to something like `python:3.11-slim`

### 3. Run Missing translation to identify the translation

```sh
docker compose -f docker-compose-translation.yml up run_missing_translation --remove-orphans
```
### 4. Running Django makemessage command

```sh
docker compose -f docker-compose-translation.yml up run_makemessages --remove-orphans
```

### 5. Running Translation
#### note: Update the value of  DEEPL_TRANSLATE_KEY in the docker-compose-translation.yml file, which can be found in 1Password.

```sh 
docker compose -f docker-compose-translation.yml --profile run_translations up --remove-orphans
```

### 5. Stopping the Services

To stop the running containers, press `Ctrl+C` in the terminal where `docker-compose up` is running, or run:

```sh
docker-compose -f docker-compose-translation.yml down
```

### 6. (Optional) Cleaning Up

To remove all containers, networks, and volumes created by Docker Compose:

```sh
docker-compose -f docker-compose-translation.yml down -v
```

## Notes

- The `volumes` section in the compose file mounts your local files into the container, so changes you make on your host are reflected inside the container.
- If you need to pass environment variables, check or edit the `cs-web-env.env` file or set them in the docker compose file.

**If you have any issues or questions, please contact the DevOps team or check the project documentation.**