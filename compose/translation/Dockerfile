FROM arm64v8/python:3.11-slim

ENV PYTHONUNBUFFERED 1
ENV DJANGO_SETTINGS_MODULE=cyber_smart.settings.cs_settings

RUN apt-get update && \
    apt-get install gettext git libgdal-dev -y && \
    pip3 install poetry && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

COPY ./pyproject.toml ./poetry.lock .
ENV POETRY_VIRTUALENVS_CREATE false
RUN poetry install --no-interaction --no-ansi --with dev

COPY . /app
RUN rm -rf /app/results

COPY ./compose/translation/start.sh start.sh
RUN sed -i 's/\r//' /start.sh
RUN chmod +x /start.sh

COPY ./compose/translation/generate_translations.sh generate_translations.sh
RUN sed -i 's/\r//' /generate_translations.sh
RUN chmod +x /generate_translations.sh

COPY ./compose/translation/run_makemessage.sh run_makemessage.sh
RUN sed -i 's/\r//' /run_makemessage.sh
RUN chmod +x /run_makemessage.sh

COPY ./compose/translation/remove_symlinks.sh remove_symlinks.sh
RUN sed -i 's/\r//' /remove_symlinks.sh
RUN chmod +x /remove_symlinks.sh

WORKDIR /app/src