#!/bin/bash

set -euo pipefail

if [ "$#" -ne 1 ]; then
  echo "Usage: $0 <language_codes>" >&2
  echo "language_codes: Single language code or comma-separated list (e.g., 'fr' or 'fr,de,it')" >&2
  echo "Example languages: sv,de,fr,it,nl,pl" >&2
  exit 1
fi

LANGUAGES_INPUT="$1"  # e.g., "fr,it,pl"
ARGS=""
IFS=',' read -ra LANG_ARRAY <<< "$LANGUAGES_INPUT"
for lang in "${LANG_ARRAY[@]}"; do
  ARGS="$ARGS -l $lang"
done
echo "$ARGS"


echo "Processing languages: $LANGUAGES_INPUT"
echo "=========================================="

echo "Making translations for files in /src for $LANGUAGES_INPUT"
python3 manage.py makemessages --no-obsolete --no-location $ARGS

echo "Deleting /static/jsi18n folder, since it clashes with the next steps"
rm -rf /app/src/cyber_smart/apps/static/jsi18n

echo "Making translations for JS and HTML files in /templates and /static for $LANGUAGES_INPUT"
python3 manage.py makemessages --symlinks --no-obsolete --no-location $ARGS
python3 manage.py makemessages --symlinks -d djangojs --no-obsolete --no-location --extension=js $ARGS
# ignore these errors, to be addressed in CUSTEAM-2583
python3 manage.py makemessages --symlinks -d djangojs --no-obsolete --no-location --extension=html $ARGS || true
echo "makemessages completed"