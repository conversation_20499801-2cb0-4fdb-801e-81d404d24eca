#!/bin/bash

set -euo pipefail
BRANCH=$1
GITHUB_PAT_TOKEN=$2
if [ -z "$BRANCH" ] || [ -z "$GITHUB_PAT_TOKEN" ]; then
    echo "Usage: $0 <branch_name> <github_pat_token>"
    exit 1
fi
pwd;
git config user.name "<PERSON><PERSON> Bot"
git config user.email "<EMAIL>"
git remote set-url origin https://$<EMAIL>/cyber-smart/cybersmart-platform-core.git
git config pull.rebase true

echo "Current branch is $BRANCH";
git checkout $BRANCH;
git add .;
git commit -m "Update translations for languages fr,de,it,nl,pl and more"
git pull origin $BRANCH;
# First attempt to push
if git push origin $BRANCH; then
    echo "Push succeeded."
else
    echo "Push failed. Attempting to pull and rebase..."
    git pull origin $BRANCH;
    if git push origin $BRANCH; then
        echo "Push succeeded after rebase."
    else
        echo "Push still failed after rebase. Manual intervention needed."
        exit 1
    fi
fi