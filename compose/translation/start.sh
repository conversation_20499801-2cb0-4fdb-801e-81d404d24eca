#!/bin/bash
set -euo pipefail
echo "--------- Checking for missing translations ----------"
echo "Running makemessages to update translation files..."

echo "Checking if symlinks exist before creating them"
rm -f /app/src/cyber_smart/apps/templates
rm -f /app/src/cyber_smart/apps/static
ln -s /app/templates /app/src/cyber_smart/apps/templates
ln -s /app/static /app/src/cyber_smart/apps/static

python3 manage.py makemessages --no-obsolete --no-location  -l sv
echo "Deleting /static/jsi18n folder, since it clashes with the next steps"
rm -rf /app/src/cyber_smart/apps/static/jsi18n
python3 manage.py makemessages --symlinks --no-obsolete --no-location  -l sv
python3 manage.py makemessages --symlinks -d djangojs --no-obsolete --no-location  --extension=js -l sv
python3 manage.py makemessages --symlinks -d djangojs --no-obsolete --no-location --extension=html -l sv || true
echo "makemessages completed"

echo "Finding and checking all Swedish .po files..."

# Use find with -exec and awk directly for efficiency
find ../ \( -path "../venv" -o -path "../.venv" \) -prune -o -type f -path '*/sv/LC_MESSAGES/*.po' -print 2>/dev/null | while read -r po_file; do
    echo "Checking file: $po_file"
    if awk '
        $0 == "msgstr \"\"" {
            if (getline && $0 !~ /^"/) {
                exit 1
            }
        }
    ' "$po_file"; then
        :  # no-op if OK
    else
        echo "Missing translations found in $po_file"
        touch /tmp/should_run_missing_translation
        echo "Created a file /tmp/should_run_missing_translation to indicate missing translations"
    fi
done
