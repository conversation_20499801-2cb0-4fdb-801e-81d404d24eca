#!/usr/bin/env bash

set -o errexit
set -o pipefail
set -o nounset
set -o xtrace

# Perform migrations
poetry run python manage.py migrate --noinput
poetry run python manage.py migrate archive --noinput --database=archive
poetry run python manage.py migrate history --noinput --database=archive
poetry run python manage.py migrate silk --noinput --database=archive

# note this only clears expired sessions
poetry run python manage.py clearsessions


if [[ "${RUN_TRANSLATIONS:-0}" -eq 1 ]]; then
  # translate dynamic fields
  echo "Translating default roles"
  poetry run python manage.py translate_default_role_name
  echo "Translating smart score components"
  poetry run python manage.py translate_smart_score_components
  echo "Translating app checks"
  poetry run python manage.py translate_app_checks
  echo "Translating app files"
  poetry run python manage.py translate_app_files
  echo "Translating fix guides"
  poetry run python manage.py translate_fix_guides
else
  echo "#### Skipping translations ####"
fi

# Rebuild materialized views if SQL has changed
echo "Creating materialized views"
poetry run python manage.py sync_pgviews --enable-materialized-views-check-sql-changed &

if [[ "${RUN_TRANSLATIONS:-0}" -eq 1 ]]; then
  # delete folder /static/jsi18n to not cause issues during compilejsi18n
  rm -rf ../static/jsi18n
  # Generate translation binary files if any
  echo "Start compiling translation files"
  poetry run python manage.py compilemessages --use-fuzzy -l en-gb -l sv -l de -l fr -l it -l nl -l pl
  for locale in en-gb sv de fr it nl pl; do
    poetry run python manage.py compilejsi18n -l "$locale"
  done
fi

echo "Complete compiling translation files"

# Collects the static files into STATIC_ROOT
echo "Start collectstatic"
poetry run python manage.py collectstatic --noinput
echo "Complete collectstatic"

poetry run python manage.py runserver_plus 0.0.0.0:8000 --nopin
