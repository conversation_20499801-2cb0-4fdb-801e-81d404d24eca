DB_HOST=postgres
DB_USER=cybersmart
DB_PASSWORD=password
DB_NAME=cybersmart
ARCHIVE_DB_NAME=cybersmart_archive
DB_PORT=5432
HTTP_X_FORWARDED_PROTO=http
ACCOUNT_DEFAULT_HTTP_PROTOCOL=http
# SITE_ID=3
# if running unittests, please set this to "True" or add as env var on your local env
IS_TEST=False
# if using dynamoDB for local development
# AWS_ACCESS_KEY_ID=<Ask someone from devops for this>
# AWS_SECRET_ACCESS_KEY=<Ask someone from devops for this>
AWS_DEFAULT_REGION="eu-west-2"
AWS_ROLE_ARN="arn:aws:iam::************:role/cs-aws-assume-role"
CS_AWS_CAP_V5_ASSUME_ROLE=<Ask someone from devops for this>
AWS_SESSION_NAME="testsession"
WEB_NOTIFICATIONS_TABLE_NAME=cs_develop_web_notification
# if using SCORM Cloud for local development
# SCORM_APP_ID=<Ask someone from devops for this>
# SCORM_SECRET_KEY=<Ask someone from devops for this>
# SCORM_CLOUD_PASSWORD=<Ask someone from devops for this>
# TRUSTD_API_KEY=<Ask someone from devops for this>
TRUSTD_CLIENT_SECRET="test_client_secret"
# POSTHOG_API_KEY=<Get from PostHog dashboard>
BULK_DOWNLOAD_MSI_SERVER=""
BULK_DOWNLOAD_PKG_SERVER=""
CAP_V5_BULK_DOWNLOAD_MSI_SERVER=""
CAP_V5_BULK_DOWNLOAD_PKG_SERVER=""
APP_BUILD_S3_BUCKET_NAME=""
APP_BUILD_S3_FILE_NAME_PREFIX=""
DEEPL_TRANSLATE_KEY="fake_key"
