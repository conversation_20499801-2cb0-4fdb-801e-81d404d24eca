#!/usr/bin/env bash

export CPLUS_INCLUDE_PATH=/usr/include/gdal
export C_INCLUDE_PATH=/usr/include/gdal

echo "Creating result directories"

RESULTS='/app/results'

if [ -d "/results" ]
then
    ln -s /results ${RESULTS}
    rm -rf ${RESULTS}/*
else
    mkdir -p ${RESULTS}
fi

TIMEOUT_DURATION="${TEST_TIMEOUT:-20m}"

TEST_ENV=$1
export PLAYSTORE_FIRST_NAME=$5
export PLAYSTORE_EMAIL_ADDRESS=$6
export PLAYSTORE_PASSWORD=$7
export LAMBDA_TEST_USERNAME=$8
export LAMBDA_TEST_ACCESS_KEY=$9

# Export load test parameters if they're set
[ ! -z "$LOAD_TEST_NUMBER_OF_USERS" ] && export LOAD_TEST_NUMBER_OF_USERS
[ ! -z "$LOAD_TEST_SPAWN_RATE" ] && export LOAD_TEST_SPAWN_RATE
[ ! -z "$LOAD_TEST_DURATION" ] && export LOAD_TEST_DURATION
[ ! -z "$LOAD_TEST_CAP_VERSION" ] && export LOAD_TEST_CAP_VERSION

if [[ "$2" != *"load"* ]]; then
  TESTS=$(echo "$2" | tr '_' ' ')
else
  TESTS="$2"
fi
SERVER=$(cat app/acceptance/circleci_acceptance_tests_server.txt)
ZEPHYR_TOKEN=$(cat app/acceptance/circleci_acceptance_tests_zephyr_token.txt)

if [ "$TEST_ENV" == "staging" ]
then
  export TRUSTD_CLIENT_SECRET=$4
else
  export TRUSTD_CLIENT_SECRET=$3
fi

case "$TESTS" in
    "load_general")
        BUILD_NAME="Small load tests - general"
        TEST_CYCLE_FOLDER_ID=13524903
        ;;
    "load_appcheckin")
        BUILD_NAME="Intensive load tests - appcheckin"
        TEST_CYCLE_FOLDER_ID=13524907
        ;;
    "load_patch")
        BUILD_NAME="Intensive load tests - patch"
        TEST_CYCLE_FOLDER_ID=23145107
        ;;
    "security")
        BUILD_NAME="Security acceptance tests"
        TEST_CYCLE_FOLDER_ID=13524900
        ;;
    "T754 or T755 or T756 or T761 or T762")
        BUILD_NAME="Accessibility acceptance tests"
        TEST_CYCLE_FOLDER_ID=13524904
        ;;
    "emailflow")
        BUILD_NAME="Acceptance email tests"
        TEST_CYCLE_FOLDER_ID=13526518
        ;;
    "mobileflowIndividual")
        BUILD_NAME="Acceptance mobile individual tests"
        TEST_CYCLE_FOLDER_ID=17690270
        ;;
    "mobileflowBulk")
        BUILD_NAME="Acceptance mobile bulk tests"
        TEST_CYCLE_FOLDER_ID=17690310
        ;;
    *)
        BUILD_NAME="Acceptance tests"
        TEST_CYCLE_FOLDER_ID=13524926
        ;;
esac

sleep 10

echo "Running tests on $TEST_ENV"
if [ "$TESTS" == "load_general" ] || [ "$TESTS" == "load_appcheckin" ] || [ "$TESTS" == "load_patch" ]
then
    echo "Load test configuration:"
    echo "  Number of users: ${LOAD_TEST_NUMBER_OF_USERS:-not set}"
    echo "  Spawn rate: ${LOAD_TEST_SPAWN_RATE:-not set}"
    echo "  Duration: ${LOAD_TEST_DURATION:-not set} seconds"
    echo "  CAP version: ${LOAD_TEST_CAP_VERSION:-not set}"
    python3 -m pytest /app/acceptance/load_tests --test_env "$TEST_ENV" -k "$TESTS" --env circleci
else
    awk 'BEGIN{OFS=FS=" "} {for (i=1; i<=NF; i++) {if (match($i, /^[^/]+/)) {prefix=substr($i, RSTART, RLENGTH); gsub("\\.", "/", prefix); $i=prefix substr($i, RLENGTH+1)}; gsub("app/acceptance/acceptance_tests/", "", $i); gsub("acceptance/acceptance_tests/", "", $i); gsub("\\.test", "/test", $i); if ($i !~ /\.py$/) $i=$i".py"}; print}' app/acceptance/files.txt > app/acceptance/acceptance_tests/test_files.txt
    cd app/acceptance/acceptance_tests || exit 1
    NUM_PROCESSES="-n 6"
    if [ "$TESTS" == "emailflow" ]; then
        NUM_PROCESSES=""
    fi
    timeout --signal=TERM --kill-after=30s "$TIMEOUT_DURATION" \
    python3 -m pytest $(cat test_files.txt) --test_env "$TEST_ENV" -k "$TESTS" --env circleci "$NUM_PROCESSES"  --dist loadfile --reruns 1 --reruns-delay 2
fi

EXIT_CODE=$?

if [ "$EXIT_CODE" -eq 124 ] || [ "$EXIT_CODE" -eq 137 ]; then
  echo "Acceptance tests timed out after $TIMEOUT_DURATION."
  echo "Acceptance tests timed out after $TIMEOUT_DURATION." > ./../../results/known_failure_message.txt
fi

sleep 3
if [ "$SERVER" == "circleci" ]; then
    curl -H "Authorization: Bearer ${ZEPHYR_TOKEN}" -F "file=@./../../results/junitxml_report.xml;type=application/xml" -F 'testCycle={"name" : "'"${BUILD_NAME} ${TEST_ENV} $(date +"%d-%m-%Y %H:%M:%S")"'", "folderId" : "'"${TEST_CYCLE_FOLDER_ID}"'"};type=application/json' "https://api.zephyrscale.smartbear.com/v2/automations/executions/junit?projectKey=CUSTEAM"
fi

test $EXIT_CODE -eq 0 || exit $EXIT_CODE

echo "All done"
exit 0