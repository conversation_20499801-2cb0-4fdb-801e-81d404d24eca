[tool.poetry]
name = "cyber-smart"
version = "1"
description = "a monolith and consists in our WebApp and API source code, but they are deployed separately"
authors = ["Your Name <<EMAIL>>"]
package-mode = false

[tool.poetry.dependencies]
python = "^3.11.7"
psycopg = "^3.2.1"
psycopg-binary = "^3.2.1"
requests = "~2.32.4"
django = "~5.1.10"
oauthlib = "3.1.0"
boto3 = "1.38.25"
stripe = "2.40.0"
pillow = "^11.3.0"
qrcode = "7.3.1"
celery = "5.5.2"
celery-once = "3.0.1"
redis = "^6.2.0"
numpy = "^2.2.0"
whitenoise = "6.8.2"
sendgrid = "6.10.0"
flower = {git = "https://github.com/cyber-smart/flower.git", rev = "master"}
django-sendgrid-v5 = "^1.2.4"
djangorestframework = "^3.15.2"
django-import-export = "3.3.1"
django-widget-tweaks = "^1.5.0"
jsonschema = "<4.0"
django-allauth = "0.56.1"
django-allauth-2fa = {git = "https://github.com/cyber-smart/django-allauth-2fa.git", rev = "0.11.4"}
django-cors-headers = "4.2.0"
django-ckeditor = ">=6.7.1"
django-storages = "^1.14.4"
django-impersonate = "^1.9.4"
django-extensions = "^3.2.3"
django-celery-results = "^2.6.0"
celery-redbeat = "^2.3.2"
django-admin-cursor-paginator="0.1.6"
django-cachalot = "^2.7.0"
sentry-sdk = ">=2.8.0"
aiocontextvars = "0.2.2"
fdfgen = "0.16.1"
pypdf = "3.17.0"
pdfkit = "1.0.0"
reportlab = "3.6.13"
django-redis = "^5.4.0"
django-silk = "5.2.0"
drf-yasg = ">=1.21.7"
cpe = "1.2.1"
django-sesame = "1.7"
django-oauth-toolkit = "2.3.0"
python-dateutil = "2.8.2"
django-debug-toolbar = "4.2.0"
django-environ = "0.4.5"
vine = "5.1.0"
django-json-widget = "^2.0.0"
django-inline-svg = "0.1.1"
chargebee = "2.8.4"
django-password-policies-iplweb = "0.8.5"
swagger-spec-validator = "2.7.6"
gocardless_pro = "1.18.0"
django-phonenumber-field = "4.0.0"
phonenumbers = "8.12.4"
dataclasses = "0.6"
dataclasses-json = "0.5.1"
django-colorful = "1.3"
ua-parser = "0.15.0"
user-agents = "2.1"
django-user-agents = "0.4.0"
django-upload-validator = "1.1.6"
facebook-business = "12.0.1"
python-postmark-inbound = "1.1.0"
django-waffle = "^4.2.0"
django-model-utils = "^5.0.0"
python-slugify = "4.0.1"
tenacity = "6.2.0"
bpython = "0.22.1"
rustici-software-cloud-v2 = "3.0.0"
xlrd = "1.2.0"
xlwt = "1.3.0"
openpyxl = "^3.1.5"
odfpy = "1.4.1"
MarkupPy = "1.18"
mock = "4.0.3"
django-admin-shell = {git = "https://github.com/cyber-smart/django-admin-shell.git", rev = "master"}
django-admin-autocomplete-filter = "0.7.1"
django-more-admin-filters = "^1.9"
django-simple-history = "3.5.0"
pyminizip = {git = "https://github.com/cyber-smart/pyminizip"}
django-statici18n = "^2.6.0"
django-pgviews-redux = "^0.11.0"
django-versionfield = "^1.0.3"
beautifulsoup4 = "4.11.1"
pandas = "^2.2.3"
shortuuid = "1.0.9"
httpx = ">=0.23.0,<1.0.0"
twisted = ">=24.7.0rc1"
setuptools = ">=80.7.1"
django-ratelimit = "4.0.0"
tornado = ">=6.5"
django-simple-sso = {git = "https://github.com/cyber-smart/django-simple-sso.git", rev = "1.1"}
aiohttp = ">=3.12.14"
cryptography = "^44.0.1"
sqlparse = "^0.5.0"
django-select2 = ">=8.4.1"
semantic-version = "2.10.0"
freezegun = "^1.5.1"
flask-cors = "^6.0.0"
ijson = "^3.4.0"

# pymupdf to 1.24.9 is currently broken
# see: https://github.com/cyber-smart/cybersmart-platform-core/pull/9364#issuecomment-2275364958
pymupdf = "1.24.7"
urllib3 = ">=2.5.0"
django-citext = "^1.0.2"
regex = "^2024.11.6"
deep-translator = "~=1.11.4"
django-deep-translator = "~=1.5.1"
h11 = ">=0.16.0,<1.0.0"
django-modeltranslation = "^0.19.14"
deepl = "^1.22.0"

# Pin it until bug https://github.com/certifi/python-certifi/issues/349 is fixed
certifi = "2025.1.31"

# WebSocket dependencies
websockets = "^14.1"
aioredis = "^2.0.1"

[tool.poetry.group.test.dependencies]
coverage = "7.9.2"
tblib = "1.7.0"
pytest = "8.4.1"
pytest-django = "4.11.1"
factory-boy = "3.2.1"
pytest-html = "4.1.1"
pytest-xdist = "^3.6.1"
django-migrations-ci = "^0.11"
pytest-asyncio = "^1.0.0"

[tool.poetry.group.dev.dependencies]
werkzeug = "~3.0.6"
watchdog = "3.0.0"
gunicorn = "^23.0.0"
blessings = "1.7"
ruff = "^0.1.15"
django-debug-toolbar-template-profiler = "^2.1.0"
mypy = "^1.8.0"
django-stubs = "^4.2.7"
types-redis = "^4.6.0"
mypy-extensions = "^1.1.0"

[tool.poetry.group.production.dependencies]
gunicorn = "^23.0.0"
newrelic = "^10.3.1"

[tool.poetry.group.acceptance_test.dependencies]
requests = "~2.32.4"
pytest = "8.4.1"
axe-selenium-python-nhsuk = "1.0.1"
beautifulsoup4 = "4.11.1"
pypdf = "3.17.0"
# only upgrade selenium to 4.32.0 if bug https://github.com/certifi/python-certifi/issues/349 is fixed
selenium = "4.32.0"
Appium-Python-Client = "4.0.0"
pytest-html = "4.1.1"
pytest-xdist = "^3.8.0"
requests-toolbelt = "1.0.0"
gevent = "^24.2.1"
pytest-rerunfailures = "15.1"
python-dateutil = "2.8.2"
chargebee = "2.8.4"
mailosaur = "7.15.0"
locust = "^2.33.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]



# Assume Python 3.11
target-version = "py311"

# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = ["F", "E","W"]
extend-select = []
ignore = ["E501","F403"]

# Show fixes for each error.
show-fixes = true

[tool.ruff.format]

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

[tool.mypy]
python_version = "3.11"
mypy_path = "src"
namespace_packages = true
explicit_package_bases = true
# Enable strict mode settings that aren't default
disallow_untyped_defs = true
disallow_incomplete_defs = true
disallow_untyped_calls = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
strict_equality = true

# Only check websocket_integration app - use pattern
packages = ["cyber_smart.apps.websocket_integration", "cyber_smart.apps.opswat_patch"]

# Ignore imports from untyped modules
[[tool.mypy.overrides]]
module = [
    "django.*",
    "appusers.*",
    "django_redis.*",
    "websockets.*",
    "software_inventory.*",
    "redis.*",
    "common.*",
]
ignore_missing_imports = true
ignore_errors = true

[[tool.mypy.overrides]]
module = [
    "cyber_smart.__init__",
    "cyber_smart.celery_app",
]
ignore_missing_imports = true
ignore_errors = true
no_implicit_reexport = false
