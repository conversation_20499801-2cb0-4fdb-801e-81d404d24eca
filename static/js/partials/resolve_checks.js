function manuallyResolveCheck(element) {
    const app_check_id = element.dataset.appCheckId;
    const reason = document.getElementById('mark-resolved-reason-' + app_check_id);
    const csrf_token = document.getElementsByName("csrfmiddlewaretoken")[0].value;
    $.ajax({
        url: "bulk-resolve/",
        method: "POST",
        data: {
            'csrfmiddlewaretoken' : csrf_token,
            'question_id': app_check_id,
            'manual_resolved': true,
            'reason': reason.value,
        }
    }).done(function(response) {
        if (response.status === 1) {
            swal(gettext('Success'), gettext("Has been marked as resolved"), "success");
            location.reload();
        } else {
            swal(gettext("Error"), gettext("Something went wrong"), "error");
            location.reload();
        }
    });
}

function permanentlyResolveCheck(element) {
    const app_check_id = element.dataset.appCheckId;
    const reason = document.getElementById('mark-resolved-reason-' + app_check_id);
    const csrf_token = document.getElementsByName("csrfmiddlewaretoken")[0].value;
    $('#mark-resolved-modal-' + app_check_id).modal('hide');
    $.ajax({
        url: "bulk-permanent-resolve/",
        method: "POST",
        data: {
            'csrfmiddlewaretoken' : csrf_token,
            'question_pk': app_check_id,
            'reason': reason.value,
        }
    }).done(function(response) {
        if (response.status === 1) {
            swal(gettext('Success'), gettext("Has been marked as permanently resolved"), "success");
            location.reload();
        } else {
            swal(gettext("Error"), gettext("Something went wrong"), "error");
            location.reload();
        }
    });
}
