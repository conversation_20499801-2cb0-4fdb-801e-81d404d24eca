/* global $ */
/* global swal */
/* global gettext */
/* global console */

$(document).ready(function() {
    "use strict";
    function getDeviceModal(element) {
        // get the device modal from the element
        return $(element).closest(".modal");
    }

    function getURL(deviceModal) {
        return deviceModal.data("device-assign-user-url");
    }

    // =============================
    // Assign Device to User
    $(".assign-device-to-user").on("click", function () {
        let assignButton = $(this);
        let deviceModal = getDeviceModal(this);
        let newAppUserUUID = $(this).data("app-user-uuid");
        let csrfToken = $("input[name='csrfmiddlewaretoken']").val();

        // after first assignment we show the unassign button
        deviceModal.find(".unassign-from-user").removeClass("hidden");

        $.ajax({
            url: getURL(deviceModal),
            method: "POST",
            headers: {
                "X-CSRFToken": csrfToken
            },
            data: {
                "new_app_user_uuid": newAppUserUUID
            }
        }).done(function(response) {
            if (response.success) {
                // remove old assignments if exist
                deviceModal.find(".assign-device-to-user.btn-success").text("Assign").removeClass("btn-success").addClass("btn-primary").attr("disabled", false);
                // set new button style
                assignButton.removeClass("btn-primary").addClass("btn-success").text("Assigned").attr("disabled", true);
                // update the current app user uuid
                deviceModal.data("current-app-user-uuid", newAppUserUUID);
                // update device assign url to the actual one
                deviceModal.data("device-assign-user-url", response.url);

            } else {
                swal(gettext("Error"), response.error, "error");
            }
        });
    });
    // =============================

    // handle modal close and redirect after changing the app user
    $(".user-attribution-modal").on('hide.bs.modal', function (e) {
        let deviceModal = $(this);
        let originalAppUserUUID = deviceModal.data("original-app-user-uuid");
        let currentAppUserUUID = deviceModal.data("current-app-user-uuid");
        if (originalAppUserUUID !== currentAppUserUUID) {
            // fade out the modal for 800ms
            $(".modal-dialog").fadeOut("slow");

            // Replace the href with the new app user uuid,
            // and redirect gracefully after closing the modal.
            // Preserve search parameters in case we are on the list device page.
            window.location.href = location.href.replace(originalAppUserUUID, currentAppUserUUID);
            // keep the backdrop as we move
            e.preventDefault();
        }
    });

    //=============================
    //Warning Message
    $(".unassign-from-user").on("click", function(){
        let deviceModal = getDeviceModal(this);
        let deviceHostname = deviceModal.data("app-install-hostname");
        let csrfToken = $("input[name='csrfmiddlewaretoken']").val();

        // before hiding modal update original uuid with the current one to prevent page reload
        deviceModal.data("original-app-user-uuid", deviceModal.data("current-app-user-uuid"));
        // hide user assignment modal
        deviceModal.modal("toggle");

        // show the warning message
        swal({
            title: gettext("Unassign this device?"),
            text: interpolate(gettext("Unassigning %(device)s will remove it from the current user. It can always be assigned to any user at a later time."), {device: deviceHostname}, true),
            type: "warning",
            confirmButtonColor: "#DD6B55",
            showCancelButton: true,
            confirmButtonText: gettext("Yes, unassign it"),
            cancelButtonText: gettext("Cancel"),
            closeOnConfirm: false
        }, function(){
            $.ajax({
                url: getURL(deviceModal),
                method: "POST",
                headers: {
                    "X-CSRFToken": csrfToken
                },
                data: {} // no data needed
            }).done(function(response) {
                if (response.success) {
                    swal({title: gettext("Device unassigned"), text:gettext("This device has been unassigned from the user"), confirmButtonText:gettext("OK"), type:"success"});
                    // update the current app user uuid
                    deviceModal.data("current-app-user-uuid", response.uuid);
                    deviceModal.modal("hide");
                } else {
                    swal(gettext("Something went wrong"), gettext("Cannot unassign this device"), "error");
                    console.log(response.error);
                }
            }).error(function (response) {
                swal(gettext("Something went wrong"), gettext("Cannot unassign this device"), "error");
                console.log(response.error);
            });
        });
        return false;
    });
    //=============================

    // close the modal when done
    $(".assign-done").on("click", function(e){
        // prevent form submit
        e.preventDefault();
        let deviceModal = getDeviceModal(this);
        deviceModal.modal("toggle");
    });
});
