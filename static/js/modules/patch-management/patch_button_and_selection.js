/**
 * This script manages packages selection and patch button state in the patch management UI.
 * It handles checkbox states, updates the "Select All" button, and manages the visibility of the patch button and selected rows.
 * It uses a custom `CheckboxStateManager` to persist checkbox states across page loads.
 */


const PackagesToPatchStorageKey = JSON.parse(document.getElementById("id_packages_to_patch_storage_key").textContent);

function initializeSelectedPackages() {
    CheckboxStateManager.init(PackagesToPatchStorageKey, ".selected-package");
    updateSelectAllButton();
}

// Run on initial page load
document.addEventListener("DOMContentLoaded", initializeSelectedPackages);

// Run every time HTMX updates part of the DOM
document.addEventListener("htmx:afterSettle", initializeSelectedPackages);


/**
 * Syncs the "Select All" checkbox state with individual package checkboxes.
 *
 * If any individual `.selected-package` checkbox is unchecked,
 * this function will automatically uncheck the "#id_selected_package_all" checkbox.
 * This ensures that the "Select All" reflects the true selection state.
 */
$("body").on("change", ".selected-package", function () {
    if (!this.checked) {
        $("#id_selected_package_all").prop("checked", false);
    }
});

// Function to update the select all checkbox based on the selected packages
function updateSelectAllButton() {
    const $selectAllCheckbox = $("#id_selected_package_all");

    if (!$selectAllCheckbox.length) {
        // If the select all checkboxes don't exist, exit the function (when switch to patch history tab)
        return;
    }

    const patchablePackages = JSON.parse(document.getElementById("id_patchable_packages").textContent);
    const selectedPackages = CheckboxStateManager.getSelected(PackagesToPatchStorageKey);
    const allSelected = selectedPackages.length === patchablePackages.length;
    $selectAllCheckbox.prop("checked", allSelected);
}


// Function to update the button and selected rows container visibility
function updatePatchButtonAndRows(e) {
    const $btn = $('#patch-package-button');
    const $label = $btn.find('.button-label');
    const $rowsSelected = $(".rows-selected-container");

    if (!$btn.length) {
        // If the button doesn't exist, exit the function (when switch to patch history tab)
        return;
    }

    // Update button disabled state
    $btn.prop('disabled', e.detail.total === 0);

    // Update button label text
    const count = e.detail.total;
    $label.text(`Patch Now${count ? ` (${count})` : ''}`);

    // Toggle visibility of the selected rows container
    $rowsSelected.toggleClass('tw-invisible', count === 0);

    // Update the selected count in the rows container
    $rowsSelected.find('#rows-selected').text(count);

    updateSelectAllButton();
}


// Event listeners for state changes
["CheckboxStateManager:init", "CheckboxStateManager:change"].forEach(eventType => {
    $(document).on(eventType, updatePatchButtonAndRows);
});

// Handle deselection of all checkboxes
$("body").on("click", "#deselect-packages", function() {
    $(".selected-package").prop("checked", false).trigger("change");
    $(".rows-selected-container").addClass('tw-invisible');
    CheckboxStateManager.clear(PackagesToPatchStorageKey);
    // Dispatch a custom change event to inform other listeners
    const event = new CustomEvent("CheckboxStateManager:change", {
        detail: {
            id: null,
            total: 0
        }
    });
    document.dispatchEvent(event);
});

// Handle selection of all checkboxes
$("body").on("click", "#id_selected_package_all", function () {
    const isChecked = $(this).is(":checked");
    const patchablePackages = JSON.parse(document.getElementById("id_patchable_packages").textContent);

    if (!isChecked) {
        // Uncheck all: trigger the existing deselect logic
        $("#deselect-packages").trigger("click");
        return;
    }
    CheckboxStateManager.addMultiple(PackagesToPatchStorageKey, patchablePackages);
    patchablePackages.forEach((packageId) => {
        $(`.selected-package[data-id="${packageId}"]`)
            .prop("checked", true)
            .trigger("change");
    });
});

// When going to patch history tab, clear the selected packages
$("body").on("click", "#tab-main-patch-history", function () {
    CheckboxStateManager.clear(PackagesToPatchStorageKey);
    $(".selected-package").prop("checked", false);
    $(".rows-selected-container").addClass('tw-invisible');
    updateSelectAllButton();
});