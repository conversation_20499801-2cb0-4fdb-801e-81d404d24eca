$(document).ready(function () {
  const { BehaviorSubject, map } = rxjs;

  const table = $('#groups-table');
  table.footable();

  const getInputsRow = () => $('#addUserGroup .inputs-row').last()
  const addRowBtn = $('#addRow')
  const deleteRowBtn = $('[data-action="deleteRow"]')
  const form = $('#addUserGroup form');
  const errorContainer = $('#user-group-error');
  const submitBtn = $('#btn-submit-add-group');
  const modal = $('#addUserGroup');
  const isFormDirty$ = new BehaviorSubject(false)
  const entityToEdit$ = new BehaviorSubject(null)
  const isEditMode$ = entityToEdit$.pipe(map(entity => !!entity))

  const initialFormData$ = new BehaviorSubject(null)
  const getFormValues = () => {
    const formData = new FormData(form[0]);
    return JSON.stringify(Array.from(formData.entries()))
  }

  const orgSecureId = /((\w{4,12}-?)){5}/.exec(location.pathname)[0];
  const url = `${location.origin}/api/v2/groups/${orgSecureId}/`;


  // When the modal is shown
  modal
    .on('show.bs.modal', function (e) {
      const dataset = e.relatedTarget.dataset
      if (dataset.id) {
        entityToEdit$.next({
          name: dataset.name,
          id: dataset.id,
          description: dataset.description,
        })
      } else {
        entityToEdit$.next(null);
      }

      initialFormData$.next(getFormValues());
      isFormDirty$.next(false);

      // Set focus on the input element
      $('#name-0').focus();
    })
    .on('hide.bs.modal', function (e) {
      errorContainer.html('');
      const entity = entityToEdit$.getValue()
      if (entityToEdit$.getValue()) {
        Object.entries(entity).forEach(([key]) => {
          getInputsRow().find(`input[id="${key}-0"]`).each(function (_, e) {
            e.value = ''
          })
        })
      }
    })

  form.on('submit', async function (e) {
    e.preventDefault();

    if (!isFormDirty$.getValue()) {
      return;
    }

    errorContainer.html('');
    const formData = new FormData(this);
    const data = {
      list: new Map()
    }

    Array.from(formData.entries()).forEach(([key, value]) => {
      if (key !== 'csrfmiddlewaretoken') {
        const [fieldName, idx] = key.split('-');
        data.list.set(idx, {
          ...(data.list.get(idx) ?? {}),
          [fieldName]: value
        })
      }
    })

    if (entityToEdit$.getValue()) {
      const body = {
        ...data.list.get('0'),
      }
      editGroup(body)
    } else {
      const body = Array.from(data.list.values())
      createGroups(body)
    }
  })

    modal.on('click', '[data-action="deleteRow"]', function(e) {
        if ($('.group-modal .inputs-row').length > 1) {
            $(this).closest('.group-modal .inputs-row').remove()
        }
    })

  form.on('change keyup paste', function () {
    const isFormDirty = getFormValues() !== initialFormData$.getValue();

    isFormDirty$.next(isFormDirty);
  })

  isFormDirty$.subscribe(isDirty => {
    submitBtn.attr('disabled', !isDirty)
  })

  addRowBtn.on('click', function () {
    const newRow = getInputsRow().clone();
    newRow.find('input').each(function (_, item) {
      const $item = $(item);
      const [name, idx] = $item.attr('id').split('-');
      const newId = `${name}-${+idx + 1}`;
      $item.attr('id', newId)
      $item.attr('name', newId)
      $item.val('')
      $item.closest('.form-group').find('label').attr('for', newId)
    })

    newRow.insertAfter(getInputsRow())
    newRow.find('input').first().focus()
  })


  isEditMode$.subscribe(isEditing => {
    if (isEditing) {
      addRowBtn.hide();
      deleteRowBtn.hide();
      $('#addUserGroupLabel').hide();
      $('#editUserGroupLabel').show();
      modal.find('[data-action="deleteRow"]').each(function (_, e) {
        e.click();
      })

      const initialData = entityToEdit$.getValue();
      Object.entries(initialData).forEach(([key, value]) => {
        getInputsRow().find(`input[id="${key}-0"]`).each(function (_, e) {
          e.value = value
        })
      })
    } else {
      addRowBtn.show();
      deleteRowBtn.show();
      $('#addUserGroupLabel').show();
      $('#editUserGroupLabel').hide();
    }
  })

  function editGroup(body) {
    $.ajax({
      url: `${url}${entityToEdit$.getValue()?.id}/`,
      data: JSON.stringify(body),
      method: 'PATCH',
      setRequestHeader() {},
        headers: {
            'Content-Type': 'application/json',
        },
      success: function (response) {
        updateRowData(response);
        $('#alert-success-edit').show()

        setTimeout(() => {
          $('#alert-success-edit').hide();
        }, 5000)
      },
      error: function ({ responseJSON }) {
        let errors = [];
        if (Array.isArray(responseJSON)) {
          errors = responseJSON.map(errorObj => Object.values(errorObj).flat(5)).flat(5)
        } else {
          errors = Object.values(responseJSON).flat(5)
        }

        errors.forEach(errorText => errorContainer.append($(`<p class="text-danger">${errorText}</p>`)))
      }
    });
  }

  function deleteGroup(id) {
    return new Promise((resolve, reject) => {
        $.ajax({
          url: `${url}${id}/`,
          method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        },
          success: function (response) {
            $('#alert-success-deleted').show()
            resolve(response);
            $(`td.group[data-id="${id}"]`).text($('#unassigned').text()).data('id', '')

            setTimeout(() => {
              $('#alert-success-deleted').hide();
            }, 5000)
          },
          error: function ({ responseJSON }) {
            reject(responseJSON)
          }
        });
    })
  }

  function createGroups(body) {
    $.ajax({
      url,
      data: JSON.stringify(body),
      method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
      success: function () {
        location.reload();
      },
      error: function ({ responseJSON }) {
        let errors = [];
        if (Array.isArray(responseJSON)) {
          errors = responseJSON.map(errorObj => Object.values(errorObj).flat(5)).flat(5)
        } else {
          errors = Object.values(responseJSON).flat(5)
        }

        errors.forEach(errorText => errorContainer.append($(`<p class="text-danger">${errorText}</p>`)))
      }
    });
  }

  function updateRowData({ id, name, description }) {
    const editBtn = $(`#groups .edit[data-id="${id}"]`)
      .attr('data-name', name)
      .attr('data-description', description);
    const row = editBtn.closest('tr');
    row.find('.name').text(name)
    $(`td.group[data-id="${id}"]`).text(name)
    row.find('.delete').data('name', name)
    row.find('.description').text(description)
    $(`select[name="group"] option[value=${id}]`).text(name)
    $(`select[name="form-0-groups"] option[value=${id}]`).text(name)
    modal.modal('hide');
  }

  $(".alert").on("close.bs.alert", function () {
    $(this).hide();
    return false;
  });

  $('#groups a.delete')
  .on('click', function () {
    $('#group-name').text($(this).data('name'))
  })
  .on('confirm', function(e) {
      const dialog = e.relatedTarget;
      const button = $(this);
    button.attr('disabled', true);
    const id = button.data('id')
    deleteGroup(id)
        .then(() => {
            //get the row we are wanting to delete
            const row = button.parents('tr:first');
            table.data('footable').removeRow(row);
            const counter = $('#groups-counter');
            counter.text(+counter.text() - 1);
            $(`select[name="group"] option[value=${id}]`).remove();
            $(`select[name="form-0-groups"] option[value=${id}]`).remove();
        })
        .finally(() => {
            dialog.modal('hide');
            button.attr('disabled', false);
        })
  })



  /////////// open user modal
  initialUserData$ = new BehaviorSubject(null);

  let data = JSON.parse(document.getElementById('app_users_to_send_email_to').textContent);
  const appUsersToSendEmailTo$ = new BehaviorSubject(data);

  data = JSON.parse(document.getElementById('app_users_without_app_installs').textContent);
  const appUsersWithoutAppInstalls$ = new BehaviorSubject(data);


  window.csApp = {
        ...(window.csApp ?? {}),
        initialUserData$,
        appUsersToSendEmailTo$,
        appUsersWithoutAppInstalls$,
    };

  /////////// user modal
  // click button submit edit form
        $('body').on('click', '#btn-submit-edit', function(e){
            e.preventDefault();

            var csrfmiddlewaretoken = $('input[name=csrfmiddlewaretoken]').val(),
                id = $('.Formedit input[name=id]').val(),
                firstname = $('.Formedit input[name=firstname]').val(),
                lastname = $('.Formedit input[name=lastname]').val(),
                email = $('.Formedit input[name=email]').val(),
                group = $('.Formedit select[name=group]').val(),
                is_admin = $('.Formedit input[name=is_admin]').is(":checked"),
                org_id = $('.Formedit').attr("org_id");
            var formData = new FormData();
            formData.append('email', email);
            formData.append('first_name', firstname);
            formData.append('last_name', lastname);
            formData.append('is_admin', is_admin);
            formData.append('groups', group);
            $("#first_name_error").text("");
            $("#last_name_error").text("");
            $.ajax({
                method: 'PUT',
                url: '/api/dashboard/'+org_id+'/appuser/'+id+'/',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                beforeSend: function(request) {
                    request.setRequestHeader("X-CSRFTOKEN", csrfmiddlewaretoken);
                },
            }).success(function(e){
                const alert = $('#alert-success-edit-user');
                alert.show();
                setTimeout(() => {
                  alert.hide();
                }, 5000)
            }).error(function(e){
                $.each(e.responseJSON, function (key, value) {
                    if (key === 'first_name') {
                        $("#first_name_error").text(e.responseJSON['first_name'])
                    } else if (key === 'last_name') {
                        $("#last_name_error").text(e.responseJSON['last_name'])
                    } else if (key === 'email') {
                        $("#email_error").text(e.responseJSON['email'])
                    }
                })
            }).done(function(response){
                if (response.success==0) {
                    $('p.error').css('display','block');
                    $('p.error').html(gettext('This email address is already in use, please use another'));
                } else if (response.success==-1) {
                    $('p.error').css('display','block');
                    $('p.error').html(gettext('You do not have access'));
                } else if (response.success==-2) {
                    // bug task cs-202
                    const data = response.data;
                    $('#myModal2').modal('hide');
                    $('#demo-foo-addrow tr#'+data.pk+' td.name').text(data.first_name + " " + data.last_name);
                    if(data.is_admin == true) {
                        $('#demo-foo-addrow tr#'+data.pk+' td.is__admin').html('<span class="label label-table label-primary cl_admin" style="display: inline;">' + gettext("Dashboard Admin") + '</span>');
                        $('#demo-foo-addrow tr#'+data.pk).attr('is_admin', 'admin_');
                    } else {
                        $('#demo-foo-addrow tr#'+data.pk+' td.is__admin').html('<span class="label label-table label-primary label-outline cl_user" style="display: inline;">' + gettext("User") + '</span>');
                        $('#demo-foo-addrow tr#'+data.pk).attr('is_admin', 'user_');
                    }
                } else if (response.success==-3) {
                    $('p.error').css('display','block');
                    $('p.error').html(gettext('you must edit users in G Suite / Microsoft 365 and these changes will sync here'));
                } else if (response.success==-4) {
                    $('p.error').css('display','block');
                    $('p.error').html(gettext('This email address is already in use, please use another'));
                }else if (response.success==1) {
                    let data = response.data;
                    $('#myModal2').modal('hide')
                    $('#demo-foo-addrow tr#'+data.pk+' td.name').text(data.first_name + " " + data.last_name);
                    $('#demo-foo-addrow tr#'+data.pk+' td.email').text(data.email);
                    $('#demo-foo-addrow tr#'+data.pk+' .remove-user-modal-name').text(data.first_name + " " + data.last_name);

                    $('#demo-foo-addrow tr#'+data.pk+' td.group').text(data.groups.map(({name}) => name).join(', ') || $('#unassigned').text())
                        .attr('data-id', data.groups[0]?.id ?? '')
                        .data('id', data.groups[0]?.id ?? '');
                    const initialGroup = +initialUserData$.getValue().group;

                    if (data.groups.length > 1 || +data.groups[0]?.id !== initialGroup) {
                        const groupCountCell = $(`#groups-table [data-id="${initialGroup}"]`).closest('tr').find('.amount');
                        groupCountCell.text(+groupCountCell.text() - 1)
                        data.groups.forEach(({id}) => {
                            const changedGroupCountCell = $(`#groups-table [data-id="${id}"]`).closest('tr').find('.amount');
                            changedGroupCountCell.text(+changedGroupCountCell.text() + 1)
                        })
                    }

                    if(data.is_admin == true) {
                        $('#demo-foo-addrow tr#'+data.pk+' td.is__admin').html('<span class="label label-table label-primary cl_admin" style="display: inline;">' + gettext("Dashboard Admin") + '</span>');
                        $('#demo-foo-addrow tr#'+data.pk).attr('is_admin', 'admin_');
                    } else {
                        $('#demo-foo-addrow tr#'+data.pk+' td.is__admin').html('<span class="label label-table label-primary label-outline cl_user" style="display: inline;">' + gettext("User") + '</span>');
                        $('#demo-foo-addrow tr#'+data.pk).attr('is_admin', 'user_');
                    }
                }
            });
        });


  //bulk users edit
    const {selectedIds$} = window.csApp ?? {};
    const selector = $('#header-group-selector');
    const headerForm = selector.closest('form');
    const errorNode = headerForm.find('.error-text');
    const submitHeaderFormButton = $('#submit-header-btn');

    headerForm.on('change keyup paste', function () {
      submitHeaderFormButton.attr('disabled', selector.val() === null || !selectedIds$ || selectedIds$.getValue().size === 0)
    })


    submitHeaderFormButton.on('click', function() {
        const btn = $(this);
        btn.attr('disabled', true);
        errorNode.hide();
        const body = {
            group_ids: [selector.val() ? +selector.val() : null],
            app_user_ids: Array.from(selectedIds$.getValue()),
        }

        $.ajax({
          url: `${headerForm.attr('action')}`,
          data: JSON.stringify(body),
          method: 'PATCH',
          setRequestHeader() {},
            headers: {
                'Content-Type': 'application/json',
            },
          success: function ({updated_count}) {
          const alert = $('#alert-success-edit-user-bulk');
          alert.find('#user-count').text(updated_count);
          const group = selector.val();
          let text_to_add = gettext('Unassigned');
          if (group) {
              text_to_add = selector.find(`option[value=${group}]`).text();
          }
          alert.find('#user-group-name').text("\"" + text_to_add + "\"");
          if (updated_count > 1) {
            alert.find('#user-plural').show();
            alert.find('#user-singular').hide();
          } else {
            alert.find('#user-plural').hide();
            alert.find('#user-singular').show();
          }
          alert.show()
            body.app_user_ids.forEach(id => {
                const tr = $(`#demo-foo-addrow tr#${id}`);
                const initialGroup = +tr.find('.group').data('id');

                if (body.group_ids.length > 1 || body.group_ids[0] !== initialGroup) {
                    const groupCountCell = $(`#groups-table [data-id="${initialGroup}"]`).closest('tr').find('.amount');
                    groupCountCell.text(+groupCountCell.text() - 1);
                        body.group_ids.forEach((id) => {
                        const changedGroupCountCell = $(`#groups-table [data-id="${id}"]`).closest('tr').find('.amount');
                        changedGroupCountCell.text(+changedGroupCountCell.text() + 1)
                    })
                }
                const groupCell = tr.find('td.group');

                if (body.group_ids[0] === null) {
                    groupCell.text(gettext('Unassigned'));
                } else {
                    groupCell.text(body.group_ids.map((id) => selector.find(`option[value=${id}]`).text()).join(', '));
                }
                groupCell.data('id', body.group_ids[0] ?? '');
                groupCell.attr('data-id', body.group_ids[0] ?? '');
            })

            setTimeout(() => {
              alert.hide();
            }, 5000);

            $('#deselect').click();
            selector.val(null)
          },
          error: function ({ responseJSON }) {
            errorNode.show();
            btn.attr('disabled', false);
          },
        }).done(function () {
            btn.attr('disabled', false);
        });

    })



  $("#add-new-users-modal").on('hidden.bs.modal', function () {
        $(this).find('.remove_more_r').each(function(i, el){
            if ( i === 0) {
               return
            };
            el.click();
        });
  });

    const sendAppBtn = $('#send-app');
    sendAppBtn.on('click', function () {
        console.log(Array.from(selectedIds$.getValue()))
        const body = {
            app_user_ids: Array.from(selectedIds$.getValue()),
        }

        $.ajax({
            url: `${sendAppBtn.data('action')}`,
            data: JSON.stringify(body),
            method: 'POST',
            setRequestHeader() {
            },
            headers: {
                'Content-Type': 'application/json',
            },
            success: function () {
                $('#alert-success-sent-app').hide().show()

                setTimeout(() => {
                    $('#alert-success-sent-app').hide();
                }, 5000)
            },
            error: function () {
                errorNode.show();

                setTimeout(() => {
                    errorNode.hide();
                }, 5000)
            },
        }).done(function () {
            $('#deselect').click();
        });
    })
});
