$('#deactivate-button').click(function(){
    let app_install_id = $(this).attr('app_install_id');
    let url = $(this).attr('url');
    let org_url = $(this).attr('org_url');
    const csrfmiddlewaretoken = document.getElementsByName("csrfmiddlewaretoken")[0].value;
    swal({
        title: gettext("Deactivate this device?"),
        text: gettext("This device will no longer appear in the dashboard or compliance reports"),
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#DD6B55",
        confirmButtonText: gettext("Yes, deactivate it"),
        cancelButtonText: gettext("Cancel"),
        closeOnConfirm: false
    }, function(){
        fetch(url, {
            method: "PATCH",
            headers: {
                'Content-Type': 'application/json',
                "X-CSRFToken": csrfmiddlewaretoken,
            },
            body: JSON.stringify({
                'inactive': 'true',
                'app_install_ids': [app_install_id]
            })
        }).then(response => {
            if (response.ok) {
                swal({title: gettext("<PERSON><PERSON> deactivated"), text:gettext("This device has been deactivated from your dashboard"), confirmButtonText:gettext("OK"), type:"success"});
                window.location.href = org_url;
            } else {
                console.log(response)
                swal(gettext("Something went wrong"), gettext("Cannot deactivate this device"), "error");
            }
        }).catch(error => {
            console.log(error)
            swal(gettext("Something went wrong"), gettext("Cannot deactivate this device"), "error");
        });
    });
    return false;
});

function deviceDetailResolve(check_id, action, elem, url) {
    const csrfmiddlewaretoken = document.getElementsByName("csrfmiddlewaretoken")[0].value;
    var id_response = $( ".mark_as_resolved" ).attr('id_response');
    var data = {
        'csrfmiddlewaretoken' : csrfmiddlewaretoken,
        'check_id': check_id ,
        'id_response': id_response
    };
    data[action] = true;
    $.ajax({
        url: url,
        method: "POST",
        data: data
    }).done(function(response) {
        location.reload();
        if(response.status > 0){
            setTimeout(function(){
                elem.removeClass('active');
                // elem.css('display','none');
            }, 1000);
        }
    });
}

// Remote fix popup
$('.remote-fix-button').click(function(){
    var remoteButton = $(this);
    var url = remoteButton.attr('url');
    swal({
        title: gettext("Remote fix"),
        text: gettext("This has been added for remote remediation and the CyberSmart app will attempt to resolve on the users machine on the next check in (less than 15 minutes when in use)"),
        type: "success",
        showCancelButton: true,
        confirmButtonText: gettext("Confirm"),
        cancelButtonText: gettext("Cancel"),
        closeOnConfirm: false
    }, function(){
        var check_id = parseInt(remoteButton.attr('check-id'));
        var action = 'automatic_fix';
        remoteButton.addClass('active');
        remoteButton.attr('disabled','disabled');
        deviceDetailResolve(check_id, action, remoteButton, url);
    });
    return false;
});

$( "#sendPdf" ).click(function(e) {
    const csrfmiddlewaretoken = document.getElementsByName("csrfmiddlewaretoken")[0].value;
    $(this).addClass("active");
    $.ajax({
        url: window.location.href,
        method: "POST",
        data: {
            'csrfmiddlewaretoken' : csrfmiddlewaretoken
        }
    }).done(function(response) {
        $("#sendPdf").removeClass("active");
        if (response.status) {
            swal(
                { title: gettext("Success!"), text: gettext("You have successfully emailed the user a PDF"), confirmButtonText: gettext("OK"), type: "success", html: true }
            );
        } else {
            swal(gettext("Something went wrong"), gettext("Cannot send pdf to user"), "error");
        }
    }).error(function () {
        $("#sendPdf").removeClass("active");
        swal(gettext("Something went wrong"), gettext("Cannot send pdf to user"), "error");
    })
});
