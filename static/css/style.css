@charset "UTF-8";

/*Theme Colors*/
/*bootstrap Color*/
/*Normal Color*/
/*Border radius*/
/*Preloader*/
.preloader {
  width: 100%;
  height: 100%;
  top: 0px;
  position: fixed;
  z-index: 99999;
  background: #fff;
}

.preloader .cssload-speeding-wheel {
  position: absolute;
  top: calc(50% - 3.5px);
  left: calc(50% - 3.5px);
}

/* This is for popins font for firefox */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/poppins/v1/2fCJtbhSlhNNa6S2xlh9GyEAvth_LlrfE80CYdSH47w.woff2) format('woff2');
  unicode-range: U+02BC, U+0900-097F, U+1CD0-1CF6, U+1CF8-1CF9, U+200B-200D, U+20A8, U+20B9, U+25CC, U+A830-A839, U+A8E0-A8FB;
}

/* This is for popins font for firefox */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/poppins/v1/UGh2YG8gx86rRGiAZYIbVyEAvth_LlrfE80CYdSH47w.woff2) format('woff2');
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}

/* This is for popins font for firefox */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/poppins/v1/yQWaOD4iNU5NTY0apN-qj_k_vArhqVIZ0nv9q090hN8.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215, U+E0FF, U+EFFD, U+F000;
}

* {
  outline: none !important;
}

/* The new CyberSmart fonts for Headings*/
/*Comment*/
@font-face {
  font-family: 'Circular Std Book';
  font-style: normal;
  font-weight: normal;
  src: local('Circular Std Book'), url('/static/fonts/CircularStd-Book.woff') format('woff');
}

@font-face {
  font-family: 'Circular Std Book Italic';
  font-style: normal;
  font-weight: normal;
  src: local('Circular Std Book Italic'), url('/static/fonts/CircularStd-BookItalic.woff') format('woff');
}

@font-face {
  font-family: 'Circular Std Medium';
  font-style: normal;
  font-weight: normal;
  src: local('Circular Std Medium'), url('/static/fonts/CircularStd-Medium.woff') format('woff');
}

@font-face {
  font-family: 'Circular Std Medium Italic';
  font-style: normal;
  font-weight: normal;
  src: local('Circular Std Medium Italic'), url('/static/fonts/CircularStd-MediumItalic.woff') format('woff');
}

@font-face {
  font-family: 'Circular Std Bold';
  font-style: normal;
  font-weight: normal;
  src: local('Circular Std Bold'), url('/static/fonts/CircularStd-Bold.woff') format('woff');
}

@font-face {
  font-family: 'Circular Std Bold Italic';
  font-style: normal;
  font-weight: normal;
  src: local('Circular Std Bold Italic'), url('/static/fonts/CircularStd-BoldItalic.woff') format('woff');
}

@font-face {
  font-family: 'Circular Std Black';
  font-style: normal;
  font-weight: normal;
  src: local('Circular Std Black'), url('/static/fonts/CircularStd-Black.woff') format('woff');
}

@font-face {
  font-family: 'Circular Std Black Italic';
  font-style: normal;
  font-weight: normal;
  src: local('Circular Std Black Italic'), url('/static/fonts/CircularStd-BlackItalic.woff') format('woff');
}


/* The new CyberSmart font for body text*/
@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap');

body {
  /*This is for body gradiant*/
  background: #73c7d2 !important;
  /* Old browsers */
  background: -moz-linear-gradient(top, #73c7d2 0%, #73c8cf 0%, #7ec9ce 5%, #80cacb 7%, #83cacc 7%, #8acac9 11%, #8fcdca 12%, #8fccc7 13%, #97ccc6 16%, #9fcdc2 20%, #a1cdc0 20%, #a4cdbd 24%, #a7cdbe 24%, #a7ccbb 26%, #abcdbc 26%, #adccba 29%, #afcebc 29%, #b1cdb7 31%, #b3cfb9 31%, #b5ccb8 32%, #b7cfb9 34%, #bcceb4 37%, #c2d0b6 40%, #c4d1b3 43%, #cad0b4 44%, #c8d1b2 45%, #cbd2b1 45%, #ccd1b1 47%, #ced3b3 47%, #d2d2ae 51%, #d6d3b0 51%, #d4d1ae 52%, #ddd1ab 59%, #dcd0a8 59%, #ddcea5 62%, #e0cea6 63%, #e0cda5 65%, #decba3 65%, #e2cba2 67%, #e0c79e 70%, #e2c69f 71%, #e2c59d 72%, #e1c49a 73%, #e4c49b 73%, #e4c49b 74%, #e2c299 74%, #e3c098 77%, #e2bf95 77%, #e4be97 78%, #e3be92 78%, #e3bd96 78%, #e1ba91 81%, #e3b993 82%, #e1b58e 84%, #e3b790 84%, #e1b58e 85%, #e1b590 85%, #e1b48d 86%, #e0ae89 90%, #e0ae8b 91%, #dfad8a 92%, #deac87 92%, #e0ab89 94%, #dfab86 94%, #dfab86 94%, #dfaa8a 94%, #dfaa88 95%, #deaa85 95%, #dda785 100%) !important;
  /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #73c7d2 0%, #73c8cf 0%, #7ec9ce 5%, #80cacb 7%, #83cacc 7%, #8acac9 11%, #8fcdca 12%, #8fccc7 13%, #97ccc6 16%, #9fcdc2 20%, #a1cdc0 20%, #a4cdbd 24%, #a7cdbe 24%, #a7ccbb 26%, #abcdbc 26%, #adccba 29%, #afcebc 29%, #b1cdb7 31%, #b3cfb9 31%, #b5ccb8 32%, #b7cfb9 34%, #bcceb4 37%, #c2d0b6 40%, #c4d1b3 43%, #cad0b4 44%, #c8d1b2 45%, #cbd2b1 45%, #ccd1b1 47%, #ced3b3 47%, #d2d2ae 51%, #d6d3b0 51%, #d4d1ae 52%, #ddd1ab 59%, #dcd0a8 59%, #ddcea5 62%, #e0cea6 63%, #e0cda5 65%, #decba3 65%, #e2cba2 67%, #e0c79e 70%, #e2c69f 71%, #e2c59d 72%, #e1c49a 73%, #e4c49b 73%, #e4c49b 74%, #e2c299 74%, #e3c098 77%, #e2bf95 77%, #e4be97 78%, #e3be92 78%, #e3bd96 78%, #e1ba91 81%, #e3b993 82%, #e1b58e 84%, #e3b790 84%, #e1b58e 85%, #e1b590 85%, #e1b48d 86%, #e0ae89 90%, #e0ae8b 91%, #dfad8a 92%, #deac87 92%, #e0ab89 94%, #dfab86 94%, #dfab86 94%, #dfaa8a 94%, #dfaa88 95%, #deaa85 95%, #dda785 100%) !important;
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #73c7d2 0%, #73c8cf 0%, #7ec9ce 5%, #80cacb 7%, #83cacc 7%, #8acac9 11%, #8fcdca 12%, #8fccc7 13%, #97ccc6 16%, #9fcdc2 20%, #a1cdc0 20%, #a4cdbd 24%, #a7cdbe 24%, #a7ccbb 26%, #abcdbc 26%, #adccba 29%, #afcebc 29%, #b1cdb7 31%, #b3cfb9 31%, #b5ccb8 32%, #b7cfb9 34%, #bcceb4 37%, #c2d0b6 40%, #c4d1b3 43%, #cad0b4 44%, #c8d1b2 45%, #cbd2b1 45%, #ccd1b1 47%, #ced3b3 47%, #d2d2ae 51%, #d6d3b0 51%, #d4d1ae 52%, #ddd1ab 59%, #dcd0a8 59%, #ddcea5 62%, #e0cea6 63%, #e0cda5 65%, #decba3 65%, #e2cba2 67%, #e0c79e 70%, #e2c69f 71%, #e2c59d 72%, #e1c49a 73%, #e4c49b 73%, #e4c49b 74%, #e2c299 74%, #e3c098 77%, #e2bf95 77%, #e4be97 78%, #e3be92 78%, #e3bd96 78%, #e1ba91 81%, #e3b993 82%, #e1b58e 84%, #e3b790 84%, #e1b58e 85%, #e1b590 85%, #e1b48d 86%, #e0ae89 90%, #e0ae8b 91%, #dfad8a 92%, #deac87 92%, #e0ab89 94%, #dfab86 94%, #dfab86 94%, #dfaa8a 94%, #dfaa88 95%, #deaa85 95%, #dda785 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#73c7d2', endColorstr='#dda785', GradientType=0) !important;
  /* IE6-9 */
  font-family: 'Lato', sans-serif;
  margin: 0;
  overflow-x: hidden;
  color: #000;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

html {
  position: relative;
  min-height: 100%;
  background: #ffffff;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #2b2b2b;
  font-family: 'Circular Std Medium', sans-serif;
  margin: 10px 0;
}

h1 {
  font-family: 'Circular Std Book', sans-serif;
  line-height: 48px;
  font-size: 44px;
}

h2 {
  font-family: 'Circular Std Book', sans-serif;
  line-height: 36px;
  font-size: 36px;
}

h3 {
  font-family: 'Circular Std Book', sans-serif;
  line-height: 1.2em;
  font-size: 20px;
  padding: 0 0 16px 0;
}

h4 {
  font-family: 'Circular Std Bold', sans-serif;
  line-height: 26px;
  font-size: 20px;
}

h5 {
  font-family: 'Circular Std Book', sans-serif;
  line-height: 1.2em;
  font-size: 14px;
}

h6 {
  line-height: 16px;
  font-size: 14px;
}

.dn {
  display: none;
}

.db {
  display: block;
}

.light_op_text {
  color: rgba(255, 255, 255, 0.5);
}

blockquote {
  border-left: 5px solid #ff6849 !important;
  border: 1px solid rgba(120, 130, 140, 0.13);
}

p {
  line-height: 1.6;
  padding: 0;
}

b {
  font-weight: 600;
}

a {
  color: var(--primary-400);
}

a:hover {
  outline: 0;
  text-decoration: none;
}

a:active {
  outline: 0;
  text-decoration: none;
}

a:focus {
  outline: 0;
  text-decoration: none;
}

.clear {
  clear: both;
}

.font-12 {
  font-size: 12px;
}

.text-align-c {
  text-align: center !important;
}

.text-align-r {
  text-align: right !important;
}

hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border-color: rgba(120, 130, 140, 0.13);
}

.b-t {
  border-top: 1px solid rgba(120, 130, 140, 0.13);
}

.b-b {
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
}

.b-l {
  border-left: 1px solid rgba(120, 130, 140, 0.13);
}

.b-r {
  border-right: 1px solid rgba(120, 130, 140, 0.13);
}

.b-all {
  border: 1px solid rgba(120, 130, 140, 0.13);
}

.b-none {
  border: 0px !important;
}

.max-height {
  height: 310px;
  overflow: auto;
}

.p-0 {
  padding: 0px !important;
}

.p-10 {
  padding: 10px !important;
}

.p-20 {
  padding: 20px !important;
}

.p-30 {
  padding: 30px !important;
}

.p-t-5 {
  padding-top: 5px !important;
}

.p-x-0 {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.p-x-8 {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.p-x-10 {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.p-x-20 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.p-x-36 {
  padding-left: 36px !important;
  padding-right: 36px !important;
}

.p-x-40 {
  padding-left: 40px !important;
  padding-right: 40px !important;
}

.p-y-0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.p-y-5 {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}

.p-y-10 {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.p-y-15 {
  padding-top: 15px !important;
  padding-bottom: 15px !important;
}

.p-y-20 {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.p-l-0 {
  padding-left: 0px !important;
}

.p-l-10 {
  padding-left: 10px !important;
}

.p-l-20 {
  padding-left: 20px !important;
}

.p-l-30 {
  padding-left: 30px !important;
}

.p-r-0 {
  padding-right: 0px !important;
}

.p-r-5 {
  padding-right: 5px !important;
}

.p-r-10 {
  padding-right: 10px !important;
}

.p-r-15 {
  padding-right: 15px !important;
}

.p-r-20 {
  padding-right: 20px !important;
}

.p-r-30 {
  padding-right: 30px !important;
}

.p-r-40 {
  padding-right: 40px !important;
}

.p-t-0 {
  padding-top: 0px !important;
}

.p-t-10 {
  padding-top: 10px !important;
}

.p-t-20 {
  padding-top: 20px !important;
}

.p-t-30 {
  padding-top: 30px !important;
}

.p-t-35 {
  padding-top: 35px !important;
}

.p-t-40 {
  padding-top: 40px !important;
}

.p-b-0 {
  padding-bottom: 0px !important;
}

.p-b-10 {
  padding-bottom: 10px !important;
}

.p-b-20 {
  padding-bottom: 20px !important;
}

.p-b-30 {
  padding-bottom: 30px !important;
}

.p-b-40 {
  padding-bottom: 40px !important;
}

.m-0 {
  margin: 0px !important;
}

.m-y-20 {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

.m-l-5 {
  margin-left: 5px !important;
}

.m-l-10 {
  margin-left: 10px !important;
}

.m-l-15 {
  margin-left: 15px !important;
}

.m-l-20 {
  margin-left: 20px !important;
}

.m-l-30 {
  margin-left: 30px !important;
}

.m-l-40 {
  margin-left: 40px !important;
}

.m-l-50 {
  margin-left: 50px !important;
}

.m-r-5 {
  margin-right: 5px !important;
}

.m-r-10 {
  margin-right: 10px !important;
}

.m-r-15 {
  margin-right: 15px !important;
}

.m-r-20 {
  margin-right: 20px !important;
}

.m-r-30 {
  margin-right: 30px !important;
}

.m-r-40 {
  margin-right: 40px !important;
}

.m-t-5 {
  margin-top: 5px !important;
}

.m-t-0 {
  margin-top: 0px !important;
}

.m-t-10 {
  margin-top: 10px !important;
}

.m-t-15 {
  margin-top: 15px !important;
}

.m-t-20 {
  margin-top: 20px !important;
}

.m-t-25 {
  margin-top: 25px !important;
}

.m-t-30 {
  margin-top: 30px !important;
}

.m-t-40 {
  margin-top: 40px !important;
}

.m-b-0 {
  margin-bottom: 0px !important;
}

.m-b-5 {
  margin-bottom: 5px !important;
}

.m-b-10 {
  margin-bottom: 10px !important;
}

.m-b-15 {
  margin-bottom: 15px !important;
}

.m-b-20 {
  margin-bottom: 20px !important;
}

.m-b-30 {
  margin-bottom: 30px !important;
}

.m-b-40 {
  margin-bottom: 40px !important;
}

.m-x-20 {
  margin-left: 20px !important;
  margin-right: 20px !important;
}

.vt {
  vertical-align: top;
}

.vb {
  vertical-align: bottom;
}

.font-extra-bold {
  font-weight: 700;
}

.font-bold {
  font-weight: 600;
}

.font-normal {
  font-weight: normal;
}

.font-light {
  font-weight: 300;
}

.pull-in {
  margin-left: -15px;
  margin-right: -15px;
}

.width-auto {
  width: auto !important;
}

.br-8 {
  border-radius: 8px !important;
}

.b-0 {
  border: none !important;
}

.b-r-1 {
  border-right: 1px solid rgba(120, 130, 140, 0.13);
}

.b-c-geyser {
  border-color: #DFE5E5;
}

.vertical-middle {
  vertical-align: middle;
}

.bx-shadow {
  -moz-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
}

.mx-box {
  max-height: 380px;
  min-height: 380px;
}

.thumb-sm {
  height: 32px;
  width: 32px;
}

.thumb-md {
  height: 48px;
  width: 48px;
}

.thumb-lg {
  height: 88px;
  width: 88px;
}

.txt-oflo {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.get-code {
  color: #2b2b2b;
  cursor: pointer;
  border-radius: 100%;
  background: #ffffff;
  padding: 4px 5px;
  font-size: 10px;
  margin: 0 5px;
  vertical-align: middle;
}

/* Badge */
.badge {
  text-transform: uppercase;
  font-weight: 600;
  padding: 3px 5px;
  font-size: 12px;
  margin-top: 1px;
  background-color: #fec107;
}

.badge-xs {
  font-size: 9px;
}

.badge-xs,
.badge-sm {
  -webkit-transform: translate(0, -2px);
  -ms-transform: translate(0, -2px);
  -o-transform: translate(0, -2px);
  transform: translate(0, -2px);
}

.badge-lg {
  padding: 3px 8px;
  font-size: 10px;
  line-height: 18px;
  border-radius: 100px;
}

.badge-success {
  background-color: var(--success-400);
}

.badge-primary {
  background-color: var(--primary-400);
}

.badge-info {
  background-color: #03a9f3;
}

.badge-warning {
  background-color: var(--warning-400);
}

.badge-danger {
  background-color: var(--danger-400);
}

.badge-purple {
  background-color: #9675ce;
}

.badge-red {
  background-color: #fb3a3a;
}

.badge-inverse {
  background-color: #4c5667;
}

/*notify*/
.notify {
  position: relative;
  margin-top: -30px;
}

.notify .heartbit {
  position: absolute;
  top: -20px;
  right: -16px;
  height: 25px;
  width: 25px;
  z-index: 10;
  border: 5px solid #EA5836;
  border-radius: 70px;
  -moz-animation: heartbit 1s ease-out;
  -moz-animation-iteration-count: infinite;
  -o-animation: heartbit 1s ease-out;
  -o-animation-iteration-count: infinite;
  -webkit-animation: heartbit 1s ease-out;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

.notify .point {
  width: 6px;
  height: 6px;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  background-color: #EA5836;
  position: absolute;
  right: -6px;
  top: -10px;
}

@-moz-keyframes heartbit {
  0% {
    -moz-transform: scale(0);
    opacity: 0.0;
  }

  25% {
    -moz-transform: scale(0.1);
    opacity: 0.1;
  }

  50% {
    -moz-transform: scale(0.5);
    opacity: 0.3;
  }

  75% {
    -moz-transform: scale(0.8);
    opacity: 0.5;
  }

  100% {
    -moz-transform: scale(1);
    opacity: 0.0;
  }
}

@-webkit-keyframes heartbit {
  0% {
    -webkit-transform: scale(0);
    opacity: 0.0;
  }

  25% {
    -webkit-transform: scale(0.1);
    opacity: 0.1;
  }

  50% {
    -webkit-transform: scale(0.5);
    opacity: 0.3;
  }

  75% {
    -webkit-transform: scale(0.8);
    opacity: 0.5;
  }

  100% {
    -webkit-transform: scale(1);
    opacity: 0.0;
  }
}

/* Text colors */
.text-white {
  color: #ffffff;
}

.text-danger {
  color: var(--danger-500);
}

.text-muted {
  color: var(--grey-700);
}

.text-warning {
  color: var(--warning-500);
}

.text-success {
  color: var(--success-500);
}

.text-info {
  color: #03a9f3;
}

.text-info-dark {
  color: #3E6B89;
}

.text-inverse {
  color: #4c5667;
}

.text-blue {
  color: #02bec9;
}

.text-purple {
  color: #9675ce;
}

.text-primary {
  color: var(--primary-400);
}

.text-primary-dark {
  color: var(--primary-500);
}

.text-megna {
  color: #01c0c8;
}

.text-dark {
  color: #686868 !important;
}

.text-black {
  color: #2b2b2b;
}

/* Background colors */
.bg-primary {
  background-color: var(--primary-400) !important;
}

.bg-success {
  background-color: #15D19B !important;
}

.bg-info {
  background-color: #03a9f3 !important;
}

.bg-info-light {
  background-color: #D7E9F5 !important;
}

.bg-warning {
  background-color: #fec107 !important;
}

.bg-orange {
  background: #FF8A00 !important;
}

.bg-danger {
  background-color: #EA5836 !important;
}

.bg-theme {
  background-color: #ff6849 !important;
}

.bg-theme-dark {
  background-color: #4F5467 !important;
}

.bg-inverse {
  background-color: #4c5667 !important;
}

.bg-purple {
  background-color: #9675ce !important;
}

.bg-white {
  background-color: #ffffff !important;
}

.bg-light-grey {
  background-color: var(--grey-50) !important;
}

/* Borders */
.border-blue-light {
  border: 2px solid #CDE6EC;
}

/* Labels */
.label {
  letter-spacing: 0.05em;
  border-radius: 60px;
  padding: 4px 16px 3px;
  font-weight: 500;
}

.label-rounded,
.label-rouded {
  border-radius: 60px;
  padding: 4px 16px 3px;
  font-weight: 500;
}

.label-custom {
  background-color: #01c0c8;
}

.label-success {
  background-color: #15D19B;
}

.label-info {
  background-color: #03a9f3;
}

.label-warning {
  background-color: #fec107;
}

.label-danger {
  background-color: #EA5836;
}

.label-megna {
  background-color: #01c0c8;
}

.label-primary {
  background-color: var(--primary-400);
}

.label-purple {
  background-color: #9675ce;
}

.label-red {
  background-color: #fb3a3a;
}

.label-inverse {
  background-color: #4c5667;
}

.label-white {
  background-color: #ffffff;
}

.label-default {
  background-color: #e4e7ea;
}

.label-primary.label-outline {
  background: none;
  border: 1px solid var(--primary-400);
  color: var(--primary-400);
}

.beta-label {
  font-size: 11px;
  background-color: #4EACE5;
  border-radius: 5px;
  padding: 2px 4px;
  color: #FFFFFF;

}

/*Bootstrap overight*/
.dropdown-menu {
  border: 1px solid rgba(120, 130, 140, 0.13);
  border-radius: 6px;
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.07) !important;
  -webkit-box-shadow: 0px !important;
  -moz-box-shadow: 0px !important;
  padding-bottom: 8px;
  margin-top: 4px;
}

.dropdown-menu>li>a {
  padding: 6px 12px;
}

.dropdown-menu>li>a:focus,
.dropdown-menu>li>a:hover {
  background: #F4F4F5;
}

.navbar-top-links .progress {
  margin-bottom: 6px;
}

label {
  font-weight: 500;
}

.btn {
  border-radius: 6px;
  font-family: 'Lexend', 'sans-serif';
}

.form-control {
  background-color: #ffffff;
  border: 1px solid #e4e7ea;
  border-radius: 6px;
  box-shadow: none;
  color: #000;
  height: 34px;
  max-width: 100%;
  padding: 7px 12px;
  transition: all 300ms linear 0s;
}

.form-control:focus {
  box-shadow: none;
  border-color: #2b2b2b;
}

.form-group-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.input-sm {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
}

.input-lg {
  height: 44px;
  padding: 5px 10px;
  font-size: 18px;
}

.bootstrap-tagsinput {
  border: 1px solid #e4e7ea;
  border-radius: 0px;
  box-shadow: none;
  display: block;
  padding: 7px 12px;
}

.bootstrap-touchspin .input-group-btn-vertical>.btn {
  padding: 9px 10px;
}

.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-up,
.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-down {
  border-radius: 0px;
}

.input-group-btn .btn {
  padding: 8px 12px;
}

.form-horizontal .form-group {
  margin-left: -7.5px;
  margin-right: -7.5px;
  margin-bottom: 25px;
}

.form-group {
  margin-bottom: 25px;
}

.select2-container-multi .select2-choices {
  border: 1px solid #e4e7ea;
}

.list-group-item,
.list-group-item:first-child,
.list-group-item:last-child {
  border-radius: 4px;
  /*border-color: transparent;*/
  border-bottom: 1px solid #DFE5E5;
  margin-bottom: 2px;
  background-color: #fff;
}

.list-group-item.active,
.list-group-item.active:focus,
.list-group-item.active:hover {
  background: #03a9f3;
  border-color: #03a9f3;
}

.list-task .list-group-item,
.list-task .list-group-item:first-child {
  border-radius: 0px;
  border: 0px;
}

.list-task .list-group-item:last-child {
  border-radius: 0px;
  border: 0px;
}

.media {
  border: 1px solid rgba(120, 130, 140, 0.13);
  margin-bottom: 10px;
  padding: 15px;
}

.media .media-heading {
  font-weight: 500;
}

.well,
pre {
  background: #ffffff;
  border-radius: 0px;
}

.nav-tabs>li>a {
  border-radius: 4px 4px 0 0;
  color: #2b2b2b;
}

.nav-tabs>li>a:hover,
.nav-tabs>li>a:focus {
  background: #ffffff;
}

.modal-content {
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.modal-md {
  width: 720px;
}

.alert {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 0 !important;
  text-align: left;
  font-family: 'Lexend', 'sans-serif';
}

.alert svg {
  width: 20px;
}

.alert-success {
  background-color: var(--success-50);
  border-color: var(--success-100);
  color: var(--success-400);
}

.alert-success svg path {
  stroke: var(--success-400);
}

.alert-warning {
  background-color: var(--warning-50);
  border-color: var(--warning-300);
  color: var(--warning-400);
}

.alert-warning svg path {
  stroke: var(--warning-400);
}

.alert-danger {
  background-color: var(--danger-50);
  border-color: var(--danger-300);
  color: var(--danger-400);
}

.alert-danger svg path {
  stroke: var(--danger-400);
}

.alert-dismissable .close, .alert-dismissible .close {
  top: -6px;
  right: 0;
}

.close {
  font-weight: 400;
  font-size: 28px;
}

.carousel-control {
  width: 8%;
}

.carousel-control span {
  position: absolute;
  top: 50%;
  /* pushes the icon in the middle of the height */
  z-index: 5;
  display: inline-block;
  font-size: 30px;
}

.popover {
  border-radius: 0px;
}

.popover-title {
  padding: 5px 14px;
}

.container-fluid {
  padding-left: 25px;
  padding-right: 25px;
  padding-bottom: 15px;
}

.col-lg-1,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-md-1,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-xs-1,
.col-xs-10,
.col-xs-11,
.col-xs-12,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9 {
  padding-left: 7.5px;
  padding-right: 7.5px;
}

.row {
  margin-right: -7.5px;
  margin-left: -7.5px;
}

.btn-group-vertical>.btn:first-child:not(:last-child),
.btn-group-vertical>.btn:last-child:not(:first-child) {
  border-radius: 0px;
}

.table-responsive {
  overflow-y: hidden;
}

/* Make view-billing table have a top scrollbar always shown */
.top-scroll-wrapper {
  overflow-x: scroll;
  overflow-y: hidden;
  height: 20px;
}

.top-scroll {
  height: 20px;
}

.table-responsive::-webkit-scrollbar,
.top-scroll-wrapper::-webkit-scrollbar {
  -webkit-appearance: none;
}

.table-responsive::-webkit-scrollbar:horizontal,
.top-scroll-wrapper::-webkit-scrollbar:horizontal {
  height: 12px;
}

.table-responsive::-webkit-scrollbar-thumb,
.top-scroll-wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, .5);
  border-radius: 10px;
  border: 2px solid #ffffff;
}

.table-responsive::-webkit-scrollbar-track,
.top-scroll-wrapper::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #ffffff;
}

.footable-page-arrow.disabled a {
  border: 0;
}

.pagination>li>a,
.pagination>li>span {
  color: #2b2b2b;
  margin: 0 1px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-family: 'Lexend', 'sans-serif';
}

.pagination>li:first-child>a, .pagination>li:first-child>span, .pagination>li:last-child>a, .pagination>li:last-child>span {
  border-radius: 6px;
}

.pagination>li>a:hover,
.pagination>li>span:hover,
.pagination>li>a:focus,
.pagination>li>span:focus {
  background-color: #E4E4E7;
  color: #000;
}

.pagination-split li {
  margin-left: 5px;
  display: inline-block;
  float: left;
}

.pagination-split li:first-child {
  margin-left: 0;
}

.pagination-split li a {
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
  border-radius: 0px;
}

.pagination>.active>a,
.pagination>.active>span,
.pagination>.active>a:hover,
.pagination>.active>span:hover,
.pagination>.active>a:focus,
.pagination>.active>span:focus {
  background: none;
  border: 1px solid #E4E4E7;
  -moz-border-radius: 6px;
  -webkit-border-radius: 6px;
  font-weight: 500;
  border-radius: 6px;
  color: #000;
}

.pager li>a,
.pager li>span {
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
  border-radius: 0px;
  color: #2b2b2b;
}

.table-box {
  display: table;
  width: 100%;
}

.cell {
  display: table-cell;
  vertical-align: middle;
}

.jqstooltip {
  width: auto !important;
  height: auto !important;
}

#wrapper {
  width: 100%;
  margin: 0 auto;

}

#page-wrapper {
  padding-bottom: 30px;
  padding-left: 290px;
}

#page-wrapper .modal {
  z-index: 100000;
}

.footer {
  bottom: 0;
  color: #58666e;
  left: 0px;
  padding: 20px 30px;
  position: absolute;
  width: 100%;
  right: 0;
  border-radius: 8px;
}

.modal-open {
  overflow-y: auto;
  padding-right: 0px !important;
}

/* End page header */

.bg-title {
  overflow: hidden;
  padding: 15px 15px 10px;
  margin-bottom: 5px;
  margin-left: -25.5px;
  margin-right: -15px;
}

@media (max-width: 767px) {
  .bg-title {
    padding: 0 15px 0;
  }
}

@media (min-width: 768px) {
  .container-fluid>.bg-title:first-child {
    margin-top: 80px;
  }
}

.bg-title h4 {
  color: #55676F;
  font-weight: 600;
  margin-top: 6px;
}

.bg-title .breadcrumb {
  background: none;
  margin-bottom: 0px;
  float: right;
  padding: 0;
  margin-top: 8px;
}

.bg-title .breadcrumb a {
  color: rgba(0, 0, 0, 0.5);
}

.bg-title .breadcrumb a:hover {
  color: #000000;
}

.bg-title .breadcrumb .active {
  color: #ff6849;
}

.logo b {
  height: 60px;
  display: inline-block;
  width: 60px;
  line-height: 60px;
  text-align: center;
}

.logo i {
  color: #ffffff;
}

.logo2 img {
  padding: 4px;
  max-height: 58px;
  max-width: 210px;
}

.top-left-part {
  float: left;
}

.top-left-part a {
  color: #ffffff;
  font-size: 18px;
  padding-left: 0px;
  text-transform: uppercase;
}

.navbar-header {
  width: 100%;
  text-align: center;
}

.navbar-default {
  border: 0px;
  background: none;
}

.navbar-top-links {
  margin-right: 0;
}

.navbar-top-links .badge {
  position: absolute;
  right: 6px;
  top: 15px;
}

.navbar-top-links>li {
  float: left;
}

.navbar-top-links>li>a:hover {
  background: rgba(0, 0, 0, 0.1);
}

.navbar-top-links>li>a:focus {
  background: rgba(0, 0, 0, 0);
}

.nav .open>a,
.nav .open>a:focus,
.nav .open>a:hover {
  background: rgba(255, 255, 255, 0.2);
}

.navbar-top-links .dropdown-menu li {
  display: block;
}

.navbar-top-links .dropdown-menu li:last-child {
  margin-right: 0;
}

.navbar-top-links .dropdown-menu li a div {
  white-space: normal;
}

.navbar-top-links .dropdown-messages,
.navbar-top-links .dropdown-tasks,
.navbar-top-links .dropdown-alerts {
  width: 310px;
  min-width: 0;
}

.navbar-top-links .dropdown-messages {
  margin-left: 5px;
}

.navbar-top-links .dropdown-tasks {
  margin-left: -59px;
}

.navbar-top-links .dropdown-alerts {
  margin-left: -123px;
}

.navbar-top-links .dropdown-user {
  right: 0;
  left: auto;
}

.navbar-header .navbar-toggle {
  float: none;
  padding: 0 15px;
  line-height: 60px;
  border: 0px;
  color: rgba(255, 255, 255, 0.5);
  margin: 0px;
  display: inline-block;
  border-radius: 0px;
}

.navbar-header .navbar-toggle:hover,
.navbar-header .navbar-toggle:focus {
  background: rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

/* custom */
#side-menu .home-item {
  float: left;
  position: sticky;
}

@media (max-width: 767px) {
  .top-org-name {
    display: none;
  }

  #side-menu .home-item {
    float: none;
    position: relative;
    left: 0;
  }

  #side-menu .hide-menu {
    float: left;
  }

  .navbar-header .navbar-toggle {
    float: left;
  }
}

.navbar-header i.ti-menu,
.navbar-header i.ti-close {
  font-size: 2em;
  color: white;
  vertical-align: middle;
}

/*Search*/
.app-search {
  position: relative;
  margin: 0px;
}

.app-search a {
  position: absolute;
  top: 20px;
  right: 10px;
  color: #4c5667;
}

.app-search .form-control,
.app-search .form-control:focus {
  border: none;
  font-size: 13px;
  color: #4c5667;
  padding-left: 20px;
  padding-right: 40px;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: none;
  height: 30px;
  font-weight: 600;
  width: 180px;
  display: inline-block;
  line-height: 30px;
  margin-top: 15px;
  border-radius: 40px;
  transition: 0.5s ease-out;
}

.app-search .form-control::-moz-placeholder {
  color: #4c5667;
  opacity: 0.5;
}

.app-search .form-control::-webkit-input-placeholder {
  color: #4c5667;
  opacity: 0.5;
}

.app-search .form-control::-ms-placeholder {
  color: #4c5667;
  opacity: 0.5;
}

.nav-small-cap {
  color: #a6afbb;
  cursor: default;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 13px;
  letter-spacing: 0.035em;
  padding: 12px 15px !important;
  pointer-events: none;
  margin: 20px 0 0 -15px;
  display: none !important;
}

.profile-pic {
  padding: 0px 20px;
  line-height: 50px;
}

.profile-pic img {
  margin-right: 10px;
}

.drop-title {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  color: #2b2b2b;
  font-size: 15px;
  font-weight: 600;
  padding: 11px 20px 15px;
}

.btn-outline {
  color: inherit;
  background-color: transparent;
  transition: all .5s;
}

.btn-rounded {
  border-radius: 60px;
}

.btn-custom,
.btn-custom.disabled {
  background: #ff6849;
  border: 1px solid #ff6849;
  color: #ffffff;
}

.btn-custom:hover,
.btn-custom.disabled:hover,
.btn-custom:focus,
.btn-custom.disabled:focus,
.btn-custom.focus,
.btn-custom.disabled.focus {
  background: #ff6849;
  opacity: 0.9;
  color: #ffffff;
  border: 1px solid #ff6849;
}

.btn-primary,
.btn-primary.disabled {
  border: none;
  background: #4074D2;
  border: 1px solid #4074D2;
  transition: 0.2s;
}

.btn-primary:hover,
.btn-primary.disabled:hover,
.btn-primary:focus,
.btn-primary.disabled:focus,
.btn-primary.focus,
.btn-primary.disabled.focus {
  background: #355FC0;
  border-color: #355FC0;
  transition: 0.2s;
}

.btn-tertiary,
.btn-tertiary.disabled {
  background: #E4E4E7;
  color: #000;
  transition: 0.2s;
}

.btn-tertiary:hover,
.btn-tertiary.disabled:hover,
.btn-tertiary:focus,
.btn-tertiary.disabled:focus,
.btn-tertiary.focus,
.btn-tertiary.disabled.focus {
  background: #D4D4D8;
  transition: 0.2s;

}

.btn-success,
.btn-success.active,
.btn-success.disabled {
  background: #15803D;
  border: 1px solid #15803D;
}

.btn-success:hover,
.btn-success.disabled:hover,
.btn-success:focus,
.btn-success.disabled:focus,
.btn-success.focus,
.btn-success.disabled.focus,
.btn-success[disabled]:hover {
  background: #15803D;
  opacity: 0.9;
  border: 1px solid #15803D;
}

.btn-info,
.btn-info.disabled {
  background: #03a9f3;
  border: 1px solid #03a9f3;
}

.btn-info:hover,
.btn-info.disabled:hover,
.btn-info:focus,
.btn-info.disabled:focus,
.btn-info.focus,
.btn-info.disabled.focus {
  background: #03a9f3;
  opacity: 0.9;
  border: 1px solid #03a9f3;
}

.btn-warning,
.btn-warning.disabled {
  background: #fec107;
  border: 1px solid #fec107;
}

.btn-warning:hover,
.btn-warning.disabled:hover,
.btn-warning:focus,
.btn-warning.disabled:focus,
.btn-warning.focus,
.btn-warning.disabled.focus {
  background: #fec107;
  opacity: 0.9;
  border: 1px solid #fec107;
}

.btn-danger,
.btn-danger.disabled {
  background: #EA5836;
  border: 1px solid #EA5836;
}

.btn-danger:hover,
.btn-danger.disabled:hover,
.btn-danger:focus,
.btn-danger.disabled:focus,
.btn-danger.focus,
.btn-danger.disabled.focus {
  background: #EA5836;
  opacity: 0.9;
  border: 1px solid #EA5836;
}

.btn-default,
.btn-default.disabled {
  background: #fff;
  border: 1px solid #d1d5db;
  transition: 0.2s;
  color: #000;
}

.btn-default:hover,
.btn-default.disabled:hover,
.btn-default:focus,
.btn-default.disabled:focus,
.btn-default.focus,
.btn-default.disabled.focus {
  background: #F4F4F5;
  border: 1px solid #d1d5db;
  opacity: 0.9;
}

.btn-default.btn-outline {
  background-color: #ffffff;
}

.btn-default.btn-outline:hover,
.btn-default.btn-outline:focus,
.btn-default.btn-outline.focus {
  background: #F4F4F5;
}

.btn-primary.btn-outline {
  color: #112935;
  background-color: transparent;
  box-shadow: none;
  border: 1px solid #D4D4D8;
  ;
}

.btn-primary.btn-outline:hover,
.btn-primary.btn-outline:focus,
.btn-primary.btn-outline.focus {
  border: 1px solid #A1A1AA;
}

.btn-success.btn-outline {
  color: #15D19B;
  background-color: transparent;
}

.btn-success.btn-outline:hover,
.btn-success.btn-outline:focus,
.btn-success.btn-outline.focus {
  background: #15D19B;
  color: #ffffff;
}

.btn-info.btn-outline {
  color: #03a9f3;
  background-color: transparent;
}

.btn-info.btn-outline:hover,
.btn-info.btn-outline:focus,
.btn-info.btn-outline.focus {
  background: #03a9f3;
  color: #ffffff;
}

.btn-warning.btn-outline {
  color: #fec107;
  background-color: transparent;
}

.btn-warning.btn-outline:hover,
.btn-warning.btn-outline:focus,
.btn-warning.btn-outline.focus {
  background: #fec107;
  color: #ffffff;
}

.btn-danger.btn-outline {
  color: #EA5836;
  background-color: transparent;
}

.btn-danger.btn-outline:hover,
.btn-danger.btn-outline:focus,
.btn-danger.btn-outline.focus {
  background: #EA5836;
  color: #ffffff;
}

.button-box .btn {
  margin: 0 8px 8px 0px;
}

.btn-success.btn-outline:hover,
.btn-info.btn-outline:hover,
.btn-warning.btn-outline:hover,
.btn-danger.btn-outline:hover {
  color: white;
  opacity: 1;
}

.btn-label {
  display: inline-block;
  margin: -6px 0px -6px -12px;
  padding: 7px 15px;
}

.btn-facebook {
  color: #ffffff !important;
  background-color: #3b5998 !important;
}

.btn-twitter {
  color: #ffffff !important;
  background-color: #55acee !important;
}

.btn-linkedin {
  color: #ffffff !important;
  background-color: #007bb6 !important;
}

.btn-dribbble {
  color: #ffffff !important;
  background-color: #ea4c89 !important;
}

.btn-googleplus {
  color: #ffffff !important;
  background-color: #dd4b39 !important;
}

.btn-instagram {
  color: #ffffff !important;
  background-color: #3f729b !important;
}

.btn-pinterest {
  color: #ffffff !important;
  background-color: #cb2027 !important;
}

.btn-dropbox {
  color: #ffffff !important;
  background-color: #007ee5 !important;
}

.btn-flickr {
  color: #ffffff !important;
  background-color: #ff0084 !important;
}

.btn-tumblr {
  color: #ffffff !important;
  background-color: #32506d !important;
}

.btn-skype {
  color: #ffffff !important;
  background-color: #00aff0 !important;
}

.btn-youtube {
  color: #ffffff !important;
  background-color: #bb0000 !important;
}

.btn-github {
  color: #ffffff !important;
  background-color: #171515 !important;
}

/*Button Transtions*/
.btn-primary.active.focus,
.btn-primary.active:focus,
.btn-primary.active:hover,
.btn-primary.focus:active,
.btn-primary:active:focus,
.btn-primary:active:hover,
.open>.dropdown-toggle.btn-primary.focus,
.open>.dropdown-toggle.btn-primary:focus,
.open>.dropdown-toggle.btn-primary:hover,
.btn-primary.focus,
.btn-primary:focus {
  background-color: #446EE2;
  border: 1px solid #446EE2;
}

.btn-success.active.focus,
.btn-success.active:focus,
.btn-success.active:hover,
.btn-success.focus:active,
.btn-success:active:focus,
.btn-success:active:hover,
.open>.dropdown-toggle.btn-success.focus,
.open>.dropdown-toggle.btn-success:focus,
.open>.dropdown-toggle.btn-success:hover,
.btn-success.focus,
.btn-success:focus {
  background-color: #15D19B;
  border: 1px solid #15D19B;
}

.btn-warning.active.focus,
.btn-warning.active:focus,
.btn-warning.active:hover,
.btn-warning.focus:active,
.btn-warning:active:focus,
.btn-warning:active:hover,
.open>.dropdown-toggle.btn-warning.focus,
.open>.dropdown-toggle.btn-warning:focus,
.open>.dropdown-toggle.btn-warning:hover,
.btn-warning.focus,
.btn-warning:focus {
  background-color: #fec107;
  border: 1px solid #fec107;
}

.btn-danger.active.focus,
.btn-danger.active:focus,
.btn-danger.active:hover,
.btn-danger.focus:active,
.btn-danger:active:focus,
.btn-danger:active:hover,
.open>.dropdown-toggle.btn-danger.focus,
.open>.dropdown-toggle.btn-danger:focus,
.open>.dropdown-toggle.btn-danger:hover,
.btn-danger.focus,
.btn-danger:focus {
  background-color: #EA5836;
  border: 1px solid #EA5836;
}

.btn-inverse,
.btn-inverse:hover,
.btn-inverse:focus,
.btn-inverse:active,
.btn-inverse.active,
.btn-inverse.focus,
.btn-inverse:active,
.btn-inverse:focus,
.btn-inverse:hover,
.open>.dropdown-toggle.btn-inverse {
  background-color: #4c5667;
  border: 1px solid #4c5667;
  color: #ffffff;
}

.btn-xlg {
  font-size: 20px;
  line-height: 24px;
  padding: 18px 60px;
}

.chat {
  margin: 0;
  padding: 0;
  list-style: none;
}

.chat li {
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px dotted rgba(120, 130, 140, 0.13);
}

.chat li.left .chat-body {
  margin-left: 60px;
}

.chat li.right .chat-body {
  margin-right: 60px;
}

.chat li .chat-body p {
  margin: 0;
}

.panel .slidedown .glyphicon,
.chat .glyphicon {
  margin-right: 5px;
}

.chat-panel .panel-body {
  height: 350px;
  overflow-y: scroll;
}

.login-panel {
  margin-top: 25%;
}

.flot-chart {
  display: block;
  height: 400px;
}

.flot-chart-content {
  width: 100%;
  height: 100%;
}

table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc,
table.dataTable thead .sorting_asc_disabled,
table.dataTable thead .sorting_desc_disabled {
  background: transparent;
}

table.dataTable thead .sorting_asc:after {
  content: "\f0de";
  float: right;
  font-family: fontawesome;
}

table.dataTable thead .sorting_desc:after {
  content: "\f0dd";
  float: right;
  font-family: fontawesome;
}

table.dataTable thead .sorting:after {
  content: "\f0dc";
  float: right;
  font-family: fontawesome;
  color: rgba(50, 50, 50, 0.5);
}

.btn-circle {
  width: 30px;
  height: 30px;
  padding: 6px 0;
  border-radius: 15px;
  text-align: center;
  font-size: 12px;
  line-height: 1.428571429;
}

.btn-circle.btn-lg {
  width: 50px;
  height: 50px;
  padding: 10px 16px;
  border-radius: 25px;
  font-size: 18px;
  line-height: 1.33;
}

.btn-circle.btn-xl {
  width: 70px;
  height: 70px;
  padding: 10px 16px;
  border-radius: 35px;
  font-size: 24px;
  line-height: 1.33;
}

.show-grid [class^="col-"] {
  padding-top: 10px;
  padding-bottom: 10px;
  border: 1px solid rgba(120, 130, 140, 0.13);
  background-color: #f7fafc;
}

.show-grid {
  margin: 15px 0;
}

.huge {
  font-size: 40px;
}

.white-box {
  background: #ffffff;
  padding: 25px;
  margin-bottom: 15px;
  border-radius: 8px;
}

.white-box .box-title {
  margin: 0px 0px 12px;
  font-family: "Circular Std Bold", sans-serif;
  font-weight: 600;
}

.panel {
  border-radius: 8px;
  margin-bottom: 15px;
  border: 0px;
  background-color: transparent;
}

.panel .panel-heading {
  border-radius: 8px;
  font-weight: 600;
  text-transform: uppercase;
  padding: 20px 25px;
}

.panel .panel-heading .panel-title {
  font-size: 14px;
  color: #2b2b2b;
}

.panel .panel-heading .panel-notifications-title {
  font-size: 18px;
  color: #2b2b2b;
}

.panel .panel-heading a i {
  font-size: 12px;
  margin-left: 8px;
}

.panel .panel-action {
  float: right;
}

.panel .panel-action a {
  opacity: 0.5;
}

.panel .panel-action a:hover {
  opacity: 1;
}

.panel .panel-body {
  padding: 25px;
}

.panel .panel-body:first-child h3 {
  margin-top: 0px;
  font-weight: 600;
  font-family: 'Circular Std Bold', sans-serif;
  font-size: 14px;
  text-transform: uppercase;
}

.panel .panel-footer {
  background: #ffffff;
  border-radius: 0px;
  padding: 20px 25px;
}

.panel-green,
.panel-success {
  border-color: #15D19B;
}

.panel-green .panel-heading,
.panel-success .panel-heading {
  border-color: #15D19B;
  color: white;
  background-color: #15D19B;
}

.panel-green .panel-heading a,
.panel-success .panel-heading a {
  color: #ffffff;
}

.panel-green .panel-heading a:hover,
.panel-success .panel-heading a:hover {
  color: rgba(255, 255, 255, 0.5);
}

.panel-green a,
.panel-success a {
  color: #15D19B;
}

.panel-green a:hover,
.panel-success a:hover {
  color: #007658;
}

.panel-black,
.panel-inverse {
  border-color: #4c5667;
}

.panel-black .panel-heading,
.panel-inverse .panel-heading {
  border-color: #4c5667;
  color: white;
  background-color: #4c5667;
}

.panel-black .panel-heading a,
.panel-inverse .panel-heading a {
  color: #ffffff;
}

.panel-black .panel-heading a:hover,
.panel-inverse .panel-heading a:hover {
  color: rgba(255, 255, 255, 0.5);
}

.panel-black a,
.panel-inverse a {
  color: #4c5667;
}

.panel-black a:hover,
.panel-inverse a:hover {
  color: #2c313b;
}

.panel-darkblue,
.panel-primary {
  border-color: #4c5aa0;
  padding: 25px;
}

.panel-darkblue .panel-heading,
.panel-primary .panel-heading {
  border-color: #446EE2;
  color: white;
  background-color: #446EE2;
}

.panel-darkblue .panel-heading a,
.panel-primary .panel-heading a {
  color: #ffffff;
}

.panel-darkblue .panel-heading a:hover,
.panel-primary .panel-heading a:hover {
  color: rgba(255, 255, 255, 0.5);
}

.panel-darkblue a,
.panel-primary a {
  color: #446EE2;
}

.certification-implementation-tips a {
  text-decoration: underline;
}

.certification-implementation-tips.text-primary {
  color: #55676F;
}

.certification-implementation-tips label {
  font-weight: bold;
}

.panel-blue,
.panel-info {
  border-color: #03a9f3;
}

.panel-blue .panel-heading,
.panel-info .panel-heading {
  border-color: #03a9f3;
  color: white;
  background-color: #03a9f3;
}

.panel-blue .panel-heading a,
.panel-info .panel-heading a {
  color: #ffffff;
}

.panel-blue .panel-heading a:hover,
.panel-info .panel-heading a:hover {
  color: rgba(255, 255, 255, 0.5);
}

.panel-blue a,
.panel-info a {
  color: #03a9f3;
}

.panel-blue a:hover,
.panel-info a:hover {
  color: #0274a7;
}

.panel-red,
.panel-danger {
  border-color: #EA5836;
}

.panel-red .panel-heading,
.panel-danger .panel-heading {
  border-color: #EA5836;
  color: white;
  background-color: #EA5836;
}

.panel-red .panel-heading a,
.panel-danger .panel-heading a {
  color: #ffffff;
}

.panel-red .panel-heading a:hover,
.panel-danger .panel-heading a:hover {
  color: rgba(255, 255, 255, 0.5);
}

.panel-red a,
.panel-danger a {
  color: #EA5836;
}

.panel-red a:hover,
.panel-danger a:hover {
  color: #f95c2e;
}

.panel-yellow,
.panel-warning {
  border-color: #fec107;
}

.panel-yellow .panel-heading,
.panel-warning .panel-heading {
  border-color: #fec107;
  color: white;
  background-color: #fec107;
}

.panel-yellow .panel-heading a,
.panel-warning .panel-heading a {
  color: #ffffff;
}

.panel-yellow .panel-heading a:hover,
.panel-warning .panel-heading a:hover {
  color: rgba(255, 255, 255, 0.5);
}

.panel-yellow a:not([class^='text-']),
.panel-warning a:not([class^='text-']) {
  color: #fec107;
}

.panel-yellow a:not([class^='text-']):hover,
.panel-warning a:not([class^='text-']):hover {
  color: #b88b01;
}

.panel-white,
.panel-default {
  border-color: rgba(120, 130, 140, 0.13);
}

.panel-white .panel-heading,
.panel-default .panel-heading {
  color: #2b2b2b;
  background-color: #ffffff;
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
}

.panel-white .panel-body,
.panel-default .panel-body {
  color: #2b2b2b;
}

.panel-white .panel-action a,
.panel-default .panel-action a {
  color: #2b2b2b;
  opacity: 0.5;
}

.panel-white .panel-action a:hover,
.panel-default .panel-action a:hover {
  opacity: 1;
  color: #2b2b2b;
}

.panel-white .panel-footer,
.panel-default .panel-footer {
  background: #ffffff;
  color: #2b2b2b;
  border-top: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-info {
  border-color: #03a9f3;
}

.full-panel-info .panel-heading {
  border-color: #03a9f3;
  color: white;
  background-color: #03a9f3;
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-info .panel-body {
  background: #03a9f3;
  color: #ffffff;
}

.full-panel-info .panel-footer {
  background: #03a9f3;
  color: #ffffff;
  border-top: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-info a {
  color: #03a9f3;
}

.full-panel-info a:hover {
  color: #0274a7;
}

.full-panel-warning {
  border-color: #fec107;
}

.full-panel-warning .panel-heading {
  border-color: #fec107;
  color: white;
  background-color: #fec107;
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-warning .panel-body {
  background: #fec107;
  color: #ffffff;
}

.full-panel-warning .panel-footer {
  background: #fec107;
  color: #ffffff;
  border-top: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-warning a {
  color: #fec107;
}

.full-panel-warning a:hover {
  color: #b88b01;
}

.full-panel-success {
  border-color: #15D19B;
}

.full-panel-success .panel-heading {
  border-color: #15D19B;
  color: white;
  background-color: #15D19B;
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-success .panel-body {
  background: #15D19B;
  color: #ffffff;
}

.full-panel-success .panel-footer {
  background: #15D19B;
  color: #ffffff;
  border-top: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-success a {
  color: #15D19B;
}

.full-panel-success a:hover {
  color: #007658;
}

.full-panel-purple {
  border-color: #9675ce;
}

.full-panel-purple .panel-heading {
  color: white;
  background-color: #9675ce;
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-purple .panel-body {
  background: #9675ce;
  color: #ffffff;
}

.full-panel-purple .panel-footer {
  background: #9675ce;
  color: #ffffff;
  border-top: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-purple a {
  color: #9675ce;
}

.full-panel-purple a:hover {
  color: #6c41b6;
}

.full-panel-danger {
  border-color: #EA5836;
}

.full-panel-danger .panel-heading {
  border-color: #EA5836;
  color: white;
  background-color: #EA5836;
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-danger .panel-body {
  background: #EA5836;
  color: #ffffff;
}

.full-panel-danger .panel-footer {
  background: #EA5836;
  color: #ffffff;
  border-top: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-danger a {
  color: #EA5836;
}

.full-panel-danger a:hover {
  color: #f95c2e;
}

.full-panel-inverse {
  border-color: #4c5667;
}

.full-panel-inverse .panel-heading {
  border-color: #4c5667;
  color: white;
  background-color: #4c5667;
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-inverse .panel-body {
  background: #4c5667;
  color: #ffffff;
}

.full-panel-inverse .panel-footer {
  background: #4c5667;
  color: #ffffff;
  border-top: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-inverse a {
  color: #4c5667;
}

.full-panel-inverse a:hover {
  color: #2c313b;
}

.full-panel-default {
  border-color: rgba(120, 130, 140, 0.13);
}

.full-panel-default .panel-heading {
  color: #2b2b2b;
  background-color: #ffffff;
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-default .panel-body {
  color: #2b2b2b;
}

.full-panel-default .panel-footer {
  background: #ffffff;
  color: #2b2b2b;
  border-top: 1px solid rgba(120, 130, 140, 0.13);
}

.full-panel-default a {
  color: #2b2b2b;
}

.full-panel-default a:hover {
  color: #2c313b;
}

.panel-opcl {
  float: right;
}

.panel-opcl i {
  margin-left: 8px;
  font-size: 10px;
  cursor: pointer;
}

.fa-fw {
  width: 20px !important;
  display: inline-block !important;
  text-align: left !important;
  margin-right: 16px;
}

/*Wave Effeects*/
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.waves-effect .waves-ripple {
  position: absolute;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  opacity: 0;
  background: rgba(0, 0, 0, 0.08);
  -webkit-transition: all 0.5s ease-out;
  -moz-transition: all 0.5s ease-out;
  -o-transition: all 0.5s ease-out;
  transition: all 0.5s ease-out;
  -webkit-transition-property: -webkit-transform, opacity;
  -moz-transition-property: -moz-transform, opacity;
  -o-transition-property: -o-transform, opacity;
  transition-property: transform, opacity;
  -webkit-transform: scale(0) translate(0, 0);
  -moz-transform: scale(0) translate(0, 0);
  -ms-transform: scale(0) translate(0, 0);
  -o-transform: scale(0) translate(0, 0);
  transform: scale(0) translate(0, 0);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  pointer-events: none;
}

.waves-effect.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.waves-effect.waves-classic .waves-ripple {
  background: rgba(0, 0, 0, 0.2);
}

.waves-effect.waves-classic.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
}

.waves-notransition {
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  transition: none !important;
}

.waves-button,
.waves-circle {
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);
}

.waves-button,
.waves-button:hover,
.waves-button:visited,
.waves-button-input {
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  border: none;
  outline: none;
  color: inherit;
  background-color: rgba(0, 0, 0, 0);
  font-size: 1em;
  line-height: 1em;
  text-align: center;
  text-decoration: none;
  z-index: 1;
}

.waves-button {
  padding: 0.85em 1.1em;
  border-radius: 0.2em;
}

.waves-button-input {
  margin: 0;
  padding: 0.85em 1.1em;
}

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom;
}

.waves-input-wrapper.waves-button {
  padding: 0;
}

.waves-input-wrapper .waves-button-input {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
}

.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%;
}

.waves-float {
  -webkit-mask-image: none;
  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  -webkit-transition: all 300ms;
  -moz-transition: all 300ms;
  -o-transition: all 300ms;
  transition: all 300ms;
}

.waves-float:active {
  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
}

.waves-block {
  display: block;
}

/* =============
   Checkbox and Radios
============= */
.checkbox {
  padding-left: 20px;
}

.checkbox label {
  display: inline-block;
  padding-left: 5px;
  position: relative;
}

.checkbox label::before {
  -o-transition: 0.3s ease-in-out;
  -webkit-transition: 0.3s ease-in-out;
  background-color: #ffffff;
  border-radius: 1px;
  border: 1px solid rgba(120, 130, 140, 0.13);
  content: "";
  display: inline-block;
  height: 17px;
  left: 0;
  margin-left: -20px;
  position: absolute;
  transition: 0.3s ease-in-out;
  width: 17px;
  outline: none !important;
}

.checkbox label::after {
  color: #2b2b2b;
  display: inline-block;
  font-size: 11px;
  height: 16px;
  left: 0;
  margin-left: -20px;
  padding-left: 3px;
  padding-top: 1px;
  position: absolute;
  top: 0;
  width: 16px;
}

.checkbox input[type="checkbox"] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
  outline: none !important;
}

.checkbox input[type="checkbox"]:disabled+label {
  opacity: 0.65;
}

.checkbox input[type="checkbox"]:focus+label::before {
  outline-offset: -2px;
  outline: none;
  outline: thin dotted;
}

.checkbox input[type="checkbox"]:checked+label::after {
  content: "\f00c";
  font-family: 'FontAwesome';
}

.checkbox input[type="checkbox"]:disabled+label::before {
  background-color: #e4e7ea;
  cursor: not-allowed;
}

.checkbox.checkbox-circle label::before {
  border-radius: 50%;
}

.checkbox.checkbox-inline {
  margin-top: 0;
}

.checkbox.checkbox-single label {
  height: 17px;
}

.checkbox-primary input[type="checkbox"]:checked+label::before {
  background-color: #4c5aa0;
  border-color: #4c5aa0;
}

.checkbox-primary input[type="checkbox"]:checked+label::after {
  color: #ffffff;
}

.checkbox-danger input[type="checkbox"]:checked+label::before {
  background-color: #EA5836;
  border-color: #EA5836;
}

.checkbox-danger input[type="checkbox"]:checked+label::after {
  color: #ffffff;
}

.checkbox-info input[type="checkbox"]:checked+label::before {
  background-color: #03a9f3;
  border-color: #03a9f3;
}

.checkbox-info input[type="checkbox"]:checked+label::after {
  color: #ffffff;
}

.checkbox-warning input[type="checkbox"]:checked+label::before {
  background-color: #fec107;
  border-color: #fec107;
}

.checkbox-warning input[type="checkbox"]:checked+label::after {
  color: #ffffff;
}

.checkbox-success input[type="checkbox"]:checked+label::before {
  background-color: #15D19B;
  border-color: #15D19B;
}

.checkbox-success input[type="checkbox"]:checked+label::after {
  color: #ffffff;
}

.checkbox-purple input[type="checkbox"]:checked+label::before {
  background-color: #9675ce;
  border-color: #9675ce;
}

.checkbox-purple input[type="checkbox"]:checked+label::after {
  color: #ffffff;
}

.checkbox-red input[type="checkbox"]:checked+label::before {
  background-color: #EA5836;
  border-color: #EA5836;
}

.checkbox-red input[type="checkbox"]:checked+label::after {
  color: #ffffff;
}

.checkbox-inverse input[type="checkbox"]:checked+label::before {
  background-color: #4c5667;
  border-color: #4c5667;
}

.checkbox-inverse input[type="checkbox"]:checked+label::after {
  color: #ffffff;
}

/* Radios */
.radio {
  padding-left: 20px;
}

.radio label {
  display: inline-block;
  padding-left: 5px;
  position: relative;
}

.radio label::before {
  -o-transition: border 0.5s ease-in-out;
  -webkit-transition: border 0.5s ease-in-out;
  background-color: #ffffff;
  border-radius: 50%;
  border: 1px solid #55676F;
  content: "";
  display: inline-block;
  height: 17px;
  left: 0;
  margin-left: -20px;
  position: absolute;
  transition: border 0.5s ease-in-out;
  width: 17px;
  outline: none !important;
}

.radio label::after {
  -moz-transition: -moz-transform 0.3s cubic-bezier(0.8, -0.33, 0.2, 1.33);
  -ms-transform: scale(0, 0);
  -o-transform: scale(0, 0);
  -o-transition: -o-transform 0.3s cubic-bezier(0.8, -0.33, 0.2, 1.33);
  -webkit-transform: scale(0, 0);
  -webkit-transition: -webkit-transform 0.3s cubic-bezier(0.8, -0.33, 0.2, 1.33);
  background-color: #2b2b2b;
  border-radius: 50%;
  content: " ";
  display: inline-block;
  height: 7px;
  left: 5px;
  margin-left: -20px;
  position: absolute;
  top: 5px;
  transform: scale(0, 0);
  transition: transform 0.3s cubic-bezier(0.8, -0.33, 0.2, 1.33);
  width: 7px;
}

.radio input[type="radio"] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
}

.radio input[type="radio"]:disabled+label {
  opacity: 0.65;
}

.radio input[type="radio"]:focus+label::before {
  outline-offset: -2px;
  outline: 5px auto -webkit-focus-ring-color;
  outline: thin dotted;
}

.radio input[type="radio"]:checked+label::after {
  -ms-transform: scale(1, 1);
  -o-transform: scale(1, 1);
  -webkit-transform: scale(1, 1);
  transform: scale(1, 1);
}

.radio input[type="radio"]:disabled+label::before {
  cursor: not-allowed;
}

.radio.radio-inline {
  margin-top: 0;
}

.radio.radio-single label {
  height: 17px;
}

.radio-primary input[type="radio"]+label::after {
  background-color: #4c5aa0;
}

.radio-primary input[type="radio"]:checked+label::before {
  border-color: #4c5aa0;
}

.radio-primary input[type="radio"]:checked+label::after {
  background-color: #4c5aa0;
}

.radio-danger input[type="radio"]+label::after {
  background-color: #EA5836;
}

.radio-danger input[type="radio"]:checked+label::before {
  border-color: #EA5836;
}

.radio-danger input[type="radio"]:checked+label::after {
  background-color: #EA5836;
}

.radio-info input[type="radio"]+label::after {
  background-color: #4c5aa0;
}

.radio-info input[type="radio"]:checked+label::before {
  border-color: #4c5aa0;
}

.radio-info input[type="radio"]:checked+label::after {
  background-color: #4c5aa0;
}

.radio-warning input[type="radio"]+label::after {
  background-color: #fec107;
}

.radio-warning input[type="radio"]:checked+label::before {
  border-color: #fec107;
}

.radio-warning input[type="radio"]:checked+label::after {
  background-color: #fec107;
}

.radio-success input[type="radio"]+label::after {
  background-color: #15D19B;
}

.radio-success input[type="radio"]:checked+label::before {
  border-color: #15D19B;
}

.radio-success input[type="radio"]:checked+label::after {
  background-color: #15D19B;
}

.radio-purple input[type="radio"]+label::after {
  background-color: #9675ce;
}

.radio-purple input[type="radio"]:checked+label::before {
  border-color: #9675ce;
}

.radio-purple input[type="radio"]:checked+label::after {
  background-color: #9675ce;
}

.radio-red input[type="radio"]+label::after {
  background-color: #EA5836;
}

.radio-red input[type="radio"]:checked+label::before {
  border-color: #EA5836;
}

.radio-red input[type="radio"]:checked+label::after {
  background-color: #EA5836;
}

/* File Upload */
.fileupload {
  overflow: hidden;
  position: relative;
}

.fileupload input.upload {
  cursor: pointer;
  filter: alpha(opacity=0);
  font-size: 20px;
  margin: 0;
  opacity: 0;
  padding: 0;
  position: absolute;
  right: 0;
  top: 0;
}

/**
Models
**/
.model_img {
  cursor: pointer;
}

/*Nestable*/
.myadmin-dd .dd-list .dd-item .dd-handle {
  background: #ffffff;
  border: 1px solid rgba(120, 130, 140, 0.13);
  padding: 8px 16px;
  height: auto;
  font-weight: 600;
  border-radius: 0px;
}

.myadmin-dd .dd-list .dd-item .dd-handle:hover {
  color: #03a9f3;
}

.myadmin-dd .dd-list .dd-item button {
  height: auto;
  font-size: 17px;
  margin: 8px auto;
  color: #2b2b2b;
  width: 30px;
}

.myadmin-dd-empty .dd-list .dd3-handle {
  border: 1px solid rgba(120, 130, 140, 0.13);
  border-bottom: 0px;
  background: #ffffff;
  height: 36px;
  width: 36px;
}

.myadmin-dd-empty .dd-list .dd3-handle:before {
  color: inherit;
  top: 7px;
}

.myadmin-dd-empty .dd-list .dd3-handle:hover {
  color: #03a9f3;
}

.myadmin-dd-empty .dd-list .dd3-content {
  height: auto;
  border: 1px solid rgba(120, 130, 140, 0.13);
  padding: 8px 16px 8px 46px;
  background: #ffffff;
  font-weight: 600;
}

.myadmin-dd-empty .dd-list .dd3-content:hover {
  color: #03a9f3;
}

.myadmin-dd-empty .dd-list button {
  width: 26px;
  height: 26px;
  font-size: 16px;
  font-weight: 600;
}

/*Setting box*/
.settings_box {
  position: absolute;
  top: 75px;
  right: 0px;
  z-index: 100;
}

.settings_box a {
  background: #ffffff;
  padding: 15px;
  display: inline-block;
  vertical-align: top;
}

.settings_box a i {
  display: block;
  -webkit-animation-name: rotate;
  -webkit-animation-duration: 2s;
  -moz-animation-name: rotate;
  -moz-animation-duration: 2s;
  -moz-animation-iteration-count: infinite;
  -moz-animation-timing-function: linear;
  animation-name: rotate;
  font-size: 16px;
  animation-duration: 1s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

@-webkit-keyframes rotate {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

@-moz-keyframes rotate {
  from {
    -moz-transform: rotate(0deg);
  }

  to {
    -moz-transform: rotate(360deg);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.theme_color {
  margin: 0px;
  padding: 0px;
  display: inline-block;
  overflow: hidden;
  width: 0px;
  transition: 0.5s ease-out;
  background: #ffffff;
}

.theme_color li {
  list-style: none;
  width: 30%;
  float: left;
  margin: 0 1.5%;
}

.theme_color li a {
  padding: 5px;
  height: 50px;
  display: block;
}

.theme_color li a.theme-green {
  background: #15D19B;
}

.theme_color li a.theme-red {
  background: #EA5836;
}

.theme_color li a.theme-dark {
  background: #4c5667;
}

.theme_block {
  width: 200px;
  padding: 30px;
}

/*Common Ul*/
ul.common li {
  display: inline-block;
  line-height: 40px;
  list-style: outside none none;
  width: 48%;
}

ul.common li a {
  color: #686868;
}

ul.common li a:hover {
  color: #03a9f3;
}

/*ROW -IN*/
.row-in i {
  font-size: 24px;
}

/*Inbox widgets*/
.mailbox {
  width: 280px;
  overflow: auto;
  padding-bottom: 0px;
}

.message-center a {
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
  display: block;
  padding: 9px 15px;
}

.message-center a:hover {
  background: #f7fafc;
}

.message-center .user-img {
  width: 40px;
  float: left;
  position: relative;
  margin: 0 10px 15px 0px;
}

.message-center .user-img img {
  width: 100%;
}

.message-center .user-img .profile-status {
  border: 2px solid #ffffff;
  border-radius: 50%;
  display: inline-block;
  height: 10px;
  left: 30px;
  position: absolute;
  top: 1px;
  width: 10px;
}

.message-center .user-img .online {
  background: #15D19B;
}

.message-center .user-img .busy {
  background: #EA5836;
}

.message-center .user-img .away {
  background: #fec107;
}

.message-center .user-img .offline {
  background: #fec107;
}

.message-center .mail-contnet h5 {
  margin: 0px;
  font-weight: 400;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.message-center .mail-contnet .mail-desc {
  font-size: 12px;
  display: block;
  margin: 5px 0;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: #2b2b2b;
}

.message-center .mail-contnet .time {
  display: block;
  font-size: 10px;
  color: #2b2b2b;
}

.mail-contnet a.action {
  margin-left: 10px;
  font-size: 12px;
  visibility: hidden;
}

.mail-contnet:hover a.action {
  visibility: visible;
}

/*Inbox Center*/
.inbox-center .unread td {
  font-weight: 600;
}

.inbox-center a {
  color: #686868;
  padding: 2px 0 3px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

/*Comment center*/
.comment-center {
  margin: 0 -25px;
}

.comment-center .comment-body {
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
  display: table;
  padding: 20px 25px;
}

.comment-center .comment-body:hover {
  background: #f7fafc;
}

.comment-center .user-img {
  width: 40px;
  display: table-cell;
  position: relative;
  margin: 0 10px 0px 0px;
}

.comment-center .user-img img {
  width: 100%;
}

.comment-center .mail-contnet {
  display: table-cell;
  padding-left: 15px;
  vertical-align: top;
}

.comment-center .mail-contnet h5 {
  margin: 0px;
  font-weight: 400;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.comment-center .mail-contnet .mail-desc {
  font-size: 14px;
  display: block;
  margin: 8px 0;
  line-height: 25px;
  color: #848a96;
  height: 50px;
  overflow: hidden;
}

.comment-center .mail-contnet .time {
  display: block;
  font-size: 10px;
  color: #2b2b2b;
}

/*Sales report*/
.sales-report {
  background: #f7fafc;
  margin: 12px -25px;
  padding: 15px;
}

/*Task*/
.dropdown-tasks,
.dropdown-alerts {
  padding: 0px;
}

.dropdown-tasks li a,
.dropdown-alerts li a,
.mailbox li>a {
  padding: 15px 20px;
}

.dropdown-tasks li.divider,
.dropdown-alerts li.divider {
  margin: 0px;
}

/*col-in*/
.row-in-br {
  border-right: 1px solid rgba(120, 130, 140, 0.13);
}

.col-in {
  padding: 20px;
}

.col-in h3 {
  font-size: 34px;
}

/*
Basic List
*/
.basic-list {
  padding: 0px;
}

.basic-list li {
  display: block;
  padding: 15px 0px;
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
  line-height: 27px;
}

.basic-list li:last-child {
  border-bottom: 0px;
}

/* Steam line widget */
.steamline {
  position: relative;
  border-left: 1px solid rgba(120, 130, 140, 0.13);
  margin-left: 20px;
}

.steamline .sl-left {
  float: left;
  margin-left: -20px;
  z-index: 1;
  margin-right: 15px;
}

.steamline .sl-left img {
  max-width: 40px;
}

.steamline .sl-right {
  padding-left: 35px;
}

.steamline .sl-item {
  margin-top: 8px;
  margin-bottom: 30px;
}

.sl-date {
  font-size: 10px;
  color: #98a6ad;
}

.time-item {
  border-color: rgba(120, 130, 140, 0.13);
  padding-bottom: 1px;
  position: relative;
}

.time-item:before {
  content: " ";
  display: table;
}

.time-item:after {
  background-color: #ffffff;
  border-color: rgba(120, 130, 140, 0.13);
  border-radius: 10px;
  border-style: solid;
  border-width: 2px;
  bottom: 0;
  content: '';
  height: 14px;
  left: 0;
  margin-left: -8px;
  position: absolute;
  top: 5px;
  width: 14px;
}

.time-item-item:after {
  content: " ";
  display: table;
}

.item-info {
  margin-bottom: 15px;
  margin-left: 15px;
}

.item-info p {
  margin-bottom: 10px !important;
}

/*User-box*/
.user-bg {
  margin: -25px;
  height: 230px;
  overflow: hidden;
  position: relative;
}

.user-bg .overlay-box {
  background: #9675ce;
  opacity: 0.9;
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  height: 100%;
  text-align: center;
}

.user-bg .overlay-box .user-content {
  padding: 15px;
  margin-top: 30px;
}

.user-btm-box {
  padding: 40px 0 10px;
  clear: both;
  overflow: hidden;
}

/*    Vertical Carousel */
.vertical .carousel-inner {
  height: 100%;
  position: relative;
}

.carousel.vertical .item {
  -webkit-transition: 0.6s ease-in-out top;
  -moz-transition: 0.6s ease-in-out top;
  -ms-transition: 0.6s ease-in-out top;
  -o-transition: 0.6s ease-in-out top;
  transition: 0.6s ease-in-out top;
}

.carousel.vertical .active {
  top: 0;
}

.carousel.vertical .next {
  top: 400px;
}

.carousel.vertical .prev {
  top: -400px;
}

.carousel.vertical .next.left,
.carousel.vertical .prev.right {
  top: 0;
}

.carousel.vertical .active.left {
  top: -400px;
}

.carousel.vertical .active.right {
  top: 400px;
}

.carousel.vertical .item {
  left: 0;
}

.twi-user img {
  margin-right: 20px;
  width: 50px;
}

.twi-user {
  margin: 18px 0;
}

.carousel-inner h3 {
  height: 112px;
  overflow: hidden;
}

/*Chart Box*/
.chart-box {
  margin: 25px -15px -17px -17px;
}

/*Todo list*/
.list-task .task-done span {
  text-decoration: line-through;
}

/* Chat widget */
.chat-list {
  list-style: none;
  max-height: 332px;
  padding: 0px 20px;
}

.chat-list li {
  margin-bottom: 24px;
  overflow: auto;
}

.chat-list .chat-image {
  display: inline-block;
  float: left;
  text-align: center;
  width: 50px;
}

.chat-list .chat-image img {
  border-radius: 100%;
  width: 100%;
}

.chat-list .chat-text {
  background: #f7fafc;
  border-radius: 0px;
  display: inline-block;
  padding: 15px;
  position: relative;
}

.chat-list .chat-text h4 {
  color: #1a2942;
  display: block;
  font-size: 12px;
  font-style: normal;
  font-weight: bold;
  margin: 0;
  line-height: 15px;
  position: relative;
}

.chat-list .chat-text p {
  margin: 0px;
  padding-top: 3px;
}

.chat-list .chat-text b {
  font-size: 10px;
  opacity: 0.9;
}

.chat-list .chat-body {
  display: inline-block;
  float: left;
  font-size: 12px;
  margin-left: 12px;
  width: 65%;
}

.chat-list .odd .chat-image {
  float: right !important;
}

.chat-list .odd .chat-body {
  float: right !important;
  margin-right: 12px;
  text-align: right;
  color: #ffffff;
}

.chat-list .odd .chat-text {
  background: #ff6849;
}

.chat-list .odd .chat-text h4 {
  color: #ffffff;
}

.chat-send {
  padding-left: 0px;
  padding-right: 30px;
}

.chat-send button {
  width: 100%;
}

/*Weather*/
.weather-box .weather-top {
  overflow: hidden;
  padding: 10px 25px;
  margin: 0 -25px;
  background: #f7fafc;
}

.weather-box .weather-top h2 {
  line-height: 24px;
}

.weather-box .weather-top h2 small {
  font-size: 13px;
}

.weather-box .weather-top .today_crnt {
  font-size: 45px;
  font-weight: 100;
}

.weather-box .weather-top .today_crnt canvas {
  display: inline-block;
  margin-right: 10px;
  vertical-align: middle;
}

.weather-box .weather-info {
  padding: 10px 0;
}

.weather-box .weather-time {
  overflow: hidden;
  text-align: center;
  padding-top: 15px;
}

.weather-box .weather-time li span {
  display: block;
}

.weather-box .weather-time li canvas {
  font-size: 20px;
  margin: 10px 0;
}

.demo-container {
  width: 100%;
  height: 350px;
}

.demo-placeholder {
  width: 100%;
  height: 100%;
  font-size: 14px;
  line-height: 1.2em;
}

/*Notification alert*/
.myadmin-alert {
  border-radius: 0px;
  color: #fff;
  padding: 12px 30px 12px 12px;
  position: relative;
  text-align: left;
}

.myadmin-alert a {
  color: inherit;
  font-weight: 600;
  text-decoration: underline;
}

.myadmin-alert h4 {
  color: inherit;
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  margin: 0;
}

.myadmin-alert .img {
  border-radius: 3px;
  height: 40px;
  left: 12px;
  position: absolute;
  top: 12px;
  width: 40px;
}

.myadmin-alert-img {
  min-height: 64px;
  padding-left: 65px;
}

.myadmin-alert-icon {
  padding-left: 20px;
}

.myadmin-alert-icon i {
  padding-right: 10px;
}

.myadmin-alert .closed {
  color: rgba(255, 255, 255, 0.5);
  font-size: 20px;
  font-weight: bold;
  padding: 4px;
  position: absolute;
  right: 3px;
  text-decoration: none;
  top: 0;
}

.myadmin-alert .closed:hover {
  color: #fff;
}

.myadmin-alert-click {
  cursor: pointer;
  padding-right: 12px;
}

.myadmin-alert .primary {
  background: rgba(0, 0, 0, 0.4) none repeat scroll 0 0;
  border: medium none;
  border-radius: 3px;
  color: inherit;
  outline: 0 none;
  padding: 4px 10px;
}

.myadmin-alert .cancel {
  background: rgba(255, 255, 255, 0.4) none repeat scroll 0 0;
  border: medium none;
  border-radius: 3px;
  color: rgba(0, 0, 0, 0.8);
  outline: 0 none;
  padding: 4px 10px;
}

.myadmin-alert .primary:hover,
.myadmin-alert .cancel:hover {
  opacity: 0.9;
}

.myadmin-alert-top,
.myadmin-alert-bottom,
.myadmin-alert-top-left,
.myadmin-alert-top-right,
.myadmin-alert-bottom-left,
.myadmin-alert-bottom-right,
.myadmin-alert-fullscreen {
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
  display: none;
  position: fixed;
  z-index: 1000;
}

.myadmin-alert-top {
  left: 0;
  right: 0;
  top: 0;
}

.myadmin-alert-bottom {
  bottom: 0;
  left: 0;
  right: 0;
}

.myadmin-alert-top-left {
  left: 20px;
  top: 80px;
}

.myadmin-alert-top-right {
  right: 20px;
  top: 80px;
}

.myadmin-alert-bottom-left {
  bottom: 20px;
  left: 20px;
}

.myadmin-alert-bottom-right {
  bottom: 20px;
  right: 20px;
}

.myadmin-alert-fullsize {
  left: 50%;
  margin: -20px;
  top: 50%;
}

.glyphicon {
  margin-right: 10px;
}

.alert a:not([class^='text-']) {
  color: gold;
  margin-bottom: 0 !important;
}

.alert.alert-success a:not([class^='text-']) {
  color: #446EE2;
}

/*custom tab*/
.tab-content {
  margin-top: 30px;
}

.customtab {
  border-bottom: 2px solid #f7fafc;
}

.customtab li.active a,
.customtab li.active a:hover,
.customtab li.active a:focus {
  background: #ffffff;
  border: 0px;
  border-bottom: 2px solid #ff6849;
  margin-bottom: -1px;
  color: #ff6849;
}

.customtab li a,
.customtab li a:hover,
.customtab li a:focus {
  border: 0px;
}

/*custom tab2*/
.customtab2 {
  border-bottom: 1px solid #f7fafc;
  border-top: 1px solid #f7fafc;
  padding: 10px 0;
}

.customtab2 li.active a,
.customtab2 li.active a:hover,
.customtab2 li.active a:focus {
  background: #ff6849;
  border: 1px solid #ff6849;
  color: #ffffff;
}

.customtab2 li a,
.customtab2 li a:hover,
.customtab2 li a:focus {
  border: 0px;
}

/*Vertical tabs*/
.vtabs {
  display: table;
}

.vtabs .tabs-vertical {
  width: 150px;
  border-right: 1px solid rgba(120, 130, 140, 0.13);
  display: table-cell;
  vertical-align: top;
}

.vtabs .tabs-vertical li a {
  color: #2b2b2b;
  margin-bottom: 10px;
}

.vtabs .tab-content {
  display: table-cell;
  padding: 20px;
  vertical-align: top;
}

.tabs-vertical li.active a,
.tabs-vertical li.active a:hover,
.tabs-vertical li.active a:focus {
  background: #ff6849;
  border: 0px;
  border-right: 2px solid #ff6849;
  margin-right: -1px;
  color: #ffffff;
}

/*Custom vertical tab*/
.customvtab .tabs-vertical li.active a,
.customvtab .tabs-vertical li.active a:hover,
.customvtab .tabs-vertical li.active a:focus {
  background: #ffffff;
  border: 0px;
  border-right: 2px solid #ff6849;
  margin-right: -1px;
  color: #2b2b2b;
}

/*Nav pills*/
.nav-pills>li.active>a,
.nav-pills>li.active>a:focus,
.nav-pills>li.active>a:hover {
  background: #ff6849;
  color: #ffffff;
}

.nav-pills>li>a {
  color: #2b2b2b;
  border-radius: 0px;
}

/*Accordion*/
.panel-group .panel .panel-heading a[data-toggle=collapse].collapsed:before {
  content: '\e64b';
}

.report-nav-item {
  height: 100%;
}

.panel-group .panel .panel-heading .accordion-toggle.collapsed:before {
  content: '\e64b';
}

.panel-group .panel .panel-heading a[data-toggle=collapse] {
  display: block;
}

.panel-group .panel .panel-heading a[data-toggle=collapse]:before {
  content: '\e648';
  display: block;
  float: right;
  font-family: 'themify';
  font-size: 14px;
  text-align: right;
  width: 25px;
}

.panel-group .panel .panel-heading .accordion-toggle {
  display: block;
}

.panel-group .panel .panel-heading .accordion-toggle:before {
  content: '\e648';
  display: block;
  float: right;
  font-family: 'themify';
  font-size: 14px;
  text-align: right;
  width: 25px;
}

.panel-group .panel .panel-heading+.panel-collapse .panel-body {
  border-top: none;
}

.panel-group .panel-heading {
  padding: 12px 20px;
}

/*Progressbars*/
.progress {
  -webkit-box-shadow: none !important;
  background-color: rgba(120, 130, 140, 0.13);
  box-shadow: none !important;
  height: 4px;
  border-radius: 0px;
  margin-bottom: 18px;
  overflow: hidden;
}

.progress-bar {
  box-shadow: none;
  font-size: 8px;
  font-weight: 600;
  line-height: 12px;
  min-width: 10%;
  border-radius: 50px;
}

.progress.progress-sm {
  height: 8px !important;
}

.progress.progress-sm .progress-bar {
  font-size: 8px;
  line-height: 5px;
}

.progress.progress-md {
  height: 15px !important;
}

.progress.progress-md .progress-bar {
  font-size: 10.8px;
  line-height: 14.4px;
}

.progress.progress-lg {
  height: 20px !important;
}

.progress.progress-lg .progress-bar {
  font-size: 12px;
  line-height: 20px;
}

.progress-bar-primary {
  background-color: var(--primary-400);
}

.progress-bar-success {
  background-color: var(--success-400);
}

.progress-bar-info {
  background-color: #03a9f3;
}

.progress-bar-megna {
  background-color: #01c0c8;
}

.progress-bar-warning {
  background-color: var(--warning-400);
}

.progress-bar-danger {
  background-color: var(--danger-400);
}

.progress-bar-inverse {
  background-color: #4c5667;
}

.progress-bar-purple {
  background-color: #9675ce;
}

.progress-bar-custom {
  background-color: #03a9f3;
}

.progress-animated {
  -webkit-animation-duration: 5s;
  -webkit-animation-name: myanimation;
  -webkit-transition: 5s all;
  animation-duration: 5s;
  animation-name: myanimation;
  transition: 5s all;
}

/* Progressbar Animated */
@-webkit-keyframes myanimation {
  from {
    width: 0;
  }
}

@keyframes myanimation {
  from {
    width: 0;
  }
}

/* Progressbar Vertical */
.progress-vertical {
  min-height: 250px;
  height: 250px;
  width: 4px;
  position: relative;
  display: inline-block;
  margin-bottom: 0;
  margin-right: 20px;
}

.progress-vertical .progress-bar {
  width: 100%;
}

.progress-vertical-bottom {
  min-height: 250px;
  height: 250px;
  position: relative;
  width: 4px;
  display: inline-block;
  margin-bottom: 0;
  margin-right: 20px;
}

.progress-vertical-bottom .progress-bar {
  width: 100%;
  position: absolute;
  bottom: 0;
}

.progress-vertical.progress-sm,
.progress-vertical-bottom.progress-sm {
  width: 8px !important;
}

.progress-vertical.progress-sm .progress-bar,
.progress-vertical-bottom.progress-sm .progress-bar {
  font-size: 8px;
  line-height: 5px;
}

.progress-vertical.progress-md,
.progress-vertical-bottom.progress-md {
  width: 15px !important;
}

.progress-vertical.progress-md .progress-bar,
.progress-vertical-bottom.progress-md .progress-bar {
  font-size: 10.8px;
  line-height: 14.4px;
}

.progress-vertical.progress-lg,
.progress-vertical-bottom.progress-lg {
  width: 20px !important;
}

.progress-vertical.progress-lg .progress-bar,
.progress-vertical-bottom.progress-lg .progress-bar {
  font-size: 12px;
  line-height: 20px;
}

/*Timeline*/
.timeline {
  position: relative;
  padding: 20px 0 20px;
  list-style: none;
  max-width: 1200px;
  margin: 0 auto;
}

.timeline:before {
  content: " ";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 3px;
  margin-left: -1.5px;
  background-color: #eeeeee;
}

.timeline>li {
  position: relative;
  margin-bottom: 20px;
}

.timeline>li:before,
.timeline>li:after {
  content: " ";
  display: table;
}

.timeline>li:after {
  clear: both;
}

.timeline>li:before,
.timeline>li:after {
  content: " ";
  display: table;
}

.timeline>li:after {
  clear: both;
}

.timeline>li>.timeline-panel {
  float: left;
  position: relative;
  width: 46%;
  padding: 20px;
  border: 1px solid rgba(120, 130, 140, 0.13);
  border-radius: 0px;
  -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
}

.timeline>li>.timeline-panel:before {
  content: " ";
  display: inline-block;
  position: absolute;
  top: 26px;
  right: -8px;
  border-top: 8px solid transparent;
  border-right: 0 solid rgba(120, 130, 140, 0.13);
  border-bottom: 8px solid transparent;
  border-left: 8px solid rgba(120, 130, 140, 0.13);
}

.timeline>li>.timeline-panel:after {
  content: " ";
  display: inline-block;
  position: absolute;
  top: 27px;
  right: -7px;
  border-top: 7px solid transparent;
  border-right: 0 solid #fff;
  border-bottom: 7px solid transparent;
  border-left: 7px solid #fff;
}

.timeline>li>.timeline-badge {
  z-index: 100;
  position: absolute;
  top: 16px;
  left: 50%;
  width: 50px;
  height: 50px;
  margin-left: -25px;
  border-radius: 50% 50% 50% 50%;
  text-align: center;
  font-size: 1.4em;
  line-height: 50px;
  color: #fff;
  overflow: hidden;
  background-color: #4c5667;
}

.timeline>li.timeline-inverted>.timeline-panel {
  float: right;
}

.timeline>li.timeline-inverted>.timeline-panel:before {
  right: auto;
  left: -8px;
  border-right-width: 8px;
  border-left-width: 0;
}

.timeline>li.timeline-inverted>.timeline-panel:after {
  right: auto;
  left: -7px;
  border-right-width: 7px;
  border-left-width: 0;
}

.timeline-badge.primary {
  background-color: #4c5aa0 !important;
}

.timeline-badge.success {
  background-color: #15D19B !important;
}

.timeline-badge.warning {
  background-color: #fec107 !important;
}

.timeline-badge.danger {
  background-color: #EA5836 !important;
}

.timeline-badge.info {
  background-color: #03a9f3 !important;
}

.timeline-title {
  margin-top: 0;
  color: inherit;
  font-weight: 400;
}

.timeline-body>p,
.timeline-body>ul {
  margin-bottom: 0;
}

.timeline-body>p+p {
  margin-top: 5px;
}

/*Easy Pie charts*/
.chart {
  position: relative;
  display: inline-block;
  width: 100px;
  height: 100px;
  margin-top: 20px;
  margin-bottom: 20px;
  text-align: center;
}

.chart canvas {
  position: absolute;
  top: 0;
  left: 0;
}

.chart.chart-widget-pie {
  margin-top: 5px;
  margin-bottom: 5px;
}

.pie-chart>span {
  left: 0;
  margin-top: -2px;
  position: absolute;
  right: 0;
  text-align: center;
  top: 50%;
  transform: translateY(-50%);
}

.chart>span>img {
  left: 0;
  margin-top: -2px;
  position: absolute;
  right: 0;
  text-align: center;
  top: 50%;
  width: 60%;
  height: 60%;
  transform: translateY(-50%);
  margin: 0 auto;
}

.percent {
  display: inline-block;
  line-height: 100px;
  z-index: 2;
  font-weight: 600;
  font-size: 18px;
  color: #2b2b2b;
}

.percent:after {
  content: '%';
  margin-left: 0.1em;
  font-size: .8em;
}

.table-striped>tbody>tr:nth-of-type(odd),
.table-hover>tbody>tr:hover,
.table>thead>tr>td.active,
.table>tbody>tr>td.active,
.table>tfoot>tr>td.active,
.table>thead>tr>th.active,
.table>tbody>tr>th.active,
.table>tfoot>tr>th.active,
.table>thead>tr.active>td,
.table>tbody>tr.active>td,
.table>tfoot>tr.active>td,
.table>thead>tr.active>th,
.table>tbody>tr.active>th,
.table>tfoot>tr.active>th {
  background-color: #FAFAFA !important;
}

.table>thead>tr>th,
.table>tbody>tr>th,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>tbody>tr>td,
.table>tfoot>tr>td,
.table>thead>tr>th,
.table-bordered {
  border-top: 1px solid #e4e7ea;
}

.table>tbody>tr>td,
.table>tbody>tr>th,
.table>tfoot>tr>td,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>thead>tr>th {
  padding: 12px 12px;
}

.table-bordered>thead>tr>th,
.table-bordered>tbody>tr>th,
.table-bordered>tfoot>tr>th,
.table-bordered>thead>tr>td,
.table-bordered>tbody>tr>td,
.table-bordered>tfoot>tr>td {
  border: 1px solid #e4e7ea;
}

.table>thead>tr>th {
  vertical-align: bottom;
  border-bottom: 1px solid #e4e7ea;
}

th {
  font-weight: 500;
  color: #55676F;
}

table.focus-on tbody tr.focused th {
  background-color: #ff6849;
  color: #ffffff;
}

table.focus-on tbody tr.focused td {
  background-color: #ff6849;
  color: #ffffff;
}

.table-rep-plugin .table-responsive {
  border: none !important;
}

.table-rep-plugin tbody th {
  font-size: 14px;
  font-weight: normal;
}

.jsgrid .jsgrid-table {
  margin-bottom: 0px;
}

.jsgrid-selected-row>td {
  background: #f7fafc;
  border-color: #f7fafc;
}

.jsgrid-header-row>th {
  background: #ffffff;
}

.footable-odd {
  background-color: #fff;
}

/*Inputs*/
.form-control-line {
  border-left: 0 none;
  border-radius: 0;
  border-right: 0 none;
  border-top: 0 none;
  box-shadow: none;
  padding-left: 0;
}

.has-success .form-control {
  border-color: #15D19B;
  box-shadow: none !important;
}

.has-warning .form-control {
  border-color: #fec107;
  box-shadow: none !important;
}

.has-error .form-control {
  border-color: #EA5836;
  box-shadow: none !important;
}

.input-group-addon {
  border-radius: 2px;
  border: 1px solid rgba(120, 130, 140, 0.13);
}

.input-daterange input:first-child,
.input-daterange input:last-child {
  border-radius: 0px;
}

/*Material inputs*/
.form-material .form-group {
  overflow: hidden;
}

.form-material .form-control {
  background-color: rgba(0, 0, 0, 0);
  background-position: center bottom, center calc(99%);
  background-repeat: no-repeat;
  background-size: 0 2px, 100% 1px;
  padding: 0;
  transition: background 0s ease-out 0s;
}

.form-material .form-control,
.form-material .form-control.focus,
.form-material .form-control:focus {
  background-image: linear-gradient(#262668, #262668), linear-gradient(rgba(120, 130, 140, 0.13), rgba(120, 130, 140, 0.13));
  border: 0 none;
  border-radius: 0;
  box-shadow: none;
  float: none;
}

.form-material .form-control.focus,
.form-material .form-control:focus {
  background-size: 100% 2px, 100% 1px;
  outline: 0 none;
  transition-duration: 0.3s;
}

.form-bordered .form-group {
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
  padding-bottom: 20px;
}

/*Select 2*/
.select2 {
  width: 100% !important;
}

.select2-container .select2-choice {
  background-image: none !important;
  border: none !important;
  height: auto !important;
  padding: 0px !important;
  line-height: 22px !important;
  background-color: transparent !important;
  box-shadow: none !important;
}

.select2-container .select2-choice .select2-arrow {
  background-image: none !important;
  background: transparent;
  border: none;
  width: 14px;
  top: -2px;
}

.select2-container .select2-container-multi.form-control {
  height: auto;
}

.select2-results .select2-highlighted {
  color: #ffffff;
  background-color: #03a9f3;
}

.select2-drop-active {
  border: 1px solid #e3e3e3 !important;
  padding-top: 5px;
}

.select2-search input {
  border: 1px solid rgba(120, 130, 140, 0.13);
}

.select2-container-multi {
  width: 100%;
}

.select2-container-multi .select2-choices {
  border: 1px solid #e4e7ea !important;
  box-shadow: none !important;
  background-image: none !important;
  border-radius: 0px !important;
  min-height: 38px;
}

.select2-container-multi .select2-choices .select2-search-choice {
  padding: 4px 7px 4px 18px;
  margin: 5px 0 3px 5px;
  color: #555555;
  background: #f5f5f5;
  border-color: rgba(120, 130, 140, 0.13);
  -webkit-box-shadow: none;
  box-shadow: none;
}

.select2-container-multi .select2-choices .select2-search-field input {
  padding: 7px 7px 7px 10px;
  font-family: inherit;
}

/*Icons*/
.icon-list-demo div {
  cursor: pointer;
  line-height: 60px;
  white-space: nowrap;
  color: #686868;
}

.icon-list-demo div:hover {
  color: #2b2b2b;
}

.icon-list-demo div p {
  margin: 10px 0;
  padding: 5px 0;
}

.icon-list-demo i {
  -webkit-transition: all 0.2s;
  -webkit-transition: font-size 0.2s;
  display: inline-block;
  font-size: 18px;
  margin: 0 15px 0 10px;
  text-align: left;
  transition: all 0.2s;
  transition: font-size 0.2s;
  vertical-align: middle;
  width: auto;
  transition: all 0.3s ease 0s;
}

.icon-list-demo .col-md-4 {
  border-radius: 0px;
}

.icon-list-demo .col-md-4:hover {
  background-color: #f7fafc;
}

.icon-list-demo .col-md-4:hover i {
  font-size: 2em;
}

/*Google map*/
.gmaps,
.gmaps-panaroma {
  height: 300px;
}

.gmaps,
.gmaps-panaroma {
  height: 300px;
  background: #e4e7ea;
  border-radius: 3px;
}

.gmaps-overlay {
  display: block;
  text-align: center;
  color: #ffffff;
  font-size: 16px;
  line-height: 40px;
  background: #4c5aa0;
  border-radius: 4px;
  padding: 10px 20px;
}

.gmaps-overlay_arrow {
  left: 50%;
  margin-left: -16px;
  width: 0;
  height: 0;
  position: absolute;
}

.gmaps-overlay_arrow.above {
  bottom: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-top: 16px solid #4c5aa0;
}

.gmaps-overlay_arrow.below {
  top: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-bottom: 16px solid #4c5aa0;
}

.jvectormap-zoomin,
.jvectormap-zoomout {
  width: 10px;
  height: 10px;
  line-height: 10px;
}

.jvectormap-zoomout {
  top: 40px;
}

/* Login- register pages */
.login-register {
  height: 100%;
}

.login-box {
  background: #ffffff;
  width: 400px;
  margin: 0 auto;
}

.login-box .footer {
  width: 100%;
  left: 0px;
  right: 0px;
}

.login-box .social {
  display: block;
  margin-bottom: 30px;
}

#recoverform {
  display: none;
}

/*Pricing*/
.pricing-box {
  position: relative;
  text-align: center;
  margin-top: 30px;
}

.featured-plan {
  margin-top: 0px;
}

.featured-plan .pricing-body {
  padding: 60px 0;
  background: #f7fafc;
  border: 1px solid #ddd;
}

.featured-plan .price-table-content .price-row {
  border-top: 1px solid rgba(120, 130, 140, 0.13);
}

.pricing-body {
  border-radius: 0px;
  border-top: 1px solid rgba(120, 130, 140, 0.13);
  border-bottom: 5px solid rgba(120, 130, 140, 0.13);
  vertical-align: middle;
  padding: 30px 0;
  position: relative;
}

.pricing-body h2 {
  position: relative;
  font-size: 56px;
  margin: 20px 0 10px;
  font-weight: bold;
}

.pricing-body h2 span {
  position: absolute;
  font-size: 15px;
  top: -10px;
  margin-left: -10px;
}

.price-table-content .price-row {
  padding: 20px 0;
  border-top: 1px solid rgba(120, 130, 140, 0.13);
}

.pricing-plan {
  padding: 0 15px;
}

.pricing-plan .no-padding {
  padding: 0px;
}

.price-lable {
  position: absolute;
  top: -10px;
  padding: 5px 10px;
  margin: 0 auto;
  display: inline-block;
  width: 100px;
  left: 0px;
  right: 0px;
}

/*Inbox*/
.mails a {
  color: #2b2b2b;
}

.mails td {
  vertical-align: middle !important;
  position: relative;
}

.mails td:last-of-type {
  width: 100px;
  padding-right: 20px;
}

.mails tr:hover .text-white {
  display: none;
}

.mails .mail-select {
  padding: 12px 20px;
  min-width: 134px;
}

.mails .checkbox {
  margin-bottom: 0px;
  margin-top: 0px;
  vertical-align: middle;
  display: inline-block;
  height: 17px;
}

.mails .checkbox label {
  min-height: 16px;
}

.mail-list .list-group-item {
  background-color: transparent;
  border: 0px;
  border-left: 3px solid #ffffff;
  border-radius: 0px;
}

.mail-list .list-group-item:hover {
  background: #f7fafc;
  border-left: 3px solid #f7fafc;
}

.mail-list .list-group-item:focus {
  border-left: 3px solid #f7fafc;
}

.mail-list .list-group-item.active:focus {
  background: #f7fafc;
  border-left: 3px solid #EA5836;
}

.mail-list .list-group-item.active {
  border-left: 3px solid #EA5836;
  border-radius: 0px;
  color: #2b2b2b !important;
}

.mail_listing {
  min-height: 500px;
}

.inbox_listing .inbox-item:hover {
  background: #f7fafc;
}

.inbox_listing .inbox-item {
  padding-left: 20px;
}

.inbox-widget.inbox_listing .inbox-item .inbox-item-text {
  height: 19px;
  overflow: hidden;
}

.message-center .unread .mail-contnet h5,
.message-center .unread .mail-contnet .mail-desc {
  font-weight: 600;
  color: #2b2b2b !important;
}

/*Calendar*/
.calendar {
  float: left;
  margin-bottom: 0px;
}

.fc-view {
  margin-top: 30px;
}

.none-border .modal-footer {
  border-top: none;
}

.none-border .modal-header {
  border-bottom: none;
}

.modal-footer.centered {
  text-align: center;
}

.fc-toolbar {
  margin-bottom: 5px;
  margin-top: 15px;
}

.fc-toolbar h2 {
  font-size: 18px;
  font-weight: 600;
  line-height: 30px;
  text-transform: uppercase;
}

.fc-day {
  background: #ffffff;
}

.fc-toolbar .fc-state-active,
.fc-toolbar .ui-state-active,
.fc-toolbar button:focus,
.fc-toolbar button:hover,
.fc-toolbar .ui-state-hover {
  z-index: 0;
}

.fc-widget-header {
  border: 0px !important;
}

.fc-widget-content {
  border-color: rgba(120, 130, 140, 0.13) !important;
}

.fc th.fc-widget-header {
  background: #9675ce;
  color: #ffffff;
  font-size: 14px;
  line-height: 20px;
  padding: 7px 0px;
  text-transform: uppercase;
}

.fc-button {
  background: #ffffff;
  border: 1px solid rgba(120, 130, 140, 0.13);
  color: #555555;
  text-transform: capitalize;
}

.fc-text-arrow {
  font-family: inherit;
  font-size: 16px;
}

.fc-state-hover {
  background: #F5F5F5;
}

.fc-unthemed .fc-today {
  border: 1px solid #EA5836;
  background: #fcf8e3 !important;
}

.fc-state-highlight {
  background: #f0f0f0;
}

.fc-cell-overlay {
  background: #f0f0f0;
}

.fc-unthemed .fc-today {
  background: #ffffff;
}

.fc-event {
  border-radius: 0px;
  border: none;
  cursor: move;
  font-size: 13px;
  margin: 1px -1px 0 -1px;
  padding: 5px 5px;
  text-align: center;
  background: #03a9f3;
}

.calendar-event {
  cursor: move;
  margin: 10px 5px 0 0;
  padding: 6px 10px;
  display: inline-block;
  color: #ffffff;
  min-width: 140px;
  text-align: center;
  background: #03a9f3;
}

.calendar-event a {
  float: right;
  opacity: 0.6;
  font-size: 10px;
  margin: 4px 0 0 10px;
  color: #ffffff;
}

.fc-basic-view td.fc-week-number span {
  padding-right: 5px;
}

.fc-basic-view td.fc-day-number {
  padding-right: 5px;
}

/*Weather small widget*/
.weather h1 {
  color: #ffffff;
  font-size: 50px;
  font-weight: 100;
}

.weather i {
  color: #ffffff;
  font-size: 40px;
}

.weather .w-title-sub {
  color: rgba(255, 255, 255, 0.6);
}

/*Right sidebar*/
.navbar-top-links>li.right-side-toggle a:focus {
  background: #4F5467;
}

.right-sidebar {
  position: fixed;
  right: -240px;
  width: 240px;
  display: none;
  z-index: 1000;
  background: #ffffff;
  top: 0px;
  height: 100%;
  box-shadow: 5px 1px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.right-sidebar .rpanel-title {
  display: block;
  padding: 21px;
  color: #fff;
  text-transform: uppercase;
  font-size: 13px;
  background: #ff6849;
}

.right-sidebar .rpanel-title span {
  float: right;
  cursor: pointer;
  font-size: 11px;
}

.right-sidebar .rpanel-title span:hover {
  color: #2b2b2b;
}

.right-sidebar .r-panel-body {
  padding: 20px;
}

.right-sidebar .r-panel-body ul {
  margin: 0px;
  padding: 0px;
}

.right-sidebar .r-panel-body ul li {
  list-style: none;
  padding: 5px 0;
}

.shw-rside {
  right: 0px;
  width: 240px;
  display: block;
}

/*Chat online*/
.chatonline img {
  margin-right: 10px;
  float: left;
  width: 30px;
}

.chatonline li a {
  padding: 15px 0;
  float: left;
  width: 100%;
}

.chatonline li a span {
  color: #686868;
}

.chatonline li a span small {
  display: block;
  font-size: 10px;
}

/*Style switcher*/
ul#themecolors {
  display: block;
}

ul#themecolors li {
  display: inline-block;
}

ul#themecolors li:first-child {
  display: block;
}

#themecolors li a {
  width: 50px;
  height: 50px;
  display: inline-block;
  margin: 5px;
  color: transparent;
  position: relative;
}

#themecolors li a.working:before {
  content: "\f00c";
  font-family: "FontAwesome";
  font-size: 18px;
  line-height: 50px;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 0;
  left: 0;
  color: #fff;
  text-align: center;
}

.default-theme {
  background: #EA5836;
}

.green-theme {
  background: #15D19B;
}

.yellow-theme {
  background: #a0aec4;
}

.blue-theme {
  background: #03a9f3;
}

.purple-theme {
  background: #9675ce;
}

.megna-theme {
  background: #01c0c8;
}

.default-dark-theme {
  background: #4f5467;
  /* Old browsers */
  background: -moz-linear-gradient(left, #4f5467 0%, #4f5467 23%, #EA5836 23%, #EA5836 99%);
  /* FF3.6-15 */
  background: -webkit-linear-gradient(left, #4f5467 0%, #4f5467 23%, #EA5836 23%, #EA5836 99%);
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to right, #4f5467 0%, #4f5467 23%, #EA5836 23%, #EA5836 99%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#4f5467', endColorstr='#EA5836', GradientType=1);
  /* IE6-9 */
}

.green-dark-theme {
  background: #4f5467;
  /* Old browsers */
  background: -moz-linear-gradient(left, #4f5467 0%, #4f5467 23%, #15D19B 23%, #15D19B 99%);
  /* FF3.6-15 */
  background: -webkit-linear-gradient(left, #4f5467 0%, #4f5467 23%, #15D19B 23%, #15D19B 99%);
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to right, #4f5467 0%, #4f5467 23%, #15D19B 23%, #15D19B 99%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#4f5467', endColorstr='#15D19B', GradientType=1);
  /* IE6-9 */
}

.yellow-dark-theme {
  background: #4f5467;
  /* Old browsers */
  background: -moz-linear-gradient(left, #4f5467 0%, #4f5467 23%, #a0aec4 23%, #a0aec4 99%);
  /* FF3.6-15 */
  background: -webkit-linear-gradient(left, #4f5467 0%, #4f5467 23%, #a0aec4 23%, #a0aec4 99%);
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to right, #4f5467 0%, #4f5467 23%, #a0aec4 23%, #a0aec4 99%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#4f5467', endColorstr='#a0aec4', GradientType=1);
  /* IE6-9 */
}

.blue-dark-theme {
  background: #4f5467;
  /* Old browsers */
  background: -moz-linear-gradient(left, #4f5467 0%, #4f5467 23%, #03a9f3 23%, #03a9f3 99%);
  /* FF3.6-15 */
  background: -webkit-linear-gradient(left, #4f5467 0%, #4f5467 23%, #03a9f3 23%, #03a9f3 99%);
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to right, #4f5467 0%, #4f5467 23%, #03a9f3 23%, #03a9f3 99%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#4f5467', endColorstr='@info', GradientType=1);
  /* IE6-9 */
}

.purple-dark-theme {
  background: #4f5467;
  /* Old browsers */
  background: -moz-linear-gradient(left, #4f5467 0%, #4f5467 23%, #9675ce 23%, #9675ce 99%);
  /* FF3.6-15 */
  background: -webkit-linear-gradient(left, #4f5467 0%, #4f5467 23%, #9675ce 23%, #9675ce 99%);
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to right, #4f5467 0%, #4f5467 23%, #9675ce 23%, #9675ce 99%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#4f5467', endColorstr='@purple', GradientType=1);
  /* IE6-9 */
}

.megna-dark-theme {
  background: #4f5467;
  /* Old browsers */
  background: -moz-linear-gradient(left, #4f5467 0%, #4f5467 23%, #01c0c8 23%, #01c0c8 99%);
  /* FF3.6-15 */
  background: -webkit-linear-gradient(left, #4f5467 0%, #4f5467 23%, #01c0c8 23%, #01c0c8 99%);
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to right, #4f5467 0%, #4f5467 23%, #01c0c8 23%, #01c0c8 99%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#4f5467', endColorstr='@megna', GradientType=1);
  /* IE6-9 */
}

/*visited ul li*/
.visited li a {
  color: #686868;
}

.visited li.active a {
  color: #ff6849;
}

/*Stats Row*/
.stats-row {
  margin-bottom: 20px;
}

.stat-item {
  display: inline-block;
  padding-right: 15px;
}

.stat-item+.stat-item {
  padding-left: 15px;
  border-left: 1px solid #eee;
}

/*country-state*/
.country-state {
  list-style: none;
  margin: 0px;
  padding: 0px 0 0 10px;
}

.country-state h2 {
  margin: 0px;
}

.country-state .progress {
  margin-top: 8px;
}

/*Two part*/
.two-part li {
  width: 48.8%;
}

.two-part li i {
  font-size: 50px;
}

.two-part li span {
  font-size: 50px;
  font-weight: 100;
  font-family: 'Lato', sans-serif;
}

/*News Slides*/
.news-slide {
  position: relative;
}

.news-slide .overlaybg {
  height: 360px;
  overflow: hidden;
}

.news-slide .overlaybg img {
  width: 100%;
  height: 100%;
}

.news-slide .news-content {
  position: absolute;
  height: 360px;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10;
  width: 100%;
  top: 0px;
  padding: 30px;
}

.news-slide .news-content h2 {
  height: 240px;
  overflow: hidden;
  color: #ffffff;
}

.news-slide .news-content a {
  color: #ffffff;
  opacity: 0.6;
  text-transform: uppercase;
}

.news-slide .news-content a:hover {
  opacity: 1;
}

/*Nav pill rounded*/
.nav-pills-rounded li {
  display: inline-block;
  float: none;
}

.nav-pills-rounded li a {
  border-radius: 60px;
  -moz-border-radius: 60px;
  -webkit-border-radius: 60px;
  color: #686868;
  padding: 10px 25px;
}

.nav-pills-rounded li.active a,
.nav-pills-rounded li.active a:focus,
.nav-pills-rounded li.active a:hover {
  background: #ff6849;
  color: #ffffff;
}

/*analytics-info*/
.analytics-info .list-inline {
  margin-bottom: 0px;
}

.analytics-info .list-inline li {
  vertical-align: middle;
}

.analytics-info .list-inline li span {
  font-size: 24px;
}

.analytics-info .list-inline li i {
  font-size: 20px;
}

/*Feeds*/
.feeds {
  margin: 0px;
  padding: 0px;
}

.feeds li {
  list-style: none;
  padding: 10px;
  display: block;
}

.feeds li:hover {
  background: #f7fafc;
}

.feeds li>div {
  width: 40px;
  height: 40px;
  margin-right: 5px;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  border-radius: 100%;
}

.feeds li>div i {
  line-height: 40px;
}

.feeds li span {
  float: right;
  width: auto;
  font-size: 12px;
}

/*Dropzone*/
.dropzone {
  border-style: dashed;
  border-width: 1px;
}

.dropzone-policies {
  border: 2px dashed #262668 !important;
  color: #262668;
}

/*sales boxes*/
.weather h1 sup {
  font-size: 20px;
  top: -1.2em;
}

/* Button 1c */
.fcbtn {
  position: relative;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  transition: all 0.3s;
  padding: 8px 20px;
}

.fcbtn:after {
  content: '';
  position: absolute;
  z-index: -1;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  transition: all 0.3s;
}

/* Button 1b */
.btn-1b:after {
  width: 100%;
  height: 0;
  top: 0;
  left: 0;
}

.btn-1b:hover,
.btn-1b:active {
  color: #fff;
}

.btn-1b:hover:after,
.btn-1b:active:after {
  height: 100%;
}

.btn-1b.btn-info:after,
.btn-1c.btn-info:after,
.btn-1d.btn-info:after,
.btn-1e.btn-info:after,
.btn-1f.btn-info:after {
  background: #03a9f3;
}

.btn-1b.btn-warning:after,
.btn-1c.btn-warning:after,
.btn-1d.btn-warning:after,
.btn-1e.btn-warning:after,
.btn-1f.btn-warning:after {
  background: #fec107;
}

.btn-1b.btn-danger:after,
.btn-1c.btn-danger:after,
.btn-1d.btn-danger:after,
.btn-1e.btn-danger:after,
.btn-1f.btn-danger:after {
  background: #EA5836;
}

.btn-1b.btn-primary:after,
.btn-1c.btn-primary:after,
.btn-1d.btn-primary:after,
.btn-1e.btn-primary:after,
.btn-1f.btn-primary:after {
  background: #9675ce;
}

.btn-1b.btn-success:after,
.btn-1c.btn-success:after,
.btn-1d.btn-success:after,
.btn-1e.btn-success:after,
.btn-1f.btn-success:after {
  background: #15D19B;
}

.btn-1b.btn-inverse:after,
.btn-1c.btn-inverse:after,
.btn-1d.btn-inverse:after,
.btn-1e.btn-inverse:after,
.btn-1f.btn-inverse:after {
  background: #4c5667;
}

/* Button 1c */
.btn-1c:after {
  width: 0%;
  height: 100%;
  top: 0;
  left: 0;
}

.btn-1c:hover,
.btn-1c:active {
  color: #000;
}

.btn-1c:hover:after,
.btn-1c:active:after {
  width: 100%;
}

/* Button 1d */
.btn-1d {
  overflow: hidden;
}

.btn-1d:after {
  width: 0;
  height: 103%;
  top: 50%;
  left: 50%;
  opacity: 0;
  -webkit-transform: translateX(-50%) translateY(-50%);
  -moz-transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
}

.btn-1d:hover:after {
  width: 100%;
  opacity: 1;
}

/* Button 1e */
.btn-1e {
  overflow: hidden;
}

.btn-1e:after {
  width: 100%;
  height: 0;
  top: 50%;
  left: 50%;
  background: #fff;
  opacity: 0;
  -webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg);
  -moz-transform: translateX(-50%) translateY(-50%) rotate(45deg);
  -ms-transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
}

.btn-1e:hover:after {
  height: 260%;
  opacity: 1;
}

.btn-1e:active:after {
  height: 400%;
  opacity: 1;
}

/* Button 1f */
.btn-1f {
  overflow: hidden;
}

.btn-1f:after {
  width: 101%;
  height: 0;
  top: 50%;
  left: 50%;
  background: #fff;
  opacity: 0;
  -webkit-transform: translateX(-50%) translateY(-50%);
  -moz-transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
}

.btn-1f:hover:after {
  height: 100%;
  opacity: 1;
}

.btn-1f:active:after {
  height: 130%;
  opacity: 1;
}

/*sweat Aleart*/
.sweet-alert {
  padding: 25px;
}

.sweet-alert h2 {
  margin-top: 0px;
}

.sweet-alert p {
  line-height: 30px;
}

/*List icon*/
ul.list-icons {
  margin: 0px;
  padding: 0px;
}

ul.list-icons li {
  list-style: none;
  line-height: 40px;
}

ul.list-icons li i {
  font-size: 12px;
  margin-right: 5px;
}

/*Tooltip*/
.demo-tooltip .tooltip,
.demo-popover .popover {
  position: relative;
  margin-right: 25px;
  opacity: 1;
  display: inline-block;
}

.tooltip.right .tooltip-arrow {
  border-right-color: #55676f;
}

.tooltip.bottom .tooltip-arrow {
  border-bottom-color: #55676f;
}

.tooltip-inner {
  border-radius: 6px;
  padding: 8px 12px;
  background-color: #55676f;
  font-size: 14px;
  font-family: 'Lato', sans-serif;
  text-align: left;
}

.tooltip.in {
  opacity: 1;
}

.tooltip-primary.tooltip .tooltip-inner,
.tooltip-primary+.tooltip .tooltip-inner {
  color: #ffffff;
  background-color: #4c5aa0;
}

.tooltip-primary.tooltip.top .tooltip-arrow,
.tooltip-primary+.tooltip.top .tooltip-arrow {
  border-top-color: #4c5aa0;
}

.tooltip-primary.tooltip.right .tooltip-arrow,
.tooltip-primary+.tooltip.right .tooltip-arrow {
  border-right-color: #4c5aa0;
}

.tooltip-primary.tooltip.bottom .tooltip-arrow,
.tooltip-primary+.tooltip.bottom .tooltip-arrow {
  border-bottom-color: #4c5aa0;
}

.tooltip-primary.tooltip.left .tooltip-arrow,
.tooltip-primary+.tooltip.left .tooltip-arrow {
  border-left-color: #4c5aa0;
}

.tooltip-success.tooltip .tooltip-inner,
.tooltip-success+.tooltip .tooltip-inner {
  color: #ffffff;
  background-color: #15D19B;
}

.tooltip-success.tooltip.top .tooltip-arrow,
.tooltip-success+.tooltip.top .tooltip-arrow {
  border-top-color: #15D19B;
}

.tooltip-success.tooltip.right .tooltip-arrow,
.tooltip-success+.tooltip.right .tooltip-arrow {
  border-right-color: #15D19B;
}

.tooltip-success.tooltip.bottom .tooltip-arrow,
.tooltip-success+.tooltip.bottom .tooltip-arrow {
  border-bottom-color: #15D19B;
}

.tooltip-success.tooltip.left .tooltip-arrow,
.tooltip-success+.tooltip.left .tooltip-arrow {
  border-left-color: #15D19B;
}

.tooltip-warning.tooltip .tooltip-inner,
.tooltip-warning+.tooltip .tooltip-inner {
  color: #ffffff;
  background-color: #fec107;
}

.tooltip-warning.tooltip.top .tooltip-arrow,
.tooltip-warning+.tooltip.top .tooltip-arrow {
  border-top-color: #fec107;
}

.tooltip-warning.tooltip.right .tooltip-arrow,
.tooltip-warning+.tooltip.right .tooltip-arrow {
  border-right-color: #fec107;
}

.tooltip-warning.tooltip.bottom .tooltip-arrow,
.tooltip-warning+.tooltip.bottom .tooltip-arrow {
  border-bottom-color: #fec107;
}

.tooltip-warning.tooltip.left .tooltip-arrow,
.tooltip-warning+.tooltip.left .tooltip-arrow {
  border-left-color: #fec107;
}

.tooltip-info.tooltip .tooltip-inner,
.tooltip-info+.tooltip .tooltip-inner {
  color: #ffffff;
  background-color: #03a9f3;
}

.tooltip-info.tooltip.top .tooltip-arrow,
.tooltip-info+.tooltip.top .tooltip-arrow {
  border-top-color: #03a9f3;
}

[data-toggle="tooltip"]+.tooltip.top .tooltip-arrow {
  border-top-color: #56666f;
}

.tooltip-info.tooltip.right .tooltip-arrow,
.tooltip-info+.tooltip.right .tooltip-arrow {
  border-right-color: #03a9f3;
}

.tooltip-info.tooltip.bottom .tooltip-arrow,
.tooltip-info+tooltip.bottom .tooltip-arrow {
  border-bottom-color: #03a9f3;
}

.tooltip-info.tooltip.left .tooltip-arrow,
.tooltip-info+.tooltip.left .tooltip-arrow {
  border-left-color: #03a9f3;
}

.tooltip-danger.tooltip .tooltip-inner,
.tooltip-danger+.tooltip .tooltip-inner {
  color: #ffffff;
  background-color: #EA5836;
}

.tooltip-danger.tooltip.top .tooltip-arrow,
.tooltip-danger+.tooltip.top .tooltip-arrow {
  border-top-color: #EA5836;
}

.tooltip-danger.tooltip.right .tooltip-arrow,
.tooltip-danger+.tooltip.right .tooltip-arrow {
  border-right-color: #EA5836;
}

.tooltip-danger.tooltip.bottom .tooltip-arrow,
.tooltip-danger+.tooltip.bottom .tooltip-arrow {
  border-bottom-color: #EA5836;
}

.tooltip-danger.tooltip.left .tooltip-arrow,
.tooltip-danger+.tooltip.left .tooltip-arrow {
  border-left-color: #EA5836;
}

.flotTip {
  padding: 8px 12px;
  background-color: #2b2b2b;
  z-index: 100;
  color: #ffffff;
  opacity: 0.9;
  font-size: 13px;
}

/*Popover*/
.popover {
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.popover .popover-title {
  border-radius: 0px;
}

.popover-primary+.popover .popover-title {
  color: #ffffff;
  background-color: #4c5aa0;
  border-color: #4c5aa0;
}

.popover-primary+.popover.bottom .arrow {
  border-bottom-color: #4c5aa0;
}

.popover-primary+.popover.bottom .arrow:after {
  border-bottom-color: #4c5aa0;
}

.popover-success+.popover .popover-title {
  color: #ffffff;
  background-color: #15D19B;
  border-color: #15D19B;
}

.popover-success+.popover.bottom .arrow {
  border-bottom-color: #15D19B;
}

.popover-success+.popover.bottom .arrow:after {
  border-bottom-color: #15D19B;
}

.popover-info+.popover .popover-title {
  color: #ffffff;
  background-color: #03a9f3;
  border-color: #03a9f3;
}

.popover-info+.popover.bottom .arrow {
  border-bottom-color: #03a9f3;
}

.popover-info+.popover.bottom .arrow:after {
  border-bottom-color: #03a9f3;
}

.popover-warning+.popover .popover-title {
  color: #ffffff;
  background-color: #fec107;
  border-color: #fec107;
}

.popover-warning+.popover.bottom .arrow {
  border-bottom-color: #fec107;
}

.popover-warning+.popover.bottom .arrow:after {
  border-bottom-color: #fec107;
}

.popover-danger+.popover .popover-title {
  color: #ffffff;
  background-color: #EA5836;
  border-color: #EA5836;
}

.popover-danger+.popover.bottom .arrow {
  border-bottom-color: #EA5836;
}

.popover-danger+.popover.bottom .arrow:after {
  border-bottom-color: #EA5836;
}

/*File Upload*/
.btn-file {
  overflow: hidden;
  position: relative;
  vertical-align: middle;
}

.btn-file>input {
  position: absolute;
  top: 0;
  right: 0;
  margin: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  font-size: 23px;
  height: 100%;
  width: 100%;
  direction: ltr;
  cursor: pointer;
  border-radius: 0px;
}

.fileinput {
  margin-bottom: 9px;
  display: inline-block;
}

.fileinput .form-control {
  padding-top: 7px;
  padding-bottom: 5px;
  display: inline-block;
  margin-bottom: 0px;
  vertical-align: middle;
  cursor: text;
}

.fileinput .thumbnail {
  overflow: hidden;
  display: inline-block;
  margin-bottom: 5px;
  vertical-align: middle;
  text-align: center;
}

.fileinput .thumbnail>img {
  max-height: 100%;
}

.fileinput .btn {
  vertical-align: middle;
}

.fileinput-exists .fileinput-new,
.fileinput-new .fileinput-exists {
  display: none;
}

.fileinput-inline .fileinput-controls {
  display: inline;
}

.fileinput-filename {
  vertical-align: middle;
  display: inline-block;
  overflow: hidden;
}

.form-control .fileinput-filename {
  vertical-align: bottom;
}

.fileinput.input-group {
  display: table;
}

.fileinput.input-group>* {
  position: relative;
  z-index: 2;
}

.fileinput.input-group>.btn-file {
  z-index: 1;
}

/*Bootstrap select*/
.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 100%;
}

.ms-container .ms-list {
  border-radius: 0px;
  box-shadow: none;
}

.ms-container .ms-selectable li.ms-elem-selectable,
.ms-container .ms-selection li.ms-elem-selection {
  padding: 6px 10px;
}

.ms-container .ms-selectable li.ms-hover,
.ms-container .ms-selection li.ms-hover {
  background: #03a9f3;
}

/*Dropzone*/
.dropzone .dz-message {
  text-align: center;
  margin: 10% 0;
  font-size: 20px;
}

/*xeditable*/
.editable-input .form-control {
  height: 30px;
}

/*ascolorpicker*/
.asColorPicker-trigger {
  position: absolute;
  top: 0;
  right: -35px;
  height: 38px;
  width: 37px;
  border: 0px;
}

.asColorPicker-dropdown {
  max-width: 260px;
}

.asColorPicker-clear {
  top: 7px;
  right: 16px;
}

/*Datepicker*/
.datepicker table tr td.today,
.datepicker table tr td.today:hover,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover {
  background-image: none;
  background: #ff6849;
  color: #ffffff;
}

.datepicker table tr td.active,
.datepicker table tr td.active:hover,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover {
  background-image: none;
  background: #03a9f3;
  color: #ffffff;
}

/*Datatable*/
.editable-table+input.error {
  border: 1px solid #fc2929;
  outline: 0;
  outline-offset: 0;
}

.editable-table+input,
.editable-table+input:focus,
#editable-datatable_wrapper+input:focus {
  border: 1px solid #03a9f3 !important;
  outline: 0 !important;
  outline-offset: 0 !important;
}

.editable-table td:focus {
  outline: 0;
}

/*Form-Wizard*/
.wizard-steps {
  display: table;
  width: 100%;

}

.wizard-steps>li {
  display: table-cell;
  padding: 10px 20px;
  background: #f7fafc;
  border-radius: 8px;
  border: 4px white solid;
}

.wizard-steps>li span {
  border-radius: 100%;
  border: 1px solid rgba(120, 130, 140, 0.13);
  width: 40px;
  height: 40px;
  display: inline-block;
  /*vertical-align: middle;*/
  padding-top: 9px;
  margin-right: 8px;
  text-align: center;
  line-height: 0.8;
}

.wizard-content {
  padding: 25px;
  border-color: rgba(120, 130, 140, 0.13);
  margin-bottom: 30px;
}

.wizard-steps>li.current,
.wizard-steps>li.done {
  background: #03a9f3;
  color: #ffffff;
}

.wizard-steps>li.current span,
.wizard-steps>li.done span {
  border-color: #ffffff;
  color: #ffffff;
}

.wizard-steps>li.current h4,
.wizard-steps>li.done h4 {
  color: #ffffff;
}

.wizard-steps>li.done {
  background: #15D19B;
}

.wizard-steps>li.error {
  background: #EA5836;
}

.wiz-aco .pager {
  margin: 0px;
}

.sidebar {
  background: #E8EDED;
  font-family: "Circular Std Book", "Lato", sans-serif;
}

.sidebar .sidebar-nav.navbar-collapse {
  padding-left: 0;
  padding-right: 0;
}

.sidebar .fa-fw {
  width: 20px;
  text-align: left;
  display: inline-block;
}

.sidebar .label {
  font-size: 9px;
  width: 18px;
  height: 18px;
}

.sidebar .sidebar-search {
  padding: 15px;
}

#side-menu li a {
  color: #ffffff;
}

#side-menu>li>a {
  color: #112935;
  padding: 15px 15px;
  border-radius: 4px;
  margin: 10px 0;
}

#side-menu>li>a:hover,
#side-menu>li>a:focus {
  background: #f7fafc;
  border-color: #f7fafc;
}

#side-menu>li>a.active {
  color: #ffffff;
  background-color: white;

}

#side-menu ul>li>a:hover {
  color: #02bec9;
  background: #f7fafc;
}

#side-menu ul>li>a.active {
  color: #02bec9;

}

.sidebar .arrow {
  position: absolute;
  right: 15px;
  top: 13px;
}

.sidebar .fa.arrow:before {
  content: "\f105";
}

.sidebar .active>a>.fa.arrow:before {
  content: "\f107";
}

.sidebar .nav-second-level li a {
  padding-left: 37px;
}

.sidebar .nav-third-level li a {
  padding-left: 52px;
}

@media (min-width: 767px) {
  .nav-second-level {
    position: absolute;
    width: 220px;
  }

  .nav-second-level li {
    background: #f7fafc;
  }

  #side-menu>li>a span.arrow {
    display: none;
  }

  #side-menu .fa-fw {
    width: 100% !important;
    display: block !important;
    text-align: center !important;
    margin-bottom: 5px;
  }

  #side-menu .fa-fw.cs_navbar_button {
    width: auto !important;
    margin: 0 auto 11px;
  }

  .sidebar .nav-second-level li a {
    padding-left: 15px;
  }

  .sidebar .nav-third-level li a {
    padding-left: 30px;
  }

  #side-menu>li {
    float: left;
  }

  #side-menu>li .collapse.in {
    display: none;
  }

  #side-menu>li:hover .nav-second-level,
  #side-menu>li:hover .collapse.in {
    display: block;
  }

  /*-----Update 1.7------*/
  .nav-third-level,
  #side-menu .nav-second-level li .nav-third-level.collapse.in {
    position: absolute;
    width: 220px;
    background: #f7fafc;
    left: 221px;
    top: 0px;
    display: none;
    box-shadow: 5px 10px 20px rgba(0, 0, 0, 0.05);
  }

  #side-menu .nav-second-level li:hover>a {
    color: #ff6849;
  }

  #side-menu .nav-second-level li:hover .nav-third-level,
  #side-menu .nav-second-level li:hover .nav-third-level.collapse.in {
    display: block;
  }

  /*--------Update 1.8 -------*/
  #side-menu .nav-second-level.two-li {
    width: 450px;
    background: #f7fafc;
  }

  #side-menu .nav-second-level.two-li>li {
    width: 50%;
    float: left;
  }

  #side-menu .nav-second-level.two-li>li:hover .nav-third-level {
    z-index: 1;
    box-shadow: 2px 0px 20px rgba(0, 0, 0, 0.05);
  }
}

.open-close {
  display: none !important;
}

/* tweaks? */
.glyphs.character-mapping {
  margin: 0 0 20px 0;
  padding: 20px 0 20px 30px;
  color: rgba(0, 0, 0, 0.5);
  border: 1px solid #d8e0e5;
  -webkit-border-radius: 3px;
  border-radius: 3px;
}

.glyphs.character-mapping li {
  margin: 0 30px 20px 0;
  display: inline-block;
  width: 90px;
  text-align: center;
  font-size: 24px;
  color: #2b2b2b;
}

.linea-icon {
  position: relative;
}

.linea-icon svg {
  fill: #000;
}

.glyphs.character-mapping input {
  margin: 0;
  padding: 5px 0;
  line-height: 12px;
  font-size: 12px;
  display: block;
  width: 100%;
  border: 1px solid #d8e0e5;
  text-align: center;
  outline: 0;
}

.glyphs.character-mapping input:focus {
  border: 1px solid #fbde4a;
  -webkit-box-shadow: inset 0 0 3px #fbde4a;
  box-shadow: inset 0 0 3px #fbde4a;
}

.glyphs.character-mapping input:hover {
  -webkit-box-shadow: inset 0 0 3px #fbde4a;
  box-shadow: inset 0 0 3px #fbde4a;
}

/*------------------ Style tabs ------------------*/
.sttabs {
  position: relative;
  overflow: hidden;
  margin: 0 auto;
  width: 100%;
  font-weight: 300;
  border-radius: 6px;
}

.sticon::before {
  display: inline-block;
  margin: 0 0.4em 0 0;
  vertical-align: middle;
  font-size: 20px;
  speak: none;
  -webkit-backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.sttabs nav {
  text-align: center;
  border-radius: 6px;
}

.sttabs nav ul {
  position: relative;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  display: flex;
  margin: 0 auto;
  padding: 0;

  list-style: none;
  -ms-box-orient: horizontal;
  -ms-box-pack: center;
  -webkit-flex-flow: row wrap;
  -moz-flex-flow: row wrap;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  -ms-justify-content: center;
  justify-content: center;
}

.sttabs nav ul li {
  position: relative;
  z-index: 1;
  display: block;
  margin: 0 2px;
  text-align: center;
  -webkit-flex: 1;
  -moz-flex: 1;
  -ms-flex: 1;
  flex: 1;
  border-radius: 4px;
}

.sttabs nav ul li:hover {
  background-color: #D4D4D8;
}

.sttabs nav a {
  position: relative;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 2.5;
}

.sttabs nav a span {
  vertical-align: middle;
  font-size: 14px;
  font-family: 'Lexend', 'sans-serif';
  font-weight: 400;
}

.sttabs nav a:focus {
  outline: none;
}

.sttabs nav li.tab-current a {
  color: #EA5836;
}

/* Individual tab styles */
/*****************************/
/* Bar */
/*****************************/
.tabs-style-bar nav ul li a {
  margin: 0 2px;
  background-color: #f7fafc;
  color: #686868;
  padding: 5px 0;
  transition: background-color 0.2s, color 0.2s;
}

.tabs-style-bar nav ul li a:hover,
.tabs-style-bar nav ul li a:focus {
  color: #EA5836;
}

.tabs-style-bar nav ul li a span {
  letter-spacing: 1px;
  font-size: 14px;
  font-family: 'Poppins', sans-serif;
}

.tabs-style-bar nav ul li.tab-current a {
  background: #EA5836;
  color: #fff;
}

/*****************************/
/* Icon box */
/*****************************/
.tabs-style-iconbox nav {
  background: #f7fafc;

}

.tabs-style-iconbox nav ul li a {
  overflow: visible;
  padding: 8px 0;
  line-height: 1;
  -webkit-transition: color 0.2s;
  transition: color 0.2s;
  color: #2b2b2b;
  border-radius: 4px;
}

.tabs-style-iconbox nav ul li.tab-current {
  z-index: 1;
}

.tabs-style-iconbox nav ul li.tab-current a {
  background: #EA5836;
  color: #ffffff;
}



.tabs-style-iconbox nav ul li:first-child::before,
.tabs-style-iconbox nav ul li::after {
  position: absolute;
  top: 20%;
  right: 0;
  z-index: -1;
  width: 1px;
  height: 60%;
  content: '';
}

.tabs-style-iconbox nav ul li:first-child::before {
  right: auto;
  left: 0;
}

.tabs-style-iconbox .sticon::before {
  display: block;
  margin: 0 0 0.25em 0;
}

/*****************************/
/* Underline */
/*****************************/
.tabs-style-underline nav {
  border: 1px solid rgba(120, 130, 140, 0.13);
}

.tabs-style-underline nav a {
  padding: 20px 0;
  border-left: 1px solid rgba(120, 130, 140, 0.13);
  -webkit-transition: color 0.2s;
  transition: color 0.2s;
  color: #2b2b2b;
}

.tabs-style-underline nav li:last-child a {
  border-right: 1px solid rgba(120, 130, 140, 0.13);
}

.tabs-style-underline nav li a::after {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: #EA5836;
  content: '';
  -webkit-transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  -webkit-transform: translate3d(0, 150%, 0);
  transform: translate3d(0, 150%, 0);
}

.tabs-style-underline nav li.tab-current a::after {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

/*****************************/
/* Triangle and line */
/*****************************/
.tabs-style-linetriangle nav a {
  overflow: visible;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  -webkit-transition: color 0.2s;
  transition: color 0.2s;
}

.tabs-style-linetriangle nav a span {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  padding: 15px 0;
  color: #2b2b2b;
}

.tabs-style-linetriangle nav li.tab-current a:after,
.tabs-style-linetriangle nav li.tab-current a:before {
  position: absolute;
  top: 100%;
  left: 50%;
  width: 0;
  height: 0;
  border: solid transparent;
  content: '';
  pointer-events: none;
}

.tabs-style-linetriangle nav li.tab-current a:after {
  margin-left: -10px;
  border-width: 10px;
  border-top-color: #ffffff;
}

.tabs-style-linetriangle nav li.tab-current a span {
  color: #EA5836;
}

.tabs-style-linetriangle nav li.tab-current a:before {
  margin-left: -11px;
  border-width: 11px;
  border-top-color: rgba(0, 0, 0, 0.2);
}

/*****************************/
/* Falling Icon, from http://vintageproductions.eu/grid/interactivity/ */
/*****************************/
.tabs-style-iconfall {
  overflow: visible;
}

.tabs-style-iconfall nav {
  max-width: 1200px;
  margin: 0 auto;
}

.tabs-style-iconfall nav a {
  display: inline-block;
  overflow: visible;
  padding: 1em 0 2em;
  color: #2b2b2b;
  line-height: 1;
  -webkit-transition: color 0.3s cubic-bezier(0.7, 0, 0.3, 1);
  transition: color 0.3s cubic-bezier(0.7, 0, 0.3, 1);
}

.tabs-style-iconfall nav a:hover,
.tabs-style-iconfall nav a:focus {
  color: #EA5836;
}

.tabs-style-iconfall nav li.tab-current a {
  color: #EA5836;
}

.tabs-style-iconfall nav li::before {
  position: absolute;
  bottom: 1em;
  left: 50%;
  margin-left: -20px;
  width: 40px;
  height: 4px;
  background: #EA5836;
  content: '';
  opacity: 0;
  -webkit-transition: -webkit-transform 0.2s ease-in;
  transition: transform 0.2s ease-in;
  -webkit-transform: scale3d(0, 1, 1);
  transform: scale3d(0, 1, 1);
}

.tabs-style-iconfall nav li.tab-current::before {
  opacity: 1;
  -webkit-transform: scale3d(1, 1, 1);
  transform: scale3d(1, 1, 1);
}

.tabs-style-iconfall nav li.tab-current .sticon::before {
  opacity: 1;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.tabs-style-iconfall .sticon::before {
  display: block;
  margin: 0 0 0.35em;
  opacity: 0;
  font-size: 24px;
  -webkit-transition: -webkit-transform 0.2s, opacity 0.2s;
  transition: transform 0.2s, opacity 0.2s;
  -webkit-transform: translate3d(0, -100px, 0);
  transform: translate3d(0, -100px, 0);
  pointer-events: none;
}

@media screen and (max-width: 58em) {
  .tabs-style-iconfall nav li .sticon::before {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

/*****************************/
/* Moving Line */
/*****************************/
.tabs-style-linemove nav {
  background: #f7fafc;
}

.tabs-style-linemove nav li:last-child::before {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: #EA5836;
  content: '';
  -webkit-transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
}

.tabs-style-linemove nav li:first-child.tab-current~li:last-child::before {
  -webkit-transform: translate3d(-400%, 0, 0);
  transform: translate3d(-400%, 0, 0);
}

.tabs-style-linemove nav li:nth-child(2).tab-current~li:last-child::before {
  -webkit-transform: translate3d(-300%, 0, 0);
  transform: translate3d(-300%, 0, 0);
}

.tabs-style-linemove nav li:nth-child(3).tab-current~li:last-child::before {
  -webkit-transform: translate3d(-200%, 0, 0);
  transform: translate3d(-200%, 0, 0);
}

.tabs-style-linemove nav li:nth-child(4).tab-current~li:last-child::before {
  -webkit-transform: translate3d(-100%, 0, 0);
  transform: translate3d(-100%, 0, 0);
}

.tabs-style-linemove nav a {
  padding: 30px 0;
  color: #2b2b2b;
  line-height: 1;
  -webkit-transition: color 0.3s, -webkit-transform 0.3s;
  transition: color 0.3s, transform 0.3s;
}

.tabs-style-linemove nav li.tab-current a {
  color: #EA5836;
}

/*****************************/
/* Line */
/*****************************/
.tabs-style-line nav a {
  padding: 20px 10px;
  box-shadow: inset 0 -2px #d1d3d2;
  color: #686868;
  text-align: left;
  text-transform: uppercase;
  letter-spacing: 1px;
  line-height: 1;
  -webkit-transition: color 0.3s, box-shadow 0.3s;
  transition: color 0.3s, box-shadow 0.3s;
}

.tabs-style-line nav a:hover,
.tabs-style-line nav a:focus {
  box-shadow: inset 0 -2px #74777b;
}

.tabs-style-line nav li.tab-current a {
  box-shadow: inset 0 -2px #EA5836;
  color: #EA5836;
}

@media screen and (max-width: 58em) {
  .tabs-style-line nav ul {
    display: block;
    box-shadow: none;
  }

  .tabs-style-line nav ul li {
    display: block;
    -webkit-flex: none;
    flex: none;
  }
}

/*****************************/
/* Circle */
/*****************************/
.tabs-style-circle {
  overflow: visible;
}

.tabs-style-circle nav li {
  margin-top: 60px !important;
  margin-bottom: 60px !important;
}

.tabs-style-circle nav li::before {
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -60px 0 0 -60px;
  width: 120px;
  height: 120px;
  border: 1px solid #EA5836;
  border-radius: 50%;
  content: '';
  opacity: 0;
  -webkit-transition: -webkit-transform 0.2s, opacity 0.2s;
  transition: transform 0.2s, opacity 0.2s;
  -webkit-transition-timing-function: cubic-bezier(0.7, 0, 0.3, 1);
  transition-timing-function: cubic-bezier(0.7, 0, 0.3, 1);
}

.tabs-style-circle nav a {
  overflow: visible;
  color: #2b2b2b;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.1;
  -webkit-transition: color 0.3s cubic-bezier(0.7, 0, 0.3, 1);
  transition: color 0.3s cubic-bezier(0.7, 0, 0.3, 1);
}

.tabs-style-circle nav a span {
  display: inline-block;
}

.tabs-style-circle nav a:hover,
.tabs-style-circle nav a:focus {
  color: #EA5836;
}

.tabs-style-circle nav li.tab-current a {
  color: #EA5836;
}

.tabs-style-circle nav li.tab-current a span {
  -webkit-transform: translate3d(0, 4px, 0);
  transform: translate3d(0, 4px, 0);
}

@media screen and (max-width: 58em) {
  .tabs-style-circle nav li::before {
    margin: -40px 0 0 -40px;
    width: 80px;
    height: 80px;
  }
}

.tabs-style-circle nav li.tab-current::before {
  opacity: 1;
  -webkit-transform: scale3d(1, 1, 1);
  transform: scale3d(1, 1, 1);
}

.tabs-style-circle nav a span,
.tabs-style-circle .icon::before {
  -webkit-transition: -webkit-transform 0.3s cubic-bezier(0.7, 0, 0.3, 1);
  transition: transform 0.3s cubic-bezier(0.7, 0, 0.3, 1);
}

.tabs-style-circle .sticon::before {
  display: block;
  margin: 0;
  pointer-events: none;
}

.tabs-style-circle nav li.tab-current .sticon::before {
  -webkit-transform: translate3d(0, -4px, 0);
  transform: translate3d(0, -4px, 0);
}

/*****************************/
/* Shape */
/*****************************/
.tabs-style-shape {
  max-width: 1200px;
  margin: 0 auto;
}

.tabs-style-shape nav ul li {
  margin: 0 3em;
}

.tabs-style-shape nav ul li:first-child {
  margin-left: 0;
}

.tabs-style-shape nav ul li.tab-current {
  z-index: 1;
}

.tabs-style-shape nav li a {
  overflow: visible;
  margin: 0 -3em 0 0;
  padding: 0;
  color: #fff;
  font-weight: 500;
}

.tabs-style-shape nav li a svg {
  position: absolute;
  left: 100%;
  margin: 0;
  width: 3em;
  height: 100%;
  fill: #bdc2c9;
}

.tabs-style-shape nav li:first-child a span {
  padding-left: 2em;
  border-radius: 30px 0 0 0;
}

.tabs-style-shape nav li:last-child a span {
  padding-right: 2em;
  border-radius: 0 30px 0 0;
}

.tabs-style-shape nav li a svg:nth-child(2),
.tabs-style-shape nav li:last-child a svg {
  right: 100%;
  left: auto;
  -webkit-transform: scale3d(-1, 1, 1);
  transform: scale3d(-1, 1, 1);
}

.tabs-style-shape nav li a span {
  display: block;
  overflow: hidden;
  padding: 0.65em 0;
  background-color: #bdc2c9;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tabs-style-shape nav li a:hover span {
  background-color: #EA5836;
}

.tabs-style-shape nav li a:hover svg {
  fill: #EA5836;
}

.tabs-style-shape nav li a svg {
  pointer-events: none;
}

.tabs-style-shape nav li a svg use {
  pointer-events: auto;
}

.tabs-style-shape nav li.tab-current a span,
.tabs-style-shape nav li.tab-current a svg {
  -webkit-transition: none;
  transition: none;
}

.tabs-style-shape nav li.tab-current a span {
  background: #f7fafc;
}

.tabs-style-shape nav li.tab-current a svg {
  fill: #f7fafc;
}

.tabs-style-shape .content-wrap {
  background: #f7fafc;
}

@media screen and (max-width: 58em) {
  .tabs-style-shape nav ul {
    display: block;
    padding-top: 1.5em;
  }

  .tabs-style-shape nav ul li {
    display: block;
    margin: -1.25em 0 0;
    -webkit-flex: none;
    flex: none;
  }

  .tabs-style-shape nav ul li a {
    margin: 0;
  }

  .tabs-style-shape nav ul li svg {
    display: none;
  }

  .tabs-style-shape nav ul li a span {
    padding: 1.25em 0 2em !important;
    border-radius: 30px 30px 0 0 !important;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.1);
    line-height: 1;
  }

  .tabs-style-shape nav ul li:last-child a span {
    padding: 1.25em 0 !important;
  }

  .tabs-style-shape nav ul li.tab-current {
    z-index: 1;
  }
}

/*****************************/
/* Line Box */
/*****************************/
.tabs-style-linebox nav ul li {
  margin: 0 0.5em;
  -webkit-flex: none;
  flex: none;
}

.tabs-style-linebox nav a {
  padding: 0 1.5em;
  color: #2b2b2b;
  font-weight: 500;
  -webkit-transition: color 0.3s;
  transition: color 0.3s;
}

.tabs-style-linebox nav a:hover,
.tabs-style-linebox nav a:focus {
  color: #EA5836;
}

.tabs-style-linebox nav li.tab-current a {
  color: #ffffff;
}

.tabs-style-linebox nav a::after {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background: #d2d8d6;
  content: '';
  -webkit-transition: background-color 0.3s, -webkit-transform 0.3s;
  transition: background-color 0.3s, transform 0.3s;
  -webkit-transition-timing-function: ease, cubic-bezier(0.7, 0, 0.3, 1);
  transition-timing-function: ease, cubic-bezier(0.7, 0, 0.3, 1);
  -webkit-transform: translate3d(0, 100%, 0) translate3d(0, -3px, 0);
  transform: translate3d(0, 100%, 0) translate3d(0, -3px, 0);
}

.tabs-style-linebox nav li.tab-current a::after {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.tabs-style-linebox nav a:hover::after,
.tabs-style-linebox nav a:focus::after,
.tabs-style-linebox nav li.tab-current a::after {
  background: #EA5836;
}

@media screen and (max-width: 58em) {
  .tabs-style-linebox nav ul {
    display: block;
    box-shadow: none;
  }

  .tabs-style-linebox nav ul li {
    display: block;
    -webkit-flex: none;
    flex: none;
  }
}

/*****************************/
/* Flip */
/*****************************/
.tabs-style-flip {
  max-width: 1200px;
  margin: 0 auto;
}

.tabs-style-flip nav a {
  padding: 0.5em 0;
  color: #2b2b2b;
  -webkit-transition: color 0.3s;
  transition: color 0.3s;
}

.tabs-style-flip nav a:hover,
.tabs-style-flip nav a:focus {
  color: #EA5836;
}

.tabs-style-flip nav a span {
  text-transform: uppercase;
  letter-spacing: 1px;
}

.tabs-style-flip nav a::after {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  content: '';
  -webkit-transition: -webkit-transform 0.3s, background-color 0.3s;
  transition: transform 0.3s, background-color 0.3s;
  -webkit-transform: perspective(900px) rotate3d(1, 0, 0, 90deg);
  transform: perspective(900px) rotate3d(1, 0, 0, 90deg);
  -webkit-transform-origin: 50% 100%;
  transform-origin: 50% 100%;
  -webkit-perspective-origin: 50% 100%;
  perspective-origin: 50% 100%;
}

.tabs-style-flip nav li.tab-current a {
  color: #EA5836;
}

.tabs-style-flip nav li.tab-current a::after {
  background-color: #f7fafc;
  -webkit-transform: perspective(900px) rotate3d(1, 0, 0, 0deg);
  transform: perspective(900px) rotate3d(1, 0, 0, 0deg);
}

.tabs-style-flip .content-wrap {
  background: #f7fafc;
}

/*****************************/
/* Circle fill */
/*****************************/
.tabs-style-circlefill {
  max-width: 800px;
  border: 1px solid #EA5836;
  margin: 0 auto;
}

.tabs-style-circlefill nav ul li {
  overflow: hidden;
  border-right: 1px solid #EA5836;
}

.tabs-style-circlefill nav li a {
  padding: 1.5em 0;
  color: #fff;
  font-size: 1.25em;
}

.tabs-style-circlefill nav li:first-child {
  border-left: none;
}

.tabs-style-circlefill nav li:last-child {
  border: none;
}

.tabs-style-circlefill nav li::before {
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -40px 0 0 -40px;
  width: 80px;
  height: 80px;
  border: 1px solid #EA5836;
  border-radius: 50%;
  background: #EA5836;
  content: '';
  -webkit-transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
}

.tabs-style-circlefill nav li.tab-current::before {
  -webkit-transform: scale3d(2.5, 2.5, 1);
  transform: scale3d(2.5, 2.5, 1);
}

.tabs-style-circlefill nav a {
  -webkit-transition: color 0.3s;
  transition: color 0.3s;
}

.tabs-style-circlefill nav a span {
  display: none;
}

.tabs-style-circlefill nav li.tab-current a {
  color: #ffffff;
}

.tabs-style-circlefill .icon::before {
  display: block;
  margin: 0;
  pointer-events: none;
}

.tabs-style-circlefill .content-wrap {
  border-top: 1px solid #EA5836;
}

/* Content */
.content-wrap {
  position: relative;
}

.content-wrap section {
  display: none;
  margin: 0 auto;
  padding: 25px;
  min-height: 150px;
}

.content-wrap section p {
  margin: 0;
  padding: 0.75em 0;
}

.content-wrap section.content-current {
  display: block;
}

/* Fallback */
.no-js .content-wrap section {
  display: block;
  padding-bottom: 2em;
  border-bottom: 1px solid rgba(255, 255, 255, 0.6);
}

.no-flexbox nav ul {
  display: block;
}

.no-flexbox nav ul li {
  min-width: 15%;
  display: inline-block;
}

@media screen and (max-width: 58em) {
  .sttabs nav a span {
    display: none;
  }

  .sttabs nav a:before {
    margin-right: 0;
  }
}

/*For Laptop (1280px)*/
@media (max-width: 1350px) {
  .carousel .item h3 {
    font-size: 17px;
    height: 90px;
  }

  .inbox-center a {
    width: 400px;
  }
}

/********* Search Result Page**********/
.search-listing {
  padding: 0px;
  margin: 0px;
}

.search-listing li {
  list-style: none;
  padding: 15px 0;
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
}

.search-listing li h3 {
  margin: 0px;
  font-size: 18px;
}

.search-listing li h3 a {
  color: #03a9f3;
}

.search-listing li h3 a:hover {
  text-decoration: underline;
}

.search-listing li a {
  color: #15D19B;
}

/********* Megamenu Page**********/
.megamenu {
  left: 0px;
  right: 0px;
  width: 100%;
}

.mega-dropdown {
  position: static !important;
}

.mega-dropdown-menu {
  padding: 20px;
  width: 100%;
  box-shadow: none;
  -webkit-box-shadow: none;
  border: 0px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2) !important;
}

.mega-dropdown-menu>li>ul {
  padding: 0;
  margin: 0;
}

.mega-dropdown-menu>li>ul>li {
  list-style: none;
}

.mega-dropdown-menu>li>ul>li>a {
  display: block;
  padding: 8px 0px;
  clear: both;
  line-height: 1.428571429;
  color: #686868;
  white-space: normal;
}

.mega-dropdown-menu>li>ul>li>a:hover,
.mega-dropdown-menu>li>ul>li>a:focus {
  text-decoration: none;
  color: #ff6849;
}

.mega-dropdown-menu .dropdown-header {
  font-size: 16px;
  font-weight: 500;
  padding: 8px 0;
  margin-top: 12px;
}

.mega-dropdown-menu li.demo-box a {
  color: #ffffff;
  display: block;
}

.mega-dropdown-menu li.demo-box a:hover {
  opacity: 0.9;
}

/*Data tables*/
button.dt-button,
div.dt-button,
a.dt-button {
  background: #03a9f3;
  color: #ffffff;
  border-color: #03a9f3;
}

button.dt-button:hover,
div.dt-button:hover,
a.dt-button:hover {
  background: #03a9f3;
}

button.dt-button:hover:not(.disabled),
div.dt-button:hover:not(.disabled),
a.dt-button:hover:not(.disabled) {
  background: #f7fafc;
  color: #2b2b2b;
  border-color: rgba(120, 130, 140, 0.13);
}

.dataTables_filter input {
  border: 1px solid rgba(120, 130, 140, 0.13);
}

table.dataTable.display tbody tr.odd>.sorting_1,
table.dataTable.order-column.stripe tbody tr.odd>.sorting_1,
table.dataTable.display tbody tr:hover>.sorting_1,
table.dataTable.order-column.hover tbody tr:hover>.sorting_1,
table.dataTable.display tbody tr.even>.sorting_1,
table.dataTable.order-column.stripe tbody tr.even>.sorting_1 {
  background: none;
}

/*Summernote*/
.note-editor {
  border: 1px solid rgba(120, 130, 140, 0.13);
}

.note-editor .panel-heading {
  padding: 6px 10px 10px;
}

/*--------------------------------------------------------------
  Update 1.6
--------------------------------------------------------------*/
/*left-right-aside-column*/
.page-aside {
  position: relative;
}

/*left-aside-column*/
.left-aside {
  position: absolute;
  background: #ffffff;
  border-right: 1px solid rgba(120, 130, 140, 0.13);
  padding: 20px;
  width: 250px;
  height: 100%;
}

.right-aside {
  padding: 20px;
  margin-left: 250px;
}

.right-aside .contact-list td {
  vertical-align: middle;
  padding: 25px 10px;
}

.right-aside .contact-list td img {
  width: 30px;
}

.list-style-none {
  margin: 0px;
  padding: 0px;
}

.list-style-none li {
  list-style: none;
  margin: 0px;
}

.list-style-none li.box-label a {
  font-weight: 500;
}

.list-style-none li.divider {
  margin: 10px 0;
  height: 1px;
  background: rgba(120, 130, 140, 0.13);
}

.list-style-none li a {
  padding: 15px 10px;
  display: block;
  color: #686868;
}

.list-style-none li a:hover {
  color: #ff6849;
}

.list-style-none li a span {
  float: right;
}

/*Chat-box*/
.chat-main-box {
  position: relative;
  background: #ffffff;
  overflow: hidden;
}

.chat-main-box .chat-left-aside {
  position: absolute;
  width: 250px;
  z-index: 9;
  top: 0px;
  border-right: 1px solid rgba(120, 130, 140, 0.13);
}

.chat-main-box .chat-left-aside .open-panel {
  display: none;
  cursor: pointer;
  position: absolute;
  left: -webkit-calc(99%);
  top: 50%;
  z-index: 100;
  background-color: #fff;
  -webkit-box-shadow: 1px 0 3px rgba(0, 0, 0, 0.2);
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.2);
  border-radius: 0 100px 100px 0;
  line-height: 1;
  padding: 15px 8px 15px 4px;
}

.chat-main-box .chat-left-aside .chat-left-inner .form-control {
  height: 60px;
}

.chat-main-box .chat-left-aside .chat-left-inner .style-none {
  padding: 0px;
}

.chat-main-box .chat-left-aside .chat-left-inner .style-none li {
  list-style: none;
  overflow: hidden;
}

.chat-main-box .chat-left-aside .chat-left-inner .style-none li a {
  padding: 20px;
}

.chat-main-box .chat-left-aside .chat-left-inner .style-none li a:hover,
.chat-main-box .chat-left-aside .chat-left-inner .style-none li a.active {
  background: #f7fafc;
}

.chat-main-box .chat-right-aside {
  margin-left: 250px;
}

.chat-main-box .chat-right-aside .chat-list {
  max-height: none;
  height: 100%;
  padding-top: 40px;
}

.chat-main-box .chat-right-aside .chat-list .chat-text {
  border-radius: 6px;
}

.chat-main-box .chat-right-aside .send-chat-box {
  position: relative;
}

.chat-main-box .chat-right-aside .send-chat-box .form-control {
  border: none;
  border-top: 1px solid rgba(120, 130, 140, 0.13);
  resize: none;
  height: 80px;
  padding-right: 180px;
}

.chat-main-box .chat-right-aside .send-chat-box .form-control:focus {
  border-color: rgba(120, 130, 140, 0.13);
}

.chat-main-box .chat-right-aside .send-chat-box .custom-send {
  position: absolute;
  right: 20px;
  bottom: 10px;
}

.chat-main-box .chat-right-aside .send-chat-box .custom-send .cst-icon {
  color: #686868;
  margin-right: 10px;
}

/*User Cards*/
.el-element-overlay .white-box {
  padding: 0px;
}

.el-element-overlay .el-card-item {
  position: relative;
  padding-bottom: 25px;
}

.el-element-overlay .el-card-item .el-card-avatar {
  margin-bottom: 15px;
}

.el-element-overlay .el-card-item .el-card-content {
  text-align: center;
}

.el-element-overlay .el-card-item .el-card-content h3 {
  margin: 0px;
}

.el-element-overlay .el-card-item .el-card-content a {
  color: #686868;
}

.el-element-overlay .el-card-item .el-card-content a:hover {
  color: #ff6849;
}

.el-element-overlay .el-card-item .el-overlay-1 {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  text-align: center;
  cursor: default;
}

.el-element-overlay .el-card-item .el-overlay-1 img {
  display: block;
  position: relative;
  -webkit-transition: all .4s linear;
  transition: all .4s linear;
  width: 100%;
  height: auto;
}

.el-element-overlay .el-card-item .el-overlay-1:hover img {
  -ms-transform: scale(1.2) translateZ(0);
  -webkit-transform: scale(1.2) translateZ(0);
  /* transform: scale(1.2) translateZ(0); */
}

.el-element-overlay .el-card-item .el-overlay-1 .el-info {
  text-decoration: none;
  display: inline-block;
  text-transform: uppercase;
  color: #ffffff;
  background-color: transparent;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  padding: 0;
  margin: auto;
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%) translateZ(0);
  -webkit-transform: translateY(-50%) translateZ(0);
  -ms-transform: translateY(-50%) translateZ(0);
}

.el-element-overlay .el-card-item .el-overlay-1 .el-info>li {
  list-style: none;
  display: inline-block;
  margin: 0 3px;
}

.el-element-overlay .el-card-item .el-overlay-1 .el-info>li a {
  border-color: #ffffff;
  color: #ffffff;
  padding: 12px 15px 10px;
}

.el-element-overlay .el-card-item .el-overlay-1 .el-info>li a:hover {
  background: #EA5836;
  border-color: #EA5836;
}

.el-element-overlay .el-card-item .el-overlay {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow: hidden;
  top: 0;
  left: 0;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.7);
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}

.el-element-overlay .el-card-item .el-overlay-1:hover .el-overlay {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
}

.el-element-overlay .el-card-item .el-overlay-1 .scrl-dwn {
  top: -100%;
}

.el-element-overlay .el-card-item .el-overlay-1 .scrl-up {
  top: 100%;
  height: 0px;
}

.el-element-overlay .el-card-item .el-overlay-1:hover .scrl-dwn {
  top: 0px;
}

.el-element-overlay .el-card-item .el-overlay-1:hover .scrl-up {
  top: 0px;
  height: 100%;
}

/*Login sidebar*/
.login-sidebar {
  position: absolute;
  right: 0px;
  margin-top: 0px;
  height: 100%;
}

/*Listing*/
.common-list {
  margin: 0px;
  padding: 0px;
}

.common-list li {
  list-style: none;
  display: block;
}

.common-list li a {
  padding: 12px 0px;
  color: #686868;
  display: block;
}

.common-list li a:hover {
  color: #ff6849;
}

/*table layouts*/
.color-table.primary-table thead th {
  background-color: #4c5aa0;
  color: #ffffff;
}

.color-table.success-table thead th {
  background-color: #15D19B;
  color: #ffffff;
}

.color-table.info-table thead th {
  background-color: #03a9f3;
  color: #ffffff;
}

.color-table.warning-table thead th {
  background-color: #fec107;
  color: #ffffff;
}

.color-table.danger-table thead th {
  background-color: #EA5836;
  color: #ffffff;
}

.color-table.inverse-table thead th {
  background-color: #4c5667;
  color: #ffffff;
}

.color-table.dark-table thead th {
  background-color: #2b2b2b;
  color: #ffffff;
}

.color-table.red-table thead th {
  background-color: #fb3a3a;
  color: #ffffff;
}

.color-table.purple-table thead th {
  background-color: #9675ce;
  color: #ffffff;
}

.color-table.muted-table thead th {
  background-color: #98a6ad;
  color: #ffffff;
}

.color-bordered-table.primary-bordered-table {
  border: 2px solid #4c5aa0;
}

.color-bordered-table.primary-bordered-table thead th {
  background-color: #4c5aa0;
  color: #ffffff;
}

.color-bordered-table.success-bordered-table {
  border: 2px solid #15D19B;
}

.color-bordered-table.success-bordered-table thead th {
  background-color: #15D19B;
  color: #ffffff;
}

.color-bordered-table.info-bordered-table {
  border: 2px solid #03a9f3;
}

.color-bordered-table.info-bordered-table thead th {
  background-color: #03a9f3;
  color: #ffffff;
}

.color-bordered-table.warning-bordered-table {
  border: 2px solid #fec107;
}

.color-bordered-table.warning-bordered-table thead th {
  background-color: #fec107;
  color: #ffffff;
}

.color-bordered-table.danger-bordered-table {
  border: 2px solid #EA5836;
}

.color-bordered-table.danger-bordered-table thead th {
  background-color: #EA5836;
  color: #ffffff;
}

.color-bordered-table.inverse-bordered-table {
  border: 2px solid #4c5667;
}

.color-bordered-table.inverse-bordered-table thead th {
  background-color: #4c5667;
  color: #ffffff;
}

.color-bordered-table.dark-bordered-table {
  border: 2px solid #2b2b2b;
}

.color-bordered-table.dark-bordered-table thead th {
  background-color: #2b2b2b;
  color: #ffffff;
}

.color-bordered-table.red-bordered-table {
  border: 2px solid #fb3a3a;
}

.color-bordered-table.red-bordered-table thead th {
  background-color: #fb3a3a;
  color: #ffffff;
}

.color-bordered-table.purple-bordered-table {
  border: 2px solid #9675ce;
}

.color-bordered-table.purple-bordered-table thead th {
  background-color: #9675ce;
  color: #ffffff;
}

.color-bordered-table.muted-bordered-table {
  border: 2px solid #98a6ad;
}

.color-bordered-table.muted-bordered-table thead th {
  background-color: #98a6ad;
  color: #ffffff;
}

.full-color-table.full-primary-table {
  background-color: rgba(171, 140, 228, 0.8);
}

.full-color-table.full-primary-table thead th {
  background-color: #4c5aa0;
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-primary-table tbody td {
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-primary-table tr:hover {
  background-color: #4c5aa0;
}

.full-color-table.full-success-table {
  background-color: rgba(0, 194, 146, 0.8);
}

.full-color-table.full-success-table thead th {
  background-color: #15D19B;
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-success-table tbody td {
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-success-table tr:hover {
  background-color: #15D19B;
}

.full-color-table.full-info-table {
  background-color: rgba(3, 169, 243, 0.8);
}

.full-color-table.full-info-table thead th {
  background-color: #03a9f3;
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-info-table tbody td {
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-info-table tr:hover {
  background-color: #03a9f3;
}

.full-color-table.full-warning-table {
  background-color: rgba(254, 193, 7, 0.8);
}

.full-color-table.full-warning-table thead th {
  background-color: #fec107;
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-warning-table tbody td {
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-warning-table tr:hover {
  background-color: #fec107;
}

.full-color-table.full-danger-table {
  background-color: rgba(251, 150, 120, 0.8);
}

.full-color-table.full-danger-table thead th {
  background-color: #EA5836;
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-danger-table tbody td {
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-danger-table tr:hover {
  background-color: #EA5836;
}

.full-color-table.full-inverse-table {
  background-color: rgba(76, 86, 103, 0.8);
}

.full-color-table.full-inverse-table thead th {
  background-color: #4c5667;
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-inverse-table tbody td {
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-inverse-table tr:hover {
  background-color: #4c5667;
}

.full-color-table.full-dark-table {
  background-color: rgba(43, 43, 43, 0.8);
}

.full-color-table.full-dark-table thead th {
  background-color: #2b2b2b;
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-dark-table tbody td {
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-dark-table tr:hover {
  background-color: #2b2b2b;
}

.full-color-table.full-red-table {
  background-color: rgba(251, 58, 58, 0.8);
}

.full-color-table.full-red-table thead th {
  background-color: #fb3a3a;
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-red-table tbody td {
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-red-table tr:hover {
  background-color: #fb3a3a;
}

.full-color-table.full-purple-table {
  background-color: rgba(150, 117, 206, 0.8);
}

.full-color-table.full-purple-table thead th {
  background-color: #9675ce;
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-purple-table tbody td {
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-purple-table tr:hover {
  background-color: #9675ce;
}

.full-color-table.full-muted-table {
  background-color: rgba(152, 166, 173, 0.8);
}

.full-color-table.full-muted-table thead th {
  background-color: #98a6ad;
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-muted-table tbody td {
  border: 0 !important;
  color: #ffffff;
}

.full-color-table.full-muted-table tr:hover {
  background-color: #98a6ad;
}

/* Material Form Input Elements */
.floating-labels .form-group {
  position: relative;
}

.floating-labels .form-control {
  font-size: 20px;
  padding: 10px 10px 10px 0;
  display: block;
  border: none;
  border-bottom: 1px solid #e4e7ea;
}

.floating-labels select.form-control>option {
  font-size: 14px;
}

.has-error .form-control {
  border-bottom: 1px solid #EA5836;
}

.has-warning .form-control {
  border-bottom: 1px solid #fec107;
}

.has-success .form-control {
  border-bottom: 1px solid #15D19B;
}

.floating-labels .form-control:focus {
  outline: none;
  border: none;
}

.floating-labels label {
  color: #686868;
  font-size: 16px;
  position: absolute;
  cursor: auto;
  top: 10px;
  transition: 0.2s ease all;
  -moz-transition: 0.2s ease all;
  -webkit-transition: 0.2s ease all;
}

.floating-labels .form-control:focus~label,
.floating-labels .form-control:valid~label {
  top: -20px;
  font-size: 12px;
  color: #4c5aa0;
}

.floating-labels .bar {
  position: relative;
  display: block;
}

.floating-labels .bar:before,
.floating-labels .bar:after {
  content: '';
  height: 2px;
  width: 0;
  bottom: 1px;
  position: absolute;
  background: #4c5aa0;
  transition: 0.2s ease all;
  -moz-transition: 0.2s ease all;
  -webkit-transition: 0.2s ease all;
}

.floating-labels .bar:before {
  left: 50%;
}

.floating-labels .bar:after {
  right: 50%;
}

.floating-labels .form-control:focus~.bar:before,
.floating-labels .form-control:focus~.bar:after {
  width: 50%;
}

.floating-labels .highlight {
  position: absolute;
  height: 60%;
  width: 100px;
  top: 25%;
  left: 0;
  pointer-events: none;
  opacity: 0.5;
}

.floating-labels .input-lg~label,
.floating-labels .input-lg {
  font-size: 24px;
}

.floating-labels .input-sm~label,
.floating-labels .input-sm {
  font-size: 16px;
}

.has-warning .bar:before,
.has-warning .bar:after {
  background: #fec107;
}

.has-success .bar:before,
.has-success .bar:after {
  background: #15D19B;
}

.has-error .bar:before,
.has-error .bar:after {
  background: #EA5836;
}

.has-warning .form-control:focus~label,
.has-warning .form-control:valid~label {
  color: #fec107;
}

.has-success .form-control:focus~label,
.has-success .form-control:valid~label {
  color: #15D19B;
}

.has-error .form-control:focus~label,
.has-error .form-control:valid~label {
  color: #EA5836;
}

.has-feedback label~.t-0 {
  top: 0;
}

/* Update 2.5 */
.table.dataTable,
table.dataTable {
  width: 99.80% !important;
}

table.dataTable thead .sorting_asc::after,
table.dataTable thead .sorting::after,
table.dataTable thead .sorting_desc::after {
  float: none;
  padding-left: 10px;
}

/* style for realestate pages */
.re ul.two-part li i,
.re ul.two-part li span {
  font-size: 36px;
}

.bg-light h4 {
  font-weight: bold;
}

.agent-contact,
.pro-desc {
  font-size: 12px;
}

.form-agent-inq .form-group {
  margin-bottom: 10px;
}

.agent-info {
  max-height: 358px;
  height: 358px;
  background: #f7fafc;
}

.pro-list {
  margin-top: 15px;
}

.pro-img,
.pro-detail {
  display: table-cell;
  vertical-align: top;
}

.pro-detail h5 a {
  color: #686868;
  line-height: 20px;
  font-weight: 500;
}

.pro-box .pro-list-img {
  display: block;
  height: 210px;
  position: relative;
  overflow: hidden;
}

.pro-box .pro-label {
  position: absolute;
  text-transform: uppercase;
  top: 0;
  right: 0;
  border-radius: 2px;
  padding: 5px;
  font-size: 80%;
}

.pro-col-label {
  padding: 7px;
  width: 26%;
  display: block;
  margin-top: -15px;
  margin-left: 37%;
  border: 1px solid rgba(120, 130, 140, 0.13);
  text-transform: uppercase;
}

.pro-box .pro-label-img {
  position: absolute;
  top: 30px;
  right: 30px;
}

.pro-box.pro-horizontal pro-content {
  width: 100%;
  height: 210px;
}

.pro-content .pro-list-details {
  height: 138px;
  max-height: 142px;
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
  border-right: 1px solid rgba(120, 130, 140, 0.13);
}

.pro-content .pro-list-info {
  border-bottom: 1px solid rgba(120, 130, 140, 0.13);
}

.pro-content .pro-list-details h3,
.pro-content .pro-list-details h4,
.pro-list-info ul.pro-info li,
.pro-agent .agent-name h5,
.pro-agent .agent-name small,
ul.pro-info li span.label,
.pro-location span,
.pro-list-info-3-col ul.pro-info li,
.pro-content-3-col .pro-list-details h3,
.pro-content-3-col .pro-list-details h4,
.pro-content-3-col .pro-list-details h4 small,
.pro-agent-col-3 .agent-name h5,
.pro-agent-col-3 .agent-name small {
  font-weight: 500;
}

.pro-list-info ul.pro-info,
.pro-list-info-3-col ul.pro-info {
  padding: 16px 10px 10px 10px;
  list-style: none;
}

.pro-list-info ul.pro-info li {
  padding: 10px 0px 10px 20px;
  font-size: 12px;
}

ul.pro-info li span.label {
  width: 25px;
  height: 25px;
  padding: 8px;
  border-radius: 50%;
  margin-top: -4px;
  margin-right: 15px;
  font-size: 12px;
}

ul.pro-info li span img,
ul.pro-amenities li span img {
  margin-top: -8px;
  padding-right: 12px;
}

.pro-agent .agent-img a img,
.pro-agent-col-3 .agent-img a img {
  border: 3px solid #ffffff;
  box-shadow: 1px 1px 1px rgba(120, 130, 140, 0.13);
  /*width: 60px;
    height: 60px;*/
}

.pro-agent .agent-img,
.pro-agent .agent-name,
.pro-agent-col-3 .agent-img,
.pro-agent-col-3 .agent-name {
  float: left;
}

.pro-agent .agent-img {
  padding-top: 12px;
}

.pro-agent .agent-name {
  padding: 10px 0 0 15px;
}

.pro-location span {
  padding-top: 27px;
}

.pro-content-3-col {
  padding: 15px;
  background: #f7fafc;
}

.pro-content-3-col .pro-list-details h4 small {
  color: #EA5836;
}

.pro-list-info-3-col ul.pro-info li {
  padding: 10px 5px;
}

.pro-agent-col-3 .agent-img {
  padding: 15px;
}

.pro-agent-col-3 .agent-name {
  padding: 15px 15px 15px 5px;
}

ul.pro-amenities {
  list-style: none;
  padding: 8px 0;
}

ul.pro-amenities li {
  padding: 10px 0 10px 0;
  font-size: 12px;
}

ul.pro-amenities li span i {
  padding-right: 12px;
}

.pro-rd .table>tbody>tr>td:first-child {
  font-weight: 500;
}

.pro-rd .table>tbody>tr>td,
.pro-rd .table>tbody>tr>th {
  border: none;
  padding: 8px 8px 8px 0;
  font-size: 12px;
}

.pd-agent-info {
  max-height: 200px;
  height: 200px;
  background: #f7fafc;
  margin-top: 15px;
}

.pd-agent-contact,
.pd-agent-inq {
  padding: 25px;
}

.pro-add-form .radio label,
.pro-add-form .checkbox label {
  font-weight: 100;
}

/*Register in steps*/
.register-box {
  max-width: 600px;
  margin: 0 auto;
  padding-top: 2%;
}

.step-register {
  position: absolute;
  height: 100%;
  left: 0;
  right: 0;
}

/*============================*/
.ex-steps {
  padding: 15px;
  margin: 0;
}

.ex-steps>li.current {
  background: #262668;
  /*opacity: 0.5;*/
}

.ex-steps>li.current-light {
  background: #262668;
  opacity: 0.5;
  color: #fff;
}

.ex-steps>li.done {
  background: #262668;
  opacity: 1;
}

.ex-steps>li h4>span.done-circle {
  border-radius: 0;
  border: none;
}

/*============================*/
/*For Laptop (1280px)*/
@media (max-width: 1350px) {
  .carousel .item h3 {
    font-size: 17px;
    height: 90px;
  }

  .inbox-center a {
    width: 400px;
  }
}

/*Small Desktop*/
@media (min-width: 1024px) {
  .app-search .form-control:focus {
    width: 300px;
  }
}

/*Ipad*/
@media (min-width: 768px) {
  #page-wrapper {
    position: inherit;
  }

  .navbar-default {
    position: relative;
    width: 100%;
    top: 0px;
  }

  .fix-header .navbar-static-top {
    position: fixed;
    max-width: 1400px;
  }

  .fix-header .sidebar {
    padding-top: 60px;
  }

  .sidebar {
    z-index: 11;
    position: relative;
    width: 100%;
  }

  .sidebar .nav-second-level {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .sidebar #side-menu>li:hover a {
    /* background: #f7fafc; */
  }

  #wrapper.fix-top .sidebar {
    position: fixed;
    max-width: 1400px;
    top: 0px;
    transition: 0.5s ease-in;
  }

  #wrapper.fix-top #page-wrapper {
    /* set to same height as #side-menu for fluid scrolling */
    padding-top: 87px;
  }

  #wrapper.fix-top #page-wrapper.onboarding {
    /* set to same height as #side-menu for fluid scrolling */
    padding-top: 0;
  }

  /*If body has content-wrapper*/
  .navbar-top-links .dropdown-messages,
  .navbar-top-links .dropdown-tasks,
  .navbar-top-links .dropdown-alerts {
    margin-left: auto;
  }

  .mail_listing {
    border-left: 1px solid rgba(120, 130, 140, 0.13);
    padding-left: 20px;
  }

  .inbox-panel {
    padding-right: 20px;
  }

  .top-minus {
    margin-top: -62px;
    float: right;
  }
}

@media (max-width: 1024px) {
  .b-r-none {
    border-right: 0px;
  }

  .carousel-inner h3 {
    height: 90px;
    overflow: hidden;
  }

  .inbox-center a {
    width: 300px;
  }
}

@media (min-width: 767px) {
  .emailuser_dash {
    width: 20%;
  }
}

/*Phone*/
@media (max-width: 767px) {
  .navbar-top-links {
    display: inline-block;
  }

  .navbar-top-links .profile-pic img {
    margin-right: 0px;
  }

  .top-left-part {
    width: 60px;

  }

  .navbar-top-links li:last-child {
    margin-right: 0px;
  }

  .navbar-top-links .dropdown-messages,
  .navbar-top-links .dropdown-tasks,
  .navbar-top-links .dropdown-alerts {
    width: 260px;
  }

  .row-in-br {
    border-right: 0px;
    border-bottom: 1px solid rgba(120, 130, 140, 0.13);
  }

  .bg-title .breadcrumb {
    float: left;
    margin-top: 0px;
    margin-bottom: 10px;
  }

  /*Timeline*/
  ul.timeline:before {
    left: 40px;
  }

  ul.timeline>li>.timeline-panel {
    width: calc(100% - 90px);
  }

  ul.timeline>li>.timeline-badge {
    top: 16px;
    left: 15px;
    margin-left: 0;
  }

  ul.timeline>li>.timeline-panel {
    float: right;
  }

  ul.timeline>li>.timeline-panel:before {
    right: auto;
    left: -15px;
    border-right-width: 15px;
    border-left-width: 0;
  }

  ul.timeline>li>.timeline-panel:after {
    right: auto;
    left: -14px;
    border-right-width: 14px;
    border-left-width: 0;
  }

  .wizard-steps>li {
    display: block;
  }

  .dropdown .mailbox,
  .dropdown .dropdown-tasks {
    left: -94px;
  }

  /***** Start Update 1.5 *****/
  .fix-header .navbar-static-top {
    position: fixed;
    top: 0px;
    width: 100%;
  }

  .fix-header #page-wrapper {
    margin-top: 60px;
  }

  .fix-header .sidebar {
    position: fixed;
    height: 350px;
    top: 60px;
    z-index: 100;
    overflow: auto !important;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
  }

  .mega-dropdown-menu {
    height: 340px;
    overflow: auto;
  }

  .left-aside {
    position: relative;
    width: 100%;
    border: 0px;
  }

  .right-aside {
    margin-left: 0px;
  }

  .chat-main-box .chat-left-aside {
    left: -250px;
    transition: 0.5s ease-in;
    background: #ffffff;
  }

  .chat-main-box .chat-left-aside.open-pnl {
    left: 0px;
  }

  .chat-main-box .chat-left-aside .open-panel {
    display: block;
  }

  .chat-main-box .chat-right-aside {
    margin: 0px;
  }

  /***** Close Update 1.5 *****/
  .table-responsive.pro-rd {
    border: none;
  }

  .step-register,
  .login-register,
  #msform fieldset {
    position: relative;
  }
}

@media (max-width: 480px) {
  .vtabs .tabs-vertical {
    width: auto;
  }

  .stat-item {
    padding-right: 0px;
  }

  .login-box {
    width: 100%;
  }

  .pro-content .pro-list-details {
    height: 100px;
    border-right: none;
  }

  .pro-list-info ul.pro-info li {
    padding: 10px 0 10px 0;
  }

  .pro-list-info ul.pro-info {
    padding-left: 0;
  }

  .pro-agent .agent-img {
    padding-top: 3px;
  }

  .pro-agent .agent-name {
    padding: 2px 0 10px 15px;
  }
}



.btn-google {
  background: #4285f4;
  color: #fff;
  box-shadow: 1px 1px 5px 0 rgba(0, 0, 0, 0.2) !important;
}

.google-button {
  height: 50px;
  width: 350px;

  background-color: #4285f4;
  border: none;
  color: #fff;

  -webkit-border-radius: 1px;
  border-radius: 1px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .25);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: background-color .218s, border-color .218s, box-shadow .218s;
  transition: background-color .218s, border-color .218s, box-shadow .218s;
  -webkit-user-select: none;
  -webkit-appearance: none;
  cursor: pointer;
  outline: none;
  overflow: hidden;
  position: relative;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  width: auto;
}

.google-button:hover {
  -webkit-box-shadow: 0 0 3px 3px rgba(66, 133, 244, .3);
  box-shadow: 0 0 3px 3px rgba(66, 133, 244, .3);
}

.g-wrapper {
  border: 1px solid transparent;
  height: 100%;
  width: 100%;
}

.g-icon {
  padding: 15px;
  background-color: #fff;
  -webkit-border-radius: 1px;
  border-radius: 1px;
  float: left;
}

.g-logo {
  width: 18px;
  height: 18px;

}

.g-text {
  font-size: 16px;
  line-height: 48px;

  font-family: Roboto, arial, sans-serif;
  font-weight: 500;
  letter-spacing: .21px;
  margin-left: 6px;
  margin-right: 6px;
  vertical-align: top;
}

.btn-windows {
  background: #F65314;
  color: #fff;
}

.btn-starling {
  background: #7433ff;
  color: #fff;
}

.btn-social {
  opacity: 1;
}

.btn-social:focus,
.btn-social:hover {
  color: #fff;
  opacity: 0.9;
}

.descriptionemailsent p {
  font-size: 13px;
  text-align: justify;
}

.swYN .switchery {
  float: left;
}

.swYN .js-check-change-field {
  float: left;
  margin-left: 15px;
  margin-top: 7px;
}

table#organisations-table td {
  vertical-align: middle;
}

.certification-plain-english {
  background: #4c5aa0;
  padding: 10px;
  min-height: 80px;
  border-radius: 5px;
  color: #fff;
  white-space: pre-line;
}

.certification-implementation-tips {
  background: none;
  padding: 7px;
  min-height: 80px;
  border-radius: 5px;
}

.certification-plain-english label,
.certification-implementation-tips label {
  margin-bottom: 0;
}

.lead-registration .modal-body {
  padding: 0;
}

.lead-registration .modal-body iframe {
  width: 100%;
  height: 800px;
  border: 0;
}

.lead-registration .modal-footer {
  display: none;
}

.help_silver_text {
  background-color: #edf1f5;
  padding: 10px 15px;
  margin: 10px 0 10px 0;
  border-radius: 8px;
}

.multiple-install {
  border-left: 2px groove rgba(171, 140, 228, 0.7);
}

.affiliate-content {
  height: 50px;
  background-color: #8f54bb;
  width: 600px;
  margin: 0 auto;
  color: white;
  -webkit-animation: swing 1s ease;
  animation: swing 1s ease;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}

.affiliate-text {
  float: left;
  padding-top: 14px;
  padding-left: 14px;
  width: 100%;
  text-align: center;
}

@-webkit-keyframes typing {
  from {
    width: 0
  }

  to {
    width: 80%
  }
}

@-webkit-keyframes blink-caret {
  50% {
    border-color: transparent;
  }
}

.topic-description {
  background-color: #DFE5E5;
  margin: -50px -15px 10px -15px;
  padding: 10px;
  border-radius: 8px;
}

.description-text {
  width: 100%;
  white-space: nowrap;
  margin: 0 auto;
  overflow: hidden;
  -webkit-animation: typing 5s steps(100, end),
    blink-caret .5s step-end;
}

.meter {
  height: 25px;
  position: relative;
  background: #f5f5f5;
  overflow: hidden;
}

.meter span {
  display: block;
  height: 100%;
  text-align: center;
  font-size: 17px;
  min-width: 5%;
}

.meter-progress {
  background-color: #112935;
  -webkit-animation: progressBar 3s ease-in-out;
  -webkit-animation-fill-mode: both;
  -moz-animation: progressBar 3s ease-in-out;
  -moz-animation-fill-mode: both;
}

@-webkit-keyframes progressBar {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}

@-moz-keyframes progressBar {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}


.file-input {
  padding: 0 !important;
  border-radius: 5px;
}

.clickable {
  cursor: pointer;
}

.switchery {
  width: 80px !important;
}

.switchery:before {
  color: #333;
  position: absolute;
  left: 40px;
  top: 50%;
  transform: translateY(-50%) translateX(-10%);
  text-align: left;
}

.js-switch:checked+.switchery:before {
  color: #fff;
  width: 100px;
  left: 30px;
}

.social-connected {
  height: 64px;
  width: 300px;
  position: relative;
  background-color: #f4f6f8;
  padding: 7px;
  margin-bottom: 10px;
}

.social-avatar {
  height: 50px;
  display: inline-block;
}

.social-name {
  height: 25px;
  display: inline-block;
  position: absolute;
  margin-top: 7px;
  padding-left: 15px;
  font-weight: bolder;
}

.social-email {
  height: 25px;
  display: inline-block;
  position: absolute;
  bottom: 6px;
  padding-left: 15px;
}


@media (min-width: 992px) {
  .modal-xlg {
    width: 900px;
  }
}

@media (min-width: 1200px) {
  .modal-xlg {
    width: 1198px;
  }
}

.policy_read {
  border-radius: 50px;
  background: #fff;
  color: #15D19B;
  display: inline-block;
  border: 1px solid #15D19B;
  text-decoration: none;
  text-transform: uppercase;
  float: left;
  padding: 0 25px;
  margin-left: 20px;
}

.policy_unread {
  border-radius: 50px;
  background: #fff;
  color: #EA5836;
  display: inline-block;
  border: 1px solid #EA5836;
  text-decoration: none;
  text-transform: uppercase;
  float: left;
  padding: 0 25px;
  margin-left: 20px;
}

.policy-name-agreed {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
  padding: 2px 4px;
  font-size: 90%;
  /*color: #25c743;*/
  color: black;
  /*background-color: #f2f9f3;*/
  border-radius: 4px;
  border-bottom: 1px solid #25c743;
}

.policy-name-read {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
  padding: 2px 4px;
  font-size: 90%;
  /*color: #496e9e;*/
  color: black;
  /*background-color: #e8f1f9;*/
  border-radius: 4px;
  border-bottom: 1px solid #496e9e;
}

.policy-name-not-agreed {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
  padding: 2px 4px;
  font-size: 90%;
  /*color: #c7254e;*/
  color: black;
  /*background-color: #f9f2f4;*/
  border-radius: 4px;
  border-bottom: 1px solid #c7254e;
}

.policy-user {
  color: #2b2b2b
}

.white-box.declaration {
  padding: 40px;
}

.white-box.declaration-signed {
  border: 2px solid #15D19B;
}

.declaration-success {
  border-left: 5px solid #37c537 !important;
  border: 1px solid rgba(120, 130, 140, 0.13);
  padding: 10px 8px;
  /*white-space: pre-wrap;*/
}

.declaration-fail {
  border-left: 5px solid #ff4f4d !important;
  border: 1px solid rgba(120, 130, 140, 0.13);
  padding: 10px 8px;
  /*white-space: pre-wrap;*/
}

.declaration-no-applicable {
  border-left: 5px solid silver !important;
  border: 1px solid rgba(120, 130, 140, 0.13);
  padding: 10px 8px;
  /*white-space: pre-wrap;*/
}

.signature-pad {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 200px;
  background-color: white;
  border: 2px dashed #ddd
}

i.fa.fa-file-word-o {
  color: #0d47a1;
}

i.fa.fa-file-excel-o {
  color: #388e3c;
}

i.fa.fa-file-powerpoint-o {
  color: #e64919;
}

i.fa.fa-file-pdf-o {
  color: #ff0400;
}

.the-gdpr-pack {
  background: white;
  padding: 20px;
  font-size: 16px;
  line-height: 2.5em;
}

.the-gdpr-pack i.fa {
  font-size: 2em;
  vertical-align: middle;
}

/* carousel */
#quote-carousel {
  padding: 0 10px 30px 10px;
  margin-top: 30px 0px 0px;
}

/* Control buttons  */
#quote-carousel .carousel-control {
  background: none;
  color: #222;
  font-size: 2.3em;
  text-shadow: none;
  margin-top: 30px;
}

/* Previous button  */
#quote-carousel .carousel-control.left {
  left: -12px;
}

/* Next button  */
#quote-carousel .carousel-control.right {
  right: -12px !important;
}

/* Changes the position of the indicators */
#quote-carousel .carousel-indicators {
  right: 50%;
  top: auto;
  bottom: 0px;
  margin-right: -19px;
}

/* Changes the color of the indicators */
#quote-carousel .carousel-indicators li {
  background: #c0c0c0;
}

#quote-carousel .carousel-indicators .active {
  background: #333333;
}

#quote-carousel img {
  width: 250px;
  height: 100px
}

/* End carousel */

.item blockquote {
  border: none !important;
  margin: 0;
  font-size: 14px;
}

.item blockquote img {
  margin-bottom: 10px;
}

.item blockquote p:before {
  content: "\f10d";
  font-family: 'Fontawesome';
  float: left;
  margin-right: 10px;
}



/**
  MEDIA QUERIES
*/

/* Small devices (tablets, 768px and up) */
@media (min-width: 768px) {
  #quote-carousel {
    margin-bottom: 0;
    padding: 0 40px 30px 40px;
    margin-top: 30px;
  }

}

/* Small devices (tablets, up to 768px) */
@media (max-width: 768px) {

  /* Make the indicators larger for easier clicking with fingers/thumb on mobile */

  #quote-carousel .carousel-indicators {
    bottom: -20px !important;
  }

  #quote-carousel .carousel-indicators li {
    display: inline-block;
    margin: 0px 5px;
    width: 15px;
    height: 15px;
  }

  #quote-carousel .carousel-indicators li.active {
    margin: 0px 5px;
    width: 20px;
    height: 20px;
  }
}


.policies-upgrade-container {
  background-color: black;
  color: white;
  opacity: 0.9;
  width: 100%;
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  bottom: 0;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  align-items: center;
  justify-content: center;
}

.policies-upgrade-container h4 {
  color: white;
  font-size: 25px;
}

.policies-upgrade-container small {
  color: white;
  font-size: 15px;
}


.loading-container {
  background-color: rgba(237, 241, 245, 0.6);
  color: white;
  width: 100%;
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  bottom: 0;
  padding-top: 17%;
  text-align: center;
}

.loading-container.new {
  background-color: rgba(202, 203, 204, 0.79);
  color: white;
  width: 100%;
  max-height: 100vh;
  overflow: auto;
  position: fixed;
  z-index: 10;
  top: 0;
  left: 0;
  bottom: 0;
  text-align: center;
}

.loading-container:hover {
  cursor: not-allowed;
}

.loading-text {
  color: #262668;
}

.disable-survey-question {
  background-color: #f2f2f2;
  height: 100%;
  width: 100%;
  opacity: 0.7;
  top: 0;
  left: 0;
  position: absolute;
  padding: 0;
  pointer-events: none;
  z-index: 1;
}

.lds-ellipsis {
  display: inline-block;
  position: relative;
  width: 64px;
  height: 64px;
}

.lds-ellipsis.centered {
  position: absolute !important;
  top: 50%;
  left: 50%;
  margin-left: -32px;
}

.lds-ellipsis div {
  position: absolute;
  top: 27px;
  width: 11px;
  height: 11px;
  border-radius: 50%;
  background: #262668;
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.lds-ellipsis div:nth-child(1) {
  left: 6px;
  animation: lds-ellipsis1 0.6s infinite;
}

.lds-ellipsis div:nth-child(2) {
  left: 6px;
  animation: lds-ellipsis2 0.6s infinite;
}

.lds-ellipsis div:nth-child(3) {
  left: 26px;
  animation: lds-ellipsis2 0.6s infinite;
}

.lds-ellipsis div:nth-child(4) {
  left: 45px;
  animation: lds-ellipsis3 0.6s infinite;
}

@keyframes lds-ellipsis1 {
  0% {
    transform: scale(0);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes lds-ellipsis3 {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(0);
  }
}

@keyframes lds-ellipsis2 {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(19px, 0);
  }
}

.sweet-alert {
  font-family: "Circular Std Book", "Lato", sans-serif !important;
}

.sweet-alert button.confirm {
  background-color: #446EE2;
}

.sweet-alert button.confirm:hover {
  opacity: .9;
}

.sweet-alert p {
  font-family: "Lato", sans-serif;
}

.filter-text {
  font-size: 15px;
  line-height: 1em;
}

.nav-addon-switch {
  text-align: center;
}

.nav-addon-switch.nav-pills>li {
  float: none;
  display: inline-block;
}

.stripe-coupon-close {
  position: absolute;
  right: 0;
  top: 12px;
  background: white;
}

.software-plan-upgrade-container {
  background-color: black;
  color: white;
  opacity: 0.9;
  width: 100%;
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  bottom: 0;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  align-items: center;
  justify-content: center;
}

.software-plan-upgrade-container .center {
  text-align: center;
}

.assessment-organisations-block .software-plan-upgrade-container {
  padding-top: 10px;
}

.software-plan-upgrade-container h4 {
  color: white;
  font-size: 25px;
  font-weight: 300;
}

.software-plan-upgrade-popup h4 {
  line-height: 22px;
  font-size: 18px;
  font-weight: 300;
  color: #2b2b2b;
  font-family: 'Poppins', sans-serif;
  margin: 10px 0;
}

.software-plan-upgrade-container small {
  color: white;
  font-size: 15px;
}

.btn-primary-upgrade,
.btn-primary-upgrade:focus {
  background-color: #262668;
  border-color: #262668;
  color: white
}

.btn-primary-upgrade:hover {
  background-color: #262668;
  border-color: #262668;
  color: white
}

.no-hover {
  pointer-events: none !important;
}

.no-report-hover {
  background-color: white !important;
  opacity: 0.5;
  cursor: default;
}

.no-report-hover:hover {
  background-color: white !important;
  opacity: 0.5;
  cursor: default;
}

.org-name-button,
.host-name-button {
  white-space: normal;
  width: 100%;
  font-size: 15px;
}

.org-name-button .pull-right,
.host-name-button .pull-right {
  line-height: inherit;
}

/* mobile */
@media (max-width: 767px) {
  .white-box {
    padding: 15px;
  }

  .container-fluid {
    padding-right: 15px;
    padding-left: 15px;
  }

  .thin-plan-container {
    padding-bottom: 20px;
  }

  .thin-plan-footer {
    position: initial;
    margin-bottom: 10px;
  }

  .plan-container {
    min-height: 0 !important;
    padding-left: 10%;
    padding-right: 10%;
  }

  .thin-plan-container {
    min-height: 0 !important;
  }

  .software-plans .plan-container {
    padding-bottom: 80px;
  }

  .plan-navigation-back {
    margin-top: 0 !important;
    margin-bottom: 20px;
  }

  .pricing-package-column-wrapper {
    padding: 0;
  }
}

.badge-extra-danger {
  background-color: red;
}

.badge-grey {
  background-color: var(--grey-500);
}

.modal-logout-icon {
  font-size: 65px;
}

.modal-logout-timer {
  font-size: 45px;
  position: relative;
}

.sub-item {
  border-left: 1px dotted #4c5aa0;
  width: 15px;
  height: 15px;
  border-bottom: 1px dotted #4c5aa0;
  display: inline-block;
  margin-right: 7px;
}

.color-very-bad {
  color: #d81159;
}

.color-bad {
  color: #8f2d56;
}

.color-average {
  color: #03648b;
}

.color-good {
  color: #52aa8a;
}

.color-excellent {
  color: #16db93;
}

@keyframes blink {
  0% {
    color: #f7fbff;
  }

  100% {
    color: #fec107;
  }
}

@-webkit-keyframes blink {
  0% {
    color: #f7fbff;
  }

  100% {
    color: #fec107;
  }
}

.blink {
  -webkit-animation: blink 1s linear infinite;
  -moz-animation: blink 1s linear infinite;
  animation: blink 1s linear infinite;
}

.assessment-organisations-block {}

.not-started-cert .content .organisation-name {
  background-color: #dee5e5;
}

.not-started-cert .content .organisation-name .collapsed-text {
  color: #3e3c3c;
  font-weight: bold;
}

.not-started-cert .status-label {
  background-color: #435d65;
  padding: 0 10px 0 10px;
  border-radius: 15px;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: bold;
  margin-right: 10px;
}

.assessment-organisations-block .organisation.not-started-cert .content {
  border: 1px solid #dee5e5;
}

.completed-cert .content .organisation-name {
  background-color: #00d597;
}

.completed-cert .content .organisation-name .collapsed-text {
  color: white;
  font-weight: bold;
}

.completed-cert .status-label {
  background-color: #fd431a;
  padding: 0 10px 0 10px;
  border-radius: 15px;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: bold;
  margin-right: 10px;
}

.completed-cert .renewed {
  background-color: #435d65;
  padding: 0 10px 0 10px;
  border-radius: 15px;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: bold;
  margin-right: 10px;
}

.assessment-organisations-block .organisation.completed-cert .content {
  border: 1px solid #00d597;
}

.assessment-organisations-block .organisation {
  color: white;
  padding-bottom: 20px;
  display: block;
}

.assessment-organisations-block .organisation .content {
  min-height: 100px;
  color: rgba(0, 0, 0, 0.56);
  border: 1px solid #446EE2;
  border-radius: 8px;
  overflow: hidden;
}

.content .organisation-name {
  background-color: #446EE2;
  padding: 5px;
  text-align: center;
  color: white;
  /*border-radius: 8px;*/
}

div.collapsed-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /*background-color: #4c5aa0;*/
  text-align: center;
  color: white;
  width: calc((100%) - (20px));

}

.content .organisation-certification {
  padding: 10px;
}

.assessment-organisations-block .organisation .content.active {
  border: 2px solid #262668;
  height: 100px;
}

.content.active .organisation-name {
  background-color: #4c5aa0;
  padding: 5px;
  text-align: center;
  color: white
}

.content.active .organisation-certification {
  padding: 10px;
}

.assessment-answer,
.re-assessment-answer {
  border-bottom: 1px solid #4c5aa0;
  padding: 10px;
  margin-bottom: 2px;
  color: #446EE2;
  font-weight: bolder;
  font-size: 15px;
}

.assessment-ranking {
  text-transform: uppercase;
  font-size: 10px;
  font-weight: bolder;
  padding: 1px 3px 1px 3px;
  border-radius: 2px;
  margin-bottom: 16px;
}

.assessment-ranking-counter {
  text-transform: uppercase;
  font-size: 10px;
  font-weight: bolder;
  padding: 1px 3px 1px 3px;
}

.rank-hide {
  display: none;
}

.rank-0 {
  background-color: #a6abb3;
  color: #fff
}

.rank-1 {
  background-color: #ff7733;
  color: white
}


.rank-2 {
  background-color: #e63b2e;
  color: white
}

.rank-3 {
  background-color: #bd1e1e;
  color: white;
}

.rank-4 {
  background-color: #3041bd;
  color: white;
}

.rank-5 {
  background-color: #4ab14a;
  color: white;
}

.assessment-cr-input,
.re-assessment-cr-input {
  display: none;
  padding: 10px;
  margin-bottom: 2px;
}

.assessment-cr-input:focus,
.re-assessment-cr-input:focus {
  border: none;
}

.assessor-cr,
.re-assessor-cr {
  position: relative;
  padding: 10px;
  margin-bottom: 10px;
  color: #4c5aa0;
  border: 2px dotted #4c5aa0;
}

.assessor-cr-icon,
.re-assessor-cr-icon {
  position: absolute;
  right: 10px;
  bottom: 10px;
}

.assessment-cr-input,
.re-assessment-cr-input {
  border: 2px dotted #4c5aa0;
}

.assessment-cr-input:focus,
.re-assessment-cr-input:focus {
  border: 2px dotted #4c5aa0;
}

.accept-cr-hide {
  display: none;
}

.cr-accepted-hide {
  display: none;
}

.assessment-answer-input {
  display: none;
  padding: 10px;
  margin-bottom: 2px;
}

.assessment-answer-input:focus {
  border: none;
}

.assessment-answer-input {
  border: 2px dotted #4c5aa0;
}

.assessment-answer-input:focus {
  border: 2px dotted #4c5aa0;
}

.acceptance-confirm {
  font-size: 25px;
  display: none
}

.question-code {
  width: 130px;
  display: inline-block;
}

.login-with-partners-button {
  background-color: #4c5aa0;
  color: white;
  width: 100%;
  font-weight: lighter;
  font-size: 15px;
}

.login-with-partners-button:focus,
.login-with-partners-button:hover {
  color: white;
}

.login-with-partners-dropdown {
  background-color: rgba(76, 90, 160, 0.70);
  width: 100%;
  padding: 10px;
  color: white
}

.assessment-failed-topic {
  background-color: #e6c9d3;
}

.assessment-failed-question {
  padding: 10px;
  border: 3px dotted #a94442;
}

.signup-input {
  width: 480px;
  padding: 9px;
  border: none;
  border-radius: 4px;
  box-shadow: 0 7px 14px 0 rgba(49, 49, 93, 0.10), 0 3px 6px 0 rgba(0, 0, 0, 0.08);
  padding-left: 20px;
}

.no-border {
  border: none !important;
}


/* The switch - the box around the slider */
.switch {
  --circle-size: 26px;
  --switch-size: 34px;
  --switch-width: 60px;

  position: relative;
  display: inline-block;
  width: var(--switch-width);
  height: var(--switch-size);
  float: right;
}

/* Hide default HTML checkbox */
.switch input {
  display: none;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: var(--circle-size);
  width: var(--circle-size);
  left: calc((var(--switch-size) - var(--circle-size)) / 2);
  bottom: calc((var(--switch-size) - var(--circle-size)) / 2);
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
}

.switch input:checked+.slider::after {
  position: absolute;
  left: 8px;
  bottom: 9px;
  color: #fff;
  font-size: 12px;
}

.switch input+.slider::after {
  right: 10px;
  bottom: 9px;
  position: absolute;
  color: #fff;
  font-size: 12px;
}

input.success:checked+.slider {
  background-color: #4074D2;
}

input.primary:checked+.slider {
  background-color: var(--primary-400);
}

.slider~.text-label {
  position: absolute;
  left: 50px;
  text-wrap: nowrap;
}

input:checked+.slider~.text-label.checked {
  display: block;
}

input:checked+.slider~.text-label.not-checked {
  display: none;
}

input+.slider~.text-label.checked {
  display: none;
}

input+.slider~.text-label.not-checked {
  display: block;
}

input:focus+.slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked+.slider:before {
  -webkit-transform: translateX(var(--circle-size));
  -ms-transform: translateX(var(--circle-size));
  transform: translateX(var(--circle-size));
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}


/* The tiny slider */
.switch-tiny {
  --circle-size: 22px;
  --switch-size: 24px;
  --switch-width: 46px;
}

.switch-tiny input:checked+.slider::after {
  content: "";
}

.switch-tiny input+.slider::after {
  content: "";
}

.slider.round:before {
  border-radius: 50%;
}

.link-underline {
  text-decoration: underline;
}

.buy-certificate-container {
  border: 2px solid #4c5aa0;
  padding: 20px;
}

/* certos live marking scheme */

.certos-live-title {
  width: 20px;
  position: fixed;
  margin-left: -50px;
  background-color: #4c5aa0;
  color: white;
  font-size: 11px;
  height: 64px;
  letter-spacing: 3px;
  text-transform: uppercase;
}

#certos-live-text {
  transform: rotate(-90deg);
  display: block;
  margin-top: 30px;
}


.certos-live-marking-scheme-block {
  width: 20px;
  position: fixed;
  margin-left: -30px;
  display: inline-block
}

.spin-this {
  animation-name: spin-this;
  animation-duration: 5000ms;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

@keyframes spin-this {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.security-controls-days-input {
  width: 30px;
  display: inline-block;
  border: none;
  border-bottom: 1px solid silver;
  padding-left: 0px;
  padding-right: 0px;
  text-align: center;
  font-weight: bold;
}


.survey-company-name-highlight {
  color: blue;
}

.survey-company-name-highlight input {
  background-color: #e3ecf2;
  border: 3px solid #448dff;
}

/* installed software */

.software_passing,
.software_failing,
.cve-summary-link {
  cursor: pointer;
}

.no-margins {
  margin: 0 !important;
}

.btn-smart-score,
.btn-smart-score.disabled {
  border-radius: 0;
  background: #3b60c4;
  border: 1px solid #3b60c4;
}

.btn-smart-score:hover,
.btn-smart-score.disabled:hover,
.btn-smart-score:focus,
.btn-smart-score.disabled:focus,
.btn-smart-score.focus,
.btn-smart-score.disabled.focus {
  background: #3b60c4;
  opacity: 0.9;
  border: 1px solid #3b60c4;
}

.btn-smart-score.btn-outline {
  color: #3b60c4;
  background-color: transparent;
}

.btn-smart-score:hover,
.btn-smart-score.disabled:hover,
.btn-smart-score:focus,
.btn-smart-score.disabled:focus,
.btn-smart-score.focus,
.btn-smart-score.disabled.focus {
  background: #1E45AF;
  color: white;
  transition: 0.2s;
}

.software-report-table {}

.software-report-table.non-vulnerable {
  border-top: 1px solid #15d19b70;
}

.software-report-table.non-vulnerable td {
  border-color: #15d19b70;
}

.software-report-table.non-vulnerable th {
  border-color: #15d19b70;
}

.software-report-table.vulnerable {
  border-top: 1px solid #ea583670;
}

.software-report-table.vulnerable td {
  border-color: #ea583670;
}

.software-report-table.vulnerable th {
  border-color: #ea583670;
}

/* Manage notifications view */
.notifications-switch {
  padding: 10px;
}

.notifications-reminders-label {
  display: inline-block;
  color: slategrey;
}

.notifications-hours-input {
  width: 40px;
  display: inline-block;
  border: none;
  border-bottom: 1px solid silver;
  padding-left: 0px;
  padding-right: 0px;
  text-align: center;
  font-weight: bold;
}

.notifications-checkbox {
  margin-right: 10px !important;
}

.notifications-section-label {
  text-transform: uppercase;
  color: slategrey;
  font-weight: bold;
}

/* Specific styling for the nav bar icons */
.cs_navbar_icon {
  width: 80px;
  height: 30px
}

.waves-effect.cs_navbar_button {
  width: 120px;
  height: 100px;
  padding: 10px 10px !important;
}

.cert-migration-message {
  background-color: #d9edf7;
  padding: 12px;
}

.renew-button {
  margin-bottom: 10px;
}

/* device report page -> check-report */
.extra-info-check {
  margin-top: -20px;
}

.check-report-page h3.sub-title {
  font-family: 'Lato', sans-serif;
  font-weight: 700;
  font-size: 24px;
}

.panel-default.devices-table {
  border-left: 1px solid rgba(120, 130, 140, 0.13);
  border-right: 1px solid rgba(120, 130, 140, 0.13);
  border-top: 1px solid rgba(120, 130, 140, 0.13);
  margin-top: 0px !important;
  border-radius: 0;
}

.panel-default.devices-table.panel .panel-heading {
  position: relative;
}

.panel-default.devices-table.panel .panel-heading a[data-toggle=collapse]:before {
  top: 50%;
  position: absolute;
  transform: translateY(-50%);
  right: 10px;
}

.panel-default.devices-table.total-devices {
  background-color: #0F222D;
}

.panel-default.devices-table.total-devices>.panel-heading {
  background-color: #0F222D;
}

.panel-default.devices-table.total-devices>.panel-heading>h4.panel-title {
  color: #E5E5E5;
}

.panel-default.devices-table>.panel-heading>h4.panel-title {
  font-family: inherit !important;
  color: #55676F;
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
}

.panel-default.devices-table>.panel-heading {
  border: none;
}

.panel-default.devices-table>.panel-heading>div.panel-title {
  font-family: inherit !important;
  text-transform: none;
  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  min-height: 25px;
  margin-top: 10px;
}

.panel-default.devices-table>.panel-collapse>.panel-body {
  padding-top: 0px;
  padding-right: 0px;
  padding-left: 0px;
}

.panel-default.devices-table>.panel-collapse>.panel-body>table>thead>tr>th {
  text-transform: none;
  font-family: inherit !important;
  color: inherit;
  background-color: #F5F5F5;
  padding-top: 10px;
  padding-bottom: 10px;
}

.panel-default.devices-table>.panel-collapse>.panel-body>table>tbody>tr>td {
  background-color: #FCFCFF;
  padding-top: 10px;
  padding-bottom: 10px;
  color: #55676F;
}

.check-report-device-type {
  color: var(--grey-375);
  border: 1px solid #d1d5db;
  background-color: #fff;
  margin-right: -3px;
  min-width: 115px;
  height: 36px;
  border-radius: 6px;
  text-align: center;

}

.check-report-device-type.active-filter {
  background-color: var(--grey-100);
}

.check-report-device-type:hover {
  transition: border-color 0s, z-index 0s;
  z-index: 999;
}

.check-report-device-type {
  border-radius: 0;
  margin-left: -1px;
}

.check-report-device-type:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.check-report-device-type:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

@media (max-width: 1600px) {
  .dashboard-counter {
    font-size: 16px !important;
    padding-top: 30px !important;
  }
}

/* Pages header */
h2.cs-page-header {
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  font-size: 30px;
}

/* User profile notifications settings table */
.table.table-striped.table-notifications-settings {
  border-collapse: inherit;
}

.table.table-striped.table-notifications-settings>thead>tr>th {
  background-color: #F3F7F7;
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  text-align: right;
  padding-right: 40px;
}

.table.table-striped.table-notifications-settings>thead>tr>th>div {
  padding-right: 8px;
}

.table.table-striped.table-notifications-settings>tbody>tr>td {
  text-align: right;
  padding-right: 46px;
  padding-top: 25px;
}

.selector-all {
  font-family: 'Lato', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: #0F222D;
  text-transform: none;
  display: inline;
  padding-right: 25px;
}

.enable-category {
  width: 20px;
  height: 20px;
}

.settings-note {
  font-size: 16px;
}

.row.settings-note-row {
  padding-top: 15px;
}

.table.table-striped.table-notifications-settings>tbody>tr>th,
.table.table-striped.table-notifications-settings>tbody>tr>td {
  background-color: #F3F7F7;
  font-family: 'Lato', sans-serif;
  font-weight: 700;
  font-size: 18px;
  color: #000000;
  text-transform: none;
  padding-left: 30px;
}

.table.table-striped.table-notifications-settings>tbody>tr:nth-child(odd)>th,
.table.table-striped.table-notifications-settings>tbody>tr:nth-child(odd)>td {
  background-color: #FFFFFF;
}

.table.table-striped.table-notifications-settings>tbody>tr>th>p {
  margin-bottom: 0;
}

.table.table-striped.table-notifications-settings>tbody>tr>th>small {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #0F222D;
  text-transform: none;
}

/* Switches white boxes styling */
.switch-box {
  padding: 8px 15px 8px 15px !important;
}

.switch-label {
  font-family: 'Lato', sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  margin-bottom: 2px;
  margin-top: 12px;
}

.switch-help-text {
  font-family: 'Lato', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #0F222D;
}

.svg-dark {
  filter: invert(0%) sepia(0%) saturate(7500%) hue-rotate(275deg) brightness(20%) contrast(97%);
}

.svg-green {
  filter: invert(61%) sepia(77%) saturate(371%) hue-rotate(87deg) brightness(100%) contrast(84%);
}

.svg-red {
  filter: invert(37%) sepia(55%) saturate(1426%) hue-rotate(322deg) brightness(83%) contrast(90%);
}

.svg-blue {
  filter: invert(60%) sepia(94%) saturate(2790%) hue-rotate(162deg) brightness(98%) contrast(89%);
}

.svg-royalblue {
  filter: invert(37%) sepia(54%) saturate(3251%) hue-rotate(215deg) brightness(91%) contrast(93%);
}

.svg-lightsteelblue {
  filter: invert(79%) sepia(13%) saturate(471%) hue-rotate(174deg) brightness(97%) contrast(90%);
}

.svg-white-smoke {
  filter: invert(81%) sepia(11%) saturate(223%) hue-rotate(160deg) brightness(84%) contrast(93%);
}

.svg-invert {
  filter: invert(100%);
}

.partner-certificates .search-button {
  position: absolute;
  right: 6px;
  border: 1px solid #f0f0f0;
  padding: 5px 10px 5px 10px;
  border-radius: 5px;
  margin: 10px 10px 10px 10px;
  z-index: 1000;
}

.smart-delimiter {
  width: 100%;
  height: 20px;
  border-bottom: 1px solid #e8eded;
  text-align: center;
}

.smart-delimiter .text {
  display: inline;
  font-size: 11px;
  background-color: white;
  padding: 0 5px;
  color: silver;
  line-height: 35px;
  text-transform: uppercase;
}

.nice-number-input {
  width: 80px;
  position: relative;
  display: block;
  border: 1px solid #e8eded;
  padding: 4px 10px;
  border-radius: 5px;
}

.csv-report-download-item {
  background-color: #446ee2;
  display: block;
  margin: 5px;
  border-radius: 5px;
  color: white;
  font-weight: bold;
}

.csv-report-download-item:hover {
  color: white;
}

.job-element {
  font-size: 11px;
  line-height: 18px;
  text-shadow: -1px -1px 0 #606060, 1px -1px 0 #606060, -1px 1px 0 #606060, 1px 1px 0 #606060
}

.no-access td span,
.no-access td div {
  color: #606060;
  opacity: .65;
}

.no-access td button,
.no-access td a {
  pointer-events: none;
  opacity: .65;
}

.no-access {
  pointer-events: none;
}

.not-accessible-org {
  cursor: not-allowed;
}

.not-accessible-org td:first-of-type {
  position: relative;
}

.not-accessible-org td:first-of-type::before {
  content: '';
  display: block;
  width: 6px;
  background: var(--warning-100);
  height: 100%;
  left: 0;
  top: 0;
  position: absolute;
}

.not-accessible-org a:active {
  pointer-events: none;
}

.not-accessible-org a {
  cursor: not-allowed;
  text-decoration: underline;
}

.beating-animation {
  -webkit-animation: beat .25s infinite alternate;
  -moz-animation: beat .25s infinite alternate;
  -ms-animation: beat .25s infinite alternate;
  -o-animation: beat .25s infinite alternate;
  animation: beat .25s infinite alternate;

  -webkit-transform-origin: center;
  -moz-transform-origin: center;
  -o-transform-origin: center;
  -ms-transform-origin: center;
  transform-origin: center;
}


@keyframes beat {
  to {
    -webkit-transform: scale(1.4);
    -moz-transform: scale(1.4);
    -o-transform: scale(1.4);
    -ms-transform: scale(1.4);
    transform: scale(1.4);
  }

}

@-moz-keyframes beat {
  to {
    -moz-transform: scale(1.4);
    transform: scale(1.4);
  }

}

@-webkit-keyframes beat {
  to {
    -webkit-transform: scale(1.4);
    transform: scale(1.4);
  }

}

@-ms-keyframes beat {
  to {
    -ms-transform: scale(1.4);
    transform: scale(1.4);
  }

}

@-o-keyframes beat {
  to {
    -o-transform: scale(1.4);
    transform: scale(1.4);
  }

}

/* Checkbox container */
.cs-checkbox {
  position: relative;
  width: 16px;
  height: 16px;
  margin: 0;
  padding: 0;
  display: inline-block;
}

.cs-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  z-index: 99999;
  width: 16px;
  height: 16px;
}

.cs-checkbox-label {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  border: 1px solid #52525B;
  box-shadow: 0 1px 2px #E4E4E7;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 0;
}

.cs-checkbox-label::before {
  content: '\2713';
  position: absolute;
  top: -1px;
  left: 2px;
  width: 16px;
  height: 16px;
  color: #fff;
  opacity: 0;
  transition: all 0.1s ease;
  font-family: 'Circular Std Bold';
  font-size: 11px;
}

/* Checked state */
.cs-checkbox input[type="checkbox"]:checked+.cs-checkbox-label {
  background-color: #4074D2;
  border-color: #4074D2;
}

.cs-checkbox input[type="checkbox"]:checked+.cs-checkbox-label::before {
  opacity: 1;
}

/* selectable table */
.selectable-cell {
  vertical-align: middle !important;
}

.header-cs-table {
  border: 1px solid var(--geyser, #DFE5E5);
  background: #F5F5F5;
}

.header-cs-table tr th {
  color: var(--primary-400, #4074D2);
  font-weight: 700;
  font-size: 13px;
}

.header-cs-table-selector {
  align-items: center;
  font-size: 13px;
  width: 150px;
  border-radius: 6px;
  border: 1px solid #E4E4E7;
  background: var(--common-white, #FFF);
  padding: 0px 11px;
}

.cs-table-transparent,
.cs-table-transparent td,
.cs-table-transparent th,
.cs-table-transparent thead,
.cs-table-transparent tbody,
.cs-table-transparent tr {
  background: transparent !important;
  border-color: transparent !important;
}

/* Heart beat animation */
@keyframes beat {
  to {
    transform: scale(1.4);
  }
}

.heart-beating {
  animation: beat .25s infinite alternate;
  transform-origin: center;
}

.unicode-code a {
  color: blue !important;
  font-size: 12px;
  font-weight: bold;
  text-decoration: underline;
}

.beta-pill::after {
  content: 'Beta';
  color: #fff;
  background: var(--primary-400);
  margin-left: 5px;
  padding: 6px 7px;
  font-size: 10px;
  border-radius: 7px;
  text-transform: uppercase;
}

.validation-message-error {
  color: #a94442;
}

.no-padding {
  padding: 0px;
}

/* Tooltip styling: hidden by default */
[data-icon-help="true"] {
  visibility: hidden;
  opacity: 0;
  background-color: white;
  padding: 6px 10px;
  border-radius: 8px;
  position: absolute;
  left: 26px;
  border: 1px solid #E4E4E7;
  box-shadow: 1px 1px 1px #ccc;
  color: #000;
  z-index: 1000;
  width: max-content; /* Adjust width to fit content */
  max-width: 300px; /* Optional: prevent excessive width */
  word-wrap: break-word; /* Ensures text wraps properly */
  overflow-wrap: break-word; /* Fallback for compatibility */
  transition: visibility 0s, opacity 0.2s linear;
}

/* Target a child [data-help="true"] when hovering over its parent */
*:hover > [data-icon-help="true"] {
  visibility: visible;
  opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(100%);
    }
}

@keyframes bounce {
    0% { transform:translateY(-100%); opacity: 0; }
    5% { transform:translateY(-100%); opacity: 0; }
    15% { transform:translateY(0); padding-bottom: 5px; }
    30% { transform:translateY(-50%); padding-bottom: 5px; }
    40% { transform:translateY(0%); padding-bottom: 6px; }
    50% { transform:translateY(-30%); }
    70% { transform:translateY(0%); padding-bottom: 7px; }
    80% { transform:translateY(-15%); }
    90% { transform:translateY(0%); padding-bottom: 8px;}
    95% { transform:translateY(-7%); }
    97% { transform:translateY(0%); padding-bottom: 9px; }
    99% { transform:translateY(-3%); }
    100% { transform:translateY(0); padding-bottom: 9px;}
}

@keyframes swing {
    0% { transform: rotate(0deg); }
    20% { transform: rotate(10deg); }
    40% { transform: rotate(-10deg); }
    60% { transform: rotate(5deg); }
    80% { transform: rotate(-5deg); }
    100% { transform: rotate(0deg); }
}

.swing {
    animation: swing 0.5s ease-in-out forwards;
}

.slide-in {
    animation: slideIn 0.3s ease-out forwards;
}

.slide-out {
    animation: slideOut 0.3s ease-out forwards;
}

.bounce-it {
    animation: bounce 0.6s ease-in forwards;
}

/* Hide the body scrollbar when the details drawer is open on Software Inventory pages */
body:has(.details-drawer-mask) {
    overflow: hidden;
}