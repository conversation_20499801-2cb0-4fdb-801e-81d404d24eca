volumes:
  postgres_data17: {}
  pgadmin_data: {}

services:
  django: &django
    build:
      context: .
      dockerfile: ./compose/local/Dockerfile
    depends_on:
      valkey:
        condition: service_started
      postgres:
          condition: service_healthy
      rabbitmq:
        condition: service_started
      nvdtools:
        condition: service_started
    volumes:
      - .:/app

    env_file:
      - ./compose/local/local.env
    environment:
      - RUN_TRANSLATIONS=${RUN_TRANSLATIONS:-0}
    ports:
      - "8000:8000"
    command: /start.sh
    restart: unless-stopped

  postgres:
    image: postgres:17-alpine
    volumes:
      - postgres_data17:/var/lib/postgresql/data
      - /tmp:/home/<USER>
    environment:
      - POSTGRES_USER=cybersmart
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=cybersmart
      - POSTGRES_HOST_AUTH_METHOD=trust
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U cybersmart -d cybersmart" ]
      interval: 3s
      timeout: 10s
      retries: 50
    ports:
      - "5432:5432"

  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: secret
      PGADMIN_LISTEN_PORT: 80
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    links:
      - "postgres:pgsql-server"

  valkey:
     image: valkey/valkey:alpine
     ports:
       - "6379:6379"

  rabbitmq:
     image: rabbitmq:management-alpine
     ports:
       - "5672:5672"
       - "15672:15672"
     environment:
       - RABBITMQ_DEFAULT_USER=admin
       - RABBITMQ_DEFAULT_PASS=password
       - RABBITMQ_DEFAULT_VHOST=vhost
     healthcheck:
       test: ["CMD", "rabbitmq-diagnostics", "check_running"]
       interval: 3s
       timeout: 10s
       retries: 50

  celeryworker:
     # https://github.com/docker/compose/issues/3220
     <<: *django
     depends_on:
       rabbitmq:
         condition: service_healthy
       postgres:
         condition: service_healthy
     ports:
       - "7002:7002"
     command: watchmedo auto-restart --recursive --pattern="tasks.py;common.py;create_certificate.py" --directory="." -- \
              celery -A cyber_smart worker -l INFO -Q celery,chargebee,cve,emails,analytics,materialized,pervade,dynamodb,sqs,trustd,opswat_patch

  celeryflower:
     # https://github.com/docker/compose/issues/3220
     <<: *django
     depends_on:
       rabbitmq:
         condition: service_healthy
       postgres:
         condition: service_healthy
     ports:
       - "5555:5555"
     command: celery -A cyber_smart flower --broker_api=************************************/api/

  celerybeat:
     # https://github.com/docker/compose/issues/3220
     <<: *django
     depends_on:
       - valkey
       - postgres
     ports:
       - "7001:7001"
     command: celery -A cyber_smart beat -l INFO -S redbeat.RedBeatScheduler --pidfile=

  nvdtools:
    build:
      context: .
      dockerfile: ./compose/local/nvdtools/Dockerfile
    ports:
      - "9000:9000"

  websocket:
    <<: *django
    depends_on:
      valkey:
        condition: service_started
      postgres:
        condition: service_healthy
    ports:
      - "8888:8888"
    command: python manage.py run_websocket_server --host 0.0.0.0 --port 8888
    environment:
      - REDIS_URL=redis://valkey:6379/1
